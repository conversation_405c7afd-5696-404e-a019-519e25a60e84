// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "BlackJack/PostEffect/ScreenBlend"
{
	Properties
	{
		_MainTex("Base (RGB)", 2D) = "white" {}
		_SecondTex("SecondTex", 2D) = "white" {}
		_Blend("Blend", Range(0,1)) = 0
	}
	SubShader
	{
		Tags{ "RenderType" = "Opaque" }
		LOD 100

		Pass
		{
			CGPROGRAM
#pragma vertex vert
#pragma fragment frag
#pragma multi_compile_fog
#include "UnityCG.cginc"

			struct a2v
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
			};

			struct v2f
			{
				float4 pos : SV_POSITION;
				float2 uv : TEXCOORD0;
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;
			sampler2D _SecondTex;
			float4 _SecondTex_ST;
			float _Blend;

			v2f vert(a2v v)
			{
				v2f o;
				o.pos = UnityObjectToClipPos(v.vertex);
				o.uv = v.uv;
				return o;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				fixed4 tex0 = tex2D(_MainTex, i.uv);
				fixed4 tex1 = tex2D(_SecondTex, i.uv);
				fixed4 col = lerp(tex1, tex0, _Blend);
				return col;
			}
			ENDCG
		}
	}
}