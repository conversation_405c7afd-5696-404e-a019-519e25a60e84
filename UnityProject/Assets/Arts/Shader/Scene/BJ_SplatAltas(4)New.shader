// Upgrade NOTE: upgraded instancing buffer 'Props' to new syntax.
// define Z_TEXTURE_CHANNELS 4
// define Z_MESH_ATTRIBUTES Color
// Important!  This is a generated file, any changes will be overwritten
// when the _SfSrc sufhalf version of this shader is modified.


Shader "ProjectU/Scene/SplatAltas(4) New" {
Properties
{
	[Toggle]  _VERTEXCOLOR("使用顶点色进行多层材质混合", Float) = 0
	_Splat0("Base0 (RGBA)", 2D) = "white" {}
	_Splat1("Base1 (RGBA)", 2D) = "white" {}
	_Splat2("Base2 (RGBA)", 2D) = "white" {}
	_Splat3("Base3 (RGBA)", 2D) = "white" {}
	[Toggle]  _OPTIMIZE("打开低配", Float) = 0
	[NoScaleOffset] _BumpMap0("Normalmap 0", 2D) = "bump" {}
	[NoScaleOffset] _BumpMap1("Normalmap 1", 2D) = "bump" {}
	[NoScaleOffset] _BumpMap2("Normalmap 2", 2D) = "bump" {}
	[NoScaleOffset] _BumpMap3("Normalmap 3", 2D) = "bump" {}

	[NoScaleOffset] _Control("Control (RGBA)", 2D) = "white" {}

	_Splat0Metalic("Metalic 1",  Range(0,1)) = 0
	_Splat1Metalic("Metalic 2",  Range(0,1)) = 0
	_Splat2Metalic("Metalic 3",  Range(0,1)) = 0
	_Splat3Metalic("Metalic 4",  Range(0,1)) = 0 

	[Toggle]  _SPLATMIX("Normal Map 使用Lerp进行混合", Float) = 0

	[ToggleOff]  _SpecularHighlights("Specular Highlights", Float) = 1.000000
	[ToggleOff]  _GlossyReflections("Glossy Reflections", Float) = 1.000000
}
	SubShader{
	Tags
	{
	"RenderType" = "Opaque"
	"Queue" = "Geometry"
	"RenderPipeline" = "UniversalPipeline"
	}
	LOD 200
	
	Pass{
		Name "FORWARD"
		Blend off
		ZWrite On
		ZTest LEqual

		HLSLPROGRAM
	#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
	//#pragma surface surf Standard vertex:vert finalcolor:FinalSceneColor  fullforwardshadows noinstancing nodirlightmap nodynlightmap
	#pragma multi_compile_fog
	//#pragma multi_compile_fwdbase_fullshadows
	#pragma multi_compile _ SHADOWS_SCREEN
	#pragma multi_compile _ SHADOWS_SHADOWMASK
	#pragma multi_compile _ LIGHTMAP_SHADOWS_MIXING
	#pragma shader_feature _ _SPECULARHIGHLIGHTS_OFF
	#pragma shader_feature _ _GLOSSYREFLECTIONS_OFF
	#pragma shader_feature _ _SPLATMIX_ON
	#pragma multi_compile _ _VERTEXCOLOR_ON
	#pragma multi_compile _ _OPTIMIZE_ON
	#pragma multi_compile _ LIGHTMAP_ON
	#pragma exclude_renderers gles psp2
	//#pragma enable_d3d11_debug_symbols
	#pragma vertex vert
	#pragma fragment frag
	#pragma target 3.0
	#include "SceneCommon.cginc"

	//#include "TerrainSplatmapCommon.cginc"
	sampler2D _Splat0;
	sampler2D _BumpMap0;
	sampler2D _Splat1;
	sampler2D _BumpMap1;
	sampler2D _Splat2;
	sampler2D _BumpMap2;
	sampler2D _Splat3;
	sampler2D _BumpMap3;

	half4 _Splat0_ST;
	half4 _Splat1_ST;
	half4 _Splat2_ST;
	half4 _Splat3_ST;
	half4 _Control_ST;

	sampler2D _Control;
	half _Splat0Metalic;
	half _Splat1Metalic;
	half _Splat2Metalic;
	half _Splat3Metalic;

    struct vertexInput
	{
		float4 vertex : POSITION;
		float3 normal : NORMAL;
		float4 tangent : TANGENT;
		float2 texcoord0 : TEXCOORD0;
		float4 uv1 : TEXCOORD1;
		half4 color : COLOR;
	};

	struct vertexOutput
	{
		float4 pos : SV_POSITION;
		float2 uv_Splat0 : TEXCOORD0;
		float2 uv_Splat1 : TEXCOORD1;
		float2 uv_Splat2 : TEXCOORD2;
		float2 uv_Splat3 : TEXCOORD3;
		float2 uv_Control : TEXCOORD4;
		float4 posWorld : TEXCOORD5;
		half3 normalDir : TEXCOORD6;
#ifndef _OPTIMIZE_ON
		half3 tangentDir : TEXCOORD7;
		half3 binormalDir : TEXCOORD8;
#endif
		half4 ambientOrLightmapUV : TEXCOORD9;
		half4 vertexColor : COLOR;
	};

	void SplatmapMix(vertexOutput data,
		half4 splat_control,
		out half4 mixedDiffuse,
		out half3 mixedNormal,
		out half metalic)
	{
		mixedDiffuse = 0.0f;
		mixedNormal = 0.0f;
		metalic = 0.0f;
		half4 normal = 0.0f;
		half4 nrm = 0.0f;

		half weight = dot(splat_control, half4(1, 1, 1, 1));
		splat_control /= (weight + 1e-3f);

		mixedDiffuse += splat_control.r * tex2D(_Splat0, data.uv_Splat0);
		mixedDiffuse += splat_control.g * tex2D(_Splat1, data.uv_Splat1);
		mixedDiffuse += splat_control.b * tex2D(_Splat2, data.uv_Splat2);
		mixedDiffuse += splat_control.a * tex2D(_Splat3, data.uv_Splat3);
#ifndef _OPTIMIZE_ON
#ifndef _SPLATMIX_ON
		normal = splat_control.r * tex2D(_BumpMap0, data.uv_Splat0);
		nrm += normal;
		normal = splat_control.g * tex2D(_BumpMap1, data.uv_Splat1);
		nrm += normal;
		normal = splat_control.b * tex2D(_BumpMap2, data.uv_Splat2);
		nrm += normal;
		normal = splat_control.a * tex2D(_BumpMap3, data.uv_Splat3);
		nrm += normal;
#else
		nrm = splat_control.r * tex2D(_BumpMap0, data.uv_Splat0);
		normal = tex2D(_BumpMap1, data.uv_Splat1);
		nrm = lerp(nrm, normal, splat_control.g);
		normal = tex2D(_BumpMap2, data.uv_Splat2);
		nrm = lerp(nrm, normal, splat_control.b);
		normal = tex2D(_BumpMap3, data.uv_Splat3);
		nrm = lerp(nrm, normal, splat_control.a);
#endif		
		mixedNormal = UnpackNormal(nrm);
#endif		
		

		metalic = dot(splat_control, half4(_Splat0Metalic, _Splat1Metalic, _Splat2Metalic, _Splat3Metalic));
	}

	inline half4 ProjectU_VertexGIForward(vertexInput v, float3 posWorld, half3 normalWorld)
	{
    half4 ambientOrLightmapUV = 1;
    // Static lightmaps
    #ifdef LIGHTMAP_ON
        ambientOrLightmapUV.xy = v.uv1.xy * unity_LightmapST.xy + unity_LightmapST.zw;
        ambientOrLightmapUV.zw = 0;
    // Sample light probe for Dynamic objects only (no static or dynamic lightmaps)
    #elif UNITY_SHOULD_SAMPLE_SH
        #ifdef VERTEXLIGHT_ON
            // Approximated illumination from non-important point lights
            ambientOrLightmapUV.rgb = Shade4PointLights (
                unity_4LightPosX0, unity_4LightPosY0, unity_4LightPosZ0,
                unity_LightColor[0].rgb, unity_LightColor[1].rgb, unity_LightColor[2].rgb, unity_LightColor[3].rgb,
                unity_4LightAtten0, posWorld, normalWorld);
        #endif

        ambientOrLightmapUV.rgb = ShadeSHPerVertex (normalWorld, ambientOrLightmapUV.rgb);
    #endif

    return ambientOrLightmapUV;
	}

	vertexOutput vert(vertexInput v)
	{
		vertexOutput o = (vertexOutput)0;
		o.uv_Splat0 = TRANSFORM_TEX(v.texcoord0, _Splat0);
		o.uv_Splat1 = TRANSFORM_TEX(v.texcoord0, _Splat1);
		o.uv_Splat2 = TRANSFORM_TEX(v.texcoord0, _Splat2);
		o.uv_Splat3 = TRANSFORM_TEX(v.texcoord0, _Splat3);
		o.uv_Control = TRANSFORM_TEX(v.texcoord0, _Control);

		o.normalDir = TransformObjectToWorldNormal(v.normal);
#ifndef _OPTIMIZE_ON
		//zli@BlackJack: replace with unity built in function: UnityObjectToWorldDir
		//o.tangentDir = normalize(mul(unity_ObjectToWorld, float4(v.tangent.xyz, 0.0)).xyz);
		o.tangentDir = TransformObjectToWorldDir(v.tangent.xyz);

		//zli@BlackJack: In case we have an odd-negative mirrored object, then its binormal should be flipped, this could be handled by multiplying the unity_WorldTransformParams.w
		o.binormalDir = normalize(cross(o.normalDir, o.tangentDir) * v.tangent.w * unity_WorldTransformParams.w);
#endif		
		o.posWorld = mul(unity_ObjectToWorld, v.vertex);
		o.pos = TransformObjectToHClip(v.vertex);
		o.vertexColor = v.color;
		o.ambientOrLightmapUV = ProjectU_VertexGIForward(v, o.posWorld, o.normalDir);
		return o;
	}

	half4 frag(vertexOutput i) : Color
	{
		half4 mixed;
		half3 normal;
		half metallic;
		
		half4 splat_control;
		#ifndef _VERTEXCOLOR_ON
			splat_control= tex2D(_Control, i.uv_Control);
		#else
			splat_control.gba  = i.vertexColor;
            splat_control.r = saturate(1 - splat_control.g - splat_control.b - splat_control.a); //TODO: fix me, this will not allow any white color
		#endif
		
		SplatmapMix(i, splat_control, mixed, normal, metallic);
        half3 diffuseColor = mixed.rgb;
		half smoothness = mixed.a;
		
        half occlusion = 1.0;
		i.normalDir = normalize(i.normalDir);
		float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
#ifndef _OPTIMIZE_ON
		half3x3 tangentTransform = half3x3(i.tangentDir, i.binormalDir, i.normalDir);
		float3 normalDirection = normalize(mul(normal, tangentTransform)); // Perturbed normals
#else
		float3 normalDirection = i.normalDir;
#endif
		half3 viewReflectDirection = reflect(-viewDirection, normalDirection);


		half3 specularColor;
		half specularMonochrome;

		return float4(diffuseColor,1.0f);

		//diffuseColor = DiffuseAndSpecularFromMetallic(diffuseColor, metallic, specularColor, specularMonochrome);

		////// Lighting:
		//half attenuation = LIGHT_ATTENUATION(i);

		/////// GI Data:
	// 	UnityLight light = MainLight();


	// 	//FragmentGI
	// 	UnityGIInput d;
	// 	d.light = light;
	// 	d.worldPos = i.posWorld.xyz;
	// 	d.worldViewDir = viewDirection;
	// 	d.atten = attenuation;
	// 	#if defined(LIGHTMAP_ON) || defined(DYNAMICLIGHTMAP_ON)
	// 		d.ambient = 0;
	// 		d.lightmapUV = i.ambientOrLightmapUV;
	// 	#else
	// 		d.ambient = i.ambientOrLightmapUV.rgb;
	// 		d.lightmapUV = 0;
	// 	#endif
	// 	d.probeHDR[0] = unity_SpecCube0_HDR;
	// 	d.probeHDR[1] = unity_SpecCube1_HDR;
 //    //return float4(d.lightmapUV.xy, 0.0, 1.0);
	// #if UNITY_SPECCUBE_BLENDING || UNITY_SPECCUBE_BOX_PROJECTION
	// 	d.boxMin[0] = unity_SpecCube0_BoxMin;
	// #endif
	// #if UNITY_SPECCUBE_BOX_PROJECTION
	// 	d.boxMax[0] = unity_SpecCube0_BoxMax;
	// 	d.boxMax[1] = unity_SpecCube1_BoxMax;
	// 	d.probePosition[0] = unity_SpecCube0_ProbePosition;
	// 	d.probePosition[1] = unity_SpecCube1_ProbePosition;
	// 	d.boxMin[1] = unity_SpecCube1_BoxMin;
	// #endif

 //        //reflection
	// 	Unity_GlossyEnvironmentData ugls_en_data = UnityGlossyEnvironmentSetup(smoothness, viewDirection, normalDirection, specularColor);

	// 	ugls_en_data.reflUVW = viewReflectDirection;
		
	// 	UnityGI gi = UnityGlobalIllumination(d, occlusion, normalDirection, ugls_en_data);
 //        half4 diffuse = BRDF2_Unity_PBS(diffuseColor, specularColor, specularMonochrome, smoothness, normalDirection, viewDirection, gi.light, gi.indirect, attenuation);
//		return float4(diffuse.xyz, 1);
        //return float4(gi.indirect.diffuse, 1.0);

		// diffuse.a = 1;

		// UNITY_APPLY_FOG(i.fogCoord, diffuse);
		// diffuse.rgb = CommonFinalSceneColorWorldPos(diffuse.rgb, i.posWorld);
		// UNITY_OPAQUE_ALPHA(diffuse.a);
		// return diffuse;

	}

	ENDHLSL
	}
	
}
}

// Upgrade NOTE: upgraded instancing buffer 'Props' to new syntax.
// define Z_TEXTURE_CHANNELS 4
// define Z_MESH_ATTRIBUTES Color
// Important!  This is a generated file, any changes will be overwritten
// when the _SfSrc sufhalf version of this shader is modified.


// Shader "ProjectU/Scene/SplatAltas(4)" {
// 	Properties{
// 		_Splat0("Base0 (RGBA)", 2D) = "white" {}
// 		_Splat1("Base1 (RGBA)", 2D) = "white" {}
// 		_Splat2("Base2 (RGBA)", 2D) = "white" {}
// 		_Splat3("Base3 (RGBA)", 2D) = "white" {}
// 		[NoScaleOffset] _BumpMap0("Normalmap 0", 2D) = "bump" {}
// 		[NoScaleOffset] _BumpMap1("Normalmap 1", 2D) = "bump" {}
// 		[NoScaleOffset] _BumpMap2("Normalmap 2", 2D) = "bump" {}
// 		[NoScaleOffset] _BumpMap3("Normalmap 3", 2D) = "bump" {}
// 		[NoScaleOffset] _Control("Control (RGBA)", 2D) = "white" {}

// 		_Splat0Metalic("Metalic 1",  Range(0,1)) = 0
// 		_Splat1Metalic("Metalic 2",  Range(0,1)) = 0
// 		_Splat2Metalic("Metalic 3",  Range(0,1)) = 0
// 		_Splat3Metalic("Metalic 4",  Range(0,1)) = 0
// 	}
// 		SubShader{
// 		Tags{
// 		"RenderType" = "Opaque"
// 		"Queue" = "Geometry"
// 	}
// 		//LOD 200
// 		//CGINCLUDE
// 		//#undef UNITY_BRDF_PBS
// 		//#define UNITY_BRDF_PBS BRDF3_Unity_PBS
// 		////#define UNITY_COLORSPACE_GAMMA 1
// 		//ENDCG
// 		CGPROGRAM
// 		#pragma surface surf Standard vertex:vert finalcolor:FinalSceneColor  fullforwardshadows noinstancing nodirlightmap
// 		#pragma multi_compile_fog
// 		#pragma exclude_renderers gles psp2
// 		//#pragma enable_d3d11_debug_symbols
// 		#pragma target 3.0

// 		//#include "TerrainSplatmapCommon.cginc"
// 	sampler2D _Splat0;
// 	sampler2D _BumpMap0;
// 	sampler2D _Splat1;
// 	sampler2D _BumpMap1;
// 	sampler2D _Splat2;
// 	sampler2D _BumpMap2;
// 	sampler2D _Splat3;
// 	sampler2D _BumpMap3;

// 	sampler2D _Control;
// 	half _Splat0Metalic;
// 	half _Splat1Metalic;
// 	half _Splat2Metalic;
// 	half _Splat3Metalic;

// 	struct Input {
// 		float2 uv_Splat0 : TEXCOORD0;
// 		float2 uv_Splat1 : TEXCOORD1;
// 		float2 uv_Splat2 : TEXCOORD2;
// 		float2 uv_Splat3 : TEXCOORD3;
// 		float2 uv_Control : TEXCOORD4;
// 		float3 worldPos : TEXCOORD5;
// 		UNITY_FOG_COORDS(6)
// 	};

// 	void vert(inout appdata_full v, out Input o)
// 	{
// 		UNITY_INITIALIZE_OUTPUT(Input, o);
// 		float4 pos = UnityObjectToClipPos(v.vertex);
// 		UNITY_TRANSFER_FOG(o, pos);
// 	}

// 	void SplatmapMix(Input data,
// 		out half4 mixedDiffuse,
// 		out half3 mixedNormal,
// 		out half metalic)
// 	{
// 		mixedDiffuse = 0.0f;
// 		mixedNormal = 0.0f;
// 		metalic = 0.0f;
// 		half4 normal = 0.0f;
// 		half4 nrm = 0.0f;

// 		half4 splat_control = tex2D(_Control, data.uv_Control);
// 		half weight = dot(splat_control, half4(1, 1, 1, 1));
// 		splat_control /= (weight + 1e-3f);

// 		mixedDiffuse += splat_control.r * tex2D(_Splat0, data.uv_Splat0);
// 		mixedDiffuse += splat_control.g * tex2D(_Splat1, data.uv_Splat1);
// 		mixedDiffuse += splat_control.b * tex2D(_Splat2, data.uv_Splat2);
// 		mixedDiffuse += splat_control.a * tex2D(_Splat3, data.uv_Splat3);

// 		// mixedDiffuse = splat_control.r * tex2D(_Splat0, data.uv_Splat0);
// 		// mixedDiffuse = lerp(mixedDiffuse, tex2D(_Splat1, data.uv_Splat1), splat_control.g);
// 		// mixedDiffuse = lerp(mixedDiffuse, tex2D(_Splat2, data.uv_Splat2), splat_control.b);
// 		// mixedDiffuse = lerp(mixedDiffuse, tex2D(_Splat3, data.uv_Splat3), splat_control.a);

// 		// normal = splat_control.r * tex2D(_BumpMap0, data.uv_Splat0);
// 		// nrm += normal;
// 		// normal = splat_control.g * tex2D(_BumpMap1, data.uv_Splat1);
// 		// nrm += normal;
// 		// normal = splat_control.b * tex2D(_BumpMap2, data.uv_Splat2);
// 		// nrm += normal;
// 		// normal = splat_control.a * tex2D(_BumpMap3, data.uv_Splat3);
// 		// nrm += normal;

// 		nrm = splat_control.r * tex2D(_BumpMap0, data.uv_Splat0);
// 		normal = tex2D(_BumpMap1, data.uv_Splat1);
// 		nrm = lerp(nrm, normal, splat_control.g);
// 		normal = tex2D(_BumpMap2, data.uv_Splat2);
// 		nrm = lerp(nrm, normal, splat_control.b);
// 		normal = tex2D(_BumpMap3, data.uv_Splat3);
// 		nrm = lerp(nrm, normal, splat_control.a);
		

// 		mixedNormal = UnpackNormal(nrm);

// 		metalic = dot(splat_control, half4(_Splat0Metalic, _Splat1Metalic, _Splat2Metalic, _Splat3Metalic));
// 	}

// #include "SceneCommon.cginc"
// 	void FinalSceneColor(Input IN, SurfaceOutputStandard o, inout half4 color)
// 	{
// 		UNITY_APPLY_FOG(IN.fogCoord, color);
// 		color.rgb = CommonFinalSceneColorWorldPos(color.rgb, IN.worldPos);
// 	}

// 	void surf(Input IN, inout SurfaceOutputStandard o) {
// 		half4 mixed;
// 		half3 normal;
// 		half metallic;
// 		half smoothness;
// 		SplatmapMix(IN, mixed, normal, metallic);
// 		o.Albedo = mixed.xyz;
// 		o.Normal = normal;
// 		o.Metallic = metallic;
// 		o.Smoothness = mixed.a;

// 	}

// 	ENDCG
// 	}
// }