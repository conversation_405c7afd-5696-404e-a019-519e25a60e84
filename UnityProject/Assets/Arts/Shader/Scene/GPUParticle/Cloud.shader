Shader "ProjectU/Scene/GPUParticle/Cloud"
{
	Properties
	{
		[Toggle]_SHADOW_RECEIVE("接收阴影",Float) = 0
		[Toggle]_FOG("雾",Float) = 0
		_MainTex ("Texture", 2D) = "white" {}
		_MainTexScale("Texture RGB Scale", Range(0,10)) = 1

		_Shadow("阴影亮度",Range(0,1)) = 0
		_ShadowCasterThreshold("投射阴影Alpha阈值",Range(0,1)) = 0
	}
	SubShader
	{
		Tags { "RenderType"="Transparent" "Queue"="Transparent"}
		LOD 100

		Pass
		{
            Name "FORWARD"
            Tags { "LightMode" = "ForwardBase" "PassFlags"="OnlyDirectional"}
			Blend SrcAlpha OneMinusSrcAlpha
			Cull Off
			ZWrite Off

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			#pragma shader_feature _SHADOW_RECEIVE_ON
			#pragma shader_feature _FOG_ON
			// make fog work
			#pragma multi_compile_fog
			#pragma multi_compile_fwdbase

			#if _SHADOW_RECEIVE_ON
				#define SHADOWS_SCREEN
				#define UNITY_NO_SCREENSPACE_SHADOWS
			#endif
			
			#include "GPUParticle.cginc"

			struct v2f
			{
				float4 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
				half3 worldNormal : TEXCOORD1;
				#if _FOG_ON
					UNITY_FOG_COORDS(2)
				#endif
				#if _SHADOW_RECEIVE_ON
					SHADOW_COORDS(3)
				#endif
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;
			half _MainTexScale;
			half _Shadow;
			
			v2f vert (GPUParticleVSInput vsInput)
			{
				v2f o;

				//通用数据计算
				GPUParticleVertex vertex = decodeVSInput(vsInput);
				GPUParticleVSResult result = calculateVSResult(vertex);
				o.vertex = result.mClipSpacePosition;

				//使用剩余生命控制alpha[0-1-0]
				o.uv.zw = 1 - abs(result.mLeftLifeScalar - 0.5) * 2;

				o.uv.xy = TRANSFORM_TEX(result.mUV, _MainTex);

				o.worldNormal = normalize(result.mNodeSpacePosition);

				#if _SHADOW_RECEIVE_ON
					o._ShadowCoord = mul(unity_WorldToShadow[0], float4(result.mWorldSpacePosition,1));
				#endif
				#if _FOG_ON
					UNITY_TRANSFER_FOG(o,o.vertex);
				#endif
				return o;
			}
			
			fixed4 frag (v2f i) : SV_Target
			{
				// sample the texture
				fixed4 main_tex = tex2D(_MainTex, i.uv);
#if _SHADOW_RECEIVE_ON
				half shadow = SHADOW_ATTENUATION(i);
#else
				half shadow = 1.0;
#endif

				//把纹理rgb颜色缩放处理,因为我本地图片原因,我不得不加上这个处理,推荐这一步在制作图片的时候就处理好
				main_tex.rgb *= _MainTexScale;
				
				//简单的光照
				half n_dot_l = dot(i.worldNormal,_WorldSpaceLightPos0.xyz);
				half3 light_diffuse = max(0,n_dot_l) * _LightColor0.rgb;
				
				light_diffuse *= lerp(_Shadow,1,shadow);
				
				light_diffuse += UNITY_LIGHTMODEL_AMBIENT.xyz;

				fixed4 col = saturate(main_tex);
				col.rgb *= light_diffuse;

				col.a *= i.uv.z;

				#if _FOG_ON
					UNITY_APPLY_FOG(i.fogCoord, col);
				#endif

				return col;
			}
			ENDCG
		}
		Pass
		{
            Tags { "LightMode" = "ShadowCaster"}

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag

			#include "GPUParticle.cginc"

			struct v2f
			{
				float4 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;
			half _ShadowCasterThreshold;

			v2f vert (GPUParticleVSInput vsInput)
			{
				v2f o;

				//通用数据计算
				GPUParticleVertex vertex = decodeVSInput(vsInput);
				GPUParticleVSResult result = calculateVSResult(vertex);
				o.vertex = result.mClipSpacePosition;

				//使用剩余生命控制alpha[0-1-0]
				o.uv.zw = 1 - abs(result.mLeftLifeScalar - 0.5) * 2;

				o.uv.xy = TRANSFORM_TEX(result.mUV, _MainTex);

				return o;
			}
			fixed4 frag (v2f i) : SV_Target
			{
				// sample the texture
				fixed4 main_tex = tex2D(_MainTex, i.uv);

				fixed4 col = main_tex;
				col.a *= i.uv.z;

				if(col.a <= _ShadowCasterThreshold)
				{
					discard;
				}

				return col;
			}
			ENDCG
		}
	}
}
