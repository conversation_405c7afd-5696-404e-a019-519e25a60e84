struct appdata
{
	float4 vertex : POSITION;
	float2 texCoord : TEXCOORD0;
	fixed4 color : COLOR;
	float3 normal : NORMAL;
};

struct v2f
{
	float4 pos : SV_POSITION;
	float2 uv : TEXCOORD0;
	fixed4 color : COLOR;
	float3 wnormal : NORMAL;
	float4 wpos : TEXCOORD1;
	half3 faceMapParams : TEXCOORD2;//xy:uv  z:lightingThreshold 
#if _SHADERSH_ON					
	float4 shColor : TEXCOORD3;
	UNITY_FOG_COORDS(4)
	#if _ECLIPSE_ON ||_MATCAP_ON
		float4 uv1 : TEXCOORD5; //xy:matcap uv  zw:eclipse uv
	#endif
#else
	UNITY_FOG_COORDS(3)
	#if _ECLIPSE_ON || _MATCAP_ON
		float4 uv1 : TEXCOORD4;  //xy:matcap uv  zw:eclipse uv
	#endif
#endif					
};

fixed4 _Color;
sampler2D _MainTex;
float4 _MainTex_ST;
sampler2D _Mask1;
sampler2D _SSSTex;
fixed _ShadowContrast;
sampler2D _EmissionTex;
half4 _EmissionColor;
#if _MATCAP_ON
	sampler2D _MatCap;
#endif

float4 _SpecBright;
float4 _SpecLight;
float _SpecLength;
float _SpecThreshold;
float _specInShadow;

fixed4 _RimColor;
fixed _RimMin;
fixed _RimMax;
float4 _RimDir;
uniform float3 _Face_LightDir;
#if _SPECULAR_STEP || _SPECULAR_SMOOTH
	sampler2D _SpecTex;
	float _Shininess, _SpecStep, _SpecPow;
#endif

#if _ECLIPSE_ON
	sampler2D _EclipseTex;
	float4 _EclipseTex_ST;
	float _EclipseIntensity;
	float4 _EclipseColor;
#endif

float _Speed;
float _Radio;
float3 _Global_SColor;
float _GlobalSHRatio;

half diffuseThreshold;
half gAmbientRatio;

#define RAD2DEG 57.29578
#define THRESHOLD_MIN 0.0001f
#define THRESHOLD_MAX 1.0f
#define FIXFACE 0.00001f


half3 CalculateFaceMapParams(half3 mixLocalDir, half2 uv)
{
	bool flipInYAxis = false;

	// half curDeltaAngle = -atan2(-lightLocalDir.x, lerp(-lightLocalDir.y, dir, _MixDir) ) * RAD2DEG;
	half curDeltaAngle = -atan2(-mixLocalDir.x, -mixLocalDir.y) * RAD2DEG;

	if (curDeltaAngle > 0) 
	{
		flipInYAxis = true;
	};

	half lightingThreshold = 0.0f; // Lighting threshold arount face y axis, 0 when lighting from face forward direction, 1 when from backward direction, ranging 180 degrees
	lightingThreshold = abs(curDeltaAngle / 180.0);

	lightingThreshold = clamp(lightingThreshold, THRESHOLD_MIN, THRESHOLD_MAX);
			
	if (flipInYAxis) 
	{
		uv = uv * half2(-1.0, 1.0) + half2(1.0, 0.0);
	};

	return half3(uv, lightingThreshold);
}

v2f VertBase(appdata v)
{
	v2f o = (v2f)(0);
	o.pos = UnityObjectToClipPos(v.vertex);
	o.wnormal = UnityObjectToWorldNormal(v.normal);
	half4 wpos = mul(unity_ObjectToWorld, v.vertex);
	o.wpos.xyz = wpos.xyz / wpos.w;
	o.uv = TRANSFORM_TEX(v.texCoord, _MainTex);
	/////////////////////////////for face//////////////////////////////////////////////////////////////////////////////////////
	half3 lightWorldDir = normalize(UnityWorldSpaceLightDir(wpos));
	half3 lightLocalDir = half3(0,0,0);
	half3 mixLocalDir = half3(0,0,0);
	half tmpvar_14 = sqrt(dot(lightWorldDir, lightWorldDir));
	half3 tmpvar_11;
	
	{
		///元神 调整角度旋转矩阵（x轴，因为元神的角色object坐标是在x轴向上）
		// float3x3 mat3 = float3x3(1.0, 0.0, 0.0, 0.0, 0.98481, -0.17365, 0.0, 0.17365, 0.98481);
		// float3x3 mat3 = float3x3(1.0, 0.0, 0.0, 0.0, 0.17365, -0.98481, 0.0, 0.98481, 0.17365);
		// float3x3 mat3_1 = float3x3(1.0, 0.0, 0.0, 0.0, 0.93969, -0.34202, 0, 0.34202, 0.93969);
		// lightWorldDir = mul(lightWorldDir, mat3);
		// lightWorldDir = mul(lightWorldDir, mat3_1);
		//ProjectU 调整角度旋转矩阵（z轴，因为ProjectU的角色object坐标是在z轴向上）
		// float3x3 matFix = float3x3(   0.98481, -0.17365, 0.0,
		// 					0.17365,  0.98481, 0.0,
		// 					0.0    ,  0.0    , 1.0 );
		lightLocalDir = mul(unity_WorldToObject, lightWorldDir);
		mixLocalDir = mul(unity_WorldToObject, _Face_LightDir);
	}
	o.faceMapParams = CalculateFaceMapParams(mixLocalDir, v.texCoord);
	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#if _MATCAP_ON
	o.uv1.x = dot(normalize(UNITY_MATRIX_IT_MV[0].xyz), v.normal);
	o.uv1.y = dot(normalize(UNITY_MATRIX_IT_MV[1].xyz), v.normal);
	o.uv1.xy = o.uv1 * 0.5 + 0.5;
#endif

#if _ECLIPSE_ON
	//o.uv1.zw = TRANSFORM_TEX(v.texCoord, _EclipseTex).xy;
	o.uv1.zw = v.texCoord*_EclipseTex_ST.xy + _Time.y*_EclipseTex_ST.zw;
#endif
	o.color = v.color;

#if _SHADERSH_ON					
	o.shColor.rgb = ShadeSH9(float4(o.wnormal, 1));
#endif					
	if (_Radio > 0.1)
		o.color.a = (sin(_Time.z*_Speed) + 1.0) * 0.5;
	//TRANSFER_SHADOW(o);
	UNITY_TRANSFER_FOG(o, o.pos);
	return o;
}

fixed4 Diffuse(v2f i, half3 lightDir, half3 viewDir, out half shadowContrast, out fixed4 maskTex, out fixed4 shadowColor)
{
	fixed4 finCol;
	maskTex = tex2D(_Mask1, i.uv);

	//old
	//fixed shadowThreshold = maskTex.b;
	fixed shadowThreshold = maskTex.g;
	shadowThreshold *= i.color.r;

	shadowThreshold = 1 - shadowThreshold + _ShadowContrast;
	fixed newShadowThreshold = shadowThreshold * 10.0f;
	//shadowThreshold = lerp(newShadowThreshold,shadowThreshold , maskTex.g);
	if(maskTex.g < 0.05) shadowThreshold = newShadowThreshold;

	half3 normalDir = i.wnormal;

	half NdotL = dot(normalDir, lightDir);

	shadowContrast = step(shadowThreshold, NdotL);
#if _SHADERSH_ON					
	//return fixed4(i.color.a,i.color.a,i.color.a,1);
	//return i.shColor;
	//return fixed4(defaultLightColor, 1.0);
	fixed4 defaultLightColor = saturate(i.shColor - fixed4(_Global_SColor, 1.0));				

	fixed4 mainTex = tex2D(_MainTex, i.uv) + defaultLightColor * _GlobalSHRatio;
	fixed4 sssTex = (tex2D(_SSSTex, i.uv) + defaultLightColor * _GlobalSHRatio);
#else
	fixed4 mainTex = tex2D(_MainTex, i.uv);
	fixed4 sssTex = tex2D(_SSSTex, i.uv);
#endif			
	finCol = mainTex;
	//half shadow = SHADOW_ATTENUATION(i);
	//shadowContrast = min(shadowContrast, shadow);
	fixed3 brightCol = mainTex.rgb;
	fixed3 shadowCol = mainTex.rgb * sssTex.rgb;
	shadowColor = fixed4(shadowCol, 1.0);
	finCol.rgb = lerp(shadowCol, brightCol, shadowContrast);
#if _ECLIPSE_ON
	//return fixed4(i.color.rgb, 1.0);
	fixed4 eclipseTex = tex2D(_EclipseTex, i.uv1.zw);
	fixed4 eclipseTexShadow = eclipseTex * 0.5;
	eclipseTex.rgb = lerp(eclipseTexShadow.rgb, eclipseTex.rgb, shadowContrast)*2.0 + eclipseTex.a*_EclipseColor.rgb;
	//half eclipseRatio = i.color.g * _EclipseIntensity;
	//finCol.rgb = lerp(finCol.rgb, eclipseTex.rgb, eclipseRatio);

	half eclipseRatio = step(0.15, i.color.g * _EclipseIntensity);
	//return fixed4(eclipseRatio, eclipseRatio, eclipseRatio, 1.0);
	finCol.rgb = lerp(finCol.rgb, eclipseTex.rgb, eclipseRatio);

#endif
	return fixed4(finCol.rgb, mainTex.a);
}

half4 FaceDiffuse(v2f i)
{
	//half4 finCol;

#if _SHADERSH_ON					
	fixed4 defaultLightColor = saturate(i.shColor - fixed4(_Global_SColor, 1.0));				

	fixed4 mainTex = tex2D(_MainTex, i.uv) + defaultLightColor;
	fixed4 sssTex = (tex2D(_SSSTex, i.uv) + defaultLightColor);
#else
	fixed4 mainTex = tex2D(_MainTex, i.uv);
	fixed4 sssTex = tex2D(_SSSTex, i.uv);
#endif
			
	half4 finCol = mainTex;

	half3 brightCol = mainTex.rgb;
	half3 shadowCol = mainTex.rgb * sssTex.rgb;
	//shadowColor = fixed4(shadowCol, 1.0);
	half diffuseThreshold =  tex2D(_MainTex, i.faceMapParams.xy).w;  ///////放在basecolor的A通道里，设置为RGBA 32,配合影图在SSS的A通道里使用

	half lightingThreshold = i.faceMapParams.z;

	half Factor0 = max(lightingThreshold , THRESHOLD_MIN);
	half Factor1 = (diffuseThreshold - Factor0) / (( min (lightingThreshold , THRESHOLD_MAX) - Factor0) + FIXFACE);
	Factor1 = clamp (Factor1 , 0.0 , 1.0 ) ;
	
	half shadowContrast = (Factor1 * Factor1 * (3.0 - 2.0 * Factor1)) * sssTex.a*2;

	finCol.rgb = lerp(shadowCol, brightCol, shadowContrast);

	#if _ECLIPSE_ON
	//return fixed4(i.color.rgb, 1.0);
	fixed4 eclipseTex = tex2D(_EclipseTex, i.uv1.zw);
	fixed4 eclipseTexShadow = eclipseTex * 0.5;
	eclipseTex.rgb = lerp(eclipseTexShadow.rgb, eclipseTex.rgb, shadowContrast)*2.0 + eclipseTex.a*_EclipseColor.rgb;
	//half eclipseRatio = i.color.g * _EclipseIntensity;
	//finCol.rgb = lerp(finCol.rgb, eclipseTex.rgb, eclipseRatio);

	half eclipseRatio = step(0.15, i.color.g * _EclipseIntensity);
	//return fixed4(eclipseRatio, eclipseRatio, eclipseRatio, 1.0);
	finCol.rgb = lerp(finCol.rgb, eclipseTex.rgb, eclipseRatio);
	#endif

	return half4(finCol.rgb, mainTex.a);
}

fixed4 DiffuseILM(v2f i, half3 lightDir, half3 viewDir, out half shadowContrast, out fixed4 shadowColor)
{
	half2 uv0 = i.uv;
	float3 L = normalize(lightDir.xyz);
	float3 V = normalize(viewDir);
	float3 H = normalize(L + V);
	float3 N = normalize(i.wnormal);

	float4 ILMTex = tex2D(_Mask1, uv0);
	float halfLambert = dot(N, L) * 0.5 + 0.5;
	half shadowThreshold = 1 - ILMTex.g + _ShadowContrast;
	fixed newShadowThreshold = shadowThreshold * 10.0f;
	if (ILMTex.g < 0.05) shadowThreshold = newShadowThreshold;

	shadowContrast = step(shadowThreshold, halfLambert);
	
#if _SHADERSH_ON					
	fixed4 defaultLightColor = saturate(i.shColor - fixed4(_Global_SColor, 1.0));				

	fixed4 baseColor = tex2D(_MainTex, uv0) + defaultLightColor * _GlobalSHRatio;
	fixed4 sssTex = (tex2D(_SSSTex, uv0) + defaultLightColor * _GlobalSHRatio);
#else
	fixed4 baseColor = tex2D(_MainTex, uv0);
	fixed4 sssTex = tex2D(_SSSTex, uv0);
#endif
	float3 sssColor = baseColor * sssTex;
	shadowColor = fixed4(sssColor, 1);

	float3 NV = mul(UNITY_MATRIX_V, N);
	float3 HV = mul(UNITY_MATRIX_V, H);

	float NdotH = dot(normalize(NV.xz), normalize(HV.xz));// xzͶӰ��NdotH

	NdotH = pow(NdotH, 1 / _SpecLength);

	float specFeather = /* _SpecFeather * */ NdotH;
	float StepMax = 1.0;//saturate(1 - NdotH + specFeather);
	float StepMin = saturate(1 - NdotH - specFeather);
	float3 brightColor = smoothstep(StepMin, StepMax, ILMTex.b) *   _SpecBright.rgb * ILMTex.r;
	float3 lightColor = smoothstep(_SpecThreshold, 1, ILMTex.b) *  _SpecLight.rgb * ILMTex.r;
	float4 finalColor = fixed4(1,1,1,1);
	/*return fixed4(lightColor.rgb, 1);
	return fixed4(NdotH, NdotH, NdotH, 1);*/
	//half3 ambient = baseColor * UNITY_LIGHTMODEL_AMBIENT  * ILMTex.a;
	half3 specColor;
	finalColor.rgb = lerp(sssColor, baseColor, shadowContrast) * ILMTex.a;
	//return fixed4(sssColor.xyz,1);
	specColor = (brightColor + lightColor) * lerp(_specInShadow, 1, shadowContrast);
	finalColor.rgb = (finalColor.rgb + specColor)  * (ILMTex.a);

	#if _ECLIPSE_ON
	//return fixed4(i.color.rgb, 1.0);
	fixed4 eclipseTex = tex2D(_EclipseTex, i.uv1.zw);
	fixed4 eclipseTexShadow = eclipseTex * 0.5;
	eclipseTex.rgb = lerp(eclipseTexShadow.rgb, eclipseTex.rgb, shadowContrast)*2.0 + eclipseTex.a*_EclipseColor.rgb;
	//half eclipseRatio = i.color.g * _EclipseIntensity;
	//finCol.rgb = lerp(finCol.rgb, eclipseTex.rgb, eclipseRatio);

	half eclipseRatio = step(0.15, i.color.g * _EclipseIntensity);
	//return fixed4(eclipseRatio, eclipseRatio, eclipseRatio, 1.0);
	finalColor.rgb = lerp(finalColor.rgb, eclipseTex.rgb, eclipseRatio);
	#endif

	return finalColor;
}

#if _SPECULAR_STEP || _SPECULAR_SMOOTH
fixed4 Specular(v2f i, half3 lightDir, half3 viewDir, half4 maskTex, half shadowContrast)
{
	half3 halfDir = normalize(viewDir + lightDir);
	half NdotH = max(0, dot(i.wnormal, halfDir));

	fixed4 specTex = tex2D(_SpecTex, i.uv);
	return specTex * _SpecPow * step(_SpecStep, maskTex.g*pow(NdotH, _Shininess * maskTex.r * 128)) * shadowContrast;

}

fixed4 SpecularWithoutSpecTex(v2f i, half3 lightDir, half3 viewDir, half4 maskTex, half shadowContrast)
{
	half3 halfDir = normalize(viewDir + lightDir);
	half NdotH = max(0.001, dot(normalize(i.wnormal), halfDir));
#if _SPECULAR_SMOOTH
	return _SpecPow * maskTex.b * pow(NdotH, _Shininess * maskTex.r * 128) * max(0.001f,shadowContrast);
#elif _SPECULAR_STEP
	return _SpecPow * step(_SpecStep, maskTex.b*pow(NdotH, _Shininess * maskTex.r * 128)) * max(0.001f,shadowContrast);
#endif
}
#endif

fixed4 Rim(v2f i, half3 lightDir, half3 viewDir)
{
	//Rim
	//修复 PROU-31038 iPhone6s设备，部分美术资源显示黑色
	_RimDir.z += 0.01;
	half rim = (1.0f - max(0.001, dot(viewDir, i.wnormal)))*max(0.001, dot(i.wnormal, normalize(_RimDir.xyz)));
	rim = smoothstep(_RimMin, _RimMax, rim);
	return _RimColor*(rim * _RimColor.a);
}

#if _MATCAP_ON
fixed4 MatCap(v2f i, fixed4 maskTex)
{
	half2 matcapUV = i.uv1.xy;
	fixed4 matcap = tex2D(_MatCap, matcapUV.xy);
	//old
	//return matcap * maskTex.r;
	return matcap * maskTex.b;
}
#endif

#if _EMISSION_ON
fixed4 Emission(v2f i)
{
	fixed4 emissiveColor = tex2D(_EmissionTex, i.uv);
	return emissiveColor * _EmissionColor * _EmissionColor.a;
}
#endif
