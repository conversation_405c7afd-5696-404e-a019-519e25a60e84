// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Toony Colors Pro 2/UseMatcapEmi_Rim_ILM Dissolve GGX CG"
{
	Properties
	{
		[Header(Common Settings)]
		_Color("Color", Color) = (1,1,1,1)
		//DIFFUSE
		_MainTex("Main Texture", 2D) = "white" {}

		_SSSTex("SSS Texture", 2D) = "white" {}
		_ShadowContrast("Shadow Contrast", Range(-2,1)) = 1
		_Mask1("Mask 1 (ILM)", 2D) = "white" {}

		[Header(Specular Settings)]
		_SpecBright("Bright SpecColor（高光颜色）", Color) = (1, 1, 1, 1)
		_SpecLight("Light SpecColor（次高光颜色）", Color) = (1, 1, 1, 1)
		_SpecLength("SpecLength（高亮范围）", Range(0.001, 5)) = 0.5
		_SpecThreshold("SpecThreshold（次高光范围）", Range(0, 1)) = 0.1
		_specInShadow("Intensity In Shadow（阴影下高光强度）", Range(0, 1)) = 0.2
		//ECLIPSE
		[Header(Eclipse Settings)]
		[Toggle(_ECLIPSE_ON)] _ECLIPSE("Toggle Eclipse", Float) = 0
		_EclipseTex("Eclipse Texture (RGB)", 2D) = "black" {}
		_EclipseIntensity("Eclipse Intensity",Range(0,1)) = 1
		[HDR] _EclipseColor("Eclipse Color", Color) = (1,1,1,1.0)
		[Header(Rimlight Settings)]
		//RIM LIGHT
		_RimColor("Rim Color", Color) = (0.8,0.8,0.8,0.6)
		_RimMin("Rim Min", Range(0,1)) = 0.5
		_RimMax("Rim Max", Range(0,1)) = 1.0
		_RimDir("_RimDir",Vector) = (1,1,1,1)

		//Dissolve
		_Progress("Progress",Range(0,1)) = 0
		_DissolveTex("Dissolve Texture", 2D) = "white" {}
		//_Edge("Edge",Range(0.01,0.5)) = 0.01
		[NoScaleOffset] _EdgeAroundRamp("Edge Ramp", 2D) = "white" {}
		_EdgeAround("Edge Color Range",Range(0,0.5)) = 0
		_EdgeAroundHDR("Edge Color HDR",Range(1,3)) = 1	
		//[KeywordEnum(Off, On)] _SHADERSH("Toggle ShaderSH",  Float) = 0	
	}

		SubShader
		{
			Tags { "RenderType" = "Opaque" }
			Pass
			{
				Name "BASE"
				Tags{"LightMode" = "ForwardBase" "RenderType" = "Opaque"}
				
				Stencil
				{
					Ref 1
					Comp Always
					Pass replace
				}

				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				//#pragma multi_compile_fwdbase
				#pragma multi_compile_fog
				#pragma multi_compile __ _SHADERSH_ON
				#pragma multi_compile __ _ECLIPSE_ON

				//#pragma enable_d3d11_debug_symbols
				#include "UnityCG.cginc"
				#include "AutoLight.cginc"
				#include "GGX_Include.cginc"

				sampler2D _DissolveTex;
				fixed _Progress;
				sampler2D _EdgeAroundRamp;
				fixed _EdgeAround;
				float _EdgeAroundHDR;


				v2f vert(appdata v)
				{
					return VertBase(v);
				}

				fixed4 frag(v2f i) : SV_Target
				{
					fixed4 finCol;
					half shadowContrast;
					fixed4 ShadowCol;
					half3 lightDir = normalize(UnityWorldSpaceLightDir(i.wpos));//normalize(_LightDir.xyz);
					half3 viewDir = normalize(UnityWorldSpaceViewDir(i.wpos));

					finCol = DiffuseILM(i, lightDir, viewDir, shadowContrast, ShadowCol);
					finCol += Rim(i, lightDir, viewDir);

					//Emission
					//finCol.rgb += Emission(i);

					//dissolve
					fixed4 dissolveCol = tex2D(_DissolveTex, i.uv);
					fixed x = dissolveCol.r;
					fixed progress = _Progress;

					//Edge Around Factor
					fixed edgearound = lerp(x + _EdgeAround, x - _EdgeAround, progress);
					edgearound = smoothstep(progress + _EdgeAround, progress - _EdgeAround, edgearound);
					//Edge Around Color
					fixed3 ca = tex2D(_EdgeAroundRamp, fixed2(edgearound, 0)).rgb;

					finCol *= _Color;
					ca = (finCol.rgb + ca)*ca*_EdgeAroundHDR;	
					finCol.rgb = lerp(finCol.rgb, ca, edgearound);
					return finCol;
					UNITY_APPLY_FOG(i.fogCoord, finCol);
					return finCol;
				}
				ENDCG
			}
		}
		FallBack "Mobile/VertexLit"
}
