// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Toony Colors Pro 2/UseMatcapEmi_Rim GGX CG"
{
	Properties
	{
		[Header(Common Settings)]
		_Color("Color", Color) = (1,1,1,1)
		//DIFFUSE
		_MainTex("Main Texture", 2D) = "white" {}

		_SSSTex("SSS Texture", 2D) = "white" {}
		_ShadowContrast("Shadow Contrast", Range(-2,1)) = 1
		_Mask1("Mask 1 (MatCap,Spec,Shadow)", 2D) = "white" {}

		[Header(Specular Settings)]
		//smooth没用了，留着防止材质之前序列化的数据出问题
		[KeywordEnum(None,Smooth,Step)]  _SPECULAR("Specular Mode", Float) = 0
		//_SpecTex("Spec Texture", 2D) = "black" {}
		_Shininess("Shininess", Range(0.001, 2)) = 0.078125
		_SpecStep("_SpecStep",Range(0.1,1.0)) = 0.5
		_SpecPow("_SpecPow",Range(0.1,5)) = 1
		[Header(Emission Settings)]
		[Toggle(_EMISSION_ON)] _EMISSION("Toggle Emission", Float) = 0
		_EmissionTex("Emission Texture", 2D) = "black" {}
		[HDR] _EmissionColor("Emission Color", Color) = (1,1,1,1.0)
		[Header(MatCap Settings)]
		//MATCAP
		[Toggle] _MATCAP("Toggle MatCap", Float) = 0
		_MatCap("MatCap (RGB)", 2D) = "black" {}
		//ECLIPSE
		[Header(Eclipse Settings)]
		[Toggle(_ECLIPSE_ON)] _ECLIPSE("Toggle Eclipse", Float) = 0
		_EclipseTex("Eclipse Texture (RGB)", 2D) = "black" {}
		_EclipseIntensity("Eclipse Intensity",Range(0,1)) = 1
		[HDR] _EclipseColor("Eclipse Color", Color) = (1,1,1,1.0)
		[Header(Rimlight Settings)]
		//RIM LIGHT
		_RimColor("Rim Color", Color) = (0.8,0.8,0.8,0.6)
		_RimMin("Rim Min", Range(0,1)) = 0.5
		_RimMax("Rim Max", Range(0,1)) = 1.0
		_RimDir("_RimDir",Vector) = (1,1,1,1)
		//blink
		_Speed("Speed",Range(0,3)) = 1
		_Radio("Ratio",Range(0,1)) = 0.0
		//[Toggle(_SHADERSH_ON)] _SHADERSH("Toggle ShaderSH",  Float) = 0
	}

		SubShader
		{
			Tags { "RenderType" = "Opaque" }
			Pass
			{
				Name "BASE"
				Tags{"LightMode" = "ForwardBase" "RenderType" = "Opaque"}
				//ColorMask RGBA
				//Blend[_BlendSrc][_BlendDest]
				
				Stencil
				{
					Ref 1
					Comp Always
					Pass replace
				}

				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				//#pragma multi_compile_fwdbase
				#pragma multi_compile_fog
				#pragma multi_compile __ _MATCAP_ON
				#pragma multi_compile _SPECULAR_NONE _SPECULAR_STEP _SPECULAR_SMOOTH
				#pragma multi_compile __ _SHADERSH_ON
				#pragma multi_compile __ _ECLIPSE_ON
				#pragma multi_compile __ _EMISSION_ON

				//#pragma enable_d3d11_debug_symbols

				#include "UnityCG.cginc"
				#include "AutoLight.cginc"
				#include "GGX_Include.cginc"



				v2f vert(appdata v)
				{
					return VertBase(v);
				}

				fixed4 frag(v2f i) : SV_Target
				{
					fixed4 finCol;
					half shadowContrast;
					fixed4 maskTex;
					fixed4 AlbedoCol;
					fixed4 ShadowCol;
					half3 lightDir = normalize(_WorldSpaceLightPos0.xyz);//normalize(_LightDir.xyz);
					half3 viewDir = normalize(UnityWorldSpaceViewDir(i.wpos));
					finCol = Diffuse(i, lightDir, viewDir, shadowContrast, maskTex, ShadowCol);
					//old
					//AlbedoCol = finCol;
					AlbedoCol = fixed4(finCol.rgb*maskTex.a, finCol.a);

#if _SPECULAR_STEP || _SPECULAR_SMOOTH
					//old
					//finCol += Specular(i, lightDir, viewDir, maskTex, shadowContrast);
					finCol += ShadowCol*SpecularWithoutSpecTex(i, lightDir, viewDir, maskTex, shadowContrast);
#endif
					//old
					//finCol *= _Global_LightColor* _Global_LightIntensity;
					finCol *= maskTex.a;

					finCol += Rim(i, lightDir, viewDir);

#if _MATCAP_ON
					finCol.rgb += MatCap(i, maskTex);
#endif

					//Emission
#if _EMISSION_ON
					finCol.rgb += Emission(i);
#endif

					finCol.rgb += AlbedoCol * lerp(UNITY_LIGHTMODEL_AMBIENT, 1, gAmbientRatio);

					finCol.a = AlbedoCol.a;
					if(_Radio > 0.1)
						finCol.rgb = (_Color.rgb * i.color.a + finCol.rgb * (1.0 - i.color.a)) * _Radio  + finCol.rgb * (1 - _Radio);
					else
						finCol *= _Color;	
					UNITY_APPLY_FOG(i.fogCoord, finCol);
					return finCol;
				}
				ENDCG
			}

			Pass{
				// Need to have it know it's to be used as a forward add pass.
				Tags {"LightMode" = "ForwardAdd"}
				// And it's additive to the base pass.
				Blend One One
				ZWrite Off
				
				Stencil
				{
					Ref 1
					Comp Always
					Pass replace
				}

				CGPROGRAM
				#define POINT
				#include "Autolight.cginc" 
				#include "UnityPBSLighting.cginc"
					#pragma vertex vert
					#pragma fragment frag
				//// Need to have it know it's compiling it as forward add pass.
				//// Alternatively, you can uncomment the line below and uncomment the line below that to tell it that the forward add pass should have shadows calculated rather than just attenuation.
				//#pragma multi_compile_fwdadd
				//// #pragma multi_compile_fwdadd_fullshadows
				//#pragma fragmentoption ARB_precision_hint_fastest

				//#include "UnityCG.cginc"
				//// This includes the required macros for shadow and attenuation values.
				//#include "AutoLight.cginc"

				//float4 _MainTex_ST;
				struct appdata
				{
					float4 vertex : POSITION;
					float2 texCoord : TEXCOORD0;
					//fixed4 color : COLOR;
					float3 normal : NORMAL;
				};

				struct v2f {
					float4 pos : SV_POSITION;
					float2 uv : TEXCOORD0;
					float3 worldNormal : TEXCOORD1;
					float3 worldPos : TEXCOORD2;
				};
				sampler2D _MainTex;
				float4 _MainTex_ST;

				v2f vert(appdata v)
				{
					v2f o;

					o.pos = UnityObjectToClipPos(v.vertex);
					o.pos = UnityObjectToClipPos(v.vertex);
					o.worldNormal = UnityObjectToWorldNormal(v.normal);
					o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
					o.uv = TRANSFORM_TEX(v.texCoord, _MainTex);
					return o;
				}

				fixed4 _Color;
				fixed4 frag(v2f i) : COLOR
				{
					UNITY_LIGHT_ATTENUATION(attenuation, 0, i.worldPos);
					half3 lightdir = normalize(_WorldSpaceLightPos0.xyz - i.worldPos);
					fixed4 mainTex = tex2D(_MainTex, i.uv);
					return mainTex * _Color * attenuation * _LightColor0 * max(0, dot(i.worldNormal, lightdir));
				}
			ENDCG
			}
		}
		FallBack "Mobile/VertexLit"
}
