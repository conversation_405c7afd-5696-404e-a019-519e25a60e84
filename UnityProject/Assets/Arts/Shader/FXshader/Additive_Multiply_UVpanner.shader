// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:1,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4534,x:33373,y:32556,varname:node_4534,prsc:2|emission-2872-OUT;n:type:ShaderForge.SFN_Tex2d,id:3365,x:32154,y:32257,ptovrint:False,ptlb:Mask,ptin:_Mask,varname:node_3365,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-3513-UVOUT;n:type:ShaderForge.SFN_Multiply,id:3199,x:32340,y:32400,varname:node_3199,prsc:2|A-3365-RGB,B-6046-RGB;n:type:ShaderForge.SFN_Tex2d,id:6046,x:32137,y:32485,ptovrint:False,ptlb:Texture_01,ptin:_Texture_01,varname:node_6046,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-738-OUT;n:type:ShaderForge.SFN_Multiply,id:4834,x:32558,y:32522,varname:node_4834,prsc:2|A-3199-OUT,B-2954-RGB,C-7282-OUT;n:type:ShaderForge.SFN_Color,id:2954,x:32330,y:32615,ptovrint:False,ptlb:MASK_COLOR,ptin:_MASK_COLOR,varname:node_2954,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Time,id:3141,x:31342,y:32661,varname:node_3141,prsc:2;n:type:ShaderForge.SFN_Multiply,id:6785,x:31548,y:32568,varname:node_6785,prsc:2|A-3141-T,B-8933-OUT;n:type:ShaderForge.SFN_Multiply,id:851,x:31559,y:32784,varname:node_851,prsc:2|A-2516-OUT,B-3141-T;n:type:ShaderForge.SFN_ValueProperty,id:2516,x:31269,y:32895,ptovrint:False,ptlb:Tex01_Vspeed,ptin:_Tex01_Vspeed,varname:node_2516,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:8933,x:31177,y:32554,ptovrint:False,ptlb:Tex01_Uspeed,ptin:_Tex01_Uspeed,varname:node_8933,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Append,id:8966,x:31720,y:32678,varname:node_8966,prsc:2|A-6785-OUT,B-851-OUT;n:type:ShaderForge.SFN_TexCoord,id:1012,x:31720,y:32442,varname:node_1012,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Add,id:738,x:31943,y:32485,varname:node_738,prsc:2|A-1012-UVOUT,B-8966-OUT;n:type:ShaderForge.SFN_Tex2d,id:6912,x:32259,y:32933,ptovrint:False,ptlb:TexTure02,ptin:_TexTure02,varname:node_6912,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-3648-OUT;n:type:ShaderForge.SFN_Multiply,id:3696,x:33042,y:32670,varname:node_3696,prsc:2|A-5775-OUT,B-1072-OUT,C-6553-RGB,D-6553-A;n:type:ShaderForge.SFN_Time,id:236,x:31369,y:33051,varname:node_236,prsc:2;n:type:ShaderForge.SFN_Multiply,id:4589,x:31575,y:32958,varname:node_4589,prsc:2|A-236-T,B-8911-OUT;n:type:ShaderForge.SFN_Multiply,id:1289,x:31586,y:33174,varname:node_1289,prsc:2|A-5270-OUT,B-236-T;n:type:ShaderForge.SFN_ValueProperty,id:5270,x:31296,y:33285,ptovrint:False,ptlb:Tex02_Vspeed,ptin:_Tex02_Vspeed,varname:_Mask_Vspeed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:8911,x:31286,y:32990,ptovrint:False,ptlb:Tex02_Uspeed,ptin:_Tex02_Uspeed,varname:_Mask_Uspeed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Append,id:3148,x:31747,y:33068,varname:node_3148,prsc:2|A-4589-OUT,B-1289-OUT;n:type:ShaderForge.SFN_Add,id:3648,x:32008,y:32933,varname:node_3648,prsc:2|A-1012-UVOUT,B-3148-OUT;n:type:ShaderForge.SFN_Color,id:7670,x:32115,y:33123,ptovrint:False,ptlb:Tex02_Color,ptin:_Tex02_Color,varname:node_7670,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Multiply,id:1072,x:32535,y:33039,varname:node_1072,prsc:2|A-6912-RGB,B-7670-RGB,C-5025-OUT;n:type:ShaderForge.SFN_ValueProperty,id:5025,x:32284,y:33203,ptovrint:False,ptlb:Tex02_Color_intensity,ptin:_Tex02_Color_intensity,varname:node_5025,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_ValueProperty,id:7282,x:32330,y:32796,ptovrint:False,ptlb:MASK_Color_intensity,ptin:_MASK_Color_intensity,varname:_Tex02_Color_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:2;n:type:ShaderForge.SFN_VertexColor,id:6553,x:32508,y:32817,varname:node_6553,prsc:2;n:type:ShaderForge.SFN_Rotator,id:3513,x:31822,y:32226,varname:node_3513,prsc:2|UVIN-1012-UVOUT,SPD-9168-OUT;n:type:ShaderForge.SFN_ValueProperty,id:9168,x:31541,y:32306,ptovrint:False,ptlb:MASKRotator_speed,ptin:_MASKRotator_speed,varname:node_9168,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:8168,x:32539,y:32709,ptovrint:False,ptlb:EXP,ptin:_EXP,varname:node_8168,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Power,id:5775,x:32848,y:32590,varname:node_5775,prsc:2|VAL-4834-OUT,EXP-9468-OUT;n:type:ShaderForge.SFN_Exp,id:9468,x:32717,y:32634,varname:node_9468,prsc:2,et:0|IN-8168-OUT;n:type:ShaderForge.SFN_Multiply,id:2872,x:33223,y:32788,varname:node_2872,prsc:2|A-3696-OUT,B-7458-OUT;n:type:ShaderForge.SFN_Slider,id:7458,x:32912,y:32913,ptovrint:False,ptlb:Opacity,ptin:_Opacity,varname:node_7458,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:1;proporder:3365-2954-7282-9168-2516-8933-6046-6912-7670-5270-8911-5025-8168-7458;pass:END;sub:END;*/

Shader "ProjectU/Particle/Additive_Multiply_UVpanner" {
    Properties {
        _Mask ("Mask", 2D) = "white" {}
        [HDR]_MASK_COLOR ("MASK_COLOR", Color) = (0.5,0.5,0.5,1)
        _MASK_Color_intensity ("MASK_Color_intensity", Float ) = 2
        _MASKRotator_speed ("MASKRotator_speed", Float ) = 0
        _Tex01_Vspeed ("Tex01_Vspeed", Float ) = 0
        _Tex01_Uspeed ("Tex01_Uspeed", Float ) = 0
        _Texture_01 ("Texture_01", 2D) = "white" {}
        _TexTure02 ("TexTure02", 2D) = "white" {}
        [HDR]_Tex02_Color ("Tex02_Color", Color) = (0.5,0.5,0.5,1)
        _Tex02_Vspeed ("Tex02_Vspeed", Float ) = 0
        _Tex02_Uspeed ("Tex02_Uspeed", Float ) = 0
        _Tex02_Color_intensity ("Tex02_Color_intensity", Float ) = 1
        _EXP ("EXP", Float ) = 1
        _Opacity ("Opacity", Range(0, 1)) = 1
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
            "RenderPipeline" = "UniversalPipeline"
        }
        LOD 100
        Pass {
            Name "FORWARD"
            Blend One One
            Cull Off
            ZWrite Off
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #pragma multi_compile_fwdbase
            uniform sampler2D _Mask; uniform float4 _Mask_ST;
            uniform sampler2D _Texture_01; uniform float4 _Texture_01_ST;
            uniform float4 _MASK_COLOR;
            uniform float _Tex01_Vspeed;
            uniform float _Tex01_Uspeed;
            uniform sampler2D _TexTure02; uniform float4 _TexTure02_ST;
            uniform float _Tex02_Vspeed;
            uniform float _Tex02_Uspeed;
            uniform float4 _Tex02_Color;
            uniform float _Tex02_Color_intensity;
            uniform float _MASK_Color_intensity;
            uniform float _MASKRotator_speed;
            uniform float _EXP;
            uniform float _Opacity;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.pos = TransformObjectToHClip( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 node_7671 = _Time;
                float node_3513_ang = node_7671.g;
                float node_3513_spd = _MASKRotator_speed;
                float node_3513_cos = cos(node_3513_spd*node_3513_ang);
                float node_3513_sin = sin(node_3513_spd*node_3513_ang);
                float2 node_3513_piv = float2(0.5,0.5);
                float2 node_3513 = (mul(i.uv0-node_3513_piv,float2x2( node_3513_cos, -node_3513_sin, node_3513_sin, node_3513_cos))+node_3513_piv);
                float4 _Mask_var = tex2D(_Mask,TRANSFORM_TEX(node_3513, _Mask));
                float4 node_3141 = _Time;
                float2 node_738 = (i.uv0+float2((node_3141.g*_Tex01_Uspeed),(_Tex01_Vspeed*node_3141.g)));
                float4 _Texture_01_var = tex2D(_Texture_01,TRANSFORM_TEX(node_738, _Texture_01));
                float4 node_236 = _Time;
                float2 node_3648 = (i.uv0+float2((node_236.g*_Tex02_Uspeed),(_Tex02_Vspeed*node_236.g)));
                float4 _TexTure02_var = tex2D(_TexTure02,TRANSFORM_TEX(node_3648, _TexTure02));
                float3 emissive = ((pow(((_Mask_var.rgb*_Texture_01_var.rgb)*_MASK_COLOR.rgb*_MASK_Color_intensity),exp(_EXP))*(_TexTure02_var.rgb*_Tex02_Color.rgb*_Tex02_Color_intensity)*i.vertexColor.rgb*i.vertexColor.a)*_Opacity);
                float3 finalColor = emissive;
                return float4(finalColor,1);
            }
            ENDHLSL
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
