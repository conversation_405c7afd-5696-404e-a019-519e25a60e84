// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:1,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:3,bdst:7,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:1128,x:33863,y:32748,varname:node_1128,prsc:2|emission-1654-OUT,alpha-5508-OUT;n:type:ShaderForge.SFN_Tex2d,id:8543,x:31873,y:32906,ptovrint:False,ptlb:mask,ptin:_mask,varname:node_8543,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-1322-OUT;n:type:ShaderForge.SFN_Tex2d,id:7183,x:31958,y:32306,ptovrint:False,ptlb:Texture02,ptin:_Texture02,varname:node_7183,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-3189-OUT;n:type:ShaderForge.SFN_Time,id:5563,x:31035,y:32632,varname:node_5563,prsc:2;n:type:ShaderForge.SFN_Multiply,id:2297,x:31354,y:32554,varname:node_2297,prsc:2|A-3433-OUT,B-5563-T;n:type:ShaderForge.SFN_Multiply,id:5884,x:31342,y:32733,varname:node_5884,prsc:2|A-9026-OUT,B-5563-T;n:type:ShaderForge.SFN_Slider,id:3433,x:30957,y:32544,ptovrint:False,ptlb:tex02_Uspeed,ptin:_tex02_Uspeed,varname:node_3433,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-4,cur:0,max:4;n:type:ShaderForge.SFN_Slider,id:9026,x:30981,y:32808,ptovrint:False,ptlb:tex02_Vspeed,ptin:_tex02_Vspeed,varname:node_9026,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-4,cur:0,max:4;n:type:ShaderForge.SFN_Append,id:6057,x:31528,y:32643,varname:node_6057,prsc:2|A-2297-OUT,B-5884-OUT;n:type:ShaderForge.SFN_TexCoord,id:2053,x:31554,y:32255,varname:node_2053,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Add,id:3189,x:31735,y:32323,varname:node_3189,prsc:2|A-2053-UVOUT,B-6057-OUT;n:type:ShaderForge.SFN_Color,id:577,x:31873,y:33112,ptovrint:False,ptlb:mask_color,ptin:_mask_color,varname:node_577,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Slider,id:3370,x:31769,y:33348,ptovrint:False,ptlb:maskcolorintensity,ptin:_maskcolorintensity,varname:node_3370,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:3;n:type:ShaderForge.SFN_Color,id:5850,x:32200,y:32437,ptovrint:False,ptlb:texturecolor,ptin:_texturecolor,varname:node_5850,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Slider,id:9183,x:31903,y:32695,ptovrint:False,ptlb:texturecolorintensity,ptin:_texturecolorintensity,varname:node_9183,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1.940707,max:3;n:type:ShaderForge.SFN_TexCoord,id:6429,x:32158,y:33373,varname:node_6429,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_Tex2d,id:437,x:32205,y:33610,ptovrint:False,ptlb:tex_dissolve,ptin:_tex_dissolve,varname:node_437,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Step,id:320,x:32393,y:33471,varname:node_320,prsc:2|A-6429-U,B-437-R;n:type:ShaderForge.SFN_Multiply,id:3787,x:33026,y:33153,varname:node_3787,prsc:2|A-5179-A,B-320-OUT,C-6983-OUT,D-4310-OUT;n:type:ShaderForge.SFN_VertexColor,id:5179,x:32780,y:32976,varname:node_5179,prsc:2;n:type:ShaderForge.SFN_Time,id:9136,x:30936,y:33003,varname:node_9136,prsc:2;n:type:ShaderForge.SFN_Multiply,id:239,x:31255,y:32925,varname:node_239,prsc:2|A-7222-OUT,B-9136-T;n:type:ShaderForge.SFN_Multiply,id:5839,x:31243,y:33104,varname:node_5839,prsc:2|A-4557-OUT,B-9136-T;n:type:ShaderForge.SFN_Slider,id:7222,x:30858,y:32915,ptovrint:False,ptlb:Mask_Uspeed,ptin:_Mask_Uspeed,varname:_tex02_Uspeed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-4,cur:0,max:4;n:type:ShaderForge.SFN_Slider,id:4557,x:30882,y:33179,ptovrint:False,ptlb:Mask_Vspeed,ptin:_Mask_Vspeed,varname:_tex02_Vspeed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-4,cur:0,max:4;n:type:ShaderForge.SFN_Append,id:1169,x:31429,y:33014,varname:node_1169,prsc:2|A-239-OUT,B-5839-OUT;n:type:ShaderForge.SFN_Add,id:1322,x:31693,y:32906,varname:node_1322,prsc:2|A-2053-UVOUT,B-1169-OUT;n:type:ShaderForge.SFN_Multiply,id:5508,x:33225,y:33153,varname:node_5508,prsc:2|A-3787-OUT,B-7003-OUT;n:type:ShaderForge.SFN_Slider,id:7003,x:32881,y:33366,ptovrint:False,ptlb:Opacity,ptin:_Opacity,varname:node_7003,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:1;n:type:ShaderForge.SFN_Tex2d,id:6314,x:31981,y:32035,ptovrint:False,ptlb:Texture01,ptin:_Texture01,varname:node_6314,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-3189-OUT;n:type:ShaderForge.SFN_Add,id:5663,x:32410,y:32071,varname:node_5663,prsc:2|A-6314-RGB,B-7183-RGB;n:type:ShaderForge.SFN_Multiply,id:7978,x:32269,y:32941,varname:node_7978,prsc:2|A-8543-RGB,B-577-RGB,C-3370-OUT,D-577-A;n:type:ShaderForge.SFN_Multiply,id:1654,x:33479,y:32775,varname:node_1654,prsc:2|A-5707-OUT,B-5179-RGB;n:type:ShaderForge.SFN_Add,id:2211,x:32376,y:32674,varname:node_2211,prsc:2|A-6314-R,B-7183-R;n:type:ShaderForge.SFN_Multiply,id:7391,x:32556,y:32248,varname:node_7391,prsc:2|A-5663-OUT,B-5850-RGB,C-9183-OUT,D-5850-A;n:type:ShaderForge.SFN_Multiply,id:6983,x:32525,y:33160,varname:node_6983,prsc:2|A-8543-R,B-5699-OUT;n:type:ShaderForge.SFN_Vector1,id:5699,x:32343,y:33271,varname:node_5699,prsc:2,v1:1.5;n:type:ShaderForge.SFN_Multiply,id:4310,x:32594,y:32908,varname:node_4310,prsc:2|A-2211-OUT,B-3789-OUT;n:type:ShaderForge.SFN_Vector1,id:3789,x:32376,y:32864,varname:node_3789,prsc:2,v1:1.3;n:type:ShaderForge.SFN_Add,id:5707,x:32934,y:32473,varname:node_5707,prsc:2|A-554-OUT,B-3683-OUT;n:type:ShaderForge.SFN_Power,id:554,x:32724,y:32370,varname:node_554,prsc:2|VAL-7391-OUT,EXP-3465-OUT;n:type:ShaderForge.SFN_Slider,id:3465,x:32367,y:32541,ptovrint:False,ptlb:MaskcolorExp,ptin:_MaskcolorExp,varname:node_3465,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:3;n:type:ShaderForge.SFN_Power,id:3683,x:32839,y:32718,varname:node_3683,prsc:2|VAL-7978-OUT,EXP-3989-OUT;n:type:ShaderForge.SFN_Slider,id:3989,x:32493,y:32777,ptovrint:False,ptlb:TexturecolorExp,ptin:_TexturecolorExp,varname:node_3989,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:3;proporder:7183-3433-9026-8543-7222-4557-577-3370-5850-9183-437-7003-6314-3465-3989;pass:END;sub:END;*/

Shader "ProjectU/Particle/Blended_Multiply_UVPanner2" {
    Properties {
        _Texture02 ("Texture02", 2D) = "white" {}
        _tex02_Uspeed ("tex02_Uspeed", Range(-4, 4)) = 0
        _tex02_Vspeed ("tex02_Vspeed", Range(-4, 4)) = 0
        _mask ("mask", 2D) = "white" {}
        _Mask_Uspeed ("Mask_Uspeed", Range(-4, 4)) = 0
        _Mask_Vspeed ("Mask_Vspeed", Range(-4, 4)) = 0
        [HDR]_mask_color ("mask_color", Color) = (0.5,0.5,0.5,1)
        _maskcolorintensity ("maskcolorintensity", Range(0, 3)) = 1
        [HDR]_texturecolor ("texturecolor", Color) = (0.5,0.5,0.5,1)
        _texturecolorintensity ("texturecolorintensity", Range(0, 3)) = 1.940707
        _tex_dissolve ("tex_dissolve", 2D) = "white" {}
        _Opacity ("Opacity", Range(0, 1)) = 1
        _Texture01 ("Texture01", 2D) = "white" {}
        _MaskcolorExp ("MaskcolorExp", Range(0, 3)) = 1
        _TexturecolorExp ("TexturecolorExp", Range(0, 3)) = 1
        [HideInInspector]_Cutoff ("Alpha cutoff", Range(0,1)) = 0.5
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
            "RenderPipeline" = "UniversalPipeline"
        }
        LOD 100
        Pass {
            Name "FORWARD"
            Blend SrcAlpha OneMinusSrcAlpha
            Cull Off
            ZWrite Off
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #pragma multi_compile_fwdbase
            uniform sampler2D _mask; uniform float4 _mask_ST;
            uniform sampler2D _Texture02; uniform float4 _Texture02_ST;
            uniform float _tex02_Uspeed;
            uniform float _tex02_Vspeed;
            uniform float4 _mask_color;
            uniform float _maskcolorintensity;
            uniform float4 _texturecolor;
            uniform float _texturecolorintensity;
            uniform sampler2D _tex_dissolve; uniform float4 _tex_dissolve_ST;
            uniform float _Mask_Uspeed;
            uniform float _Mask_Vspeed;
            uniform float _Opacity;
            uniform sampler2D _Texture01; uniform float4 _Texture01_ST;
            uniform float _MaskcolorExp;
            uniform float _TexturecolorExp;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 texcoord1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.uv1 = v.texcoord1;
                o.vertexColor = v.vertexColor;
                o.pos = TransformObjectToHClip( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 node_5563 = _Time;
                float2 node_3189 = (i.uv0+float2((_tex02_Uspeed*node_5563.g),(_tex02_Vspeed*node_5563.g)));
                float4 _Texture01_var = tex2D(_Texture01,TRANSFORM_TEX(node_3189, _Texture01));
                float4 _Texture02_var = tex2D(_Texture02,TRANSFORM_TEX(node_3189, _Texture02));
                float4 node_9136 = _Time;
                float2 node_1322 = (i.uv0+float2((_Mask_Uspeed*node_9136.g),(_Mask_Vspeed*node_9136.g)));
                float4 _mask_var = tex2D(_mask,TRANSFORM_TEX(node_1322, _mask));
                float3 emissive = ((pow(((_Texture01_var.rgb+_Texture02_var.rgb)*_texturecolor.rgb*_texturecolorintensity*_texturecolor.a),_MaskcolorExp)+pow((_mask_var.rgb*_mask_color.rgb*_maskcolorintensity*_mask_color.a),_TexturecolorExp))*i.vertexColor.rgb);
                float3 finalColor = emissive;
                float4 _tex_dissolve_var = tex2D(_tex_dissolve,TRANSFORM_TEX(i.uv0, _tex_dissolve));
                return float4(finalColor,((i.vertexColor.a*step(i.uv1.r,_tex_dissolve_var.r)*(_mask_var.r*1.5)*((_Texture01_var.r+_Texture02_var.r)*1.3))*_Opacity));
            }
            ENDHLSL
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
