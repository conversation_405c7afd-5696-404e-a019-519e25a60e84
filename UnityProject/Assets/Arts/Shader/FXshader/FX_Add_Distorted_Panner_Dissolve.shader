// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:True,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0,fgcg:0,fgcb:0,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:32976,y:32676,varname:node_4795,prsc:2|emission-8200-OUT;n:type:ShaderForge.SFN_Tex2d,id:6074,x:31908,y:32680,ptovrint:False,ptlb:MainTex,ptin:_MainTex,varname:_MainTex,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-7948-OUT;n:type:ShaderForge.SFN_VertexColor,id:2053,x:31904,y:32830,varname:node_2053,prsc:2;n:type:ShaderForge.SFN_Color,id:797,x:31908,y:32347,ptovrint:True,ptlb:Color1,ptin:_TintColor,varname:_TintColor,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Tex2d,id:5358,x:31908,y:33116,ptovrint:False,ptlb:Dissolve_Tex,ptin:_Dissolve_Tex,varname:node_5358,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-7948-OUT;n:type:ShaderForge.SFN_Slider,id:1987,x:31544,y:33292,ptovrint:False,ptlb:Dissolve_Power,ptin:_Dissolve_Power,varname:node_1987,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-1,cur:0.05128205,max:1;n:type:ShaderForge.SFN_Add,id:4843,x:32469,y:33001,varname:node_4843,prsc:2|A-3932-OUT,B-7921-OUT;n:type:ShaderForge.SFN_Multiply,id:3941,x:32503,y:32648,varname:node_3941,prsc:2|A-4829-OUT,B-2053-RGB;n:type:ShaderForge.SFN_Tex2d,id:9046,x:30686,y:33090,ptovrint:False,ptlb:Noise,ptin:_Noise,varname:node_9046,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-1750-OUT;n:type:ShaderForge.SFN_TexCoord,id:7465,x:30686,y:32876,varname:node_7465,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Add,id:2617,x:31120,y:33045,varname:node_2617,prsc:2|A-7465-UVOUT,B-3802-OUT;n:type:ShaderForge.SFN_Slider,id:3765,x:30529,y:33279,ptovrint:False,ptlb:Noise_Power,ptin:_Noise_Power,varname:_Dissolve_Power_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:0,max:1;n:type:ShaderForge.SFN_Multiply,id:3802,x:30913,y:33107,varname:node_3802,prsc:2|A-9046-R,B-3765-OUT;n:type:ShaderForge.SFN_Append,id:7948,x:31701,y:33057,varname:node_7948,prsc:2|A-9451-OUT,B-3255-OUT;n:type:ShaderForge.SFN_ComponentMask,id:735,x:31291,y:33045,varname:node_735,prsc:2,cc1:0,cc2:1,cc3:-1,cc4:-1|IN-2617-OUT;n:type:ShaderForge.SFN_Add,id:9451,x:31544,y:33008,varname:node_9451,prsc:2|A-735-R,B-8671-OUT,C-4911-Z;n:type:ShaderForge.SFN_Add,id:3255,x:31544,y:33149,varname:node_3255,prsc:2|A-735-G,B-3183-OUT,C-4911-W,D-6017-OUT;n:type:ShaderForge.SFN_Slider,id:3183,x:31081,y:33236,ptovrint:False,ptlb:Dissolve_vPosi,ptin:_Dissolve_vPosi,varname:node_3183,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-1,cur:0,max:1;n:type:ShaderForge.SFN_Slider,id:8671,x:31102,y:32921,ptovrint:False,ptlb:Dissolve_uPosi,ptin:_Dissolve_uPosi,varname:node_8671,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:-1,cur:0,max:1;n:type:ShaderForge.SFN_Time,id:7679,x:29882,y:33150,varname:node_7679,prsc:2;n:type:ShaderForge.SFN_Vector4Property,id:3470,x:29882,y:33301,ptovrint:False,ptlb:X_U_Y_V,ptin:_X_U_Y_V,varname:node_3470,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0,v2:0,v3:0,v4:0;n:type:ShaderForge.SFN_TexCoord,id:5639,x:30070,y:33000,varname:node_5639,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Add,id:7310,x:30275,y:33064,varname:node_7310,prsc:2|A-5639-U,B-4702-OUT;n:type:ShaderForge.SFN_Add,id:1989,x:30275,y:33188,varname:node_1989,prsc:2|A-5639-V,B-8633-OUT;n:type:ShaderForge.SFN_Multiply,id:4702,x:30070,y:33150,varname:node_4702,prsc:2|A-7679-TSL,B-3470-X;n:type:ShaderForge.SFN_Multiply,id:8633,x:30070,y:33292,varname:node_8633,prsc:2|A-7679-TSL,B-3470-Y;n:type:ShaderForge.SFN_Append,id:1750,x:30471,y:33090,varname:node_1750,prsc:2|A-7310-OUT,B-1989-OUT;n:type:ShaderForge.SFN_Multiply,id:3932,x:32248,y:33001,varname:node_3932,prsc:2|A-2126-OUT,B-5358-R;n:type:ShaderForge.SFN_ValueProperty,id:2126,x:31908,y:32977,ptovrint:False,ptlb:EdgePower,ptin:_EdgePower,varname:node_2126,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Clamp01,id:9226,x:32625,y:32942,varname:node_9226,prsc:2|IN-4843-OUT;n:type:ShaderForge.SFN_Multiply,id:7921,x:32248,y:33163,varname:node_7921,prsc:2|A-2126-OUT,B-3492-OUT;n:type:ShaderForge.SFN_Lerp,id:4829,x:32251,y:32560,varname:node_4829,prsc:2|A-5560-RGB,B-797-RGB,T-6074-R;n:type:ShaderForge.SFN_Color,id:5560,x:31908,y:32516,ptovrint:False,ptlb:Coler2,ptin:_Coler2,varname:node_5560,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_TexCoord,id:4911,x:31238,y:33368,varname:node_4911,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_Add,id:3492,x:31908,y:33336,varname:node_3492,prsc:2|A-1987-OUT,B-2053-A;n:type:ShaderForge.SFN_Vector1,id:6017,x:31342,y:33304,varname:node_6017,prsc:2,v1:-1;n:type:ShaderForge.SFN_Multiply,id:8200,x:32796,y:32815,varname:node_8200,prsc:2|A-3941-OUT,B-9226-OUT;proporder:797-5560-6074-5358-8671-3183-1987-2126-9046-3765-3470;pass:END;sub:END;*/

Shader "ProjectU/Particle/FX_Add_Distorted_Panner_Dissolve" {
    Properties {
        [HDR]_TintColor ("Color1", Color) = (0.5,0.5,0.5,1)
        [HDR]_Coler2 ("Coler2", Color) = (0.5,0.5,0.5,1)
        _MainTex ("MainTex", 2D) = "white" {}
        _Dissolve_Tex ("Dissolve_Tex", 2D) = "white" {}
        _Dissolve_uPosi ("Dissolve_uPosi", Range(-1, 1)) = 0
        _Dissolve_vPosi ("Dissolve_vPosi", Range(-1, 1)) = 0
        _Dissolve_Power ("Dissolve_Power", Range(-1, 1)) = 0.05128205
        _EdgePower ("EdgePower", Float ) = 1
        _Noise ("Noise", 2D) = "white" {}
        _Noise_Power ("Noise_Power", Range(0, 1)) = 0
        _X_U_Y_V ("X_U_Y_V", Vector) = (0,0,0,0)
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend One One
            Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            #pragma multi_compile_fog
            //#pragma only_renderers d3d9 d3d11 glcore gles gles3 metal d3d11_9x 
            #pragma target 3.0
            uniform sampler2D _MainTex; uniform float4 _MainTex_ST;
            uniform float4 _TintColor;
            uniform sampler2D _Dissolve_Tex; uniform float4 _Dissolve_Tex_ST;
            uniform float _Dissolve_Power;
            uniform sampler2D _Noise; uniform float4 _Noise_ST;
            uniform float _Noise_Power;
            uniform float _Dissolve_vPosi;
            uniform float _Dissolve_uPosi;
            uniform float4 _X_U_Y_V;
            uniform float _EdgePower;
            uniform float4 _Coler2;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 texcoord1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float4 vertexColor : COLOR;
                UNITY_FOG_COORDS(2)
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.uv1 = v.texcoord1;
                o.vertexColor = v.vertexColor;
                o.pos = UnityObjectToClipPos( v.vertex );
                UNITY_TRANSFER_FOG(o,o.pos);
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 node_7679 = _Time;
                float2 node_1750 = float2((i.uv0.r+(node_7679.r*_X_U_Y_V.r)),(i.uv0.g+(node_7679.r*_X_U_Y_V.g)));
                float4 _Noise_var = tex2D(_Noise,TRANSFORM_TEX(node_1750, _Noise));
                float2 node_735 = (i.uv0+(_Noise_var.r*_Noise_Power)).rg;
                float2 node_7948 = float2((node_735.r+_Dissolve_uPosi+i.uv1.b),(node_735.g+_Dissolve_vPosi+i.uv1.a+(-1.0)));
                float4 _MainTex_var = tex2D(_MainTex,TRANSFORM_TEX(node_7948, _MainTex));
                float4 _Dissolve_Tex_var = tex2D(_Dissolve_Tex,TRANSFORM_TEX(node_7948, _Dissolve_Tex));
                float3 emissive = ((lerp(_Coler2.rgb,_TintColor.rgb,_MainTex_var.r)*i.vertexColor.rgb)*saturate(((_EdgePower*_Dissolve_Tex_var.r)+(_EdgePower*(_Dissolve_Power+i.vertexColor.a)))));
                float3 finalColor = emissive;
                fixed4 finalRGBA = fixed4(finalColor,1);
                UNITY_APPLY_FOG(i.fogCoord, finalRGBA);
                return finalRGBA;
            }
            ENDCG
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
