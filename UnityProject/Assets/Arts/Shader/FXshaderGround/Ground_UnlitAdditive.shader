// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:0,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:0,bsrc:0,bdst:0,dpts:6,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:False,igpj:True,qofs:500,qpre:2,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0,fgcg:0,fgcb:0,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:True,atwp:False,stva:0,stmr:255,stmw:255,stcp:4,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:32724,y:32693,varname:node_4795,prsc:2|emission-9097-OUT;n:type:ShaderForge.SFN_Tex2d,id:6074,x:31505,y:32458,ptovrint:False,ptlb:MainTex,ptin:_MainTex,varname:_MainTex,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Multiply,id:2393,x:31913,y:32615,varname:node_2393,prsc:2|A-6074-RGB,B-2053-RGB,C-797-RGB,D-2178-OUT,E-2053-A;n:type:ShaderForge.SFN_VertexColor,id:2053,x:31505,y:32629,varname:node_2053,prsc:2;n:type:ShaderForge.SFN_Color,id:797,x:31505,y:32787,ptovrint:True,ptlb:Color,ptin:_TintColor,varname:_TintColor,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,c1:1,c2:1,c3:1,c4:1;n:type:ShaderForge.SFN_Slider,id:2178,x:31348,y:32973,ptovrint:False,ptlb:Exp,ptin:_Exp,varname:node_2178,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:10;n:type:ShaderForge.SFN_Multiply,id:9601,x:32122,y:32925,varname:node_9601,prsc:2|A-2393-OUT,B-779-R;n:type:ShaderForge.SFN_Tex2d,id:779,x:31505,y:33092,ptovrint:False,ptlb:MaskTex,ptin:_MaskTex,varname:node_779,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Power,id:9097,x:32431,y:32908,varname:node_9097,prsc:2|VAL-9601-OUT,EXP-1419-OUT;n:type:ShaderForge.SFN_Slider,id:1419,x:31948,y:33108,ptovrint:False,ptlb:ColorPower,ptin:_ColorPower,varname:node_1419,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:20;proporder:6074-797-1419-2178-779;pass:END;sub:END;*/

Shader "ProjectU/Particle/Ground/G_UnlitAdditive" {
    Properties {
        _MainTex ("MainTex", 2D) = "white" {}
        _TintColor ("Color", Color) = (1,1,1,1)
        _ColorPower ("ColorPower", Range(0, 20)) = 1
        _Exp ("Exp", Range(0, 10)) = 1
        _MaskTex ("MaskTex", 2D) = "white" {}
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="AlphaTest+500"
            "RenderType"="Transparent"
            "RenderPipeline" = "UniversalPipeline"
        }
        Pass {
            Name "FORWARD"
            Blend One One
            ZTest Always
            ZWrite Off
            
            Stencil {
                Ref 0
                Comp Equal
            }
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #pragma multi_compile_fwdbase
            uniform sampler2D _MainTex; uniform float4 _MainTex_ST;
            uniform float4 _TintColor;
            uniform float _Exp;
            uniform sampler2D _MaskTex; uniform float4 _MaskTex_ST;
            uniform float _ColorPower;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.pos = TransformObjectToHClip( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i) : COLOR {
////// Lighting:
////// Emissive:
                float4 _MainTex_var = tex2D(_MainTex,TRANSFORM_TEX(i.uv0, _MainTex));
                float4 _MaskTex_var = tex2D(_MaskTex,TRANSFORM_TEX(i.uv0, _MaskTex));
                float3 emissive = pow(((_MainTex_var.rgb*i.vertexColor.rgb*_TintColor.rgb*_Exp*i.vertexColor.a)*_MaskTex_var.r),_ColorPower);
                float3 finalColor = emissive;
                return float4(finalColor,1);
            }
            ENDHLSL
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
