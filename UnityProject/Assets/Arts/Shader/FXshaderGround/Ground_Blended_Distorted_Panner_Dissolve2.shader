// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:1,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:3,bdst:7,dpts:6,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:False,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0.4558823,fgcg:0.8017241,fgcb:0.9117647,fgca:1,fgde:0.01,fgrn:28.7,fgrf:152.39,stcl:True,atwp:False,stva:0,stmr:255,stmw:255,stcp:4,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4840,x:34584,y:32711,varname:node_4840,prsc:2|emission-7195-OUT,alpha-7845-OUT;n:type:ShaderForge.SFN_Tex2d,id:2782,x:31916,y:32740,ptovrint:False,ptlb:Noise,ptin:_Noise,varname:node_2782,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-3462-OUT;n:type:ShaderForge.SFN_Multiply,id:5796,x:32142,y:32740,varname:node_5796,prsc:2|A-1150-OUT,B-2782-R,C-2737-V;n:type:ShaderForge.SFN_Add,id:8507,x:32323,y:32780,varname:node_8507,prsc:2|A-5796-OUT,B-2737-V;n:type:ShaderForge.SFN_TexCoord,id:2737,x:31776,y:32943,varname:node_2737,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Tex2d,id:1589,x:32763,y:32669,varname:node_1589,prsc:2,ntxv:0,isnm:False|UVIN-5211-OUT,TEX-2123-TEX;n:type:ShaderForge.SFN_Time,id:1212,x:31053,y:32650,varname:node_1212,prsc:2;n:type:ShaderForge.SFN_Multiply,id:9582,x:31309,y:32582,varname:node_9582,prsc:2|A-4705-OUT,B-1212-T;n:type:ShaderForge.SFN_Multiply,id:6943,x:31309,y:32738,varname:node_6943,prsc:2|A-1331-OUT,B-1212-T;n:type:ShaderForge.SFN_ValueProperty,id:4705,x:31073,y:32582,ptovrint:False,ptlb:Noise_uspeed,ptin:_Noise_uspeed,varname:node_4705,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:1331,x:31073,y:32810,ptovrint:False,ptlb:Noise_vspeed,ptin:_Noise_vspeed,varname:_node_4705_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:-3;n:type:ShaderForge.SFN_Append,id:4979,x:31477,y:32673,varname:node_4979,prsc:2|A-9582-OUT,B-6943-OUT;n:type:ShaderForge.SFN_Add,id:3462,x:31739,y:32740,varname:node_3462,prsc:2|A-3771-UVOUT,B-4979-OUT;n:type:ShaderForge.SFN_TexCoord,id:3771,x:31554,y:32492,varname:node_3771,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:1667,x:32449,y:32935,varname:node_1667,prsc:2|A-8468-OUT,B-8507-OUT;n:type:ShaderForge.SFN_ValueProperty,id:1150,x:31979,y:32636,ptovrint:False,ptlb:Noise_Vintensity,ptin:_Noise_Vintensity,varname:node_1150,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Tex2d,id:5045,x:32812,y:33554,ptovrint:False,ptlb:Dissolve,ptin:_Dissolve,varname:node_5045,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-1713-OUT;n:type:ShaderForge.SFN_TexCoord,id:4341,x:32172,y:33240,varname:node_4341,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_Time,id:6388,x:31579,y:33373,varname:node_6388,prsc:2;n:type:ShaderForge.SFN_Multiply,id:7386,x:31835,y:33305,varname:node_7386,prsc:2|A-5026-OUT,B-6388-T;n:type:ShaderForge.SFN_Multiply,id:9691,x:31835,y:33461,varname:node_9691,prsc:2|A-2321-OUT,B-6388-T;n:type:ShaderForge.SFN_ValueProperty,id:5026,x:31599,y:33305,ptovrint:False,ptlb:Dissolve_uspeed,ptin:_Dissolve_uspeed,varname:_Noise_uspeed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:2321,x:31599,y:33533,ptovrint:False,ptlb:Dissolve_vspeed,ptin:_Dissolve_vspeed,varname:_Noise_vspeed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:-1;n:type:ShaderForge.SFN_Append,id:2061,x:32036,y:33446,varname:node_2061,prsc:2|A-7386-OUT,B-9691-OUT;n:type:ShaderForge.SFN_Add,id:3814,x:32296,y:33440,varname:node_3814,prsc:2|A-3771-UVOUT,B-2061-OUT;n:type:ShaderForge.SFN_Multiply,id:5560,x:33165,y:32317,varname:node_5560,prsc:2|A-2842-RGB,B-1589-RGB,C-9000-OUT;n:type:ShaderForge.SFN_Color,id:2842,x:32846,y:32305,ptovrint:False,ptlb:Color,ptin:_Color,varname:node_2842,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_ValueProperty,id:9000,x:32751,y:32516,ptovrint:False,ptlb:color_intensity,ptin:_color_intensity,varname:node_9000,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Power,id:9397,x:34012,y:32379,varname:node_9397,prsc:2|VAL-7101-OUT,EXP-71-OUT;n:type:ShaderForge.SFN_Slider,id:71,x:33607,y:32339,ptovrint:False,ptlb:EXP,ptin:_EXP,varname:node_71,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0.3,cur:1,max:3;n:type:ShaderForge.SFN_VertexColor,id:1195,x:33139,y:32886,varname:node_1195,prsc:2;n:type:ShaderForge.SFN_Multiply,id:7845,x:33758,y:33198,varname:node_7845,prsc:2|A-1195-A,B-558-OUT,C-7752-OUT;n:type:ShaderForge.SFN_Tex2dAsset,id:2123,x:32360,y:32542,ptovrint:False,ptlb:Texture01,ptin:_Texture01,varname:node_2123,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Tex2d,id:8425,x:32803,y:32921,varname:node_8425,prsc:2,ntxv:0,isnm:False|UVIN-4937-OUT,TEX-2123-TEX;n:type:ShaderForge.SFN_Add,id:5170,x:32994,y:32974,varname:node_5170,prsc:2|A-8425-R,B-8425-G,C-8425-B;n:type:ShaderForge.SFN_Clamp01,id:2816,x:33100,y:33104,varname:node_2816,prsc:2|IN-5170-OUT;n:type:ShaderForge.SFN_Multiply,id:8735,x:33035,y:32668,varname:node_8735,prsc:2|A-8425-RGB,B-8947-OUT;n:type:ShaderForge.SFN_Vector1,id:8947,x:32929,y:32839,varname:node_8947,prsc:2,v1:10;n:type:ShaderForge.SFN_Multiply,id:9500,x:33718,y:32838,varname:node_9500,prsc:2|A-8280-OUT,B-8972-RGB,C-277-OUT;n:type:ShaderForge.SFN_Color,id:8972,x:33495,y:32911,ptovrint:False,ptlb:EDgecolor,ptin:_EDgecolor,varname:node_8972,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_ValueProperty,id:277,x:33562,y:33105,ptovrint:False,ptlb:EDgecolor_intensity,ptin:_EDgecolor_intensity,varname:node_277,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_ValueProperty,id:6559,x:33224,y:32736,ptovrint:False,ptlb:EDGe,ptin:_EDGe,varname:node_6559,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Add,id:7101,x:33745,y:32459,varname:node_7101,prsc:2|A-5560-OUT,B-9500-OUT;n:type:ShaderForge.SFN_Step,id:8280,x:33381,y:32600,varname:node_8280,prsc:2|A-8735-OUT,B-6559-OUT;n:type:ShaderForge.SFN_Multiply,id:7195,x:34112,y:32622,varname:node_7195,prsc:2|A-9397-OUT,B-1195-RGB;n:type:ShaderForge.SFN_Multiply,id:8214,x:32156,y:33001,varname:node_8214,prsc:2|A-2782-R,B-2737-U,C-7105-OUT;n:type:ShaderForge.SFN_Add,id:8468,x:32323,y:32973,varname:node_8468,prsc:2|A-2737-U,B-8214-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7105,x:31857,y:33152,ptovrint:False,ptlb:Noise_Uintensity,ptin:_Noise_Uintensity,varname:node_7105,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Lerp,id:558,x:33362,y:33202,varname:node_558,prsc:2|A-8425-A,B-2816-OUT,T-3928-OUT;n:type:ShaderForge.SFN_Slider,id:3928,x:33021,y:33253,ptovrint:False,ptlb:RGB/A,ptin:_RGBA,varname:node_3928,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:0,max:1;n:type:ShaderForge.SFN_Lerp,id:1713,x:32572,y:33387,varname:node_1713,prsc:2|A-1667-OUT,B-3814-OUT,T-1632-OUT;n:type:ShaderForge.SFN_Slider,id:1632,x:32247,y:33666,ptovrint:False,ptlb:DissolveNoise,ptin:_DissolveNoise,varname:node_1632,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:1,cur:1,max:0;n:type:ShaderForge.SFN_Smoothstep,id:7752,x:33733,y:33803,varname:node_7752,prsc:2|A-6078-OUT,B-6180-OUT,V-5067-OUT;n:type:ShaderForge.SFN_Subtract,id:5067,x:33374,y:33801,varname:node_5067,prsc:2|A-5045-R,B-4341-U;n:type:ShaderForge.SFN_Vector1,id:6078,x:33516,y:33722,varname:node_6078,prsc:2,v1:0;n:type:ShaderForge.SFN_Slider,id:6180,x:33051,y:34051,ptovrint:False,ptlb:DissovleEdge,ptin:_DissovleEdge,varname:node_6180,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:1;n:type:ShaderForge.SFN_Append,id:1462,x:32640,y:33229,varname:node_1462,prsc:2|A-4341-Z,B-4341-W;n:type:ShaderForge.SFN_Add,id:4937,x:32803,y:33104,varname:node_4937,prsc:2|A-1667-OUT,B-1462-OUT;n:type:ShaderForge.SFN_Add,id:5211,x:32846,y:33264,varname:node_5211,prsc:2|A-1667-OUT,B-1462-OUT;proporder:2842-9000-71-2782-4705-1331-1150-5045-5026-2321-2123-8972-277-6559-7105-3928-1632-6180;pass:END;sub:END;*/

Shader "ProjectU/Particle/Ground/G_Blended_Distorted_Panner_Dissolve2" {
    Properties {
        [HDR]_Color ("Color", Color) = (0.5,0.5,0.5,1)
        _color_intensity ("color_intensity", Float ) = 1
        _EXP ("EXP", Range(0.3, 3)) = 1
        _Noise ("Noise", 2D) = "white" {}
        _Noise_uspeed ("Noise_uspeed", Float ) = 0
        _Noise_vspeed ("Noise_vspeed", Float ) = -3
        _Noise_Vintensity ("Noise_Vintensity", Float ) = 0
        _Dissolve ("Dissolve", 2D) = "white" {}
        _Dissolve_uspeed ("Dissolve_uspeed", Float ) = 0
        _Dissolve_vspeed ("Dissolve_vspeed", Float ) = -1
        _Texture01 ("Texture01", 2D) = "white" {}
        [HDR]_EDgecolor ("EDgecolor", Color) = (0.5,0.5,0.5,1)
        _EDgecolor_intensity ("EDgecolor_intensity", Float ) = 1
        _EDGe ("EDGe", Float ) = 0
        _Noise_Uintensity ("Noise_Uintensity", Float ) = 0
        _RGBA ("RGB/A", Range(0, 1)) = 0
        _DissolveNoise ("DissolveNoise", Range(1, 0)) = 1
        _DissovleEdge ("DissovleEdge", Range(0, 1)) = 1
        [HideInInspector]_Cutoff ("Alpha cutoff", Range(0,1)) = 0.5
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        LOD 100
        Pass {
            Name "FORWARD"
            Blend SrcAlpha OneMinusSrcAlpha
            Cull Off
            ZTest Always
            ZWrite Off
            
            Stencil {
                Ref 0
                Comp Equal
            }
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            //#pragma only_renderers d3d9 d3d11 glcore gles gles3 metal n3ds wiiu 
            #pragma target 3.0
            uniform sampler2D _Noise; uniform float4 _Noise_ST;
            uniform float _Noise_uspeed;
            uniform float _Noise_vspeed;
            uniform float _Noise_Vintensity;
            uniform sampler2D _Dissolve; uniform float4 _Dissolve_ST;
            uniform float _Dissolve_uspeed;
            uniform float _Dissolve_vspeed;
            uniform float4 _Color;
            uniform float _color_intensity;
            uniform float _EXP;
            uniform sampler2D _Texture01; uniform float4 _Texture01_ST;
            uniform float4 _EDgecolor;
            uniform float _EDgecolor_intensity;
            uniform float _EDGe;
            uniform float _Noise_Uintensity;
            uniform float _RGBA;
            uniform float _DissolveNoise;
            uniform float _DissovleEdge;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 texcoord1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.uv1 = v.texcoord1;
                o.vertexColor = v.vertexColor;
                o.pos = UnityObjectToClipPos( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 node_1212 = _Time;
                float2 node_3462 = (i.uv0+float2((_Noise_uspeed*node_1212.g),(_Noise_vspeed*node_1212.g)));
                float4 _Noise_var = tex2D(_Noise,TRANSFORM_TEX(node_3462, _Noise));
                float2 node_1667 = float2((i.uv0.r+(_Noise_var.r*i.uv0.r*_Noise_Uintensity)),((_Noise_Vintensity*_Noise_var.r*i.uv0.g)+i.uv0.g));
                float2 node_1462 = float2(i.uv1.b,i.uv1.a);
                float2 node_5211 = (node_1667+node_1462);
                float4 node_1589 = tex2D(_Texture01,TRANSFORM_TEX(node_5211, _Texture01));
                float2 node_4937 = (node_1667+node_1462);
                float4 node_8425 = tex2D(_Texture01,TRANSFORM_TEX(node_4937, _Texture01));
                float3 emissive = (pow(((_Color.rgb*node_1589.rgb*_color_intensity)+(step((node_8425.rgb*10.0),_EDGe)*_EDgecolor.rgb*_EDgecolor_intensity)),_EXP)*i.vertexColor.rgb);
                float3 finalColor = emissive;
                float4 node_6388 = _Time;
                float2 node_1713 = lerp(node_1667,(i.uv0+float2((_Dissolve_uspeed*node_6388.g),(_Dissolve_vspeed*node_6388.g))),_DissolveNoise);
                float4 _Dissolve_var = tex2D(_Dissolve,TRANSFORM_TEX(node_1713, _Dissolve));
                return fixed4(finalColor,(i.vertexColor.a*lerp(node_8425.a,saturate((node_8425.r+node_8425.g+node_8425.b)),_RGBA)*smoothstep( 0.0, _DissovleEdge, (_Dissolve_var.r-i.uv1.r) )));
            }
            ENDCG
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
