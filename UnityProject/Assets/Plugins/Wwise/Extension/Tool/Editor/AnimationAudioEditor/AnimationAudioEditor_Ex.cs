namespace AK.Wwise.Editor
{
	internal partial class AnimationAudioEditor
	{
		/*
		private void InitializeForLovania()
		{
			const string configPath = "Assets/Editor/AnimationPreviewConfig.json";
			string configStr = File.ReadAllText(configPath);
			m_animationPreviewConfig = JsonUtility.FromJson<AnimationPreviewConfig>(configStr);
		}

		private AnimationPreviewConfig m_animationPreviewConfig;
		*/

		/*
		private void ResetSkillForU2()
		{
			m_dramaGameObject = null;
			m_dramaName = string.Empty;

			// 清理之前加载Drama技能时加载的特效
			foreach (var effectGameObject in m_effectGameObjectList)
			{
				if (EditorApplication.isPlaying)
				{
					Destroy(effectGameObject);
				}
				else
				{
					DestroyImmediate(effectGameObject);
				}
			}

			m_effectGameObjectList.Clear();

			m_onHitSoundGroupTrack = null;

			m_videoDuration = 0;
			m_videoStartFrame = 0;
		}
		*/

		/*
		private string GetAnimationRelativeRootPathForLovania()
		{
			if (m_modelPrefab == null)
			{
				return string.Empty;
			}

			string modelPrefabAssetPath = AssetDatabase.GetAssetPath(m_modelPrefab);
			string modelPrefabAssetRootPath =
				modelPrefabAssetPath.Substring(0, modelPrefabAssetPath.LastIndexOf("/", StringComparison.Ordinal));
			modelPrefabAssetRootPath = modelPrefabAssetRootPath.Replace("/Model", "");
			return string.Concat(modelPrefabAssetRootPath, "/Animation");
		}

		private void CharacterViewOnGUIForLovania()
		{
			GUILayout.Space(10);
			using (new GUILayout.VerticalScope("box"))
			{
				if (m_animationPreviewConfig != null)
				{
					if (m_animationPreviewConfig.items.Length > 0)
					{
						GUILayout.Label("角色列表:");
						m_characterListScrollViewPosition = GUILayout.BeginScrollView(m_characterListScrollViewPosition,
							GUILayout.Width(position.width - 15), GUILayout.Height(150));
						for (int i = 0; i < m_animationPreviewConfig.items.Length; i++)
						{
							AnimationPreviewItem animationPreviewItem = m_animationPreviewConfig.items[i];
							GUI.skin.button.alignment = TextAnchor.MiddleLeft;
							if (GUILayout.Button($"{i + 1}. {animationPreviewItem.name}"))
							{
								var modelPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(animationPreviewItem.modelPath);
								m_modelPrefab = modelPrefab;
								// 实例化角色模型到 "Model" GameObject下
								LoadModelObject(m_modelPrefab);

								// 角色动画根路径
								foreach (string animationFolderPath in animationPreviewItem.animationFolderPaths)
								{
									// 遍历角色模型Prefab的动画资源目录, 添加到字典中
									m_modelAnimations.Clear();

									if (m_animator)
									{
										RuntimeAnimatorController animatorController = m_animator.runtimeAnimatorController;
										if (animatorController)
										{
											AnimationClip[] animations = animatorController.animationClips;
											foreach (AnimationClip animation in animations)
											{
												AddAnimationToDictionary(animation, AnimationFrom.AnimatorController);
											}
										}
									}

									if (!string.IsNullOrEmpty(animationFolderPath))
									{
										if (!Directory.Exists(animationFolderPath))
										{
											return;
										}

										RecursivelyAddDirectoryInfo(animationFolderPath);
									}
								}
							}

							m_isDisplayBasicAnimationList = true;

							GUI.skin.button.alignment = TextAnchor.MiddleCenter;

							GUILayout.Space(5);
						}
					}

					GUILayout.EndScrollView();
				}
			}
		}

		private Vector2 m_characterListScrollViewPosition;
		*/

		/*
		/// <summary>
		/// 优化缩短动画文件路径显示
		/// </summary>
		/// <param name="animationClipFilePath">动画文件路径</param>
		/// <returns>优化后的动画文件路径</returns>
		private static string TrimAnimationClipFilePathDisplayForU2(string animationClipFilePath)
		{
			return animationClipFilePath.StartsWith("Assets/GameProject/RuntimeAssets/", StringComparison.OrdinalIgnoreCase)
				? animationClipFilePath.Replace("Assets/GameProject/RuntimeAssets/", "")
				: animationClipFilePath;
		}

		private void SetCameraFollowForU2()
		{
			if (!Camera.main)
			{
				return;
			}

			var virtualCamera = GameObjectExtensions.GetOrAddComponent<CinemachineVirtualCamera>(Camera.main.gameObject);
			if (!m_modelGameObject)
			{
				virtualCamera.Follow = m_modelGameObjectRootPath.transform;
				virtualCamera.LookAt = m_modelGameObjectRootPath.transform;
			}
			else
			{
				var bip001Transform = m_modelGameObject.transform.Find("Skin/Root/Bip001");
				if (!bip001Transform)
				{
					return;
				}

				virtualCamera.LookAt = bip001Transform;
			}
		}

		#region Drama技能相关

		private GameObject m_dramaGameObject; // 当前Drama技能的游戏对象

		private string m_dramaName; // Drama技能名称

		/// <summary>
		/// Drama技能信息列表
		/// </summary>
		private readonly Dictionary<string, DramaSkillInfo> m_dramaSkillInfoDictionary = new Dictionary<string, DramaSkillInfo>();

		/// <summary>
		/// 当前加载角色的Drama技能信息列表
		/// </summary>
		private readonly List<DramaSkillInfo> m_dramaSkillInfoList = new List<DramaSkillInfo>();

		private string m_dramaID = string.Empty; // 角色Drama技能ID

		private Vector2 m_dramaSkillListScrollViewPosition;

		private void OnDramaSkillGUIForU2()
		{
			GUILayout.Space(10);
			using (new GUILayout.VerticalScope("box"))
			{
				EditorGUILayout.BeginHorizontal();
				{
					m_dramaID = EditorGUILayout.TextField("Drama序号:", m_dramaID.Trim());
					if (GUILayout.Button("加载", GUILayout.MaxWidth(150)))
					{
						ReadDramaSkillID(m_dramaID);
					}
				}
				EditorGUILayout.EndHorizontal();

				if (m_dramaGameObject != null)
				{
					EditorGUILayout.ObjectField("Drama游戏对象: ", m_dramaGameObject, typeof(Object), false);
				}

				if (m_dramaSkillInfoList.Count > 0)
				{
					m_dramaSkillListScrollViewPosition = GUILayout.BeginScrollView(m_dramaSkillListScrollViewPosition,
						GUILayout.Width(position.width - 15), GUILayout.Height(150));
					for (int i = 0; i < m_dramaSkillInfoList.Count; i++)
					{
						var dramaSkillInfo = m_dramaSkillInfoList[i];
						GUI.skin.button.alignment = TextAnchor.MiddleLeft;
						if (GUILayout.Button(
								string.Format("{0}. {1} ({2})", i + 1, dramaSkillInfo.m_description, dramaSkillInfo.m_animation),
								GUILayout.Width(500)))
						{
							LoadDramaSkill(dramaSkillInfo);
						}

						GUI.skin.button.alignment = TextAnchor.MiddleCenter;

						GUILayout.Space(5);
					}

					GUILayout.EndScrollView();
				}
			}
		}

		private void LoadSkillShowExcelForU2()
		{
			// 读取SkillShow表
			string skillShowExcelPath = Application.dataPath + "/../../../Design/配置数据表/SkillShow.xlsx";
			var skillShowExcelFileStream = new FileStream(skillShowExcelPath, FileMode.Open, FileAccess.Read);
			var skillShowExcelPackage = new ExcelPackage(skillShowExcelFileStream);
			var skillShowWorkSheet = skillShowExcelPackage.Workbook.Worksheets[1];

			int idHeaderColumn = 0, descriptionHeaderColumn = 0, animationHeaderColumn = 0, castEffectHeaderColumn = 0;
			for (int i = 1; i < skillShowWorkSheet.Dimension.End.Column; i++)
			{
				string header = skillShowWorkSheet.GetValue<string>(4, i);
				switch (header)
				{
					case "ID":
						idHeaderColumn = i;
						break;
					case "Desc":
						descriptionHeaderColumn = i;
						break;
					case "Animation":
						animationHeaderColumn = i;
						break;
					case "CastEffect":
						castEffectHeaderColumn = i;
						break;
				}
			}

			if (idHeaderColumn == 0 || descriptionHeaderColumn == 0 || animationHeaderColumn == 0 || castEffectHeaderColumn == 0)
			{
				Debug.LogError("音频工具: SKillAudioEditor: 无法解析SkillShow表的表头.");
				return;
			}

			m_dramaSkillInfoDictionary.Clear();

			for (int i = 13; i <= skillShowWorkSheet.Dimension.End.Row; i++)
			{
				string id = skillShowWorkSheet.GetValue<string>(i, idHeaderColumn);
				if (string.IsNullOrEmpty(id))
				{
					continue;
				}

				string description = skillShowWorkSheet.GetValue<string>(i, descriptionHeaderColumn);
				description = string.IsNullOrEmpty(description) ? string.Empty : description.Trim();

				string animation = skillShowWorkSheet.GetValue<string>(i, animationHeaderColumn);
				if (string.IsNullOrEmpty(animation))
				{
					continue;
				}

				string castEffect = skillShowWorkSheet.GetValue<string>(i, castEffectHeaderColumn);
				if (string.IsNullOrEmpty(castEffect))
				{
					castEffect = string.Empty;
				}

				var dramaSkillInfo = new DramaSkillInfo
				{
					m_animation = animation.Trim(),
					m_description = description.Trim(),
					m_castEffect = castEffect.Trim()
				};

				m_dramaSkillInfoDictionary.Add(id.Trim(), dramaSkillInfo);
			}

			skillShowWorkSheet.Dispose();
			skillShowExcelPackage.Dispose();
			skillShowExcelFileStream.Close();
		}

		/// <summary>
		/// 查找当前加载角色的Drama技能
		/// </summary>
		private void SearchCharacterDramaSkillForU2()
		{
			if (m_modelPrefab == null)
			{
				return;
			}

			m_dramaSkillInfoList.Clear();

			// 如果选择的角色是Hero, 搜索技能ID以 "71" 开头后接角色ID的技能ID, 怪物类的搜索技能ID以 "7" 开头后接怪物ID的技能ID
			string modelName = m_modelPrefab.name;
			modelName = modelName.Replace("U2_", "");
			string modelID = Regex.Match(modelName, @"\d+").Value;
			string searchID = string.Concat(modelName.StartsWith("Hero") ? "71" : "7", modelID);

			foreach (var dramaSkillInfo in m_dramaSkillInfoDictionary)
			{
				if (dramaSkillInfo.Key.StartsWith(searchID))
				{
					m_dramaSkillInfoList.Add(dramaSkillInfo.Value);
				}
			}
		}

		private void ReadDramaSkillID(string dramaSkillID)
		{
			if (m_modelPrefab == null)
			{
				return;
			}

			dramaSkillID = dramaSkillID.Trim();
			if (m_dramaSkillInfoDictionary.ContainsKey(dramaSkillID))
			{
				LoadDramaSkill(m_dramaSkillInfoDictionary[dramaSkillID]);
			}
			else
			{
				EditorUtility.DisplayDialog("错误", string.Format("无法找到该Drama技能ID: \"{0}\".", dramaSkillID), "确定");
			}
		}

		private void LoadDramaSkill(DramaSkillInfo dramaSkillInfo)
		{
			ResetSkill();

			if (m_modelPrefab == null)
			{
				return;
			}

			m_dramaName = dramaSkillInfo.m_animation;

			// 查找Drama
			if (m_modelPrefab.transform.Find("Drama/" + m_dramaName) == null)
			{
				return;
			}

			var originalTracks = m_skillTimelineAsset.GetRootTracks();
			foreach (var originalTrack in originalTracks)
			{
				m_skillTimelineAsset.DeleteTrack(originalTrack);
			}

			m_dramaGameObject = m_modelPrefab.transform.Find("Drama/" + m_dramaName).gameObject;
			var dramaSequence = m_dramaGameObject.GetComponent<DramaSequenceAnimationWrap>();
			var dramaList = dramaSequence.GetDramaPramaInfoList();

			foreach (var dramaPramaInfo in dramaList)
			{
				foreach (var dramaParam in dramaPramaInfo.m_dramaParams)
				{
					// 加载动画及Wwise事件
					if (dramaParam.m_dramaType == DramaBase.E_DramaType.E_PlayAnimation)
					{
						float startTime = dramaPramaInfo.m_startFrame / m_skillTimelineAsset.editorSettings.fps;
						if (!dramaParam.m_dramaParamsDict.m_keys.Contains("replaceName"))
						{
							continue;
						}

						string animationName = dramaParam.m_dramaParamsDict["replaceName"].m_stringValue;
						if (string.IsNullOrEmpty(animationName))
						{
							continue;
						}

						string[] animationAssetGuids = AssetDatabase.FindAssets(animationName, new[] { m_animationAssetRelativeRootPath });
						if (animationAssetGuids.First() == "")
						{
							continue;
						}

						string animationAssetPath = AssetDatabase.GUIDToAssetPath(animationAssetGuids.First());
						var animationClip = LoadAnimation(animationAssetPath, animationName);
						LoadAnimationToTrack(animationClip, startTime);
						var animationEvents = animationClip.events;
						foreach (var animationEvent in animationEvents)
						{
							if (animationEvent.functionName != AnimationAudioSaveData.AnimAudioFunctionName)
							{
								continue;
							}

							LoadWwiseEvent(animationEvent.stringParameter, animationEvent.time, startTime);
						}

						break;
					}

					// 加载CriWare Mana视频进入Timeline中
					if (dramaParam.m_dramaType == DramaBase.E_DramaType.E_DramaPlayVideo)
					{
						LoadVideo(dramaParam, dramaPramaInfo.m_startFrame);
					}
				}
			}

			// 预创建3个空白Wwise Event轨
			m_skillTimelineAsset.CreateTrack<BjTimelineWwiseEventTrack>(null, "");
			m_skillTimelineAsset.CreateTrack<BjTimelineWwiseEventTrack>(null, "");
			m_skillTimelineAsset.CreateTrack<BjTimelineWwiseEventTrack>(null, "");

			// 读取特效表json
			string effectJsonPath = Application.dataPath + "/../../../ConfigData/GMTool/Data/Json/ConfigDataCharacterEffectInfo.json";
			string effectJsonFullPath = Path.GetFullPath(effectJsonPath);
			var effectStreamReader = new StreamReader(effectJsonFullPath);
			string effectJsonFile = effectStreamReader.ReadToEnd();
			var effectJsonNode = JSON.Parse(effectJsonFile);

			// 载入特效
			string castEffect = dramaSkillInfo.m_castEffect;
			string[] castEffectList = { };
			if (!string.IsNullOrEmpty(castEffect))
			{
				castEffectList = castEffect.Split(new[] { "/" }, StringSplitOptions.RemoveEmptyEntries);
			}

			int castEffectIndex = 0;
			foreach (var t1 in dramaList)
			{
				foreach (var t in t1.m_dramaParams)
				{
					bool isLoadEffectFromDrama = false;
					if (t.m_dramaType != DramaBase.E_DramaType.E_PlayEfx)
					{
						continue;
					}

					float startFrame = t1.m_startFrame;
					var attachType = (DramaAttachType) t.m_dramaParamsDict["attachType"].m_enumValueIndex;
					string effectPath = string.Empty;

					if (t.m_dramaParamsDict.m_keys.Contains("efxPath"))
					{
						effectPath = t.m_dramaParamsDict["efxPath"].m_stringValue;
					}

					int effectID = 0;
					if (t.m_dramaParamsDict.m_keys.Contains("efxId"))
					{
						effectID = t.m_dramaParamsDict["efxId"].m_intValue;
					}

					if (!string.IsNullOrEmpty(effectPath))
					{
						LoadEffect(effectPath, startFrame, attachType);
						isLoadEffectFromDrama = true;
					}
					else if (effectID != 0)
					{
						effectPath = effectJsonNode[effectID.ToString()]["ResPath"].Value;
						LoadEffect(effectPath, startFrame, attachType);
						isLoadEffectFromDrama = true;
					}

					if (castEffectList.Length <= castEffectIndex)
					{
						continue;
					}

					if (!isLoadEffectFromDrama)
					{
						string castEffectID = castEffectList[castEffectIndex];
						effectPath = effectJsonNode[castEffectID]["ResPath"].Value;
						LoadEffect(effectPath, startFrame, attachType);
						castEffectIndex++;
					}
				}
			}

			// 从角色motions文件查找该Drama动画的受击声音数据
			if (m_characterMotionsSettingAsset != null)
			{
				foreach (var characterMotion in m_characterMotionsSettingAsset.m_motions)
				{
					if (characterMotion.m_animationClipName == m_dramaName)
					{
						m_onHitUnits = characterMotion.m_units;
						break;
					}
				}
			}

			RefreshOnHitSoundToOnHitTrack();

			RefreshTimelineWindow();
		}


		/// <summary>
		/// 动画名过滤, 用于动画列表显示时过滤不需要的动画
		/// </summary>
		/// <param name="animationName">动画名</param>
		/// <returns>true表示需要过滤掉, false相反</returns>
		private bool AnimationNameFilterForU2(string animationName)
		{
			// 排除以下条件
			if (m_dramaSkillInfoList != null)
			{
				if (m_dramaSkillInfoList.Any(dramaSkillInfo => dramaSkillInfo.m_animation == animationName))
				{
					return true;
				}
			}

			if (animationName.StartsWith("Camp_", StringComparison.OrdinalIgnoreCase))
			{
				return true;
			}

			if (animationName.StartsWith("OpenAnim_", StringComparison.OrdinalIgnoreCase))
			{
				return true;
			}

			if (animationName.StartsWith("Story_", StringComparison.OrdinalIgnoreCase))
			{
				return true;
			}

			if (animationName.StartsWith("Combat_", StringComparison.OrdinalIgnoreCase))
			{
				if (animationName.Contains("PreOnHitAttack"))
				{
					return true;
				}

				if (animationName.Contains("ReturnBackRootMotion"))
				{
					return true;
				}

				if (animationName.Contains("ReturnForwardRootMotion"))
				{
					return true;
				}

				if (animationName.Contains("TakeAttackRootMotion"))
				{
					return true;
				}
			}

			if (animationName.Contains("Idle") && !animationName.Contains("Pose"))
			{
				return true;
			}

			if (animationName.Contains("Stun"))
			{
				return true;
			}

			return false;
		}

		/// <summary>
		/// 动画路径过滤, 用于动画列表显示时过滤不需要的动画
		/// </summary>
		/// <param name="animationPath">动画路径</param>
		/// <returns>true表示需要过滤掉, false相反</returns>
		private bool AnimationPathFilterForU2(string animationPath)
		{
			// 排除以下条件
			if (animationPath.Contains("/Morph/"))
			{
				return true;
			}

			return false;
		}

		/// <summary>
		/// Drama技能信息
		/// </summary>
		private class DramaSkillInfo
		{
			/// <summary>
			/// Drama的动画
			/// </summary>
			public string m_animation = string.Empty;

			/// <summary>
			/// Drama技能的描述
			/// </summary>
			public string m_description = string.Empty;

			/// <summary>
			/// Drama技能的特效
			/// </summary>
			public string m_castEffect = string.Empty;
		}

		#endregion Drama技能相关

		#region 特效相关

		/// <summary>
		/// 加载的特效游戏对象列表
		/// </summary>
		private readonly List<GameObject> m_effectGameObjectList = new List<GameObject>();

		/// <summary>
		/// 特效GroupTrack
		/// </summary>
		private GroupTrack m_effectGroupTrack;

		private const string EffectGroupTrackName = "特效";

		public void LoadEffect(string resPath, float startFrame, DramaAttachType attachType)
		{
			if (m_effectGroupTrack == null)
			{
				m_effectGroupTrack = m_skillTimelineAsset.CreateTrack<GroupTrack>(null, EffectGroupTrackName);
			}

			var effectPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(resPath);
			var effectGameObject = PrefabUtility.InstantiatePrefab(effectPrefab) as GameObject;
			if (ReferenceEquals(effectGameObject, null))
			{
				return;
			}

			if (!effectGameObject.GetComponentInChildren<ParticleSystem>())
			{
				if (EditorApplication.isPlaying)
				{
					Destroy(effectGameObject);
				}
				else
				{
					DestroyImmediate(effectGameObject);
				}

				return;
			}

			if (!m_effectGameObjectList.Contains(effectGameObject))
			{
				m_effectGameObjectList.Add(effectGameObject);
			}

			effectGameObject.transform.SetParent(m_effectGameObjectRootPath.transform);
			effectGameObject.SetActive(false);
			if (attachType != DramaAttachType.Self)
			{
				effectGameObject.transform.position = new Vector3(0, 0, 5);
			}
			else
			{
				effectGameObject.transform.SetParent(m_modelGameObject.transform);
				effectGameObject.transform.localPosition = Vector3.zero;
			}

			string castEffectName = resPath.Substring(resPath.LastIndexOf("/", StringComparison.Ordinal) + 1,
				resPath.LastIndexOf(".", StringComparison.Ordinal) - resPath.LastIndexOf("/", StringComparison.Ordinal) - 1);
			var castEffectControlTrack =
				m_skillTimelineAsset.CreateTrack<ControlTrack>(m_effectGroupTrack, castEffectName);
			var castEffectClip = castEffectControlTrack.CreateClip<ControlPlayableAsset>();
			castEffectClip.displayName = castEffectName;
			var castEffectClipAsset = castEffectClip.asset as ControlPlayableAsset;
			if (!ReferenceEquals(castEffectClipAsset, null))
			{
				castEffectClipAsset.sourceGameObject.exposedName = GUID.Generate().ToString();
				m_playableDirector.SetReferenceValue(castEffectClipAsset.sourceGameObject.exposedName, effectGameObject);
			}

			castEffectClip.start = startFrame / m_skillTimelineAsset.editorSettings.fps;
			castEffectClip.duration = effectGameObject.GetComponentInChildren<ParticleSystem>().main.duration;
		}

		#endregion 特效相关

		#region 受击相关

		private CharacterMotionsSetting m_characterMotionsSettingAsset; // 角色模型的Motions设置, 用于读写其中的技能受击声音配置

		private List<MotionInterruptUnit> m_onHitUnits; // 当前加载Drama技能的受击点配置

		private bool m_isLoadAndPreviewOnHitSoundInTimeline; // 是否在Timeline中加载并预览受击声音

		/// <summary>
		/// 受击声音GroupTrack
		/// </summary>
		private GroupTrack m_onHitSoundGroupTrack;

		private const string OnHitSoundGroupTrackName = "受击声音";

		private const string OnHitSoundTrackName = "受击声音轨道";

		private const string OnHitSoundEventBasicName = "CH_General_OnHit_";

		/// <summary>
		/// 受击声音配置GUI显示
		/// </summary>
		private void OnOnHitSoundGUIForU2()
		{
			using (new EditorGUILayout.VerticalScope("box"))
			{
				EditorGUILayout.BeginVertical();
				{
					EditorGUI.BeginChangeCheck();
					{
						m_isLoadAndPreviewOnHitSoundInTimeline =
							EditorGUILayout.Toggle("是否加载受击声音试听: ", m_isLoadAndPreviewOnHitSoundInTimeline);
					}
					if (EditorGUI.EndChangeCheck())
					{
						RefreshOnHitSoundToOnHitTrack();
					}

					var onHitUnits = m_onHitUnits;
					if (onHitUnits != null && onHitUnits.Count > 0)
					{
						GUILayout.Space(10);

						EditorGUILayout.LabelField("打击点:");

						EditorGUILayout.BeginHorizontal();
						{
							GUILayout.Label("武器类型", GUILayout.Width(150));
							GUILayout.Space(10);
							GUILayout.Label("攻击方式", GUILayout.Width(150));
						}
						EditorGUILayout.EndHorizontal();

						EditorGUI.BeginChangeCheck();
						for (int i = 0; i < onHitUnits.Count; i++)
						{
							var onHitUnit = onHitUnits[i];
							EditorGUILayout.BeginHorizontal();
							{
								var weaponTypeDisplay = (WeaponTypeDisplay) (int) onHitUnit.m_weaponType;
								onHitUnit.m_weaponType =
									(AudioUtility.WeaponType) EditorGUILayout.EnumPopup(weaponTypeDisplay, GUILayout.Width(150));
								GUILayout.Space(5);
								if (onHitUnit.m_weaponType != AudioUtility.WeaponType.None)
								{
									switch (onHitUnit.m_weaponType)
									{
										case AudioUtility.WeaponType.Sword:
										case AudioUtility.WeaponType.Spear:
										case AudioUtility.WeaponType.BroadSword:
										case AudioUtility.WeaponType.Dagger:
										{
											var attackTypeDisplay = (AttackTypeDisplay1) (int) onHitUnit.m_attackType;
											onHitUnit.m_attackType =
												(AudioUtility.AttackType) EditorGUILayout.EnumPopup(attackTypeDisplay,
													GUILayout.Width(150));
											break;
										}
										case AudioUtility.WeaponType.Arrow:
										case AudioUtility.WeaponType.Magic:
										{
											var attackTypeDisplay = (AttackTypeDisplay2) (int) onHitUnit.m_attackType;
											onHitUnit.m_attackType =
												(AudioUtility.AttackType) EditorGUILayout.EnumPopup(attackTypeDisplay,
													GUILayout.Width(150));
											break;
										}
										case AudioUtility.WeaponType.CQB:
										{
											var attackTypeDisplay = (AttackTypeDisplay3) (int) onHitUnit.m_attackType;
											onHitUnit.m_attackType =
												(AudioUtility.AttackType) EditorGUILayout.EnumPopup(attackTypeDisplay,
													GUILayout.Width(150));
											break;
										}
										case AudioUtility.WeaponType.None:
											break;
										default:
										{
											var attackTypeDisplay = (AttackTypeDisplay) (int) onHitUnit.m_attackType;
											onHitUnit.m_attackType =
												(AudioUtility.AttackType) EditorGUILayout.EnumPopup(attackTypeDisplay,
													GUILayout.Width(150));
											break;
										}
									}
								}
								else
								{
									onHitUnit.m_attackType = AudioUtility.AttackType.None;
								}

								GUILayout.Space(5);

								if (i == 0 && onHitUnit.m_weaponType != AudioUtility.WeaponType.None &&
									onHitUnit.m_attackType != AudioUtility.AttackType.None)
								{
									if (GUILayout.Button("复制", GUILayout.MaxWidth(150)))
									{
										copyFirstOnHitUnitSoundConfigure(onHitUnits);
									}
								}
							}
							EditorGUILayout.EndHorizontal();
						}

						if (EditorGUI.EndChangeCheck())
						{
							RefreshOnHitSoundToOnHitTrack();
						}
					}
				}
				EditorGUILayout.EndVertical();
			}
		}

		/// <summary>
		/// 加载角色的motions文件以读写攻击技能的受击声音属性
		/// </summary>
		private void LoadCharacterMotionsAssetForU2(GameObject modelPrefab)
		{
			m_characterMotionsSettingAsset = null;
			m_onHitUnits = null;
			string modelPrefabAssetPath = AssetDatabase.GetAssetPath(modelPrefab);
			string modelPrefabAssetRootPath =
				modelPrefabAssetPath.Substring(0, modelPrefabAssetPath.LastIndexOf("/", StringComparison.Ordinal));
			string motionsAssetPath = string.Concat(modelPrefabAssetRootPath, "/motions.asset");
			if (!File.Exists(motionsAssetPath))
			{
				Debug.Log("音频工具: 技能声音编辑工具: 该角色的motions文件不存在, 请确认: " + motionsAssetPath);
			}
			else
			{
				m_characterMotionsSettingAsset =
					AssetDatabase.LoadAssetAtPath<CharacterMotionsSetting>(motionsAssetPath);
			}
		}

		/// <summary>
		/// 复制第一个受击声音配置到后面所有受击点
		/// </summary>
		private void copyFirstOnHitUnitSoundConfigure(List<MotionInterruptUnit> onHitUnits)
		{
			if (onHitUnits == null || onHitUnits.Count <= 1 || onHitUnits[0].m_weaponType == AudioUtility.WeaponType.None ||
				onHitUnits[0].m_attackType == AudioUtility.AttackType.None)
			{
				return;
			}

			for (int i = 1; i < onHitUnits.Count; i++)
			{
				onHitUnits[i].m_weaponType = onHitUnits[0].m_weaponType;
				onHitUnits[i].m_attackType = onHitUnits[0].m_attackType;
			}
		}

		/// <summary>
		/// 刷新受击声音轨道及受击声音片段
		/// </summary>
		private void RefreshOnHitSoundToOnHitTrack()
		{
			var onHitUnits = m_onHitUnits;

			if (m_onHitSoundGroupTrack != null)
			{
				var onHitSoundTracks = m_onHitSoundGroupTrack.GetChildTracks();
				foreach (var onHitSoundTrack in onHitSoundTracks)
				{
					m_skillTimelineAsset.DeleteTrack(onHitSoundTrack);
				}
			}

			if (onHitUnits != null && onHitUnits.Count > 0 && m_isLoadAndPreviewOnHitSoundInTimeline)
			{
				if (m_onHitSoundGroupTrack == null)
				{
					m_onHitSoundGroupTrack = m_skillTimelineAsset.CreateTrack<GroupTrack>(null, OnHitSoundGroupTrackName);
				}

				// 读取当前技能Drama中的OnHit的Drama点时间
				if (m_modelPrefab.transform.Find("Drama/" + m_dramaName) == null)
				{
					return;
				}

				var skillName = m_modelPrefab.transform.Find("Drama/" + m_dramaName).gameObject;
				var dramaSequence = skillName.GetComponent<DramaSequenceAnimationWrap>();
				var dramaList = dramaSequence.GetDramaPramaInfoList();

				int onHitSoundNo = 0;
				foreach (float onHitSoundStartFrame in from dramaPramaInfo in dramaList
						 from dramaParam in dramaPramaInfo.m_dramaParams
						 where dramaParam.m_dramaType == DramaBase.E_DramaType.E_OnHit ||
							   dramaParam.m_dramaType == DramaBase.E_DramaType.E_DramaSpawnProjectile
						 select dramaPramaInfo.m_startFrame)
				{
					if (onHitSoundNo >= onHitUnits.Count || onHitUnits[onHitSoundNo].m_attackType == AudioUtility.AttackType.None ||
						onHitUnits[onHitSoundNo].m_weaponType == AudioUtility.WeaponType.None)
					{
						onHitSoundNo++;
						continue;
					}

					string onHitSoundEventName = OnHitSoundEventBasicName + onHitUnits[onHitSoundNo].m_weaponType + "_" +
												 onHitUnits[onHitSoundNo].m_attackType;
					float finalStartTime = onHitSoundStartFrame >= m_videoStartFrame
						? onHitSoundStartFrame / m_skillTimelineAsset.editorSettings.fps + m_videoDuration
						: onHitSoundStartFrame / m_skillTimelineAsset.editorSettings.fps;
					string onHitSoundTrackName = string.Format("{0} {1}", OnHitSoundTrackName, onHitSoundNo);
					var onHitSoundTrack =
						m_skillTimelineAsset.CreateTrack<BjTimelineWwiseEventTrack>(m_onHitSoundGroupTrack, onHitSoundTrackName);
					LoadWwiseEvent(onHitSoundEventName, 0, finalStartTime, onHitSoundTrack);

					onHitSoundNo++;
				}
			}

			RefreshTimelineWindow();
		}

		/// <summary>
		/// 保存受击声音数据
		/// </summary>
		private void SaveForU2()
		{
			if (m_characterMotionsSettingAsset == null)
			{
				return;
			}

			EditorUtility.SetDirty(m_characterMotionsSettingAsset);
			AssetDatabase.SaveAssets();
			AssetDatabase.Refresh();
		}

		#region 受击声音配置枚举显示, 请与原枚举定义严格一致!

		/// <summary>
		/// 武器类型显示
		/// </summary>
		private enum WeaponTypeDisplay
		{
			无 = 0,
			剑 = 1,
			枪矛 = 2,
			大斧大剑 = 3,
			匕首 = 4,
			弓箭 = 5,
			魔法 = 6,
			肉搏 = 7
		}

		/// <summary>
		/// 攻击方式完整显示
		/// </summary>
		private enum AttackTypeDisplay
		{
			无 = 0,
			挥砍 = 1,
			刺 = 2,
			通用 = 3,
			拳 = 4,
			脚 = 5,
			臂铠 = 6,
			盾 = 7
		}

		/// <summary>
		/// 攻击方式显示1, 用于剑, 矛, 大斧大剑, 匕首
		/// </summary>
		private enum AttackTypeDisplay1
		{
			挥砍 = 1,
			刺 = 2
		}

		/// <summary>
		/// 攻击方式显示2, 用于弓箭, 魔法
		/// </summary>
		private enum AttackTypeDisplay2
		{
			通用 = 3
		}

		/// <summary>
		/// 攻击方式显示3, 用于肉搏
		/// </summary>
		private enum AttackTypeDisplay3
		{
			拳 = 4,
			脚 = 5,
			臂铠 = 6,
			盾 = 7
		}

		#endregion 受击声音配置枚举显示

		#endregion 受击相关

		#region 技能属性相关

		private const string AttributeSwitchGroup = "AttackElement"; // 角色技能属性切换组名称

		private List<string> m_attributeSwitchList = new List<string>(); // 角色技能属性列表

		private int m_attributeSwitchSelectionIndex; // 角色技能属性选择索引

		private void OnAttributeGUIForU2()
		{
			if (m_attributeSwitchList != null && m_attributeSwitchList.Count > 0)
			{
				GUILayout.Space(10);
				using (new GUILayout.VerticalScope("box"))
				{
					EditorGUILayout.BeginHorizontal();
					{
						m_attributeSwitchSelectionIndex = EditorGUILayout.Popup("技能属性试听:", m_attributeSwitchSelectionIndex,
							m_attributeSwitchList.ToArray());
						if (GUILayout.Button("设置", GUILayout.MaxWidth(150)))
						{
							SetAttributeSwitch(m_attributeSwitchList[m_attributeSwitchSelectionIndex]);
						}
					}
					EditorGUILayout.EndHorizontal();
				}
			}
		}

		/// <summary>
		/// 设置技能属性以试听
		/// </summary>
		/// <param name="attributeValue">技能属性名称</param>
		private void SetAttributeSwitch(string attributeValue)
		{
			if (AudioManager4Wwise.Instance == null)
			{
				return;
			}

			AkSoundEngine.RegisterGameObj(m_playableDirector.gameObject);
			AkSoundEngine.SetSwitch(AttributeSwitchGroup, attributeValue, m_playableDirector.gameObject);
		}

		#endregion 技能属性相关

		/*
		#region Criware Mana Video 相关

		private void LoadVideo(DramaSequenceAnimationWrap.DramaParam dramaParam, float startFrame)
		{
			// 读取视频表json
			string videoJsonPath = Application.dataPath + "/../../../ConfigData/GMTool/Data/Json/ConfigDataCutInVideo.json";
			string videoJsonFullPath = Path.GetFullPath(videoJsonPath);
			var videoStreamReader = new StreamReader(videoJsonFullPath);
			string videoJsonFile = videoStreamReader.ReadToEnd();
			var videoJsonNode = JSON.Parse(videoJsonFile);

			int videoID = dramaParam.m_dramaParamsDict["VideoId"].m_intValue;
			string videoSoundEvent = dramaParam.m_dramaParamsDict["SoundEvent"].m_stringValue;
			string videoPath = videoJsonNode[videoID.ToString()]["VideoAfter"].Value;

			if (!CriManaPlugin.IsLibraryInitialized())
			{
				m_criWareInitializer.Initialize();
			}

			m_videoStartFrame = startFrame;

			CriManaPlugin.InitializeLibrary();

			var rootTracks = m_skillTimelineAsset.GetRootTracks();
			CriManaTrack criManaTrack = null;
			foreach (var rootTrack in rootTracks)
			{
				var track = rootTrack as CriManaTrack;
				if (ReferenceEquals(track, null))
				{
					continue;
				}

				criManaTrack = track;
				break;
			}

			if (criManaTrack == null)
			{
				criManaTrack = m_skillTimelineAsset.CreateTrack<CriManaTrack>(null, "");
				m_playableDirector.SetGenericBinding(criManaTrack, m_criManaMovieControllerForUI);
			}

			var criManaClip = criManaTrack.CreateClip<CriManaClip>();
			var criManaClipAsset = criManaClip.asset as CriManaClip;
			if (ReferenceEquals(criManaClipAsset, null))
			{
				return;
			}

			criManaClipAsset.m_moviePath = videoPath;
			criManaClip.start = startFrame / m_skillTimelineAsset.editorSettings.fps;
			m_playableDirector.time = (startFrame + 1) / m_skillTimelineAsset.editorSettings.fps;

			var videoSoundTrack = m_skillTimelineAsset.CreateTrack<BjTimelineWwiseEventTrack>(null, VideoSoundTrackName);

			LoadWwiseEvent(videoSoundEvent, 0, (float) criManaClip.start, videoSoundTrack);

			RefreshTimelineWindow();

			EditorCoroutineHelper.StartEditorCoroutine(CheckDelayVideoDuration(criManaClip, startFrame));
		}

		private IEnumerator CheckDelayVideoDuration(TimelineClip criManaClip, float videoStartFrame)
		{
			while (m_criManaMovieControllerForUI.player.movieInfo == null)
			{
				yield return null;
			}

			var movieInfo = m_criManaMovieControllerForUI.player.movieInfo;
			double duration = movieInfo.totalFrames * 1000.0 / movieInfo.framerateN;
			// Debug.LogWarning(string.Format("音频工具: SkillAudioEditor: CriManaMovie duration: \"{0}\"", duration));
			criManaClip.duration = (float) duration;
			m_videoDuration = (float) duration;
			m_playableDirector.time = 0;

			// 将当前Timeline所有其余轨道在该视频时间点及之后的片段均向后移动视频时长.
			var originalTracks = m_skillTimelineAsset.GetRootTracks();
			foreach (var originalTrack in originalTracks)
			{
				if (originalTrack is CriManaTrack || originalTrack.name == VideoSoundTrackName)
				{
					continue;
				}

				var timelineClips = originalTrack.GetClips();
				foreach (var timelineClip in timelineClips)
				{
					if ((int) Math.Round(timelineClip.start / SingleFrameTime) >= videoStartFrame)
					{
						timelineClip.start += duration;
					}
				}
			}

			if (m_effectGroupTrack != null)
			{
				var effectTracks = m_effectGroupTrack.GetChildTracks();
				foreach (var effectTrack in effectTracks)
				{
					var timelineClips = effectTrack.GetClips();
					foreach (var timelineClip in timelineClips)
					{
						if ((int) Math.Round(timelineClip.start / SingleFrameTime) >= videoStartFrame)
						{
							timelineClip.start += duration;
						}
					}
				}
			}

			RefreshOnHitSoundToOnHitTrack();

			RefreshTimelineWindow();
		}

		private const string VideoCanvasGameObjectName = "Video Canvas"; // 视频画布游戏对象名字

		private const string VideoGameObjectName = "Video"; // 视频游戏对象名字

		private const string VideoSoundTrackName = "CG视频声音轨"; // 视频声音轨名字

		private GameObject m_videoCanvasGameObject; // 视频画布游戏对象

		private GameObject m_videoGameObject; // 视频游戏对象

		private CriWareInitializer m_criWareInitializer; // CriWare初始化组件

		private CriManaMovieControllerForUI m_criManaMovieControllerForUI;

		private float m_videoDuration; // 视频时长

		private float m_videoStartFrame; // 视频开始帧

		#endregion Criware Mana Video 相关
		*/
	}
}