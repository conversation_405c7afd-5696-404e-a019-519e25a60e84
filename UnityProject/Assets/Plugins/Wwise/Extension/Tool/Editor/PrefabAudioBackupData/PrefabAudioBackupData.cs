using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace AK.Wwise.Editor
{
	[Serializable]
	public partial class PrefabAudioBackupData
	{
		/// <summary>
		/// 保存预制件的音频数据
		/// </summary>
		/// <param name="prefabPath">预制件路径</param>
		private static void SavePrefabAudioData(string prefabPath)
		{
			var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
			if (prefab == null)
			{
				WwiseLogManager.PrintLogError($"[{nameof(PrefabAudioBackupData)}] 无法加载预制件: \"{prefabPath}\".");
				return;
			}

			string guid = AssetDatabase.AssetPathToGUID(prefabPath);

			PrefabData prefabData = Instance.PrefabDataList.Find(prefabData =>
				prefabData.Guid == guid || prefabData.Path == prefabPath) ?? new PrefabData();

			prefabData.Guid = guid;
			prefabData.Path = prefabPath;
			prefabData.GameObjectDataList.Clear();

			Transform[] transforms = prefab.GetComponentsInChildren<Transform>(true);
			foreach (Transform transform in transforms)
			{
				GameObject gameObject = transform.gameObject;

				bool isInNestedPrefab = AudioToolHelper.IsInNestedPrefab(gameObject, prefab);
				string gameObjectPath = AudioToolHelper.GetGameObjectPath(gameObject);

				var gameObjectData = new GameObjectData
				{
					Path = gameObjectPath
				};

				/*
				#region WwiseEvent

				gameObjectData.WwiseEvents.Clear();
				var wwiseEvent = gameObject.GetComponent<WwiseEvent>();
				if (wwiseEvent != null)
				{
					if (isInNestedPrefab)
					{
						if (AudioToolHelper.IsPropertyOverride(wwiseEvent, nameof(WwiseEvent.m_eventName), wwiseEvent.m_eventName, out _))
						{
							gameObjectData.PropertyModificationDatas.Add(new PropertyModificationData(nameof(WwiseEvent), nameof(WwiseEvent.m_eventName), wwiseEvent.m_eventName));
						}

						if (AudioToolHelper.IsPropertyOverride(wwiseEvent, nameof(WwiseEvent.m_eventReference), wwiseEvent.m_eventReference))
						{
							gameObjectData.PropertyModificationDatas.Add(new PropertyModificationData(nameof(WwiseEvent), nameof(WwiseEvent.m_eventReference), wwiseEvent.m_eventReference.ToString()));
						}

						if (AudioToolHelper.IsPropertyOverride(wwiseEvent, nameof(WwiseEvent.m_playOnEnable), wwiseEvent.m_playOnEnable))
						{
							gameObjectData.PropertyModificationDatas.Add(new PropertyModificationData(nameof(WwiseEvent), nameof(WwiseEvent.m_playOnEnable), wwiseEvent.m_playOnEnable.ToString()));
						}

						if (AudioToolHelper.IsPropertyOverride(wwiseEvent, nameof(WwiseEvent.m_playOnStart), wwiseEvent.m_playOnStart))
						{
							gameObjectData.PropertyModificationDatas.Add(new PropertyModificationData(nameof(WwiseEvent), nameof(WwiseEvent.m_playOnStart), wwiseEvent.m_playOnStart.ToString()));
						}

						if (AudioToolHelper.IsPropertyOverride(wwiseEvent, nameof(WwiseEvent.m_stopOnDisable), wwiseEvent.m_stopOnDisable))
						{
							gameObjectData.PropertyModificationDatas.Add(new PropertyModificationData(nameof(WwiseEvent), nameof(WwiseEvent.m_stopOnDisable), wwiseEvent.m_stopOnDisable.ToString()));
						}
					}
					else
					{
						gameObjectData.WwiseEvents.Add(wwiseEvent);
					}
				}

				#endregion
				*/

				if (SaveWwiseAmbientAudioData(gameObject, gameObjectData, isInNestedPrefab))
				{
					WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 预制件\"{prefab.name}\"上的WwiseAmbient组件有数据备份.\n预制件文件路径: \"{prefabPath}\".");
				}

				if (SaveTimelineAudioData(gameObject, gameObjectData, isInNestedPrefab))
				{
					WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 预制件\"{prefab.name}\"上的Timeline组件有音频数据备份.\n预制件文件路径: \"{prefabPath}\".");
				}

				if (gameObjectData.WwiseEventDataList.Count > 0 || gameObjectData.WwiseAmbientDataList.Count > 0 || gameObjectData.TimelineDataList.Count > 0 || gameObjectData.PropertyModificationDataList.Count > 0)
				{
					prefabData.GameObjectDataList.Add(gameObjectData);
					prefabData.GameObjectDataList.Sort((a, b) => string.Compare(a.Path, b.Path, StringComparison.Ordinal));
				}
			}

			if (prefabData.GameObjectDataList.Count > 0 && !Instance.PrefabDataList.Contains(prefabData))
			{
				Instance.PrefabDataList.Add(prefabData);
			}

			Instance.PrefabDataList.Sort((a, b) => string.Compare(a.Path, b.Path, StringComparison.Ordinal));

			SaveData();
		}

		/// <summary>
		/// 更新预制件的音频数据
		/// </summary>
		/// <param name="prefabPath">预制件的文件路径</param>
		/// <returns>是否有更新到音频数据</returns>
		private static bool UpdatePrefabAudioData(string prefabPath)
		{
			if (Instance.PrefabDataList.Count == 0)
			{
				WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 无备份音频数据.");
				return false;
			}

			var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
			if (prefab == null)
			{
				WwiseLogManager.PrintLogError($"[{nameof(PrefabAudioBackupData)}] 无法加载预制件: \"{prefabPath}\".");
				return false;
			}

			string guid = AssetDatabase.AssetPathToGUID(prefabPath);

			PrefabData prefabData = Instance.PrefabDataList.Find(m => m.Guid == guid || m.Path == prefabPath);
			if (prefabData == null)
			{
				return false;
			}

			bool needToSave = false;

			Transform[] transforms = prefab.GetComponentsInChildren<Transform>(true);
			foreach (Transform transform in transforms)
			{
				GameObject gameObject = transform.gameObject;
				bool isInNestedPrefab = AudioToolHelper.IsInNestedPrefab(gameObject, prefab);
				string gameObjectPath = AudioToolHelper.GetGameObjectPath(gameObject);

				GameObjectData gameObjectData = prefabData.GameObjectDataList.Find(m => m.Path == gameObjectPath);
				if (gameObjectData == null)
				{
					continue;
				}

				if (UpdateWwiseAmbientAudioData(gameObject, gameObjectData, isInNestedPrefab))
				{
					WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 预制件\"{prefab.name}\"上的WwiseAmbient组件有数据更新.\n预制件文件路径: \"{prefabPath}\".");
					needToSave = true;
				}

				if (UpdateTimelineAudioData(gameObject, gameObjectData, isInNestedPrefab))
				{
					WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 预制件\"{prefab.name}\"上的Timeline组件有数据更新.\n预制件文件路径: \"{prefabPath}\".");
					needToSave = true;
				}

				#region PropertyModification

				// 比较PropertyModification并更新
				bool isPropertyModificationChange = false;
				List<PropertyModificationData> propertyModificationDatas = gameObjectData.PropertyModificationDataList;
				PropertyModification[] propertyModificationsArray = PrefabUtility.GetPropertyModifications(gameObject);
				var propertyModifications = new List<PropertyModification>();
				if (propertyModificationsArray != null)
				{
					propertyModifications.AddRange(propertyModificationsArray);
				}

				foreach (PropertyModificationData propertyModificationData in propertyModificationDatas)
				{
					Type componentType = null;
					var currentAssembly = Assembly.GetExecutingAssembly();
					AssemblyName[] referencedAssemblies = currentAssembly.GetReferencedAssemblies();
					foreach (AssemblyName assemblyName in referencedAssemblies)
					{
						Assembly assembly = Assembly.Load(assemblyName);
						if (assembly == null)
						{
							continue;
						}

						componentType = assembly.GetType($"AK.Wwise.{propertyModificationData.ComponentType}");
						if (componentType != null)
						{
							break;
						}
					}

					if (componentType == null)
					{
						continue;
					}

					Component component = gameObject.GetComponent(componentType);
					if (component == null)
					{
						continue;
					}

					string originalComponentSourcePrefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(component);
					Component originalSourceComponent =
						PrefabUtility.GetCorrespondingObjectFromSourceAtPath(component, originalComponentSourcePrefabPath);
					var newPropertyModification = new PropertyModification
					{
						target = originalSourceComponent,
						propertyPath = propertyModificationData.PropertyPath,
						value = propertyModificationData.Value
					};

					PropertyModification propertyModification = propertyModifications.Find(m =>
						m.target == originalSourceComponent && m.propertyPath == propertyModificationData.PropertyPath);
					if (propertyModification == null)
					{
						isPropertyModificationChange = true;
						propertyModifications.Add(newPropertyModification);
					}
					else if (propertyModification.value != propertyModificationData.Value)
					{
						isPropertyModificationChange = true;
						propertyModification.value = propertyModificationData.Value;
					}
				}

				if (isPropertyModificationChange)
				{
					WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 预制件\"{prefab.name}\"上的有音频覆写数据更新.\n预制件文件路径: \"{prefabPath}\".");
					PrefabUtility.SetPropertyModifications(gameObject, propertyModifications.ToArray());
					needToSave = true;
				}

				#endregion
			}

			if (!needToSave)
			{
				return false;
			}

			WwiseLogManager.PrintLog($"[{nameof(PrefabAudioBackupData)}] 预制件的有音频数据更新: \"{prefabPath}\".");

			EditorUtility.SetDirty(prefab);
			AssetDatabase.SaveAssets();

			return true;
		}

		/// <summary>
		/// 游戏对象数据上是否有音频数据
		/// </summary>
		/// <param name="gameObjectData">游戏对象数据</param>
		/// <returns>是否有音频数据</returns>
		private static bool IsGameObjectDataHasAudioData(GameObjectData gameObjectData)
		{
			if (gameObjectData == null)
			{
				return false;
			}

			bool hasAudioData = false;
			hasAudioData |= gameObjectData.WwiseEventDataList.Count > 0;
			hasAudioData |= gameObjectData.WwiseAmbientDataList.Count > 0;
			hasAudioData |= gameObjectData.TimelineDataList.Count > 0;
			hasAudioData |= gameObjectData.PropertyModificationDataList.Count > 0;
			return hasAudioData;
		}

		#region 菜单项方法

		/// <summary>
		/// 保存选中预制件或选中目录中的预制件的音频数据
		/// </summary>
		[MenuItem("Assets/Wwise/预制件/保存预制件中的音频数据", priority = 20)]
		public static void SaveSelectionPrefabAudioDataMenuItem()
		{
			EditorCoroutineHelper.StartEditorCoroutine(SaveSelectionPrefabAudioDataEnumerator());
		}

		/// <summary>
		/// 保存选中预制件或选中目录中的预制件的音频数据的枚举器
		/// </summary>
		private static IEnumerator SaveSelectionPrefabAudioDataEnumerator()
		{
			List<string> prefabPaths = AudioToolHelper.GetFilePathsInSelections(".prefab");

			int totalCount = prefabPaths.Count;
			int currentCount = 0;

			try
			{
				foreach (string prefabPath in prefabPaths)
				{
					bool cancel = EditorUtility.DisplayCancelableProgressBar("保存预制件中的音频数据...", prefabPath, (float) currentCount / totalCount);
					if (cancel)
					{
						break;
					}

					SavePrefabAudioData(prefabPath);
					currentCount++;
				}
			}
			finally
			{
				EditorUtility.ClearProgressBar();
			}

			EditorUtility.DisplayDialog("结果", "保存完成!", "确认");
			yield return null;
		}

		/// <summary>
		/// 更新选中预制件或选中目录中的预制件的音频数据
		/// </summary>
		[MenuItem("Assets/Wwise/预制件/更新预制件中的音频数据", priority = 21)]
		public static void UpdatePrefabAudioDataMenuItem()
		{
			EditorCoroutineHelper.StartEditorCoroutine(UpdatePrefabAudioDataEnumerator());
		}

		/// <summary>
		/// 更新选中预制件或选中目录中的预制件的音频数据的枚举器
		/// </summary>
		private static IEnumerator UpdatePrefabAudioDataEnumerator()
		{
			List<string> prefabPaths = AudioToolHelper.GetFilePathsInSelections(".prefab");

			int totalCount = prefabPaths.Count;
			int currentCount = 0;

			try
			{
				foreach (string prefabPath in prefabPaths)
				{
					bool cancel = EditorUtility.DisplayCancelableProgressBar("更新预制件中的音频数据...", prefabPath, (float) currentCount / totalCount);
					if (cancel)
					{
						break;
					}

					UpdatePrefabAudioData(prefabPath);
					currentCount++;
				}
			}
			finally
			{
				EditorUtility.ClearProgressBar();
			}

			EditorUtility.DisplayDialog("结果", "更新完成, 详情请在Console中确认!", "确认");
			yield return null;
		}

		#endregion

		#region 文件读写相关

		public static bool LoadData()
		{
			try
			{
				if (File.Exists(s_dataFilePath))
				{
					string jsonString = File.ReadAllText(s_dataFilePath);
					s_instance = JsonUtility.FromJson<PrefabAudioBackupData>(jsonString);
					return true;
				}
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"[{nameof(PrefabAudioBackupData)}] 读取预制件声音备份数据发生异常: {e.Message}.");
				return false;
			}

			return false;
		}

		public static bool SaveData()
		{
			try
			{
				string jsonString = JsonUtility.ToJson(Instance, true);
				string parentDir = Path.GetDirectoryName(s_dataParentPath);
				if (!Directory.Exists(s_dataParentPath))
				{
					AssetDatabase.CreateFolder(parentDir, "BackupData");
				}

				File.WriteAllText(s_dataFilePath, jsonString);
				return true;
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"[{nameof(PrefabAudioBackupData)}] 保存预制件声音备份数据发生异常: {e.Message}.");
				return false;
			}
		}

		#endregion

		public static PrefabAudioBackupData Instance
		{
			get
			{
				if (s_instance != null)
				{
					return s_instance;
				}

				if (!LoadData())
				{
					s_instance = new PrefabAudioBackupData();
				}

				return s_instance;
			}

			private set => s_instance = value;
		}

		private static readonly string s_dataParentPath = $"{WwiseHelper.WwiseEditorAssetPath}/BackupData"; // 信息数据文件父路径

		private static readonly string s_dataFilePath = $"{s_dataParentPath}/{nameof(PrefabAudioBackupData)}.json"; // 信息数据文件路

		private static PrefabAudioBackupData s_instance;

		private List<PrefabData> PrefabDataList
		{
			get => m_prefabDataListList;
			set => m_prefabDataListList = value;
		}

		[SerializeField] private List<PrefabData> m_prefabDataListList = new List<PrefabData>();

		[Serializable]
		public class PrefabData
		{
			public string Guid
			{
				get => m_guid;
				set => m_guid = value;
			}

			public string Path
			{
				get => m_path;
				set => m_path = value;
			}

			public List<GameObjectData> GameObjectDataList
			{
				get => m_gameObjectDataList;
				set => m_gameObjectDataList = value;
			}

			[SerializeField] private string m_guid = string.Empty;

			[SerializeField] private string m_path = string.Empty;

			[SerializeField] private List<GameObjectData> m_gameObjectDataList = new List<GameObjectData>();
		}

		[Serializable]
		public class GameObjectData
		{
			public string Path
			{
				get => m_path;
				set => m_path = value;
			}

			public List<WwiseEventData> WwiseEventDataList
			{
				get => m_wwiseEventDataDataList;
				set => m_wwiseEventDataDataList = value;
			}

			public List<WwiseAmbientData> WwiseAmbientDataList
			{
				get => m_wwiseAmbientDataList;
				set => m_wwiseAmbientDataList = value;
			}

			public List<TimelineData> TimelineDataList
			{
				get => m_timelineDataList;
				set => m_timelineDataList = value;
			}

			public List<PropertyModificationData> PropertyModificationDataList
			{
				get => m_propertyModificationDataList;
				set => m_propertyModificationDataList = value;
			}

			[SerializeField] private string m_path = string.Empty;

			[SerializeField] private List<WwiseEventData> m_wwiseEventDataDataList = new List<WwiseEventData>();

			[SerializeField] private List<WwiseAmbientData> m_wwiseAmbientDataList = new List<WwiseAmbientData>();

			[SerializeField] private List<TimelineData> m_timelineDataList = new List<TimelineData>();

			[SerializeField] private List<PropertyModificationData> m_propertyModificationDataList = new List<PropertyModificationData>();
		}

		[Serializable]
		public class WwiseEventData
		{
			public string eventGuid
			{
				get => m_eventGuid;
				set => m_eventGuid = value;
			}

			public string EventName
			{
				get => m_eventName;
				set => m_eventName = value;
			}

			[SerializeField] private string m_eventGuid;

			[SerializeField] private string m_eventName;
		}

		/// <summary>
		/// 嵌套Prefab覆写参数数据
		/// </summary>
		[Serializable]
		public class PropertyModificationData
		{
			public PropertyModificationData(string componentType, string propertyPath, string value)
			{
				m_componentType = componentType;
				m_propertyPath = propertyPath;
				m_value = value;
			}

			/// <summary>
			/// 组件类型
			/// </summary>
			public string ComponentType
			{
				get => m_componentType;
				set => m_componentType = value;
			}

			/// <summary>
			/// 参数路径
			/// </summary>
			public string PropertyPath
			{
				get => m_propertyPath;
				set => m_propertyPath = value;
			}

			/// <summary>
			/// 覆写值
			/// </summary>
			public string Value
			{
				get => m_value;
				set => m_value = value;
			}

			[SerializeField] private string m_componentType;

			[SerializeField] private string m_propertyPath;

			[SerializeField] private string m_value;
		}
	}
}