// ReSharper disable AutoPropertyCanBeMadeGetOnly.Global
// ReSharper disable BitwiseOperatorOnEnumWithoutFlags
// ReSharper disable EventNeverSubscribedTo.Global
// ReSharper disable FieldCanBeMadeReadOnly.Global
// ReSharper disable MemberCanBePrivate.Global

using System;
using System.Collections;
using AK.Wwise;
using UnityEngine;
using UnityEngine.Playables;
#if UNITY_EDITOR
using UnityEditor;
using AK.Wwise.Editor;
#endif

public class WwiseEventPlayableBehaviour : PlayableBehaviour
{
	public override void OnBehaviourPause(Playable playable, FrameData info)
	{
		base.OnBehaviourPause(playable, info);

		if (string.IsNullOrEmpty(WwiseEventPlayable?.EventName))
		{
			return;
		}

		// Debug.Log("Audio: OnBehaviourPause: " + playable.GetTime());
		if (WwiseEventPlayable.StopEventAtClipEnd && m_playingID != WwiseHelper.InValidPlayingID)
		{
			// Debug.Log("Audio: 停止事件: playingID: " + m_playingID);
			if (!WwiseHelper.IsGameRunning)
			{
				bool isClipEnd = Math.Abs(playable.GetTime() - playable.GetDuration()) < 0.01;
				WwiseAudioManager.StopPlayingID(m_playingID, !isClipEnd ? 100 : WwiseEventPlayable.FadeOutTime);
			}
			else
			{
				// 如果片段结束的时候事件也正好要结束, 则不需要再调用StopPlayingID了
				bool isEventEnd = Math.Abs(playable.GetTime() - m_currentDuration) < 0.01;
				if (!isEventEnd)
				{
					WwiseAudioManager.StopPlayingID(m_playingID, WwiseEventPlayable.FadeOutTime);
				}
			}

			m_playingID = WwiseHelper.InValidPlayingID;
			m_currentDuration = -1;
		}

		if (Application.isPlaying)
		{
			if (m_akTriggerBase != null && m_checkForPlayingSyncCoroutine != null)
			{
				m_akTriggerBase.StopCoroutine(m_checkForPlayingSyncCoroutine);
				m_checkForPlayingSyncCoroutine = null;
			}
		}

		m_isPlaying = false;
		m_playTimes = 0;
	}

	public override void ProcessFrame(Playable playable, FrameData info, object playerData)
	{
		base.ProcessFrame(playable, info, playerData);

		string wwiseEventName = WwiseEventPlayable?.EventName;
		if (string.IsNullOrEmpty(wwiseEventName) || !WwiseAudioManager.IsWwiseRunning())
		{
			return;
		}

		if (Application.isPlaying)
		{
			if (m_akTriggerBase == null)
			{
				m_akTriggerBase = Emitter.GetOrAddComponent<AkWwiseTrigger>();
			}

			if (m_isCheckingForReadyToPlay)
			{
				if (m_checkForReadyToPlayCoroutine != null)
				{
					m_akTriggerBase.StopCoroutine(m_checkForReadyToPlayCoroutine);
					m_checkForReadyToPlayCoroutine = null;
				}
			}
		}

		if (m_isCheckingForReadyToPlay)
		{
			m_isCheckingForReadyToPlay = false;

			if (m_playTimes == 0 && m_playingID == WwiseHelper.InValidPlayingID)
			{
				if (!string.IsNullOrEmpty(wwiseEventName))
				{
					m_playingID = PostEvent(WwiseEventPlayable.AudioAPIType, wwiseEventName, Emitter, CallbackFlags, CallbackHandler);
				}

				if (m_playingID != WwiseHelper.InValidPlayingID)
				{
					// Debug.Log("Audio: 播放: playingID: " + m_playingID);
					m_playTimes++;
					WwiseAudioManager.PausePlayingID(m_playingID);
					OnWwiseEventPlayableStart?.Invoke(wwiseEventName, m_playingID);
				}
			}

			if (!m_isPlaying)
			{
				// 可以开始继续
				if (m_playingID != WwiseHelper.InValidPlayingID)
				{
					// 如果事件的时长是无限(即循环型事件)
					if (WwiseEventPlayable.DurationType == WwiseEventPlayable.EventDurationType.Infinited)
					{
						WwiseAudioManager.ResumePlayingID(m_playingID, WwiseEventPlayable.FadeInTime + 50);
						// Debug.Log("Audio: 继续.");
						m_isPlaying = true;
					}
					else
					{
						// 如果事件的时长是有限的, 且当前时长已知(不确定是否已知是因为有可能是时长不确定但有限的事件(随机型事件))
						if (m_currentDuration > 0)
						{
							double proportionalTime = (Playable.GetTime() + WwiseEventPlayable.SeekTime / 1000f) / m_currentDuration;
							if (proportionalTime < 1 && proportionalTime > 0.03)
							{
								SeekTimeForSound((float) proportionalTime);

								WwiseAudioManager.ResumePlayingID(m_playingID, WwiseEventPlayable.FadeInTime + 50);
								// Debug.Log("Audio: 继续.");
								m_isPlaying = true;
							}
							else if (proportionalTime >= 0 && proportionalTime <= 0.03)
							{
								WwiseAudioManager.ResumePlayingID(m_playingID);
								// Debug.Log("Audio: 继续.");
								m_isPlaying = true;
							}

							if (Application.isPlaying)
							{
								if (m_akTriggerBase != null)
								{
									if (m_checkForPlayingSyncCoroutine != null)
									{
										m_akTriggerBase.StopCoroutine(m_checkForPlayingSyncCoroutine);
									}

									m_checkForPlayingSyncCoroutine = m_akTriggerBase.StartCoroutine(CheckForPlayingSync());
								}
							}
							else
							{
								#if UNITY_EDITOR
								EditorCoroutineHelper.StartEditorCoroutine(CheckForPlayingSync());
								#endif
							}
						}
					}
				}
			}
		}

		if (Application.isPlaying)
		{
			if (m_checkForReadyToPlayCoroutine == null && m_akTriggerBase != null)
			{
				m_checkForReadyToPlayCoroutine = m_akTriggerBase.StartCoroutine(CheckForReadyToPlay());
			}
		}
		else
		{
			#if UNITY_EDITOR
			EditorCoroutineHelper.StartEditorCoroutine(CheckForReadyToPlay());
			#endif
		}
	}

	private static WwiseAudioManager WwiseAudioManager => WwiseAudioManager.Instance;

	/// <summary>
	/// Wwise片段开始的Action委托
	/// </summary>
	public static event Action<string, uint> OnWwiseEventPlayableStart;

	public delegate uint PostEventDelegate(WwiseProvider.AudioAPIType audioAPIType, string eventName, GameObject gameObject, uint flags,
		AkCallbackManager.EventCallback callback);

	private static uint DefaultPostEventDelegate(WwiseProvider.AudioAPIType audioAPIType, string eventName, GameObject emitter,
		uint callbackFlags, AkCallbackManager.EventCallback callback)
	{
		#if UNITY_EDITOR
		if (!WwiseHelper.IsGameRunning)
		{
			emitter = null;
		}
		#endif
		switch (audioAPIType)
		{
			case WwiseProvider.AudioAPIType.PlaySound:
				return WwiseAudioManager.PlaySound(eventName, emitter, callbackFlag: callbackFlags, callback: callback);
			case WwiseProvider.AudioAPIType.PlayMusic:
				return WwiseAudioManager.PlayMusic(eventName);
			case WwiseProvider.AudioAPIType.PlayVoice:
				return WwiseAudioManager.PlayVoice(eventName, emitter, callbackFlag: callbackFlags, callback: callback);
			default:
				return WwiseHelper.InValidPlayingID;
		}
	}

	public static PostEventDelegate PostEvent
	{
		get => s_postEventDelegate;
		set => s_postEventDelegate = value ?? DefaultPostEventDelegate;
	}

	private static PostEventDelegate s_postEventDelegate = DefaultPostEventDelegate;

	/// <summary>
	/// 跳转至指定时间比例
	/// </summary>
	/// <param name="proportionalTime">时间比例(0-1f)</param>
	private void SeekTimeForSound(float proportionalTime)
	{
		if (m_playingID == WwiseHelper.InValidPlayingID)
		{
			return;
		}

		WwiseAudioManager.SeekTimeOnPlayingID(m_playingID, proportionalTime);

		/*
		GameObject emitter = Emitter;
		#if UNITY_EDITOR
		if (!WwiseHelper.IsGameRunning)
		{
			switch (WwiseEventPlayable.AudioAPIType)
			{
				case WwiseProvider.AudioAPIType.PlaySound:
					emitter = WwiseProvider.DefaultSoundEmitter;
					break;
				case WwiseProvider.AudioAPIType.PlayMusic:
					emitter = WwiseProvider.DefaultMusicEmitter;
					break;
				case WwiseProvider.AudioAPIType.PlayVoice:
					emitter = WwiseProvider.DefaultVoiceEmitter;
					break;
			}
		}
		#endif
		string eventName = WwiseEventPlayable.EventName;
		ulong akGameObjectID = AkUnitySoundEngine.GetAkGameObjectID(emitter);
		if (!string.IsNullOrEmpty(eventName))
		{
			AkUnitySoundEngine.SeekOnEvent(eventName, akGameObjectID, proportionalTime, false, m_playingID);
		}
		*/
	}

	/// <summary>
	/// 检查是否可以准备播放, 在固定时间内检测ProcessFrame有否再次执行来判断
	/// </summary>
	private IEnumerator CheckForReadyToPlay()
	{
		m_isCheckingForReadyToPlay = true;
		if (!Application.isPlaying)
		{
			#if UNITY_EDITOR
			double targetTime = EditorApplication.timeSinceStartup + CheckForReadyToPlayDurationEditorMode;

			while (EditorApplication.timeSinceStartup < targetTime)
			{
				yield return null;
			}
			#endif
		}
		else
		{
			yield return s_waitForSeconds;
		}

		// 过了检查时间后恢复标志
		if (m_isCheckingForReadyToPlay)
		{
			if (m_isPlaying)
			{
				// 如果正在播放, 则暂停
				if (m_playingID != WwiseHelper.InValidPlayingID)
				{
					WwiseAudioManager.PausePlayingID(m_playingID, 100);
					if (Application.isPlaying)
					{
						if (m_akTriggerBase != null && m_checkForPlayingSyncCoroutine != null)
						{
							m_akTriggerBase.StopCoroutine(m_checkForPlayingSyncCoroutine);
							m_checkForPlayingSyncCoroutine = null;
						}
					}

					// Debug.Log("Audio: 暂停");
					m_isPlaying = false;
				}
			}

			m_isCheckingForReadyToPlay = false;
			m_checkForReadyToPlayCoroutine = null;
		}

		yield return null;
	}

	/// <summary>
	/// 检查当前播放的声音的时间是否与片段时间同步
	/// </summary>
	private IEnumerator CheckForPlayingSync()
	{
		while (m_isPlaying)
		{
			if (!Application.isPlaying)
			{
				#if UNITY_EDITOR
				double targetTime = EditorApplication.timeSinceStartup + CheckForPlayingSyncDuration;

				while (EditorApplication.timeSinceStartup < targetTime)
				{
					yield return null;
				}
				#endif
			}
			else
			{
				yield return s_waitForCheckPlayingSyncSeconds;
			}

			if (m_playingID != WwiseHelper.InValidPlayingID && m_currentDuration > 0)
			{
				if (!WwiseAudioManager.GetPlayingIDSourcePlayPosition(m_playingID, out int soundTimePosition))
				{
					continue;
				}

				bool isSync = Math.Abs((int) Playable.GetTime() * 1000 + WwiseEventPlayable.SeekTime - soundTimePosition) < 10;
				if (isSync)
				{
					continue;
				}

				float proportionalTime = (float) (Playable.GetTime() + WwiseEventPlayable.SeekTime / 1000f + 0.001) / m_currentDuration;

				if (proportionalTime < 1 && proportionalTime > 0.03)
				{
					// Debug.Log($"[{typeof(WwiseEventPlayableBehaviour)}]: 触发播放同步: PlayingID: {m_playingID}; 片段播放时间: {(m_playable.GetTime() + m_wwiseEventPlayable.m_seekTime / 1000f) / m_currentDuration}; 声音播放时间比例: {soundTimePosition / 1000f / m_currentDuration}.");
					SeekTimeForSound(proportionalTime);
				}
			}
		}

		// Debug.Log($"[{typeof(WwiseEventPlayableBehaviour)}]: 播放同步检查协程停止.");
		if (Application.isPlaying)
		{
			if (m_akTriggerBase != null)
			{
				m_akTriggerBase.StopCoroutine(m_checkForPlayingSyncCoroutine);
				m_checkForPlayingSyncCoroutine = null;
			}
		}
		else
		{
			yield return null;
		}
	}

	/// <summary>
	/// 事件回调
	/// </summary>
	private void CallbackHandler(object inCookie, AkCallbackType inType, AkCallbackInfo inInfo)
	{
		switch (inType)
		{
			case AkCallbackType.AK_EndOfEvent:
				if (m_playingID == ((AkEventCallbackInfo) inInfo).playingID)
				{
					m_playingID = WwiseHelper.InValidPlayingID;
					m_currentDuration = -1f;
					m_isPlaying = false;
				}

				if (Application.isPlaying)
				{
					if (m_akTriggerBase != null && m_checkForPlayingSyncCoroutine != null)
					{
						m_akTriggerBase.StopCoroutine(m_checkForPlayingSyncCoroutine);
						m_checkForPlayingSyncCoroutine = null;
					}
				}

				// Debug.Log("Audio: 事件结束: playingID: " + ((AkEventCallbackInfo) inInfo).playingID);
				break;
			case AkCallbackType.AK_Duration:
			{
				m_currentDuration = ((AkDurationCallbackInfo) inInfo).fEstimatedDuration / 1000;
				break;
			}
		}
	}

	public WwiseEventPlayable WwiseEventPlayable { get; set; }

	public GameObject Emitter
	{
		get
		{
			if (m_emitter != null)
			{
				return m_emitter;
			}

			switch (WwiseEventPlayable.AudioAPIType)
			{
				case WwiseProvider.AudioAPIType.PlaySound:
					return WwiseAudioManager.DefaultSoundEmitter;
				case WwiseProvider.AudioAPIType.PlayMusic:
					return WwiseAudioManager.DefaultMusicEmitter;
				case WwiseProvider.AudioAPIType.PlayVoice:
					return WwiseAudioManager.DefaultVoiceEmitter;
				default:
					return WwiseAudioManager.DefaultSoundEmitter;
			}
		}
	}

	/// <summary>
	/// 发送事件的游戏对象
	/// </summary>
	public GameObject m_emitter = null;

	public Playable Playable { get; set; }

	/// <summary>
	/// 回调标志位
	/// </summary>
	private const uint CallbackFlags = (uint) (AkCallbackType.AK_EndOfEvent | AkCallbackType.AK_Duration | AkCallbackType.AK_EnableGetSourcePlayPosition);

	/// <summary>
	/// 检查是否可以准备播放的时长
	/// </summary>
	private const float CheckForReadyToPlayDurationEditorMode = 0.1f;

	/// <summary>
	/// 检查是否可以准备播放的时长
	/// </summary>
	private const float CheckForReadyToPlayDurationPlayMode = 0.2f;

	/// <summary>
	/// 检查是否播放同步的时长
	/// </summary>
	private const float CheckForPlayingSyncDuration = 2f;

	/// <summary>
	/// 检查是否可以准备播放的时长
	/// </summary>
	private static readonly WaitForSeconds s_waitForSeconds = new WaitForSeconds(CheckForReadyToPlayDurationPlayMode);

	/// <summary>
	/// 检查是否播放同步的时长
	/// </summary>
	private static readonly WaitForSeconds s_waitForCheckPlayingSyncSeconds = new WaitForSeconds(CheckForPlayingSyncDuration);

	/// <summary>
	/// PlayingID
	/// </summary>
	private uint m_playingID = WwiseHelper.InValidPlayingID;

	/// <summary>
	/// 当前事件时长, 不能直接取EventDurationMax的值, 因为有的事件的时长是不确定的, 只能通过收到事件回调时确认时长
	/// </summary>
	private float m_currentDuration = -1f;

	/// <summary>
	/// 是否正在检查是否可以准备播放
	/// </summary>
	private bool m_isCheckingForReadyToPlay;

	/// <summary>
	/// 当前片段是否正在播放, 代表真实的播放情况
	/// </summary>
	private bool m_isPlaying;

	/// <summary>
	/// 发送事件的游戏对象上的ATriggerBase组件
	/// </summary>
	private AkWwiseTrigger m_akTriggerBase;

	/// <summary>
	/// 检查是否可以准备播放事件的协程
	/// </summary>
	private Coroutine m_checkForReadyToPlayCoroutine;

	/// <summary>
	/// 检查播放位置是否同步的协程
	/// </summary>
	private Coroutine m_checkForPlayingSyncCoroutine;

	/// <summary>
	/// 在片段中该事件播放计数, 如果当前片段的时长大于事件的当前时长, 则当播放完第一遍后会重新触发播放第二遍, 于是需要对播放次数做计数, 限制二次播放
	/// </summary>
	private uint m_playTimes;
}