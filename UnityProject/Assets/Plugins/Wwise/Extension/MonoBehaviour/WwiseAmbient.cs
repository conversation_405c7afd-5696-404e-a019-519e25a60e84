using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using AK.Wwise.Editor;
using UnityEditor;
#endif

namespace AK.Wwise
{
	[ExecuteInEditMode]
	public class WwiseAmbient : MonoBehaviour
	{
		/// <summary>
		/// 设置事件
		/// 运行时用
		/// </summary>
		/// <param name="eventName"></param>
		/// <returns></returns>
		// ReSharper disable once UnusedMember.Global
		public bool SetEvent(string eventName)
		{
			if (string.IsNullOrEmpty(eventName))
			{
				return false;
			}

			if (!WwiseAudioManager.IsEventValid(eventName))
			{
				return false;
			}

			m_eventName = eventName;

			OnDisable();
			OnEnable();

			return true;
		}

		/// <summary>
		/// 使用简化网格数据创建网格
		/// </summary>
		public void CreateMeshFromSimplifyMeshData()
		{
			if (!IsPlaneSound)
			{
				return;
			}

			var mesh = new Mesh
			{
				vertices = m_vertexArray,
				triangles = m_triangleArray
				// uv = m_uvArray
			};

			mesh.RecalculateNormals();
			mesh.RecalculateBounds();

			SimplifyMesh = mesh;
		}

		/// <summary>
		/// 构建KD树数据
		/// 用于在网格上找出距离最近的点的功能
		/// </summary>
		public void BuildKDTreeData()
		{
			if (!IsPlaneSound)
			{
				return;
			}

			m_verticeTriangleList = new VerticeTriangleList(m_triangleArray, VertexArrayCount);
			m_verticeKDTree = KDTree.MakeFromPoints(m_vertexArray);
		}

		/// <summary>
		/// 找出网格上距离指定的点最近的点的功能
		/// </summary>
		/// <param name="point">指定点</param>
		/// <param name="nearestPoint">网格上最近的点</param>
		/// <returns>是否成功</returns>
		public bool NearestPointOnMesh(Vector3 point, out Vector3 nearestPoint)
		{
			nearestPoint = Vector3.zero;
			if (!IsPlaneSound)
			{
				return false;
			}

			if (m_verticeTriangleList == null || m_verticeKDTree == null)
			{
				return false;
			}

			point = transform.InverseTransformPoint(point);

			// First, find the nearest vertex (the nearest point must be on one of the triangles that uses this vertex if the mesh is convex).
			int nearest = m_verticeKDTree.FindNearest(point);

			// Get the list of triangles in which the nearest vert "participates".
			int[] nearTriangleList = m_verticeTriangleList[nearest];

			float nearestSqDist = float.PositiveInfinity;

			foreach (int nearTriangle in nearTriangleList)
			{
				int triOff = nearTriangle * 3;
				Vector3 a = m_vertexArray[m_triangleArray[triOff]];
				Vector3 b = m_vertexArray[m_triangleArray[triOff + 1]];
				Vector3 c = m_vertexArray[m_triangleArray[triOff + 2]];

				Vector3 possNearestPt = NearestPointOnTriangle(point, a, b, c);
				float possNearestSqDist = (point - possNearestPt).sqrMagnitude;

				if (!(possNearestSqDist < nearestSqDist))
				{
					continue;
				}

				nearestPoint = transform.TransformPoint(possNearestPt);
				m_nearestPoint = nearestPoint;
				nearestSqDist = possNearestSqDist;
			}

			return true;
		}

		/// <summary>
		/// 清理简化网格及数据
		/// </summary>
		public void CleanSimplifyMeshData()
		{
			SimplifyMesh = null;
			// m_uvArray = null;
			m_vertexArray = null;
			m_triangleArray = null;
		}

		/// <summary>
		/// 获取指点变换在世界坐标系下的伸缩值
		/// </summary>
		/// <param name="transform">指定变换</param>
		/// <returns>伸缩值</returns>
		private static Vector3 GetWorldScale(Transform transform)
		{
			Vector3 worldScale = transform.localScale;
			Transform parent = transform.parent;

			while (parent != null)
			{
				worldScale = Vector3.Scale(worldScale, parent.localScale);
				parent = parent.parent;
			}

			return worldScale;
		}

		private void OnEnable()
		{
			m_nearestPoint = transform.position;
			BuildKDTreeData();
			CreateMeshFromSimplifyMeshData();
			WwiseEnvironmentManager.AddEvent(this);
		}

		private void OnDisable()
		{
			WwiseEnvironmentManager.RemoveEvent(this);
			m_nearestPoint = transform.position;
		}

		/// <summary>
		/// 找出三角形上距离指定的点最近的点的功能
		/// </summary>
		/// <param name="point">指定的点</param>
		/// <param name="a">三角形的其中一个点</param>
		/// <param name="b">三角形的其中一个点</param>
		/// <param name="c">三角形的其中一个点</param>
		/// <returns>最近的点</returns>
		private static Vector3 NearestPointOnTriangle(Vector3 point, Vector3 a, Vector3 b, Vector3 c)
		{
			Vector3 edge1 = b - a;
			Vector3 edge2 = c - a;
			Vector3 edge3 = c - b;
			float edge1Length = edge1.magnitude;
			float edge2Length = edge2.magnitude;
			float edge3Length = edge3.magnitude;

			Vector3 pointLineA = point - a;
			Vector3 pointLineB = point - b;
			Vector3 pointLineC = point - c;
			Vector3 xAxis = edge1 / edge1Length;
			Vector3 zAxis = Vector3.Cross(edge1, edge2).normalized;
			Vector3 yAxis = Vector3.Cross(zAxis, xAxis);

			Vector3 edge1Cross = Vector3.Cross(edge1, pointLineA);
			Vector3 edge2Cross = Vector3.Cross(edge2, -pointLineC);
			Vector3 edge3Cross = Vector3.Cross(edge3, pointLineB);
			bool edge1On = Vector3.Dot(edge1Cross, zAxis) > 0f;
			bool edge2On = Vector3.Dot(edge2Cross, zAxis) > 0f;
			bool edge3On = Vector3.Dot(edge3Cross, zAxis) > 0f;

			// If the point is inside the triangle then return its coordinate.
			if (edge1On && edge2On && edge3On)
			{
				float xExtent = Vector3.Dot(pointLineA, xAxis);
				float yExtent = Vector3.Dot(pointLineA, yAxis);
				return a + xAxis * xExtent + yAxis * yExtent;
			}

			// Otherwise, the nearest point is somewhere along one of the edges.
			Vector3 edge1Norm = xAxis;
			Vector3 edge2Norm = edge2.normalized;
			Vector3 edge3Norm = edge3.normalized;

			float edge1Ext = Mathf.Clamp(Vector3.Dot(edge1Norm, pointLineA), 0f, edge1Length);
			float edge2Ext = Mathf.Clamp(Vector3.Dot(edge2Norm, pointLineA), 0f, edge2Length);
			float edge3Ext = Mathf.Clamp(Vector3.Dot(edge3Norm, pointLineB), 0f, edge3Length);

			Vector3 edge1Pt = a + edge1Ext * edge1Norm;
			Vector3 edge2Pt = a + edge2Ext * edge2Norm;
			Vector3 edge3Pt = b + edge3Ext * edge3Norm;

			float sqDist1 = (point - edge1Pt).sqrMagnitude;
			float sqDist2 = (point - edge2Pt).sqrMagnitude;
			float sqDist3 = (point - edge3Pt).sqrMagnitude;

			if (sqDist1 < sqDist2)
			{
				return sqDist1 < sqDist3 ? edge1Pt : edge3Pt;
			}

			return sqDist2 < sqDist3 ? edge2Pt : edge3Pt;
		}

		#if UNITY_EDITOR
		public void OnDrawGizmos()
		{
			Gizmos.color = Color.red;
			Vector3 position = transform.position;
			if (IsPlaneSound)
			{
				position = m_nearestPoint;
			}

			if (string.IsNullOrEmpty(m_eventName))
			{
				Gizmos.DrawSphere(position, 0.5f);
				Handles.Label(position, $"游戏对象\"{gameObject.name}\"上的{nameof(WwiseAmbient)}组件未设置事件", GUI.skin.FindStyle("WarningOverlay"));
			}

			if (Array.Exists(Selection.gameObjects, go => go == gameObject) || (!string.IsNullOrEmpty(m_eventName) && WwiseEnvironmentManager.EventInfos.ContainsKey(m_eventName) && WwiseEnvironmentManager.EventInfos[m_eventName].m_isDisplayDebugInfoOnSceneView))
			{
				Gizmos.DrawSphere(position, 0.5f);
				Transform selfTransform = transform;
				if (Array.Exists(Selection.gameObjects, go => go == gameObject) && (SimplifyMesh != null || OriginalMesh != null))
				{
					var meshFilter = gameObject.GetOrAddComponent<MeshFilter>();
					meshFilter.hideFlags = HideFlags.HideAndDontSave | HideFlags.HideInInspector;
					meshFilter.sharedMesh = SimplifyMesh != null ? SimplifyMesh : OriginalMesh;
					var meshRenderer = gameObject.GetOrAddComponent<MeshRenderer>();
					meshRenderer.hideFlags = HideFlags.HideAndDontSave | HideFlags.HideInInspector;
					meshRenderer.enabled = false;
					SceneView.lastActiveSceneView.Frame(meshRenderer.bounds);
				}

				if (SimplifyMesh != null)
				{
					Gizmos.color = new Color(0, 1, 0, 0.5f);
					Gizmos.DrawMesh(SimplifyMesh, selfTransform.position, selfTransform.rotation, selfTransform.localScale);
					if (SimplifyMesh.vertices.Length <= MaxSimpleMeshVertexCount)
					{
						foreach (Vector3 vertice in SimplifyMesh.vertices)
						{
							Vector3 positionPoint = selfTransform.TransformPoint(vertice);
							Handles.color = Color.white;
							if (!string.IsNullOrEmpty(m_eventName) && WwiseEnvironmentManager.EventInfos.TryGetValue(m_eventName, out WwiseEnvironmentManager.SoundInfo eventInfo))
							{
								Handles.color = eventInfo.PlayingPositionPointInfoList.Exists(info => info.PositionPoint == positionPoint)
									? Color.red
									: Color.blue;
							}

							Handles.RadiusHandle(Quaternion.identity, positionPoint, 0.1f, true);
						}
					}
				}
				else if (Array.Exists(Selection.gameObjects, go => go == gameObject) && !string.IsNullOrEmpty(m_eventName) && WwiseEnvironmentManager.EventInfos.ContainsKey(m_eventName) && WwiseEnvironmentManager.EventInfos[m_eventName].m_isDisplayDebugInfoOnSceneView)
				{
					float radius = (float) Math.Sqrt(WwiseEnvironmentManager.EventInfos[m_eventName].MaxAttenuationSqrMagnitude);
					Gizmos.DrawWireSphere(selfTransform.position, radius);
				}
			}
		}
		#endif // UNITY_EDITOR

		/// <summary>
		/// 简化网格的最大限制顶点数
		/// </summary>
		public const uint MaxSimpleMeshVertexCount = 1000;

		/// <summary>
		/// 声音事件
		/// </summary>
		public Event m_soundEvent = new Event();

		/// <summary>
		/// 声音事件名
		/// </summary>
		public string m_eventName;

		/// <summary>
		/// 简化网格
		/// </summary>
		public Mesh SimplifyMesh { get; private set; }

		/// <summary>
		/// 简化网格的顶点数
		/// </summary>
		public int SimplifyMeshVertexCount => SimplifyMesh != null ? SimplifyMesh.vertexCount : 0;

		/// <summary>
		/// 简化网格的顶点数组
		/// </summary>
		public Vector3[] VertexArray => m_vertexArray;

		/// <summary>
		/// 简化网格的顶点数
		/// </summary>
		public int VertexArrayCount => VertexArray.Length;

		/// <summary>
		/// 与默认听者距离最近的位置点, 仅用于面音源的情况
		/// </summary>
		[NonSerialized] public Vector3 m_nearestPoint = Vector3.zero;

		/// <summary>
		/// 是否是面音源
		/// </summary>
		public bool IsPlaneSound => m_vertexArray != null && VertexArrayCount > 0 && m_triangleArray != null && m_triangleArray.Length > 0;
		// public bool IsPlaneSound => m_uvArray != null && m_uvArray.Length > 0 && m_vertexArray != null && VertexArrayCount > 0 && m_triangleArray != null && m_triangleArray.Length > 0;

		/// <summary>
		/// 简化网格的uv数组
		/// </summary>
		// [SerializeField] private Vector2[] m_uvArray;

		/// <summary>
		/// 简化网格的顶点数组
		/// </summary>
		[SerializeField] private Vector3[] m_vertexArray;

		/// <summary>
		/// 简化网格的三角形数组
		/// </summary>
		[SerializeField] private int[] m_triangleArray;

		/// <summary>
		/// 简化网格的每个顶点所属的三角形的列表
		/// </summary>
		private VerticeTriangleList m_verticeTriangleList;

		/// <summary>
		/// 简化网格的顶点KD树
		/// </summary>
		private KDTree m_verticeKDTree;

		private static WwiseEnvironmentManager WwiseEnvironmentManager => WwiseEnvironmentManager.Instance;

		private static WwiseAudioManager WwiseAudioManager => WwiseAudioManager.Instance;

		#region 简化网格数据相关

		#if UNITY_EDITOR

		/// <summary>
		/// 计算简化网格临时数据
		/// </summary>
		public void PrecomputeMeshSimplifyData()
		{
			if (MeshFilter == null)
			{
				CleanPrecomputeSimplifyMeshData();
				EditorUtility.DisplayDialog("结果", "所选网格过滤器为空, 清空网格计算数据.", "确认");
				return;
			}

			if (OriginalMesh == null)
			{
				CleanPrecomputeSimplifyMeshData();
				EditorUtility.DisplayDialog("结果", "网格过滤器的网格为空, 清空网格计算数据.", "确认");
				return;
			}

			Transform meshFilterTransform = MeshFilter.transform;
			Transform selfTransform = transform;
			if (selfTransform != meshFilterTransform)
			{
				selfTransform.position = meshFilterTransform.position;
				selfTransform.rotation = meshFilterTransform.rotation;
				Vector3 worldScale = GetWorldScale(meshFilterTransform);
				selfTransform.localScale = worldScale;
			}

			gameObject.isStatic = true;
			m_nearestPoint = selfTransform.position;

			EditorCoroutineHelper.StartEditorCoroutine(ProgressiveMesh());
		}

		/// <summary>
		/// 指定顶点数量以生成简化网格
		/// </summary>
		/// <param name="vertexCount">顶点数量</param>
		public void ComputeSimplifyMeshWithVertexCount(int vertexCount)
		{
			if (OriginalMesh == null || OriginalMeshVertexCount <= 0)
			{
				return;
			}

			if (m_vertexPermutation == null || m_vertexPermutation.Length == 0 || m_vertexMap == null || m_vertexMap.Length == 0)
			{
				return;
			}

			if (vertexCount < 3)
			{
				return;
			}

			SimplifyMesh = ConsolidateMesh(OriginalMesh, m_vertexPermutation, m_vertexMap, vertexCount);
		}

		public bool SaveSimplifyMeshData()
		{
			if (SimplifyMesh == null)
			{
				CleanSimplifyMeshData();
				return false;
			}

			CleanPrecomputeSimplifyMeshData();
			// m_uvArray = SimplifyMesh.uv;
			m_vertexArray = SimplifyMesh.vertices;
			m_triangleArray = SimplifyMesh.triangles;
			return true;
		}

		/// <summary>
		/// 清理简化网格计算临时数据
		/// </summary>
		private void CleanPrecomputeSimplifyMeshData()
		{
			m_vertexMap = null;
			m_vertexPermutation = null;
			m_vertexList = null;
			m_triangleList = null;
			m_heap = null;
			m_currentPrecomputeCount = 0;
		}

		private static Mesh ConsolidateMesh(Mesh originalMesh, IReadOnlyList<int> permutation, IReadOnlyList<int> collapseMap, int vertexCount)
		{
			int[][] subMeshesOriginal = new int[originalMesh.subMeshCount][];
			for (int nSubMesh = 0; nSubMesh < originalMesh.subMeshCount; nSubMesh++)
			{
				subMeshesOriginal[nSubMesh] = originalMesh.GetTriangles(nSubMesh);
			}

			int subMeshCount = subMeshesOriginal.Length;
			var vertices = (Vector3[]) originalMesh.vertices.Clone();
			var texCoord1In = (Vector2[]) originalMesh.uv.Clone();
			int[][] subMeshes = new int[subMeshCount][];
			int[] triangleCount = new int[subMeshCount];

			bool bUV1 = texCoord1In.Length > 0;
			int[] vertexMap = new int[vertices.Length];
			for (int i = 0, imax = vertexMap.Length; i < imax; i++)
			{
				vertexMap[i] = -1;
			}

			int n = 0;
			for (int nSubMesh = 0; nSubMesh < subMeshCount; nSubMesh++)
			{
				if (null == subMeshes[nSubMesh])
				{
					subMeshes[nSubMesh] = (int[]) subMeshesOriginal[nSubMesh].Clone();
				}
				else
				{
					subMeshesOriginal[nSubMesh].CopyTo(subMeshes[nSubMesh], 0);
				}

				int[] triangles = subMeshes[nSubMesh];
				for (int i = 0; i < triangles.Length; i += 3)
				{
					int idx0 = triangles[i];
					int idx1 = triangles[i + 1];
					int idx2 = triangles[i + 2];
					while (permutation[idx0] >= vertexCount)
					{
						int idx = collapseMap[idx0];
						if (idx == -1 || idx1 == idx || idx2 == idx)
						{
							idx0 = -1;
							break;
						}

						idx0 = idx;
					}

					while (permutation[idx1] >= vertexCount)
					{
						int idx = collapseMap[idx1];
						if (idx == -1 || idx0 == idx || idx2 == idx)
						{
							idx1 = -1;
							break;
						}

						idx1 = idx;
					}

					while (permutation[idx2] >= vertexCount)
					{
						int idx = collapseMap[idx2];
						if (idx == -1 || idx1 == idx || idx0 == idx)
						{
							idx2 = -1;
							break;
						}

						idx2 = idx;
					}

					if (idx0 == -1 || idx1 == -1 || idx2 == -1)
					{
						triangles[i] = -1;
						triangles[i + 1] = -1;
						triangles[i + 2] = -1;
						continue;
					}

					if (vertexMap[idx0] == -1)
					{
						vertexMap[idx0] = n++;
					}

					triangles[i] = vertexMap[idx0];
					if (vertexMap[idx1] == -1)
					{
						vertexMap[idx1] = n++;
					}

					triangles[i + 1] = vertexMap[idx1];
					if (vertexMap[idx2] == -1)
					{
						vertexMap[idx2] = n++;
					}

					triangles[i + 2] = vertexMap[idx2];
				}

				int l = triangles.Length;
				int h = 0;
				int t = l - 1;
				while (h < t)
				{
					if (triangles[t] == -1)
					{
						t -= 3;
						continue;
					}

					if (triangles[h] != -1)
					{
						h += 3;
						continue;
					}

					triangles[h] = triangles[t - 2];
					triangles[h + 1] = triangles[t - 1];
					triangles[h + 2] = triangles[t];
					triangles[t - 2] = -1;
					triangles[t - 1] = -1;
					triangles[t] = -1;
					h += 3;
					t -= 3;
				}

				if (t < l - 1)
				{
					triangleCount[nSubMesh] = t + 1;
				}
				else
				{
					triangleCount[nSubMesh] = l;
				}
			}

			Vector2 tmpUV = Vector2.zero;
			Vector2 tmpUV2 = Vector2.zero;
			for (int i = 0; i < vertexMap.Length; i++)
			{
				int idx = i;
				Vector3 tmp = vertices[idx];
				if (bUV1)
				{
					tmpUV = texCoord1In[idx];
				}

				while (vertexMap[idx] != -1)
				{
					Vector3 tmp2 = vertices[vertexMap[idx]];
					if (bUV1)
					{
						tmpUV2 = texCoord1In[vertexMap[idx]];
					}

					vertices[vertexMap[idx]] = tmp;
					if (bUV1)
					{
						texCoord1In[vertexMap[idx]] = tmpUV;
					}

					tmp = tmp2;
					tmpUV = tmpUV2;
					int tmpI = vertexMap[idx];
					vertexMap[idx] = -1;
					idx = tmpI;
				}
			}

			var newMesh = new Mesh();

			var newVertices = new Vector3[n];
			Array.Copy(vertices, 0, newVertices, 0, n);
			newMesh.vertices = newVertices;
			if (bUV1)
			{
				var newUV = new Vector2[n];
				Array.Copy(texCoord1In, 0, newUV, 0, n);
				newMesh.uv = newUV;
			}

			newMesh.triangles = null;
			for (int i = 0; i < subMeshCount; i++)
			{
				int length = triangleCount[i];
				int[] newSubMeshes = new int[length];
				Array.Copy(subMeshes[i], 0, newSubMeshes, 0, length);
				newMesh.SetTriangles(newSubMeshes, i);
			}

			newMesh.subMeshCount = subMeshes.Length;

			newMesh.RecalculateNormals();
			newMesh.RecalculateBounds();

			return newMesh;
		}

		private IEnumerator ProgressiveMesh()
		{
			m_currentPrecomputeCount = 0;
			m_vertexMap = new int[OriginalMeshVertexCount];
			m_vertexPermutation = new int[OriginalMeshVertexCount];
			m_vertexList = new List<Vertex>();
			m_triangleList = new TriangleList[OriginalMesh.subMeshCount];
			m_heap = Heap<Vertex>.CreateMinHeap();

			for (int i = 0; i < OriginalMeshVertexCount; i++)
			{
				m_vertexMap[i] = -1;
				m_vertexPermutation[i] = -1;
			}

			Vector2[] av2Mapping = OriginalMesh.uv;

			AddVertices();

			int triangleCount = 0;
			for (int subMeshCount = 0; subMeshCount < OriginalMesh.subMeshCount; subMeshCount++)
			{
				int[] anIndices = OriginalMesh.GetTriangles(subMeshCount);
				m_triangleList[subMeshCount] = new TriangleList(anIndices.Length / 3);
				triangleCount = AddFaceListSubMesh(subMeshCount, anIndices, av2Mapping, triangleCount);
			}

			float[] costs = new float[m_vertexList.Count];
			int[] collapses = new int[m_vertexList.Count];
			CostComputation.Compute(m_vertexList, m_triangleList, m_originalMeshSize, costs, collapses);

			for (int i = 0; i < m_vertexList.Count; i++)
			{
				Vertex v = m_vertexList[i];
				v.m_objDist = costs[i];
				v.m_collapseVertex = collapses[i] == -1 ? null : m_vertexList[collapses[i]];
				m_heap.Insert(v);
			}

			int vertexNum = m_vertexList.Count;

			while (vertexNum-- > 0)
			{
				Vertex mn = m_heap.ExtractTop();
				m_vertexPermutation[mn.ID] = vertexNum;
				m_vertexMap[mn.ID] = mn.m_collapseVertex?.ID ?? -1;
				Collapse(mn, mn.m_collapseVertex);
				EditorUtility.DisplayProgressBar("计算简化网格数据", "收缩处理...",
					(float) (m_vertexList.Count - vertexNum) / m_vertexList.Count);
			}

			m_currentPrecomputeCount = 0;
			EditorUtility.ClearProgressBar();
			EditorUtility.DisplayDialog("结果", "计算简化网格数据成功.", "确认");
			yield return null;
		}

		private void AddVertices()
		{
			Vector3[] vertexArray = OriginalMesh.vertices;
			// var uvArray = OriginalMesh.uv;
			Vector3[] normalArray = OriginalMesh.normals;
			for (int i = 0; i < vertexArray.Length; i++)
			{
				EditorUtility.DisplayProgressBar("计算简化网格数据", $"顶点处理... {m_currentPrecomputeCount}/{OriginalMeshVertexCount}",
					(float) m_currentPrecomputeCount / OriginalMeshVertexCount);

				// var v = new Vertex(vertexArray[i], i, uvArray[i], normalArray[i]);
				var v = new Vertex(vertexArray[i], i, normalArray[i]);
				foreach (Vertex u in m_vertexList.FindAll(u => Vector3.Distance(v.Position, u.Position) / m_originalMeshSize < float.Epsilon))
				{
					v.ListNeighbors.Add(u);
					u.ListNeighbors.Add(v);
				}

				m_vertexList.Add(v);
				m_currentPrecomputeCount++;
			}
		}

		private int AddFaceListSubMesh(int nSubMesh, IReadOnlyList<int> anIndices, Vector2[] v2Mapping, int nTriangles)
		{
			bool bUVData = false;

			if (v2Mapping != null)
			{
				if (v2Mapping.Length > 0)
				{
					bUVData = true;
				}
			}

			List<Triangle> list = m_triangleList[nSubMesh].m_listTriangles;
			for (int i = 0; i < anIndices.Count; i += 3)
			{
				var tri = new Triangle(nSubMesh, nTriangles + list.Count,
					m_vertexList[anIndices[i]], m_vertexList[anIndices[i + 1]], m_vertexList[anIndices[i + 2]],
					bUVData, anIndices[i], anIndices[i + 1], anIndices[i + 2], true);

				list.Add(tri);
				ShareUV(v2Mapping, tri);
			}

			return nTriangles + list.Count;
		}

		private static void ShareUV(IReadOnlyList<Vector2> aMapping, Triangle t)
		{
			if (t.HasUVData == false)
			{
				return;
			}

			// It so happens that neighboring faces that share vertices
			// sometimes share uv coordinates at those verts but have
			// their own entries in the tex vert list

			if (aMapping == null || aMapping.Count == 0)
			{
				return;
			}

			for (int i = 0; i < 3; i++)
			{
				foreach (Triangle n in t.Vertices[i].ListFaces)
				{
					if (t == n)
					{
						continue;
					}

					int tx1 = t.TexAt(i);
					int tx2 = n.TexAt(t.Vertices[i]);

					if (tx1 == tx2)
					{
						continue;
					}

					Vector2 uv1 = aMapping[tx1];
					Vector2 uv2 = aMapping[tx2];

					if (uv1 == uv2)
					{
						t.SetTexAt(i, tx2);
					}
				}
			}
		}

		private void Collapse(Vertex u, Vertex v)
		{
			if (v == null)
			{
				u.Destructor();
				return;
			}

			int i;

			var tmpTriangleList = new List<Triangle>();
			var tmpVerticeList = new List<Vertex>();

			for (i = 0; i < u.ListNeighbors.Count; i++)
			{
				Vertex nb = u.ListNeighbors[i];
				if (nb != u)
				{
					tmpVerticeList.Add(nb);
				}
			}

			for (i = 0; i < u.ListFaces.Count; i++)
			{
				if (u.ListFaces[i].HasVertex(v))
				{
					tmpTriangleList.Add(u.ListFaces[i]);
				}
			}

			// Delete triangles on edge uv
			for (i = tmpTriangleList.Count - 1; i >= 0; i--)
			{
				Triangle t = tmpTriangleList[i];
				t.Destructor();
			}

			// Update remaining triangles to have v instead of u
			for (i = u.ListFaces.Count - 1; i >= 0; i--)
			{
				u.ListFaces[i].ReplaceVertex(u, v);
			}

			u.Destructor();

			// Recompute the edge collapse costs for neighboring vertices
			for (i = 0; i < tmpVerticeList.Count; i++)
			{
				ComputeEdgeCostAtVertex(tmpVerticeList[i]);
				m_heap.ModifyValue(tmpVerticeList[i].HeapIndex, tmpVerticeList[i]);
			}
		}

		private void ComputeEdgeCostAtVertex(Vertex v)
		{
			if (v.ListNeighbors.Count == 0)
			{
				v.m_collapseVertex = null;
				v.m_objDist = -0.01f;
				return;
			}

			v.m_objDist = MaxVertexCollapseCost;
			v.m_collapseVertex = null;

			const float fRelevanceBias = 0.0f;

			foreach (Vertex t in v.ListNeighbors)
			{
				float dist = ComputeEdgeCollapseCost(v, t, fRelevanceBias);

				if (v.m_collapseVertex != null && !(dist < v.m_objDist))
				{
					continue;
				}

				v.m_collapseVertex = t;
				v.m_objDist = dist;
			}
		}

		private float ComputeEdgeCollapseCost(Vertex u, Vertex v, float fRelevanceBias)
		{
			const float fBorderCurvature = 2;

			int i;
			float fEdgeLength = Vector3.Magnitude(v.Position - u.Position) / m_originalMeshSize;
			float fCurvature = 0.001f;
			// if (fEdgeLength < float.Epsilon)
			// {
			// 	return fBorderCurvature * (1 - Vector3.Dot(u.Normal, v.Normal) + 2 * Vector3.Distance(u.UV, v.UV));
			// }

			var sides = new List<Triangle>();

			for (i = 0; i < u.ListFaces.Count; i++)
			{
				if (u.ListFaces[i].HasVertex(v))
				{
					sides.Add(u.ListFaces[i]);
				}
			}

			for (i = 0; i < u.ListFaces.Count; i++)
			{
				float fMinCurvature = 1.0f;

				foreach (Triangle triangle in sides)
				{
					float dotProd = Vector3.Dot(u.ListFaces[i].Normal, triangle.Normal);
					fMinCurvature = Mathf.Min(fMinCurvature, (1.0f - dotProd) / 2.0f);
				}

				fCurvature = Mathf.Max(fCurvature, fMinCurvature);
			}

			bool isBorder = u.IsBorder();
			if (isBorder && sides.Count > 1)
			{
				fCurvature = 1.0f;
			}

			if (isBorder)
			{
				fCurvature = fBorderCurvature;
			}

			fCurvature += fRelevanceBias;

			return fEdgeLength * fCurvature;
		}

		/// <summary>
		/// 网格过滤器组件
		/// </summary>
		public MeshFilter MeshFilter { get; set; }

		/// <summary>
		/// 网格渲染器组件
		/// </summary>
		public MeshRenderer MeshRenderer
		{
			get
			{
				if (MeshFilter)
				{
					return MeshFilter.GetComponent<MeshRenderer>();
				}

				return null;
			}
		}

		/// <summary>
		/// 原始网格
		/// </summary>
		public Mesh OriginalMesh
		{
			get
			{
				if (MeshFilter == null)
				{
					m_originalMesh = null;
					m_originalMeshSize = 0f;
				}
				else
				{
					if (MeshFilter.sharedMesh != null)
					{
						if (m_originalMesh != MeshFilter.sharedMesh)
						{
							Mesh sharedMesh = MeshFilter.sharedMesh;
							m_originalMeshSize = Mathf.Max(sharedMesh.bounds.size.x, sharedMesh.bounds.size.y, sharedMesh.bounds.size.z);
						}
					}

					m_originalMesh = MeshFilter.sharedMesh;
				}

				return m_originalMesh;
			}
		}

		/// <summary>
		/// 原始网格顶点数
		/// </summary>
		public int OriginalMeshVertexCount => OriginalMesh != null ? OriginalMesh.vertexCount : 0;

		/// <summary>
		/// 是否有简化网格计算临时数据
		/// </summary>
		public bool HasPrecomputeSimplifyMeshData => m_vertexPermutation != null && m_vertexPermutation.Length > 0 && m_vertexMap != null && m_vertexMap.Length > 0;

		private const float MaxVertexCollapseCost = 10000000.0f;

		/// <summary>
		/// 原始网格
		/// </summary>
		private Mesh m_originalMesh;

		/// <summary>
		/// 原始网格大小
		/// </summary>
		private float m_originalMeshSize;

		private int[] m_vertexMap;

		private int[] m_vertexPermutation;

		private List<Vertex> m_vertexList;

		private TriangleList[] m_triangleList;

		private Heap<Vertex> m_heap;

		/// <summary>
		/// 计算简化网格临时数据的进度计数
		/// </summary>
		private int m_currentPrecomputeCount;

		#endif

		#endregion
	}
}