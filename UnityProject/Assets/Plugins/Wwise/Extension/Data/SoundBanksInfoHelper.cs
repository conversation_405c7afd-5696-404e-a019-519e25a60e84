using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using UnityEngine;
#if UNITY_EDITOR
using Newtonsoft.Json;
using UnityEditor;
#endif

namespace AK.Wwise
{
	/// <summary>
	/// Wwise的SoundbanksInfo.json文件帮助类
	/// </summary>
	[Serializable]
	public class SoundBanksInfoHelper
	{
		#if UNITY_EDITOR
		[InitializeOnLoadMethod]
		private static void SetupAutoRefreshSoundBanksInfo()
		{
			if (!EditorApplication.isPlayingOrWillChangePlaymode)
			{
				if (AkUtilities.IsWwiseProjectAvailable)
				{
					string[] settingsToEnable = { "GenerateSoundBankJSON", "SoundBankGenerateEstimatedDuration", "SoundBankGenerateMaxAttenuationInfo", "SoundBankGeneratePrintGUID", "SoundBankGeneratePrintPath" };
					AkUtilities.ToggleBoolSoundbankSettingInWproj(settingsToEnable, AkWwiseEditorSettings.WwiseProjectAbsolutePath);
				}
			}

			AkWwiseEditorSettings.SoundBanksInfoUpdatedFunc += RefreshSoundBanksInfo;
		}
		#endif

		/// <summary>
		/// 检查Wwise事件是否存在
		/// </summary>
		/// <param name="eventName">事件名</param>
		public static bool CheckWwiseEventExist(string eventName)
		{
			return Instance.WwiseEventInfos.ContainsKey(eventName);
		}

		/// <summary>
		/// 检查声音库是否是用户定义的
		/// </summary>
		/// <param name="soundBankName">声音库名</param>
		/// <param name="isUserDefined">声音库是否是用户定义的</param>
		/// <returns>声音库是否存在</returns>
		public static bool CheckSoundBankIsUserDefined(string soundBankName, out bool isUserDefined)
		{
			isUserDefined = true;
			SoundBanksInfo.SoundBank currentSoundBank = null;
			foreach (SoundBanksInfo.SoundBank soundBank in Instance.SoundBanksInfo.SoundBanks)
			{
				if (soundBank.ShortName == soundBankName)
				{
					currentSoundBank = soundBank;
				}
			}

			if (currentSoundBank == null)
			{
				return false;
			}

			isUserDefined = currentSoundBank.Type == "User";
			return true;
		}

		/// <summary>
		/// 获取Wwise事件信息
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="wwiseEventInfo">事件信息</param>
		/// <param name="enableDebugLog">是否打印日志</param>
		/// <returns>是否成功</returns>
		public static bool GetWwiseEventInfo(string eventName, out WwiseEventInfo wwiseEventInfo, bool enableDebugLog = true)
		{
			wwiseEventInfo = null;
			if (!Instance.WwiseEventInfos.TryGetValue(eventName, out WwiseEventInfo eventInfo))
			{
				if (enableDebugLog)
				{
					WwiseLogManager.PrintLogError($"[{nameof(SoundBanksInfoHelper)}] 无法找到事件\"{eventName}\", 请确认项目内声音资源包含事件并更新SoundBanksInfo事件数据.");
				}

				return false;
			}

			wwiseEventInfo = eventInfo;
			return true;
		}

		/// <summary>
		/// 获取Wwise事件信息
		/// </summary>
		/// <param name="guid">事件Guid</param>
		/// <param name="wwiseEventInfo">事件信息</param>
		/// <returns>是否成功</returns>
		public static bool GetWwiseEventInfo(Guid guid, out WwiseEventInfo wwiseEventInfo)
		{
			wwiseEventInfo = null;
			foreach (KeyValuePair<string, WwiseEventInfo> pair in Instance.WwiseEventInfos)
			{
				WwiseEventInfo eventInfo = pair.Value;
				if (eventInfo.Guid != guid)
				{
					continue;
				}

				wwiseEventInfo = eventInfo;
				return true;
			}

			WwiseLogManager.PrintLogError($"[{nameof(SoundBanksInfoHelper)}] 无法通过Guid\"{guid}\"找到事件, 请确认项目内声音资源包含事件并更新SoundBanksInfo事件数据.");
			return false;
		}

		public static SoundBanksInfo.SoundBank.Event.SwitchContainer FindSwitchContainerByGUID(SoundBanksInfo.SoundBank.Event.SwitchContainer switchContainer, string guid)
		{
			string stateGUID = switchContainer.SwitchValue.GUID;
			if (stateGUID == guid)
			{
				return switchContainer;
			}

			if (switchContainer.Children == null)
			{
				return null;
			}

			foreach (SoundBanksInfo.SoundBank.Event.SwitchContainer childSwitchContainer in switchContainer.Children)
			{
				SoundBanksInfo.SoundBank.Event.SwitchContainer targetSwitchContainer = FindSwitchContainerByGUID(childSwitchContainer, guid);
				if (targetSwitchContainer != null)
				{
					return targetSwitchContainer;
				}
			}

			return null;
		}

		/// <summary>
		/// 从json文件刷新并解析SoundBanksInfo数据
		/// </summary>
		public static bool RefreshSoundBanksInfo()
		{
			s_instance = null;
			#if UNITY_EDITOR
			try
			{
				if (!File.Exists(SoundBanksInfoJsonPath))
				{
					#if UNITY_EDITOR_OSX
					// 对于macOS平台, 如果无法找到相应平台的json, 则尝试读取Windows平台的json
					WwiseLogManager.PrintLogWarning($"[{nameof(SoundBanksInfoHelper)}] macOS平台的\"{SoundBanksInfoJsonFileName}\"文件不存在, 尝试从Windows平台读取.");
					s_platformName = "Windows";
					if (!File.Exists(SoundBanksInfoJsonPath))
					{
						WwiseLogManager.PrintLogWarning($"[{nameof(SoundBanksInfoHelper)}] Windows平台的\"{SoundBanksInfoJsonFileName}\"文件不存在: \"{SoundBanksInfoJsonPath}\".");
						return false;
					}
					#else
					WwiseLogManager.PrintLogWarning($"[{nameof(SoundBanksInfoHelper)}] \"{SoundBanksInfoJsonFileName}\"文件不存在: \"{SoundBanksInfoJsonPath}\".");
					return false;
					#endif
				}

				string jsonString = File.ReadAllText(SoundBanksInfoJsonPath);
				s_instance = JsonConvert.DeserializeObject<SoundBanksInfoHelper>(jsonString);
				if (s_instance?.SoundBanksInfo == null)
				{
					return false;
				}
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"[{nameof(SoundBanksInfoHelper)}] 读取\"{SoundBanksInfoJsonFileName}\"文件发生异常: {e.Message}.");
				return false;
			}

			Instance.WwiseEventInfos.Clear();

			foreach (SoundBanksInfo.SoundBank soundBank in s_instance.SoundBanksInfo.SoundBanks)
			{
				foreach (SoundBanksInfo.SoundBank.Event wwiseEvent in soundBank.Events)
				{
					string eventName = wwiseEvent.Name;
					float eventDurationMax = string.IsNullOrEmpty(wwiseEvent.DurationMax) ? -1 : Convert.ToSingle(wwiseEvent.DurationMax);

					float eventDurationMin = string.IsNullOrEmpty(wwiseEvent.DurationMin) ? -1 : Convert.ToSingle(wwiseEvent.DurationMin);

					if (Instance.WwiseEventInfos.ContainsKey(eventName))
					{
						if (eventDurationMax > Instance.WwiseEventInfos[eventName].DurationMax)
						{
							Instance.WwiseEventInfos[eventName].DurationMax = eventDurationMax;
						}

						if (eventDurationMin < Instance.WwiseEventInfos[eventName].DurationMin)
						{
							Instance.WwiseEventInfos[eventName].DurationMin = eventDurationMin;
						}
					}
					else
					{
						var eventGuid = new Guid(wwiseEvent.GUID);
						float eventAttenuation = string.IsNullOrEmpty(wwiseEvent.MaxAttenuation) ? 0 : Convert.ToSingle(wwiseEvent.MaxAttenuation);

						Instance.WwiseEventInfos.Add(eventName, new WwiseEventInfo
						{
							EventName = eventName,
							Guid = eventGuid,
							DurationMax = eventDurationMax,
							DurationMin = eventDurationMin,
							Attenuation = eventAttenuation,
							SoundBankName = soundBank.ShortName
						});
					}
				}
			}

			// WwiseLogManager.PrintLog($"[{nameof(SoundBanksInfoHelper)}] 成功解析\"{SoundBanksInfoJsonFileName}\"文件");
			return true;
			#endif
			return false;
		}

		#region 菜单项相关

		#if UNITY_EDITOR
		[MenuItem("Wwise/工具/更新SoundBanksInfo数据", priority = 150)]
		public static void RefreshSoundBanksInfoMenuItem()
		{
			if (RefreshSoundBanksInfo())
			{
				EditorUtility.DisplayDialog("结果", "更新完毕.", "确认");
			}
			else
			{
				EditorUtility.DisplayDialog("错误", $"\"{SoundBanksInfoJsonFileName}\"文件不存在: {SoundBanksInfoJsonPath}", "确认");
			}
		}
		#endif

		#endregion

		public static SoundBanksInfoHelper Instance
		{
			get
			{
				if (s_instance != null)
				{
					return s_instance;
				}

				return RefreshSoundBanksInfo() ? s_instance : new SoundBanksInfoHelper();
			}
		}

		private static SoundBanksInfoHelper s_instance;

		/// <summary>
		/// 声音库信息json文件名
		/// </summary>
		public const string SoundBanksInfoJsonFileName = "SoundbanksInfo.json";

		#if UNITY_EDITOR
		/// <summary>
		/// SoundbanksInfo.json文件路径
		/// </summary>
		public static string SoundBanksInfoJsonPath => WwiseHelper.FixSlash(Path.GetFullPath($"{Application.dataPath}/{AkWwiseEditorSettings.Instance.SoundbankPath}/{s_platformName}/{SoundBanksInfoJsonFileName}"));
		#endif

		/// <summary>
		/// Wwise事件信息列表
		/// </summary>
		private SortedDictionary<string, WwiseEventInfo> WwiseEventInfos { get; } = new SortedDictionary<string, WwiseEventInfo
		>();

		/// <summary>
		/// Wwise平台名称
		/// </summary>
		private static string s_platformName = AkBasePathGetter.GetPlatformName();

		/// <summary>
		/// 声音库信息
		/// </summary>
		[SuppressMessage("ReSharper", "InconsistentNaming")]
		public SoundBanksInfo SoundBanksInfo;

		/// <summary>
		/// Wwise事件信息
		/// </summary>
		[SuppressMessage("ReSharper", "NotAccessedField.Global")]
		public class WwiseEventInfo
		{
			/// <summary>
			/// 事件名
			/// </summary>
			public string EventName { get; set; }

			/// <summary>
			/// 事件Guid
			/// </summary>
			public Guid Guid { get; set; }

			/// <summary>
			/// 事件最大时长
			/// </summary>
			public float DurationMax { get; set; } = -1;

			/// <summary>
			/// 事件最小时长
			/// </summary>
			public float DurationMin { get; set; } = -1;

			/// <summary>
			/// 衰减范围
			/// </summary>
			public float Attenuation { get; set; }

			/// <summary>
			/// 声音库名
			/// </summary>
			public string SoundBankName { get; set; }
		}
	}
}