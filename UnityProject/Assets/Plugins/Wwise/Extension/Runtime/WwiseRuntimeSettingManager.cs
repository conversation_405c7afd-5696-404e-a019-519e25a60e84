using System;

namespace AK.Wwise
{
	/// <summary>
	/// Wwise运行时设置管理器
	/// </summary>
	public class WwiseRuntimeSettingManager
	{
		/// <summary>
		/// 单例
		/// </summary>
		public static WwiseRuntimeSettingManager Instance => s_instance.Value;

		/// <summary>
		/// 单例
		/// </summary>
		private static readonly Lazy<WwiseRuntimeSettingManager> s_instance = new Lazy<WwiseRuntimeSettingManager>(() => new WwiseRuntimeSettingManager());

		/// <summary>
		/// 游戏在脱焦或切换至后台时后声音是否保持渲染
		/// 默认保持渲染
		/// </summary>
		public bool IsKeepRenderingDuringFocusLoss
		{
			get => m_isKeepRenderingDuringFocusLoss;
			set
			{
				#if UNITY_EDITOR || !UNITY_IOS
				AkSoundEngineController.Instance.ActivateAudio(value || AkSoundEngineController.Instance.IsFocus, true);
				#endif

				m_isKeepRenderingDuringFocusLoss = value;
			}
		}

		private bool m_isKeepRenderingDuringFocusLoss = true;
	}
}