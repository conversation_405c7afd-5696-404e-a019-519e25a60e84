// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace AK.Wwise
{
	/// <summary>
	/// 存放Wwise事件与Bank索引信息
	/// </summary>
	public class WwiseBanksManifest : ScriptableObject
	{
		#region 编辑器相关

		/// <summary>
		/// 加载资产文件(仅编辑器下使用)
		/// </summary>
		/// <returns></returns>
		public static WwiseBanksManifest LoadAsset()
		{
			#if UNITY_EDITOR
			var wwiseBanksManifest = AssetDatabase.LoadAssetAtPath<WwiseBanksManifest>(WwiseBanksManifestPath);
			if (wwiseBanksManifest)
			{
				return wwiseBanksManifest;
			}

			WwiseLogManager.PrintLogError($"[{nameof(WwiseBanksManifest)}] 无法在默认路径加载\"WwiseBanksManifest\": \"{WwiseBanksManifestPath}\".");
			#endif
			return null;
		}

		/// <summary>
		/// 获取事件关联的声音库(仅编辑器下使用)
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <returns>包含事件的声音库, 如果事件不合法则返回空</returns>
		public string GetEventRelatedSoundBank(string eventName)
		{
			#if UNITY_EDITOR
			if (SoundBankList == null)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseBanksManifest)}] 声音库列表为空, 请提供正确生成的\"WwiseBanksManifest\".");
				return null;
			}

			foreach (WwiseAkBank wwiseAkBank in SoundBankList)
			{
				foreach (WwiseAkEvent wwiseAkEvent in wwiseAkBank.EventList)
				{
					if (wwiseAkEvent.Name == eventName)
					{
						return wwiseAkBank.SoundBankName;
					}
				}
			}

			WwiseLogManager.PrintLogWarning($"[{nameof(WwiseBanksManifest)}] 事件不合法: \"{eventName}\".");
			#endif
			return null;
		}

		/// <summary>
		/// 默认WwiseBanksManifest资产文件路径
		/// </summary>
		public static string WwiseBanksManifestPath =>
			$"{WwiseHelper.WwiseScriptableObjectRelativePath}/{nameof(WwiseBanksManifest)}.asset";

		#endregion

		/// <summary>
		/// 声音库列表, 用于序列化
		/// </summary>
		public List<WwiseAkBank> SoundBankList;

		/// <summary>
		/// 流文件列表
		/// </summary>
		public List<WwiseStreamedFileInfo> StreamedFileInfos;

		/// <summary>
		/// 切换组列表, 用于序列化
		/// </summary>
		public List<WwiseAkSwitch> SwitchGroups;

		/// <summary>
		/// 状态组列表, 用于序列化
		/// </summary>
		public List<WwiseAkState> StateGroups;

		/// <summary>
		/// RTPC列表, 用于序列化
		/// </summary>
		public List<string> RTPCs;

		/// <summary>
		/// 外部源列表, 用于序列化
		/// </summary>
		public List<WwiseExternalSource> ExternalSources;

		[Serializable]
		public class WwiseAkBank
		{
			public ulong ID;

			/// <summary>
			/// 声音库名字
			/// </summary>
			public string SoundBankName;

			/// <summary>
			/// 是否本地化区域相关
			/// </summary>
			public bool isLocalized;

			/// <summary>
			/// 包含的事件列表
			/// </summary>
			public List<WwiseAkEvent> EventList = new List<WwiseAkEvent>();
		}

		/// <summary>
		/// 流文件信息
		/// </summary>
		[Serializable]
		public class WwiseStreamedFileInfo
		{
			/// <summary>
			/// 流文件ID
			/// 对应流文件的文件名
			/// 不带文件拓展名
			/// </summary>
			public string FileID;

			/// <summary>
			/// 本地化区域
			/// 如果无本地化区域则默认为"SFX"
			/// </summary>
			public string Language;

			/// <summary>
			/// 对应源音频素材文件名
			/// 带文件拓展名".wav"
			/// </summary>
			public string AudioSampleFileName;

			/// <summary>
			/// 需要加载此流文件的事件列表
			/// </summary>
			public List<string> EventList = new List<string>();
		}

		[Serializable]
		public class WwiseAkEvent
		{
			/// <summary>
			/// 事件时长类型枚举
			/// </summary>
			public enum DurationTypeEnum
			{
				OneShot = 0, // 有限时长事件
				Infinite = 1 // 无限时长事件
			}

			public uint ID;
			public string Name;
			public DurationTypeEnum DurationType;
			public float Duration;

			/// <summary>
			/// 最大衰减距离
			/// </summary>
			public float MaxAttenuation;

			/// <summary>
			/// 该事件引用的声音库列表, 为哈希表, 不序列化, 仅在运行期使用
			/// </summary>
			public HashSet<string> SoundBankList = new HashSet<string>();

			/// <summary>
			/// 该事件引用的流文件列表, 不序列化, 仅在运行期使用
			/// </summary>
			public HashSet<string> StreamedFileRelativePathSet { get; } = new HashSet<string>();
		}

		[Serializable]
		public class WwiseAkSwitch
		{
			public string GroupName;
			public List<string> ValueNames = new List<string>();
		}

		[Serializable]
		public class WwiseAkState
		{
			public string GroupName;
			public List<string> ValueNames = new List<string>();
		}

		[Serializable]
		public class WwiseExternalSource
		{
			public string Name;
			public string Path;
		}
	}
}