using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using Object = UnityEngine.Object;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace AK.Wwise
{
	/// <summary>
	/// Wwise相关功能的封装类
	/// </summary>
	public partial class WwiseProvider
	{
		#region 初始化相关

		/// <summary>
		/// 初始化
		/// </summary>
		public bool Init(AkWwiseInitializationSettings akWwiseInitializationSettings = null, WwiseBanksManifest wwiseBanksManifest = null)
		{
			if (IsInited)
			{
				return false;
			}

			if (!InitWwiseConfigData(akWwiseInitializationSettings, wwiseBanksManifest))
			{
				return false;
			}

			AkBasePathGetter.Get().EvaluateGamePaths();

			if (!InitWwiseHierarchyStructure())
			{
				return false;
			}

			IsInited = true;

			#if UNITY_WEBGL && !UNITY_EDITOR
			bool loadInitBank = WwiseResourceManager.LoadSoundBankForWebGL("Init");
			#else
			bool loadInitBank = WwiseResourceManager.LoadSoundBank("Init");
			#endif

			if (string.IsNullOrEmpty(CurrentLanguage))
			{
				CurrentLanguage = AkWwiseInitializationSettings.ActivePlatformSettings.InitialLanguage;
			}

			if (loadInitBank)
			{
				WwiseMusicManager.Init();
			}

			WwiseExternalSourceManager.Init();

			return true;
		}

		public void UnInit()
		{
			if (!IsInited)
			{
				return;
			}

			IsInited = false;

			WwiseEnvironmentManager.UnInit();
			AkSoundEngineController.Instance.Terminate();
			Object.DestroyImmediate(WwiseGlobalGameObject);

			Reset();
		}

		/// <summary>
		/// 添加基础路径
		/// 供项目调用可在AkSoundEngine已初始化后通过程序添加基础路径(Wwise音频资源存放的路径)
		/// 使用该接口添加的基础路径是相对于可读写的PersistentDataPath下的路径)
		/// 通过该接口添加的基础路径, 后添加的路径比先添加的有更高的音频资源(声音库/流文件)读取加载的优先级
		/// 所以调用该接口添加的基础路径比Wwise初始化时使用的配置文件AkWwiseInitializationSettings里面的基础路径以及默认热更路径具有更高的优先级
		/// </summary>
		/// <param name="basePath">基础路径, 相对于PersistentDataPath下的路径, 不是绝对路径</param>
		/// <returns>是否成功</returns>
		public bool AddBasePath(string basePath)
		{
			try
			{
				basePath = Path.GetFullPath(Path.Combine(Application.persistentDataPath, basePath));
				basePath = WwiseHelper.FixSlash(basePath);
				AKRESULT result = AkUnitySoundEngine.AddBasePath(basePath);
				if (result == AKRESULT.AK_Success)
				{
					WwiseLogManager.PrintLog($"[{nameof(WwiseProvider)}] 添加基础路径成功. 路径: \"{basePath}\".");
					return true;
				}

				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 添加基础路径失败. 路径: \"{basePath}\".\n\"{result}\".");
				return false;
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 添加基础路径异常. 路径: \"{basePath}\".\n\"{e.Message}\".");
				return false;
			}
		}

		/// <summary>
		/// AkSoundEngine是否已初始化
		/// </summary>
		/// <returns>是否已初始化</returns>
		public bool IsAkSoundEngineInitialized(bool debugLog = true)
		{
			bool result = AkUnitySoundEngine.IsInitialized();
			#if !UNITY_EDITOR
			if (!result && debugLog)
			{
				WwiseLogManager.PrintLogWarning($"[{nameof(WwiseProvider)}] AkSoundEngine未初始化.");
			}
			#endif

			return result;
		}

		public void Tick()
		{
			if (!IsInited)
			{
				return;
			}

			WwiseResourceManager.Tick();
			WwiseExternalSourceManager.Tick();
			AkGameObjectManager.Tick();
			TickForSoundTag();
			WwiseMusicManager.Tick();
			WwiseEnvironmentManager.Tick();
			WwisePlayingIDManager.Tick();
			// TickForBankItem();
		}

		/// <summary>
		/// 设置本地化语言
		/// </summary>
		/// <param name="localization">语音语言</param>
		public bool SetLocalization(string localization)
		{
			if (CurrentLanguage == localization)
			{
				return true;
			}

			AKRESULT result = AkUnitySoundEngine.SetCurrentLanguage(localization);

			if (result != AKRESULT.AK_Success)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 设置本地化语言失败. 本地化语言: \"{localization}\"\n\"{result}\".");
				return false;
			}

			CurrentLanguage = localization;
			WwiseResourceManager.UnloadLocalizedSoundBank();
			return true;
		}

		/// <summary>
		/// 读取WwiseBanksManifest以生成各种映射索引数据供Wwise运行时使用
		/// </summary>
		/// <returns>是否成功</returns>
		public bool LoadWwiseBanksManifest(WwiseBanksManifest wwiseBanksManifest)
		{
			if (!wwiseBanksManifest)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] WwiseBanksManifest为空, 无法读取.");
				return false;
			}

			if (!WwiseResourceManager.Init(wwiseBanksManifest))
			{
				return false;
			}

			CreateEventDictionary(wwiseBanksManifest.SoundBankList);
			CreateStreamedFileInfoDictionary(wwiseBanksManifest.StreamedFileInfos);
			CreateSwitchGroupDictionary(wwiseBanksManifest.SwitchGroups);
			CreateStateGroupDictionary(wwiseBanksManifest.StateGroups);
			CreateRTPCHashSet(wwiseBanksManifest.RTPCs);

			return true;
		}

		/// <summary>
		/// 初始化配置数据
		/// </summary>
		private bool InitWwiseConfigData(AkWwiseInitializationSettings akWwiseInitializationSettings = null,
			WwiseBanksManifest wwiseBanksManifest = null)
		{
			// 加载Wwise初始化配置资产, 目的是为了让加载的资产成为AkWwiseInitializationSettings的单例, 以便可以在Wwise初始化过程中被读取
			if (!akWwiseInitializationSettings)
			{
				#if !UNITY_EDITOR
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 需要加载并提供\"AkWwiseInitializationSettings\".");
				return false;
				#endif
				#if UNITY_EDITOR
				string akWwiseInitializationSettingsPath = $"{WwiseHelper.WwiseScriptableObjectRelativePath}/{nameof(AkWwiseInitializationSettings)}.asset";
				akWwiseInitializationSettings = AssetDatabase.LoadAssetAtPath<AkWwiseInitializationSettings>(akWwiseInitializationSettingsPath);
				if (!akWwiseInitializationSettings)
				{
					WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 无法在默认路径加载Wwise初始化配置\"AkWwiseInitializationSettings\": \"{akWwiseInitializationSettingsPath}\".");
					return false;
				}
				#endif
			}

			// 加载WwiseBanksManifest文件中各种映射数据
			if (!wwiseBanksManifest)
			{
				#if !UNITY_EDITOR
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 需要加载并提供\"WwiseBanksManifest\".");
				return false;
				#endif
				#if UNITY_EDITOR
				string wwiseBanksManifestPath = WwiseBanksManifest.WwiseBanksManifestPath;
				wwiseBanksManifest = AssetDatabase.LoadAssetAtPath<WwiseBanksManifest>(wwiseBanksManifestPath);
				if (!wwiseBanksManifest)
				{
					WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 无法在默认路径加载\"WwiseBanksManifest\": \"{wwiseBanksManifestPath}\".");
					return false;
				}
				#endif
			}

			return LoadWwiseBanksManifest(wwiseBanksManifest);
		}

		/// <summary>
		/// 初始化层级结构
		/// </summary>
		private bool InitWwiseHierarchyStructure()
		{
			try
			{
				WwiseGlobalGameObject = new GameObject(WwiseGlobalGameObjectName);
				WwiseGlobalGameObject.AddComponent<AkInitializer>();

				if (!IsAkSoundEngineInitialized(false))
				{
					Object.DestroyImmediate(WwiseGlobalGameObject);
					Reset();
					return false;
				}

				/*
				#if (UNITY_ANDROID || UNITY_IOS) && !UNITY_EDITOR
				WwiseGlobalGameObject.AddComponent<AudioRouteHelper>();
				#endif
				*/
				// 创建默认听者对象并注册
				var listener = new GameObject(AudioListenerGameObjectName);
				listener.transform.SetParent(WwiseGlobalGameObject.transform);
				AkGameObjectManager.SetDefaultListener(listener);

				SoundGameObject = new GameObject(SFXEmitterGameObjectName);
				SoundGameObject.transform.SetParent(listener.transform, false);
				VoiceGameObject = new GameObject(VoiceEmitterGameObjectName);
				VoiceGameObject.transform.SetParent(listener.transform, false);
				MusicGameObject = new GameObject(MusicEmitterGameObjectName);
				MusicGameObject.transform.SetParent(listener.transform, false);

				/*
				// 添加耳机插拔监听
				#if !UNITY_EDITOR && (UNITY_ANDROID || UNITY_IOS)
				if (WwiseGlobalGameObject.GetComponent<AudioRouteHelper>() == null)
				{
					WwiseGlobalGameObject.AddComponent<AudioRouteHelper>();
				}
				#endif
				*/

				return true;
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 初始化发生异常: {e.Message}.");
				if (!WwiseGlobalGameObject)
				{
					return false;
				}

				Object.DestroyImmediate(WwiseGlobalGameObject);
				Reset();
				return false;
			}
		}

		/*
		/// <summary>
		/// 从资源创建Wwise环境
		/// </summary>
		/// <returns></returns>
		private bool InitWwiseHierarchyStructureFromAsset(GameObject wwiseGlobalPrefabAsset = null)
		{
			// 如果没有提供WwiseGlobal对象, 则尝试使用默认的存放路径加载
			if (wwiseGlobalPrefabAsset == null)
			{
				#if !UNITY_EDITOR
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 需要加载并提供\"WwiseGlobal\" Prefab.");
				#else
				wwiseGlobalPrefabAsset = AssetDatabase.LoadAssetAtPath<GameObject>(WwiseGlobalPrefabAssetPath);
				#endif
				if (wwiseGlobalPrefabAsset == null)
				{
					return false;
				}
			}

			//  创建WwiseGlobal对象
			WwiseGlobalGameObject = Object.Instantiate(wwiseGlobalPrefabAsset);

			WwiseGlobalGameObject.name = WwiseGlobalGameObject.name.Replace("(Clone)", "");
			var listenerRoot = WwiseGlobalGameObject.transform.Find(AudioListenerGameObjectName);
			if (!listenerRoot)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] Can't find \"{AudioListenerGameObjectName}\" GameObject in \"{WwiseGlobalGameObjectName}.prefab\".");
				Object.Destroy(WwiseGlobalGameObject);
				WwiseGlobalGameObject = null;
				return false;
			}

			var soundRoot = listenerRoot.Find(SFXEmitterGameObjectName);
			if (!soundRoot)
			{
				soundRoot = WwiseGlobalGameObject.transform.Find(SFXEmitterGameObjectName);
				if (!soundRoot)
				{
					WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] Can't find \"{SFXEmitterGameObjectName}\" GameObject in \"{WwiseGlobalGameObjectName}.prefab\".");
					Object.Destroy(WwiseGlobalGameObject);
					WwiseGlobalGameObject = null;
					return false;
				}
			}

			var voiceRoot = listenerRoot.Find(VoiceEmitterGameObjectName);
			if (!voiceRoot)
			{
				voiceRoot = WwiseGlobalGameObject.transform.Find(VoiceEmitterGameObjectName);
				if (!voiceRoot)
				{
					WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] Can't find \"{VoiceEmitterGameObjectName}\" GameObject in \"{WwiseGlobalGameObjectName}.prefab\".");
					Object.Destroy(WwiseGlobalGameObject);
					WwiseGlobalGameObject = null;
					return false;
				}
			}

			var musicRoot = listenerRoot.Find(MusicEmitterGameObjectName);
			if (!musicRoot)
			{
				musicRoot = WwiseGlobalGameObject.transform.Find(MusicEmitterGameObjectName);
				if (!musicRoot)
				{
					WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] Can't find \"{MusicEmitterGameObjectName}\" GameObject in \"{WwiseGlobalGameObjectName}.prefab\".");
					Object.Destroy(WwiseGlobalGameObject);
					WwiseGlobalGameObject = null;
					return false;
				}
			}

			// 获取/创建Initializer
			WwiseGlobalGameObject.GetOrAddComponent<AkInitializer>();

			// 注册默认听者
			var listener = listenerRoot.gameObject;
			var akAudioListener = listener.GetComponent<AkAudioListener>();
			if (akAudioListener)
			{
				Object.DestroyImmediate(akAudioListener);
			}

			var akGameObj = listener.GetComponent<AkGameObj>();
			if (akGameObj)
			{
				Object.DestroyImmediate(akGameObj);
			}

			AkGameObjectManager.SetDefaultListener(listener);

			SoundGameObject = soundRoot.gameObject;
			VoiceGameObject = voiceRoot.gameObject;
			MusicGameObject = musicRoot.gameObject;

			return true;
		}
		*/

		/// <summary>
		/// 重启Wwise
		/// 需要注意重启不代表Wwise销毁并重新初始化, 而是执行如下步骤:
		/// 1. 停止所有声音, 卸载所有声音库.
		/// 2. 重新加载"Init.bnk"声音库, 播放起始音乐事件
		/// </summary>
		/// <returns>是否发生重启行为并且重启成功</returns>
		public bool Restart()
		{
			try
			{
				// 停止所有声音
				AkUnitySoundEngine.StopAll();
				WwiseMusicManager.Reset();
				WwisePlayingIDManager.Reset();

				// 卸载并释放所有声音库
				WwiseResourceManager.UnloadAllSoundBank();

				// 加载"Init.bnk"声音库
				#if UNITY_WEBGL && !UNITY_EDITOR
				WwiseResourceManager.LoadSoundBankForWebGL("Init");
				#else
				if (!WwiseResourceManager.LoadSoundBank("Init"))
				{
					WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 加载\"Init.bnk\"声音库失败, Wwise重启失败.");
					return false;
				}
				#endif

				// 播放起始音乐事件
				WwiseMusicManager.Init();

				WwiseLogManager.PrintLog($"[{nameof(WwiseProvider)}] Wwise重启成功.");
				return true;
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] Wwise重启发生异常: {e.Message}.");
				return false;
			}
		}

		private void Reset()
		{
			WwiseGlobalGameObject = null;
			SoundGameObject = null;
			VoiceGameObject = null;
			MusicGameObject = null;

			WwisePlayingIDManager.Reset();
			AkGameObjectManager.Reset();
			WwiseResourceManager.Reset();
			WwiseExternalSourceManager.Reset();
			WwiseMusicManager.Reset();
		}

		/// <summary>
		/// 是否已初始化
		/// </summary>
		public bool IsInited { get; private set; }

		/// <summary>
		/// 当前语言
		/// </summary>
		public string CurrentLanguage { get; private set; } = string.Empty;

		/// <summary>
		/// WwiseGlobal游戏对象
		/// </summary>
		public GameObject WwiseGlobalGameObject { get; private set; }

		/// <summary>
		/// 默认音效发声体游戏对象
		/// </summary>
		public GameObject SoundGameObject { get; private set; }

		/// <summary>
		/// 默认音乐发声体游戏对象
		/// </summary>
		public GameObject MusicGameObject { get; private set; }

		/// <summary>
		/// 默认语音发声体游戏对象
		/// </summary>
		public GameObject VoiceGameObject { get; private set; }

		/// <summary>
		/// 默认听者
		/// </summary>
		private GameObject DefaultListener => AkGameObjectManager.Instance.DefaultListener;

		/// <summary>
		/// WwiseGlobal游戏对象名字
		/// </summary>
		private const string WwiseGlobalGameObjectName = "WwiseGlobal";

		/// <summary>
		/// 听者游戏对象名字
		/// </summary>
		private const string AudioListenerGameObjectName = "AudioListener";

		/// <summary>
		/// 默认音效发声体游戏对象名字
		/// </summary>
		private const string SFXEmitterGameObjectName = "SFXEmitter";

		/// <summary>
		/// 默认音乐发声体游戏对象名字
		/// </summary>
		private const string MusicEmitterGameObjectName = "MusicEmitter";

		/// <summary>
		/// 默认语音发声体游戏对象名字
		/// </summary>
		private const string VoiceEmitterGameObjectName = "VoiceEmitter";

		/// <summary>
		/// Wwise资源管理器
		/// </summary>
		private static WwiseResourceManager WwiseResourceManager => WwiseResourceManager.Instance;

		/// <summary>
		/// Ak游戏对象管理器
		/// </summary>
		private static AkGameObjectManager AkGameObjectManager => AkGameObjectManager.Instance;

		/// <summary>
		/// Wwise PlayingID管理器
		/// </summary>
		private static WwisePlayingIDManager WwisePlayingIDManager => WwisePlayingIDManager.Instance;

		/// <summary>
		/// Wwise音乐管理器
		/// </summary>
		private static WwiseMusicManager WwiseMusicManager => WwiseMusicManager.Instance;

		/// <summary>
		/// Wwise环境管理器
		/// </summary>
		private static WwiseEnvironmentManager WwiseEnvironmentManager => WwiseEnvironmentManager.Instance;

		/// <summary>
		/// Wwise外部源管理器
		/// </summary>
		private static WwiseExternalSourceManager WwiseExternalSourceManager => WwiseExternalSourceManager.Instance;

		#endregion 初始化相关

		#region 播放相关

		/// <summary>
		/// 播放声音
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="gameObject">游戏对象</param>
		/// <param name="tag">标签</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <param name="callbackFlag">回调标志位</param>
		/// <param name="callback">回调</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PlaySound(string eventName, GameObject gameObject = null, string tag = null, int seekTime = 0, uint callbackFlag = 0, AkCallbackManager.EventCallback callback = null)
		{
			// 未指定发声游戏对象，使用默认的声音游戏对象
			gameObject = !gameObject ? SoundGameObject : gameObject;

			tag = string.IsNullOrEmpty(tag) ? "SFX" : tag;

			uint playingID = PostEventImp(eventName, gameObject, tag, seekTime, callbackFlag, callback);
			return playingID;
		}

		/// <summary>
		/// 播放声音
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="akGameObjectID">Ak游戏对象ID</param>
		/// <param name="tag">标签</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <param name="callbackFlag">回调标志位</param>
		/// <param name="callback">回调</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PlaySound(string eventName, ulong akGameObjectID, string tag = null, int seekTime = 0, uint callbackFlag = 0, AkCallbackManager.EventCallback callback = null)
		{
			tag = string.IsNullOrEmpty(tag) ? "SFX" : tag;

			uint playingID = PostEventImp(eventName, akGameObjectID, tag, seekTime, callbackFlag, callback);
			return playingID;
		}

		/// <summary>
		/// 播放声音
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="position">方位</param>
		/// <param name="akGameObjectID">Ak游戏对象ID, 这里返回用于此次播放声音调用而分配的自动注册的Ak游戏对象</param>
		/// <param name="tag">标签</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <param name="callbackFlag">回调标志位</param>
		/// <param name="callback">回调</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PlaySound(string eventName, Vector3 position, out ulong akGameObjectID, string tag = null, int seekTime = 0, uint callbackFlag = 0, AkCallbackManager.EventCallback callback = null)
		{
			tag = string.IsNullOrEmpty(tag) ? "SFX" : tag;
			akGameObjectID = AkGameObjectManager.AllocateFreeAutoRegisterAkGameObject();
			if (!AkGameObjectManager.SetAkGameObjectPosition(akGameObjectID, position))
			{
				return InValidPlayingID;
			}

			uint playingID = PostEventImp(eventName, akGameObjectID, tag, seekTime, callbackFlag, callback);
			return playingID;
		}

		/// <summary>
		/// 播放语音
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="gameObject">游戏对象</param>
		/// <param name="tag">标签</param>
		/// <param name="callbackFlag">回调标志位</param>
		/// <param name="callback">回调</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PlayVoice(string eventName, GameObject gameObject = null, string tag = null, uint callbackFlag = 0, AkCallbackManager.EventCallback callback = null, int seekTime = 0)
		{
			gameObject = !gameObject ? VoiceGameObject : gameObject;
			tag = string.IsNullOrEmpty(tag) ? "Voice" : tag;

			uint playingID = PostEventImp(eventName, gameObject, tag, seekTime, callbackFlag, callback);
			return playingID;
		}

		/// <summary>
		/// 播放UI声音
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="gameObject">游戏对象</param>
		/// /// <param name="tag">标签</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PlayUISound(string eventName, GameObject gameObject = null, string tag = null, int seekTime = 0)
		{
			if (!IsInited)
			{
				return 0;
			}

			tag = string.IsNullOrEmpty(tag) ? "UI" : tag;

			uint playingID = InValidPlayingID;
			ulong uiListenerID = AkGameObjectManager.UIListenerID;
			// 未指定发声游戏对象，使用默认的UI听者
			if (gameObject == null)
			{
				playingID = AkGameObjectManager.IsEnableUIListener ? PostEventImp(eventName, uiListenerID, tag, seekTime) : PostEventImp(eventName, SoundGameObject, tag, seekTime);
			}
			else
			{
				if (!AkGameObjectManager.RegisterGameObject(gameObject, out ulong akGameObjectID))
				{
					return playingID;
				}

				AkGameObjectManager.AkGameObjectInfo emitterInfo = AkGameObjectManager.AkGameObjectInfoDict[akGameObjectID];
				emitterInfo.IsUseUIListener = true;
				if (AkGameObjectManager.IsEnableUIListener)
				{
					AkGameObjectManager.SetEmitterListener(akGameObjectID, uiListenerID);
				}

				playingID = PostEventImp(eventName, akGameObjectID, tag, seekTime);
			}

			return playingID;
		}

		public uint PostEvent(uint eventID, GameObject gameObject, uint callbackFlags, AkCallbackManager.EventCallback callback, object callbackCookie = null)
		{
			if (!IsInited)
			{
				return InValidPlayingID;
			}

			if (!gameObject)
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 游戏对象为空.");
				return InValidPlayingID;
			}

			string eventName = GetEventNameFromID(eventID);
			return string.IsNullOrEmpty(eventName) ? InValidPlayingID : PostEventImp(eventName, gameObject, callbackFlag: callbackFlags, callback: callback, callbackCookie: callbackCookie);
		}

		/// <summary>
		/// 发送事件
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="gameObject">游戏对象</param>
		/// <param name="tag">标签</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <param name="callbackFlag">回调标志位</param>
		/// <param name="callback">回调</param>
		/// <param name="callbackCookie">回调Cookie</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PostEventImp(string eventName, GameObject gameObject, string tag = null, int seekTime = 0, uint callbackFlag = 0, AkCallbackManager.EventCallback callback = null,
			object callbackCookie = null)
		{
			if (!AkGameObjectManager.RegisterGameObject(gameObject, out ulong akGameObjectID))
			{
				return InValidPlayingID;
			}

			return PostEventImp(eventName, akGameObjectID, tag, seekTime, callbackFlag, callback, callbackCookie);
		}

		/// <summary>
		/// 发送事件
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="akGameObjectID">Ak游戏对象ID</param>
		/// <param name="tag">标签</param>
		/// <param name="seekTime">跳转播放时间(毫秒)</param>
		/// <param name="callbackFlag">回调标志位</param>
		/// <param name="callback">回调</param>
		/// <param name="callbackCookie">回调Cookie</param>
		/// <returns>PlayingID, 若播放失败则为0</returns>
		public uint PostEventImp(string eventName, ulong akGameObjectID, string tag = null, int seekTime = 0, uint callbackFlag = 0, AkCallbackManager.EventCallback callback = null,
			object callbackCookie = null)
		{
			if (string.IsNullOrEmpty(tag))
			{
				tag = "SFX";
			}

			if (BlockSoundTagSet.Contains(tag))
			{
				return InValidPlayingID;
			}

			if (!AkGameObjectManager.IsAkGameObjectRegister(akGameObjectID))
			{
				return InValidPlayingID;
			}

			if (GameObjectBlockSoundTagSetDict.ContainsKey(akGameObjectID) && GameObjectBlockSoundTagSetDict[akGameObjectID] != null && GameObjectBlockSoundTagSetDict[akGameObjectID].Contains(tag))
			{
				return InValidPlayingID;
			}

			bool isEventValid = GetEventDependencyInfo(eventName, out WwiseBanksManifest.WwiseAkEvent wwiseAkEvent);
			if (!isEventValid || wwiseAkEvent == null)
			{
				return InValidPlayingID;
			}

			bool needAudioResourcePathSet = OnPostEventPreloadResource != null;
			#if UNITY_EDITOR
			needAudioResourcePathSet |= AkWwiseEditorSettings.Instance.m_enableRecordAudioResourceUsage;
			#endif
			if (needAudioResourcePathSet)
			{
				var audioRelativePathSet = new HashSet<string>();
				foreach (string soundBank in wwiseAkEvent.SoundBankList)
				{
					bool isLocalized = SoundBankLocalizedInfoDictionary[soundBank];
					string soundBankPath = isLocalized ? $"{CurrentLanguage}/{soundBank}.bnk" : $"{soundBank}.bnk";
					audioRelativePathSet.Add(soundBankPath);
				}

				audioRelativePathSet.UnionWith(wwiseAkEvent.StreamedFileRelativePathSet);

				OnPostEventPreloadResource?.Invoke(audioRelativePathSet);

				#if UNITY_EDITOR
				if (AkWwiseEditorSettings.Instance.m_enableRecordAudioResourceUsage)
				{
					foreach (string audioRelativePath in audioRelativePathSet)
					{
						AudioResourceUsageInfoHelper.AppendAudioResourceUsage(audioRelativePath);
					}
				}
				#endif
			}

			// 加载未加载过的Bank
			bool loadSoundBankResult = false;

			foreach (string soundBank in wwiseAkEvent.SoundBankList)
			{
				#if UNITY_WEBGL && !UNITY_EDITOR
				var postEventParameterList = new WwiseResourceManager.PostEventParameterList()
				{
					EventName = eventName,
					AkGameObjectID = akGameObjectID,
					Tag = tag,
					SeekTime = seekTime,
					CallbackFlag = callbackFlag,
					Callback = callback,
					CallbackCookie = callbackCookie
				};
				if (WwiseResourceManager.LoadSoundBankForWebGL(soundBank, eventName, postEventParameterList))
				{
					if (!loadSoundBankResult)
					{
						loadSoundBankResult = true;
					}
				}
				#else
				if (WwiseResourceManager.LoadSoundBank(soundBank))
				{
					if (!loadSoundBankResult)
					{
						loadSoundBankResult = true;
					}
				}
				#endif
			}

			if (!loadSoundBankResult)
			{
				return InValidPlayingID;
			}

			uint playingID = AkUnitySoundEngine.PostEvent(wwiseAkEvent.ID, akGameObjectID, callbackFlag, callback, callbackCookie);

			if (playingID == InValidPlayingID)
			{
				return InValidPlayingID;
			}

			if (seekTime != 0)
			{
				AkUnitySoundEngine.SeekOnEvent(wwiseAkEvent.ID, akGameObjectID, seekTime, false, playingID);
			}

			WwisePlayingIDManager.AddPlayingID(playingID, tag);

			// 增加对Bank的引用
			foreach (string soundBank in wwiseAkEvent.SoundBankList)
			{
				WwiseResourceManager.AddSoundBankReference(soundBank, playingID);
			}

			return playingID;
		}

		/// <summary>
		/// 在PostEventImp方法中加载音频资源前用于准备资源的Action委托
		/// 参数为音频资源哈希集(bnk及wem文件的相对路径, 格式与GetEventDependencyResourceRelativePathList方法获得的列表相同)
		/// </summary>
		public static event Action<HashSet<string>> OnPostEventPreloadResource;

		#endregion 播放相关

		#region 停止相关

		/// <summary>
		/// 停止指定游戏对象上的声音
		/// 不指定事件名则为停止指定游戏对象上的所有事件
		/// 不指定游戏对象则为停止所有游戏对象上的指定事件
		/// 若都不指定, 则停止所有声音
		/// </summary>
		/// <param name="eventName">事件名</param>
		/// <param name="gameObject">游戏对象</param>
		/// <param name="transitionTime">过渡时间(毫秒)</param>
		public void StopSound(string eventName = "", GameObject gameObject = null, int transitionTime = 0)
		{
			if (!WwisePlayingIDManager.GetPlayingID(eventName, gameObject, m_playingIDList))
			{
				return;
			}

			foreach (uint playingID in m_playingIDList)
			{
				StopPlayingID(playingID, transitionTime);
			}
		}

		/// <summary>
		/// 停止指定PlayingID
		/// </summary>
		/// <param name="playingID">PlayingID</param>
		/// <param name="transitionTime">淡出时间(毫秒)</param>
		public void StopPlayingID(uint playingID, int transitionTime = 0)
		{
			if (!WwisePlayingIDManager.IsPlayingIDValid(playingID))
			{
				return;
			}

			AkUnitySoundEngine.StopPlayingID(playingID, transitionTime);
		}

		#endregion 停止相关

		#region 音频行为相关

		/// <summary>
		/// 对指定PlayingID执行事件行为
		/// </summary>
		/// <param name="actionOnEventType">事件行为类型</param>
		/// <param name="playingID">PlayingID</param>
		/// <param name="transitionDuration">过渡时间(毫秒)</param>
		/// <returns>是否成功</returns>
		public bool ExecuteActionOnPlayingID(AkActionOnEventType actionOnEventType, uint playingID, int transitionDuration = 0)
		{
			if (!IsInited)
			{
				return false;
			}

			if (!WwisePlayingIDManager.PlayingInfoDictionary.ContainsKey(playingID))
			{
				return false;
			}

			if (actionOnEventType == AkActionOnEventType.AkActionOnEventType_Pause)
			{
				WwisePlayingIDManager.PlayingInfoDictionary[playingID].IsPaused = true;
			}
			else if (actionOnEventType == AkActionOnEventType.AkActionOnEventType_Resume)
			{
				WwisePlayingIDManager.PlayingInfoDictionary[playingID].IsPaused = false;
			}

			AkUnitySoundEngine.ExecuteActionOnPlayingID(actionOnEventType, playingID, transitionDuration);

			return true;
		}

		/// <summary>
		/// 对PlayingID跳转至指定时间
		/// </summary>
		/// <param name="playingID">PlayingID</param>
		/// <param name="time">跳转时间(毫秒)</param>
		/// <returns>是否成功</returns>
		public bool SeekTimeOnPlayingID(uint playingID, int time)
		{
			if (!WwisePlayingIDManager.PlayingInfoDictionary.ContainsKey(playingID))
			{
				return false;
			}

			if (time < 0)
			{
				time = 0;
			}

			WwisePlayingIDManager.PlayingIDInfo playingIDInfo = WwisePlayingIDManager.PlayingInfoDictionary[playingID];
			return AkUnitySoundEngine.SeekOnEvent(playingIDInfo.EventID, playingIDInfo.AkGameObjectID, time, false, playingID) == AKRESULT.AK_Success;
		}

		/// <summary>
		/// 对PlayingID跳转至指定比例
		/// </summary>
		/// <param name="playingID">PlayingID</param>
		/// <param name="percentage">跳转比例(0-1)</param>
		/// <returns>是否成功</returns>
		public bool SeekTimeOnPlayingID(uint playingID, float percentage)
		{
			if (!WwisePlayingIDManager.PlayingInfoDictionary.ContainsKey(playingID))
			{
				return false;
			}

			if (percentage < 0)
			{
				percentage = 0;
			}
			else if (percentage > 1)
			{
				percentage = 1;
			}

			WwisePlayingIDManager.PlayingIDInfo playingIDInfo = WwisePlayingIDManager.PlayingInfoDictionary[playingID];
			return AkUnitySoundEngine.SeekOnEvent(playingIDInfo.EventID, playingIDInfo.AkGameObjectID, percentage, false, playingID) == AKRESULT.AK_Success;
		}

		#endregion 播放行为相关

		#region 标签相关

		private void TickForSoundTag()
		{
			RemoveBlockSoundTagGameObjectList.Clear();
			foreach (ulong akGameObjectID in GameObjectBlockSoundTagSetDict.Keys)
			{
				if (!AkGameObjectManager.RegisterGameObjectDict.ContainsKey(akGameObjectID))
				{
					RemoveBlockSoundTagGameObjectList.Add(akGameObjectID);
				}
			}

			foreach (ulong akGameObjectID in RemoveBlockSoundTagGameObjectList)
			{
				GameObjectBlockSoundTagSetDict.Remove(akGameObjectID);
			}

			RemoveBlockSoundTagGameObjectList.Clear();
		}

		/// <summary>
		/// 拦截指定标签的声音
		/// </summary>
		/// <param name="tag">标签</param>
		/// <param name="gameObject">游戏对象, 如果不为空, 则拦截指定游戏对象上指定标签的声音</param>
		public bool BlockSoundByTag(string tag, GameObject gameObject = null)
		{
			try
			{
				if (string.IsNullOrEmpty(tag))
				{
					return false;
				}

				if (gameObject)
				{
					if (AkGameObjectManager.RegisterGameObject(gameObject, out ulong akGameObjectID))
					{
						if (GameObjectBlockSoundTagSetDict.ContainsKey(akGameObjectID) && GameObjectBlockSoundTagSetDict[akGameObjectID] != null &&
						    GameObjectBlockSoundTagSetDict[akGameObjectID].Contains(tag))
						{
							return false;
						}

						// WwiseLogManager.PrintLog($"[{nameof(WwiseProvider)}] 拦截游戏对象\"{gameObject.name}\"上的\"{tag}\"标签的声音.");
						if (!GameObjectBlockSoundTagSetDict.ContainsKey(akGameObjectID) || GameObjectBlockSoundTagSetDict[akGameObjectID] == null)
						{
							GameObjectBlockSoundTagSetDict[akGameObjectID] = new HashSet<string>();
						}

						GameObjectBlockSoundTagSetDict[akGameObjectID].Add(tag);
					}
				}
				else
				{
					if (BlockSoundTagSet.Contains(tag))
					{
						return false;
					}

					WwiseLogManager.PrintLog($"[{nameof(WwiseProvider)}] 拦截\"{tag}\"标签的声音.");
					BlockSoundTagSet.Add(tag);
				}

				return true;
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"拦截指定标签的声音发生异常: {e}.");
				return false;
			}
		}

		/// <summary>
		/// 取消拦截指定标签的声音
		/// </summary>
		/// <param name="tag">标签</param>
		/// <param name="gameObject">游戏对象, 如果不为空, 则取消拦截指定游戏对象上指定标签的声音</param>
		public bool UnblockSoundByTag(string tag, GameObject gameObject = null)
		{
			try
			{
				if (string.IsNullOrEmpty(tag))
				{
					return false;
				}

				if (gameObject)
				{
					if (!AkGameObjectManager.IsGameObjectRegister(gameObject, out ulong akGameObjectID))
					{
						return false;
					}

					if (!GameObjectBlockSoundTagSetDict.ContainsKey(akGameObjectID))
					{
						return false;
					}

					if (GameObjectBlockSoundTagSetDict[akGameObjectID] == null || !GameObjectBlockSoundTagSetDict[akGameObjectID].Contains(tag))
					{
						return false;
					}

					// WwiseLogManager.PrintLog($"[{nameof(WwiseProvider)}] 取消拦截游戏对象\"{gameObject.name}\"上的\"{tag}\"标签的声音.");
					GameObjectBlockSoundTagSetDict[akGameObjectID].Remove(tag);
					if (GameObjectBlockSoundTagSetDict[akGameObjectID].Count == 0)
					{
						GameObjectBlockSoundTagSetDict.Remove(akGameObjectID);
					}
				}
				else
				{
					if (!BlockSoundTagSet.Contains(tag))
					{
						return false;
					}

					WwiseLogManager.PrintLog($"[{nameof(WwiseProvider)}] 取消拦截\"{tag}\"标签的声音.");
					BlockSoundTagSet.Remove(tag);
				}

				return true;
			}
			catch (Exception e)
			{
				WwiseLogManager.PrintLogError($"取消拦截指定标签的声音发生异常: {e}.");
				return false;
			}
		}

		/// <summary>
		/// 拦截的声音的标签集
		/// </summary>
		public HashSet<string> BlockSoundTagSet { get; } = new HashSet<string>();

		/// <summary>
		/// 游戏对象上拦截的声音标签集的字典
		/// </summary>
		public Dictionary<ulong, HashSet<string>> GameObjectBlockSoundTagSetDict { get; } = new Dictionary<ulong, HashSet<string>>();

		/// <summary>
		/// 需要移除的有指定拦截声音标签的游戏对象列表
		/// </summary>
		private List<ulong> RemoveBlockSoundTagGameObjectList { get; } = new List<ulong>();

		#endregion 标签相关

		#region 播放状态相关

		/// <summary>
		/// 指定游戏对象上的指定事件是否正在播放
		/// </summary>
		/// <param name="gameObject">游戏对象, 若null则为默认游戏对象</param>
		/// <param name="eventName">事件名, 若空则为任意声音事件</param>
		/// <returns>是否正在播放</returns>
		public bool IsEventPlaying(GameObject gameObject = null, string eventName = "")
		{
			return WwisePlayingIDManager.GetPlayingID(eventName, gameObject, m_playingIDList) && m_playingIDList.Count > 0;
		}

		/// <summary>
		/// 获取PlayingID所播放的声音的当前播放位置
		/// </summary>
		/// <param name="playingID">PlayingID</param>
		/// <param name="position">当前播放位置(毫秒)</param>
		/// <returns>是否成功</returns>
		public bool GetPlayingIDSourcePlayPosition(uint playingID, out int position)
		{
			position = -1;
			if (!WwisePlayingIDManager.IsPlayingIDValid(playingID))
			{
				WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] PlayingID不合法: \"{playingID}\".");
				return false;
			}

			AKRESULT result = AkUnitySoundEngine.GetSourcePlayPosition(playingID, out position, true);
			return result == AKRESULT.AK_Success;
		}

		/// <summary>
		/// 非法PlayingID
		/// </summary>
		public static uint InValidPlayingID => WwiseHelper.InValidPlayingID;

		private readonly List<uint> m_playingIDList = new List<uint>();

		#endregion 播放状态相关

		#region 设置变量相关

		/// <summary>
		/// 设置全局的State状态
		/// </summary>
		/// <param name="stateGroup">StateGroup名</param>
		/// <param name="state">StateGroup中的枚举</param>
		/// <returns>是否成功</returns>
		public bool SetState(string stateGroup, string state)
		{
			if (!IsInited)
			{
				return false;
			}

			if (!IsStateGroupValid(stateGroup, state))
			{
				return false;
			}

			AKRESULT ret = AkUnitySoundEngine.SetState(stateGroup, state);
			if (ret == AKRESULT.AK_Success)
			{
				return true;
			}

			WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 设置状态失败: \"{ret}\"; 组名: \"{stateGroup}\", 值名: \"{state}\".");
			return false;
		}

		/// <summary>
		/// 对游戏对象设置Switch
		/// </summary>
		/// <param name="switchGroup">Switch组名</param>
		/// <param name="switchName">Switch组中的值名</param>
		/// <param name="gameObject">游戏对象, 若null时则为全局发声体</param>
		/// <returns>是否成功</returns>
		public bool SetSwitch(string switchGroup, string switchName, GameObject gameObject = null)
		{
			GameObject targetGameObject = gameObject != null ? gameObject : SoundGameObject;

			if (!AkGameObjectManager.RegisterGameObject(targetGameObject, out ulong akGameObjectID))
			{
				return false;
			}

			return SetSwitch(switchGroup, switchName, akGameObjectID);
		}

		/// <summary>
		/// 对Ak游戏对象设置Switch
		/// </summary>
		/// <param name="switchGroup">Switch组名</param>
		/// <param name="switchName">Switch组中的值名</param>
		/// <param name="akGameObjectID">Ak游戏对象ID</param>
		/// <returns>是否成功</returns>
		public bool SetSwitch(string switchGroup, string switchName, ulong akGameObjectID)
		{
			if (!IsInited)
			{
				return false;
			}

			if (!IsSwitchGroupValid(switchGroup, switchName))
			{
				return false;
			}

			if (!AkGameObjectManager.IsAkGameObjectRegister(akGameObjectID))
			{
				return false;
			}

			AKRESULT ret = AkUnitySoundEngine.SetSwitch(switchGroup, switchName, akGameObjectID);

			if (ret == AKRESULT.AK_Success)
			{
				return true;
			}

			WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 设置切换失败: \"{ret}\"; 组名: \"{switchGroup}\", 值名: \"{switchName}\", Ak游戏对象ID: \"{akGameObjectID}\".");
			return false;
		}

		/// <summary>
		/// 设置RTPC(Real Time Parameter Control)
		/// </summary>
		/// <param name="rtpcName">RTPC名字</param>
		/// <param name="value">RTPC值</param>
		/// <param name="gameObject">游戏对象</param>
		/// <param name="valueChangeDuration">RTPC值变化时长(毫秒), 默认为-1, 如果传入大于等于0的值, 则会让RTPC值的变化无视Wwise内的插值变化设置, 例如设置为0, 则会无视插值函数, 直接设置RTPC到指定值</param>
		/// <param name="bypassInternalValueInterpolation">无视内部插值效果, 默认不启用</param>
		/// <returns>是否成功</returns>
		public bool SetRTPC(string rtpcName, float value, GameObject gameObject = null, int valueChangeDuration = -1, bool bypassInternalValueInterpolation = false)
		{
			if (!IsInited)
			{
				return false;
			}

			if (!IsRTPCValid(rtpcName))
			{
				return false;
			}

			ulong akGameObjectID = AkUnitySoundEngine.AK_INVALID_GAME_OBJECT;
			if (gameObject)
			{
				if (!AkGameObjectManager.RegisterGameObject(gameObject, out akGameObjectID))
				{
					return false;
				}
			}

			AKRESULT akResult;
			if (valueChangeDuration == -1)
			{
				akResult = AkUnitySoundEngine.SetRTPCValue(rtpcName, value, akGameObjectID);
			}
			else
			{
				valueChangeDuration = Math.Min(valueChangeDuration, 0);
				akResult = AkUnitySoundEngine.SetRTPCValue(rtpcName, value, akGameObjectID, valueChangeDuration, AkCurveInterpolation.AkCurveInterpolation_Linear, bypassInternalValueInterpolation);
			}

			if (akResult == AKRESULT.AK_Success)
			{
				return true;
			}

			WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 设置RTPC\"{rtpcName}\"失败: \"{akResult}\".");
			return false;
		}

		/// <summary>
		/// 通过PlayingID设置RTPC(Real Time Parameter Control)
		/// 如果通过该方法设置RTPC, 则此RTPC值将会具有比PlayIngID对应的游戏对象以及全局更高的优先级
		/// </summary>
		/// <param name="rtpcName">RTPC名字</param>
		/// <param name="value">RTPC值</param>
		/// <param name="playingID">PlayingID</param>
		/// <param name="valueChangeDuration">RTPC值变化时长(毫秒), 默认为-1, 如果传入大于等于0的值, 则会让RTPC值的变化无视Wwise内的插值变化设置, 例如设置为0, 则会无视插值函数, 直接设置RTPC到指定值</param>
		/// <param name="bypassInternalValueInterpolation">无视内部插值效果, 默认不启用</param>
		/// <returns>是否成功</returns>
		public bool SetRTPCByPlayingID(string rtpcName, float value, uint playingID, int valueChangeDuration = -1, bool bypassInternalValueInterpolation = false)
		{
			if (!IsInited)
			{
				return false;
			}

			if (!IsRTPCValid(rtpcName))
			{
				return false;
			}

			if (!WwisePlayingIDManager.GetAkGameObjectFromPlayingID(playingID, out _))
			{
				return false;
			}

			AKRESULT akResult;
			if (valueChangeDuration == -1)
			{
				akResult = AkUnitySoundEngine.SetRTPCValueByPlayingID(rtpcName, value, playingID);
			}
			else
			{
				valueChangeDuration = Math.Min(valueChangeDuration, 0);
				akResult = AkUnitySoundEngine.SetRTPCValueByPlayingID(rtpcName, value, playingID, valueChangeDuration, AkCurveInterpolation.AkCurveInterpolation_Linear, bypassInternalValueInterpolation);
			}

			if (akResult == AKRESULT.AK_Success)
			{
				return true;
			}

			WwiseLogManager.PrintLogError($"[{nameof(WwiseProvider)}] 设置RTPC失败: \"{akResult}\"; RTPC名: \"{rtpcName}\".");
			return false;
		}

		/// <summary>
		/// 设置游戏对象的声音音量
		/// </summary>
		/// <param name="gameObject">游戏对象, 不允许为null</param>
		/// <param name="volume">音量[0-1f]</param>
		/// <returns>是否成功</returns>
		public bool SetGameObjectVolume(GameObject gameObject, float volume)
		{
			if (!IsInited || DefaultListener == null)
			{
				return false;
			}

			if (!AkGameObjectManager.RegisterGameObject(gameObject, out ulong akGameObjectID))
			{
				return false;
			}

			volume = volume < 0f ? 0 : volume;
			volume = volume > 1f ? 1 : volume;
			AKRESULT result = AkUnitySoundEngine.SetGameObjectOutputBusVolume(akGameObjectID, AkGameObjectManager.DefaultListenerID, volume);
			return result == AKRESULT.AK_Success;
		}

		#endregion 设置变量相关

		#region 回调相关

		/// <summary>
		/// 取消事件回调
		/// </summary>
		/// <param name="playingID">PlayingID</param>
		public void CancelEventCallback(uint playingID)
		{
			if (!IsInited)
			{
				return;
			}

			if (WwisePlayingIDManager.PlayingInfoDictionary.ContainsKey(playingID))
			{
				AkUnitySoundEngine.CancelEventCallback(playingID);
			}
		}

		#endregion

		/// <summary>
		/// 播放声音的API类型枚举
		/// </summary>
		public enum AudioAPIType
		{
			/// <summary>
			/// 播放音效
			/// </summary>
			PlaySound,

			/// <summary>
			/// 播放音乐
			/// 注意, 如果使用该API, 则发声体游戏对象使用默认的全局音乐发声体游戏对象, 传入的游戏对象参数将被忽略
			/// </summary>
			PlayMusic,

			/// <summary>
			/// 播放语音
			/// </summary>
			PlayVoice
		}
	}
}