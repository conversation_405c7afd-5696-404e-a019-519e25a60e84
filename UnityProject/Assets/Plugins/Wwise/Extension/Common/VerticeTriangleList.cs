using System.Collections.Generic;
using UnityEngine;

namespace AK.Wwise
{
	//	This is a list recording which triangles use each vertex. It is essentially
	//	a jagged array with one top level entry per vertex. Each of the separate
	//	sub-arrays is a list of all the triangles that use that vertex.
	public class VerticeTriangleList
	{
		private int[][] m_list;

		//	Indexable - use "vertTri[i]" to get the list of triangles for vertex i.
		public int[] this[int index] => m_list[index];

		public VerticeTriangleList(int[] tri, int numVerts)
		{
			Init(tri, numVerts);
		}

		public VerticeTriangleList(Mesh mesh)
		{
			Init(mesh.triangles, mesh.vertexCount);
		}

		//	You don't usually need to call this - it's just to assist the implementation
		//	of the constructors.
		private void Init(IReadOnlyList<int> tri, int numVerts)
		{
			//	First, go through the triangles, keeping a count of how many times
			//	each vert is used.
			int[] counts = new int[numVerts];

			foreach (int t in tri)
			{
				counts[t]++;
			}

			//	Initialise an empty jagged array with the appropriate number of elements
			//	for each vert.
			m_list = new int[numVerts][];

			for (int i = 0; i < counts.Length; i++)
			{
				m_list[i] = new int[counts[i]];
			}

			//	Assign the appropriate triangle number each time a given vert
			//	is encountered in the triangles.
			for (int i = 0; i < tri.Count; i++)
			{
				int vert = tri[i];
				m_list[vert][--counts[vert]] = i / 3;
			}
		}
	}
}