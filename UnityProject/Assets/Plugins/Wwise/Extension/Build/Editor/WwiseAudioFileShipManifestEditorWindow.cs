using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

namespace AK.Wwise.Editor
{
	public class WwiseAudioFileShipManifestEditorWindow : EditorWindow
	{
		private void OnEnable()
		{
			WwiseAudioFileShipManifest.UpdateManifest();
		}

		private void OnGUI()
		{
			InitializeTreeView();
			EditorGUILayout.BeginVertical();
			{
				OnSearchFieldGUI();
				OnFileSizeSummaryView();
				Rect searchFieldRect = GUILayoutUtility.GetLastRect();
				float treeViewY = searchFieldRect.y + searchFieldRect.height + 5;
				var treeViewRect = new Rect(searchFieldRect.x, treeViewY, searchFieldRect.width, position.height - treeViewY - 5);
				OnTreeViewGUI(treeViewRect);
			}
			EditorGUILayout.EndVertical();
		}

		private void OnSearchFieldGUI()
		{
			EditorGUILayout.BeginHorizontal();
			{
				EditorGUILayout.LabelField("搜索:", GUILayout.Width(50));
				EditorGUI.BeginChangeCheck();
				{
					m_searchString = EditorGUILayout.TextField("", m_searchString);
				}
				if (EditorGUI.EndChangeCheck())
				{
					m_treeView.searchString = m_searchString;
				}

				GUILayout.Space(20);
				EditorGUILayout.LabelField("仅显示无标签的条目:", GUILayout.Width(150));
				m_isOnlyDisplayNoTagItem = EditorGUILayout.Toggle("", m_isOnlyDisplayNoTagItem, GUILayout.Width(20));

				if (GUILayout.Button(EditorGUIUtility.TrIconContent("RotateTool", "刷新Wwise音频文件清单"), GUILayout.Width(30)))
				{
					if (!WwiseAudioFileShipManifest.UpdateManifest())
					{
						EditorUtility.DisplayDialog("错误", "\"SoundBanksInfo.xml\"文件不存在, 请在Console确认详情!", "确认");
					}

					m_treeView.Reload();
				}
			}
			EditorGUILayout.EndHorizontal();
		}

		private void OnFileSizeSummaryView()
		{
			EditorGUILayout.BeginVertical();
			{
				using (new GUILayout.VerticalScope("box"))
				{
					EditorGUILayout.BeginHorizontal();
					{
						if (!m_isDisplayFileSizeSummaryView)
						{
							if (GUILayout.Button("+", GUILayout.Width(30)))
							{
								m_isDisplayFileSizeSummaryView = true;
							}
						}
						else
						{
							if (GUILayout.Button("-", GUILayout.Width(30)))
							{
								m_isDisplayFileSizeSummaryView = false;
							}
						}

						EditorGUILayout.LabelField("文件大小概况(以移动端为准):", GUILayout.Width(170));
						if (GUILayout.Button(EditorGUIUtility.TrIconContent("RotateTool", "刷新文件大小概况"), GUILayout.Width(30)))
						{
							m_isDisplayFileSizeSummaryView = true;
							RefreshFileSize();
						}
					}
					EditorGUILayout.EndHorizontal();

					if (m_isDisplayFileSizeSummaryView)
					{
						foreach (string fileSizeSummaryStr in m_fileSizeSummaryStrList)
						{
							EditorGUILayout.LabelField(fileSizeSummaryStr);
						}
					}
				}
			}
			EditorGUILayout.EndHorizontal();
		}

		private void InitializeTreeView()
		{
			if (m_initialized)
			{
				return;
			}

			if (m_searchField == null)
			{
				m_searchField = new SearchField();
			}

			if (m_treeViewState == null)
			{
				m_treeViewState = new TreeViewState();
			}

			bool firstInit = m_multiColumnHeaderState == null;
			MultiColumnHeaderState headerState = WwiseAudioFileShipManifestTreeView.CreateDefaultMultiColumnHeaderState(position.width);
			if (MultiColumnHeaderState.CanOverwriteSerializedFields(m_multiColumnHeaderState, headerState))
			{
				MultiColumnHeaderState.OverwriteSerializedFields(m_multiColumnHeaderState, headerState);
			}

			m_multiColumnHeaderState = headerState;

			var multiColumnHeader = new MultiColumnHeader(headerState);
			if (firstInit)
			{
				multiColumnHeader.ResizeToFit();
			}

			m_treeView = new WwiseAudioFileShipManifestTreeView(m_treeViewState, multiColumnHeader, this);

			m_initialized = true;
		}

		private void OnTreeViewGUI(Rect rect)
		{
			m_treeView.Reload();
			m_treeView.OnGUI(rect);
		}

		private void RefreshFileSize()
		{
			m_wwiseAudioFileSizeInfos.Clear();
			List<WwiseAudioFileShipManifest.WwiseAudioFileShipInfo> wwiseAudioFileShipInfoList = WwiseAudioFileShipManifest.Instance.WwiseAudioFileShipInfos;
			string platformName = AkBuildPreprocessor.GetPlatformName(BuildTarget.Android);
			string soundbankDirPath = $"{Application.dataPath}/{AkWwiseEditorSettings.Instance.SoundbankPath}/{platformName}";
			soundbankDirPath = WwiseHelper.FixSlash(Path.GetFullPath(soundbankDirPath));
			string[] directories = Directory.GetDirectories(soundbankDirPath, "*", SearchOption.TopDirectoryOnly);
			var dirNameList = new List<string>();
			foreach (string directory in directories)
			{
				var directoryInfo = new DirectoryInfo(directory);
				dirNameList.Add(directoryInfo.Name);
			}

			var sizeInfoDict = new Dictionary<string, Dictionary<string, float>>();

			foreach (string filePath in Directory.EnumerateFiles(soundbankDirPath, "*.*", SearchOption.AllDirectories))
			{
				if (!filePath.EndsWith(".bnk", StringComparison.OrdinalIgnoreCase) && !filePath.EndsWith($".{WwiseHelper.StreamedFileExtension}", StringComparison.OrdinalIgnoreCase))
				{
					continue;
				}

				string path = WwiseHelper.FixSlash(filePath);
				var fileInfo = new FileInfo(path);
				string fileName = fileInfo.Name;
				float size = (float) fileInfo.Length / 1024 / 1024;
				string language = string.Empty;
				string tag = string.Empty;
				string fileRelativePath = path.Substring(soundbankDirPath.Length + 1);

				foreach (string dirName in dirNameList)
				{
					if (!fileRelativePath.StartsWith($"{dirName}/") || dirName == "Media")
					{
						continue;
					}

					language = dirName;
					break;
				}

				foreach (WwiseAudioFileShipManifest.WwiseAudioFileShipInfo wwiseAudioFileShipInfo in wwiseAudioFileShipInfoList)
				{
					if (wwiseAudioFileShipInfo.FileName != fileName)
					{
						continue;
					}

					tag = wwiseAudioFileShipInfo.Tag;
					break;
				}

				if (!sizeInfoDict.ContainsKey(tag))
				{
					sizeInfoDict.Add(tag, new Dictionary<string, float>());
				}

				if (sizeInfoDict[tag].ContainsKey(language))
				{
					sizeInfoDict[tag][language] += size;
				}
				else
				{
					if (!sizeInfoDict[tag].ContainsKey(language))
					{
						sizeInfoDict[tag].Add(language, size);
					}
				}

				var wwiseAudioFileSizeInfo = new WwiseAudioFileSizeInfo
				{
					Path = path,
					Language = language,
					Tag = tag,
					Size = size
				};

				m_wwiseAudioFileSizeInfos.Add(wwiseAudioFileSizeInfo);
			}

			m_fileSizeSummaryStrList.Clear();
			float totalSize = 0;
			foreach (KeyValuePair<string, Dictionary<string, float>> tagPair in sizeInfoDict)
			{
				string tag = tagPair.Key;
				Dictionary<string, float> languageDict = tagPair.Value;
				float tagSize = 0;
				foreach (KeyValuePair<string, float> languagePair in languageDict)
				{
					tagSize += languagePair.Value;
				}

				totalSize += tagSize;

				m_fileSizeSummaryStrList.Add($"标签\"{tag}\": {tagSize:0.00}MB");
				foreach (KeyValuePair<string, float> languagePair in languageDict)
				{
					string language = languagePair.Key;
					m_fileSizeSummaryStrList.Add(string.IsNullOrEmpty(language) ? $"\t非语言: {languagePair.Value:0.00}MB" : $"\t语言\"{languagePair.Key}\": {languagePair.Value:0.00}MB");
				}
			}

			m_fileSizeSummaryStrList.Add($"总大小: {totalSize:0.00}MB");
		}

		public bool IsOnlyDisplayNoTagItem => m_isOnlyDisplayNoTagItem;

		[SerializeField] private TreeViewState m_treeViewState;

		[SerializeField] private MultiColumnHeaderState m_multiColumnHeaderState;

		private SearchField m_searchField;

		[SerializeField] private string m_searchString = string.Empty;

		[SerializeField] private bool m_isOnlyDisplayNoTagItem;

		[NonSerialized] private bool m_initialized;

		private WwiseAudioFileShipManifestTreeView m_treeView;

		private readonly List<WwiseAudioFileSizeInfo> m_wwiseAudioFileSizeInfos = new List<WwiseAudioFileSizeInfo>();

		[SerializeField] private bool m_isDisplayFileSizeSummaryView;
		private readonly List<string> m_fileSizeSummaryStrList = new List<string>();

		[MenuItem("Wwise/打包相关/打开Wwise音频文件部署编辑器", priority = 90)]
		public static void OpenWindow()
		{
			var window = GetWindow<WwiseAudioFileShipManifestEditorWindow>();
			window.minSize = new Vector2(1000, 600);
			window.titleContent = new GUIContent("Wwise音频文件部署清单编辑器");
			window.Focus();
			window.Repaint();
		}
	}

	public class WwiseAudioFileSizeInfo
	{
		public string Path { get; set; }

		public string Language { get; set; }

		public string Tag { get; set; }
		public float Size { get; set; } = -1;
	}
}