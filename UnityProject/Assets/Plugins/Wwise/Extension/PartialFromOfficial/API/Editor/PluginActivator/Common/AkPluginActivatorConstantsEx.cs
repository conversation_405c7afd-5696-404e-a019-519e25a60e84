using System;
using System.Collections.Generic;
using UnityEditor;

public partial class AkPluginActivatorConstants
{
	/// <summary>
	/// 根据wwisePluginFolderRelativePath来修正Plugin目录. 用于当Wwise插件放在其他或绝对目录的情况.
	/// </summary>
	private static string GetPluginFolder(string wwisePluginFolderRelativePath)
	{
		List<PluginImporter> wwisePluginImporters = AkPluginActivator.GetWwisePluginImporters();
		if (wwisePluginImporters.Count == 0 || !wwisePluginImporters[0].assetPath.Contains(wwisePluginFolderRelativePath))
		{
			return string.Empty;
		}

		string wwisePluginPath = wwisePluginImporters[0].assetPath;

		int assetsPrefixLength = "Assets/".Length;

		string pluginFolder = wwisePluginPath.Substring(assetsPrefixLength,
			wwisePluginFolderRelativePath.Length +
			wwisePluginPath.IndexOf(wwisePluginFolderRelativePath, StringComparison.OrdinalIgnoreCase) - assetsPrefixLength);

		return pluginFolder;
	}

	/// <summary>
	/// 这里做属性的原因是各个项目组可能会修改Plugin目录而导致打包时出现错误.
	/// 这里对OriginalWwisePluginFolder做一进步修正,和原有变量语义相同.
	/// </summary>
	protected internal static string WwisePluginFolder
	{
		get
		{
			if (string.IsNullOrEmpty(s_wwisePluginFolderRelativePath))
			{
				s_wwisePluginFolderRelativePath = GetPluginFolder(OriginalWwisePluginFolderRelativePath);
			}

			return s_wwisePluginFolderRelativePath;
		}
	}

	private const string OriginalWwisePluginFolderRelativePath = "API/Runtime/Plugins";

	private static string s_wwisePluginFolderRelativePath;
}