/*******************************************************************************
The content of this file includes portions of the proprietary AUDIOKINETIC Wwise
Technology released in source code form as part of the game integration package.
The content of this file may not be used without valid licenses to the
AUDIOKINETIC Wwise Technology.
Note that the use of the game engine is subject to the Unity(R) Terms of
Service at https://unity3d.com/legal/terms-of-service
 
License Usage
 
Licensees holding valid licenses to the AUDIOKINETIC Wwise Technology may use
this file in accordance with the end user license agreement provided with the
software or, alternatively, in accordance with the terms contained
in a written agreement between you and Audiokinetic Inc.
Copyright (c) 2025 Audiokinetic Inc.
*******************************************************************************/

#if UNITY_EDITOR && UNITY_OPENHARMONY
using System;
using System.IO;
using System.Linq;
using System.Text;

[UnityEditor.InitializeOnLoad]
public class AkOpenHarmonyBuildPreprocessor : UnityEditor.OpenHarmony.IPostGenerateOpenHarmonyProject
{
	private const uint OpenHarmonyBuildTarget = 48;
	public int callbackOrder => 0;

	static AkOpenHarmonyBuildPreprocessor()
	{
		if (UnityEditor.AssetDatabase.IsAssetImportWorkerProcess())
		{
			return;
		}

		AkBuildPreprocessor.RegisterBuildTarget((UnityEditor.BuildTarget)OpenHarmonyBuildTarget, new AkBuildPreprocessor.PlatformConfiguration
		{
			WwisePlatformName = "OpenHarmony"
        });

	}
	public void OnPostGenerateOpenHarmonyProject(string path)
	{
		string[] paths = Directory.GetFiles(path, "*.ts", SearchOption.AllDirectories);
		paths = paths.Concat(Directory.GetFiles(path, "*.ets", SearchOption.AllDirectories)).ToArray();
		
		string PlayerAbilityFileName = "TuanjiePlayerAbility.ts";
#if TUANJIE_1_4_OR_NEWER
		PlayerAbilityFileName = "TuanjiePlayerAbilityBase.ets";
#elif TUANJIE_1_3_OR_NEWER
		PlayerAbilityFileName = "TuanjiePlayerAbility.ets";
#endif
		var abilityPath = Array.Find(paths, s => Path.GetFileName(s) == PlayerAbilityFileName);
		if (string.IsNullOrEmpty(abilityPath))
		{
			UnityEngine.Debug.LogError("Could not find TuanjiePlayerAbility.ts file, application will be unable to initialize the Wwise OpenHarmony IO hook.");
			return;
		}
		
		FileStream file = File.Open(abilityPath, FileMode.Open);
		byte[] buffer = new byte[file.Length];
		file.Read(buffer, 0, buffer.Length);
		file.Close();

		string context = Encoding.UTF8.GetString(buffer);
		context = context.Insert(context.IndexOf("import tuanjie from 'libtuanjie.so';", StringComparison.OrdinalIgnoreCase), "import AkUnitySoundEngine from 'libAkUnitySoundEngine.so';" + Environment.NewLine);
		context = context.Insert(context.IndexOf("DisplayInfoManager.getInstance().initialize();"), "AkUnitySoundEngine.AkOpenHarmonySystemInit(this.context);" + Environment.NewLine + "\t");

		file = File.Open(abilityPath, FileMode.Open);
		file.Write(Encoding.UTF8.GetBytes(context), 0, context.Length);
		file.Close(); 
	}
}
#endif