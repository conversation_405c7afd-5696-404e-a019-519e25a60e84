#if UNITY_ANDROID && ! UNITY_EDITOR
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


class AkUnitySoundEnginePINVOKE {

  public class SWIGStringWithLengthHelper {

    [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="SWIG_csharp_string_to_c")]
    private static extern global::System.IntPtr SWIG_csharp_string_to_c0(int size, int len, [global::System.Runtime.InteropServices.In,global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr, SizeParamIndex=0)] string str);

    [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="SWIG_csharp_string_size")]
    private static extern int SWIG_csharp_string_size(global::System.IntPtr str);

    [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="SWIG_csharp_string_str")]
    private static extern global::System.IntPtr SWIG_csharp_string_str(global::System.IntPtr str);

    public static global::System.IntPtr SWIG_csharp_string_to_c(string str) {
      if (str == null)
        return global::System.IntPtr.Zero;
      global::System.Text.Encoding utf8 = global::System.Text.Encoding.UTF8;
      return SWIG_csharp_string_to_c0(utf8.GetByteCount(str), str.Length, str);
    }

    public static string SWIG_c_to_csharp_string(global::System.IntPtr str) {
      int size = SWIG_csharp_string_size(str);
      if (size > 0) {
        global::System.IntPtr s = SWIG_csharp_string_str(str);
        byte[] b = new byte[size];
        global::System.Runtime.InteropServices.Marshal.Copy(s, b, 0, size);
        global::System.Text.Encoding utf8 = global::System.Text.Encoding.UTF8;
        return utf8.GetString(b);
      }
      return null;
    }
  }


  static AkUnitySoundEnginePINVOKE() {
  }


  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_JoystickIdToWwiseId")]
  public static extern uint CSharp_JoystickIdToWwiseId(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INVALID_SHARE_SET_ID_get")]
  public static extern uint CSharp_AK_INVALID_SHARE_SET_ID_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INVALID_CACHE_ID_get")]
  public static extern ulong CSharp_AK_INVALID_CACHE_ID_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INVALID_PIPELINE_ID_get")]
  public static extern uint CSharp_AK_INVALID_PIPELINE_ID_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INVALID_AUDIO_OBJECT_ID_get")]
  public static extern ulong CSharp_AK_INVALID_AUDIO_OBJECT_ID_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_SOUNDBANK_VERSION_get")]
  public static extern uint CSharp_AK_SOUNDBANK_VERSION_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkJobType_Generic_get")]
  public static extern uint CSharp_AkJobType_Generic_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkJobType_AudioProcessing_get")]
  public static extern uint CSharp_AkJobType_AudioProcessing_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkJobType_SpatialAudio_get")]
  public static extern uint CSharp_AkJobType_SpatialAudio_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_NUM_JOB_TYPES_get")]
  public static extern uint CSharp_AK_NUM_JOB_TYPES_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioSettings_uNumSamplesPerFrame_set")]
  public static extern void CSharp_AkAudioSettings_uNumSamplesPerFrame_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioSettings_uNumSamplesPerFrame_get")]
  public static extern uint CSharp_AkAudioSettings_uNumSamplesPerFrame_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioSettings_uNumSamplesPerSecond_set")]
  public static extern void CSharp_AkAudioSettings_uNumSamplesPerSecond_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioSettings_uNumSamplesPerSecond_get")]
  public static extern uint CSharp_AkAudioSettings_uNumSamplesPerSecond_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkAudioSettings")]
  public static extern global::System.IntPtr CSharp_new_AkAudioSettings();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkAudioSettings")]
  public static extern void CSharp_delete_AkAudioSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_idDevice_set")]
  public static extern void CSharp_AkDeviceDescription_idDevice_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_idDevice_get")]
  public static extern uint CSharp_AkDeviceDescription_idDevice_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_deviceName_set")]
  public static extern void CSharp_AkDeviceDescription_deviceName_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_deviceName_get")]
  public static extern global::System.IntPtr CSharp_AkDeviceDescription_deviceName_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_deviceStateMask_set")]
  public static extern void CSharp_AkDeviceDescription_deviceStateMask_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_deviceStateMask_get")]
  public static extern int CSharp_AkDeviceDescription_deviceStateMask_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_isDefaultDevice_set")]
  public static extern void CSharp_AkDeviceDescription_isDefaultDevice_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_isDefaultDevice_get")]
  public static extern bool CSharp_AkDeviceDescription_isDefaultDevice_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_Clear")]
  public static extern void CSharp_AkDeviceDescription_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_GetSizeOf")]
  public static extern int CSharp_AkDeviceDescription_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceDescription_Clone")]
  public static extern void CSharp_AkDeviceDescription_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkDeviceDescription")]
  public static extern global::System.IntPtr CSharp_new_AkDeviceDescription();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkDeviceDescription")]
  public static extern void CSharp_delete_AkDeviceDescription(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_Position")]
  public static extern AkVector64 CSharp_AkWorldTransform_Position(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_OrientationFront")]
  public static extern UnityEngine.Vector3 CSharp_AkWorldTransform_OrientationFront(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_OrientationTop")]
  public static extern UnityEngine.Vector3 CSharp_AkWorldTransform_OrientationTop(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_Set__SWIG_0")]
  public static extern void CSharp_AkWorldTransform_Set__SWIG_0(global::System.IntPtr jarg1, AkVector64 jarg2, UnityEngine.Vector3 jarg3, UnityEngine.Vector3 jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_Set__SWIG_1")]
  public static extern void CSharp_AkWorldTransform_Set__SWIG_1(global::System.IntPtr jarg1, double jarg2, double jarg3, double jarg4, float jarg5, float jarg6, float jarg7, float jarg8, float jarg9, float jarg10);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_SetPosition__SWIG_0")]
  public static extern void CSharp_AkWorldTransform_SetPosition__SWIG_0(global::System.IntPtr jarg1, AkVector64 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_SetPosition__SWIG_1")]
  public static extern void CSharp_AkWorldTransform_SetPosition__SWIG_1(global::System.IntPtr jarg1, double jarg2, double jarg3, double jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_SetOrientation__SWIG_0")]
  public static extern void CSharp_AkWorldTransform_SetOrientation__SWIG_0(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2, UnityEngine.Vector3 jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkWorldTransform_SetOrientation__SWIG_1")]
  public static extern void CSharp_AkWorldTransform_SetOrientation__SWIG_1(global::System.IntPtr jarg1, float jarg2, float jarg3, float jarg4, float jarg5, float jarg6, float jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkWorldTransform")]
  public static extern global::System.IntPtr CSharp_new_AkWorldTransform();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkWorldTransform")]
  public static extern void CSharp_delete_AkWorldTransform(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_Position")]
  public static extern UnityEngine.Vector3 CSharp_AkTransform_Position(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_OrientationFront")]
  public static extern UnityEngine.Vector3 CSharp_AkTransform_OrientationFront(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_OrientationTop")]
  public static extern UnityEngine.Vector3 CSharp_AkTransform_OrientationTop(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_Set__SWIG_0")]
  public static extern void CSharp_AkTransform_Set__SWIG_0(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2, UnityEngine.Vector3 jarg3, UnityEngine.Vector3 jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_Set__SWIG_1")]
  public static extern void CSharp_AkTransform_Set__SWIG_1(global::System.IntPtr jarg1, float jarg2, float jarg3, float jarg4, float jarg5, float jarg6, float jarg7, float jarg8, float jarg9, float jarg10);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_SetPosition__SWIG_0")]
  public static extern void CSharp_AkTransform_SetPosition__SWIG_0(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_SetPosition__SWIG_1")]
  public static extern void CSharp_AkTransform_SetPosition__SWIG_1(global::System.IntPtr jarg1, float jarg2, float jarg3, float jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_SetOrientation__SWIG_0")]
  public static extern void CSharp_AkTransform_SetOrientation__SWIG_0(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2, UnityEngine.Vector3 jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTransform_SetOrientation__SWIG_1")]
  public static extern void CSharp_AkTransform_SetOrientation__SWIG_1(global::System.IntPtr jarg1, float jarg2, float jarg3, float jarg4, float jarg5, float jarg6, float jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkTransform")]
  public static extern global::System.IntPtr CSharp_new_AkTransform();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkTransform")]
  public static extern void CSharp_delete_AkTransform(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ConvertAkVector64ToAkVector")]
  public static extern UnityEngine.Vector3 CSharp_ConvertAkVector64ToAkVector(AkVector64 jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ConvertAkWorldTransformToAkTransform")]
  public static extern global::System.IntPtr CSharp_ConvertAkWorldTransformToAkTransform(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ConvertAkVectorToAkVector64")]
  public static extern AkVector64 CSharp_ConvertAkVectorToAkVector64(UnityEngine.Vector3 jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ConvertAkTransformToAkWorldTransform")]
  public static extern global::System.IntPtr CSharp_ConvertAkTransformToAkWorldTransform(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_occlusion_set")]
  public static extern void CSharp_AkObstructionOcclusionValues_occlusion_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_occlusion_get")]
  public static extern float CSharp_AkObstructionOcclusionValues_occlusion_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_obstruction_set")]
  public static extern void CSharp_AkObstructionOcclusionValues_obstruction_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_obstruction_get")]
  public static extern float CSharp_AkObstructionOcclusionValues_obstruction_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_Clear")]
  public static extern void CSharp_AkObstructionOcclusionValues_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_GetSizeOf")]
  public static extern int CSharp_AkObstructionOcclusionValues_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObstructionOcclusionValues_Clone")]
  public static extern void CSharp_AkObstructionOcclusionValues_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkObstructionOcclusionValues")]
  public static extern global::System.IntPtr CSharp_new_AkObstructionOcclusionValues();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkObstructionOcclusionValues")]
  public static extern void CSharp_delete_AkObstructionOcclusionValues(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelEmitter_position_set")]
  public static extern void CSharp_AkChannelEmitter_position_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelEmitter_position_get")]
  public static extern global::System.IntPtr CSharp_AkChannelEmitter_position_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelEmitter_uInputChannels_set")]
  public static extern void CSharp_AkChannelEmitter_uInputChannels_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelEmitter_uInputChannels_get")]
  public static extern uint CSharp_AkChannelEmitter_uInputChannels_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelEmitter_padding_set")]
  public static extern void CSharp_AkChannelEmitter_padding_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelEmitter_padding_get")]
  public static extern global::System.IntPtr CSharp_AkChannelEmitter_padding_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkChannelEmitter")]
  public static extern void CSharp_delete_AkChannelEmitter(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_listenerID_set")]
  public static extern void CSharp_AkAuxSendValue_listenerID_set(global::System.IntPtr jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_listenerID_get")]
  public static extern ulong CSharp_AkAuxSendValue_listenerID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_auxBusID_set")]
  public static extern void CSharp_AkAuxSendValue_auxBusID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_auxBusID_get")]
  public static extern uint CSharp_AkAuxSendValue_auxBusID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_fControlValue_set")]
  public static extern void CSharp_AkAuxSendValue_fControlValue_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_fControlValue_get")]
  public static extern float CSharp_AkAuxSendValue_fControlValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_Set")]
  public static extern void CSharp_AkAuxSendValue_Set(global::System.IntPtr jarg1, ulong jarg2, uint jarg3, float jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_IsSame")]
  public static extern bool CSharp_AkAuxSendValue_IsSame(global::System.IntPtr jarg1, ulong jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAuxSendValue_GetSizeOf")]
  public static extern int CSharp_AkAuxSendValue_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkAuxSendValue")]
  public static extern void CSharp_delete_AkAuxSendValue(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkRamp__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkRamp__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkRamp__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkRamp__SWIG_1(float jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRamp_fPrev_set")]
  public static extern void CSharp_AkRamp_fPrev_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRamp_fPrev_get")]
  public static extern float CSharp_AkRamp_fPrev_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRamp_fNext_set")]
  public static extern void CSharp_AkRamp_fNext_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRamp_fNext_get")]
  public static extern float CSharp_AkRamp_fNext_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkRamp")]
  public static extern void CSharp_delete_AkRamp(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INT_get")]
  public static extern ushort CSharp_AK_INT_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_FLOAT_get")]
  public static extern ushort CSharp_AK_FLOAT_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INTERLEAVED_get")]
  public static extern byte CSharp_AK_INTERLEAVED_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_NONINTERLEAVED_get")]
  public static extern byte CSharp_AK_NONINTERLEAVED_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_LE_NATIVE_BITSPERSAMPLE_get")]
  public static extern uint CSharp_AK_LE_NATIVE_BITSPERSAMPLE_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_LE_NATIVE_SAMPLETYPE_get")]
  public static extern uint CSharp_AK_LE_NATIVE_SAMPLETYPE_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_LE_NATIVE_INTERLEAVE_get")]
  public static extern uint CSharp_AK_LE_NATIVE_INTERLEAVE_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uSampleRate_set")]
  public static extern void CSharp_AkAudioFormat_uSampleRate_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uSampleRate_get")]
  public static extern uint CSharp_AkAudioFormat_uSampleRate_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_channelConfig_set")]
  public static extern void CSharp_AkAudioFormat_channelConfig_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_channelConfig_get")]
  public static extern global::System.IntPtr CSharp_AkAudioFormat_channelConfig_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uBitsPerSample_set")]
  public static extern void CSharp_AkAudioFormat_uBitsPerSample_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uBitsPerSample_get")]
  public static extern uint CSharp_AkAudioFormat_uBitsPerSample_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uBlockAlign_set")]
  public static extern void CSharp_AkAudioFormat_uBlockAlign_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uBlockAlign_get")]
  public static extern uint CSharp_AkAudioFormat_uBlockAlign_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uTypeID_set")]
  public static extern void CSharp_AkAudioFormat_uTypeID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uTypeID_get")]
  public static extern uint CSharp_AkAudioFormat_uTypeID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uInterleaveID_set")]
  public static extern void CSharp_AkAudioFormat_uInterleaveID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_uInterleaveID_get")]
  public static extern uint CSharp_AkAudioFormat_uInterleaveID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_GetNumChannels")]
  public static extern uint CSharp_AkAudioFormat_GetNumChannels(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_GetBitsPerSample")]
  public static extern uint CSharp_AkAudioFormat_GetBitsPerSample(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_GetBlockAlign")]
  public static extern uint CSharp_AkAudioFormat_GetBlockAlign(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_GetTypeID")]
  public static extern uint CSharp_AkAudioFormat_GetTypeID(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_GetInterleaveID")]
  public static extern uint CSharp_AkAudioFormat_GetInterleaveID(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioFormat_SetAll")]
  public static extern void CSharp_AkAudioFormat_SetAll(global::System.IntPtr jarg1, uint jarg2, global::System.IntPtr jarg3, uint jarg4, uint jarg5, uint jarg6, uint jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkAudioFormat")]
  public static extern global::System.IntPtr CSharp_new_AkAudioFormat();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkAudioFormat")]
  public static extern void CSharp_delete_AkAudioFormat(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_IsBankCodecID")]
  public static extern bool CSharp_IsBankCodecID(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_Ak3dData")]
  public static extern global::System.IntPtr CSharp_new_Ak3dData();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_xform_set")]
  public static extern void CSharp_Ak3dData_xform_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_xform_get")]
  public static extern global::System.IntPtr CSharp_Ak3dData_xform_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_spread_set")]
  public static extern void CSharp_Ak3dData_spread_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_spread_get")]
  public static extern float CSharp_Ak3dData_spread_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_focus_set")]
  public static extern void CSharp_Ak3dData_focus_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_focus_get")]
  public static extern float CSharp_Ak3dData_focus_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_uEmitterChannelMask_set")]
  public static extern void CSharp_Ak3dData_uEmitterChannelMask_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3dData_uEmitterChannelMask_get")]
  public static extern uint CSharp_Ak3dData_uEmitterChannelMask_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_Ak3dData")]
  public static extern void CSharp_delete_Ak3dData(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkBehavioralPositioningData")]
  public static extern global::System.IntPtr CSharp_new_AkBehavioralPositioningData();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_center_set")]
  public static extern void CSharp_AkBehavioralPositioningData_center_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_center_get")]
  public static extern float CSharp_AkBehavioralPositioningData_center_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panLR_set")]
  public static extern void CSharp_AkBehavioralPositioningData_panLR_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panLR_get")]
  public static extern float CSharp_AkBehavioralPositioningData_panLR_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panBF_set")]
  public static extern void CSharp_AkBehavioralPositioningData_panBF_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panBF_get")]
  public static extern float CSharp_AkBehavioralPositioningData_panBF_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panDU_set")]
  public static extern void CSharp_AkBehavioralPositioningData_panDU_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panDU_get")]
  public static extern float CSharp_AkBehavioralPositioningData_panDU_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panSpatMix_set")]
  public static extern void CSharp_AkBehavioralPositioningData_panSpatMix_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panSpatMix_get")]
  public static extern float CSharp_AkBehavioralPositioningData_panSpatMix_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_spatMode_set")]
  public static extern void CSharp_AkBehavioralPositioningData_spatMode_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_spatMode_get")]
  public static extern int CSharp_AkBehavioralPositioningData_spatMode_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panType_set")]
  public static extern void CSharp_AkBehavioralPositioningData_panType_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_panType_get")]
  public static extern int CSharp_AkBehavioralPositioningData_panType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_enableHeightSpread_set")]
  public static extern void CSharp_AkBehavioralPositioningData_enableHeightSpread_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBehavioralPositioningData_enableHeightSpread_get")]
  public static extern bool CSharp_AkBehavioralPositioningData_enableHeightSpread_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkBehavioralPositioningData")]
  public static extern void CSharp_delete_AkBehavioralPositioningData(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningData_threeD_set")]
  public static extern void CSharp_AkPositioningData_threeD_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningData_threeD_get")]
  public static extern global::System.IntPtr CSharp_AkPositioningData_threeD_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningData_behavioral_set")]
  public static extern void CSharp_AkPositioningData_behavioral_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningData_behavioral_get")]
  public static extern global::System.IntPtr CSharp_AkPositioningData_behavioral_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkPositioningData")]
  public static extern global::System.IntPtr CSharp_new_AkPositioningData();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkPositioningData")]
  public static extern void CSharp_delete_AkPositioningData(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_channelConfig_set")]
  public static extern void CSharp_Ak3DAudioSinkCapabilities_channelConfig_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_channelConfig_get")]
  public static extern global::System.IntPtr CSharp_Ak3DAudioSinkCapabilities_channelConfig_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_uMaxSystemAudioObjects_set")]
  public static extern void CSharp_Ak3DAudioSinkCapabilities_uMaxSystemAudioObjects_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_uMaxSystemAudioObjects_get")]
  public static extern uint CSharp_Ak3DAudioSinkCapabilities_uMaxSystemAudioObjects_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_uAvailableSystemAudioObjects_set")]
  public static extern void CSharp_Ak3DAudioSinkCapabilities_uAvailableSystemAudioObjects_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_uAvailableSystemAudioObjects_get")]
  public static extern uint CSharp_Ak3DAudioSinkCapabilities_uAvailableSystemAudioObjects_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_bPassthrough_set")]
  public static extern void CSharp_Ak3DAudioSinkCapabilities_bPassthrough_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_bPassthrough_get")]
  public static extern bool CSharp_Ak3DAudioSinkCapabilities_bPassthrough_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_bMultiChannelObjects_set")]
  public static extern void CSharp_Ak3DAudioSinkCapabilities_bMultiChannelObjects_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Ak3DAudioSinkCapabilities_bMultiChannelObjects_get")]
  public static extern bool CSharp_Ak3DAudioSinkCapabilities_bMultiChannelObjects_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_Ak3DAudioSinkCapabilities")]
  public static extern global::System.IntPtr CSharp_new_Ak3DAudioSinkCapabilities();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_Ak3DAudioSinkCapabilities")]
  public static extern void CSharp_delete_Ak3DAudioSinkCapabilities(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_pItem_set")]
  public static extern void CSharp_AkIterator_pItem_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_pItem_get")]
  public static extern global::System.IntPtr CSharp_AkIterator_pItem_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_NextIter")]
  public static extern global::System.IntPtr CSharp_AkIterator_NextIter(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_PrevIter")]
  public static extern global::System.IntPtr CSharp_AkIterator_PrevIter(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_GetItem")]
  public static extern global::System.IntPtr CSharp_AkIterator_GetItem(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_IsEqualTo")]
  public static extern bool CSharp_AkIterator_IsEqualTo(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkIterator_IsDifferentFrom")]
  public static extern bool CSharp_AkIterator_IsDifferentFrom(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkIterator")]
  public static extern global::System.IntPtr CSharp_new_AkIterator();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkIterator")]
  public static extern void CSharp_delete_AkIterator(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkStdMovePolicy_IsTrivial")]
  public static extern bool CSharp_AkStdMovePolicy_IsTrivial();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkStdMovePolicy")]
  public static extern global::System.IntPtr CSharp_new_AkStdMovePolicy();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkStdMovePolicy")]
  public static extern void CSharp_delete_AkStdMovePolicy(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTrivialStdMovePolicy_IsTrivial")]
  public static extern bool CSharp_AkTrivialStdMovePolicy_IsTrivial();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkTrivialStdMovePolicy")]
  public static extern global::System.IntPtr CSharp_new_AkTrivialStdMovePolicy();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkTrivialStdMovePolicy")]
  public static extern void CSharp_delete_AkTrivialStdMovePolicy(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkPlaylistItem__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkPlaylistItem__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkPlaylistItem__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkPlaylistItem__SWIG_1(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkPlaylistItem")]
  public static extern void CSharp_delete_AkPlaylistItem(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_Assign")]
  public static extern global::System.IntPtr CSharp_AkPlaylistItem_Assign(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_IsEqualTo")]
  public static extern bool CSharp_AkPlaylistItem_IsEqualTo(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_SetExternalSources")]
  public static extern int CSharp_AkPlaylistItem_SetExternalSources(global::System.IntPtr jarg1, uint jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_audioNodeID_set")]
  public static extern void CSharp_AkPlaylistItem_audioNodeID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_audioNodeID_get")]
  public static extern uint CSharp_AkPlaylistItem_audioNodeID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_msDelay_set")]
  public static extern void CSharp_AkPlaylistItem_msDelay_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_msDelay_get")]
  public static extern int CSharp_AkPlaylistItem_msDelay_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_pCustomInfo_set")]
  public static extern void CSharp_AkPlaylistItem_pCustomInfo_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistItem_pCustomInfo_get")]
  public static extern global::System.IntPtr CSharp_AkPlaylistItem_pCustomInfo_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkPlaylistArray")]
  public static extern global::System.IntPtr CSharp_new_AkPlaylistArray();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkPlaylistArray")]
  public static extern void CSharp_delete_AkPlaylistArray(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Begin")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Begin(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_End")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_End(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_FindEx")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_FindEx(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Erase__SWIG_0")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Erase__SWIG_0(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Erase__SWIG_1")]
  public static extern void CSharp_AkPlaylistArray_Erase__SWIG_1(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_EraseSwap__SWIG_0")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_EraseSwap__SWIG_0(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_EraseSwap__SWIG_1")]
  public static extern void CSharp_AkPlaylistArray_EraseSwap__SWIG_1(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_IsGrowingAllowed")]
  public static extern bool CSharp_AkPlaylistArray_IsGrowingAllowed(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Reserve")]
  public static extern int CSharp_AkPlaylistArray_Reserve(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_ReserveExtra")]
  public static extern int CSharp_AkPlaylistArray_ReserveExtra(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Reserved")]
  public static extern uint CSharp_AkPlaylistArray_Reserved(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Term")]
  public static extern void CSharp_AkPlaylistArray_Term(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Length")]
  public static extern uint CSharp_AkPlaylistArray_Length(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Data")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Data(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_IsEmpty")]
  public static extern bool CSharp_AkPlaylistArray_IsEmpty(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Exists")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Exists(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_AddLast__SWIG_0")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_AddLast__SWIG_0(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_AddLast__SWIG_1")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_AddLast__SWIG_1(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Last")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Last(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_RemoveLast")]
  public static extern void CSharp_AkPlaylistArray_RemoveLast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Remove")]
  public static extern int CSharp_AkPlaylistArray_Remove(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_RemoveSwap")]
  public static extern int CSharp_AkPlaylistArray_RemoveSwap(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_RemoveAll")]
  public static extern void CSharp_AkPlaylistArray_RemoveAll(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_ItemAtIndex")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_ItemAtIndex(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Insert__SWIG_0")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Insert__SWIG_0(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Insert__SWIG_1")]
  public static extern global::System.IntPtr CSharp_AkPlaylistArray_Insert__SWIG_1(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_GrowArray__SWIG_0")]
  public static extern bool CSharp_AkPlaylistArray_GrowArray__SWIG_0(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_GrowArray__SWIG_1")]
  public static extern bool CSharp_AkPlaylistArray_GrowArray__SWIG_1(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Resize")]
  public static extern bool CSharp_AkPlaylistArray_Resize(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Transfer")]
  public static extern void CSharp_AkPlaylistArray_Transfer(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylistArray_Copy")]
  public static extern int CSharp_AkPlaylistArray_Copy(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylist_Enqueue__SWIG_0")]
  public static extern int CSharp_AkPlaylist_Enqueue__SWIG_0(global::System.IntPtr jarg1, uint jarg2, int jarg3, global::System.IntPtr jarg4, uint jarg5, global::System.IntPtr jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylist_Enqueue__SWIG_1")]
  public static extern int CSharp_AkPlaylist_Enqueue__SWIG_1(global::System.IntPtr jarg1, uint jarg2, int jarg3, global::System.IntPtr jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylist_Enqueue__SWIG_2")]
  public static extern int CSharp_AkPlaylist_Enqueue__SWIG_2(global::System.IntPtr jarg1, uint jarg2, int jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylist_Enqueue__SWIG_3")]
  public static extern int CSharp_AkPlaylist_Enqueue__SWIG_3(global::System.IntPtr jarg1, uint jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylist_Enqueue__SWIG_4")]
  public static extern int CSharp_AkPlaylist_Enqueue__SWIG_4(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkPlaylist")]
  public static extern global::System.IntPtr CSharp_new_AkPlaylist();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkPlaylist")]
  public static extern void CSharp_delete_AkPlaylist(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceOpen__SWIG_0")]
  public static extern uint CSharp_DynamicSequenceOpen__SWIG_0(ulong jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceOpen__SWIG_1")]
  public static extern uint CSharp_DynamicSequenceOpen__SWIG_1(ulong jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceOpen__SWIG_2")]
  public static extern uint CSharp_DynamicSequenceOpen__SWIG_2(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceClose")]
  public static extern int CSharp_DynamicSequenceClose(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequencePlay__SWIG_0")]
  public static extern int CSharp_DynamicSequencePlay__SWIG_0(uint jarg1, int jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequencePlay__SWIG_1")]
  public static extern int CSharp_DynamicSequencePlay__SWIG_1(uint jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequencePlay__SWIG_2")]
  public static extern int CSharp_DynamicSequencePlay__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequencePause__SWIG_0")]
  public static extern int CSharp_DynamicSequencePause__SWIG_0(uint jarg1, int jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequencePause__SWIG_1")]
  public static extern int CSharp_DynamicSequencePause__SWIG_1(uint jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequencePause__SWIG_2")]
  public static extern int CSharp_DynamicSequencePause__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceResume__SWIG_0")]
  public static extern int CSharp_DynamicSequenceResume__SWIG_0(uint jarg1, int jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceResume__SWIG_1")]
  public static extern int CSharp_DynamicSequenceResume__SWIG_1(uint jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceResume__SWIG_2")]
  public static extern int CSharp_DynamicSequenceResume__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceStop__SWIG_0")]
  public static extern int CSharp_DynamicSequenceStop__SWIG_0(uint jarg1, int jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceStop__SWIG_1")]
  public static extern int CSharp_DynamicSequenceStop__SWIG_1(uint jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceStop__SWIG_2")]
  public static extern int CSharp_DynamicSequenceStop__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceBreak")]
  public static extern int CSharp_DynamicSequenceBreak(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Seek__SWIG_0")]
  public static extern int CSharp_Seek__SWIG_0(uint jarg1, int jarg2, bool jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Seek__SWIG_1")]
  public static extern int CSharp_Seek__SWIG_1(uint jarg1, float jarg2, bool jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceGetPauseTimes")]
  public static extern int CSharp_DynamicSequenceGetPauseTimes(uint jarg1, out uint jarg2, out uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceLockPlaylist")]
  public static extern global::System.IntPtr CSharp_DynamicSequenceLockPlaylist(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_DynamicSequenceUnlockPlaylist")]
  public static extern int CSharp_DynamicSequenceUnlockPlaylist(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkOutputSettings__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkOutputSettings__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkOutputSettings__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkOutputSettings__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, global::System.IntPtr jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkOutputSettings__SWIG_2")]
  public static extern global::System.IntPtr CSharp_new_AkOutputSettings__SWIG_2([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkOutputSettings__SWIG_3")]
  public static extern global::System.IntPtr CSharp_new_AkOutputSettings__SWIG_3([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkOutputSettings__SWIG_4")]
  public static extern global::System.IntPtr CSharp_new_AkOutputSettings__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_audioDeviceShareset_set")]
  public static extern void CSharp_AkOutputSettings_audioDeviceShareset_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_audioDeviceShareset_get")]
  public static extern uint CSharp_AkOutputSettings_audioDeviceShareset_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_idDevice_set")]
  public static extern void CSharp_AkOutputSettings_idDevice_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_idDevice_get")]
  public static extern uint CSharp_AkOutputSettings_idDevice_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_ePanningRule_set")]
  public static extern void CSharp_AkOutputSettings_ePanningRule_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_ePanningRule_get")]
  public static extern int CSharp_AkOutputSettings_ePanningRule_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_channelConfig_set")]
  public static extern void CSharp_AkOutputSettings_channelConfig_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkOutputSettings_channelConfig_get")]
  public static extern global::System.IntPtr CSharp_AkOutputSettings_channelConfig_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkOutputSettings")]
  public static extern void CSharp_delete_AkOutputSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uMaxNumPaths_set")]
  public static extern void CSharp_AkInitSettings_uMaxNumPaths_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uMaxNumPaths_get")]
  public static extern uint CSharp_AkInitSettings_uMaxNumPaths_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uCommandQueueSize_set")]
  public static extern void CSharp_AkInitSettings_uCommandQueueSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uCommandQueueSize_get")]
  public static extern uint CSharp_AkInitSettings_uCommandQueueSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bEnableGameSyncPreparation_set")]
  public static extern void CSharp_AkInitSettings_bEnableGameSyncPreparation_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bEnableGameSyncPreparation_get")]
  public static extern bool CSharp_AkInitSettings_bEnableGameSyncPreparation_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uContinuousPlaybackLookAhead_set")]
  public static extern void CSharp_AkInitSettings_uContinuousPlaybackLookAhead_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uContinuousPlaybackLookAhead_get")]
  public static extern uint CSharp_AkInitSettings_uContinuousPlaybackLookAhead_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uNumSamplesPerFrame_set")]
  public static extern void CSharp_AkInitSettings_uNumSamplesPerFrame_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uNumSamplesPerFrame_get")]
  public static extern uint CSharp_AkInitSettings_uNumSamplesPerFrame_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uMonitorQueuePoolSize_set")]
  public static extern void CSharp_AkInitSettings_uMonitorQueuePoolSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uMonitorQueuePoolSize_get")]
  public static extern uint CSharp_AkInitSettings_uMonitorQueuePoolSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uCpuMonitorQueueMaxSize_set")]
  public static extern void CSharp_AkInitSettings_uCpuMonitorQueueMaxSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uCpuMonitorQueueMaxSize_get")]
  public static extern uint CSharp_AkInitSettings_uCpuMonitorQueueMaxSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_settingsMainOutput_set")]
  public static extern void CSharp_AkInitSettings_settingsMainOutput_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_settingsMainOutput_get")]
  public static extern global::System.IntPtr CSharp_AkInitSettings_settingsMainOutput_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uMaxHardwareTimeoutMs_set")]
  public static extern void CSharp_AkInitSettings_uMaxHardwareTimeoutMs_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uMaxHardwareTimeoutMs_get")]
  public static extern uint CSharp_AkInitSettings_uMaxHardwareTimeoutMs_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bUseSoundBankMgrThread_set")]
  public static extern void CSharp_AkInitSettings_bUseSoundBankMgrThread_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bUseSoundBankMgrThread_get")]
  public static extern bool CSharp_AkInitSettings_bUseSoundBankMgrThread_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bUseLEngineThread_set")]
  public static extern void CSharp_AkInitSettings_bUseLEngineThread_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bUseLEngineThread_get")]
  public static extern bool CSharp_AkInitSettings_bUseLEngineThread_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_szPluginDLLPath_set")]
  public static extern void CSharp_AkInitSettings_szPluginDLLPath_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_szPluginDLLPath_get")]
  public static extern global::System.IntPtr CSharp_AkInitSettings_szPluginDLLPath_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_eFloorPlane_set")]
  public static extern void CSharp_AkInitSettings_eFloorPlane_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_eFloorPlane_get")]
  public static extern int CSharp_AkInitSettings_eFloorPlane_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_fGameUnitsToMeters_set")]
  public static extern void CSharp_AkInitSettings_fGameUnitsToMeters_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_fGameUnitsToMeters_get")]
  public static extern float CSharp_AkInitSettings_fGameUnitsToMeters_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uBankReadBufferSize_set")]
  public static extern void CSharp_AkInitSettings_uBankReadBufferSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_uBankReadBufferSize_get")]
  public static extern uint CSharp_AkInitSettings_uBankReadBufferSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_fDebugOutOfRangeLimit_set")]
  public static extern void CSharp_AkInitSettings_fDebugOutOfRangeLimit_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_fDebugOutOfRangeLimit_get")]
  public static extern float CSharp_AkInitSettings_fDebugOutOfRangeLimit_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bDebugOutOfRangeCheckEnabled_set")]
  public static extern void CSharp_AkInitSettings_bDebugOutOfRangeCheckEnabled_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bDebugOutOfRangeCheckEnabled_get")]
  public static extern bool CSharp_AkInitSettings_bDebugOutOfRangeCheckEnabled_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bOfflineRendering_set")]
  public static extern void CSharp_AkInitSettings_bOfflineRendering_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitSettings_bOfflineRendering_get")]
  public static extern bool CSharp_AkInitSettings_bOfflineRendering_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkInitSettings")]
  public static extern void CSharp_delete_AkInitSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_sourceID_set")]
  public static extern void CSharp_AkSourceSettings_sourceID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_sourceID_get")]
  public static extern uint CSharp_AkSourceSettings_sourceID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_pMediaMemory_set")]
  public static extern void CSharp_AkSourceSettings_pMediaMemory_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_pMediaMemory_get")]
  public static extern global::System.IntPtr CSharp_AkSourceSettings_pMediaMemory_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_uMediaSize_set")]
  public static extern void CSharp_AkSourceSettings_uMediaSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_uMediaSize_get")]
  public static extern uint CSharp_AkSourceSettings_uMediaSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_Clear")]
  public static extern void CSharp_AkSourceSettings_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_GetSizeOf")]
  public static extern int CSharp_AkSourceSettings_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSourceSettings_Clone")]
  public static extern void CSharp_AkSourceSettings_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkSourceSettings")]
  public static extern global::System.IntPtr CSharp_new_AkSourceSettings();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkSourceSettings")]
  public static extern void CSharp_delete_AkSourceSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_IsInitialized")]
  public static extern bool CSharp_IsInitialized();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetAudioSettings")]
  public static extern int CSharp_GetAudioSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSpeakerConfiguration__SWIG_0")]
  public static extern global::System.IntPtr CSharp_GetSpeakerConfiguration__SWIG_0(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSpeakerConfiguration__SWIG_1")]
  public static extern global::System.IntPtr CSharp_GetSpeakerConfiguration__SWIG_1();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetOutputDeviceConfiguration")]
  public static extern int CSharp_GetOutputDeviceConfiguration(ulong jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPanningRule__SWIG_0")]
  public static extern int CSharp_GetPanningRule__SWIG_0(out int jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPanningRule__SWIG_1")]
  public static extern int CSharp_GetPanningRule__SWIG_1(out int jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetPanningRule__SWIG_0")]
  public static extern int CSharp_SetPanningRule__SWIG_0(int jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetPanningRule__SWIG_1")]
  public static extern int CSharp_SetPanningRule__SWIG_1(int jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSpeakerAngles__SWIG_0")]
  public static extern int CSharp_GetSpeakerAngles__SWIG_0([global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg1, ref uint jarg2, out float jarg3, ulong jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSpeakerAngles__SWIG_1")]
  public static extern int CSharp_GetSpeakerAngles__SWIG_1([global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg1, ref uint jarg2, out float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSpeakerAngles__SWIG_0")]
  public static extern int CSharp_SetSpeakerAngles__SWIG_0([global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg1, uint jarg2, float jarg3, ulong jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSpeakerAngles__SWIG_1")]
  public static extern int CSharp_SetSpeakerAngles__SWIG_1([global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg1, uint jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSpeakerAngles__SWIG_2")]
  public static extern int CSharp_SetSpeakerAngles__SWIG_2([global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetVolumeThreshold")]
  public static extern int CSharp_SetVolumeThreshold(float jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMaxNumVoicesLimit")]
  public static extern int CSharp_SetMaxNumVoicesLimit(ushort jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetJobMgrMaxActiveWorkers")]
  public static extern int CSharp_SetJobMgrMaxActiveWorkers(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RenderAudio__SWIG_0")]
  public static extern int CSharp_RenderAudio__SWIG_0(bool jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RenderAudio__SWIG_1")]
  public static extern int CSharp_RenderAudio__SWIG_1();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RegisterPluginDLL__SWIG_0")]
  public static extern int CSharp_RegisterPluginDLL__SWIG_0([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RegisterPluginDLL__SWIG_1")]
  public static extern int CSharp_RegisterPluginDLL__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_IsPluginRegistered")]
  public static extern bool CSharp_IsPluginRegistered(int jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetIDFromString")]
  public static extern uint CSharp_GetIDFromString([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_0")]
  public static extern uint CSharp_PostEvent__SWIG_0(uint jarg1, ulong jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5, uint jarg6, global::System.IntPtr jarg7, uint jarg8);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_1")]
  public static extern uint CSharp_PostEvent__SWIG_1(uint jarg1, ulong jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5, uint jarg6, global::System.IntPtr jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_2")]
  public static extern uint CSharp_PostEvent__SWIG_2(uint jarg1, ulong jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_3")]
  public static extern uint CSharp_PostEvent__SWIG_3(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_4")]
  public static extern uint CSharp_PostEvent__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5, uint jarg6, global::System.IntPtr jarg7, uint jarg8);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_5")]
  public static extern uint CSharp_PostEvent__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5, uint jarg6, global::System.IntPtr jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_6")]
  public static extern uint CSharp_PostEvent__SWIG_6([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostEvent__SWIG_7")]
  public static extern uint CSharp_PostEvent__SWIG_7([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_0")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_0(uint jarg1, int jarg2, ulong jarg3, int jarg4, int jarg5, uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_1")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_1(uint jarg1, int jarg2, ulong jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_2")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_2(uint jarg1, int jarg2, ulong jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_3")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_3(uint jarg1, int jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_4")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_4(uint jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_5")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, ulong jarg3, int jarg4, int jarg5, uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_6")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_6([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, ulong jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_7")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_7([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, ulong jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_8")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_8([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnEvent__SWIG_9")]
  public static extern int CSharp_ExecuteActionOnEvent__SWIG_9([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostMIDIOnEvent__SWIG_0")]
  public static extern uint CSharp_PostMIDIOnEvent__SWIG_0(uint jarg1, ulong jarg2, global::System.IntPtr jarg3, ushort jarg4, bool jarg5, uint jarg6, global::System.IntPtr jarg7, global::System.IntPtr jarg8, uint jarg9);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostMIDIOnEvent__SWIG_1")]
  public static extern uint CSharp_PostMIDIOnEvent__SWIG_1(uint jarg1, ulong jarg2, global::System.IntPtr jarg3, ushort jarg4, bool jarg5, uint jarg6, global::System.IntPtr jarg7, global::System.IntPtr jarg8);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostMIDIOnEvent__SWIG_2")]
  public static extern uint CSharp_PostMIDIOnEvent__SWIG_2(uint jarg1, ulong jarg2, global::System.IntPtr jarg3, ushort jarg4, bool jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostMIDIOnEvent__SWIG_3")]
  public static extern uint CSharp_PostMIDIOnEvent__SWIG_3(uint jarg1, ulong jarg2, global::System.IntPtr jarg3, ushort jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopMIDIOnEvent__SWIG_0")]
  public static extern int CSharp_StopMIDIOnEvent__SWIG_0(uint jarg1, ulong jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopMIDIOnEvent__SWIG_1")]
  public static extern int CSharp_StopMIDIOnEvent__SWIG_1(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopMIDIOnEvent__SWIG_2")]
  public static extern int CSharp_StopMIDIOnEvent__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopMIDIOnEvent__SWIG_3")]
  public static extern int CSharp_StopMIDIOnEvent__SWIG_3();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PinEventInStreamCache__SWIG_0")]
  public static extern int CSharp_PinEventInStreamCache__SWIG_0(uint jarg1, sbyte jarg2, sbyte jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PinEventInStreamCache__SWIG_1")]
  public static extern int CSharp_PinEventInStreamCache__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, sbyte jarg2, sbyte jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnpinEventInStreamCache__SWIG_0")]
  public static extern int CSharp_UnpinEventInStreamCache__SWIG_0(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnpinEventInStreamCache__SWIG_1")]
  public static extern int CSharp_UnpinEventInStreamCache__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetBufferStatusForPinnedEvent__SWIG_0")]
  public static extern int CSharp_GetBufferStatusForPinnedEvent__SWIG_0(uint jarg1, out float jarg2, out int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetBufferStatusForPinnedEvent__SWIG_1")]
  public static extern int CSharp_GetBufferStatusForPinnedEvent__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, out float jarg2, out int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_0")]
  public static extern int CSharp_SeekOnEvent__SWIG_0(uint jarg1, ulong jarg2, int jarg3, bool jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_1")]
  public static extern int CSharp_SeekOnEvent__SWIG_1(uint jarg1, ulong jarg2, int jarg3, bool jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_2")]
  public static extern int CSharp_SeekOnEvent__SWIG_2(uint jarg1, ulong jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_3")]
  public static extern int CSharp_SeekOnEvent__SWIG_3([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, int jarg3, bool jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_4")]
  public static extern int CSharp_SeekOnEvent__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, int jarg3, bool jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_5")]
  public static extern int CSharp_SeekOnEvent__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_6")]
  public static extern int CSharp_SeekOnEvent__SWIG_6(uint jarg1, ulong jarg2, float jarg3, bool jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_7")]
  public static extern int CSharp_SeekOnEvent__SWIG_7(uint jarg1, ulong jarg2, float jarg3, bool jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_8")]
  public static extern int CSharp_SeekOnEvent__SWIG_8(uint jarg1, ulong jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_9")]
  public static extern int CSharp_SeekOnEvent__SWIG_9([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, float jarg3, bool jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_10")]
  public static extern int CSharp_SeekOnEvent__SWIG_10([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, float jarg3, bool jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SeekOnEvent__SWIG_11")]
  public static extern int CSharp_SeekOnEvent__SWIG_11([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_CancelEventCallbackCookie")]
  public static extern void CSharp_CancelEventCallbackCookie(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_CancelEventCallbackGameObject")]
  public static extern void CSharp_CancelEventCallbackGameObject(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_CancelEventCallback")]
  public static extern void CSharp_CancelEventCallback(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSourcePlayPosition__SWIG_0")]
  public static extern int CSharp_GetSourcePlayPosition__SWIG_0(uint jarg1, out int jarg2, bool jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSourcePlayPosition__SWIG_1")]
  public static extern int CSharp_GetSourcePlayPosition__SWIG_1(uint jarg1, out int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSourceStreamBuffering")]
  public static extern int CSharp_GetSourceStreamBuffering(uint jarg1, out int jarg2, out int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopAll__SWIG_0")]
  public static extern void CSharp_StopAll__SWIG_0(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopAll__SWIG_1")]
  public static extern void CSharp_StopAll__SWIG_1();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopPlayingID__SWIG_0")]
  public static extern void CSharp_StopPlayingID__SWIG_0(uint jarg1, int jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopPlayingID__SWIG_1")]
  public static extern void CSharp_StopPlayingID__SWIG_1(uint jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopPlayingID__SWIG_2")]
  public static extern void CSharp_StopPlayingID__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnPlayingID__SWIG_0")]
  public static extern void CSharp_ExecuteActionOnPlayingID__SWIG_0(int jarg1, uint jarg2, int jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnPlayingID__SWIG_1")]
  public static extern void CSharp_ExecuteActionOnPlayingID__SWIG_1(int jarg1, uint jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ExecuteActionOnPlayingID__SWIG_2")]
  public static extern void CSharp_ExecuteActionOnPlayingID__SWIG_2(int jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRandomSeed")]
  public static extern void CSharp_SetRandomSeed(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MuteBackgroundMusic")]
  public static extern void CSharp_MuteBackgroundMusic(bool jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetBackgroundMusicMute")]
  public static extern bool CSharp_GetBackgroundMusicMute();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SendPluginCustomGameData")]
  public static extern int CSharp_SendPluginCustomGameData(uint jarg1, ulong jarg2, int jarg3, uint jarg4, uint jarg5, global::System.IntPtr jarg6, uint jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnregisterAllGameObj")]
  public static extern int CSharp_UnregisterAllGameObj();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultiplePositions__SWIG_0")]
  public static extern int CSharp_SetMultiplePositions__SWIG_0(ulong jarg1, global::System.IntPtr jarg2, ushort jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultiplePositions__SWIG_1")]
  public static extern int CSharp_SetMultiplePositions__SWIG_1(ulong jarg1, global::System.IntPtr jarg2, ushort jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultiplePositions__SWIG_2")]
  public static extern int CSharp_SetMultiplePositions__SWIG_2(ulong jarg1, global::System.IntPtr jarg2, ushort jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultiplePositions__SWIG_3")]
  public static extern int CSharp_SetMultiplePositions__SWIG_3(ulong jarg1, global::System.IntPtr jarg2, ushort jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultiplePositions__SWIG_4")]
  public static extern int CSharp_SetMultiplePositions__SWIG_4(ulong jarg1, global::System.IntPtr jarg2, ushort jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultiplePositions__SWIG_5")]
  public static extern int CSharp_SetMultiplePositions__SWIG_5(ulong jarg1, global::System.IntPtr jarg2, ushort jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetScalingFactor")]
  public static extern int CSharp_SetScalingFactor(ulong jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetDistanceProbe")]
  public static extern int CSharp_SetDistanceProbe(ulong jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ClearBanks")]
  public static extern int CSharp_ClearBanks();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBankLoadIOSettings")]
  public static extern int CSharp_SetBankLoadIOSettings(float jarg1, sbyte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_0")]
  public static extern int CSharp_LoadBank__SWIG_0([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, out uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_1")]
  public static extern int CSharp_LoadBank__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, out uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_2")]
  public static extern int CSharp_LoadBank__SWIG_2(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_3")]
  public static extern int CSharp_LoadBank__SWIG_3(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryView__SWIG_0")]
  public static extern int CSharp_LoadBankMemoryView__SWIG_0(global::System.IntPtr jarg1, uint jarg2, out uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryView__SWIG_1")]
  public static extern int CSharp_LoadBankMemoryView__SWIG_1(global::System.IntPtr jarg1, uint jarg2, out uint jarg3, out uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryCopy__SWIG_0")]
  public static extern int CSharp_LoadBankMemoryCopy__SWIG_0(global::System.IntPtr jarg1, uint jarg2, out uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryCopy__SWIG_1")]
  public static extern int CSharp_LoadBankMemoryCopy__SWIG_1(global::System.IntPtr jarg1, uint jarg2, out uint jarg3, out uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_4")]
  public static extern int CSharp_LoadBank__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, out uint jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_5")]
  public static extern int CSharp_LoadBank__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, out uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_6")]
  public static extern int CSharp_LoadBank__SWIG_6(uint jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBank__SWIG_7")]
  public static extern int CSharp_LoadBank__SWIG_7(uint jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryView__SWIG_2")]
  public static extern int CSharp_LoadBankMemoryView__SWIG_2(global::System.IntPtr jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, out uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryView__SWIG_3")]
  public static extern int CSharp_LoadBankMemoryView__SWIG_3(global::System.IntPtr jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, out uint jarg5, out uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryCopy__SWIG_2")]
  public static extern int CSharp_LoadBankMemoryCopy__SWIG_2(global::System.IntPtr jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, out uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadBankMemoryCopy__SWIG_3")]
  public static extern int CSharp_LoadBankMemoryCopy__SWIG_3(global::System.IntPtr jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, out uint jarg5, out uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_0")]
  public static extern int CSharp_UnloadBank__SWIG_0([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_1")]
  public static extern int CSharp_UnloadBank__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_2")]
  public static extern int CSharp_UnloadBank__SWIG_2(uint jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_3")]
  public static extern int CSharp_UnloadBank__SWIG_3(uint jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_4")]
  public static extern int CSharp_UnloadBank__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_5")]
  public static extern int CSharp_UnloadBank__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_6")]
  public static extern int CSharp_UnloadBank__SWIG_6(uint jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadBank__SWIG_7")]
  public static extern int CSharp_UnloadBank__SWIG_7(uint jarg1, global::System.IntPtr jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_CancelBankCallbackCookie")]
  public static extern void CSharp_CancelBankCallbackCookie(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_0")]
  public static extern int CSharp_PrepareBank__SWIG_0(int jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, int jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_1")]
  public static extern int CSharp_PrepareBank__SWIG_1(int jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_2")]
  public static extern int CSharp_PrepareBank__SWIG_2(int jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_3")]
  public static extern int CSharp_PrepareBank__SWIG_3(int jarg1, uint jarg2, int jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_4")]
  public static extern int CSharp_PrepareBank__SWIG_4(int jarg1, uint jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_5")]
  public static extern int CSharp_PrepareBank__SWIG_5(int jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_6")]
  public static extern int CSharp_PrepareBank__SWIG_6(int jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, int jarg5, uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_7")]
  public static extern int CSharp_PrepareBank__SWIG_7(int jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_8")]
  public static extern int CSharp_PrepareBank__SWIG_8(int jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_9")]
  public static extern int CSharp_PrepareBank__SWIG_9(int jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, int jarg5, uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_10")]
  public static extern int CSharp_PrepareBank__SWIG_10(int jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBank__SWIG_11")]
  public static extern int CSharp_PrepareBank__SWIG_11(int jarg1, uint jarg2, global::System.IntPtr jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ClearPreparedEvents")]
  public static extern int CSharp_ClearPreparedEvents();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareEvent__SWIG_0")]
  public static extern int CSharp_PrepareEvent__SWIG_0(int jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareEvent__SWIG_1")]
  public static extern int CSharp_PrepareEvent__SWIG_1(int jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareEvent__SWIG_2")]
  public static extern int CSharp_PrepareEvent__SWIG_2(int jarg1, global::System.IntPtr jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareEvent__SWIG_3")]
  public static extern int CSharp_PrepareEvent__SWIG_3(int jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBus__SWIG_0")]
  public static extern int CSharp_PrepareBus__SWIG_0(int jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBus__SWIG_1")]
  public static extern int CSharp_PrepareBus__SWIG_1(int jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBus__SWIG_2")]
  public static extern int CSharp_PrepareBus__SWIG_2(int jarg1, global::System.IntPtr jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareBus__SWIG_3")]
  public static extern int CSharp_PrepareBus__SWIG_3(int jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, uint jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMedia")]
  public static extern int CSharp_SetMedia(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareGameSyncs__SWIG_0")]
  public static extern int CSharp_PrepareGameSyncs__SWIG_0(int jarg1, int jarg2, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg3, global::System.IntPtr jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareGameSyncs__SWIG_1")]
  public static extern int CSharp_PrepareGameSyncs__SWIG_1(int jarg1, int jarg2, uint jarg3, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareGameSyncs__SWIG_2")]
  public static extern int CSharp_PrepareGameSyncs__SWIG_2(int jarg1, int jarg2, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg3, global::System.IntPtr jarg4, uint jarg5, global::System.IntPtr jarg6, global::System.IntPtr jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PrepareGameSyncs__SWIG_3")]
  public static extern int CSharp_PrepareGameSyncs__SWIG_3(int jarg1, int jarg2, uint jarg3, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg4, uint jarg5, global::System.IntPtr jarg6, global::System.IntPtr jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddListener")]
  public static extern int CSharp_AddListener(ulong jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveListener")]
  public static extern int CSharp_RemoveListener(ulong jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddDefaultListener")]
  public static extern int CSharp_AddDefaultListener(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveDefaultListener")]
  public static extern int CSharp_RemoveDefaultListener(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetListenersToDefault")]
  public static extern int CSharp_ResetListenersToDefault(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetListenerSpatialization__SWIG_0")]
  public static extern int CSharp_SetListenerSpatialization__SWIG_0(ulong jarg1, bool jarg2, global::System.IntPtr jarg3, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetListenerSpatialization__SWIG_1")]
  public static extern int CSharp_SetListenerSpatialization__SWIG_1(ulong jarg1, bool jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_0")]
  public static extern int CSharp_SetRTPCValue__SWIG_0(uint jarg1, float jarg2, ulong jarg3, int jarg4, int jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_1")]
  public static extern int CSharp_SetRTPCValue__SWIG_1(uint jarg1, float jarg2, ulong jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_2")]
  public static extern int CSharp_SetRTPCValue__SWIG_2(uint jarg1, float jarg2, ulong jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_3")]
  public static extern int CSharp_SetRTPCValue__SWIG_3(uint jarg1, float jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_4")]
  public static extern int CSharp_SetRTPCValue__SWIG_4(uint jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_5")]
  public static extern int CSharp_SetRTPCValue__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, ulong jarg3, int jarg4, int jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_6")]
  public static extern int CSharp_SetRTPCValue__SWIG_6([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, ulong jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_7")]
  public static extern int CSharp_SetRTPCValue__SWIG_7([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, ulong jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_8")]
  public static extern int CSharp_SetRTPCValue__SWIG_8([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValue__SWIG_9")]
  public static extern int CSharp_SetRTPCValue__SWIG_9([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_0")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_0(uint jarg1, float jarg2, uint jarg3, int jarg4, int jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_1")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_1(uint jarg1, float jarg2, uint jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_2")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_2(uint jarg1, float jarg2, uint jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_3")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_3(uint jarg1, float jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_4")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, uint jarg3, int jarg4, int jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_5")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, uint jarg3, int jarg4, int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_6")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_6([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, uint jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRTPCValueByPlayingID__SWIG_7")]
  public static extern int CSharp_SetRTPCValueByPlayingID__SWIG_7([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, float jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_0")]
  public static extern int CSharp_ResetRTPCValue__SWIG_0(uint jarg1, ulong jarg2, int jarg3, int jarg4, bool jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_1")]
  public static extern int CSharp_ResetRTPCValue__SWIG_1(uint jarg1, ulong jarg2, int jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_2")]
  public static extern int CSharp_ResetRTPCValue__SWIG_2(uint jarg1, ulong jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_3")]
  public static extern int CSharp_ResetRTPCValue__SWIG_3(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_4")]
  public static extern int CSharp_ResetRTPCValue__SWIG_4(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_5")]
  public static extern int CSharp_ResetRTPCValue__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, int jarg3, int jarg4, bool jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_6")]
  public static extern int CSharp_ResetRTPCValue__SWIG_6([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, int jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_7")]
  public static extern int CSharp_ResetRTPCValue__SWIG_7([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_8")]
  public static extern int CSharp_ResetRTPCValue__SWIG_8([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValue__SWIG_9")]
  public static extern int CSharp_ResetRTPCValue__SWIG_9([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_0")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_0(uint jarg1, uint jarg2, int jarg3, int jarg4, bool jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_1")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_1(uint jarg1, uint jarg2, int jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_2")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_2(uint jarg1, uint jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_3")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_3(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_4")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, int jarg3, int jarg4, bool jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_5")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_5([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, int jarg3, int jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_6")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_6([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetRTPCValueByPlayingID__SWIG_7")]
  public static extern int CSharp_ResetRTPCValueByPlayingID__SWIG_7([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSwitch__SWIG_0")]
  public static extern int CSharp_SetSwitch__SWIG_0(uint jarg1, uint jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSwitch__SWIG_1")]
  public static extern int CSharp_SetSwitch__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostTrigger__SWIG_0")]
  public static extern int CSharp_PostTrigger__SWIG_0(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostTrigger__SWIG_1")]
  public static extern int CSharp_PostTrigger__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetState__SWIG_0")]
  public static extern int CSharp_SetState__SWIG_0(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetState__SWIG_1")]
  public static extern int CSharp_SetState__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGameObjectAuxSendValues")]
  public static extern int CSharp_SetGameObjectAuxSendValues(ulong jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGameObjectOutputBusVolume")]
  public static extern int CSharp_SetGameObjectOutputBusVolume(ulong jarg1, ulong jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetActorMixerEffect")]
  public static extern int CSharp_SetActorMixerEffect(uint jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBusEffect__SWIG_0")]
  public static extern int CSharp_SetBusEffect__SWIG_0(uint jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBusEffect__SWIG_1")]
  public static extern int CSharp_SetBusEffect__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetOutputDeviceEffect")]
  public static extern int CSharp_SetOutputDeviceEffect(ulong jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBusConfig__SWIG_0")]
  public static extern int CSharp_SetBusConfig__SWIG_0(uint jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBusConfig__SWIG_1")]
  public static extern int CSharp_SetBusConfig__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetObjectObstructionAndOcclusion")]
  public static extern int CSharp_SetObjectObstructionAndOcclusion(ulong jarg1, ulong jarg2, float jarg3, float jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMultipleObstructionAndOcclusion")]
  public static extern int CSharp_SetMultipleObstructionAndOcclusion(ulong jarg1, ulong jarg2, global::System.IntPtr jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StartOutputCapture")]
  public static extern int CSharp_StartOutputCapture([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopOutputCapture")]
  public static extern int CSharp_StopOutputCapture();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddOutputCaptureMarker")]
  public static extern int CSharp_AddOutputCaptureMarker([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddOutputCaptureBinaryMarker")]
  public static extern int CSharp_AddOutputCaptureBinaryMarker(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSampleRate")]
  public static extern uint CSharp_GetSampleRate();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StartProfilerCapture")]
  public static extern int CSharp_StartProfilerCapture([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopProfilerCapture")]
  public static extern int CSharp_StopProfilerCapture();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetOfflineRenderingFrameTime")]
  public static extern int CSharp_SetOfflineRenderingFrameTime(float jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetOfflineRendering")]
  public static extern int CSharp_SetOfflineRendering(bool jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveOutput")]
  public static extern int CSharp_RemoveOutput(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ReplaceOutput__SWIG_0")]
  public static extern int CSharp_ReplaceOutput__SWIG_0(global::System.IntPtr jarg1, ulong jarg2, out ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ReplaceOutput__SWIG_1")]
  public static extern int CSharp_ReplaceOutput__SWIG_1(global::System.IntPtr jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetOutputID__SWIG_0")]
  public static extern ulong CSharp_GetOutputID__SWIG_0(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetOutputID__SWIG_1")]
  public static extern ulong CSharp_GetOutputID__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBusDevice__SWIG_0")]
  public static extern int CSharp_SetBusDevice__SWIG_0(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBusDevice__SWIG_1")]
  public static extern int CSharp_SetBusDevice__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDeviceList__SWIG_0")]
  public static extern int CSharp_GetDeviceList__SWIG_0(uint jarg1, uint jarg2, out uint jarg3, global::System.IntPtr jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDeviceList__SWIG_1")]
  public static extern int CSharp_GetDeviceList__SWIG_1(uint jarg1, out uint jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetOutputVolume")]
  public static extern int CSharp_SetOutputVolume(ulong jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDeviceSpatialAudioSupport")]
  public static extern int CSharp_GetDeviceSpatialAudioSupport(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Suspend__SWIG_0")]
  public static extern int CSharp_Suspend__SWIG_0(bool jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Suspend__SWIG_1")]
  public static extern int CSharp_Suspend__SWIG_1(bool jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Suspend__SWIG_2")]
  public static extern int CSharp_Suspend__SWIG_2();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_WakeupFromSuspend__SWIG_0")]
  public static extern int CSharp_WakeupFromSuspend__SWIG_0(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_WakeupFromSuspend__SWIG_1")]
  public static extern int CSharp_WakeupFromSuspend__SWIG_1();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetBufferTick")]
  public static extern uint CSharp_GetBufferTick();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSampleTick")]
  public static extern ulong CSharp_GetSampleTick();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iCurrentPosition_set")]
  public static extern void CSharp_AkSegmentInfo_iCurrentPosition_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iCurrentPosition_get")]
  public static extern int CSharp_AkSegmentInfo_iCurrentPosition_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iPreEntryDuration_set")]
  public static extern void CSharp_AkSegmentInfo_iPreEntryDuration_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iPreEntryDuration_get")]
  public static extern int CSharp_AkSegmentInfo_iPreEntryDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iActiveDuration_set")]
  public static extern void CSharp_AkSegmentInfo_iActiveDuration_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iActiveDuration_get")]
  public static extern int CSharp_AkSegmentInfo_iActiveDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iPostExitDuration_set")]
  public static extern void CSharp_AkSegmentInfo_iPostExitDuration_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iPostExitDuration_get")]
  public static extern int CSharp_AkSegmentInfo_iPostExitDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iRemainingLookAheadTime_set")]
  public static extern void CSharp_AkSegmentInfo_iRemainingLookAheadTime_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_iRemainingLookAheadTime_get")]
  public static extern int CSharp_AkSegmentInfo_iRemainingLookAheadTime_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fBeatDuration_set")]
  public static extern void CSharp_AkSegmentInfo_fBeatDuration_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fBeatDuration_get")]
  public static extern float CSharp_AkSegmentInfo_fBeatDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fBarDuration_set")]
  public static extern void CSharp_AkSegmentInfo_fBarDuration_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fBarDuration_get")]
  public static extern float CSharp_AkSegmentInfo_fBarDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fGridDuration_set")]
  public static extern void CSharp_AkSegmentInfo_fGridDuration_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fGridDuration_get")]
  public static extern float CSharp_AkSegmentInfo_fGridDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fGridOffset_set")]
  public static extern void CSharp_AkSegmentInfo_fGridOffset_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSegmentInfo_fGridOffset_get")]
  public static extern float CSharp_AkSegmentInfo_fGridOffset_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkSegmentInfo")]
  public static extern global::System.IntPtr CSharp_new_AkSegmentInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkSegmentInfo")]
  public static extern void CSharp_delete_AkSegmentInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_totalCPU_set")]
  public static extern void CSharp_AkResourceMonitorDataSummary_totalCPU_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_totalCPU_get")]
  public static extern float CSharp_AkResourceMonitorDataSummary_totalCPU_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_pluginCPU_set")]
  public static extern void CSharp_AkResourceMonitorDataSummary_pluginCPU_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_pluginCPU_get")]
  public static extern float CSharp_AkResourceMonitorDataSummary_pluginCPU_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_physicalVoices_set")]
  public static extern void CSharp_AkResourceMonitorDataSummary_physicalVoices_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_physicalVoices_get")]
  public static extern uint CSharp_AkResourceMonitorDataSummary_physicalVoices_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_virtualVoices_set")]
  public static extern void CSharp_AkResourceMonitorDataSummary_virtualVoices_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_virtualVoices_get")]
  public static extern uint CSharp_AkResourceMonitorDataSummary_virtualVoices_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_totalVoices_set")]
  public static extern void CSharp_AkResourceMonitorDataSummary_totalVoices_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_totalVoices_get")]
  public static extern uint CSharp_AkResourceMonitorDataSummary_totalVoices_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_nbActiveEvents_set")]
  public static extern void CSharp_AkResourceMonitorDataSummary_nbActiveEvents_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkResourceMonitorDataSummary_nbActiveEvents_get")]
  public static extern uint CSharp_AkResourceMonitorDataSummary_nbActiveEvents_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkResourceMonitorDataSummary")]
  public static extern global::System.IntPtr CSharp_new_AkResourceMonitorDataSummary();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkResourceMonitorDataSummary")]
  public static extern void CSharp_delete_AkResourceMonitorDataSummary(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INVALID_MIDI_CHANNEL_get")]
  public static extern byte CSharp_AK_INVALID_MIDI_CHANNEL_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_INVALID_MIDI_NOTE_get")]
  public static extern byte CSharp_AK_INVALID_MIDI_NOTE_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byChan_set")]
  public static extern void CSharp_AkMIDIEvent_byChan_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byChan_get")]
  public static extern byte CSharp_AkMIDIEvent_byChan_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tGen_byParam1_set")]
  public static extern void CSharp_AkMIDIEvent_tGen_byParam1_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tGen_byParam1_get")]
  public static extern byte CSharp_AkMIDIEvent_tGen_byParam1_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tGen_byParam2_set")]
  public static extern void CSharp_AkMIDIEvent_tGen_byParam2_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tGen_byParam2_get")]
  public static extern byte CSharp_AkMIDIEvent_tGen_byParam2_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tGen")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tGen();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tGen")]
  public static extern void CSharp_delete_AkMIDIEvent_tGen(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteOnOff_byNote_set")]
  public static extern void CSharp_AkMIDIEvent_tNoteOnOff_byNote_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteOnOff_byNote_get")]
  public static extern byte CSharp_AkMIDIEvent_tNoteOnOff_byNote_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteOnOff_byVelocity_set")]
  public static extern void CSharp_AkMIDIEvent_tNoteOnOff_byVelocity_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteOnOff_byVelocity_get")]
  public static extern byte CSharp_AkMIDIEvent_tNoteOnOff_byVelocity_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tNoteOnOff")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tNoteOnOff();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tNoteOnOff")]
  public static extern void CSharp_delete_AkMIDIEvent_tNoteOnOff(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tCc_byCc_set")]
  public static extern void CSharp_AkMIDIEvent_tCc_byCc_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tCc_byCc_get")]
  public static extern byte CSharp_AkMIDIEvent_tCc_byCc_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tCc_byValue_set")]
  public static extern void CSharp_AkMIDIEvent_tCc_byValue_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tCc_byValue_get")]
  public static extern byte CSharp_AkMIDIEvent_tCc_byValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tCc")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tCc();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tCc")]
  public static extern void CSharp_delete_AkMIDIEvent_tCc(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tPitchBend_byValueLsb_set")]
  public static extern void CSharp_AkMIDIEvent_tPitchBend_byValueLsb_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tPitchBend_byValueLsb_get")]
  public static extern byte CSharp_AkMIDIEvent_tPitchBend_byValueLsb_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tPitchBend_byValueMsb_set")]
  public static extern void CSharp_AkMIDIEvent_tPitchBend_byValueMsb_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tPitchBend_byValueMsb_get")]
  public static extern byte CSharp_AkMIDIEvent_tPitchBend_byValueMsb_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tPitchBend")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tPitchBend();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tPitchBend")]
  public static extern void CSharp_delete_AkMIDIEvent_tPitchBend(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteAftertouch_byNote_set")]
  public static extern void CSharp_AkMIDIEvent_tNoteAftertouch_byNote_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteAftertouch_byNote_get")]
  public static extern byte CSharp_AkMIDIEvent_tNoteAftertouch_byNote_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteAftertouch_byValue_set")]
  public static extern void CSharp_AkMIDIEvent_tNoteAftertouch_byValue_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tNoteAftertouch_byValue_get")]
  public static extern byte CSharp_AkMIDIEvent_tNoteAftertouch_byValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tNoteAftertouch")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tNoteAftertouch();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tNoteAftertouch")]
  public static extern void CSharp_delete_AkMIDIEvent_tNoteAftertouch(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tChanAftertouch_byValue_set")]
  public static extern void CSharp_AkMIDIEvent_tChanAftertouch_byValue_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tChanAftertouch_byValue_get")]
  public static extern byte CSharp_AkMIDIEvent_tChanAftertouch_byValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tChanAftertouch")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tChanAftertouch();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tChanAftertouch")]
  public static extern void CSharp_delete_AkMIDIEvent_tChanAftertouch(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tProgramChange_byProgramNum_set")]
  public static extern void CSharp_AkMIDIEvent_tProgramChange_byProgramNum_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tProgramChange_byProgramNum_get")]
  public static extern byte CSharp_AkMIDIEvent_tProgramChange_byProgramNum_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tProgramChange")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tProgramChange();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tProgramChange")]
  public static extern void CSharp_delete_AkMIDIEvent_tProgramChange(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tWwiseCmd_uCmd_set")]
  public static extern void CSharp_AkMIDIEvent_tWwiseCmd_uCmd_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tWwiseCmd_uCmd_get")]
  public static extern ushort CSharp_AkMIDIEvent_tWwiseCmd_uCmd_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tWwiseCmd_uArg_set")]
  public static extern void CSharp_AkMIDIEvent_tWwiseCmd_uArg_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_tWwiseCmd_uArg_get")]
  public static extern uint CSharp_AkMIDIEvent_tWwiseCmd_uArg_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent_tWwiseCmd")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent_tWwiseCmd();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent_tWwiseCmd")]
  public static extern void CSharp_delete_AkMIDIEvent_tWwiseCmd(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_Gen_set")]
  public static extern void CSharp_AkMIDIEvent_Gen_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_Gen_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_Gen_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_Cc_set")]
  public static extern void CSharp_AkMIDIEvent_Cc_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_Cc_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_Cc_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_NoteOnOff_set")]
  public static extern void CSharp_AkMIDIEvent_NoteOnOff_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_NoteOnOff_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_NoteOnOff_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_PitchBend_set")]
  public static extern void CSharp_AkMIDIEvent_PitchBend_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_PitchBend_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_PitchBend_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_NoteAftertouch_set")]
  public static extern void CSharp_AkMIDIEvent_NoteAftertouch_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_NoteAftertouch_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_NoteAftertouch_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_ChanAftertouch_set")]
  public static extern void CSharp_AkMIDIEvent_ChanAftertouch_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_ChanAftertouch_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_ChanAftertouch_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_ProgramChange_set")]
  public static extern void CSharp_AkMIDIEvent_ProgramChange_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_ProgramChange_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_ProgramChange_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_WwiseCmd_set")]
  public static extern void CSharp_AkMIDIEvent_WwiseCmd_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_WwiseCmd_get")]
  public static extern global::System.IntPtr CSharp_AkMIDIEvent_WwiseCmd_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byType_set")]
  public static extern void CSharp_AkMIDIEvent_byType_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byType_get")]
  public static extern int CSharp_AkMIDIEvent_byType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byOnOffNote_set")]
  public static extern void CSharp_AkMIDIEvent_byOnOffNote_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byOnOffNote_get")]
  public static extern byte CSharp_AkMIDIEvent_byOnOffNote_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byVelocity_set")]
  public static extern void CSharp_AkMIDIEvent_byVelocity_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byVelocity_get")]
  public static extern byte CSharp_AkMIDIEvent_byVelocity_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byCc_set")]
  public static extern void CSharp_AkMIDIEvent_byCc_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byCc_get")]
  public static extern int CSharp_AkMIDIEvent_byCc_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byCcValue_set")]
  public static extern void CSharp_AkMIDIEvent_byCcValue_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byCcValue_get")]
  public static extern byte CSharp_AkMIDIEvent_byCcValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byValueLsb_set")]
  public static extern void CSharp_AkMIDIEvent_byValueLsb_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byValueLsb_get")]
  public static extern byte CSharp_AkMIDIEvent_byValueLsb_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byValueMsb_set")]
  public static extern void CSharp_AkMIDIEvent_byValueMsb_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byValueMsb_get")]
  public static extern byte CSharp_AkMIDIEvent_byValueMsb_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byAftertouchNote_set")]
  public static extern void CSharp_AkMIDIEvent_byAftertouchNote_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byAftertouchNote_get")]
  public static extern byte CSharp_AkMIDIEvent_byAftertouchNote_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byNoteAftertouchValue_set")]
  public static extern void CSharp_AkMIDIEvent_byNoteAftertouchValue_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byNoteAftertouchValue_get")]
  public static extern byte CSharp_AkMIDIEvent_byNoteAftertouchValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byChanAftertouchValue_set")]
  public static extern void CSharp_AkMIDIEvent_byChanAftertouchValue_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byChanAftertouchValue_get")]
  public static extern byte CSharp_AkMIDIEvent_byChanAftertouchValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byProgramNum_set")]
  public static extern void CSharp_AkMIDIEvent_byProgramNum_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_byProgramNum_get")]
  public static extern byte CSharp_AkMIDIEvent_byProgramNum_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_uCmd_set")]
  public static extern void CSharp_AkMIDIEvent_uCmd_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_uCmd_get")]
  public static extern ushort CSharp_AkMIDIEvent_uCmd_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_uArg_set")]
  public static extern void CSharp_AkMIDIEvent_uArg_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEvent_uArg_get")]
  public static extern uint CSharp_AkMIDIEvent_uArg_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEvent")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEvent();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEvent")]
  public static extern void CSharp_delete_AkMIDIEvent(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_uOffset_set")]
  public static extern void CSharp_AkMIDIPost_uOffset_set(global::System.IntPtr jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_uOffset_get")]
  public static extern ulong CSharp_AkMIDIPost_uOffset_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_PostOnEvent__SWIG_0")]
  public static extern uint CSharp_AkMIDIPost_PostOnEvent__SWIG_0(global::System.IntPtr jarg1, uint jarg2, ulong jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_PostOnEvent__SWIG_1")]
  public static extern uint CSharp_AkMIDIPost_PostOnEvent__SWIG_1(global::System.IntPtr jarg1, uint jarg2, ulong jarg3, uint jarg4, bool jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_PostOnEvent__SWIG_2")]
  public static extern uint CSharp_AkMIDIPost_PostOnEvent__SWIG_2(global::System.IntPtr jarg1, uint jarg2, ulong jarg3, uint jarg4, bool jarg5, uint jarg6, global::System.IntPtr jarg7, global::System.IntPtr jarg8);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_PostOnEvent__SWIG_3")]
  public static extern uint CSharp_AkMIDIPost_PostOnEvent__SWIG_3(global::System.IntPtr jarg1, uint jarg2, ulong jarg3, uint jarg4, bool jarg5, uint jarg6, global::System.IntPtr jarg7, global::System.IntPtr jarg8, uint jarg9);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_Clone")]
  public static extern void CSharp_AkMIDIPost_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_GetSizeOf")]
  public static extern int CSharp_AkMIDIPost_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIPost")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIPost();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIPost")]
  public static extern void CSharp_delete_AkMIDIPost(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSettings_fStreamingLookAheadRatio_set")]
  public static extern void CSharp_AkMusicSettings_fStreamingLookAheadRatio_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSettings_fStreamingLookAheadRatio_get")]
  public static extern float CSharp_AkMusicSettings_fStreamingLookAheadRatio_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMusicSettings")]
  public static extern void CSharp_delete_AkMusicSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPlayingSegmentInfo__SWIG_0")]
  public static extern int CSharp_GetPlayingSegmentInfo__SWIG_0(uint jarg1, global::System.IntPtr jarg2, bool jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPlayingSegmentInfo__SWIG_1")]
  public static extern int CSharp_GetPlayingSegmentInfo__SWIG_1(uint jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSerializedCallbackHeader_pPackage_get")]
  public static extern global::System.IntPtr CSharp_AkSerializedCallbackHeader_pPackage_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSerializedCallbackHeader_eType_get")]
  public static extern uint CSharp_AkSerializedCallbackHeader_eType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSerializedCallbackHeader_GetData")]
  public static extern global::System.IntPtr CSharp_AkSerializedCallbackHeader_GetData(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSerializedCallbackHeader_pNext_get")]
  public static extern global::System.IntPtr CSharp_AkSerializedCallbackHeader_pNext_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkSerializedCallbackHeader")]
  public static extern global::System.IntPtr CSharp_new_AkSerializedCallbackHeader();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkSerializedCallbackHeader")]
  public static extern void CSharp_delete_AkSerializedCallbackHeader(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackInfo_pCookie_get")]
  public static extern global::System.IntPtr CSharp_AkCallbackInfo_pCookie_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackInfo_gameObjID_get")]
  public static extern ulong CSharp_AkCallbackInfo_gameObjID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkCallbackInfo")]
  public static extern void CSharp_delete_AkCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkEventCallbackInfo_playingID_get")]
  public static extern uint CSharp_AkEventCallbackInfo_playingID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkEventCallbackInfo_eventID_get")]
  public static extern uint CSharp_AkEventCallbackInfo_eventID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkEventCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkEventCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkEventCallbackInfo")]
  public static extern void CSharp_delete_AkEventCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byChan_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byChan_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byParam1_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byParam1_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byParam2_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byParam2_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byType_get")]
  public static extern int CSharp_AkMIDIEventCallbackInfo_byType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byOnOffNote_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byOnOffNote_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byVelocity_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byVelocity_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byCc_get")]
  public static extern int CSharp_AkMIDIEventCallbackInfo_byCc_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byCcValue_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byCcValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byValueLsb_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byValueLsb_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byValueMsb_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byValueMsb_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byAftertouchNote_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byAftertouchNote_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byNoteAftertouchValue_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byNoteAftertouchValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byChanAftertouchValue_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byChanAftertouchValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_byProgramNum_get")]
  public static extern byte CSharp_AkMIDIEventCallbackInfo_byProgramNum_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMIDIEventCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkMIDIEventCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMIDIEventCallbackInfo")]
  public static extern void CSharp_delete_AkMIDIEventCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMarkerCallbackInfo_uIdentifier_get")]
  public static extern uint CSharp_AkMarkerCallbackInfo_uIdentifier_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMarkerCallbackInfo_uPosition_get")]
  public static extern uint CSharp_AkMarkerCallbackInfo_uPosition_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMarkerCallbackInfo_strLabel_get")]
  public static extern global::System.IntPtr CSharp_AkMarkerCallbackInfo_strLabel_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMarkerCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkMarkerCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMarkerCallbackInfo")]
  public static extern void CSharp_delete_AkMarkerCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDurationCallbackInfo_fDuration_get")]
  public static extern float CSharp_AkDurationCallbackInfo_fDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDurationCallbackInfo_fEstimatedDuration_get")]
  public static extern float CSharp_AkDurationCallbackInfo_fEstimatedDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDurationCallbackInfo_audioNodeID_get")]
  public static extern uint CSharp_AkDurationCallbackInfo_audioNodeID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDurationCallbackInfo_mediaID_get")]
  public static extern uint CSharp_AkDurationCallbackInfo_mediaID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDurationCallbackInfo_bStreaming_get")]
  public static extern bool CSharp_AkDurationCallbackInfo_bStreaming_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkDurationCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkDurationCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkDurationCallbackInfo")]
  public static extern void CSharp_delete_AkDurationCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDynamicSequenceItemCallbackInfo_playingID_get")]
  public static extern uint CSharp_AkDynamicSequenceItemCallbackInfo_playingID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDynamicSequenceItemCallbackInfo_audioNodeID_get")]
  public static extern uint CSharp_AkDynamicSequenceItemCallbackInfo_audioNodeID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDynamicSequenceItemCallbackInfo_pCustomInfo_get")]
  public static extern global::System.IntPtr CSharp_AkDynamicSequenceItemCallbackInfo_pCustomInfo_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkDynamicSequenceItemCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkDynamicSequenceItemCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkDynamicSequenceItemCallbackInfo")]
  public static extern void CSharp_delete_AkDynamicSequenceItemCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_playingID_get")]
  public static extern uint CSharp_AkMusicSyncCallbackInfo_playingID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_iCurrentPosition_get")]
  public static extern int CSharp_AkMusicSyncCallbackInfo_segmentInfo_iCurrentPosition_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_iPreEntryDuration_get")]
  public static extern int CSharp_AkMusicSyncCallbackInfo_segmentInfo_iPreEntryDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_iActiveDuration_get")]
  public static extern int CSharp_AkMusicSyncCallbackInfo_segmentInfo_iActiveDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_iPostExitDuration_get")]
  public static extern int CSharp_AkMusicSyncCallbackInfo_segmentInfo_iPostExitDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_iRemainingLookAheadTime_get")]
  public static extern int CSharp_AkMusicSyncCallbackInfo_segmentInfo_iRemainingLookAheadTime_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_fBeatDuration_get")]
  public static extern float CSharp_AkMusicSyncCallbackInfo_segmentInfo_fBeatDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_fBarDuration_get")]
  public static extern float CSharp_AkMusicSyncCallbackInfo_segmentInfo_fBarDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_fGridDuration_get")]
  public static extern float CSharp_AkMusicSyncCallbackInfo_segmentInfo_fGridDuration_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_segmentInfo_fGridOffset_get")]
  public static extern float CSharp_AkMusicSyncCallbackInfo_segmentInfo_fGridOffset_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_musicSyncType_get")]
  public static extern int CSharp_AkMusicSyncCallbackInfo_musicSyncType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_userCueName_get")]
  public static extern global::System.IntPtr CSharp_AkMusicSyncCallbackInfo_userCueName_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMusicSyncCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkMusicSyncCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMusicSyncCallbackInfo")]
  public static extern void CSharp_delete_AkMusicSyncCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicPlaylistCallbackInfo_playlistID_get")]
  public static extern uint CSharp_AkMusicPlaylistCallbackInfo_playlistID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicPlaylistCallbackInfo_uNumPlaylistItems_get")]
  public static extern uint CSharp_AkMusicPlaylistCallbackInfo_uNumPlaylistItems_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicPlaylistCallbackInfo_uPlaylistSelection_get")]
  public static extern uint CSharp_AkMusicPlaylistCallbackInfo_uPlaylistSelection_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicPlaylistCallbackInfo_uPlaylistItemDone_get")]
  public static extern uint CSharp_AkMusicPlaylistCallbackInfo_uPlaylistItemDone_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMusicPlaylistCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkMusicPlaylistCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMusicPlaylistCallbackInfo")]
  public static extern void CSharp_delete_AkMusicPlaylistCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBankCallbackInfo_bankID_get")]
  public static extern uint CSharp_AkBankCallbackInfo_bankID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBankCallbackInfo_inMemoryBankPtr_get")]
  public static extern global::System.IntPtr CSharp_AkBankCallbackInfo_inMemoryBankPtr_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkBankCallbackInfo_loadResult_get")]
  public static extern int CSharp_AkBankCallbackInfo_loadResult_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkBankCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkBankCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkBankCallbackInfo")]
  public static extern void CSharp_delete_AkBankCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMonitoringCallbackInfo_errorCode_get")]
  public static extern int CSharp_AkMonitoringCallbackInfo_errorCode_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMonitoringCallbackInfo_errorLevel_get")]
  public static extern int CSharp_AkMonitoringCallbackInfo_errorLevel_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMonitoringCallbackInfo_playingID_get")]
  public static extern uint CSharp_AkMonitoringCallbackInfo_playingID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMonitoringCallbackInfo_gameObjID_get")]
  public static extern ulong CSharp_AkMonitoringCallbackInfo_gameObjID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMonitoringCallbackInfo_message_get")]
  public static extern global::System.IntPtr CSharp_AkMonitoringCallbackInfo_message_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkMonitoringCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkMonitoringCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkMonitoringCallbackInfo")]
  public static extern void CSharp_delete_AkMonitoringCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioInterruptionCallbackInfo_bEnterInterruption_get")]
  public static extern bool CSharp_AkAudioInterruptionCallbackInfo_bEnterInterruption_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkAudioInterruptionCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkAudioInterruptionCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkAudioInterruptionCallbackInfo")]
  public static extern void CSharp_delete_AkAudioInterruptionCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAudioSourceChangeCallbackInfo_bOtherAudioPlaying_get")]
  public static extern bool CSharp_AkAudioSourceChangeCallbackInfo_bOtherAudioPlaying_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkAudioSourceChangeCallbackInfo")]
  public static extern global::System.IntPtr CSharp_new_AkAudioSourceChangeCallbackInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkAudioSourceChangeCallbackInfo")]
  public static extern void CSharp_delete_AkAudioSourceChangeCallbackInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_Init")]
  public static extern int CSharp_AkCallbackSerializer_Init();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_Term")]
  public static extern void CSharp_AkCallbackSerializer_Term();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_Lock")]
  public static extern global::System.IntPtr CSharp_AkCallbackSerializer_Lock();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_Unlock")]
  public static extern void CSharp_AkCallbackSerializer_Unlock();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_SetLocalOutput")]
  public static extern void CSharp_AkCallbackSerializer_SetLocalOutput(uint jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2, uint jarg3, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg4, uint jarg5, uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_FreeXmlTranslatorHandle")]
  public static extern void CSharp_AkCallbackSerializer_FreeXmlTranslatorHandle([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCallbackSerializer_AudioSourceChangeCallbackFunc")]
  public static extern int CSharp_AkCallbackSerializer_AudioSourceChangeCallbackFunc(bool jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkCallbackSerializer")]
  public static extern global::System.IntPtr CSharp_new_AkCallbackSerializer();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkCallbackSerializer")]
  public static extern void CSharp_delete_AkCallbackSerializer(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MsgContext__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_MsgContext__SWIG_0(uint jarg1, ulong jarg2, uint jarg3, bool jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MsgContext__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_MsgContext__SWIG_1(uint jarg1, ulong jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MsgContext__SWIG_2")]
  public static extern global::System.IntPtr CSharp_new_MsgContext__SWIG_2(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MsgContext__SWIG_3")]
  public static extern global::System.IntPtr CSharp_new_MsgContext__SWIG_3(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MsgContext__SWIG_4")]
  public static extern global::System.IntPtr CSharp_new_MsgContext__SWIG_4();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_playingID_set")]
  public static extern void CSharp_MsgContext_in_playingID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_playingID_get")]
  public static extern uint CSharp_MsgContext_in_playingID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_gameObjID_set")]
  public static extern void CSharp_MsgContext_in_gameObjID_set(global::System.IntPtr jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_gameObjID_get")]
  public static extern ulong CSharp_MsgContext_in_gameObjID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_soundID_set")]
  public static extern void CSharp_MsgContext_in_soundID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_soundID_get")]
  public static extern uint CSharp_MsgContext_in_soundID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_bIsBus_set")]
  public static extern void CSharp_MsgContext_in_bIsBus_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MsgContext_in_bIsBus_get")]
  public static extern bool CSharp_MsgContext_in_bIsBus_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_MsgContext")]
  public static extern void CSharp_delete_MsgContext(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MonitorErrorInfo__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_MonitorErrorInfo__SWIG_0([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MonitorErrorInfo__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_MonitorErrorInfo__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_MonitorErrorInfo__SWIG_2")]
  public static extern global::System.IntPtr CSharp_new_MonitorErrorInfo__SWIG_2();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorErrorInfo_m_name_set")]
  public static extern void CSharp_MonitorErrorInfo_m_name_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorErrorInfo_m_name_get")]
  public static extern global::System.IntPtr CSharp_MonitorErrorInfo_m_name_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorErrorInfo_m_message_set")]
  public static extern void CSharp_MonitorErrorInfo_m_message_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorErrorInfo_m_message_get")]
  public static extern global::System.IntPtr CSharp_MonitorErrorInfo_m_message_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_MonitorErrorInfo")]
  public static extern void CSharp_delete_MonitorErrorInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostCode__SWIG_0")]
  public static extern int CSharp_PostCode__SWIG_0(int jarg1, int jarg2, uint jarg3, ulong jarg4, uint jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostCode__SWIG_1")]
  public static extern int CSharp_PostCode__SWIG_1(int jarg1, int jarg2, uint jarg3, ulong jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostCode__SWIG_2")]
  public static extern int CSharp_PostCode__SWIG_2(int jarg1, int jarg2, uint jarg3, ulong jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostCode__SWIG_3")]
  public static extern int CSharp_PostCode__SWIG_3(int jarg1, int jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostCode__SWIG_4")]
  public static extern int CSharp_PostCode__SWIG_4(int jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostCodeVarArg")]
  public static extern int CSharp_PostCodeVarArg(int jarg1, int jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostString__SWIG_0")]
  public static extern int CSharp_PostString__SWIG_0([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, uint jarg3, ulong jarg4, uint jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostString__SWIG_1")]
  public static extern int CSharp_PostString__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, uint jarg3, ulong jarg4, uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostString__SWIG_2")]
  public static extern int CSharp_PostString__SWIG_2([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, uint jarg3, ulong jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostString__SWIG_3")]
  public static extern int CSharp_PostString__SWIG_3([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PostString__SWIG_4")]
  public static extern int CSharp_PostString__SWIG_4([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetTranslator")]
  public static extern int CSharp_ResetTranslator();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetTimeStamp")]
  public static extern int CSharp_GetTimeStamp();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorStreamMgrInit")]
  public static extern void CSharp_MonitorStreamMgrInit(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorStreamingDeviceInit")]
  public static extern void CSharp_MonitorStreamingDeviceInit(uint jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorStreamingDeviceDestroyed")]
  public static extern void CSharp_MonitorStreamingDeviceDestroyed(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_MonitorStreamMgrTerm")]
  public static extern void CSharp_MonitorStreamMgrTerm();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMemCpy")]
  public static extern void CSharp_AkMemCpy(global::System.IntPtr jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMemMove")]
  public static extern void CSharp_AkMemMove(global::System.IntPtr jarg1, global::System.IntPtr jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMemSet")]
  public static extern void CSharp_AkMemSet(global::System.IntPtr jarg1, int jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGetDefaultHighPriorityThreadProperties")]
  public static extern void CSharp_AkGetDefaultHighPriorityThreadProperties(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkForceCrash")]
  public static extern void CSharp_AkForceCrash();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResolveDialogueEvent__SWIG_0")]
  public static extern uint CSharp_ResolveDialogueEvent__SWIG_0(uint jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, uint jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResolveDialogueEvent__SWIG_1")]
  public static extern uint CSharp_ResolveDialogueEvent__SWIG_1(uint jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDialogueEventCustomPropertyValue__SWIG_0")]
  public static extern int CSharp_GetDialogueEventCustomPropertyValue__SWIG_0(uint jarg1, uint jarg2, out int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDialogueEventCustomPropertyValue__SWIG_1")]
  public static extern int CSharp_GetDialogueEventCustomPropertyValue__SWIG_1(uint jarg1, uint jarg2, out float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fCenterPct_set")]
  public static extern void CSharp_AkPositioningInfo_fCenterPct_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fCenterPct_get")]
  public static extern float CSharp_AkPositioningInfo_fCenterPct_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_pannerType_set")]
  public static extern void CSharp_AkPositioningInfo_pannerType_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_pannerType_get")]
  public static extern int CSharp_AkPositioningInfo_pannerType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_e3dPositioningType_set")]
  public static extern void CSharp_AkPositioningInfo_e3dPositioningType_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_e3dPositioningType_get")]
  public static extern int CSharp_AkPositioningInfo_e3dPositioningType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_bHoldEmitterPosAndOrient_set")]
  public static extern void CSharp_AkPositioningInfo_bHoldEmitterPosAndOrient_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_bHoldEmitterPosAndOrient_get")]
  public static extern bool CSharp_AkPositioningInfo_bHoldEmitterPosAndOrient_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_e3DSpatializationMode_set")]
  public static extern void CSharp_AkPositioningInfo_e3DSpatializationMode_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_e3DSpatializationMode_get")]
  public static extern int CSharp_AkPositioningInfo_e3DSpatializationMode_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_bEnableAttenuation_set")]
  public static extern void CSharp_AkPositioningInfo_bEnableAttenuation_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_bEnableAttenuation_get")]
  public static extern bool CSharp_AkPositioningInfo_bEnableAttenuation_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_bUseConeAttenuation_set")]
  public static extern void CSharp_AkPositioningInfo_bUseConeAttenuation_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_bUseConeAttenuation_get")]
  public static extern bool CSharp_AkPositioningInfo_bUseConeAttenuation_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fInnerAngle_set")]
  public static extern void CSharp_AkPositioningInfo_fInnerAngle_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fInnerAngle_get")]
  public static extern float CSharp_AkPositioningInfo_fInnerAngle_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fOuterAngle_set")]
  public static extern void CSharp_AkPositioningInfo_fOuterAngle_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fOuterAngle_get")]
  public static extern float CSharp_AkPositioningInfo_fOuterAngle_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fConeMaxAttenuation_set")]
  public static extern void CSharp_AkPositioningInfo_fConeMaxAttenuation_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fConeMaxAttenuation_get")]
  public static extern float CSharp_AkPositioningInfo_fConeMaxAttenuation_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_LPFCone_set")]
  public static extern void CSharp_AkPositioningInfo_LPFCone_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_LPFCone_get")]
  public static extern float CSharp_AkPositioningInfo_LPFCone_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_HPFCone_set")]
  public static extern void CSharp_AkPositioningInfo_HPFCone_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_HPFCone_get")]
  public static extern float CSharp_AkPositioningInfo_HPFCone_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fMaxDistance_set")]
  public static extern void CSharp_AkPositioningInfo_fMaxDistance_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fMaxDistance_get")]
  public static extern float CSharp_AkPositioningInfo_fMaxDistance_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fVolDryAtMaxDist_set")]
  public static extern void CSharp_AkPositioningInfo_fVolDryAtMaxDist_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fVolDryAtMaxDist_get")]
  public static extern float CSharp_AkPositioningInfo_fVolDryAtMaxDist_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fVolAuxGameDefAtMaxDist_set")]
  public static extern void CSharp_AkPositioningInfo_fVolAuxGameDefAtMaxDist_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fVolAuxGameDefAtMaxDist_get")]
  public static extern float CSharp_AkPositioningInfo_fVolAuxGameDefAtMaxDist_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fVolAuxUserDefAtMaxDist_set")]
  public static extern void CSharp_AkPositioningInfo_fVolAuxUserDefAtMaxDist_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_fVolAuxUserDefAtMaxDist_get")]
  public static extern float CSharp_AkPositioningInfo_fVolAuxUserDefAtMaxDist_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_LPFValueAtMaxDist_set")]
  public static extern void CSharp_AkPositioningInfo_LPFValueAtMaxDist_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_LPFValueAtMaxDist_get")]
  public static extern float CSharp_AkPositioningInfo_LPFValueAtMaxDist_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_HPFValueAtMaxDist_set")]
  public static extern void CSharp_AkPositioningInfo_HPFValueAtMaxDist_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPositioningInfo_HPFValueAtMaxDist_get")]
  public static extern float CSharp_AkPositioningInfo_HPFValueAtMaxDist_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkPositioningInfo")]
  public static extern global::System.IntPtr CSharp_new_AkPositioningInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkPositioningInfo")]
  public static extern void CSharp_delete_AkPositioningInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_objID_set")]
  public static extern void CSharp_AkObjectInfo_objID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_objID_get")]
  public static extern uint CSharp_AkObjectInfo_objID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_parentID_set")]
  public static extern void CSharp_AkObjectInfo_parentID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_parentID_get")]
  public static extern uint CSharp_AkObjectInfo_parentID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_iDepth_set")]
  public static extern void CSharp_AkObjectInfo_iDepth_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_iDepth_get")]
  public static extern int CSharp_AkObjectInfo_iDepth_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_Clear")]
  public static extern void CSharp_AkObjectInfo_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_GetSizeOf")]
  public static extern int CSharp_AkObjectInfo_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkObjectInfo_Clone")]
  public static extern void CSharp_AkObjectInfo_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkObjectInfo")]
  public static extern global::System.IntPtr CSharp_new_AkObjectInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkObjectInfo")]
  public static extern void CSharp_delete_AkObjectInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPosition")]
  public static extern int CSharp_GetPosition(ulong jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetListenerPosition")]
  public static extern int CSharp_GetListenerPosition(ulong jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetRTPCValue__SWIG_0")]
  public static extern int CSharp_GetRTPCValue__SWIG_0(uint jarg1, ulong jarg2, uint jarg3, out float jarg4, ref int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetRTPCValue__SWIG_1")]
  public static extern int CSharp_GetRTPCValue__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, uint jarg3, out float jarg4, ref int jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSwitch__SWIG_0")]
  public static extern int CSharp_GetSwitch__SWIG_0(uint jarg1, ulong jarg2, out uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSwitch__SWIG_1")]
  public static extern int CSharp_GetSwitch__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ulong jarg2, out uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetState__SWIG_0")]
  public static extern int CSharp_GetState__SWIG_0(uint jarg1, out uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetState__SWIG_1")]
  public static extern int CSharp_GetState__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, out uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetGameObjectAuxSendValues")]
  public static extern int CSharp_GetGameObjectAuxSendValues(ulong jarg1, global::System.IntPtr jarg2, ref uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetGameObjectDryLevelValue")]
  public static extern int CSharp_GetGameObjectDryLevelValue(ulong jarg1, ulong jarg2, out float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetObjectObstructionAndOcclusion")]
  public static extern int CSharp_GetObjectObstructionAndOcclusion(ulong jarg1, ulong jarg2, out float jarg3, out float jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_QueryAudioObjectIDs__SWIG_0")]
  public static extern int CSharp_QueryAudioObjectIDs__SWIG_0(uint jarg1, ref uint jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_QueryAudioObjectIDs__SWIG_1")]
  public static extern int CSharp_QueryAudioObjectIDs__SWIG_1([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, ref uint jarg2, global::System.IntPtr jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPositioningInfo")]
  public static extern int CSharp_GetPositioningInfo(uint jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetIsGameObjectActive")]
  public static extern bool CSharp_GetIsGameObjectActive(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetMaxRadius")]
  public static extern float CSharp_GetMaxRadius(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetEventIDFromPlayingID")]
  public static extern uint CSharp_GetEventIDFromPlayingID(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetGameObjectFromPlayingID")]
  public static extern ulong CSharp_GetGameObjectFromPlayingID(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetPlayingIDsFromGameObject")]
  public static extern int CSharp_GetPlayingIDsFromGameObject(ulong jarg1, ref uint jarg2, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetCustomPropertyValue__SWIG_0")]
  public static extern int CSharp_GetCustomPropertyValue__SWIG_0(uint jarg1, uint jarg2, out int jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetCustomPropertyValue__SWIG_1")]
  public static extern int CSharp_GetCustomPropertyValue__SWIG_1(uint jarg1, uint jarg2, out float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_SPEAKER_SETUP_FIX_LEFT_TO_CENTER")]
  public static extern void CSharp_AK_SPEAKER_SETUP_FIX_LEFT_TO_CENTER(ref uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_SPEAKER_SETUP_FIX_REAR_TO_SIDE")]
  public static extern void CSharp_AK_SPEAKER_SETUP_FIX_REAR_TO_SIDE(ref uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AK_SPEAKER_SETUP_CONVERT_TO_SUPPORTED")]
  public static extern void CSharp_AK_SPEAKER_SETUP_CONVERT_TO_SUPPORTED(ref uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ChannelMaskToNumChannels")]
  public static extern byte CSharp_ChannelMaskToNumChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ChannelMaskFromNumChannels")]
  public static extern uint CSharp_ChannelMaskFromNumChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ChannelBitToIndex")]
  public static extern byte CSharp_ChannelBitToIndex(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_HasSurroundChannels")]
  public static extern bool CSharp_HasSurroundChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_HasStrictlyOnePairOfSurroundChannels")]
  public static extern bool CSharp_HasStrictlyOnePairOfSurroundChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_HasSideAndRearChannels")]
  public static extern bool CSharp_HasSideAndRearChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_HasHeightChannels")]
  public static extern bool CSharp_HasHeightChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_BackToSideChannels")]
  public static extern uint CSharp_BackToSideChannels(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_uNumChannels_set")]
  public static extern void CSharp_AkChannelConfig_uNumChannels_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_uNumChannels_get")]
  public static extern uint CSharp_AkChannelConfig_uNumChannels_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_eConfigType_set")]
  public static extern void CSharp_AkChannelConfig_eConfigType_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_eConfigType_get")]
  public static extern uint CSharp_AkChannelConfig_eConfigType_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_uChannelMask_set")]
  public static extern void CSharp_AkChannelConfig_uChannelMask_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_uChannelMask_get")]
  public static extern uint CSharp_AkChannelConfig_uChannelMask_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Standard")]
  public static extern global::System.IntPtr CSharp_AkChannelConfig_Standard(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Anonymous")]
  public static extern global::System.IntPtr CSharp_AkChannelConfig_Anonymous(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Ambisonic")]
  public static extern global::System.IntPtr CSharp_AkChannelConfig_Ambisonic(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Object")]
  public static extern global::System.IntPtr CSharp_AkChannelConfig_Object();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkChannelConfig__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkChannelConfig__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkChannelConfig__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkChannelConfig__SWIG_1(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Clear")]
  public static extern void CSharp_AkChannelConfig_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetStandard")]
  public static extern void CSharp_AkChannelConfig_SetStandard(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetStandardOrAnonymous")]
  public static extern void CSharp_AkChannelConfig_SetStandardOrAnonymous(global::System.IntPtr jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetAnonymous")]
  public static extern void CSharp_AkChannelConfig_SetAnonymous(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetAmbisonic")]
  public static extern void CSharp_AkChannelConfig_SetAmbisonic(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetObject")]
  public static extern void CSharp_AkChannelConfig_SetObject(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetSameAsMainMix")]
  public static extern void CSharp_AkChannelConfig_SetSameAsMainMix(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_SetSameAsPassthrough")]
  public static extern void CSharp_AkChannelConfig_SetSameAsPassthrough(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_IsValid")]
  public static extern bool CSharp_AkChannelConfig_IsValid(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Serialize")]
  public static extern uint CSharp_AkChannelConfig_Serialize(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_Deserialize")]
  public static extern void CSharp_AkChannelConfig_Deserialize(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_RemoveLFE")]
  public static extern global::System.IntPtr CSharp_AkChannelConfig_RemoveLFE(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkChannelConfig_RemoveCenter")]
  public static extern global::System.IntPtr CSharp_AkChannelConfig_RemoveCenter(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkChannelConfig")]
  public static extern void CSharp_delete_AkChannelConfig(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkImageSourceParams__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkImageSourceParams__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkImageSourceParams__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkImageSourceParams__SWIG_1(AkVector64 jarg1, float jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_sourcePosition_set")]
  public static extern void CSharp_AkImageSourceParams_sourcePosition_set(global::System.IntPtr jarg1, AkVector64 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_sourcePosition_get")]
  public static extern AkVector64 CSharp_AkImageSourceParams_sourcePosition_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fDistanceScalingFactor_set")]
  public static extern void CSharp_AkImageSourceParams_fDistanceScalingFactor_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fDistanceScalingFactor_get")]
  public static extern float CSharp_AkImageSourceParams_fDistanceScalingFactor_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fLevel_set")]
  public static extern void CSharp_AkImageSourceParams_fLevel_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fLevel_get")]
  public static extern float CSharp_AkImageSourceParams_fLevel_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fDiffraction_set")]
  public static extern void CSharp_AkImageSourceParams_fDiffraction_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fDiffraction_get")]
  public static extern float CSharp_AkImageSourceParams_fDiffraction_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fOcclusion_set")]
  public static extern void CSharp_AkImageSourceParams_fOcclusion_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_fOcclusion_get")]
  public static extern float CSharp_AkImageSourceParams_fOcclusion_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_uDiffractionEmitterSide_set")]
  public static extern void CSharp_AkImageSourceParams_uDiffractionEmitterSide_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_uDiffractionEmitterSide_get")]
  public static extern byte CSharp_AkImageSourceParams_uDiffractionEmitterSide_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_uDiffractionListenerSide_set")]
  public static extern void CSharp_AkImageSourceParams_uDiffractionListenerSide_set(global::System.IntPtr jarg1, byte jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceParams_uDiffractionListenerSide_get")]
  public static extern byte CSharp_AkImageSourceParams_uDiffractionListenerSide_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkImageSourceParams")]
  public static extern void CSharp_delete_AkImageSourceParams(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kDefaultDiffractionMaxEdges_get")]
  public static extern uint CSharp_kDefaultDiffractionMaxEdges_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kDefaultDiffractionMaxPaths_get")]
  public static extern uint CSharp_kDefaultDiffractionMaxPaths_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kHashListBlockAllocItemCount_get")]
  public static extern uint CSharp_kHashListBlockAllocItemCount_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kRayPoolBlockAllocItemCount_get")]
  public static extern uint CSharp_kRayPoolBlockAllocItemCount_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kDiffractionMaxEdges_get")]
  public static extern uint CSharp_kDiffractionMaxEdges_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kDiffractionMaxPaths_get")]
  public static extern uint CSharp_kDiffractionMaxPaths_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_kPortalToPortalDiffractionMaxPaths_get")]
  public static extern uint CSharp_kPortalToPortalDiffractionMaxPaths_get();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkSpatialAudioInitSettings")]
  public static extern global::System.IntPtr CSharp_new_AkSpatialAudioInitSettings();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxSoundPropagationDepth_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uMaxSoundPropagationDepth_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxSoundPropagationDepth_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uMaxSoundPropagationDepth_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fMovementThreshold_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_fMovementThreshold_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fMovementThreshold_get")]
  public static extern float CSharp_AkSpatialAudioInitSettings_fMovementThreshold_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uNumberOfPrimaryRays_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uNumberOfPrimaryRays_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uNumberOfPrimaryRays_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uNumberOfPrimaryRays_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxReflectionOrder_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uMaxReflectionOrder_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxReflectionOrder_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uMaxReflectionOrder_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxDiffractionOrder_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uMaxDiffractionOrder_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxDiffractionOrder_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uMaxDiffractionOrder_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxDiffractionPaths_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uMaxDiffractionPaths_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxDiffractionPaths_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uMaxDiffractionPaths_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxGlobalReflectionPaths_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uMaxGlobalReflectionPaths_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxGlobalReflectionPaths_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uMaxGlobalReflectionPaths_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxEmitterRoomAuxSends_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uMaxEmitterRoomAuxSends_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uMaxEmitterRoomAuxSends_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uMaxEmitterRoomAuxSends_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uDiffractionOnReflectionsOrder_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uDiffractionOnReflectionsOrder_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uDiffractionOnReflectionsOrder_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uDiffractionOnReflectionsOrder_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fMaxDiffractionAngleDegrees_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_fMaxDiffractionAngleDegrees_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fMaxDiffractionAngleDegrees_get")]
  public static extern float CSharp_AkSpatialAudioInitSettings_fMaxDiffractionAngleDegrees_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fMaxPathLength_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_fMaxPathLength_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fMaxPathLength_get")]
  public static extern float CSharp_AkSpatialAudioInitSettings_fMaxPathLength_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fCPULimitPercentage_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_fCPULimitPercentage_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fCPULimitPercentage_get")]
  public static extern float CSharp_AkSpatialAudioInitSettings_fCPULimitPercentage_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fSmoothingConstantMs_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_fSmoothingConstantMs_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_fSmoothingConstantMs_get")]
  public static extern float CSharp_AkSpatialAudioInitSettings_fSmoothingConstantMs_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uLoadBalancingSpread_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_uLoadBalancingSpread_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_uLoadBalancingSpread_get")]
  public static extern uint CSharp_AkSpatialAudioInitSettings_uLoadBalancingSpread_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_bEnableGeometricDiffractionAndTransmission_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_bEnableGeometricDiffractionAndTransmission_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_bEnableGeometricDiffractionAndTransmission_get")]
  public static extern bool CSharp_AkSpatialAudioInitSettings_bEnableGeometricDiffractionAndTransmission_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_bCalcEmitterVirtualPosition_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_bCalcEmitterVirtualPosition_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_bCalcEmitterVirtualPosition_get")]
  public static extern bool CSharp_AkSpatialAudioInitSettings_bCalcEmitterVirtualPosition_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_eTransmissionOperation_set")]
  public static extern void CSharp_AkSpatialAudioInitSettings_eTransmissionOperation_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkSpatialAudioInitSettings_eTransmissionOperation_get")]
  public static extern int CSharp_AkSpatialAudioInitSettings_eTransmissionOperation_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkSpatialAudioInitSettings")]
  public static extern void CSharp_delete_AkSpatialAudioInitSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkImageSourceSettings__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkImageSourceSettings__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkImageSourceSettings__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkImageSourceSettings__SWIG_1(AkVector64 jarg1, float jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceSettings_SetOneTexture")]
  public static extern void CSharp_AkImageSourceSettings_SetOneTexture(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceSettings_params__set")]
  public static extern void CSharp_AkImageSourceSettings_params__set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkImageSourceSettings_params__get")]
  public static extern global::System.IntPtr CSharp_AkImageSourceSettings_params__get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkImageSourceSettings")]
  public static extern void CSharp_delete_AkImageSourceSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkExtent__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkExtent__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkExtent__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkExtent__SWIG_1(float jarg1, float jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExtent_halfWidth_set")]
  public static extern void CSharp_AkExtent_halfWidth_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExtent_halfWidth_get")]
  public static extern float CSharp_AkExtent_halfWidth_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExtent_halfHeight_set")]
  public static extern void CSharp_AkExtent_halfHeight_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExtent_halfHeight_get")]
  public static extern float CSharp_AkExtent_halfHeight_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExtent_halfDepth_set")]
  public static extern void CSharp_AkExtent_halfDepth_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExtent_halfDepth_get")]
  public static extern float CSharp_AkExtent_halfDepth_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkExtent")]
  public static extern void CSharp_delete_AkExtent(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkTriangle__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkTriangle__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkTriangle__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkTriangle__SWIG_1(ushort jarg1, ushort jarg2, ushort jarg3, ushort jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_point0_set")]
  public static extern void CSharp_AkTriangle_point0_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_point0_get")]
  public static extern ushort CSharp_AkTriangle_point0_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_point1_set")]
  public static extern void CSharp_AkTriangle_point1_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_point1_get")]
  public static extern ushort CSharp_AkTriangle_point1_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_point2_set")]
  public static extern void CSharp_AkTriangle_point2_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_point2_get")]
  public static extern ushort CSharp_AkTriangle_point2_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_surface_set")]
  public static extern void CSharp_AkTriangle_surface_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_surface_get")]
  public static extern ushort CSharp_AkTriangle_surface_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_Clear")]
  public static extern void CSharp_AkTriangle_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_GetSizeOf")]
  public static extern int CSharp_AkTriangle_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkTriangle_Clone")]
  public static extern void CSharp_AkTriangle_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkTriangle")]
  public static extern void CSharp_delete_AkTriangle(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkAcousticSurface")]
  public static extern global::System.IntPtr CSharp_new_AkAcousticSurface();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_textureID_set")]
  public static extern void CSharp_AkAcousticSurface_textureID_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_textureID_get")]
  public static extern uint CSharp_AkAcousticSurface_textureID_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_transmissionLoss_set")]
  public static extern void CSharp_AkAcousticSurface_transmissionLoss_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_transmissionLoss_get")]
  public static extern float CSharp_AkAcousticSurface_transmissionLoss_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_strName_set")]
  public static extern void CSharp_AkAcousticSurface_strName_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_strName_get")]
  public static extern global::System.IntPtr CSharp_AkAcousticSurface_strName_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_Clear")]
  public static extern void CSharp_AkAcousticSurface_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_DeleteName")]
  public static extern void CSharp_AkAcousticSurface_DeleteName(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_GetSizeOf")]
  public static extern int CSharp_AkAcousticSurface_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkAcousticSurface_Clone")]
  public static extern void CSharp_AkAcousticSurface_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkAcousticSurface")]
  public static extern void CSharp_delete_AkAcousticSurface(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_imageSource_set")]
  public static extern void CSharp_AkReflectionPathInfo_imageSource_set(global::System.IntPtr jarg1, AkVector64 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_imageSource_get")]
  public static extern AkVector64 CSharp_AkReflectionPathInfo_imageSource_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_numPathPoints_set")]
  public static extern void CSharp_AkReflectionPathInfo_numPathPoints_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_numPathPoints_get")]
  public static extern uint CSharp_AkReflectionPathInfo_numPathPoints_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_numReflections_set")]
  public static extern void CSharp_AkReflectionPathInfo_numReflections_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_numReflections_get")]
  public static extern uint CSharp_AkReflectionPathInfo_numReflections_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_level_set")]
  public static extern void CSharp_AkReflectionPathInfo_level_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_level_get")]
  public static extern float CSharp_AkReflectionPathInfo_level_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_isOccluded_set")]
  public static extern void CSharp_AkReflectionPathInfo_isOccluded_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_isOccluded_get")]
  public static extern bool CSharp_AkReflectionPathInfo_isOccluded_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_GetSizeOf")]
  public static extern int CSharp_AkReflectionPathInfo_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_GetPathPoint")]
  public static extern UnityEngine.Vector3 CSharp_AkReflectionPathInfo_GetPathPoint(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_GetTextureIDs")]
  public static extern uint CSharp_AkReflectionPathInfo_GetTextureIDs(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_GetDiffraction")]
  public static extern float CSharp_AkReflectionPathInfo_GetDiffraction(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkReflectionPathInfo_Clone")]
  public static extern void CSharp_AkReflectionPathInfo_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkReflectionPathInfo")]
  public static extern global::System.IntPtr CSharp_new_AkReflectionPathInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkReflectionPathInfo")]
  public static extern void CSharp_delete_AkReflectionPathInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_emitterPos_set")]
  public static extern void CSharp_AkDiffractionPathInfo_emitterPos_set(global::System.IntPtr jarg1, AkVector64 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_emitterPos_get")]
  public static extern AkVector64 CSharp_AkDiffractionPathInfo_emitterPos_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_virtualPos_set")]
  public static extern void CSharp_AkDiffractionPathInfo_virtualPos_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_virtualPos_get")]
  public static extern global::System.IntPtr CSharp_AkDiffractionPathInfo_virtualPos_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_nodeCount_set")]
  public static extern void CSharp_AkDiffractionPathInfo_nodeCount_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_nodeCount_get")]
  public static extern uint CSharp_AkDiffractionPathInfo_nodeCount_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_diffraction_set")]
  public static extern void CSharp_AkDiffractionPathInfo_diffraction_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_diffraction_get")]
  public static extern float CSharp_AkDiffractionPathInfo_diffraction_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_transmissionLoss_set")]
  public static extern void CSharp_AkDiffractionPathInfo_transmissionLoss_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_transmissionLoss_get")]
  public static extern float CSharp_AkDiffractionPathInfo_transmissionLoss_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_totLength_set")]
  public static extern void CSharp_AkDiffractionPathInfo_totLength_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_totLength_get")]
  public static extern float CSharp_AkDiffractionPathInfo_totLength_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_obstructionValue_set")]
  public static extern void CSharp_AkDiffractionPathInfo_obstructionValue_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_obstructionValue_get")]
  public static extern float CSharp_AkDiffractionPathInfo_obstructionValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_occlusionValue_set")]
  public static extern void CSharp_AkDiffractionPathInfo_occlusionValue_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_occlusionValue_get")]
  public static extern float CSharp_AkDiffractionPathInfo_occlusionValue_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_gain_set")]
  public static extern void CSharp_AkDiffractionPathInfo_gain_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_gain_get")]
  public static extern float CSharp_AkDiffractionPathInfo_gain_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_GetSizeOf")]
  public static extern int CSharp_AkDiffractionPathInfo_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_GetNodes")]
  public static extern UnityEngine.Vector3 CSharp_AkDiffractionPathInfo_GetNodes(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_GetAngles")]
  public static extern float CSharp_AkDiffractionPathInfo_GetAngles(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_GetPortals")]
  public static extern ulong CSharp_AkDiffractionPathInfo_GetPortals(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_GetRooms")]
  public static extern ulong CSharp_AkDiffractionPathInfo_GetRooms(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDiffractionPathInfo_Clone")]
  public static extern void CSharp_AkDiffractionPathInfo_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkDiffractionPathInfo")]
  public static extern global::System.IntPtr CSharp_new_AkDiffractionPathInfo();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkDiffractionPathInfo")]
  public static extern void CSharp_delete_AkDiffractionPathInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkRoomParams")]
  public static extern global::System.IntPtr CSharp_new_AkRoomParams();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_Front_set")]
  public static extern void CSharp_AkRoomParams_Front_set(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_Front_get")]
  public static extern UnityEngine.Vector3 CSharp_AkRoomParams_Front_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_Up_set")]
  public static extern void CSharp_AkRoomParams_Up_set(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_Up_get")]
  public static extern UnityEngine.Vector3 CSharp_AkRoomParams_Up_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_ReverbAuxBus_set")]
  public static extern void CSharp_AkRoomParams_ReverbAuxBus_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_ReverbAuxBus_get")]
  public static extern uint CSharp_AkRoomParams_ReverbAuxBus_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_ReverbLevel_set")]
  public static extern void CSharp_AkRoomParams_ReverbLevel_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_ReverbLevel_get")]
  public static extern float CSharp_AkRoomParams_ReverbLevel_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_TransmissionLoss_set")]
  public static extern void CSharp_AkRoomParams_TransmissionLoss_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_TransmissionLoss_get")]
  public static extern float CSharp_AkRoomParams_TransmissionLoss_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_RoomGameObj_AuxSendLevelToSelf_set")]
  public static extern void CSharp_AkRoomParams_RoomGameObj_AuxSendLevelToSelf_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_RoomGameObj_AuxSendLevelToSelf_get")]
  public static extern float CSharp_AkRoomParams_RoomGameObj_AuxSendLevelToSelf_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_RoomGameObj_KeepRegistered_set")]
  public static extern void CSharp_AkRoomParams_RoomGameObj_KeepRegistered_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_RoomGameObj_KeepRegistered_get")]
  public static extern bool CSharp_AkRoomParams_RoomGameObj_KeepRegistered_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_RoomPriority_set")]
  public static extern void CSharp_AkRoomParams_RoomPriority_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkRoomParams_RoomPriority_get")]
  public static extern float CSharp_AkRoomParams_RoomPriority_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkRoomParams")]
  public static extern void CSharp_delete_AkRoomParams(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkGeometryInstanceParams")]
  public static extern global::System.IntPtr CSharp_new_AkGeometryInstanceParams();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_PositionAndOrientation_set")]
  public static extern void CSharp_AkGeometryInstanceParams_PositionAndOrientation_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_PositionAndOrientation_get")]
  public static extern global::System.IntPtr CSharp_AkGeometryInstanceParams_PositionAndOrientation_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_Scale_set")]
  public static extern void CSharp_AkGeometryInstanceParams_Scale_set(global::System.IntPtr jarg1, UnityEngine.Vector3 jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_Scale_get")]
  public static extern UnityEngine.Vector3 CSharp_AkGeometryInstanceParams_Scale_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_UseForReflectionAndDiffraction_set")]
  public static extern void CSharp_AkGeometryInstanceParams_UseForReflectionAndDiffraction_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_UseForReflectionAndDiffraction_get")]
  public static extern bool CSharp_AkGeometryInstanceParams_UseForReflectionAndDiffraction_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_BypassPortalSubtraction_set")]
  public static extern void CSharp_AkGeometryInstanceParams_BypassPortalSubtraction_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_BypassPortalSubtraction_get")]
  public static extern bool CSharp_AkGeometryInstanceParams_BypassPortalSubtraction_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_IsSolid_set")]
  public static extern void CSharp_AkGeometryInstanceParams_IsSolid_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkGeometryInstanceParams_IsSolid_get")]
  public static extern bool CSharp_AkGeometryInstanceParams_IsSolid_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkGeometryInstanceParams")]
  public static extern void CSharp_delete_AkGeometryInstanceParams(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGameObjectRadius")]
  public static extern int CSharp_SetGameObjectRadius(ulong jarg1, float jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetImageSource__SWIG_0")]
  public static extern int CSharp_SetImageSource__SWIG_0(uint jarg1, global::System.IntPtr jarg2, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg3, uint jarg4, ulong jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetImageSource__SWIG_1")]
  public static extern int CSharp_SetImageSource__SWIG_1(uint jarg1, global::System.IntPtr jarg2, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetImageSource__SWIG_2")]
  public static extern int CSharp_SetImageSource__SWIG_2(uint jarg1, global::System.IntPtr jarg2, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveImageSource__SWIG_0")]
  public static extern int CSharp_RemoveImageSource__SWIG_0(uint jarg1, uint jarg2, ulong jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveImageSource__SWIG_1")]
  public static extern int CSharp_RemoveImageSource__SWIG_1(uint jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveImageSource__SWIG_2")]
  public static extern int CSharp_RemoveImageSource__SWIG_2(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ClearImageSources__SWIG_0")]
  public static extern int CSharp_ClearImageSources__SWIG_0(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ClearImageSources__SWIG_1")]
  public static extern int CSharp_ClearImageSources__SWIG_1(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ClearImageSources__SWIG_2")]
  public static extern int CSharp_ClearImageSources__SWIG_2();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveGeometry")]
  public static extern int CSharp_RemoveGeometry(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveGeometryInstance")]
  public static extern int CSharp_RemoveGeometryInstance(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveRoom")]
  public static extern int CSharp_RemoveRoom(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemovePortal")]
  public static extern int CSharp_RemovePortal(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetReverbZone")]
  public static extern int CSharp_SetReverbZone(ulong jarg1, ulong jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RemoveReverbZone")]
  public static extern int CSharp_RemoveReverbZone(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGameObjectInRoom")]
  public static extern int CSharp_SetGameObjectInRoom(ulong jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnsetGameObjectInRoom")]
  public static extern int CSharp_UnsetGameObjectInRoom(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetReflectionsOrder")]
  public static extern int CSharp_SetReflectionsOrder(uint jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetDiffractionOrder")]
  public static extern int CSharp_SetDiffractionOrder(uint jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMaxGlobalReflectionPaths")]
  public static extern int CSharp_SetMaxGlobalReflectionPaths(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMaxDiffractionPaths__SWIG_0")]
  public static extern int CSharp_SetMaxDiffractionPaths__SWIG_0(uint jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMaxDiffractionPaths__SWIG_1")]
  public static extern int CSharp_SetMaxDiffractionPaths__SWIG_1(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetMaxEmitterRoomAuxSends")]
  public static extern int CSharp_SetMaxEmitterRoomAuxSends(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetNumberOfPrimaryRays")]
  public static extern int CSharp_SetNumberOfPrimaryRays(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetLoadBalancingSpread")]
  public static extern int CSharp_SetLoadBalancingSpread(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSmoothingConstant__SWIG_0")]
  public static extern int CSharp_SetSmoothingConstant__SWIG_0(float jarg1, ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetSmoothingConstant__SWIG_1")]
  public static extern int CSharp_SetSmoothingConstant__SWIG_1(float jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetEarlyReflectionsAuxSend")]
  public static extern int CSharp_SetEarlyReflectionsAuxSend(ulong jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetEarlyReflectionsVolume")]
  public static extern int CSharp_SetEarlyReflectionsVolume(ulong jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetPortalObstructionAndOcclusion")]
  public static extern int CSharp_SetPortalObstructionAndOcclusion(ulong jarg1, float jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGameObjectToPortalObstruction")]
  public static extern int CSharp_SetGameObjectToPortalObstruction(ulong jarg1, ulong jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetPortalToPortalObstruction")]
  public static extern int CSharp_SetPortalToPortalObstruction(ulong jarg1, ulong jarg2, float jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_QueryWetDiffraction")]
  public static extern int CSharp_QueryWetDiffraction(ulong jarg1, out float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetTransmissionOperation")]
  public static extern int CSharp_SetTransmissionOperation(int jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ResetStochasticEngine")]
  public static extern int CSharp_ResetStochasticEngine();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadLEngine_set")]
  public static extern void CSharp_AkPlatformInitSettings_threadLEngine_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadLEngine_get")]
  public static extern global::System.IntPtr CSharp_AkPlatformInitSettings_threadLEngine_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadOutputMgr_set")]
  public static extern void CSharp_AkPlatformInitSettings_threadOutputMgr_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadOutputMgr_get")]
  public static extern global::System.IntPtr CSharp_AkPlatformInitSettings_threadOutputMgr_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadBankManager_set")]
  public static extern void CSharp_AkPlatformInitSettings_threadBankManager_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadBankManager_get")]
  public static extern global::System.IntPtr CSharp_AkPlatformInitSettings_threadBankManager_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadMonitor_set")]
  public static extern void CSharp_AkPlatformInitSettings_threadMonitor_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_threadMonitor_get")]
  public static extern global::System.IntPtr CSharp_AkPlatformInitSettings_threadMonitor_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_eAudioAPI_set")]
  public static extern void CSharp_AkPlatformInitSettings_eAudioAPI_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_eAudioAPI_get")]
  public static extern int CSharp_AkPlatformInitSettings_eAudioAPI_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_uSampleRate_set")]
  public static extern void CSharp_AkPlatformInitSettings_uSampleRate_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_uSampleRate_get")]
  public static extern uint CSharp_AkPlatformInitSettings_uSampleRate_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_uNumRefillsInVoice_set")]
  public static extern void CSharp_AkPlatformInitSettings_uNumRefillsInVoice_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_uNumRefillsInVoice_get")]
  public static extern ushort CSharp_AkPlatformInitSettings_uNumRefillsInVoice_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_bRoundFrameSizeToHWSize_set")]
  public static extern void CSharp_AkPlatformInitSettings_bRoundFrameSizeToHWSize_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_bRoundFrameSizeToHWSize_get")]
  public static extern bool CSharp_AkPlatformInitSettings_bRoundFrameSizeToHWSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_bVerboseSink_set")]
  public static extern void CSharp_AkPlatformInitSettings_bVerboseSink_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_bVerboseSink_get")]
  public static extern bool CSharp_AkPlatformInitSettings_bVerboseSink_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_bEnableLowLatency_set")]
  public static extern void CSharp_AkPlatformInitSettings_bEnableLowLatency_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlatformInitSettings_bEnableLowLatency_get")]
  public static extern bool CSharp_AkPlatformInitSettings_bEnableLowLatency_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkPlatformInitSettings")]
  public static extern void CSharp_delete_AkPlatformInitSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetFastPathSettings")]
  public static extern int CSharp_GetFastPathSettings(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkStreamMgrSettings")]
  public static extern void CSharp_delete_AkStreamMgrSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_pIOMemory_set")]
  public static extern void CSharp_AkDeviceSettings_pIOMemory_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_pIOMemory_get")]
  public static extern global::System.IntPtr CSharp_AkDeviceSettings_pIOMemory_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uIOMemorySize_set")]
  public static extern void CSharp_AkDeviceSettings_uIOMemorySize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uIOMemorySize_get")]
  public static extern uint CSharp_AkDeviceSettings_uIOMemorySize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uIOMemoryAlignment_set")]
  public static extern void CSharp_AkDeviceSettings_uIOMemoryAlignment_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uIOMemoryAlignment_get")]
  public static extern uint CSharp_AkDeviceSettings_uIOMemoryAlignment_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_ePoolAttributes_set")]
  public static extern void CSharp_AkDeviceSettings_ePoolAttributes_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_ePoolAttributes_get")]
  public static extern uint CSharp_AkDeviceSettings_ePoolAttributes_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uGranularity_set")]
  public static extern void CSharp_AkDeviceSettings_uGranularity_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uGranularity_get")]
  public static extern uint CSharp_AkDeviceSettings_uGranularity_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_threadProperties_set")]
  public static extern void CSharp_AkDeviceSettings_threadProperties_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_threadProperties_get")]
  public static extern global::System.IntPtr CSharp_AkDeviceSettings_threadProperties_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_fTargetAutoStmBufferLength_set")]
  public static extern void CSharp_AkDeviceSettings_fTargetAutoStmBufferLength_set(global::System.IntPtr jarg1, float jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_fTargetAutoStmBufferLength_get")]
  public static extern float CSharp_AkDeviceSettings_fTargetAutoStmBufferLength_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uMaxConcurrentIO_set")]
  public static extern void CSharp_AkDeviceSettings_uMaxConcurrentIO_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uMaxConcurrentIO_get")]
  public static extern uint CSharp_AkDeviceSettings_uMaxConcurrentIO_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_bUseStreamCache_set")]
  public static extern void CSharp_AkDeviceSettings_bUseStreamCache_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_bUseStreamCache_get")]
  public static extern bool CSharp_AkDeviceSettings_bUseStreamCache_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uMaxCachePinnedBytes_set")]
  public static extern void CSharp_AkDeviceSettings_uMaxCachePinnedBytes_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDeviceSettings_uMaxCachePinnedBytes_get")]
  public static extern uint CSharp_AkDeviceSettings_uMaxCachePinnedBytes_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkDeviceSettings")]
  public static extern void CSharp_delete_AkDeviceSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_nPriority_set")]
  public static extern void CSharp_AkThreadProperties_nPriority_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_nPriority_get")]
  public static extern int CSharp_AkThreadProperties_nPriority_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_uStackSize_set")]
  public static extern void CSharp_AkThreadProperties_uStackSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_uStackSize_get")]
  public static extern uint CSharp_AkThreadProperties_uStackSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_uSchedPolicy_set")]
  public static extern void CSharp_AkThreadProperties_uSchedPolicy_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_uSchedPolicy_get")]
  public static extern int CSharp_AkThreadProperties_uSchedPolicy_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_dwAffinityMask_set")]
  public static extern void CSharp_AkThreadProperties_dwAffinityMask_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkThreadProperties_dwAffinityMask_get")]
  public static extern uint CSharp_AkThreadProperties_dwAffinityMask_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkThreadProperties")]
  public static extern global::System.IntPtr CSharp_new_AkThreadProperties();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkThreadProperties")]
  public static extern void CSharp_delete_AkThreadProperties(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetErrorLogger__SWIG_0")]
  public static extern void CSharp_SetErrorLogger__SWIG_0(AkLogger.ErrorLoggerInteropDelegate jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetErrorLogger__SWIG_1")]
  public static extern void CSharp_SetErrorLogger__SWIG_1();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetAudioInputCallbacks")]
  public static extern void CSharp_SetAudioInputCallbacks(AkAudioInputManager.AudioSamplesInteropDelegate jarg1, AkAudioInputManager.AudioFormatInteropDelegate jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkCommunicationSettings")]
  public static extern global::System.IntPtr CSharp_new_AkCommunicationSettings();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_uPoolSize_set")]
  public static extern void CSharp_AkCommunicationSettings_uPoolSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_uPoolSize_get")]
  public static extern uint CSharp_AkCommunicationSettings_uPoolSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_uDiscoveryBroadcastPort_set")]
  public static extern void CSharp_AkCommunicationSettings_uDiscoveryBroadcastPort_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_uDiscoveryBroadcastPort_get")]
  public static extern ushort CSharp_AkCommunicationSettings_uDiscoveryBroadcastPort_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_uCommandPort_set")]
  public static extern void CSharp_AkCommunicationSettings_uCommandPort_set(global::System.IntPtr jarg1, ushort jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_uCommandPort_get")]
  public static extern ushort CSharp_AkCommunicationSettings_uCommandPort_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_commSystem_set")]
  public static extern void CSharp_AkCommunicationSettings_commSystem_set(global::System.IntPtr jarg1, int jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_commSystem_get")]
  public static extern int CSharp_AkCommunicationSettings_commSystem_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_bInitSystemLib_set")]
  public static extern void CSharp_AkCommunicationSettings_bInitSystemLib_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_bInitSystemLib_get")]
  public static extern bool CSharp_AkCommunicationSettings_bInitSystemLib_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_szAppNetworkName_set")]
  public static extern void CSharp_AkCommunicationSettings_szAppNetworkName_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkCommunicationSettings_szAppNetworkName_get")]
  public static extern global::System.IntPtr CSharp_AkCommunicationSettings_szAppNetworkName_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkCommunicationSettings")]
  public static extern void CSharp_delete_AkCommunicationSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkInitializationSettings")]
  public static extern global::System.IntPtr CSharp_new_AkInitializationSettings();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkInitializationSettings")]
  public static extern void CSharp_delete_AkInitializationSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_streamMgrSettings_set")]
  public static extern void CSharp_AkInitializationSettings_streamMgrSettings_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_streamMgrSettings_get")]
  public static extern global::System.IntPtr CSharp_AkInitializationSettings_streamMgrSettings_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_deviceSettings_set")]
  public static extern void CSharp_AkInitializationSettings_deviceSettings_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_deviceSettings_get")]
  public static extern global::System.IntPtr CSharp_AkInitializationSettings_deviceSettings_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_initSettings_set")]
  public static extern void CSharp_AkInitializationSettings_initSettings_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_initSettings_get")]
  public static extern global::System.IntPtr CSharp_AkInitializationSettings_initSettings_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_platformSettings_set")]
  public static extern void CSharp_AkInitializationSettings_platformSettings_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_platformSettings_get")]
  public static extern global::System.IntPtr CSharp_AkInitializationSettings_platformSettings_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_musicSettings_set")]
  public static extern void CSharp_AkInitializationSettings_musicSettings_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_musicSettings_get")]
  public static extern global::System.IntPtr CSharp_AkInitializationSettings_musicSettings_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimarySbaInitSize_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryPrimarySbaInitSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimarySbaInitSize_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryPrimarySbaInitSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryTlsfInitSize_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryPrimaryTlsfInitSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryTlsfInitSize_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryPrimaryTlsfInitSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryTlsfSpanSize_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryPrimaryTlsfSpanSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryTlsfSpanSize_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryPrimaryTlsfSpanSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryAllocSizeHuge_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryPrimaryAllocSizeHuge_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryAllocSizeHuge_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryPrimaryAllocSizeHuge_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryReservedLimit_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryPrimaryReservedLimit_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryPrimaryReservedLimit_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryPrimaryReservedLimit_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaTlsfInitSize_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryMediaTlsfInitSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaTlsfInitSize_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryMediaTlsfInitSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaTlsfSpanSize_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryMediaTlsfSpanSize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaTlsfSpanSize_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryMediaTlsfSpanSize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaAllocSizeHuge_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryMediaAllocSizeHuge_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaAllocSizeHuge_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryMediaAllocSizeHuge_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaReservedLimit_set")]
  public static extern void CSharp_AkInitializationSettings_uMemoryMediaReservedLimit_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemoryMediaReservedLimit_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemoryMediaReservedLimit_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemDebugLevel_set")]
  public static extern void CSharp_AkInitializationSettings_uMemDebugLevel_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_uMemDebugLevel_get")]
  public static extern uint CSharp_AkInitializationSettings_uMemDebugLevel_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_bUseSubFoldersForGeneratedFiles_set")]
  public static extern void CSharp_AkInitializationSettings_bUseSubFoldersForGeneratedFiles_set(global::System.IntPtr jarg1, bool jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkInitializationSettings_bUseSubFoldersForGeneratedFiles_get")]
  public static extern bool CSharp_AkInitializationSettings_bUseSubFoldersForGeneratedFiles_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkExternalSourceInfo__SWIG_0")]
  public static extern global::System.IntPtr CSharp_new_AkExternalSourceInfo__SWIG_0();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_delete_AkExternalSourceInfo")]
  public static extern void CSharp_delete_AkExternalSourceInfo(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkExternalSourceInfo__SWIG_1")]
  public static extern global::System.IntPtr CSharp_new_AkExternalSourceInfo__SWIG_1(global::System.IntPtr jarg1, uint jarg2, uint jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkExternalSourceInfo__SWIG_2")]
  public static extern global::System.IntPtr CSharp_new_AkExternalSourceInfo__SWIG_2([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_new_AkExternalSourceInfo__SWIG_3")]
  public static extern global::System.IntPtr CSharp_new_AkExternalSourceInfo__SWIG_3(uint jarg1, uint jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_Clear")]
  public static extern void CSharp_AkExternalSourceInfo_Clear(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_Clone")]
  public static extern void CSharp_AkExternalSourceInfo_Clone(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_GetSizeOf")]
  public static extern int CSharp_AkExternalSourceInfo_GetSizeOf();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_iExternalSrcCookie_set")]
  public static extern void CSharp_AkExternalSourceInfo_iExternalSrcCookie_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_iExternalSrcCookie_get")]
  public static extern uint CSharp_AkExternalSourceInfo_iExternalSrcCookie_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_idCodec_set")]
  public static extern void CSharp_AkExternalSourceInfo_idCodec_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_idCodec_get")]
  public static extern uint CSharp_AkExternalSourceInfo_idCodec_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_szFile_set")]
  public static extern void CSharp_AkExternalSourceInfo_szFile_set(global::System.IntPtr jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_szFile_get")]
  public static extern global::System.IntPtr CSharp_AkExternalSourceInfo_szFile_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_pInMemory_set")]
  public static extern void CSharp_AkExternalSourceInfo_pInMemory_set(global::System.IntPtr jarg1, global::System.IntPtr jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_pInMemory_get")]
  public static extern global::System.IntPtr CSharp_AkExternalSourceInfo_pInMemory_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_uiMemorySize_set")]
  public static extern void CSharp_AkExternalSourceInfo_uiMemorySize_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_uiMemorySize_get")]
  public static extern uint CSharp_AkExternalSourceInfo_uiMemorySize_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_idFile_set")]
  public static extern void CSharp_AkExternalSourceInfo_idFile_set(global::System.IntPtr jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkExternalSourceInfo_idFile_get")]
  public static extern uint CSharp_AkExternalSourceInfo_idFile_get(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetAndroidActivity")]
  public static extern void CSharp_SetAndroidActivity(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Init")]
  public static extern int CSharp_Init(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_InitSpatialAudio")]
  public static extern int CSharp_InitSpatialAudio(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_InitCommunication")]
  public static extern int CSharp_InitCommunication(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_Term")]
  public static extern void CSharp_Term();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RegisterGameObjInternal")]
  public static extern int CSharp_RegisterGameObjInternal(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnregisterGameObjInternal")]
  public static extern int CSharp_UnregisterGameObjInternal(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RegisterGameObjInternal_WithName")]
  public static extern int CSharp_RegisterGameObjInternal_WithName(ulong jarg1, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetBasePath")]
  public static extern int CSharp_SetBasePath([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetCurrentLanguage")]
  public static extern int CSharp_SetCurrentLanguage([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadFilePackage")]
  public static extern int CSharp_LoadFilePackage([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, out uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddBasePath")]
  public static extern int CSharp_AddBasePath([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGameName")]
  public static extern int CSharp_SetGameName([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetDecodedBankPath")]
  public static extern int CSharp_SetDecodedBankPath([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadAndDecodeBank")]
  public static extern int CSharp_LoadAndDecodeBank([global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg1, bool jarg2, out uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_LoadAndDecodeBankFromMemory")]
  public static extern int CSharp_LoadAndDecodeBankFromMemory(global::System.IntPtr jarg1, uint jarg2, bool jarg3, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg4, bool jarg5, out uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetCurrentLanguage")]
  public static extern global::System.IntPtr CSharp_GetCurrentLanguage();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadFilePackage")]
  public static extern int CSharp_UnloadFilePackage(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnloadAllFilePackages")]
  public static extern int CSharp_UnloadAllFilePackages();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetObjectPosition")]
  public static extern int CSharp_SetObjectPosition(ulong jarg1, UnityEngine.Vector3 jarg2, UnityEngine.Vector3 jarg3, UnityEngine.Vector3 jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSourceMultiplePlayPositions__SWIG_0")]
  public static extern int CSharp_GetSourceMultiplePlayPositions__SWIG_0(uint jarg1, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg3, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]int[] jarg4, ref uint jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSourceMultiplePlayPositions__SWIG_1")]
  public static extern int CSharp_GetSourceMultiplePlayPositions__SWIG_1(uint jarg1, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg2, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]uint[] jarg3, [global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]int[] jarg4, ref uint jarg5);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetListeners")]
  public static extern int CSharp_SetListeners(ulong jarg1, ulong[] jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetDefaultListeners")]
  public static extern int CSharp_SetDefaultListeners(ulong[] jarg1, uint jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetNumOutputDevices")]
  public static extern uint CSharp_GetNumOutputDevices(uint jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddOutput__SWIG_0")]
  public static extern int CSharp_AddOutput__SWIG_0(global::System.IntPtr jarg1, out ulong jarg2, ulong[] jarg3, uint jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddOutput__SWIG_1")]
  public static extern int CSharp_AddOutput__SWIG_1(global::System.IntPtr jarg1, out ulong jarg2, ulong[] jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddOutput__SWIG_2")]
  public static extern int CSharp_AddOutput__SWIG_2(global::System.IntPtr jarg1, out ulong jarg2);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AddOutput__SWIG_3")]
  public static extern int CSharp_AddOutput__SWIG_3(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDefaultStreamSettings")]
  public static extern void CSharp_GetDefaultStreamSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDefaultDeviceSettings")]
  public static extern void CSharp_GetDefaultDeviceSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDefaultMusicSettings")]
  public static extern void CSharp_GetDefaultMusicSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDefaultInitSettings")]
  public static extern void CSharp_GetDefaultInitSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetDefaultPlatformInitSettings")]
  public static extern void CSharp_GetDefaultPlatformInitSettings(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetMajorMinorVersion")]
  public static extern uint CSharp_GetMajorMinorVersion();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetSubminorBuildVersion")]
  public static extern uint CSharp_GetSubminorBuildVersion();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StartResourceMonitoring")]
  public static extern void CSharp_StartResourceMonitoring();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopResourceMonitoring")]
  public static extern void CSharp_StopResourceMonitoring();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetResourceMonitorDataSummary")]
  public static extern void CSharp_GetResourceMonitorDataSummary(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StartDeviceCapture")]
  public static extern void CSharp_StartDeviceCapture(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_StopDeviceCapture")]
  public static extern void CSharp_StopDeviceCapture(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_ClearCaptureData")]
  public static extern void CSharp_ClearCaptureData();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UpdateCaptureSampleCount")]
  public static extern uint CSharp_UpdateCaptureSampleCount(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_GetCaptureSamples")]
  public static extern uint CSharp_GetCaptureSamples(ulong jarg1, [global::System.Runtime.InteropServices.In, global::System.Runtime.InteropServices.Out, global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPArray)]float[] jarg2, uint jarg3);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRoomPortal")]
  public static extern int CSharp_SetRoomPortal(ulong jarg1, ulong jarg2, ulong jarg3, global::System.IntPtr jarg4, global::System.IntPtr jarg5, bool jarg6, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg7);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetRoom")]
  public static extern int CSharp_SetRoom(ulong jarg1, global::System.IntPtr jarg2, ulong jarg3, [global::System.Runtime.InteropServices.MarshalAs(global::System.Runtime.InteropServices.UnmanagedType.LPStr)]string jarg4);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_RegisterSpatialAudioListener")]
  public static extern int CSharp_RegisterSpatialAudioListener(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_UnregisterSpatialAudioListener")]
  public static extern int CSharp_UnregisterSpatialAudioListener(ulong jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGeometry")]
  public static extern int CSharp_SetGeometry(ulong jarg1, global::System.IntPtr jarg2, uint jarg3, UnityEngine.Vector3[] jarg4, uint jarg5, global::System.IntPtr jarg6, uint jarg7, bool jarg8, bool jarg9);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_SetGeometryInstance")]
  public static extern int CSharp_SetGeometryInstance(ulong jarg1, global::System.IntPtr jarg2, UnityEngine.Vector3 jarg3, ulong jarg4, bool jarg5, bool jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_QueryReflectionPaths")]
  public static extern int CSharp_QueryReflectionPaths(ulong jarg1, uint jarg2, ref UnityEngine.Vector3 jarg3, ref UnityEngine.Vector3 jarg4, global::System.IntPtr jarg5, out uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_QueryDiffractionPaths")]
  public static extern int CSharp_QueryDiffractionPaths(ulong jarg1, uint jarg2, ref UnityEngine.Vector3 jarg3, ref UnityEngine.Vector3 jarg4, global::System.IntPtr jarg5, out uint jarg6);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_PerformStreamMgrIO")]
  public static extern void CSharp_PerformStreamMgrIO();
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkPlaylist_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkPlaylist_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIPost_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkMIDIPost_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkEventCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkEventCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMIDIEventCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkMIDIEventCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMarkerCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkMarkerCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDurationCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkDurationCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkDynamicSequenceItemCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkDynamicSequenceItemCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicSyncCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkMusicSyncCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);
  [global::System.Runtime.InteropServices.DllImport("AkUnitySoundEngine", EntryPoint="CSharp_AkMusicPlaylistCallbackInfo_SWIGUpcast")]
  public static extern global::System.IntPtr CSharp_AkMusicPlaylistCallbackInfo_SWIGUpcast(global::System.IntPtr jarg1);}
#endif // #if UNITY_ANDROID && ! UNITY_EDITOR