#if UNITY_EDITOR


using System;

namespace NodeCanvas.Framework
{

    public partial class Node
    {
        protected Boolean InvalidFlag => String.IsNullOrEmpty(InvalidMessage) == false;
        protected String InvalidMessage { get;  set; } 

        public void UpdateValidState()
        {
            OnUpdateValidState();
        }

        protected virtual void OnUpdateValidState() { }
    }
}

#endif
