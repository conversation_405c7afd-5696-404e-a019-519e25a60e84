#if UNITY_EDITOR


using System;

namespace NodeCanvas.Framework
{

    public partial class Graph
    {
        private const Single CheckInternal = 1f;
        private Single m_tickTimer = 0f;
        public void UpdateValidState(Single dt)
        {
            m_tickTimer += dt;
            if (m_tickTimer > CheckInternal)
            {
                m_tickTimer -= CheckInternal;
                for (var i = 0; i < allNodes.Count; i++)
                {
                    allNodes[i].UpdateValidState();
                }
            }
        }
    }
}

#endif
