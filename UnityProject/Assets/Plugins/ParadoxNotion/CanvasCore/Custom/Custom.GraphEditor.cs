#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using NodeCanvas.Framework;
using System;
using ParadoxNotion.Design;
using System.IO;

namespace NodeCanvas.Editor
{

    ///<summary>Custom</summary>
    public partial class GraphEditor
    {

        public static void ShowNotification(String text, double fadeoutWait = 2.0)
        {
            current?.ShowNotification(new GUIContent(text), fadeoutWait);
        }

        public static void Save(Graph graph)
        {
            CustomGraphEditor.Instance.Reset();
            EditorUtility.SetDirty(graph);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        public static void Revert(Graph graph, String originalJsonData)
        {
            graph.Deserialize(originalJsonData, null, false);
        }








        protected void ShowCustomToolbar()
        {
            GUILayout.BeginHorizontal();
            {
                GUILayout.Space(4);
                if (GUILayout.Button("File [Empty] ", EditorStyles.toolbarDropDown, GUILayout.Width(180)))
                {
                    ShowToolbarMenu_Custom().ShowAsBrowser("选择要打开的节点图文件");
                }
                GUILayout.FlexibleSpace();
                GUILayout.Space(4);
                if (GUILayout.Button(Icons.helpIcon, EditorStyles.toolbarButton)) { WelcomeWindow.ShowWindow(null); }
            }
            GUILayout.EndHorizontal();
        }


        protected void UpdateCustom(Single deltaTime)
        {
            if (rootGraph == null)
            {
                CustomGraphEditor.Destroy();
                return;
            }
            UpdateGraphValidState(deltaTime);
        }




        private GenericMenu ShowToolbarMenu_Custom()
        {
            var menu = new GenericMenu();
            Action<String> Selected = (filePath) =>
            {
                filePath.Replace('\\', '/');
                String path = filePath.Substring(filePath.IndexOf("Assets"));
                Graph graph = AssetDatabase.LoadAssetAtPath(path, typeof(Graph)) as Graph;
                if (graph != null)
                {
                    OpenWindow(graph);
                    Selection.activeObject = graph;
                    EditorGUIUtility.PingObject(graph);
                }
            };
            String filePath = Path.Combine(Application.dataPath, $"Local/QuestAsset");
            CollectAssetDirectoryAndFile(menu, filePath, String.Empty, Selected);
            return menu;
        }

        private void CollectAssetDirectoryAndFile(GenericMenu menu, string filePath, String subFolder, Action<String> onSelected)
        {
            String[] files = Directory.GetFiles(filePath, "*.asset");
            foreach (String file in files)
            {
                String fileName = Path.GetFileName(file);
                String content = String.IsNullOrEmpty(subFolder) ? fileName : $"{subFolder}/{fileName}";
                menu.AddItem(new GUIContent(content), false, () => { onSelected?.Invoke(file); });
            }
            String[] folders = Directory.GetDirectories(filePath, "*", SearchOption.TopDirectoryOnly);
            foreach (String folder in folders)
            {
                String folderName = Path.GetFileName(folder);
                String newSubFolder = String.IsNullOrEmpty(subFolder) ? folderName : $"{subFolder}/{folderName}";
                CollectAssetDirectoryAndFile(menu, folder, newSubFolder, onSelected);
            }
        }

        private void OnGraphWindowClosed()
        {
            CustomGraphEditor.Destroy();
        }

        private void UpdateGraphValidState(Single deltaTime)
        {
            rootGraph?.UpdateValidState(deltaTime);
        }

    }
}

#endif
