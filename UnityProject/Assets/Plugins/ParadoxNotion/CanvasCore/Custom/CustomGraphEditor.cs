#if UNITY_EDITOR

using System;
using System.IO;
using NodeCanvas.Framework;
using UnityEditor;
using UnityEngine;

namespace NodeCanvas.Editor
{
    public class CustomGraphEditor
    {
        public static void ExportAllQuestGraphData()
        {
            String sourceFolder = Path.Combine(Application.dataPath, $"Local/QuestAsset");
            String targetFolder = Path.Combine(Application.dataPath, $"Res/ConfigData/QuestGraphData");
            String[] files = Directory.GetFiles(sourceFolder, "*.asset", SearchOption.AllDirectories);
            foreach (String file in files)
            {
                ExportQuestGraphDataFunc(file, targetFolder);
            }
            AssetDatabase.Refresh();
        }
        public static void ExportQuestGraphDataFunc(String graphPath, String targetFolder)
        {
            graphPath.Replace('\\', '/');
            String assetPath = graphPath.Substring(graphPath.IndexOf("Assets"));
            Graph graph = AssetDatabase.LoadAssetAtPath<Graph>(assetPath);
            if (graph != null)
            {
                String path = $"{targetFolder}/{graph.name}.json";
                var json = graph.Serialize(null);
                json = ParadoxNotion.Serialization.JSONSerializer.PrettifyJson(json);
                File.WriteAllText(path, json);
            }
        }

        public static CustomGraphEditor Instance
        {
            get
            {
                if (m_instance == null)
                {
                    m_instance = new CustomGraphEditor();
                    m_instance.Init();
                }
                return m_instance;
            }
        }
        private static CustomGraphEditor m_instance;



        private String m_originalSerializedGraph;
        private Graph m_currentGraph;


        public Graph CurrentGraph => m_currentGraph;
        public Boolean Dirty { get; set; }


        public static void Destroy()
        {
            if (m_instance != null)
            {
                m_instance.ResetDirty(m_instance.m_currentGraph);
                m_instance.UnInit();
                m_instance = null;
            }
        }

        public void Reset()
        {
            InitGraphJsonData(m_currentGraph);
        }

        private void Init()
        {
            InitGraphJsonData(GraphEditor.currentGraph);
            //Debug.LogError($"CustomGraphEditor.Init({m_originalSerializedGraph})");
            Graph.onGraphSerialized -= m_instance.OnGraphSerialized;
            Graph.onGraphSerialized += m_instance.OnGraphSerialized;
            Graph.onGraphDeserialized -= m_instance.OnGraphDeserialized;
            Graph.onGraphDeserialized += m_instance.OnGraphDeserialized;
            GraphEditor.onCurrentGraphChanged -= m_instance.OnCurrentGraphChanged;
            GraphEditor.onCurrentGraphChanged += m_instance.OnCurrentGraphChanged;
        }

        private void UnInit()
        {
            m_originalSerializedGraph = String.Empty;
            m_currentGraph = null;
            Graph.onGraphSerialized -= m_instance.OnGraphSerialized;
            Graph.onGraphDeserialized -= m_instance.OnGraphDeserialized;
            GraphEditor.onCurrentGraphChanged -= m_instance.OnCurrentGraphChanged;
        }

        private void InitGraphJsonData(Graph graph)
        {
            Dirty = false;
            m_currentGraph = graph;
            m_originalSerializedGraph = graph != null ? graph.GetSerializedJsonData() : String.Empty;
        }

        private void OnGraphSerialized(Graph graph)
        {
            if (graph != m_currentGraph)
            {
                return;
            }

            String serializedGraph = graph.GetSerializedJsonData();
            if (serializedGraph != m_originalSerializedGraph)
            {
                Dirty = true;
            }
            else
            {
                Dirty = false;
            }
        }

        private void OnGraphDeserialized(Graph graph)
        {
            if (graph != m_currentGraph)
            {
                return;
            }

            String serializedGraph = graph.GetSerializedJsonData();
            if (serializedGraph != m_originalSerializedGraph)
            {
                Dirty = true;
            }
            else
            {
                Dirty = false;
            }
        }

        private void OnCurrentGraphChanged(Graph graph)
        {
            if (Dirty)
            {
                ResetDirty(m_currentGraph);
            }
            InitGraphJsonData(graph);
        }

        private void ResetDirty(Graph graph)
        {
            if (graph == null)
            {
                return;
            }

            if (Dirty)
            {
                if (EditorUtility.DisplayDialog("提示", "是否保存？", "保存", "取消"))
                {
                    GraphEditor.Save(graph);
                    GraphEditor.ShowNotification("已保存");
                }
                else
                {
                    GraphEditor.Revert(graph, m_originalSerializedGraph);
                    GraphEditor.ShowNotification("已放弃保存");
                }
                Dirty = false;
            }
        }
    }
}


#endif
