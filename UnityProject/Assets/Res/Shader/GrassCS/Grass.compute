//#define SHOW_TRAIL_TEXTURE
#pragma kernel Main

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "GrassData.hlsl"
#include "Math.hlsl"

//float _MipLevel;

float4 _CullDistance; // LOD0, LOD1, LOD2, Cull
float4 _CullRate;

float4x4 _FieldWorldMatrix; // 草场的世界矩阵

uint _NumOfSeed; // 草的数量
float _Springiness; // 草的弹性

// mesh bounds min point
float4 _BoundMin;

// mesh bounds max point
float4 _BoundMax;

// 摄像机位置，用于判断LOD。
float3 _CameraPosition;

// Scale。
float3 _GrassScale;

// WorldViewProjection Matrix
float4x4 _CameraViewProj;

Texture2D _HiZTexture;
SamplerState samplerPointClamp;

StructuredBuffer<float4> _SeedBuffer; // 草的位置（草场内的局部坐标）
RWStructuredBuffer<float> _TrampleIntensityBuffer; // 踩踏力度

AppendStructuredBuffer<GrassData> _GrassDataBuffer; // 草的实例
RWStructuredBuffer<int> _IndirectArgsBuffer; // 草的实例数量

StructuredBuffer<float4> _TramplerDataBuffer; // 踩踏者数据（xyz：踩踏者的世界坐标；w：踩踏者的影响半径）
int _NumOfTrampler;

#ifdef SHOW_TRAIL_TEXTURE
float2 _GrasslandSize;
float3 _GrasslandOrigin;
float2 _TrailTextureSize;
RWTexture2D<float> _TrailTexture;
#endif

// 计算每个草的世界矩阵
float4x4 GetGrassMatrix(float3 positionWS)
{
    // 根据位置随机缩放，区间[0.5, 1.5]。
    float s = lerp(0.5, 1.5, rand3dTo1d(positionWS));
    float3 scale = _GrassScale * s;
    float4x4 translation = float4x4
        (
            scale.x, 0, 0, positionWS.x,
            0, scale.y, 0, positionWS.y,
            0, 0, scale.z, positionWS.z,
            0, 0, 0, 1
        );

    // 根据位置绕Y轴随机旋转，区间[-180, 180]。
    float4x4 rotation = AngleAxis4x4(rand3dTo1d(positionWS) * PI, float3(0, 1, 0));

    return mul(translation, rotation);
}

float UpdateTrampleIntensity(int grassIndex, float3 grassPositionWS)
{
    float intensity = _TrampleIntensityBuffer[grassIndex]; // 读取上一帧的踩踏力度
    for (int i = 0; i < _NumOfTrampler; i++)
    {
        float3 tramplerPosition = _TramplerDataBuffer[i].xyz;
        float trampleRadius = _TramplerDataBuffer[i].w;

        float d = distance(grassPositionWS, tramplerPosition);
        if (d < trampleRadius) // 如果在踩踏范围内
        {
            intensity += 1 - saturate(d / trampleRadius); // 叠加踩踏力度
        }
    }
    intensity = saturate(intensity) / (1.0 + _Springiness); // 逐帧衰减
    _TrampleIntensityBuffer[grassIndex] = intensity; // 保存当前帧的踩踏力度

#ifdef SHOW_TRAIL_TEXTURE // 具象化踩踏力度
    int2 uv = int2((grassPositionWS.xz - _GrasslandOrigin.xz) * _TrailTextureSize / _GrasslandSize); // 根据草在草场中的位置计算uv
    _TrailTexture[uv] = intensity; // 将踩踏力度保存到贴图里
#endif
    
    return intensity;
}

// 判断Clip空间的顶点是否在Frustum内
uint FrustumBoundClipPoint(float4 clipPos)
{
    return (clipPos.z > clipPos.w || clipPos.x < -clipPos.w || clipPos.x > clipPos.w || clipPos.y < -clipPos.w || clipPos.y > clipPos.w) ? 0 : 1;
}

float MaxDepth(float x, float y, float z, float w)
{
#if UNITY_REVERSED_Z
    return min(min(x, y), min(z, w));
#else
    return max(max(x, y), max(z, w));
#endif
}

// 根据距离插值剔除比例
float InterpolateRate(float distance, float startDistance, float endDistance, float startRate, float endRate)
{
    float t = (distance - startDistance) / (endDistance - startDistance);
    return lerp(startRate, endRate, t);
}

[numthreads(64, 1, 1)]
void Main(uint3 id : SV_DispatchThreadID)
{
    uint seedIndex = id.x;
    if (seedIndex >= _NumOfSeed) // 每个线程处理一个种子，生成一棵草。
    {
        return;
    }
    
    float3 positionWS = mul(_FieldWorldMatrix, _SeedBuffer[seedIndex]).xyz;
    float2 rnd = float2(positionWS.x, positionWS.z);
    float distance = positionWS.z - _CameraPosition.z; // 草到摄像机的距离
    if (distance > _CullDistance.w) // Cull
    {
        return;
    }
    else if (distance > _CullDistance.z) // LOD2
    {
        float r = InterpolateRate(distance, _CullDistance.z, _CullDistance.w, _CullRate.z, _CullRate.w);
        if (rand2dTo1d(rnd) >= r)
        {
            return;
        }
    }
    else if (distance > _CullDistance.y) // LOD1
    {
        float r = InterpolateRate(distance, _CullDistance.y, _CullDistance.z, _CullRate.y, _CullRate.z);
        if (rand2dTo1d(rnd) > r)
        {
            return;
        }
    }
    else // LOD0
    {
        float r = InterpolateRate(distance, _CullDistance.x, _CullDistance.y, _CullRate.x, _CullRate.y);
        if (rand2dTo1d(rnd) > r)
        {
            return;
        }
    }

    float4x4 worldMatrix = GetGrassMatrix(positionWS);
    float trampleIntensity = UpdateTrampleIntensity(seedIndex, positionWS);

    float4 minPos = mul(worldMatrix, _BoundMin);
    float4 maxPos = mul(worldMatrix, _BoundMax);

    float4 boxCorners[8] =
    {
        float4(minPos.x, minPos.y, minPos.z, 1.0),
        float4(minPos.x, minPos.y, maxPos.z, 1.0),
        float4(minPos.x, maxPos.y, minPos.z, 1.0),
        float4(minPos.x, maxPos.y, maxPos.z, 1.0),
        float4(maxPos.x, minPos.y, minPos.z, 1.0),
        float4(maxPos.x, minPos.y, maxPos.z, 1.0),
        float4(maxPos.x, maxPos.y, minPos.z, 1.0),
        float4(maxPos.x, maxPos.y, maxPos.z, 1.0)
    }; // 世界坐标系的包围盒

    //float4 clipPos = mul(_CameraViewProj, boxCorners[0]);
    //uint inFrustum = FrustumBoundClipPoint(clipPos);

    //clipPos.xyz = clipPos.xyz / clipPos.w;
    //float clipMinX = clipPos.x;
    //float clipMaxX = clipPos.x;
    //float clipMinY = clipPos.y;
    //float clipMaxY = clipPos.y;
    //float clipMinZ = clipPos.z;
    //float clipMaxZ = clipPos.z;

    float4 clipPos;
    uint inFrustum = 0;
    [unroll]
    for (int i = 1; i < 8; i++)
    {
        clipPos = mul(_CameraViewProj, boxCorners[i]);
        inFrustum = saturate(inFrustum + FrustumBoundClipPoint(clipPos)); // 不考虑Frustum在AABB内的情况

        //clipPos.xyz /= clipPos.w;
        //clipMinX = min(clipPos.x, clipMinX);
        //clipMaxX = max(clipPos.x, clipMaxX);
        //clipMinY = min(clipPos.y, clipMinY);
        //clipMaxY = max(clipPos.y, clipMaxY);
        //clipMinZ = min(clipPos.z, clipMinZ);
        //clipMaxZ = max(clipPos.z, clipMaxZ);
    }

    if (inFrustum == 0) // Frustum Culling
    {
        return;
    }

//    float2 bottomLeft = float2(clipMinX, clipMinY) * 0.5 + 0.5;
//    float2 topRight = float2(clipMaxX, clipMaxY) * 0.5 + 0.5;

//    float width = (topRight.x - bottomLeft.x) * _ScreenParams.x;
//    float height = (topRight.y - bottomLeft.y) * _ScreenParams.x;

//    float level = ceil(log2(max(width, height) / 2.0));
//    //if (level != _MipLevel) return; // 调试用途，查看哪些草与哪个MipLevel作比较。

//    float4 samples;
//    samples.x = _HiZTexture.SampleLevel(samplerPointClamp, float2(bottomLeft.x, bottomLeft.y), level).r;
//    samples.y = _HiZTexture.SampleLevel(samplerPointClamp, float2(bottomLeft.x, topRight.y), level).r;
//    samples.z = _HiZTexture.SampleLevel(samplerPointClamp, float2(topRight.x, bottomLeft.y), level).r;
//    samples.w = _HiZTexture.SampleLevel(samplerPointClamp, float2(topRight.x, topRight.y), level).r;
//    float maxDepth = MaxDepth(samples.x, samples.y, samples.z, samples.w);

//    // Occlusion Culling
//#if UNITY_REVERSED_Z
//    float depth = clipMaxZ;
//    if (depth < maxDepth)   // DX Depth近大远小
//    {
//        return;
//    }
//#else
//    float depth = clipMinZ * 0.5 + 0.5;
//    if (depth > maxDepth)   // GL Depth近小远大
//    {
//        return;
//    }
//#endif

    GrassData grassData;
    grassData.worldMatrix = worldMatrix;
    grassData.trampleIntensity = trampleIntensity;

    _GrassDataBuffer.Append(grassData);

    InterlockedAdd(_IndirectArgsBuffer[1], 1); // Instance Count
}
