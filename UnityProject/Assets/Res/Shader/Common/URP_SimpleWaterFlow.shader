Shader "Phoenix/URP/URP_SimpleWaterFlow"
{
    Properties
    {
        [Header(Wave geometry)][Space]
        [KeywordEnum(None, Round, Grid, Pointy)] _WaveMode ("Shape{Wave}", Float)   = 1.0
        _WaveSpeed("Speed{Wave}", Float) = 0.5
        _WaveAmplitude("Amplitude{Wave}", Float)                                    = 0.25
        _WaveFrequency("Frequency{Wave}", Float)                                    = 1.0
        _WaveDirection("Direction{Wave}", Range(-1.0, 1.0))                         = 0
        _WaveNoise("Noise{Wave}", Range(0, 1))                                      = 0.25

        _FadeDistance("FadeDistance(Shallow depth)", Float)                         = 0.5
        _WaterDepth("Water Depth(Gradient size)", Float)                            = 5.0

        [Enum(UnityEngine.Rendering.BlendMode)]_SrcFactor("SrcFactor",int)          = 0
        [Enum(UnityEngine.Rendering.BlendMode)]_DstFactor("DstFactor",int)          = 0

        [Space(20)][Header(SquareMask)][Space(10)]
        _SquareMask("Square:Size(XY)Pos(ZW)",vector)                                = (1,1,1,1)
        _MaskPow("Mask Range",float)                                                = 1.0
        [Toggle(_DISPALYMASK_ON)] _DisplayMask ("Display Mask", Float)              = 0

        [Space(20)][Header(TexMask)][Space(10)]
        _NoiseMap("NoiseMap",2D)                                                    = "white" {}
        [Toggle(_INVERTMASK_ON)] _Invert ("Invert", Float)                          = 0
        _NoisePow("Noise Range",float)                                              = 1.0
        _NoiseMaskMap("NoiseMaskMap",2D)                                            = "white" {}
        _NoiseMaskIntensity("NoiseMaskIntensity", Range(0, 5.0))                    = 1.0

        _VertexColorIntensity("VertexColorIntensity", Range(0, 100))                = 1.0

        [Space(20)][Header(DistorMask)][Space(10)]
        _DistorMap("DistorMaskMap",2D)                                              = "white" {}
        [Toggle(_INVERTMASK_ON)] _Invert ("Invert", Float)                          = 0
        _DistorPow("DistorMask Range",float)                                        = 1.0

        _DistorIntensity("DistorIntensity",float)                                   = 1.0

        [HDR]_TintColor("Tint Color",color)                                         = (1,1,1,1)
        _NoiseDistorSpeed("NoiseDistorSpeed", Vector)                               = (0.5, 0, 0.5, 0)
        _EmissiveScale("Emissive Scale",Float)                                      = 1

        [Space(20)][Header(Distort)][Space(10)]
        [Toggle(_DISTORT_ON)] _Distort ("Distort", Float)                           = 0

		[Space(20)]
		_Cutoff ("Transparent", Range(0,1))                                         = 0.5
    }
    SubShader
    {
        Tags { "Queue" = "Transparent" "RenderType" = "Transparent" "IgnoreProjector" = "True" "RenderPipeline" = "UniversalPipeline" }
        LOD 100
        Blend [_SrcFactor] [_DstFactor]

        Pass
        {
            Name "Unlit"
            HLSLPROGRAM
            // Required to compile gles 2.0 with standard srp library
            #pragma prefer_hlslcc gles
            #pragma exclude_renderers d3d11_9x
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fog
            #pragma shader_feature_local_fragment _INVERTMASK_ON
            #pragma shader_feature_local_fragment _DISTORT_ON
            #pragma shader_feature_local_fragment _DISPALYMASK_ON
            #pragma shader_feature_local _COLORMODE_LINEAR _COLORMODE_GRADIENT_TEXTURE
            #pragma shader_feature_local _FOAMMODE_NONE _FOAMMODE_GRADIENT_NOISE _FOAMMODE_TEXTURE
            #pragma shader_feature_local _WAVEMODE_NONE _WAVEMODE_ROUND _WAVEMODE_GRID _WAVEMODE_POINTY

            //#include "Common.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareOpaqueTexture.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"

            struct Attributes
            {
                float4 positionOS       : POSITION;
                float2 uv               : TEXCOORD0;
                float3 normalOS         : NORMAL;
                float4 tangentOS        : TANGENT;
                float4 vertexColor      : COLOR;

                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS       : SV_POSITION;
                float2 uv               : TEXCOORD0;
                float4 screenPosition   : TEXCOORD1;
                //float fogCoord          : TEXCOORD1;

                float waveHeight        : TEXCOORD2;
                float3 worldNormal      : TEXCOORD3; // World space.
                float3 viewDir          : TEXCOORD4; // World space.
                float3 positionWS       : TEXCOORD6;
                float4 vertexColor      : COLOR;

                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
            };

            CBUFFER_START(UnityPerMaterial)
            float _FadeDistance, _WaterDepth;
            half _WaveFrequency, _WaveAmplitude, _WaveSpeed, _WaveDirection, _WaveNoise;
            half4 _TintColor;
            half4 _SquareMask;
            float _Cutoff;
            half _EmissiveScale,_NoisePow,_MaskPow;
            float _NoiseMaskIntensity;
            float _VertexColorIntensity;
			//half _noiseSpeedU,_noiseSpeedV;
            float4 _NoiseDistorSpeed;
            float _DistorIntensity;
            CBUFFER_END
            TEXTURE2D (_NoiseMap);SAMPLER(sampler_NoiseMap);float4 _NoiseMap_ST;
            TEXTURE2D (_NoiseMaskMap);SAMPLER(sampler_NoiseMaskMap);float4 _NoiseMaskMap_ST;
            TEXTURE2D (_DistorMap);SAMPLER(sampler_DistorMap);float4 _DistorMap_ST;

            half sdBox( half2 p, half2 b )
            {
                half2 d = abs(p)-b;
                return length(max(d,0.0)) + min(max(d.x,d.y),0.0);
            }

            float2 GradientNoise_Dir(float2 p)
            {
                // Permutation and hashing used in webgl-nosie goo.gl/pX7HtC
                // 3d0a9085-1fec-441a-bba6-f1121cdbe3ba
                p = p % 289;
                float x = (34 * p.x + 1) * p.x % 289 + p.y;
                x = (34 * x + 1) * x % 289;
                x = frac(x / 41) * 2 - 1;
                return normalize(float2(x - floor(x + 0.5), abs(x) - 0.5));
            }

            float GradientNoise(float2 UV, float Scale)
            {
                const float2 p = UV * Scale;
                const float2 ip = floor(p);
                float2 fp = frac(p);
                const float d00 = dot(GradientNoise_Dir(ip), fp);
                const float d01 = dot(GradientNoise_Dir(ip + float2(0, 1)), fp - float2(0, 1));
                const float d10 = dot(GradientNoise_Dir(ip + float2(1, 0)), fp - float2(1, 0));
                const float d11 = dot(GradientNoise_Dir(ip + float2(1, 1)), fp - float2(1, 1));
                fp = fp * fp * fp * (fp * (fp * 6 - 15) + 10);
                return lerp(lerp(d00, d01, fp.y), lerp(d10, d11, fp.y), fp.x) + 0.5;
            }

            inline float DepthFade(float2 uv, Varyings i)
            {
                //unity_OrthoParams	float4	x 是正交摄像机的宽度，y 是正交摄像机的高度，z 未使用，w 在摄像机为正交模式时是 1.0，而在摄像机为透视模式时是 0.0。
                const float is_ortho = unity_OrthoParams.w;
                const float is_persp = 1.0 - unity_OrthoParams.w;

                #if UNITY_REVERSED_Z
                    real depth_packed = SampleSceneDepth(uv);
                #else
                    // Adjust z to match NDC for OpenGL
                    real depth_packed = lerp(UNITY_NEAR_CLIP_VALUE, 1, SampleSceneDepth(uv));
                #endif
                // const float depth_packed = SampleSceneDepth(uv);

                //_ProjectionParams	float4	x 是 1.0（如果当前使用翻转投影矩阵进行渲染，则为 –1.0），y 是摄像机的近平面，z 是摄像机的远平面，w 是远平面的倒数。
                //_ZBufferParams	float4	用于线性化 Z 缓冲区值。x 是 (1-远/近)，y 是 (远/近)，z 是 (x/远)，w 是 (y/远)。
                // Separately handles orthographic and perspective cameras.
                // const float scene_depth = lerp(_ProjectionParams.z, _ProjectionParams.y, depth_packed) * is_ortho +
                //     LinearEyeDepth(depth_packed, _ZBufferParams) * is_persp;  
                // const float surface_depth = lerp(_ProjectionParams.z, _ProjectionParams.y, i.screenPosition.z) *
                //     is_ortho + i.screenPosition.w * is_persp;
                      
                const float scene_depth = LinearEyeDepth(depth_packed, _ZBufferParams);
                const float surface_depth = i.screenPosition.w;

                const float water_depth = scene_depth - surface_depth;
                // return water_depth;

                return saturate((water_depth - _FadeDistance) / _WaterDepth);
            }

            inline float SineWave(float3 pos, float offset)
            {
                return sin(
                    offset + _Time.z * _WaveSpeed + (pos.x * sin(offset + _WaveDirection * PI) + pos.z *
                        cos(offset + _WaveDirection * PI)) * _WaveFrequency);
            }

            inline float WaveHeight(float2 texcoord, float3 position)
            {
                float s = 0;

                #if !defined(_WAVEMODE_NONE)
                    float2 noise_uv = texcoord * _WaveFrequency;
                    float noise01 = GradientNoise(noise_uv, 1.0);
                    float noise = (noise01 * 2.0 - 1.0) * _WaveNoise;

                    s = SineWave(position, noise);

                #if defined(_WAVEMODE_GRID)
                        s *= SineWave(position, HALF_PI + noise);
                #endif

                #if defined(_WAVEMODE_POINTY)
                        s = 1.0 - abs(s);
                #endif
                #endif

                return s;
            }

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;

                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_TRANSFER_INSTANCE_ID(v, o);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                // Vertex animation.
                const float3 originalPositionWS = TransformObjectToWorld(v.positionOS.xyz);
                const float s = WaveHeight(v.uv, originalPositionWS);
                o.waveHeight = s;
                o.positionWS = originalPositionWS;
                o.positionWS.y += s * _WaveAmplitude;
                
                o.positionCS = TransformWorldToHClip(o.positionWS);
                o.screenPosition = ComputeFogFactor(o.positionCS.z);
                o.uv = TRANSFORM_TEX(v.uv, _NoiseMap);
                o.vertexColor = v.vertexColor;
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half4 color = 0;
                half alpha = 1.0;
                half mask = saturate(pow(1-sdBox(i.uv-_SquareMask.zw,_SquareMask.xy),_MaskPow));
                #if defined(_DISPALYMASK_ON)
                    return mask;
                #endif

                float2 noiseSpeed = _NoiseDistorSpeed.xy * _Time.y;
                float2 distorSpeed = _NoiseDistorSpeed.zw * _Time.y;
                float2 noiseMaskSpeed = _NoiseDistorSpeed.xy * float2(sin(_Time.y*0.005)*0.2, sin(_Time.y*0.01)*0.2) * _Time.y * 0.1;
                half4 noiseMaskMap =1;

                #if defined(_INVERTMASK_ON)
                    half4 tex01 = 1-SAMPLE_TEXTURE2D(_NoiseMap, sampler_NoiseMap, i.uv + noiseSpeed);
                #else
                    half4 tex01 = SAMPLE_TEXTURE2D(_NoiseMap, sampler_NoiseMap, i.uv + noiseSpeed);
                #endif

                #if defined(_DISTORT_ON)
                    half4 DistorMap = SAMPLE_TEXTURE2D(_DistorMap, sampler_DistorMap, i.uv * _DistorMap_ST.xy + distorSpeed * float2(-0.1, 0.1));

                    //noiseSpeed = _NoiseDistorSpeed.xy * _Time.y *DistorMap.r ;
                    
                    half4 tex02 = SAMPLE_TEXTURE2D(_NoiseMap, sampler_NoiseMap, i.uv  + noiseSpeed * float2(-1.7, 1.43) - DistorMap.xy * half2(-1,1) * _DistorIntensity );
                    noiseMaskMap.r = SAMPLE_TEXTURE2D(_NoiseMaskMap, sampler_NoiseMaskMap, i.uv *_NoiseMaskMap_ST.xy +_NoiseMaskMap_ST.zw + noiseMaskSpeed - DistorMap.xy * half2(-0.33, 0.33) * _DistorIntensity).r;
                    noiseMaskMap = saturate(noiseMaskMap * _NoiseMaskIntensity).r;
                    color = pow(min(tex01,tex02), _NoisePow) * mask * noiseMaskMap * half4(i.vertexColor.rgb * _VertexColorIntensity,1) * i.vertexColor.a;
                    //color = lerp(color ,pow(min(tex01,tex02), _NoisePow) * mask * noiseMaskMap , i.vertexColor.a);

                #else
                    color = pow(tex01,_NoisePow)*mask;
                #endif

                color *= _TintColor * _EmissiveScale * mask;
                color.w = saturate((color.x - _Cutoff));
                //color.rgba = half4(noiseMaskMap.rrr,1);
                return color;
            }
            ENDHLSL
        }
    }

    // FallBack "Hidden/Shader Graph/FallbackError"
}
