
Shader "Phoenix/SceneGrid/DangerGridShader" {
    Properties {
        _MainMask ("MainMask", 2D) = "white" {}
        _Col ("Col", Float ) = 4
        _Row ("Row", Float ) = 4
        _MainColor ("MainColor", Color) = (0.5,0.5,0.5,1)
        _LineLolor ("LineLolor", Color) = (0.5,0.5,0.5,1)
        _HightColor ("HightColor", Color) = (0.5,0.5,0.5,1)
        _Padding ("Padding", Range(-0.1, 0.1)) = 0
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        LOD 100
        Pass 
        {
            Name "FORWARD"
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            //#pragma only_renderers d3d9 d3d11 glcore gles gles3 metal d3d11_9x xboxone ps4 
            #pragma target 3.0
            CBUFFER_START(UnityPerMaterial)
				float4 _MainMask_ST;
				float _Col;
				float _Row;
                float4 _MainColor;
                float4 _LineLolor;
                float4 _HightColor;
				float _Padding;
			CBUFFER_END

            uniform sampler2D _MainMask; 
            // uniform float4 _MainMask_ST;
            // uniform float _Col;
            // uniform float _Row;
            
            // uniform float4 _MainColor;
            // uniform float4 _LineLolor;
            // uniform float4 _HightColor;
            // uniform float _Padding;

            struct VertexInput 
            {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };

            struct VertexOutput 
            {
                float4 pos1 : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 posWorld : TEXCOORD1;
                float4 vertexColor : COLOR;
            };

            VertexOutput vert (VertexInput v) 
            {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                o.pos1 = UnityObjectToClipPos( v.vertex );
                return o;
            }
            
            float2 Rotate( float2 uv01 , float colorb)
            {
                float2 pivot = float2(0.5, 0.5);
                float cosAngle = cos(radians(colorb));
                float sinAngle = sin(radians(colorb));
                float2x2 rot = float2x2(cosAngle, -sinAngle, sinAngle, cosAngle);
                float2 uv1 =  uv01 - pivot;
                float2 uv2 =  mul(rot, uv1);
                float2 uv3 =  uv2 + pivot;
                return uv3;
            }

            float4 frag(VertexOutput i) : COLOR
            {
                float2 tempUV = (float2(i.vertexColor.r, i.vertexColor.g) + Rotate(i.uv0, i.vertexColor.b) * (1.0 - _Padding)) / float2(_Col, _Row);
                float4 _MainMask_var = tex2D(_MainMask, TRANSFORM_TEX(tempUV, _MainMask));

                float3 finalColor = _MainColor.rgb + _MainMask_var.g * _LineLolor.rgb + _MainMask_var.b * _HightColor.rgb;
                float alpha = saturate((_MainColor.a * _MainMask_var.r + _LineLolor.a * _MainMask_var.g) * (abs(cos(_Time.b) * 0.5) + 0.5));

                fixed4 result = fixed4(_MainMask_var.rgb, _MainMask_var.a);
                return result;
            }
            ENDCG
        }
    }
}
