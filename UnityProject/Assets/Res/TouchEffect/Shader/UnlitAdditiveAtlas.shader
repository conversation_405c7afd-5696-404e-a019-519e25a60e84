// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:0,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:False,igpj:True,qofs:2,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:False,fsmp:False;n:type:ShaderForge.SFN_Final,id:5760,x:33183,y:32715,varname:node_5760,prsc:2|emission-4792-OUT;n:type:ShaderForge.SFN_Tex2d,id:2741,x:31415,y:32332,ptovrint:False,ptlb:MainTex,ptin:_MainTex,varname:_MainTex,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Tex2d,id:4327,x:31343,y:33335,ptovrint:False,ptlb:AlphaTex,ptin:_AlphaTex,varname:_AlphaTex,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Multiply,id:3789,x:32543,y:32720,varname:node_3789,prsc:2|A-921-OUT,B-1521-RGB,C-1521-A,D-6255-RGB,E-8131-OUT;n:type:ShaderForge.SFN_VertexColor,id:1521,x:32155,y:32713,varname:node_1521,prsc:2;n:type:ShaderForge.SFN_Color,id:6255,x:32141,y:33015,ptovrint:False,ptlb:Color,ptin:_Color,varname:_Color,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:1,c2:1,c3:1,c4:1;n:type:ShaderForge.SFN_SwitchProperty,id:5753,x:32141,y:33419,ptovrint:False,ptlb:EnableAlpha,ptin:_EnableAlpha,varname:_EnableAlpha,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-3882-OUT,B-9044-OUT;n:type:ShaderForge.SFN_Vector1,id:3882,x:31890,y:33328,varname:node_3882,prsc:2,v1:1;n:type:ShaderForge.SFN_Multiply,id:3467,x:32625,y:32976,varname:node_3467,prsc:2|A-3789-OUT,B-5753-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:921,x:32155,y:32577,ptovrint:False,ptlb:UseMainTex_RGBChannel,ptin:_UseMainTex_RGBChannel,varname:_UseMainTex_RGBChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-2741-RGB,B-6163-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:4384,x:31813,y:32563,ptovrint:False,ptlb:UseMainTex_RChannel,ptin:_UseMainTex_RChannel,varname:_UseMainTex_RChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:True|A-7229-OUT,B-2741-R;n:type:ShaderForge.SFN_Vector1,id:7229,x:31386,y:32675,varname:node_7229,prsc:2,v1:1;n:type:ShaderForge.SFN_SwitchProperty,id:6150,x:31813,y:32712,ptovrint:False,ptlb:UseMainTex_GChannel,ptin:_UseMainTex_GChannel,varname:_UseMainTex_GChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-7229-OUT,B-2741-G;n:type:ShaderForge.SFN_SwitchProperty,id:8553,x:31813,y:32869,ptovrint:False,ptlb:UseMainTex_BChannel,ptin:_UseMainTex_BChannel,varname:_UseMainTex_BChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-7229-OUT,B-2741-B;n:type:ShaderForge.SFN_Multiply,id:6163,x:31993,y:32697,varname:node_6163,prsc:2|A-4384-OUT,B-6150-OUT,C-8553-OUT;n:type:ShaderForge.SFN_Multiply,id:9044,x:31890,y:33407,varname:node_9044,prsc:2|A-8950-OUT,B-1012-OUT,C-7428-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:1012,x:31681,y:33417,ptovrint:False,ptlb:UseAlphaTex_GChannel,ptin:_UseAlphaTex_GChannel,varname:_UseAlphaTex_GChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-7294-OUT,B-4327-G;n:type:ShaderForge.SFN_SwitchProperty,id:8950,x:31681,y:33280,ptovrint:False,ptlb:UseAlphaTex_RChannel,ptin:_UseAlphaTex_RChannel,varname:_UseAlphaTex_RChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:True|A-7294-OUT,B-4327-R;n:type:ShaderForge.SFN_SwitchProperty,id:7428,x:31681,y:33561,ptovrint:False,ptlb:UseAlphaTex_BChannel,ptin:_UseAlphaTex_BChannel,varname:_UseAlphaTex_BChannel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-7294-OUT,B-4327-B;n:type:ShaderForge.SFN_Vector1,id:7294,x:31316,y:33636,varname:node_7294,prsc:2,v1:1;n:type:ShaderForge.SFN_Slider,id:8131,x:32406,y:32621,ptovrint:False,ptlb:Intensive,ptin:_Intensive,varname:_Intensive,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,min:0,cur:1,max:10;n:type:ShaderForge.SFN_Power,id:4792,x:32905,y:32942,varname:node_4792,prsc:2|VAL-3467-OUT,EXP-4944-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4944,x:32583,y:33142,ptovrint:False,ptlb:ColorPower,ptin:_ColorPower,varname:_ColorPower,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_SwitchProperty,id:2610,x:30652,y:32678,ptovrint:False,ptlb:node_2610,ptin:_node_2610,varname:_node_2610,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False;proporder:6255-8131-4944-2741-921-4384-6150-8553-5753-4327-8950-1012-7428;pass:END;sub:END;*/

Shader "Vam/Unlit AdditiveAtlas" {
    Properties {
        [HDR]_Color ("Color", Color) = (1,1,1,1)
        _Intensive ("Intensive", Range(0, 10)) = 1
        _ColorPower ("ColorPower", Float ) = 1
        _MainTex ("MainTex", 2D) = "white" {}
        _UseMainTex_RGBChannel ("UseMainTex_RGBChannel", Float ) = 0
        _UseMainTex_RChannel ("UseMainTex_RChannel", Float ) = 0
        _UseMainTex_GChannel ("UseMainTex_GChannel", Float ) = 1
        _UseMainTex_BChannel ("UseMainTex_BChannel", Float ) = 1
        _EnableAlpha ("EnableAlpha", Float ) = 1
        _AlphaTex ("AlphaTex", 2D) = "white" {}
        _UseAlphaTex_RChannel ("UseAlphaTex_RChannel", Float ) = 0
        _UseAlphaTex_GChannel ("UseAlphaTex_GChannel", Float ) = 1
        _UseAlphaTex_BChannel ("UseAlphaTex_BChannel", Float ) = 1
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent+2"
            "RenderType"="Transparent"
            "RenderPipeline" = "UniversalPipeline"
        }
        LOD 100
        Pass {
            Name "FORWARD"
            Tags {
            }
            Blend One One
            Cull Off
            ZWrite Off
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #pragma multi_compile_fwdbase
            #pragma target 3.0
            uniform sampler2D _MainTex; uniform float4 _MainTex_ST;
            uniform sampler2D _AlphaTex; uniform float4 _AlphaTex_ST;
            uniform float4 _Color;
            uniform half _EnableAlpha;
            uniform half _UseMainTex_RGBChannel;
            uniform half _UseMainTex_RChannel;
            uniform half _UseMainTex_GChannel;
            uniform half _UseMainTex_BChannel;
            uniform half _UseAlphaTex_GChannel;
            uniform half _UseAlphaTex_RChannel;
            uniform half _UseAlphaTex_BChannel;
            uniform float _Intensive;
            uniform float _ColorPower;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.pos = TransformObjectToHClip( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 _MainTex_var = tex2D(_MainTex,TRANSFORM_TEX(i.uv0, _MainTex));
                float node_7229 = 1.0;
                float node_7294 = 1.0;
                float4 _AlphaTex_var = tex2D(_AlphaTex,TRANSFORM_TEX(i.uv0, _AlphaTex));
                float3 emissive = pow(((lerp( _MainTex_var.rgb, (lerp( node_7229, _MainTex_var.r, _UseMainTex_RChannel )*lerp( node_7229, _MainTex_var.g, _UseMainTex_GChannel )*lerp( node_7229, _MainTex_var.b, _UseMainTex_BChannel )), _UseMainTex_RGBChannel )*i.vertexColor.rgb*i.vertexColor.a*_Color.rgb*_Intensive)*lerp( 1.0, (lerp( node_7294, _AlphaTex_var.r, _UseAlphaTex_RChannel )*lerp( node_7294, _AlphaTex_var.g, _UseAlphaTex_GChannel )*lerp( node_7294, _AlphaTex_var.b, _UseAlphaTex_BChannel )), _EnableAlpha )),_ColorPower);
                float3 finalColor = emissive;
                return half4(finalColor,1);
            }
            ENDHLSL
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
