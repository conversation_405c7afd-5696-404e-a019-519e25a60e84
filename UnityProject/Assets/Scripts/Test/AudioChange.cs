using UnityEngine;
using System.Collections;

public class MusicSwitcher : MonoBehaviour
{
    public AudioSource[] musicSources; // 将所有的音频源拖到这个数组中
    public int musicIndex; // 当前触发区对应的音频源索引
    public float fadeDuration = 1.0f; // 淡入淡出时间

    private static AudioSource currentSource = null; // 当前正在播放的音频源
    private Coroutine fadeCoroutine;
    private static bool firstSwitch = true; // 是否是首次切换音乐

    private static bool isInitialized = false; // 检查是否已经初始化

    private void Awake()
    {
        if (!isInitialized)
        {
            isInitialized = true;
            DontDestroyOnLoad(gameObject); // 保留这个gameObject，防止在场景切换时被销毁
            PreloadMusicResources();
        }
    }

    // 预加载所有音乐资源
    private void PreloadMusicResources()
    {
        foreach (AudioSource source in musicSources)
        {
            if (source.clip != null)
            {
                source.clip.LoadAudioData(); // 预加载音频数据到内存中
            }
        }
    }

    // 当玩家进入触发区域时调用
    private void OnTriggerEnter(Collider other)
    {
        // 检查碰撞体是否是玩家
        if (other.CompareTag("Player"))
        {
            // 检查当前区域的音乐是否没有在播放
            if (currentSource == null || currentSource != musicSources[musicIndex])
            {
                SwitchMusic(musicIndex);
            }
        }
    }

    // 切换音乐的方法
    private void SwitchMusic(int index)
    {
        if (fadeCoroutine != null)
        {
            StopCoroutine(fadeCoroutine);
        }

        if (index >= 0 && index < musicSources.Length)
        {
            fadeCoroutine = StartCoroutine(FadeMusic(musicSources[index]));
        }
    }

    private IEnumerator FadeMusic(AudioSource newSource)
    {
        // 如果是第一次切换，先淡出所有正在播放的音源
        if (firstSwitch)
        {
            foreach (AudioSource source in musicSources)
            {
                if (source.isPlaying)
                {
                    float startVolume = source.volume;

                    while (source.volume > 0)
                    {
                        source.volume -= startVolume * Time.deltaTime / fadeDuration;
                        yield return null;
                    }

                    source.Stop();
                    source.volume = startVolume; // 重置音量，以便下次播放
                }
            }

            firstSwitch = false;
        }
        else
        {
            // 淡出当前正在播放的音源（如果有）
            if (currentSource != null)
            {
                float startVolume = currentSource.volume;

                while (currentSource.volume > 0)
                {
                    currentSource.volume -= startVolume * Time.deltaTime / fadeDuration;
                    yield return null;
                }

                currentSource.Stop();
                currentSource.volume = startVolume; // 重置音量，以便下次播放
            }
        }

        // 淡入新音乐
        newSource.volume = 0;
        newSource.Play();
        currentSource = newSource; // 更新当前正在播放的音频源

        while (newSource.volume < 1)
        {
            newSource.volume += Time.deltaTime / fadeDuration;
            yield return null;
        }

        newSource.volume = 1; // 确保音量完全饱和
    }
}