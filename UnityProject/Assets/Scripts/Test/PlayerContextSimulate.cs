
using System;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

public class PlayerContextSimulate : Singleton<PlayerContextSimulate>
{
    /*
    protected override void OnInit()
    {
        GamePlayerContext.instance.m_questHandler.EventOnQuestAccept += EventOnQuestAccept;
        GamePlayerContext.instance.m_questHandler.EventOnQuestProgressUpdate += EventOnQuestProgressUpdate;
        GamePlayerContext.instance.m_questHandler.EventOnQuestStatusChange += EventOnQuestStatusChange;
        GamePlayerContext.instance.m_questHandler.EventOnQuestCompleted += EventOnQuestCompleted;

        TickManager.instance.RegisterGlobalTick(OnTick);
    }


    protected override void OnUnInit()
    {
        GamePlayerContext.instance.m_questHandler.EventOnQuestAccept -= EventOnQuestAccept;
        GamePlayerContext.instance.m_questHandler.EventOnQuestProgressUpdate -= EventOnQuestProgressUpdate;
        GamePlayerContext.instance.m_questHandler.EventOnQuestStatusChange -= EventOnQuestStatusChange;
        GamePlayerContext.instance.m_questHandler.EventOnQuestCompleted -= EventOnQuestCompleted;

        TickManager.instance.UnRegisterGlobalTick(OnTick);
    }


    protected void OnTick(TimeSlice ts)
    {
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha0))
        {
            SimulateAcceptQuest();
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha1))
        {
            GamePlayerContext.instance.m_eventHandler.PushEvent(new GameEventDefault(GameEventId.TalkEnd, 1010));
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha2))
        {
            GamePlayerContext.instance.m_eventHandler.PushEvent(new GameEventDefault(GameEventId.TalkEnd, 1003));
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha3))
        {
            GamePlayerContext.instance.m_eventHandler.PushEvent(new GameEventDefault(GameEventId.TalkEnd, 1005));
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha4))
        {
            GamePlayerContext.instance.m_eventHandler.PushEvent(new GameEventDefault(GameEventId.BattleCompleted, 2));
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha5))
        {
            GamePlayerContext.instance.m_eventHandler.PushEvent(new GameEventDefault(GameEventId.Collect, 101, 1));
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Alpha6))
        {
            GamePlayerContext.instance.m_eventHandler.PushEvent(new GameEventDefault(GameEventId.Collect, 101, -1));
        }
        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.F1))
        {
            WorldBase world = GamePlayerContext.instance.m_worldHandler.EnterWorld(9998);
            if (world != null)
            {
                world.EventOnWorldEntityAdd += EventOnWorldEntityAdd;
                world.EventOnWorldEntityRemove += EventOnWorldEntityRemove;
                world.EventOnWorldEntityInteractionAdd += EventOnWorldEntityInteractionAdd;
                world.EventOnWorldEntityInteractionRemove += EventOnWorldEntityInteractionRemove;
            }
        }

        if (UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.F2))
        {
            WorldBase world = GamePlayerContext.instance.m_worldHandler.GetWorld(9998);
            if (world != null)
            {
                world.EventOnWorldEntityAdd -= EventOnWorldEntityAdd;
                world.EventOnWorldEntityRemove -= EventOnWorldEntityRemove;
                world.EventOnWorldEntityInteractionAdd -= EventOnWorldEntityInteractionAdd;
                world.EventOnWorldEntityInteractionRemove -= EventOnWorldEntityInteractionRemove;
                GamePlayerContext.instance.m_worldHandler.ExitWorld(9998);
            }
        }
    }

    protected void SimulateAcceptQuest()
    {
    }

    private void EventOnQuestAccept(QuestProcessorHandler handle)
    {
        UnityEngine.Debug.LogError($"[PlayerContext] 任务接受：{handle.QuestConfigData.Name} --> {handle.Id}");
    }
    private void EventOnQuestProgressUpdate(QuestProcessorHandler handle, Int32 condIndex, Int32 progress)
    {
        UnityEngine.Debug.LogError($"[PlayerContext] 任务进度更新：{handle.QuestConfigData.Name} --> {handle.Id} --> {progress}");
    }
    private void EventOnQuestStatusChange(QuestProcessorHandler handle, Boolean status)
    {
        UnityEngine.Debug.LogError($"[PlayerContext] 任务状态变化：{handle.QuestConfigData.Name} --> {handle.Id} --> {status}");
    }
    private void EventOnQuestCompleted(QuestProcessorHandler handle)
    {
        UnityEngine.Debug.LogError($"[PlayerContext] 任务完成：{handle.QuestConfigData.Name} --> {handle.Id}");
    }

    private void EventOnWorldEntityAdd(WorldEntity entityId)
    {

    }

    private void EventOnWorldEntityRemove(Int32 entityId)
    {

    }

    private void EventOnWorldEntityInteractionAdd(Int32 entityId, WorldEntityInteraction interaction)
    {

    }

    private void EventOnWorldEntityInteractionRemove(Int32 entityId, Int32 interactionId)
    {

    }

    */
}
