using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class TouchEffectManager : MonoSingleton<TouchEffectManager>
    {
        private Camera m_camera;
        private GameObjectPool m_clickPool;
        private GameObjectPool m_touchPool;
        private bool m_isActive;

        private List<TouchInfo> m_touchInfoList = new List<TouchInfo>(10);
        private List<GameObject> m_clickObjList = new List<GameObject>(10);
        private List<float> m_clickTimeList = new List<float>(10);
        private List<TouchInfo> m_tempTouchInfoList = new List<TouchInfo>();

        protected override void OnInit()
        {
            Input.multiTouchEnabled = true;
            Input.simulateMouseWithTouches = false;
            GameObjectInventory inventory = gameObject.GetComponent<GameObjectInventory>();
            m_clickPool = inventory.GetComponent<GameObjectPool>((int)EInventoryObj.ClickPool);
            m_touchPool = inventory.GetComponent<GameObjectPool>((int)EInventoryObj.TouchPool);
            m_camera = inventory.GetComponent<Camera>((int)EInventoryObj.Camera);
            TickManager.instance.RegisterGlobalTick(Tick);

            m_clickPool.Init();
            m_touchPool.Init();
        }

        protected override void OnUnInit()
        {
            TickManager.instance.UnRegisterGlobalTick(Tick);
        }

        public void SetActive(bool flag)
        {
            m_clickPool.SetActiveSafely(flag);
            m_touchPool.SetActiveSafely(flag);
        }

        private TouchInfo GetTouchInfo(int fingerId)
        {
            for (int i = 0; i < m_touchInfoList.Count; ++i)
            {
                if (m_touchInfoList[i].fingerId == fingerId)
                {
                    return m_touchInfoList[i];
                }
            }
            TouchInfo info = new TouchInfo();
            info.fingerId = fingerId;
            m_touchInfoList.Add(info);
            return info;
        }

        public void Tick(TimeSlice timeSlice)
        {
            if (m_camera == null)
            {
                return;
            }
#if UNITY_EDITOR || UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX
            for (int i = 0; i < 3; ++i)
            {
                if (Input.GetMouseButtonDown(i))
                {
                    TouchInfo info = GetTouchInfo(i);
                    info.phase = TouchPhase.Began;
                    info.position = Input.mousePosition;
                    m_tempTouchInfoList.Add(info);
                }
                else if (Input.GetMouseButton(i))
                {
                    TouchInfo info = GetTouchInfo(i);
                    info.phase = TouchPhase.Moved;
                    info.position = Input.mousePosition;
                    m_tempTouchInfoList.Add(info);
                }
                else
                {
                    TouchInfo info = GetTouchInfo(i);
                    info.phase = TouchPhase.Ended;
                    info.position = Input.mousePosition;
                    m_tempTouchInfoList.Add(info);
                }
            }
#else
        for (int i = 0; i < Input.touchCount; ++i)
        {
            Touch touch = Input.GetTouch(i);
            TouchInfo info = GetTouchInfo(touch.fingerId);
            info.phase = touch.phase;
            info.position = touch.position;
            m_tempTouchInfoList.Add(info);
        }
#endif
            for (int i = 0; i < m_touchInfoList.Count; ++i)
            {
                if (!m_tempTouchInfoList.Contains(m_touchInfoList[i]))
                {
                    m_touchInfoList[i].phase = TouchPhase.Ended;
                }
            }
            m_tempTouchInfoList.Clear();

            for (int i = m_touchInfoList.Count - 1; i >= 0; --i)
            {
                TouchInfo info = m_touchInfoList[i];
                switch (info.phase)
                {
                    case TouchPhase.Began:
                    case TouchPhase.Moved:
                    case TouchPhase.Stationary:
                        Vector3 worldPos = m_camera.ScreenToWorldPoint(info.position);
                        worldPos.z += 10;
                        if (info.touchObj == null)
                        {
                            CreateClickEffect(worldPos);
                            info.touchObj = m_touchPool.Fetch().obj;
                        }
                        info.touchObj.transform.position = worldPos;
                        break;
                    case TouchPhase.Canceled:
                    case TouchPhase.Ended:
                        if (info.touchObj != null)
                        {
                            m_touchPool.Release(info.touchObj);
                            info.touchObj = null;
                        }
                        break;
                }
            }
            for (int i = m_clickTimeList.Count - 1; i >= 0; --i)
            {
                m_clickTimeList[i] -= timeSlice.unscaledDeltaTime;
                if (m_clickTimeList[i] < 0f)
                {
                    m_clickPool.Release(m_clickObjList[i]);
                    m_clickObjList.RemoveAt(i);
                    m_clickTimeList.RemoveAt(i);
                }
            }
        }

        private void CreateClickEffect(Vector3 worldPos)
        {
            GameObject clickObj = m_clickPool.Fetch().obj;
            clickObj.transform.position = worldPos;
            m_clickObjList.Add(clickObj);
            m_clickTimeList.Add(1f);
        }

        public void LogOutStartingTouches()
        {
            for (int i = 0; i < Input.touchCount; ++i)
            {
                TipUI.ShowTip("Touch: " + Input.touches[i].fingerId + ", " + Input.touches[i].phase);
            }
            for (int i = 0; i < m_touchInfoList.Count; ++i)
            {
                TipUI.ShowTip("Info: " + m_touchInfoList[i].fingerId + ", " + m_touchInfoList[i].phase);
            }
        }

        private enum EInventoryObj
        {
            ClickPool,
            TouchPool,
            Camera,
        }

        private class TouchInfo
        {
            public int fingerId;
            public Vector2 position;
            public TouchPhase phase;
            public GameObject touchObj;
        }
    }
}