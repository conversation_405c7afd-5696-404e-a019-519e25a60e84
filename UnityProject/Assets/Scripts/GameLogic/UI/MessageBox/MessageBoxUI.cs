using System;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public enum MessageBoxEventId: byte
    {
        Close,
        Cancel,
        Confirm,
    }

    public class MessageBoxUI : UIBase
    {
        public static String MessageBoxParam = "MessageBoxParam";
        private MessageBoxParam m_messageBoxParam;

        protected override void OnInit()
        {
            RegisterListener((int)MessageBoxEventId.Close, EventOnCloseButtonClick);
            RegisterListener((int)MessageBoxEventId.Cancel, EventOnCancelButtonClick);
            RegisterListener((int)MessageBoxEventId.Confirm, EventOnConfirmButtonClick);
        }
        protected override void OnUnInit()
        {
            RegisterListener((int)MessageBoxEventId.Close, EventOnCloseButtonClick);
            UnRegisterListener((int)MessageBoxEventId.Cancel, EventOnCancelButtonClick);
            UnRegisterListener((int)MessageBoxEventId.Confirm, EventOnConfirmButtonClick);
        }

        protected override void OnRefreshViews()
        {
            if (m_currentContext is UICustomContext context)
            {
                m_messageBoxParam = context.GetClassParam<MessageBoxParam>(MessageBoxParam);
                m_mainView.ShowMessageBox(m_messageBoxParam);
            }
        }

        private void EventOnConfirmButtonClick()
        {
            Action action = m_messageBoxParam.confirmButtonClickCallback;
            m_messageBoxParam.confirmButtonClickCallback = null;
            m_messageBoxParam = null;
            m_mainView.SetActiveSafely(false);
            UIManager.instance.Close(typeof(MessageBoxUI));
            action.InvokeSafely();
        }

        private void EventOnCancelButtonClick()
        {
            Action action = m_messageBoxParam.cancelButtonClickCallback;
            m_messageBoxParam.cancelButtonClickCallback = null;
            m_messageBoxParam = null;
            m_mainView.SetActiveSafely(false);
            UIManager.instance.Close(typeof(MessageBoxUI));
            action.InvokeSafely();
        }

        private void EventOnCloseButtonClick()
        {
            m_messageBoxParam.confirmButtonClickCallback = null;
            m_messageBoxParam.cancelButtonClickCallback = null;
            m_messageBoxParam = null;
            m_mainView.SetActiveSafely(false);
            UIManager.instance.Close(typeof(MessageBoxUI));
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private MessageBoxUIView m_mainView;
        
        public MessageBoxUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<MessageBoxUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
