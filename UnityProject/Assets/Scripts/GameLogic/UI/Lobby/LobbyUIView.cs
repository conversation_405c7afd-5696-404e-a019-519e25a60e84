
using Phoenix.ConfigData;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;
using UnityEngine.Events;
using System;
using System.Collections.Generic;

namespace Phoenix.GameLogic.UI
{
    public partial class LobbyUIView : UIView
    {
        private string m_groupName;
        private List<GameObject> m_detailButtonObjList = new List<GameObject>();
        private Dictionary<string, List<BattleConfigData>> m_map = new Dictionary<string, List<BattleConfigData>>();

        protected override void OnInit()
        {
            base.OnInit();
            m_pool.Init();
        }

        public void UpdateView()
        {
            foreach (var kv in ConfigDataManager.instance.testBattleMap)
            {
                var battleConfig = ConfigDataManager.instance.GetBattle(kv.Key);
                if (battleConfig == null)
                {
                    continue;
                }
                List<BattleConfigData> list = null;
                if (!m_map.TryGetValue(kv.Value.GroupName, out list))
                {
                    list = new List<BattleConfigData>();
                    m_map.Add(kv.Value.GroupName, list);
                }
                list.Add(battleConfig);
            }
            AddBattleContentList(m_battleContent);
            AddSystemContentList(m_systemContent);
            LoadSelectedGroup();
            RefreshBattleDetailList(m_battleDetailContent);
            LoadScrollRect();
        }

        private void AddBattleContentList(GameObject parent)
        {
            foreach (var kv in m_map)
            {
                var go = AddButtonItem(string.Format("{0}", string.IsNullOrEmpty(kv.Key) ? "未分类" : kv.Key), parent, () =>
                {
                    SaveScrollRect();
                    m_groupName = kv.Key;
                    SaveSelectedGroup();
                    RefreshBattleDetailList(m_battleDetailContent);
                });
            }
        }

        private void RefreshBattleDetailList(GameObject parent)
        {
            foreach(var go in m_detailButtonObjList)
            {
                m_pool.Release(go);
            }
            m_detailButtonObjList.Clear();
            List<BattleConfigData> list = null;
            m_map.TryGetValue(m_groupName, out list);
            if (list != null)
            {
                foreach (BattleConfigData battleInfo in list)
                {
                    Int32 battleId = battleInfo.id;
                    GameObject obj = AddButtonItem(string.Format("{0}.{1}", battleInfo.id, battleInfo.name), parent, () =>
                    {
                        SaveScrollRect();
                        EventManager.instance.Broadcast(EventID.BattleStart_StartRequest, battleId);
                    });
                    m_detailButtonObjList.Add(obj);
                }
            }
        }

        private void AddSystemContentList(GameObject parent)
        {
            foreach (var kv in ConfigDataManager.instance.phoenixHakoniwaMap)
            {
                var hakoniwa = kv.Value;
                Int32 id = hakoniwa.Id;
                GameObject obj = AddButtonItem(string.Format("{0}.{1}", hakoniwa.Id, hakoniwa.Name), parent, () =>
                {
                    SaveScrollRect();
                    Hakoniwa.StaticHakoniwa.EnterHakoniwa(id, 0, Hakoniwa.HakoLocation.invalid);
                });
            }
            foreach (var kv  in ConfigDataManager.instance.worldMap)
            {
                var worldMapInfo = kv.Value;
                Int32 id = worldMapInfo.Id;
                GameObject obj = AddButtonItem(string.Format("{0}.{1}", worldMapInfo.Id, worldMapInfo.Name), parent, () =>
                {
                    SaveScrollRect();
                    EventManager.instance.Broadcast(EventID.WorldEnter_StartRequest, id);
                });
            }
            AddButtonItem("Dialog", parent, () => DialogueUI.ShowDialogue(1001, null, null));
            AddButtonItem("DialogueSelection", parent, () => DialogueUI.ShowDialogue(1001, 1001, OnSelectionSelectCallBack));
            AddButtonItem("DialogCallBack", parent, () => DialogueUI.ShowDialogue(1001, null, () => { DialogSelectionUI.ShowSelectionUI(1001); }));


            //AddButtonItem("CommonRewardUI_1", parent, () => {
            //    GamePlayerContext.instance.RewardModule.AddItem(10001, 2, false);
            //    CommonRewardUI.ShowUI(); 
            //});
            AddButtonItem("旧的阴曹地府", parent, () =>
            {
                EventManager.instance.Broadcast(EventID.WorldEnter_StartRequest, 10001);
            });
            AddButtonItem("战术编辑", parent, () => { 
                DecisionUI.ShowDecisionUI();
            });
            AddButtonItem("BattleInvite", parent, () =>
            {
                BattleInviteUI.ShowUI();
            });
        }

        private void OnSelectionSelectCallBack(Int32 selectionId)
        {
            TipUI.ShowTip($"测试抛出对话选项ID {selectionId}");
        }

        private GameObject AddButtonItem(string name, GameObject parent, UnityAction clickCallback)
        {
            var extraHolder = m_pool.Fetch();
            extraHolder.obj.SetParent(parent);
            extraHolder.obj.transform.Find("Text").GetComponent<Text>().text = string.Format(name);
            Button btn = extraHolder.obj.GetComponent<Button>();
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(clickCallback);
            WwiseExtendManager.Instance.AddButtonOnClickEventListener(btn);
            return extraHolder.obj;
        }

        private void SaveScrollRect()
        {
            LayoutRebuilder.ForceRebuildLayoutImmediate(transform as RectTransform);
            PlayerPrefs.SetFloat("LobbyScroll1", gameObject.FindChild("BattleScrollView").GetComponent<ScrollRect>().verticalNormalizedPosition);
            PlayerPrefs.SetFloat("LobbyScroll2", gameObject.FindChild("BattleDetailScrollView").GetComponent<ScrollRect>().verticalNormalizedPosition);
            PlayerPrefs.Save();
        }

        private void LoadScrollRect()
        {
            LayoutRebuilder.ForceRebuildLayoutImmediate(transform as RectTransform);
            gameObject.FindChild("BattleScrollView").GetComponent<ScrollRect>().verticalNormalizedPosition = PlayerPrefs.GetFloat("LobbyScroll1");
            gameObject.FindChild("BattleDetailScrollView").GetComponent<ScrollRect>().verticalNormalizedPosition = PlayerPrefs.GetFloat("LobbyScroll2");
        }

        private void SaveSelectedGroup()
        {
            PlayerPrefs.SetString("SelectedGroup", m_groupName);
            PlayerPrefs.Save();
        }

        private void LoadSelectedGroup()
        {
            m_groupName = PlayerPrefs.GetString("SelectedGroup");
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObject m_battleContent;
        private GameObject m_systemContent;
        private GameObject m_battleDetailContent;
        private GameObjectPool m_pool;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_battleContent = UIBindUtility.GetBindObject(group.GetItemInfoByName("BattleContent"));
            m_systemContent = UIBindUtility.GetBindObject(group.GetItemInfoByName("SystemContent"));
            m_battleDetailContent = UIBindUtility.GetBindObject(group.GetItemInfoByName("BattleDetailContent"));
            m_pool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Pool"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
