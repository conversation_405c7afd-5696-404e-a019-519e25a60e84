using System;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class WorldActiveQuestConditionItemUIView : UIView
    {
        public Int32 m_QuestId;
        private Boolean m_conditionCompleted;

        protected override void OnInit()
        {
            AddButtonListener(m_conditionBtn, (Int32)WorldUIEventId.EventOnQuestTrackBtnClick);
        }

        public void UpdateView(Int32 questId, String desc, Int32 progress, Int32 target, QuestUIState state)
        {
            m_QuestId= questId;
            m_conditionCompleted = progress >= target;
            String progressTip = ConstStringUtility.GetConstString(ConstStringId.QuestConditionProgress, progress.ToString(), target.ToString());
            m_conditionText.text = $"{desc}{progressTip}";
            if (state == QuestUIState.NewQuest)
            {
                TimerManager.instance.Start(0.05f, PlayAnimation);
            }
        }

        private void PlayAnimation()
        {
            // AudioManager.instance.PlayOnce("Assets/Res/Audio/UI/Refresh_NewQuest.mp3");
            if(m_conditionItemAnimationState != null)
            {
                m_conditionItemAnimationState.Play(0);
            }
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_conditionBtn;
        private Text m_conditionText;
        private AnimationController m_conditionItemAnimationState;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_conditionBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ConditionBtn"), typeof(Button)) as Button;
            m_conditionText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ConditionText"), typeof(Text)) as Text;
            m_conditionItemAnimationState = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ConditionItemAnimationState"), typeof(AnimationController)) as AnimationController;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
