using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class WorldPointStoryAreaInfo : UIView
    {
        public void UpdateView(PhoenixWorldPointStoryConfigData cofingData)
        {
            this.gameObject.SetActiveSafely(true);
            string[] arrStrName = cofingData.Name.Split(' ');
            if (arrStrName.Length < 1)
            {
                return;
            }
            m_txtAreaNum.text = arrStrName[0];
            m_txtAreaName.text = arrStrName[1];
            m_txtAreaInfo.text = cofingData.Description;
            CommonUIUtility.UpdateIconSprite(m_owner, m_imgAreaInfo, cofingData.Picture);

            m_listRewardItem.Clear();
            for (int i = 0; i < cofingData.StoryExhibitParam.Count; i++)
            {
                StoryExhibitConfigData storyPointConfig = cofingData.StoryExhibitParam[i];
                PhoenixWorldPointStoryExhibitConfigData config = ConfigDataManager.instance.GetPhoenixWorldPointStoryExhibit(storyPointConfig.StoryExhibitParamID);
                if (config == null)
                {
                    Debug.LogError($"PhoenixWorldPointStoryExhibit is null ID = {storyPointConfig.StoryExhibitParamID}");
                    continue;
                }

                for (int j = 0; j < config.StoryDropReward.Count; j++)
                {
                    StoryDropRewardConfigData configReward = config.StoryDropReward[j];
                    List<RewardItem> listRewardItem = ItemUtility.GetRewards(configReward.DropID);
                    m_listRewardItem.AddRange(listRewardItem);
                }
            }

            for (int i = 0; i < m_listRewardItem.Count; i++)
            {
                RewardItem rewardItem = m_listRewardItem[i];
                CreateItem(i, rewardItem);
            }
            if (m_listExchangeItemView.Count > m_listRewardItem.Count)
            {
                int tempCount = m_listExchangeItemView.Count - m_listRewardItem.Count;
                for (int i = m_listExchangeItemView.Count - 1; i >= m_listExchangeItemView.Count - tempCount; i--)
                {
                    CommonItemView itemView = m_listExchangeItemView[i];
                    itemView.SetActive(false);
                }
            }

        }

        private List<RewardItem> m_listRewardItem = new List<RewardItem>();
        private List<CommonItemView> m_listExchangeItemView = new List<CommonItemView>();
        private void CreateItem(int index, RewardItem rewardItem)
        {
            CommonItemView itemView = null;
            if (m_listExchangeItemView.Count == index)
            {
                GameObject go = m_uiBindItemCommon.gameObject.Instantiate(m_objItemGroup.transform);
                go.SetActive(true);
                itemView = go.GetComponent<CommonItemView>();
                itemView.Init(this);
                m_listExchangeItemView.Add(itemView);
            }
            else
            {
                itemView = m_listExchangeItemView[index];
                itemView.SetActive(true);
            }
            
            itemView.UpdateView(rewardItem);
            itemView.name = "item_" + rewardItem.m_itemId;
            
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Text m_txtAreaNum;
        private Text m_txtAreaName;
        private Text m_txtAreaInfo;
        private Image m_imgAreaInfo;
        private GameObject m_objItemGroup;
        private CommonItemView m_uiBindItemCommon;
        private Button m_btnEnter;
        
        public CommonItemView uiBindItemCommon
        {
            get { return m_uiBindItemCommon; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("uiBindItemCommon"), typeof(CommonItemView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_txtAreaNum = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtAreaNum"), typeof(Text)) as Text;
            m_txtAreaName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtAreaName"), typeof(Text)) as Text;
            m_txtAreaInfo = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtAreaInfo"), typeof(Text)) as Text;
            m_imgAreaInfo = UIBindUtility.GetBindComponent(group.GetItemInfoByName("imgAreaInfo"), typeof(Image)) as Image;
            m_objItemGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("objItemGroup"));
            m_uiBindItemCommon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("uiBindItemCommon"), typeof(CommonItemView)) as CommonItemView;
            m_btnEnter = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnEnter"), typeof(Button)) as Button;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
