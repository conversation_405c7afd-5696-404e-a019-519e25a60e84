using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using UnityEngine.UI;
using System;

namespace Phoenix.GameLogic.UI
{
    public class WorldPointStoryUIView : UIView
    {
        PhoenixWorldPointConfigData m_worldPointConfig = null!;
        private WorldPointStoryArea m_worldPointStoryArea = null;
        private static Int32 m_pointIndex;
        protected override void OnInit()
        {
            AddButtonListener(m_btnClose, (Int32)WorldPointStoryUIEventID.CloseBtnClick);
            m_btnWorldPoint.onClick.AddListener(delegate
            {
                m_owner.Close();
                Broadcast<Int32>((Int32)WorldPointStoryUIEventID.OpenWorldPointUI, m_pointIndex);
            });
            // AddButtonListener<Int32>(m_btnWorldPoint, (Int32)WorldPointStoryUIEventID.OpenWorldPointUI, m_pointIndex);
            m_root.Init();
        }

        protected override void OnUnInit()
        {
            m_worldPointConfig = null;
            m_worldPointStoryArea = null;
        }

        public void PlayAnimation(Int32 index, Action onEnd)
        {
            m_root.Play(index, onEnd);
        }

        public void UpdateView(PhoenixWorldPointConfigData worldPointConfig)
        {
            m_worldPointConfig = worldPointConfig;
            m_pointIndex = worldPointConfig.PointIndex;
            NumberConfigData numberConfigData = ConfigDataManager.instance.GetNumber(worldPointConfig.PointIndex);
            m_txtWorldPointNum.text = numberConfigData.Traditional;
            m_txtWorldPointName.text = worldPointConfig.Name;
            m_txtWorldPointAreaName.text = worldPointConfig.AreaName;
            m_txtWorldPointAreaTime.text = worldPointConfig.AreaTime;

            m_worldPointStoryArea = m_objWordPoint.GetComponent<WorldPointStoryArea>();
            if (m_worldPointStoryArea != null)
            {
                m_worldPointStoryArea.Init(this);
                m_worldPointStoryArea.UpdateView(worldPointConfig);
            }

        }

        public void OnCloseUpArea(PhoenixWorldPointStoryConfigData configData)
        {
            m_uiBindAreaDetail.SetActiveSafely(true);
            m_uiBindAreaDetail.UpdateView(configData);
            string strAreaName = configData.AreaName.Replace("objArea", string.Empty);
            if (int.TryParse(strAreaName, out int idxTarget))
            {
                m_worldPointStoryArea?.SetMoveTarget(idxTarget, configData);
            }
            else
            {
                Debug.LogError($"ObjArea Name Error. Name = {configData.AreaName}");
            }
            
        }

        public void HideStoryAreaInfo()
        {
            m_worldPointStoryArea?.RevertToInitTrans();
            m_uiBindAreaDetail.SetActiveSafely(false);
        }

        public void SelectStoryPoint(StoryPointConfigData config)
        {
            m_worldPointStoryArea?.SelectStoryPoint(config);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private AnimationController m_root;
        private Button m_btnClose;
        private Text m_txtWorldPointNum;
        private Text m_txtWorldPointName;
        private Text m_txtWorldPointAreaName;
        private Text m_txtWorldPointAreaTime;
        private Button m_btnWorldPoint;
        private Text m_txtTaskProcess;
        private Text m_txtBranchProcess;
        private Text m_txtHideProcess;
        private GameObject m_objWordPoint;
        private WorldPointStoryAreaInfo m_uiBindAreaDetail;
        
        public WorldPointStoryAreaInfo uiBindAreaDetail
        {
            get { return m_uiBindAreaDetail; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("uiBindAreaDetail"), typeof(WorldPointStoryAreaInfo));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_root = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Root"), typeof(AnimationController)) as AnimationController;
            m_btnClose = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnClose"), typeof(Button)) as Button;
            m_txtWorldPointNum = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtWorldPointNum"), typeof(Text)) as Text;
            m_txtWorldPointName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtWorldPointName"), typeof(Text)) as Text;
            m_txtWorldPointAreaName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtWorldPointAreaName"), typeof(Text)) as Text;
            m_txtWorldPointAreaTime = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtWorldPointAreaTime"), typeof(Text)) as Text;
            m_btnWorldPoint = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnWorldPoint"), typeof(Button)) as Button;
            m_txtTaskProcess = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtTaskProcess"), typeof(Text)) as Text;
            m_txtBranchProcess = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtBranchProcess"), typeof(Text)) as Text;
            m_txtHideProcess = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtHideProcess"), typeof(Text)) as Text;
            m_objWordPoint = UIBindUtility.GetBindObject(group.GetItemInfoByName("objWordPoint"));
            m_uiBindAreaDetail = UIBindUtility.GetBindComponent(group.GetItemInfoByName("uiBindAreaDetail"), typeof(WorldPointStoryAreaInfo)) as WorldPointStoryAreaInfo;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
