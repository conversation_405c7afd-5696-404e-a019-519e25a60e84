using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using UnityEngine.UI;

namespace Phoenix.GameLogic.UI
{
    public class AreaLocationInfo : UIView
    {
        public UIGroupComponent m_uiGroupStoryScale; // Reduce Enlarge 
        public UIGroupComponent m_uiGroupStatus; // Default Progress Complete 
        public GameObject m_objLocation;
        public Image m_imgSkin;
        public GameObject m_objTreasure;
        public GameObject m_objHide;
        public Button m_btnStoryPoint;

        PhoenixWorldPointStoryExhibitConfigData m_config = null;
        PhoenixWorldPointStoryConfigData m_worldPointStoryConfig = null;
        StoryPointConfigData m_storyPointConfig = null;

        protected override void OnInit()
        {
            m_btnStoryPoint.onClick.AddListener(delegate
            {
                if (m_uiGroupStoryScale.ActiveUIGroup.m_GroupName != "Enlarge")
                {
                    return;
                }
                Broadcast<StoryPointConfigData>((int)WorldPointStoryUIEventID.SelectStoryPoint, m_storyPointConfig);
            });
        }

        protected override void OnUnInit()
        {
            m_config = null;
            m_worldPointStoryConfig = null;
            m_storyPointConfig = null;
        }

        public void UpdateView(PhoenixWorldPointStoryConfigData worldPointStoryConfig, StoryPointConfigData storyPointConfig, int storyExhibitParamID)
        {
            m_imgSkin.SetActiveSafely(false);
            m_objTreasure.SetActiveSafely(false);
            m_objHide.SetActiveSafely(false);

            m_worldPointStoryConfig = worldPointStoryConfig;
            m_storyPointConfig = storyPointConfig;

            PhoenixWorldPointStoryExhibitConfigData config = ConfigDataManager.instance.GetPhoenixWorldPointStoryExhibit(storyExhibitParamID);
            if (config == null)
            {
                Debug.LogError($"PhoenixWorldPointStoryExhibit is null ID = {storyExhibitParamID}");
                return;
            }

            m_config = config;

            m_uiGroupStoryScale.SetToUIGroup("Reduce");
            m_uiGroupStatus.SetToUIGroup("Default");


            for (int i = 0; i < config.StoryExhibitParam.Count; i++)
            {
                StoryExhibitConfigData storyExhibitConfig = config.StoryExhibitParam[i];
                if (storyExhibitConfig.ExhibitType == StoryExhibitType.Battle)
                {
                    m_imgSkin.SetActiveSafely(true);
                    CommonUIUtility.UpdateEntity2DSkinSprite(m_owner, m_imgSkin, storyExhibitConfig.P1, Entity2DSkinIconType.IconC);
                }
                if (storyExhibitConfig.ExhibitType == StoryExhibitType.HakoniwaKeyBattle)
                {
                    m_imgSkin.SetActiveSafely(true);
                    CommonUIUtility.UpdateEntity2DSkinSprite(m_owner, m_imgSkin, storyExhibitConfig.P2, Entity2DSkinIconType.IconC);
                }
                if (storyExhibitConfig.ExhibitType == StoryExhibitType.HakoniwaKeyTreasure)
                {
                    m_objTreasure.SetActiveSafely(true);
                }
            }

        }

        public void ReduceOrEnlarge(WorldPointMove worldPointMove)
        {
            if (worldPointMove == WorldPointMove.RevertToInit)
            {
                m_uiGroupStoryScale.SetToUIGroup("Reduce");
            }
            else if (worldPointMove == WorldPointMove.MoveToTarget)
            {
                m_uiGroupStoryScale.SetToUIGroup("Enlarge");
            }
        }

        public void SelectStoryPoint(StoryPointConfigData config)
        {
            m_objLocation.SetActiveSafely(config.PointName == m_storyPointConfig.PointName && config.HakoniwaLocationID == m_storyPointConfig.HakoniwaLocationID);
        }

    }
}
