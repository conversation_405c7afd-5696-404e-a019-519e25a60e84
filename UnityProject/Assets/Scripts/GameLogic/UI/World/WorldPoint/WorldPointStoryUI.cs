using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using System;

namespace Phoenix.GameLogic.UI
{
    public enum WorldPointStoryUIEventID
    {
        CloseBtnClick,
        OpenWorldPointUI,
        CloseUpBtnClick,
        HideStoryAreaInfo,
        SelectStoryPoint,
    }

    public enum WorldPointMove
    {
        Init,
        MoveToTarget,
        RevertToInit,
    }

    public class WorldPointStoryUI : UIBase
    {
        private static PhoenixWorldPointConfigData m_worldPointConfig;
        public static int CurrentWorldPointId => m_worldPointConfig.Id;
        private static int m_currentStoryId = 0;
        public static int CurrentSelectStoryId { get => m_currentStoryId; set => m_currentStoryId = value; } 

        protected override void OnInit()
        {
            base.OnInit();
            RegisterListener((Int32)WorldPointStoryUIEventID.CloseBtnClick, OnCloseBtnClick);
            RegisterListener<Int32>((Int32)WorldPointStoryUIEventID.OpenWorldPointUI, OnOpenWorldPointUI);
            RegisterListener<PhoenixWorldPointStoryConfigData>((Int32)WorldPointStoryUIEventID.CloseUpBtnClick, OnCloseUpArea);
            RegisterListener((Int32)WorldPointStoryUIEventID.HideStoryAreaInfo, OnHideStoryAreaInfo);
            RegisterListener<StoryPointConfigData>((Int32)WorldPointStoryUIEventID.SelectStoryPoint, SelectStoryPoint);
        }

        protected override void OnUnInit()
        {
            UnRegisterListener((Int32)WorldPointStoryUIEventID.CloseBtnClick, OnCloseBtnClick);
            UnRegisterListener<Int32>((Int32)WorldPointStoryUIEventID.OpenWorldPointUI, OnOpenWorldPointUI);
            UnRegisterListener<PhoenixWorldPointStoryConfigData>((Int32)WorldPointStoryUIEventID.CloseUpBtnClick, OnCloseUpArea);
            UnRegisterListener((Int32)WorldPointStoryUIEventID.HideStoryAreaInfo, OnHideStoryAreaInfo);
            UnRegisterListener<StoryPointConfigData>((Int32)WorldPointStoryUIEventID.SelectStoryPoint, SelectStoryPoint);
            base.OnUnInit();
        }

        protected override void OnPlayOpen()
        {
            mainView.PlayAnimation(0, base.OnPlayOpen);
        }

        protected override void OnPlayClose()
        {
            mainView.PlayAnimation(1, base.OnPlayClose);
        }

        protected override void OnRefreshViews()
        {
            mainView.UpdateView(m_worldPointConfig);
        }

        public static void ShowUI(PhoenixWorldPointConfigData worldPointConfig)
        {
            m_worldPointConfig = worldPointConfig;
            UICustomContext context = new UICustomContext(typeof(WorldPointStoryUI));
            UIManager.instance.Open(context, true);
        }

        private void OnCloseBtnClick()
        {
            Close(true, true);
        }

        private void OnOpenWorldPointUI(Int32 pointIdex)
        {
            WorldPointUI.ShowUI(pointIdex);
        }

        private void OnCloseUpArea(PhoenixWorldPointStoryConfigData configData)
        {
            m_mainView.OnCloseUpArea(configData);
        }

        private void OnHideStoryAreaInfo()
        {
            m_mainView.HideStoryAreaInfo();
        }

        public void SelectStoryPoint(StoryPointConfigData config)
        {
            m_mainView.SelectStoryPoint(config);
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private WorldPointStoryUIView m_mainView;

        public WorldPointStoryUIView mainView
        {
            get { return m_mainView; }
        }

        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<WorldPointStoryUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
