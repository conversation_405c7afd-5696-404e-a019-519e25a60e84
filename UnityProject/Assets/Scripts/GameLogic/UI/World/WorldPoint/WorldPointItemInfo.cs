using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine.UI;
using System;

namespace Phoenix.GameLogic.UI
{
    public class WorldPointItemInfo : UIView
    {
        private PhoenixWorldPointConfigData m_worldPointConfig;
        protected override void OnInit()
        {
            base.OnInit();
            m_btnRoot.onClick.AddListener(delegate
            {
                Broadcast<PhoenixWorldPointConfigData>((Int32)WorldPointUIEventID.SelectScrollItem, m_worldPointConfig);
            });
        }
        
        public void UpdateView(PhoenixWorldPointConfigData worldPointConfig, bool isSelect = false)
        {
            m_worldPointConfig = worldPointConfig;
            NumberConfigData numberConfigData = ConfigDataManager.instance.GetNumber(worldPointConfig.PointIndex);
            string strName = $"{numberConfigData.Traditional}   {worldPointConfig.Name}";
            m_txtWorldPointName.text = strName;
            m_txtWorldPointNameSelect.text = strName;
            SelectView(isSelect);
        }

        public void SelectView(bool isSelect = false)
        {
            if (isSelect)
            {
                m_uiGroupSelect.SetToUIGroup("Select");
            }
            else
            {
                m_uiGroupSelect.SetToUIGroup("UnSelect");
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_btnRoot;
        private Text m_txtWorldPointName;
        private Image m_imgStatus;
        private Image m_imgPerfact;
        private Text m_txtWorldPointNameSelect;
        private Text m_txtTaskProcess;
        private Text m_txtBranchProcess;
        private Text m_txtHideProcess;
        private UIGroupComponent m_uiGroupSelect; // UnSelect Select 
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_btnRoot = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnRoot"), typeof(Button)) as Button;
            m_txtWorldPointName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtWorldPointName"), typeof(Text)) as Text;
            m_imgStatus = UIBindUtility.GetBindComponent(group.GetItemInfoByName("imgStatus"), typeof(Image)) as Image;
            m_imgPerfact = UIBindUtility.GetBindComponent(group.GetItemInfoByName("imgPerfact"), typeof(Image)) as Image;
            m_txtWorldPointNameSelect = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtWorldPointNameSelect"), typeof(Text)) as Text;
            m_txtTaskProcess = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtTaskProcess"), typeof(Text)) as Text;
            m_txtBranchProcess = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtBranchProcess"), typeof(Text)) as Text;
            m_txtHideProcess = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtHideProcess"), typeof(Text)) as Text;
            m_uiGroupSelect = UIBindUtility.GetBindComponent(group.GetItemInfoByName("uiGroupSelect"), typeof(UIGroupComponent)) as UIGroupComponent;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
