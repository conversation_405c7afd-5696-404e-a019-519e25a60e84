using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace Phoenix.GameLogic.UI
{
    public class AreaLocation : UIView
    {
        public UIGroupComponent m_uiGroupObjArea; // Normarl Current 
        public GameObject m_objStory;
        public Button m_btnCloseUp;
        public Button m_btnHideStoryInfo;


        private PhoenixWorldPointStoryConfigData m_worldPointStoryConfig = null;
        private List<AreaLocationInfo> m_listAreaLocationInfo = new List<AreaLocationInfo>();

        protected override void OnInit()
        {
            m_btnCloseUp.onClick.AddListener(delegate
            {
                if (m_worldPointStoryConfig == null)
                {
                    return;
                }
                if (m_uiGroupObjArea.ActiveUIGroup.m_GroupName == "Current")
                {
                    return;
                }

                Broadcast<PhoenixWorldPointStoryConfigData>((int)WorldPointStoryUIEventID.CloseUpBtnClick, m_worldPointStoryConfig);
            });

            m_btnHideStoryInfo.onClick.AddListener(delegate
            {
                Broadcast((int)WorldPointStoryUIEventID.HideStoryAreaInfo);
            });
        }

        protected override void OnUnInit()
        {
            m_worldPointStoryConfig = null;
            m_listAreaLocationInfo.Clear();
        }

        public void UpdateView(PhoenixWorldPointStoryConfigData worldPointStoryConfig)
        {
            if (worldPointStoryConfig.StoryPoint.Count != worldPointStoryConfig.StoryExhibitParam.Count)
            {
                Debug.LogError("StoryPoint & StoryExhibitParam Count not Equal");
                return;
            }

            m_worldPointStoryConfig = worldPointStoryConfig;
            m_listAreaLocationInfo.Clear();

            m_uiGroupObjArea.SetToUIGroup("Normarl");
            for (int i = 0; i < worldPointStoryConfig.StoryPoint.Count; i++)
            {
                StoryPointConfigData storyPointConfig = worldPointStoryConfig.StoryPoint[i];
                Transform objAreaLocation = m_objStory.transform.Find(storyPointConfig.PointName);
                if (objAreaLocation == null)
                {
                    Debug.LogError($"Point not found Name = {storyPointConfig.PointName}");
                    continue;
                }

                StoryExhibitConfigData storyExhibitConfig = worldPointStoryConfig.StoryExhibitParam[i];
                AreaLocationInfo areaLocationInfo = objAreaLocation.GetComponent<AreaLocationInfo>();
                if (areaLocationInfo != null)
                {
                    areaLocationInfo.Init(this);
                    areaLocationInfo.UpdateView(m_worldPointStoryConfig, storyPointConfig, storyExhibitConfig.StoryExhibitParamID);
                    m_listAreaLocationInfo.Add(areaLocationInfo);
                }
            }
        }

        public void ReduceOrEnlarge(WorldPointMove worldPointMove, PhoenixWorldPointStoryConfigData configData = null)
        {
            if (worldPointMove == WorldPointMove.RevertToInit)
            {
                if (m_uiGroupObjArea.ActiveUIGroup.m_GroupName != "Current")
                {
                    return;
                }
                m_uiGroupObjArea.SetToUIGroup("Normarl");
            }
            else if (worldPointMove == WorldPointMove.MoveToTarget)
            {
                if (configData == null)
                {
                    return;
                }
                if (m_worldPointStoryConfig?.Id != configData.Id)
                {
                    return;
                }
                m_uiGroupObjArea.SetToUIGroup("Current");
                WorldPointStoryUI.CurrentSelectStoryId = configData.Id;
                this.transform.SetAsLastSibling();
            }

            foreach (var areaLocationInfo in m_listAreaLocationInfo)
            {
                areaLocationInfo.ReduceOrEnlarge(worldPointMove);
            }
        }

        public void SelectStoryPoint(StoryPointConfigData config)
        {
            if (WorldPointStoryUI.CurrentSelectStoryId != m_worldPointStoryConfig.Id)
            {
                return;
            }

            foreach (var areaLocationInfo in m_listAreaLocationInfo)
            {
                areaLocationInfo.SelectStoryPoint(config);
            }
        }
    }
}
