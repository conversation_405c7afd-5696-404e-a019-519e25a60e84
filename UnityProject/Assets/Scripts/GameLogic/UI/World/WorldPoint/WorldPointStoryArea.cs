using UnityEngine;
using Phoenix.Core;
using Phoenix.ConfigData;
using System.Collections.Generic;

namespace Phoenix.GameLogic.UI
{
    public class WorldPointStoryArea : UIView
    {
        PhoenixWorldPointStoryConfigData m_cofingDataCurrent = null;
        public PhoenixWorldPointStoryConfigData CofingDataCurrent { get => m_cofingDataCurrent; private set => m_cofingDataCurrent = value; } 
        private int m_idxTarget = 0;
        private WorldPointMove m_eWorldPointMove = WorldPointMove.Init;
        public WorldPointMove EWorldPointMove { get => m_eWorldPointMove; set => m_eWorldPointMove = value; } 

        public GameObject m_objArea;
        public Transform m_initTrans;
        public List<Transform> m_listTargetTrans = new List<Transform>();
        [Range(0, 1)] public float lerpFactor = 0.1f;

        protected override void OnUnInit()
        {
            m_listAreaLocation.Clear();
            m_eWorldPointMove = WorldPointMove.Init;
            m_cofingDataCurrent = null;
        }

        List<AreaLocation> m_listAreaLocation = new List<AreaLocation>();
        public void UpdateView(PhoenixWorldPointConfigData worldPointConfig)
        {
            m_listAreaLocation.Clear();
            for (int i = 0; i < worldPointConfig.StoryId.Count; i++)
            {
                int iStotyId = worldPointConfig.StoryId[i];
                PhoenixWorldPointStoryConfigData worldPointStoryConfig = ConfigDataManager.instance.GetPhoenixWorldPointStory(iStotyId);
                if (worldPointStoryConfig != null)
                {
                    Transform objArea = m_objArea.transform.Find(worldPointStoryConfig.AreaName);
                    if (objArea == null)
                    {
                        Debug.LogError($"Area not found Name = {worldPointStoryConfig.AreaName}");
                        continue;
                    }

                    AreaLocation areaLocation = objArea.GetComponent<AreaLocation>();
                    if (areaLocation != null)
                    {
                        areaLocation.Init(this);
                        areaLocation.UpdateView(worldPointStoryConfig);
                        m_listAreaLocation.Add(areaLocation);
                    }
                }
            }
        }

        public void ReduceOrEnlarge(WorldPointMove worldPointMove, PhoenixWorldPointStoryConfigData configData = null)
        {
            CofingDataCurrent = configData;
            foreach (var areaLocation in m_listAreaLocation)
            {
                areaLocation.ReduceOrEnlarge(worldPointMove, configData);
            }
        }

        public void SelectStoryPoint(StoryPointConfigData config)
        {
            foreach (var areaLocation in m_listAreaLocation)
            {
                areaLocation.SelectStoryPoint(config);
            }
        }

        public void SetMoveTarget(int idxTarget, PhoenixWorldPointStoryConfigData configData)
        {
            m_idxTarget = idxTarget - 1;
            m_eWorldPointMove = WorldPointMove.MoveToTarget;
            ReduceOrEnlarge(m_eWorldPointMove, configData);
        }

        public void RevertToInitTrans()
        {
            m_eWorldPointMove = WorldPointMove.RevertToInit;
            ReduceOrEnlarge(m_eWorldPointMove);
        }

        void Update()
        {
            if (m_eWorldPointMove == WorldPointMove.MoveToTarget)
            {
                transform.position = Vector3.Lerp(transform.position, m_listTargetTrans[m_idxTarget].position, lerpFactor);
                transform.localScale = Vector3.Lerp(transform.localScale, m_listTargetTrans[m_idxTarget].localScale, lerpFactor);
            }
            else if (m_eWorldPointMove == WorldPointMove.RevertToInit)
            {
                transform.position = Vector3.Lerp(transform.position, m_initTrans.position, lerpFactor);
                transform.localScale = Vector3.Lerp(transform.localScale, m_initTrans.localScale, lerpFactor);
            }
            
        }

    }
}
