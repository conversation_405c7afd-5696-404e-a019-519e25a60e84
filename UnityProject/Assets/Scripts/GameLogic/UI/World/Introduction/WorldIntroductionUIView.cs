using System;
using System.Collections.Generic;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class WorldIntroductionUIView : UIView
    {
        private List<WorldIntroductionItemUIView> itemUIViews = new List<WorldIntroductionItemUIView>();
        protected override void OnInit()
        {
            AddButtonListener(m_closeBtn, (Int32)WorldIntroductionUIEventId.CloseBtnClick);
            m_itemContentPool.Init(OnItemPoolInit);
            m_root.Init();
        }

        public void PlayAnimation(Int32 index, Action onEnd)
        {
            m_root.Play(index, onEnd);
        }

        public void UpdateView()
        {
            itemUIViews.Clear();
            m_itemContentPool.ReleaseAll();
            var introductionMap = ConfigDataManager.instance.worldIntroductionMap;

            Int32 selectIntroductionId = -1;
            foreach (var kv in introductionMap)
            {
                WorldIntroductionItemUIView itemUIView = m_itemContentPool.FetchComponent<WorldIntroductionItemUIView>();
                Boolean isUnlocked = ConditionUtility.CheckConditons(kv.Value.UnlockConditions);
                itemUIView.UpdateView(kv.Value, !isUnlocked);
                itemUIViews.Add(itemUIView);
                if (selectIntroductionId == -1 && isUnlocked)
                {
                    selectIntroductionId = kv.Key;
                }
            }
            if (selectIntroductionId != -1)
            {
                SelectItem(selectIntroductionId);
            }
        }

        public void SelectItem(Int32 introductionId)
        {
            foreach (var item in itemUIViews)
            {
                item.Select(introductionId == item.introductionId);
            }
            WorldIntroductionConfigData introductionConfig = ConfigDataManager.instance.GetWorldIntroduction(introductionId);
            m_introductionNameText.text = introductionConfig.Name;
            m_introductionDesc.text = introductionConfig.Desc;
        }


        private void OnItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            WorldIntroductionItemUIView itemView = holder.AddComponent<WorldIntroductionItemUIView>();
            itemView.Init(this);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private AnimationController m_root;
        private Button m_closeBtn;
        private GameObjectPool m_itemContentPool;
        private Text m_introductionNameText;
        private Text m_introductionDesc;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_root = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Root"), typeof(AnimationController)) as AnimationController;
            m_closeBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CloseBtn"), typeof(Button)) as Button;
            m_itemContentPool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ItemContentPool"), typeof(GameObjectPool)) as GameObjectPool;
            m_introductionNameText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("IntroductionNameText"), typeof(Text)) as Text;
            m_introductionDesc = UIBindUtility.GetBindComponent(group.GetItemInfoByName("IntroductionDesc"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
