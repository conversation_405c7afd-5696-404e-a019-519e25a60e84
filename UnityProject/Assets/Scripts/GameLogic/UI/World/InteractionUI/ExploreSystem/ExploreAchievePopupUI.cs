using System;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public enum ExploreAchievePopupUIEventID
    {
        CloseBtnClick,
    }

    public class ExploreAchievePopupUI : UIBase
    {
        public static void ShowUI(Int32 idEntity)
        {
            ExploreData exploreData = GamePlayerContext.instance.ExploreModule.CurExploreData;
            if (exploreData == null)
            {
                exploreData = GamePlayerContext.instance.ExploreModule.GetExploreData(idEntity);
            }

            if (exploreData == null)
            {
                Debug.LogError($"ExploreInfoPopupUI ShowUI: ExploreData is null idEntity = {idEntity}");
                return;
            }

            GamePlayerContext.instance.ExploreModule.CurExploreData = exploreData;

            UICustomContext context = new UICustomContext(typeof(ExploreAchievePopupUI));
            UIManager.instance.Open(context, true);
        }

        protected override void OnInit()
        {
            RegisterListener((Int32)ExploreInfoPopupUIEventID.CloseBtnClick, OnCloseBtnClick);
        }


        protected override void OnUnInit()
        {
            UnRegisterListener((Int32)ExploreInfoPopupUIEventID.CloseBtnClick, OnCloseBtnClick);
        }

        protected override void OnRefreshViews()
        {
            mainView.UpdateView();
        }

        private void OnCloseBtnClick()
        {
            Close();
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private ExploreAchievePopupUIView m_mainView;
        
        public ExploreAchievePopupUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<ExploreAchievePopupUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
