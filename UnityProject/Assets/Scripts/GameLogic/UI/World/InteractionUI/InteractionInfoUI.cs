using System;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.World;

namespace Phoenix.GameLogic.UI
{
    public enum InteractionInfoUIEventID
    {
        CloseBtnClick,
    }

    public class InteractionInfoUI : UIBase
    {
        private static GameObject m_interactionCamera;
        public static void ShowUI(Int32 idEntity)
        {
            ExploreData exploreData = GamePlayerContext.instance.ExploreModule.GetExploreData(idEntity);
            if (exploreData == null)
            {
                Debug.LogError($"InteractionInfoUI ShowUI: ExploreData is null idEntity = {idEntity}");
                return;
            }
            GamePlayerContext.instance.ExploreModule.CurExploreData = exploreData;

            WorldEntityView worldEntity = WorldManager.instance.m_worldEntityHandler.GetWorldEntityView(idEntity);
            if (worldEntity == null)
            {
                Debug.LogError($"InteractionInfoUI ShowUI: entityView is null idEntity = {idEntity}");
                return;
            }
            WorldManager.instance.m_worldEntityHandler.ShowOrHideAllEntityExcept(false, idEntity);
            m_interactionCamera = worldEntity.GetInteractionCamera();
            if (m_interactionCamera != null)
            {
                m_interactionCamera.SetActiveSafely(true);
            }

            FavorData favorData = GamePlayerContext.instance.FavorModule.GetFavorData(idEntity);
            if (favorData != null)
            {
                GamePlayerContext.instance.FavorModule.CurFavorData = favorData;
            }

            UICustomContext context = new UICustomContext(typeof(InteractionInfoUI));
            UIManager.instance.Open(context, true);
            EventManager.instance.Broadcast(EventID.WorldUIShowOrHide, false);
        }

        protected override void OnInit()
        {
            RegisterListener((Int32)InteractionInfoUIEventID.CloseBtnClick, OnCloseBtnClick);
        }


        protected override void OnUnInit()
        {
            UnRegisterListener((Int32)InteractionInfoUIEventID.CloseBtnClick, OnCloseBtnClick);
        }

        protected override void OnRefreshViews()
        {
            mainView.UpdateView();
        }

        private void OnCloseBtnClick()
        {
            GamePlayerContext.instance.ExploreModule.CurExploreData = null;
            GamePlayerContext.instance.FavorModule.CurFavorData = null;
            Close(false, false, (InteractionFavorUI) =>
            {
                if (m_interactionCamera != null)
                {
                    m_interactionCamera.SetActiveSafely(false);
                }
                WorldManager.instance.m_worldEntityHandler.ShowOrHideAllEntityExcept(true);
                EventManager.instance.Broadcast(EventID.WorldUIShowOrHide, true);
            });
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private InteractionInfoUIView m_mainView;
        
        public InteractionInfoUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<InteractionInfoUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
