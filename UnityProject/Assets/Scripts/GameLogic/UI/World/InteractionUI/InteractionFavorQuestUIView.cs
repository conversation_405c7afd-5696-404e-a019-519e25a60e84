using System.IO;
using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class InteractionFavorQuestUIView : UIView
    {
        public void UpdateView(FavorConfigData favorConfigData)
        {
            FavorData favorData = GamePlayerContext.instance.FavorModule.CurFavorData;
            CommonUIUtility.UpdateEntity2DSkinSpriteAByEntityId(m_owner, m_imgCharacter, favorData.m_entityId);
            m_bindGroupQuest1.SetActive(false);
            m_bindGroupQuest2.SetActive(false);
            m_bindGroupQuest3.SetActive(false);

            for (int i = 0; i < favorConfigData.QuestProvideInfo.Count; i++)
            {
                QuestProvideConfigData questProvideInfo = favorConfigData.QuestProvideInfo[i];
                FavorQuestView bindGroupQuest = null;
                if (i == 0)
                {
                    bindGroupQuest = m_bindGroupQuest1;
                }
                else if (i == 1)
                {
                    bindGroupQuest = m_bindGroupQuest2;
                }
                else if (i == 2)
                {
                    bindGroupQuest = m_bindGroupQuest3;
                }
                UpdateFavorQuestView(bindGroupQuest, questProvideInfo);
            }
        }

        private void UpdateFavorQuestView(FavorQuestView bindGroupQuest, QuestProvideConfigData questProvideInfo)
        {
            bindGroupQuest.SetActive(true);
            bindGroupQuest.UpdateView(questProvideInfo);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Image m_imgCharacter;
        private FavorQuestView m_bindGroupQuest1;
        private FavorQuestView m_bindGroupQuest2;
        private FavorQuestView m_bindGroupQuest3;
        
        public FavorQuestView bindGroupQuest1
        {
            get { return m_bindGroupQuest1; }
        }
        public FavorQuestView bindGroupQuest2
        {
            get { return m_bindGroupQuest2; }
        }
        public FavorQuestView bindGroupQuest3
        {
            get { return m_bindGroupQuest3; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("bindGroupQuest1"), typeof(FavorQuestView));
            AddBindComponent(group.GetItemInfoByName("bindGroupQuest2"), typeof(FavorQuestView));
            AddBindComponent(group.GetItemInfoByName("bindGroupQuest3"), typeof(FavorQuestView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_imgCharacter = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ImgCharacter"), typeof(Image)) as Image;
            m_bindGroupQuest1 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("bindGroupQuest1"), typeof(FavorQuestView)) as FavorQuestView;
            m_bindGroupQuest2 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("bindGroupQuest2"), typeof(FavorQuestView)) as FavorQuestView;
            m_bindGroupQuest3 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("bindGroupQuest3"), typeof(FavorQuestView)) as FavorQuestView;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
