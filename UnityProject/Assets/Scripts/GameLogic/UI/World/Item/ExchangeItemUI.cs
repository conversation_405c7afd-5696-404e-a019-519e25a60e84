using System.Collections.Generic;
using System;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.UI
{
    public enum ExchangeStatus
    {
        Empty,
        Lock,
        CanNotExchange,
        CanExchange,
        AlreadyExchanged,
    }

    public enum ExchangeItemUIEventID
    {
        CloseBtnClick,
        ItemClick,
        ExchangeBtnClick,
    }

    public class ExchangeItemUI : UIBase
    {
        private static ExchangeItemCountConfigData m_exchangeItemCountConfigData = null;
        private static ExchangeConfigData m_exchangeConfigData = null;
        public static void ShowUI(ExchangeItemCountConfigData exchangeItemCountConfigData, ExchangeConfigData exchangeConfigData)
        {
            
            if (exchangeItemCountConfigData == null || exchangeConfigData == null)
            {
                Debug.LogError($"ExchangeItemUI.ShowUI:exchangeItemCountConfigData|exchangeConfigData is null.");
                return;
            }

            m_exchangeItemCountConfigData = exchangeItemCountConfigData;
            m_exchangeConfigData = exchangeConfigData;

            UICustomContext context = new UICustomContext(typeof(ExchangeItemUI));
            UIManager.instance.Open(context, true);
        }

        protected override void OnInit()
        {
            RegisterListener((Int32)ExchangeItemUIEventID.CloseBtnClick, OnCloseBtnClick);
        }


        protected override void OnUnInit()
        {
            UnRegisterListener((Int32)ExchangeItemUIEventID.CloseBtnClick, OnCloseBtnClick);
        }


        protected override void OnRefreshViews()
        {
            if (m_exchangeConfigData != null)
            {
                mainView.UpdateView(m_exchangeItemCountConfigData, m_exchangeConfigData);
            }
        }

        private void OnCloseBtnClick()
        {
            m_exchangeConfigData = null;
            Close();
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private ExchangeItemUIView m_mainView;
        
        public ExchangeItemUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<ExchangeItemUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
