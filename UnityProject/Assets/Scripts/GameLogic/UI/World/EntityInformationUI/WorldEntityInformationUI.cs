using System;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public enum WorldEntityInformationUIEventId
    {
        CloseBtnClick
    }

    public class WorldEntityInformationUI : UIBase
    {
        private static String WorldEntityInformationIdParamKey = "WorldEntityInformationId";


        public static void ShowWorldEntityInformationUI(Int32 informationId)
        {
            UICustomContext context = new UICustomContext(typeof(WorldEntityInformationUI));
            context.SetParam(WorldEntityInformationIdParamKey, informationId);
            UIManager.instance.Open(context, true);
        }

        protected override void OnInit()
        {
            RegisterListener((Int32)WorldEntityInformationUIEventId.CloseBtnClick, OnCloseButtonClick);
        }

        protected override void OnUnInit()
        {
            UnRegisterListener((Int32)WorldEntityInformationUIEventId.CloseBtnClick, OnCloseButtonClick);
        }


        protected override void OnRefreshViews()
        {
            EventManager.instance.Broadcast(EventID.WorldUIShowOrHide, false);
            if (m_currentContext is UICustomContext context)
            {
                Int32 informationId = context.GetStructParam<Int32>(WorldEntityInformationIdParamKey);
                mainView.UpdateView(informationId);

            }
        }


        private void OnCloseButtonClick()
        {
            EventManager.instance.Broadcast(EventID.WorldUIShowOrHide, true);
            Close();
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private WorldEntityInformationUIView m_mainView;
        
        public WorldEntityInformationUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<WorldEntityInformationUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
