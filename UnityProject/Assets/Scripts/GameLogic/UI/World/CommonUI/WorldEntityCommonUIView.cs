using System;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class WorldEntityCommonUIView : UIView
    {

        public void ShowOrHudeWorldEntityCommonUI(Boolean isShow)
        {
            m_worldEntityHudView.SetActiveSafely(isShow);
        }

        protected override void OnTick(TimeSlice ts)
        {
            m_entityEmojiHudGroupView.Tick(ts);
        }


        #region AutoGen
        ////////------ Auto<PERSON>en Begin ------////////
        private WorldEntityHudGroupUIView m_worldEntityHudView;
        private WorldQuestGroupStateChangeUIView m_questStateChangeView;
        private WorldEntityEmojiGroupUIView m_entityEmojiHudGroupView;
        
        public WorldEntityHudGroupUIView worldEntityHudView
        {
            get { return m_worldEntityHudView; }
        }
        public WorldQuestGroupStateChangeUIView questStateChangeView
        {
            get { return m_questStateChangeView; }
        }
        public WorldEntityEmojiGroupUIView entityEmojiHudGroupView
        {
            get { return m_entityEmojiHudGroupView; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("WorldEntityHudView"), typeof(WorldEntityHudGroupUIView));
            AddBindComponent(group.GetItemInfoByName("QuestStateChangeView"), typeof(WorldQuestGroupStateChangeUIView));
            AddBindComponent(group.GetItemInfoByName("EntityEmojiHudGroupView"), typeof(WorldEntityEmojiGroupUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_worldEntityHudView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("WorldEntityHudView"), typeof(WorldEntityHudGroupUIView)) as WorldEntityHudGroupUIView;
            m_questStateChangeView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("QuestStateChangeView"), typeof(WorldQuestGroupStateChangeUIView)) as WorldQuestGroupStateChangeUIView;
            m_entityEmojiHudGroupView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityEmojiHudGroupView"), typeof(WorldEntityEmojiGroupUIView)) as WorldEntityEmojiGroupUIView;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
