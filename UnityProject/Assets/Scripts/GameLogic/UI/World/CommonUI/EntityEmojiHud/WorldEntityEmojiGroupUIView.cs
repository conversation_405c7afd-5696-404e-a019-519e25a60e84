


using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.World;

namespace Phoenix.GameLogic.UI
{
    public class WorldEntityEmojiGroupUIView : UIView
    {
        private List<UIView> uiViewItems = new List<UIView>();

        protected override void OnInit()
        {
            m_entityEmojiHudContent.Init(OnEntityEmojiHudPoolInit);
            EventManager.instance.RegisterListener<Int32, String>(EventID.WorldEntityViewEmojiHudShow, OnWorldEntityViewEmojiHud);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            EventManager.instance.UnRegisterListener<Int32, String>(EventID.WorldEntityViewEmojiHudShow, OnWorldEntityViewEmojiHud);
        }
        protected override void OnTick(TimeSlice ts)
        {
            if (uiViewItems.Count > 0)
            {
                for (Int32 i = 0; i < uiViewItems.Count; i++)
                {
                    if (uiViewItems.Count > i && uiViewItems[i] != null)
                    {
                        uiViewItems[i].Tick(ts);
                    }
                }
            }

            //if (Input.GetKeyDown(KeyCode.H))
            //{
            //    EventManager.instance.Broadcast(EventID.WorldEntityViewEmojiHudShow, 1007, "Happy");
            //}
            //if (Input.GetKeyDown(KeyCode.U))
            //{
            //    EventManager.instance.Broadcast(EventID.WorldEntityViewEmojiHudShow, 1005, "Speechless");
            //}
        }

        private void OnWorldEntityViewEmojiHud(Int32 entityId, String emojiName)
        {
            WorldEntityView worldEntityView = WorldManager.instance.GetWorldEntityView(entityId);
            if (worldEntityView != null)
            {
                WorldEntityEmojiItemUIView view = m_entityEmojiHudContent.FetchComponent<WorldEntityEmojiItemUIView>();
                view.ShowEmoji(worldEntityView, emojiName);
                uiViewItems.Add(view);
                float duration = WorldConfigSetting.instance.m_emojiHudShowTime;
                TimerManager.instance.Start(duration, () =>
                {
                    uiViewItems.Remove(view);
                    m_entityEmojiHudContent.Release(view.gameObject);
                });
            }
        }

        private void OnEntityEmojiHudPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            WorldEntityEmojiItemUIView itemView = holder.AddComponent<WorldEntityEmojiItemUIView>();
            itemView.Init(this);
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObjectPool m_entityEmojiHudContent;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_entityEmojiHudContent = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityEmojiHudContent"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
