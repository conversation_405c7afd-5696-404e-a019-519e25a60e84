using System;
using Phoenix.Core;
using Phoenix.Hakoniwa;
using Phoenix.Hakoniwa.Logic;
using UnityEngine;

namespace Phoenix.GameLogic.UI
{
    public enum HakoEntityInteractionUIEventId
    {
        CloseBtnClick,
        InteractItemClick,
    }
    public class HakoEntityInteractionUI : UIBase
    {

        public static void Show()
        {
            UIManager.instance.Open<HakoEntityInteractionUI>(true);
        }

        protected override void OnInit()
        {
            RegisterListener<EntityInteraction>((Int32)WorldEntityDialogInteractionUIEventId.InteractItemClick, OnInteractItemBtnClick);
            RegisterListener((Int32)WorldEntityDialogInteractionUIEventId.CloseBtnClick, OnCloseButtonClick);
        }


        protected override void OnUnInit()
        {
            UnRegisterListener<EntityInteraction>((Int32)WorldEntityDialogInteractionUIEventId.InteractItemClick, OnInteractItemBtnClick);
            UnRegisterListener((Int32)WorldEntityDialogInteractionUIEventId.CloseBtnClick, OnCloseButtonClick);
        }


        protected override void OnRefreshViews()
        {
            //EventManager.instance.Broadcast(EventID.WorldUIShowOrHide, false);

            HakoEntityView entityView = StaticHakoniwa.mainPlayerHandler.InteractEntityView;
            m_mainView.UpdateView(entityView);
        }

        protected override void OnPlayCloseEnd()
        {
            //EventManager.instance.Broadcast(EventID.WorldUIShowOrHide, true);
        }

        private void OnInteractItemBtnClick(EntityInteraction interaction)
        {
            m_mainView.Hide();
            Close();
            InteractionCore.Interact(interaction);
        }


        private void OnCloseButtonClick()
        {
            m_mainView.Hide();
            Close();
        }




        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private HakoEntityInteractionUIView m_mainView;
        
        public HakoEntityInteractionUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<HakoEntityInteractionUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
