using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class AutoJoystick : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON>own<PERSON><PERSON><PERSON>, <PERSON>oint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler
    {
        [SerializeField]
        private Boolean m_joystickActiveState;
        [SerializeField]
        private Int32 m_inputId = Int32.MinValue;

        [SerializeField]
        private Rect m_joystickTouchPadRect = new Rect(200, 200, 300, 300);
        [SerializeField]
        private Rect m_joystickBaseRect = new Rect(0, 0, 300, 300);

        [SerializeField]
        private Canvas m_parentCanvas;
        private RectTransform m_parentCanvasTrans;

        [SerializeField]
        private RectTransform m_joystickParent;
        [SerializeField]
        private RectTransform m_joystickBase;
        [SerializeField]
        private RectTransform m_joystickInner;

        [SerializeField]
        private JoystickAxis m_joystickAxis = JoystickAxis.Both;
        [SerializeField]
        private JoystickBoundary m_joystickBoundary = JoystickBoundary.Square;
        [SerializeField]
        private JoystickPosition m_joystickPosition = JoystickPosition.Fixed;

        private float m_joystickRadius = 1;
        [SerializeField]
        private float m_joystickBasePadding = 0;
        [SerializeField]
        private float m_joystickHorizontalAxis;
        [SerializeField]
        private float m_joystickVerticalAxis;

        private Vector2 m_joystickBaseOriginPosition = Vector2.zero;
        private Vector2 m_joystickBaseCurrentPosition = Vector2.zero;

        public float JoystickHorizontalAxis { get { return m_joystickHorizontalAxis; } }
        public float JoystickVerticalAxis { get { return m_joystickVerticalAxis; } }

        public event Action EventOnInputDown;
        public event Action EventOnInputMove;
        public event Action EventOnInputUp;

        void Awake()
        {
            InitializeGameObject();
            InitializeJoystickDefaultValue();
        }

        void Update()
        {
            ProcessTouchInput();

            if (m_joystickActiveState)
            {
                if (EventOnInputMove != null)
                    EventOnInputMove();
            }
        }

        void OnValidate()
        {
            try
            {
                InitializeJoystickDefaultValue();
            }
            catch(Exception e)
            {

            }
        }

        void InitializeGameObject()
        {
            m_parentCanvas = GetComponentInParent<Canvas>();
            m_parentCanvasTrans = m_parentCanvas.GetComponent<RectTransform>();

            m_joystickParent = gameObject.GetComponent<RectTransform>();
            m_joystickParent.GetOrAddComponent<Image>();

            if (m_joystickBase == null)
            {
                GameObject go = new GameObject("JoystickBase");
                go.SetParent(m_joystickParent.gameObject);
                m_joystickBase = go.AddComponent<RectTransform>();
            }
            m_joystickBase.GetOrAddComponent<Image>();
            if (m_joystickInner == null)
            {
                GameObject go = new GameObject("JoystickInner");
                go.SetParent(m_joystickBase.gameObject);
                m_joystickInner = go.AddComponent<RectTransform>();
            }
            m_joystickInner.GetOrAddComponent<Image>();
        }

        void InitializeJoystickDefaultValue()
        {
            m_joystickRadius = m_joystickBaseRect.width / 2;
            m_joystickParent.anchoredPosition = m_joystickTouchPadRect.position;
            m_joystickParent.sizeDelta = m_joystickTouchPadRect.size;
            m_joystickParent.anchorMin = Vector2.zero;
            m_joystickParent.anchorMax = Vector2.zero;
            m_joystickParent.pivot = Vector2.zero;
            Image image = m_joystickParent.GetComponent<Image>();
            image.raycastTarget = true;
            image.color = new Color(1, 1, 1, 0.2f);


            m_joystickBase.anchoredPosition = m_joystickBaseRect.position;
            m_joystickBase.sizeDelta = m_joystickBaseRect.size;
            m_joystickBase.anchorMin = Vector2.one * 0.5f;
            m_joystickBase.anchorMax = Vector2.one * 0.5f;
            m_joystickBase.pivot = Vector2.one * 0.5f;
            image = m_joystickBase.GetComponent<Image>();
            image.raycastTarget = false;
            image.color = new Color(1, 1, 1, 0.3f);
            m_joystickBaseCurrentPosition = m_joystickBaseOriginPosition = m_joystickBase.localPosition;


            m_joystickInner.anchorMin = Vector2.one * 0.5f;
            m_joystickInner.anchorMax = Vector2.one * 0.5f;
            m_joystickInner.pivot = Vector2.one * 0.5f;
            m_joystickInner.sizeDelta = m_joystickBaseRect.size / 3;
            m_joystickInner.localPosition = Vector3.zero;
            image = m_joystickInner.GetComponent<Image>();
            image.raycastTarget = false;
            image.color = new Color(1, 1, 1, 0.5f);
        }

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
            ProcessOnInputDown(eventData.position, eventData.pointerId);
        }

        void IDragHandler.OnDrag(PointerEventData eventData)
        {
            ProcessOnInputMove(eventData.position, eventData.pointerId);
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
            ProcessOnInputUp(eventData.position, eventData.pointerId);
        }

        private void ProcessTouchInput()
        {
            if (Input.touchCount > 0)
            {
                for (int fingerId = 0; fingerId < Input.touchCount; fingerId++)
                {
                    TouchPhase touchPhase = Input.GetTouch(fingerId).phase;
                    if (touchPhase == TouchPhase.Began)
                    {
                        Vector2 touchPosition = Input.GetTouch(fingerId).position;
                        if (m_joystickBaseRect.Contains(touchPosition))
                        {
                            ProcessOnInputDown(touchPosition, fingerId);
                        }
                    }
                    else if (touchPhase == TouchPhase.Moved)
                    {
                        Vector2 touchPosition = Input.GetTouch(fingerId).position;
                        ProcessOnInputMove(touchPosition, fingerId);
                    }
                    else if (touchPhase == TouchPhase.Ended || touchPhase == TouchPhase.Canceled)
                    {
                        Vector2 touchPosition = Input.GetTouch(fingerId).position;
                        ProcessOnInputUp(touchPosition, fingerId);
                    }
                }
            }
        }

        private void ProcessOnInputDown(Vector2 inputPosition, Int32 inputId)
        {
            if (m_joystickActiveState)
                return;
            m_inputId = inputId;
            m_joystickActiveState = true;

            //Debug.Log(String.Format("[Joystick] ProcessOnInputDown, position: {0}, id: {1}", inputPosition, inputId));

            if (m_joystickPosition == JoystickPosition.Dynamic)
            {
                m_joystickBaseCurrentPosition = m_joystickParent.InverseTransformPoint(inputPosition);
                m_joystickBase.localPosition = m_joystickBaseCurrentPosition;
            }

            UpdateJoystickInnerPosition(inputPosition);

            m_joystickBase.SetActiveSafely(true);

            if (EventOnInputDown != null)
                EventOnInputDown();
        }

        private void ProcessOnInputMove(Vector2 inputPosition, Int32 inputId)
        {
            if (inputId != m_inputId)
                return;

            // Debug.Log(String.Format("[Joystick] ProcessOnInputMove, position: {0}, id: {1}", position, inputId));

            UpdateJoystickInnerPosition(inputPosition);

            if (EventOnInputMove != null)
                EventOnInputMove();
        }

        private void ProcessOnInputUp(Vector2 inputPosition, Int32 inputId)
        {
            if (inputId != m_inputId)
                return;

            // Debug.Log(String.Format("[Joystick] ProcessOnInputUp, position: {0}, id: {1}", position, inputId));
            ResetJoystick();

            if (EventOnInputUp != null)
                EventOnInputUp();
        }


        private void UpdateJoystickInnerPosition(Vector2 inputPosition)
        {
            Vector2 tempPosition = m_joystickParent.InverseTransformPoint(inputPosition);
            Vector2 finalPosition = tempPosition - m_joystickBaseCurrentPosition;

            if (m_joystickAxis == JoystickAxis.X) finalPosition.y = 0;
            if (m_joystickAxis == JoystickAxis.Y) finalPosition.x = 0;

            if (m_joystickBoundary == JoystickBoundary.Square)
            {
                finalPosition.x = Mathf.Clamp(finalPosition.x, -m_joystickRadius + m_joystickBasePadding, m_joystickRadius - m_joystickBasePadding);
                finalPosition.y = Mathf.Clamp(finalPosition.y, -m_joystickRadius + m_joystickBasePadding, m_joystickRadius - m_joystickBasePadding);
            }
            else if (m_joystickBoundary == JoystickBoundary.Circle)
            {
                // Circle
                finalPosition = Vector2.ClampMagnitude(finalPosition, m_joystickRadius - m_joystickBasePadding);
            }

            m_joystickInner.localPosition = finalPosition;
            //m_joystick.localPosition = (Vector2)m_joystickBase.InverseTransformPoint(m_parentCanvasTrans.TransformPoint(inputPosition)) - (m_parentCanvasTrans.sizeDelta / 2);

            m_joystickHorizontalAxis = finalPosition.x / m_joystickRadius;
            m_joystickVerticalAxis = finalPosition.y / m_joystickRadius;
        }

        private void ResetJoystick()
        {
            m_inputId = Int32.MinValue;
            m_joystickActiveState = false;
            m_joystickHorizontalAxis = 0;
            m_joystickVerticalAxis = 0;
            m_joystickInner.localPosition = Vector3.zero;
            m_joystickBase.localPosition = m_joystickBaseOriginPosition;
        }

        private enum JoystickAxis : byte
        {
            Both,
            X,
            Y
        }

        private enum JoystickBoundary : byte
        {
            Square,
            Circle,
        }

        private enum JoystickPosition : byte
        {
            Fixed,
            Dynamic,
        }
    }
}
