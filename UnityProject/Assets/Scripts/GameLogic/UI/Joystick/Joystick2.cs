using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    /// <summary>
    /// 
    /// 
    /// 
    /// TODO : YDS m_joystickTouchPadRect 可能存在按照屏幕比例来动态划分的情况
    /// </summary>

    public class Joystick2 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IDragHandler
    {
        public Single m_DeadZone = 0.15f;
        public Int32 m_Precision = 10000;

        [SerializeField]
        private Boolean m_enableDebug;
        [SerializeField]
        private GameObject[] m_debugGoList;
        [SerializeField]
        private Boolean m_joystickActiveState;
        [SerializeField]
        private Int32 m_inputId = Int32.MinValue;

        [SerializeField]
        private Rect m_joystickTouchPadRect = new Rect(200, 200, 300, 300);
        [SerializeField]
        private Rect m_joystickBaseRect = new Rect(0, 0, 300, 300);

        [SerializeField]
        private Canvas m_parentCanvas;

        [SerializeField]
        private RectTransform m_joystickParent;
        [SerializeField]
        private RectTransform m_joystickBase;
        [SerializeField]
        private RectTransform m_joystickInner;

        [SerializeField]
        private JoystickAxis m_joystickAxis = JoystickAxis.Both;
        [SerializeField]
        private JoystickBoundary m_joystickBoundary = JoystickBoundary.Square;
        [SerializeField]
        private JoystickPosition m_joystickPosition = JoystickPosition.Fixed;

        private float m_joystickRadius = 1;
        [SerializeField]
        private float m_joystickBasePadding = 0;
        [SerializeField]
        private float m_joystickHorizontalAxis;
        [SerializeField]
        private float m_joystickVerticalAxis;

        [SerializeField]
        private float m_HorizontalAxisPostValue;
        [SerializeField]
        private float m_VerticalAxisPostValue;

        private Vector2 m_joystickBaseLocalPosition = Vector2.zero;
        private Vector2 m_joystickBaseScreenPosition = Vector2.zero;

        public Boolean IsInDeadZone { get { return Mathf.Abs(m_joystickHorizontalAxis) < m_DeadZone && Mathf.Abs(m_joystickVerticalAxis) < m_DeadZone; } }

        public float JoystickHorizontalAxis { get { return m_joystickHorizontalAxis; } }
        public float JoystickVerticalAxis { get { return m_joystickVerticalAxis; } }

        public float HorizontalAxisPostValue
        {
            get
            {
                return m_HorizontalAxisPostValue;
                //return Mathf.Abs(m_joystickHorizontalAxis) < DEADZONE ? 0 : m_HorizontalAxisPostValue;
            }
        }
        public float VerticalAxisPostValue
        {
            get
            {
                return m_VerticalAxisPostValue;
                //return Mathf.Abs(m_joystickVerticalAxis) < DEADZONE ? 0 : m_VerticalAxisPostValue;
            }
        }
        public Vector2 AxisPostValue
        {
            get { return new Vector2(HorizontalAxisPostValue, VerticalAxisPostValue); }
        }

        public event Action EventOnInputDown;
        public event Action EventOnInputMove;
        public event Action EventOnInputUp;

        public void UpdateJoystickInnerPositionWithKeyboard(Vector2 vector)
        {
            vector.x = vector.x * m_joystickRadius;
            vector.y = vector.y * m_joystickRadius;
            vector = ProcessJoystickInnerRawPosition(vector);
            UpdateJoystickInnerPosition(vector / m_parentCanvas.scaleFactor);
        }

        public void UpdateJoystickDebugState(Boolean debug)
        {
            if (m_enableDebug ^ debug)
            {
                m_enableDebug = debug;
                UpdateDebugState();
            }
        }


        void Awake()
        {
            InitializeGameObject();
            InitializeJoystickDefaultValue();
            UpdateDebugState();
        }

        void Update()
        {
            //ProcessTouchInput();

            if (m_joystickActiveState)
            {
                if (EventOnInputMove != null)
                    EventOnInputMove();
            }
            m_joystickRadius = m_joystickBaseRect.width / 2 * m_parentCanvas.scaleFactor;
        }

        void OnValidate()
        {
            try
            {
                InitializeJoystickDefaultValue();
                UpdateDebugState();
            }
            catch (Exception e)
            {

            }
        }

        void BroadcastMessage_OnScreenResize()
        {
            InitializeJoystickDefaultValue();
        }

        void InitializeGameObject()
        {
            m_parentCanvas = GetComponentInParent<Canvas>();

            m_joystickParent = gameObject.GetComponent<RectTransform>();
            m_joystickParent.GetOrAddComponent<ScreenResizeUpdater>();
            m_joystickParent.GetOrAddComponent<Image>();

            if (m_joystickBase == null)
            {
                GameObject go = new GameObject("JoystickBase");
                go.SetParent(m_joystickParent.gameObject);
                m_joystickBase = go.AddComponent<RectTransform>();
            }
            m_joystickBase.GetOrAddComponent<Image>();
            if (m_joystickInner == null)
            {
                GameObject go = new GameObject("JoystickInner");
                go.SetParent(m_joystickBase.gameObject);
                m_joystickInner = go.AddComponent<RectTransform>();
            }
            m_joystickInner.GetOrAddComponent<Image>();
        }

        void InitializeJoystickDefaultValue()
        {
            m_joystickParent.anchoredPosition = m_joystickTouchPadRect.position;
            m_joystickParent.sizeDelta = m_joystickTouchPadRect.size;
            m_joystickParent.anchorMin = Vector2.zero;
            m_joystickParent.anchorMax = Vector2.zero;
            m_joystickParent.pivot = Vector2.zero;
            Image image = m_joystickParent.GetComponent<Image>();
            image.raycastTarget = true;

            m_joystickBase.anchoredPosition = m_joystickBaseRect.position;
            m_joystickBase.sizeDelta = m_joystickBaseRect.size;
            m_joystickBase.anchorMin = Vector2.one * 0.5f;
            m_joystickBase.anchorMax = Vector2.one * 0.5f;
            m_joystickBase.pivot = Vector2.one * 0.5f;
            image = m_joystickBase.GetComponent<Image>();
            image.raycastTarget = false;

            m_joystickBaseLocalPosition = m_joystickBase.localPosition;
            m_joystickBaseScreenPosition = RectTransformUtility.WorldToScreenPoint(m_parentCanvas.worldCamera, m_joystickBase.position);


            m_joystickInner.anchorMin = Vector2.one * 0.5f;
            m_joystickInner.anchorMax = Vector2.one * 0.5f;
            m_joystickInner.pivot = Vector2.one * 0.5f;
            m_joystickInner.sizeDelta = m_joystickBaseRect.size / 3;
            m_joystickInner.localPosition = Vector3.zero;
            image = m_joystickInner.GetComponent<Image>();
            image.raycastTarget = false;


            //if (m_joystickPosition == JoystickPosition.Dynamic)
            //{
            //    m_joystickBase.SetActiveSafely(false);
            //}
            //else
            //{
            //    m_joystickBase.SetActiveSafely(true);
            //}
        }

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
            ProcessOnInputDown(eventData.position, eventData.pointerId);
        }

        void IDragHandler.OnDrag(PointerEventData eventData)
        {
            ProcessOnInputMove(eventData.position, eventData.pointerId);
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
            ProcessOnInputUp(eventData.position, eventData.pointerId);
        }

        void OnDisable()
        {
            ProcessOnInputUp(Vector2.zero, m_inputId);
        }

        private void ProcessTouchInput()
        {
            if (Input.touchCount > 0)
            {
                for (int fingerId = 0; fingerId < Input.touchCount; fingerId++)
                {
                    TouchPhase touchPhase = Input.GetTouch(fingerId).phase;
                    if (touchPhase == TouchPhase.Began)
                    {
                        Vector2 touchPosition = Input.GetTouch(fingerId).position;
                        if (m_joystickBaseRect.Contains(touchPosition))
                        {
                            ProcessOnInputDown(touchPosition, fingerId);
                        }
                    }
                    else if (touchPhase == TouchPhase.Moved)
                    {
                        Vector2 touchPosition = Input.GetTouch(fingerId).position;
                        ProcessOnInputMove(touchPosition, fingerId);
                    }
                    else if (touchPhase == TouchPhase.Ended || touchPhase == TouchPhase.Canceled)
                    {
                        Vector2 touchPosition = Input.GetTouch(fingerId).position;
                        ProcessOnInputUp(touchPosition, fingerId);
                    }
                }
            }
        }

        private void ProcessOnInputDown(Vector2 inputPosition, Int32 inputId)
        {
            if (m_joystickActiveState)
                return;
            m_inputId = inputId;
            m_joystickActiveState = true;

            // Debug.Log(String.Format("[Joystick] ProcessOnInputDown, position: {0}, id: {1}", inputPosition, inputId));

            if (m_joystickPosition == JoystickPosition.Dynamic)
            {
                m_joystickBaseScreenPosition = inputPosition;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(m_joystickParent, m_joystickBaseScreenPosition, m_parentCanvas.worldCamera, out Vector2 tempPosition);
                m_joystickBase.anchoredPosition = tempPosition - m_joystickTouchPadRect.size / 2;
            }
            // m_joystickBase.SetActiveSafely(true);

            UpdateJoystickAxis(inputPosition);


            if (EventOnInputDown != null)
                EventOnInputDown();
        }

        private void ProcessOnInputMove(Vector2 inputPosition, Int32 inputId)
        {
            if (inputId != m_inputId)
                return;

            // Debug.Log(String.Format("[Joystick] ProcessOnInputMove, position: {0}, id: {1}", inputId, inputId));

            UpdateJoystickAxis(inputPosition);

            if (EventOnInputMove != null)
                EventOnInputMove();
        }

        private void ProcessOnInputUp(Vector2 inputPosition, Int32 inputId)
        {
            if (inputId != m_inputId)
                return;

            // Debug.Log(String.Format("[Joystick] ProcessOnInputUp, position: {0}, id: {1}", inputPosition, inputId));
            ResetJoystick();

            //if (m_joystickPosition == JoystickPosition.Dynamic)
            //{
            //    m_joystickBase.SetActiveSafely(false);
            //}

            if (EventOnInputUp != null)
                EventOnInputUp();
        }

        private void UpdateJoystickAxis(Vector2 inputPosition)
        {
            Vector2 finalPosition = inputPosition - m_joystickBaseScreenPosition;
            finalPosition = ProcessJoystickInnerRawPosition(finalPosition);
            UpdateJoystickInnerPosition(finalPosition / m_parentCanvas.scaleFactor);
            m_joystickHorizontalAxis = finalPosition.x / m_joystickRadius;
            m_joystickVerticalAxis = finalPosition.y / m_joystickRadius;

            Single angle = Mathf.Atan2(finalPosition.y, finalPosition.x);
            m_HorizontalAxisPostValue = Mathf.Round(Mathf.Cos(angle) * m_Precision) / m_Precision;
            m_VerticalAxisPostValue = Mathf.Round(Mathf.Sin(angle) * m_Precision) / m_Precision;

        }

        private void ResetJoystick()
        {
            m_inputId = Int32.MinValue;
            m_joystickActiveState = false;
            m_joystickHorizontalAxis = 0;
            m_joystickVerticalAxis = 0;
            m_HorizontalAxisPostValue = 0;
            m_VerticalAxisPostValue = 0;
            UpdateJoystickInnerPosition(Vector3.zero);

            m_joystickBase.localPosition = m_joystickBaseLocalPosition;
            m_joystickBaseScreenPosition = RectTransformUtility.WorldToScreenPoint(m_parentCanvas.worldCamera, m_joystickBase.position);
        }

        private Vector2 ProcessJoystickInnerRawPosition(Vector2 rawPosition)
        {
            Vector2 finalPosition = rawPosition;
            if (m_joystickAxis == JoystickAxis.X) finalPosition.y = 0;
            if (m_joystickAxis == JoystickAxis.Y) finalPosition.x = 0;

            if (m_joystickBoundary == JoystickBoundary.Square)
            {
                float scalePadding = m_joystickBasePadding * m_parentCanvas.scaleFactor;
                finalPosition.x = Mathf.Clamp(finalPosition.x, -m_joystickRadius + scalePadding, m_joystickRadius - scalePadding);
                finalPosition.y = Mathf.Clamp(finalPosition.y, -m_joystickRadius + scalePadding, m_joystickRadius - scalePadding);
            }
            else if (m_joystickBoundary == JoystickBoundary.Circle)
            {
                // Circle
                float scalePadding = m_joystickBasePadding * m_parentCanvas.scaleFactor;
                finalPosition = Vector2.ClampMagnitude(finalPosition, m_joystickRadius - scalePadding);
            }
            return finalPosition;
        }

        private void UpdateJoystickInnerPosition(Vector3 localPosition)
        {
            m_joystickInner.localPosition = localPosition;
        }


        private void OnGUI()
        {
            if (m_enableDebug)
            {
                Int32 unit = Screen.width / 50;
                Int32 index = 0;
                GUI.Label(new Rect(unit, unit * ++index, 300, unit * index), $"CanvasScaleFactor:{m_parentCanvas.scaleFactor}", new GUIStyle() { fontSize = unit });
                GUI.Label(new Rect(unit, unit * ++index, 300, unit * index), $"Mouses:{Input.mousePosition}", new GUIStyle() { fontSize = unit });
                GUI.Label(new Rect(unit, unit * ++index, 300, unit * index), $"Canvas:{Input.mousePosition / m_parentCanvas.scaleFactor}", new GUIStyle() { fontSize = unit });
                ++index;
                GUI.Label(new Rect(unit, unit * ++index, 300, unit * index), $"JoystickBaseLocalPosition:{m_joystickBaseLocalPosition}", new GUIStyle() { fontSize = unit });
                GUI.Label(new Rect(unit, unit * ++index, 300, unit * index), $"JoystickBaseScreenPosition:{m_joystickBaseScreenPosition}", new GUIStyle() { fontSize = unit });
                GUI.Label(new Rect(unit, unit * ++index, 300, unit * index), $"JoystickInnePosition:{m_joystickInner.localPosition}", new GUIStyle() { fontSize = unit });
            }
        }

        private void OnDrawGizmos()
        {
            if (m_enableDebug)
            {
                DrawBorder(m_joystickParent, Color.yellow);
                DrawBorder(m_joystickBase, Color.yellow);
                DrawBorder(m_joystickInner, Color.yellow);
            }
        }
        private void DrawBorder(RectTransform trans, Color color)
        {
            Color originColor = Gizmos.color;
            Gizmos.color = color;
            Vector3 pos0 = trans.TransformPoint(new Vector3(trans.rect.x, trans.rect.y));
            Vector3 pos1 = trans.TransformPoint(new Vector3(trans.rect.x + trans.rect.width, trans.rect.y));
            Vector3 pos2 = trans.TransformPoint(new Vector3(trans.rect.x + trans.rect.width, trans.rect.y + trans.rect.height));
            Vector3 pos3 = trans.TransformPoint(new Vector3(trans.rect.x, trans.rect.y + trans.rect.height));
            Gizmos.DrawLine(pos0, pos1);
            Gizmos.DrawLine(pos1, pos2);
            Gizmos.DrawLine(pos2, pos3);
            Gizmos.DrawLine(pos3, pos0);
            Gizmos.color = originColor;
        }

        private void UpdateDebugState()
        {
            foreach(GameObject go in m_debugGoList)
            {
                go.SetActiveSafely(m_enableDebug);
            }
        }

        #region Enum


        private enum JoystickAxis : byte
        {
            Both,
            X,
            Y
        }

        private enum JoystickBoundary : byte
        {
            Square,
            Circle,
        }

        private enum JoystickPosition : byte
        {
            Fixed,
            Dynamic,
        }

        #endregion
    }
}
