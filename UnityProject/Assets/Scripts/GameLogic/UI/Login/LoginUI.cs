using System;
using AK.Wwise;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public enum LoginUIEventId
    {
        StartGameBtnClick,
        StartGameTestBtnClick,
        GuideBtnClick,
    }

    public class LoginUI : UIBase
    {
        protected override void OnInit()
        {
            RegisterListener((Int32)LoginUIEventId.StartGameBtnClick, OnStartGameBtnClick);
            RegisterListener((Int32)LoginUIEventId.StartGameTestBtnClick, OnStartGameTestBtnClick);
            RegisterListener((Int32)LoginUIEventId.GuideBtnClick, OnGuideBtnClick);
        }
        protected override void OnUnInit()
        {
            UnRegisterListener((Int32)LoginUIEventId.StartGameBtnClick, OnStartGameBtnClick);
            UnRegisterListener((Int32)LoginUIEventId.StartGameTestBtnClick, OnStartGameTestBtnClick);
            UnRegisterListener((Int32)LoginUIEventId.GuideBtnClick, OnGuideBtnClick);
        }

        protected override void OnPlayOpenEnd()
        {
            WwiseAudioManager.Instance.PlayMusic("BGM_Login");
            base.OnPlayOpenEnd();
        }

        protected override void OnRefreshViews()
        {
            m_mainView.UpdateView();
        }


        private void OnStartGameBtnClick()
        {
            BattleLaunchUtility.EnterBattleByRecordOrInvoke(() =>
            {
                GameStateHelper.ChangeGameState(EGameState.Lobby, LoadingFlagId.Lobby, CloseLoginUI);
            });
            //BattleLaunchUtility.EnterBattleByRecordOrInvoke(HandleStartGame);
        }

        private void OnGuideBtnClick()
        {
            BattleLaunchUtility.EnterBattleByRecordOrInvoke(() =>
            {
                GameStateHelper.ChangeGameState(EGameState.Lobby, LoadingFlagId.Lobby, CloseLoginUI);
            });
            //GuideBattleUIOpenContext uiContext = new GuideBattleUIOpenContext();
            //uiContext.showGuide = false;
            //uiContext.showGuideBattleLevel = true;
            //UIManager.instance.Open(uiContext, true);
        }

        private void OnStartGameTestBtnClick()
        {
            BattleLaunchUtility.EnterBattleByRecordOrInvoke(() =>
            {
                GameStateHelper.ChangeGameState(EGameState.Lobby, LoadingFlagId.Lobby, CloseLoginUI);
            });
        }

        private void HandleStartGame()
        {
            if (Login.FirstEnterGameFlag)
            {
                GuideBattleUIOpenContext uiContext = new GuideBattleUIOpenContext();
                uiContext.showGuide = true;
                uiContext.showGuideBattleLevel = false;
                UIManager.instance.Open(uiContext, true);
            }
            else if (Login.EexpertFlag || Login.SkipGuideBattleFlag || Login.CompletedAllGuideBattleFlag)
            {
                LoginUtility.StartLoginGame(CloseLoginUI);
            }
            else
            {
                OnGuideBtnClick();
            }
        }


        private void CloseLoginUI()
        {
            //Close(true, true);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private LoginUIView m_mainView;

        public LoginUIView mainView
        {
            get { return m_mainView; }
        }

        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<LoginUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
