using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;
using System.Collections.Generic;
using Phoenix.Core.Entity;
using Phoenix.Common.Network;
using Cysharp.Threading.Tasks;
using Phoenix.MsgPackLogic;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class SelectServerUIView : UIView
    {
        private static string PlayerPrefsKey = "com.yareta.studio_";
        private string KEY_USER_ID = $"{PlayerPrefsKey}UserId";
        private string KEY_SELECTED_SERVER_NAME = $"{PlayerPrefsKey}SelectedServerName";
        private string KEY_SELECTED_SERVER_IP = $"{PlayerPrefsKey}SelectedServerIp";
        private string KEY_SELECTED_SERVER_PORT = $"{PlayerPrefsKey}SelectedServerPort";

        private ServerItem m_serverItem;
        private InputField m_inputField = null;
        protected override void OnInit()
        {
            m_btnClose.onClick.AddListener(OnClickCloseBtn);
            m_btnOpenServerList.onClick.AddListener(OnClickSelectServer);
            m_btnCloseServerList.onClick.AddListener(OnClickCloseServerList);
            m_btnConfirm.onClick.AddListener(OnClickLogin);
            m_btnCancel.onClick.AddListener(OnClickCloseBtn);
            m_contentPool.Init(OnItemPoolInit);

            m_inputField = m_objInput.GetComponent<InputField>();
        }

        public void UpdateView(List<ServerItem> listServerItem)
        {
            ShowAndHideServerList(false);

            string strUserId = PlayerPrefs.GetString(KEY_USER_ID, string.Empty);
            m_inputField.text = strUserId;

            string strSelectedServerName = PlayerPrefs.GetString(KEY_SELECTED_SERVER_NAME, string.Empty);
            string strSelectedServerIP = PlayerPrefs.GetString(KEY_SELECTED_SERVER_IP, string.Empty);
            string strSelectedServerPort = PlayerPrefs.GetString(KEY_SELECTED_SERVER_PORT, string.Empty);
            if (!string.IsNullOrEmpty(strSelectedServerIP))
            {
                m_serverItem = new ServerItem();
                m_serverItem.strName = strSelectedServerName;
                m_serverItem.strIP = strSelectedServerIP;
                m_serverItem.iPort = ushort.Parse(strSelectedServerPort);
                string strStatus = "<color=#00FF00>登录中</color>";
                if (EntityAdmin.instance.CurEntity == null)
                {
                    strStatus = "<color=#FF0000>离线中</color>";
                }
                m_txtSelected.text = $"{strSelectedServerName} - {strSelectedServerIP}:{strSelectedServerPort}  {strStatus}";
            }
            else
            {
                m_txtSelected.text = "请选择服务器";
            }
            
            m_contentPool.ReleaseAll();
            for (int i = 0; i < listServerItem.Count; i++)
            {
                ServerItem item = listServerItem[i];
                ServerItemView itemUIView = m_contentPool.FetchComponent<ServerItemView>();
                itemUIView.UpdateView(item);
            }
        }

        private void OnClickCloseBtn()
        {
            m_owner.Close();
        }

        private void OnClickSelectServer()
        {
            ShowAndHideServerList(true);
        }

        private void OnClickCloseServerList()
        {
            ShowAndHideServerList(false);
        }

        private void OnClickLogin()
        {
            if (string.IsNullOrEmpty(m_inputField.text))
            {
                TipUI.ShowTip("请输入账户");
                return;
            }

            if (string.IsNullOrEmpty(m_serverItem.strIP))
            {
                TipUI.ShowTip("请选择服务器");
                return;
            }

            _ = ConnectAndLogin();
            
            PlayerPrefs.SetString(KEY_USER_ID, m_inputField.text);
            PlayerPrefs.SetString(KEY_SELECTED_SERVER_NAME, m_serverItem.strName);
            PlayerPrefs.SetString(KEY_SELECTED_SERVER_IP, m_serverItem.strIP);
            PlayerPrefs.SetString(KEY_SELECTED_SERVER_PORT, m_serverItem.iPort.ToString());

        }

        private void OnItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            ServerItemView itemView = holder.AddComponent<ServerItemView>();
            itemView.Init(this);
        }

        private void ShowAndHideServerList(bool isShowServerList)
        {
            m_objServerList.SetActiveSafely(isShowServerList);
            m_txtSelected.SetActiveSafely(!isShowServerList);
            m_btnOpenServerList.SetActiveSafely(!isShowServerList);
            m_btnConfirm.SetActiveSafely(!isShowServerList);
            m_btnCancel.SetActiveSafely(!isShowServerList);
        }

        public void UpdateServerContent(ServerItem serverItem)
        {
            m_serverItem = serverItem;

            m_txtSelected.text = $"{serverItem.strName} - {serverItem.strIP}:{serverItem.iPort}";

            ShowAndHideServerList(false);
        }

        async UniTaskVoid ConnectAndLogin()
        {
            if (NetworkClient.instance.IsConnected)
            {
                Debug.LogWarning($"Already Connected. Try to disconnect...");
                await NetworkClient.instance.CloseAsync();
            }
            E_ConnectState e_ConnectState = await NetworkClient.instance.ConnectAsync(m_serverItem.strIP, m_serverItem.iPort);
            if (!NetworkClient.instance.IsConnected)
            {
                Debug.LogError($"Connect failed: {e_ConnectState.ToString()}");
                // TipUI.ShowTip($"连接失败: {e_ConnectState.ToString()}");
                return;
            }

            NetworkClient.instance.SendLoginByAuthTokenReq(m_inputField.text);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_btnClose;
        private Text m_txtSelected;
        private Button m_btnOpenServerList;
        private Button m_btnCloseServerList;
        private Button m_btnConfirm;
        private Button m_btnCancel;
        private GameObject m_objInput;
        private GameObject m_objServerList;
        private GameObjectPool m_contentPool;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_btnClose = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnClose"), typeof(Button)) as Button;
            m_txtSelected = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtSelected"), typeof(Text)) as Text;
            m_btnOpenServerList = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnOpenServerList"), typeof(Button)) as Button;
            m_btnCloseServerList = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnCloseServerList"), typeof(Button)) as Button;
            m_btnConfirm = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnConfirm"), typeof(Button)) as Button;
            m_btnCancel = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnCancel"), typeof(Button)) as Button;
            m_objInput = UIBindUtility.GetBindObject(group.GetItemInfoByName("objInput"));
            m_objServerList = UIBindUtility.GetBindObject(group.GetItemInfoByName("objServerList"));
            m_contentPool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ContentPool"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
