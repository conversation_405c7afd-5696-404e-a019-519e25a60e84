using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.Common.Network;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameLogic.UI
{
    public enum SelectServerUIEventID
    {
        CloseBtnClick,
        SelectedServerItem,
    }

    public struct ServerItem
    {
        public string strName;
        public string strIP;
        public ushort iPort;
    }

    

    public class SelectServerUI : UIBase
    {
        private List<ServerItem> m_listServerItem = new List<ServerItem>
        {
            //new ServerItem() { strName = "(子路)服务器1", strIP = "************", iPort = 16692 },
            new ServerItem() { strName = "(子路)服务器1", strIP = "************", iPort = 6666 },
            new ServerItem() { strName = "(子路)服务器2", strIP = "127.0.0.1", iPort = 16692 },
            new ServerItem() { strName = "(上焕)服务器1", strIP = "************", iPort = 6666 },
            new ServerItem() { strName = "(上焕)服务器2", strIP = "127.0.0.1", iPort = 17789 }
        };

        public static void ShowUI()
        {
            UICustomContext context = new UICustomContext(typeof(SelectServerUI));
            UIManager.instance.Open(context, true);
        }

        protected override void OnInit()
        {
            base.OnInit();

            // Event
            EventManager.instance.RegisterListener(EventID.Login_Success, OnLoginSuccess);

            // UI Event
            RegisterListener<ServerItem>((Int32)SelectServerUIEventID.SelectedServerItem, OnSelectedServerItem);

            // Network
            NetworkClient.instance.RegisterCallback<PlayerBasicCompDataNtf>(OnNotifyPlayerBasicInfo);
            NetworkClient.instance.RegisterCallback<PlayerDataSectionSyncEndNtf>(OnNotifyPlayerDataEnd);
        }


        protected override void OnUnInit()
        {
            // Event
            EventManager.instance.UnRegisterListener(EventID.Login_Success, OnLoginSuccess);

            // UI Event
            UnRegisterListener<ServerItem>((Int32)SelectServerUIEventID.SelectedServerItem, OnSelectedServerItem);

            // Network
            // NetworkClient.instance.UnRegisterCallback<PlayerBasicCompDataNtf>();
            // NetworkClient.instance.UnRegisterCallback<PlayerDataSectionSyncEndNtf>();

            base.OnUnInit();
        }

        protected override void OnRefreshViews()
        {
            mainView.UpdateView(m_listServerItem);
        }

        private void OnLoginSuccess()
        {
            Close();
        }

        private void OnSelectedServerItem(ServerItem serverItem)
        {
            mainView.UpdateServerContent(serverItem);
        }

        private void OnNotifyPlayerBasicInfo(MsgPackStructBase responseMsg)
        {
            var msgData = responseMsg as PlayerBasicCompDataNtf;
            if (msgData == null)
            {
                Debug.LogError("PlayerBasicCompDataNtf msgData is null");
                return;
            }
            // todo custom logic ...

        }

        private void OnNotifyPlayerDataEnd(MsgPackStructBase responseMsg)
        {
            var msgData = responseMsg as PlayerDataSectionSyncEndNtf;
            if (msgData == null)
            {
                Debug.LogError("PlayerDataSectionSyncEndNtf msgData is null");
                return;
            }

            // todo custom logic ...
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private SelectServerUIView m_mainView;
        
        public SelectServerUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<SelectServerUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
