using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public enum QuestGroupUIEventId
    {
        CloseBtnClick,
        QuestGroupItemClick,
        QuestTracingBtnClick,
    }

    public class QuestGroupUI : UIBase
    {
        private Int32 m_tracingGroupId = 1;
        private Int32 m_selectGroupId = 1;

        public static void ShowQuestGroupUI()
        {
            UICustomContext context = new UICustomContext(typeof(QuestGroupUI));
            UIManager.instance.Open(context, true);
        }

        protected override void OnInit()
        {
            GamePlayerContext.instance.QuestModule.EventOnTracingQuestGroupUpdate += EventOnTracingQuestGroupUpdate;
            RegisterListener((Int32)QuestGroupUIEventId.CloseBtnClick, OnCloseBtnClick);
            RegisterListener<Int32>((Int32)QuestGroupUIEventId.QuestGroupItemClick, OnQuestGroupItemClick);
            RegisterListener((Int32)QuestGroupUIEventId.QuestTracingBtnClick, OnQuestTracingBtnClick);
        }


        protected override void OnUnInit()
        {
            GamePlayerContext.instance.QuestModule.EventOnTracingQuestGroupUpdate -= EventOnTracingQuestGroupUpdate;
            UnRegisterListener((Int32)QuestGroupUIEventId.CloseBtnClick, OnCloseBtnClick);
            UnRegisterListener<Int32>((Int32)QuestGroupUIEventId.QuestGroupItemClick, OnQuestGroupItemClick);
            UnRegisterListener((Int32)QuestGroupUIEventId.QuestTracingBtnClick, OnQuestTracingBtnClick);
        }


        protected override void OnRefreshViews()
        {
            m_selectGroupId = m_tracingGroupId = GamePlayerContext.instance.QuestModule.GetTracingQuestGroupId();
            UpdateMainUIView();
        }
        protected override void OnPlayOpen()
        {
            mainView.PlayAnimation(0, base.OnPlayOpen);
        }

        protected override void OnPlayClose()
        {
            mainView.PlayAnimation(1, base.OnPlayClose);
        }


        private void UpdateMainUIView()
        {
            List<QuestProcessor> questGroups = GamePlayerContext.instance.QuestModule.GetAllRunningQuestGroup();
            m_mainView.UpdateView(questGroups, m_tracingGroupId);
            if (questGroups.Count > 0)
            {
                UpdateQuestSelectGroupUIView(m_selectGroupId);
            }
        }
        private void UpdateQuestSelectGroupUIView(Int32 selectQuestGroupId)
        {
            QuestProcessor questProcessor = GamePlayerContext.instance.QuestModule.GetRunningQuestGroupProcessor(selectQuestGroupId);
            if (questProcessor != null)
            {
                m_mainView.SetItemSelectState(questProcessor);
            }
        }


        private void OnCloseBtnClick()
        {
            Close();
        }
        private void OnQuestGroupItemClick(Int32 questGroupId)
        {
            m_selectGroupId = questGroupId;
            UpdateQuestSelectGroupUIView(m_selectGroupId);
        }

        private void OnQuestTracingBtnClick()
        {
            if (m_tracingGroupId != m_selectGroupId)
            {
                GamePlayerContext.instance.ServerProxy.SendMessageChangeTracingQuestGroupId(m_selectGroupId);
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConfigData.ConstStringId.WorldTracingQuestTip));
            }
        }


        private void EventOnTracingQuestGroupUpdate(Int32 questGroupId)
        {
            if (m_tracingGroupId != questGroupId)
            {
                m_tracingGroupId = questGroupId;
                UpdateMainUIView();
            }
        }

#region AutoGen
        ////////------ AutoGen Begin ------////////
private QuestGroupUIView m_mainView;

public QuestGroupUIView mainView
{
    get { return m_mainView; }
}

protected override UIView OnAddView(GameObject viewObj, string name)
{
    switch (name)
    {
        case "MainView":
            m_mainView = viewObj.AddComponent<QuestGroupUIView>();
            return m_mainView;
    }
    return base.OnAddView(viewObj, name);
}
////////------ AutoGen End ------////////
#endregion AutoGen
    }
}
