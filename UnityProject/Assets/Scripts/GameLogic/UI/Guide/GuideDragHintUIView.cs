using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class GuideDragHintUIView : UIView
    {
        public void StartHint(Vector2 startScreenPos, Vector2 endScreenPos)
        {
            Vector2 startPos;
            Vector2 endPos;
            Camera uiCamera = canvas.worldCamera;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(transform.parent as RectTransform, startScreenPos, uiCamera, out startPos);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(transform.parent as RectTransform, endScreenPos, uiCamera, out endPos);
            m_state = EState.Appear;
            m_curTime = 0f;
            m_rectTrans.anchoredPosition = startPos;
            m_rectTrans.sizeDelta = new Vector2(m_rectTrans.sizeDelta.x, (endPos - startPos).magnitude / m_rectTrans.localScale.x);
            float angle = CalAngle(startPos, endPos);
            angle += 90f;//矫正（ui是朝下的，矫正成0度时朝右)
            m_rectTrans.localEulerAngles = Vector3.forward * angle;
            m_railHeight = m_railTrans.rect.height;
            m_handCanvas.transform.localEulerAngles = -Vector3.forward * angle;
            UpdateByState();
        }

        private void Update()
        {
            m_curTime += Time.deltaTime;
            UpdateByState();
        }

        private void UpdateByState()
        {
            if (m_state == EState.Appear)
            {
                float f = Mathf.Clamp01(m_curTime / m_appearTime);
                m_handCanvas.alpha = Mathf.Lerp(0.3f, 1f, f);
                m_handCanvas.transform.localScale = Vector3.one * Mathf.Lerp(1.5f, 1f, f);
                (m_handCanvas.transform as RectTransform).anchoredPosition = Vector3.up * m_railHeight;
                if (m_curTime >= m_appearTime)
                {
                    m_curTime -= m_appearTime;
                    m_state = EState.Move;
                    UpdateByState();
                }
            }
            else if (m_state == EState.Move)
            {
                float f = Mathf.Clamp01(m_curTime / m_moveTime);
                (m_handCanvas.transform as RectTransform).anchoredPosition = Vector3.Lerp(Vector3.zero, Vector3.up * m_railHeight, 1f - f);
                if (m_curTime >= m_moveTime)
                {
                    m_curTime -= m_moveTime;
                    m_state = EState.Hide;
                    UpdateByState();
                }
            }
            else if (m_state == EState.Hide)
            {
                float f = Mathf.Clamp01(m_curTime / m_hideTime);
                m_handCanvas.alpha = Mathf.Lerp(1f, 0f, f);
                if (m_curTime >= m_hideTime)
                {
                    m_curTime -= m_hideTime;
                    m_state = EState.Appear;
                    UpdateByState();
                }
            }
        }

        private float CalAngle(Vector3 startPos, Vector3 endPos)
        {
            Vector3 to = endPos - startPos;
            float angle;
            if (Mathf.Abs(to.x) < float.Epsilon)
            {
                angle = 90;
            }
            else
            {
                angle = Mathf.Atan(to.y / to.x) * Mathf.Rad2Deg;
            }
            if (to.y < 0f)
            {
                angle -= 180f;
            }
            return angle;
        }

        private float m_railHeight;
        private float m_curTime;
        private EState m_state;

        private const float m_appearTime = 0.5f;
        private const float m_moveTime = 1.5f;
        private const float m_hideTime = 0.3f;

        private enum EState
        {
            Appear,
            Move,
            Hide,
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private CanvasGroup m_canvasGroup;
        private RectTransform m_rectTrans;
        private CanvasGroup m_handCanvas;
        private RectTransform m_railTrans;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_canvasGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CanvasGroup"), typeof(CanvasGroup)) as CanvasGroup;
            m_rectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("RectTrans"));
            m_handCanvas = UIBindUtility.GetBindComponent(group.GetItemInfoByName("HandCanvas"), typeof(CanvasGroup)) as CanvasGroup;
            m_railTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("RailTrans"));
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
