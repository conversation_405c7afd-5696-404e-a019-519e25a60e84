using UnityEngine.UI;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class GuideUIView : UIView
    {
        protected override void OnInit()
        {
            base.OnInit();
            m_skipButton.onClick.AddListener(OnSkipBtnClicked);
        }

        public void ShowSkipBtn()
        {
            m_skipButton.SetActiveSafely(true);
        }

        public void HideSkipBtn()
        {
            m_skipButton.SetActiveSafely(false);
        }

        private void OnSkipBtnClicked()
        {
            Broadcast((int)GuideEventId.SkipBtnClicked);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GuideTipUIView m_tipView;
        private GuideHintUIView m_hintView;
        private Button m_skipButton;
        private GuideMaskUIView m_touchMaskView;
        private GuideGraphicUIView m_graphicView;
        
        public GuideTipUIView tipView
        {
            get { return m_tipView; }
        }
        public GuideHintUIView hintView
        {
            get { return m_hintView; }
        }
        public GuideMaskUIView touchMaskView
        {
            get { return m_touchMaskView; }
        }
        public GuideGraphicUIView graphicView
        {
            get { return m_graphicView; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("TipView"), typeof(GuideTipUIView));
            AddBindComponent(group.GetItemInfoByName("HintView"), typeof(GuideHintUIView));
            AddBindComponent(group.GetItemInfoByName("TouchMaskView"), typeof(GuideMaskUIView));
            AddBindComponent(group.GetItemInfoByName("GraphicView"), typeof(GuideGraphicUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_tipView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TipView"), typeof(GuideTipUIView)) as GuideTipUIView;
            m_hintView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("HintView"), typeof(GuideHintUIView)) as GuideHintUIView;
            m_skipButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkipButton"), typeof(Button)) as Button;
            m_touchMaskView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TouchMaskView"), typeof(GuideMaskUIView)) as GuideMaskUIView;
            m_graphicView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("GraphicView"), typeof(GuideGraphicUIView)) as GuideGraphicUIView;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
