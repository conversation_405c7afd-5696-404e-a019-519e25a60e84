using UnityEngine;
using Phoenix.Core;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;

namespace Phoenix.GameLogic.UI
{
    public class GuideUI : UIBase
    {
        private bool m_isGuidingDrag;

        protected override void OnInit()
        {
            RegisterListener();
            base.OnInit();
        }

        protected override void OnUnInit()
        {
            UnregisterListener();
            base.OnUnInit();
        }

        private void RegisterListener()
        {
            EventManager.instance.RegisterListener<int, int>(EventID.GuideTipShow, OnGuideTipShow);
            EventManager.instance.RegisterListener(EventID.GuideTipHide, OnGuideTipHide);
            EventManager.instance.RegisterListener<int, Vector3, int>(EventID.GuideHintShow, OnGuideHintShow);
            EventManager.instance.RegisterListener<int>(EventID.GuideHintShowNextClick, OnGuideHintShowNextClick);
            EventManager.instance.RegisterListener<int, int>(EventID.GuideTipShowNextClick, OnGuideTipShowNextClick);
            EventManager.instance.RegisterListener<int, List<Vector3>>(EventID.GuideHintShowByGrid, OnGuideHintShowByGrid);
            EventManager.instance.RegisterListener<int>(EventID.GuideHintHide, OnGuideHintHide);
            EventManager.instance.RegisterListener(EventID.GuideHintAllHide, OnGuideHintAllHide);
            EventManager.instance.RegisterListener<RectTransform>(EventID.GuideTouchMaskShowByRect, OnGuideTouchMaskByRect);
            EventManager.instance.RegisterListener<List<Vector3>>(EventID.GuideTouchMaskShowByVertexList, OnGuideTouchMaskByVertexList);
            EventManager.instance.RegisterListener<Bounds>(EventID.GuideTouchMaskShowByBounds, OnGuideTouchMaskByBounds);
            EventManager.instance.RegisterListener(EventID.GuideTouchMaskHide, OnGuideTouchMaskHide);
            EventManager.instance.RegisterListener<GameObject, List<Vector3>>(EventID.GuideDragActorToGrid, OnGuideDragActorToGrid);
            EventManager.instance.RegisterListener(EventID.GuideDragActorToGridEnd, OnGuideDragActorToGridEnd);
            EventManager.instance.RegisterListener<List<int>>(EventID.GuideGraphicShow, OnGuideGraphicShow);
            EventManager.instance.RegisterListener<Vector2>(EventID.GuideCenterBtnShow, OnGuideCenterBtnShow);
            EventManager.instance.RegisterListener(EventID.GuideHideAll, OnGuideHideAll);
            EventManager.instance.RegisterListener(EventID.GuideShowSkipBtn, OnShowSkipBtn);
        }

        private void UnregisterListener()
        {
            EventManager.instance.UnRegisterListener<int, int>(EventID.GuideTipShow, OnGuideTipShow);
            EventManager.instance.UnRegisterListener(EventID.GuideTipHide, OnGuideTipHide);
            EventManager.instance.UnRegisterListener<int, Vector3, int>(EventID.GuideHintShow, OnGuideHintShow);
            EventManager.instance.UnRegisterListener<int>(EventID.GuideHintShowNextClick, OnGuideHintShowNextClick);
            EventManager.instance.UnRegisterListener<int, int>(EventID.GuideTipShowNextClick, OnGuideTipShowNextClick);
            EventManager.instance.UnRegisterListener<int, List<Vector3>>(EventID.GuideHintShowByGrid, OnGuideHintShowByGrid);
            EventManager.instance.UnRegisterListener<int>(EventID.GuideHintHide, OnGuideHintHide);
            EventManager.instance.UnRegisterListener(EventID.GuideHintAllHide, OnGuideHintAllHide);
            EventManager.instance.UnRegisterListener<RectTransform>(EventID.GuideTouchMaskShowByRect, OnGuideTouchMaskByRect);
            EventManager.instance.UnRegisterListener<List<Vector3>>(EventID.GuideTouchMaskShowByVertexList, OnGuideTouchMaskByVertexList);
            EventManager.instance.UnRegisterListener<Bounds>(EventID.GuideTouchMaskShowByBounds, OnGuideTouchMaskByBounds);
            EventManager.instance.UnRegisterListener(EventID.GuideTouchMaskHide, OnGuideTouchMaskHide);
            EventManager.instance.UnRegisterListener<GameObject, List<Vector3>>(EventID.GuideDragActorToGrid, OnGuideDragActorToGrid);
            EventManager.instance.UnRegisterListener(EventID.GuideDragActorToGridEnd, OnGuideDragActorToGridEnd);
            EventManager.instance.UnRegisterListener<List<int>>(EventID.GuideGraphicShow, OnGuideGraphicShow);
            EventManager.instance.UnRegisterListener<Vector2>(EventID.GuideCenterBtnShow, OnGuideCenterBtnShow);
            EventManager.instance.UnRegisterListener(EventID.GuideHideAll, OnGuideHideAll);
            EventManager.instance.UnRegisterListener(EventID.GuideShowSkipBtn, OnShowSkipBtn);
        }

        private void OnGuideTipShow(int posId, int strId)
        {
            m_mainView.tipView.UpdateView(posId, strId);
        }

        private void OnGuideTipHide()
        {
            m_mainView.tipView.HideTip();
        }

        private void OnGuideHintShow(int hintId, Vector3 pos, int anchorId)
        {
            m_mainView.hintView.ShowHint(hintId, pos, anchorId);
        }

        private void OnGuideHintShowNextClick(int hintId)
        {
            m_mainView.hintView.ShowHintNextClick(hintId);
        }

        private void OnGuideTipShowNextClick(int tipId, int tipStrId)
        {
            m_mainView.tipView.ShowTipNextClick(tipId, tipStrId);
        }

        private void OnGuideHintShowByGrid(int hintId, List<Vector3> posList)
        {
            m_mainView.hintView.ShowHintByGrid(hintId, posList);
        }

        private void OnGuideHintHide(int hintId)
        {
            m_mainView.hintView.HideHint(hintId);
        }

        private void OnGuideHintAllHide()
        {
            m_mainView.hintView.HideAllHint();
        }

        private void OnGuideTouchMaskByRect(RectTransform rTrans)
        {
            m_mainView.touchMaskView.SetActiveSafely(true);
            m_mainView.touchMaskView.UpdateView(rTrans);
        }

        private void OnGuideTouchMaskByVertexList(List<Vector3> list)
        {
            m_mainView.touchMaskView.SetActiveSafely(true);
            m_mainView.touchMaskView.UpdateView(list);
        }

        private void OnGuideTouchMaskByBounds(Bounds bounds)
        {
            m_mainView.touchMaskView.SetActiveSafely(true);
            m_mainView.touchMaskView.UpdateView(bounds);
        }

        private void OnGuideTouchMaskHide()
        {
            m_mainView.touchMaskView.ClearButtonEvent();
            m_mainView.touchMaskView.SetActiveSafely(false);
            m_mainView.hintView.HideCachedHint();
            m_mainView.tipView.HideTip();
        }

        private void OnGuideDragActorToGrid(GameObject obj, List<Vector3> posList)
        {
            m_mainView.touchMaskView.SetActiveSafely(true);
            m_mainView.touchMaskView.UpdateView(obj.transform as RectTransform, false);
            m_mainView.hintView.StartDragHint(obj, posList);
            m_mainView.hintView.SetDragHintActive(true);
            m_isGuidingDrag = true;
        }

        private void OnGuideDragActorToGridEnd()
        {
            m_mainView.touchMaskView.SetActiveSafely(false);
            m_mainView.hintView.SetDragHintActive(false);
            m_isGuidingDrag = false;
        }

        private void OnGuideGraphicShow(List<int> idList)
        {
            m_mainView.graphicView.Show(idList);
        }

        private void OnGuideCenterBtnShow(Vector2 screenPos)
        {
            m_mainView.hintView.ShowCachedHint(screenPos);
            m_mainView.tipView.ShowCachedTip();
        }

        private void OnGuideHideAll()
        {
            m_mainView.graphicView.Hide();
            m_mainView.hintView.HideAllHint();
            m_mainView.touchMaskView.ClearView();
            m_mainView.touchMaskView.SetActiveSafely(false);
            m_mainView.tipView.HideTip();
            //m_mainCtrl.HideSkipBtn();
        }

        private void OnShowSkipBtn()
        {
            //m_mainCtrl.ShowSkipBtn();
        }

        private void OnSkipBtnClicked()
        {
            //GuideManager.Instance.SkipCurGuide();
        }

        private void OnMaskCenterBeginDrag(PointerEventData obj)
        {
            if (m_isGuidingDrag)
            {
                m_mainView.hintView.SetDragHintActive(false);
            }
        }

        private void OnMaskCenterEndDrag(PointerEventData obj)
        {
            if (m_isGuidingDrag)
            {
                m_mainView.hintView.SetDragHintActive(true);
            }
        }

        private void OnMaskCenterDrag(PointerEventData obj)
        {

        }

        private void OnGraphicEnd()
        {
            EventManager.instance.Broadcast(EventID.GuideGraphicShowEnd);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GuideUIView m_mainView;
        
        public GuideUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<GuideUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
