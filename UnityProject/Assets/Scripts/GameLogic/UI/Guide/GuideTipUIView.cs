using UnityEngine;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class GuideTipUIView : UIView
    {
        private int m_nextShowPosId = -1;
        private int m_nextShowStrId = -1;

        protected override void OnInit()
        {
            base.OnInit();
            this.SetActiveSafely(false);
        }

        public void UpdateView(int posId, int strId)
        {
            ShowTipInternal(posId, strId);
            m_nextShowPosId = -1;
            m_nextShowStrId = -1;
        }

        public void ShowTipNextClick(int posId, int strId)
        {
            m_nextShowPosId = posId;
            m_nextShowStrId = strId;
        }

        public void ShowCachedTip()
        {
            if (m_nextShowPosId >= 0)
            {
                ShowTipInternal(m_nextShowPosId, m_nextShowStrId);
            }
        }

        public void HideTip()
        {
            this.SetActiveSafely(false);
            m_nextShowStrId = -1;
            m_nextShowPosId = -1;
        }

        private int GetInventoryId(int posId)
        {
            return posId - 1;
        }

        private void ShowTipInternal(int index, int strId)
        {
            GameObject posObj = m_posInventory.Get(GetInventoryId(index));
            if (posObj != null)
            {
                this.SetActiveSafely(true);
                var strConfig = ConfigDataManager.instance.GetUserGuideTipStr(strId);
                m_tipText.text = strConfig != null ? strConfig.Desc : string.Empty;
                m_tipRoot.transform.position = posObj.transform.position;
            }
            else
            {
                this.SetActiveSafely(false);
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObjectInventory m_posInventory;
        private GameObject m_tipRoot;
        private Text m_tipText;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_posInventory = UIBindUtility.GetBindComponent(group.GetItemInfoByName("PosInventory"), typeof(GameObjectInventory)) as GameObjectInventory;
            m_tipRoot = UIBindUtility.GetBindObject(group.GetItemInfoByName("TipRoot"));
            m_tipText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TipText"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
