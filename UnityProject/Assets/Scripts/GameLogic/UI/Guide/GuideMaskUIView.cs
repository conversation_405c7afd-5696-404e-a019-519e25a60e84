using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class GuideMaskUIView : UIView
    {
        private GuideMaskCenterButton m_centerBtn;

        protected override void OnInit()
        {
            base.OnInit();
            m_centerBtn = m_centerObj.GetComponent<GuideMaskCenterButton>();
            m_centerBtn.actionOnPointerClick = OnPointerClick;
            m_centerBtn.actionOnPointerDown = OnPointerDown;
            m_centerBtn.actionOnPointerUp = OnPointerUp;
        }

        public void ClearView()
        {
            m_centerBtn.RegisterButton(null);
            m_centerBtn.SetActiveSafely(false);
        }

        public void UpdateView(RectTransform rTrans, bool showCenter = true)
        {
            Vector2 min = Vector2.zero, max = Vector2.zero;
            if (rTrans != null)
            {
                GuideUIUtility.GetScreenMinMax(rTrans, canvas.worldCamera, out min, out max);
            }
            UpdateViewInternal(min, max);
            if (showCenter)
            {
                m_centerBtn.SetActiveSafely(true);
                m_centerBtn.RegisterButton(rTrans != null ? rTrans.gameObject : null);
            }
            else
            {
                m_centerBtn.SetActiveSafely(false);
            }
        }

        private Camera m_MainCamera;

        public Camera MainCamera
        {
            get
            {
                if (m_MainCamera == null || !m_MainCamera.isActiveAndEnabled)
                {
                    m_MainCamera = Camera.main;
                }
                return m_MainCamera;
            }
        }

        public void UpdateView(List<Vector3> positionList)
        {
            Camera worldCamera = MainCamera;
            if (worldCamera == null)
            {
                return;
            }
            Vector2 min, max;
            GuideUIUtility.GetScreenMinMax(positionList, worldCamera, out min, out max);
            UpdateViewInternal(min, max);
            m_centerBtn.SetActiveSafely(true);
            m_centerBtn.RegisterButton(null);
        }

        public void UpdateView(Bounds bounds)
        {
            Camera worldCamera = MainCamera;
            if (worldCamera == null)
            {
                return;
            }
            Vector2 min, max;
            GuideUIUtility.GetScreenMinMax(bounds, worldCamera, out min, out max);
            UpdateViewInternal(min, max);
            m_centerBtn.SetActiveSafely(true);
            m_centerBtn.RegisterButton(null);
        }

        public void ClearButtonEvent()
        {
            m_centerBtn.RegisterButton(null);
        }

        private void UpdateViewInternal(Vector2 min, Vector2 max)
        {
            Vector2 minRate = GuideUIUtility.GetScreenRate(min);
            Vector2 maxRate = GuideUIUtility.GetScreenRate(max);
            SetAnchor(m_topLeftRectTrans, 0f, minRate.x, maxRate.y, 1f);
            SetAnchor(m_topCenterRectTrans, minRate.x, maxRate.x, maxRate.y, 1f);
            SetAnchor(m_topRightRectTrans, maxRate.x, 1f, maxRate.y, 1f);
            SetAnchor(m_centerLeftRectTrans, 0f, minRate.x, minRate.y, maxRate.y);
            SetAnchor(m_centerRightRectTrans, maxRate.x, 1f, minRate.y, maxRate.y);
            SetAnchor(m_centerRectTrans, minRate.x, maxRate.x, minRate.y, maxRate.y);
            SetAnchor(m_bottomLeftRectTrans, 0f, minRate.x, 0f, minRate.y);
            SetAnchor(m_bottomCenterRectTrans, minRate.x, maxRate.x, 0f, minRate.y);
            SetAnchor(m_bottomRightRectTrans, maxRate.x, 1f, 0f, minRate.y);
            EventManager.instance.Broadcast(EventID.GuideCenterBtnShow, (min + max) / 2f);
        }

        private void SetAnchor(RectTransform rTrans, float minX, float maxX, float minY, float maxY)
        {
            rTrans.anchorMin = new Vector2(minX, minY);
            rTrans.anchorMax = new Vector2(maxX, maxY);
            rTrans.offsetMin = Vector2.zero;
            rTrans.offsetMax = Vector2.zero;
        }

        private void OnPointerClick(PointerEventData eventData)
        {
            EventManager.instance.Broadcast(EventID.GuideCenterBtnClicked);
        }

        private void OnPointerDown(PointerEventData eventData)
        {
        }

        private void OnPointerUp(PointerEventData eventData)
        {
            EventManager.instance.Broadcast(EventID.GuideCenterBtnClickedUp);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private RectTransform m_centerRectTrans;
        private GameObject m_centerObj;
        private RectTransform m_topLeftRectTrans;
        private RectTransform m_topCenterRectTrans;
        private RectTransform m_topRightRectTrans;
        private RectTransform m_centerLeftRectTrans;
        private RectTransform m_centerRightRectTrans;
        private RectTransform m_bottomLeftRectTrans;
        private RectTransform m_bottomCenterRectTrans;
        private RectTransform m_bottomRightRectTrans;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_centerRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("CenterRectTrans"));
            m_centerObj = UIBindUtility.GetBindObject(group.GetItemInfoByName("CenterObj"));
            m_topLeftRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("TopLeftRectTrans"));
            m_topCenterRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("TopCenterRectTrans"));
            m_topRightRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("TopRightRectTrans"));
            m_centerLeftRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("CenterLeftRectTrans"));
            m_centerRightRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("CenterRightRectTrans"));
            m_bottomLeftRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("BottomLeftRectTrans"));
            m_bottomCenterRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("BottomCenterRectTrans"));
            m_bottomRightRectTrans = UIBindUtility.GetBindRectTransform(group.GetItemInfoByName("BottomRightRectTrans"));
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
