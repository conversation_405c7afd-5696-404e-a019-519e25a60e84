using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Phoenix.GameLogic.UI
{
    public static class GuideUIUtility
    {
        private static Vector3[] m_corners = new Vector3[4];
        private static List<Vector3> m_vertexList = new List<Vector3>();
        private static List<Transform> m_tempTransList = new List<Transform>();
        private static List<Transform> m_tempTransList2 = new List<Transform>();

        public static void GetScreenMinMax(RectTransform rTrans, Camera camera, out Vector2 min, out Vector2 max)
        {
            rTrans.GetWorldCorners(m_corners);
            min = new Vector2(float.MaxValue, float.MaxValue);
            max = new Vector2(float.MinValue, float.MinValue);
            for (int i = 0; i < m_corners.Length; ++i)
            {
                Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(camera, m_corners[i]);
                min = Vector2.Min(screenPoint, min);
                max = Vector2.Max(screenPoint, max);
            }
        }

        public static void GetScreenMinMax(List<Vector3> positionList, Camera camera, out Vector2 min, out Vector2 max)
        {
            min = new Vector2(float.MaxValue, float.MaxValue);
            max = new Vector2(float.MinValue, float.MinValue);
            for (int i = 0; i < positionList.Count; ++i)
            {
                Vector2 screenPoint = camera.WorldToScreenPoint(positionList[i]);
                min = Vector2.Min(screenPoint, min);
                max = Vector2.Max(screenPoint, max);
            }
        }

        public static void GetScreenMinMax(Bounds bounds, Camera camera, out Vector2 min, out Vector2 max)
        {
            m_vertexList.Clear();
            AddBoundVertex(bounds, true, true, true);
            AddBoundVertex(bounds, true, true, false);
            AddBoundVertex(bounds, true, false, true);
            AddBoundVertex(bounds, true, false, false);
            AddBoundVertex(bounds, false, true, true);
            AddBoundVertex(bounds, false, true, false);
            AddBoundVertex(bounds, false, false, true);
            AddBoundVertex(bounds, false, false, false);
            GetScreenMinMax(m_vertexList, camera, out min, out max);
        }

        public static Vector2 GetScreenRate(Vector2 screenPoint)
        {
            return new Vector2(screenPoint.x / Screen.width, screenPoint.y / Screen.height);
        }

        private static void AddBoundVertex(Bounds bounds, bool isXMin, bool isYMin, bool isZMin)
        {
            float x = isXMin ? bounds.min.x : bounds.max.x;
            float y = isYMin ? bounds.min.y : bounds.max.y;
            float z = isZMin ? bounds.min.z : bounds.max.z;
            m_vertexList.Add(new Vector3(x, y, z));
        }

        public static Transform GetHandlerChild(Transform parent)
        {
            m_tempTransList.Add(parent);
            while (m_tempTransList.Count > 0)
            {
                for (int i = 0; i < m_tempTransList.Count; ++i)
                {
                    Transform trans = m_tempTransList[i];
                    if (!trans.gameObject.activeInHierarchy)
                    {
                        continue;
                    }
                    IEventSystemHandler handler = trans.GetComponent<IEventSystemHandler>();
                    if (handler != null)
                    {
                        m_tempTransList.Clear();
                        m_tempTransList2.Clear();
                        return trans;
                    }
                    for (int j = 0; j < trans.childCount; ++j)
                    {
                        m_tempTransList2.Add(trans.GetChild(j));
                    }
                }
                m_tempTransList.Clear();
                m_tempTransList.AddRange(m_tempTransList2);
                m_tempTransList2.Clear();
            }
            m_tempTransList.Clear();
            m_tempTransList2.Clear();
            return null;
        }
    }
}
