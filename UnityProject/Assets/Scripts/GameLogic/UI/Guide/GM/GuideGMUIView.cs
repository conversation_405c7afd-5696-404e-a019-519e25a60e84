using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;
using Phoenix.Guide;

namespace Phoenix.GameLogic.UI
{
    public class GuideGMUIView : UIView
    {
        public Action actionOnReturnBtnClicked;

        public Action actionOnCompleteCurBtnClicked;
        public Action actionOnCompleteAllBtnClicked;

        public Action<GuideGroupInfo> actionOnLockBtnClicked;
        public Action<GuideGroupInfo> actionOnUnlockBtnClicked;
        public Action<GuideGroupInfo> actionOnCompleteBtnClicked;
        public Action<GuideGroupInfo> actionOnStartBtnClicked;
        public Action<GuideGroupInfo> actionOnResetBtnClicked;
        public Action<GuideGroupInfo, GuideStepInfo> actionOnActionBtnClicked;

        private List<GuideGMItemUIView> m_itemCtrlList = new List<GuideGMItemUIView>();
        private GuideGroupInfo m_selectedGroupInfo;
        private GuideStepInfo m_selectedStepInfo;
        private EState m_state = EState.Group;
        private float m_groupScrollNormalized = 1f;
        private float m_stepScrollNormalized = 1f;

        private enum EState
        {
            Group,
            Step,
        }

        protected override void OnInit()
        {
            base.OnInit();
            m_returnBtn.onClick.AddListener(OnReturnBtnClicked);

            m_completeCurBtn.onClick.AddListener(OnCompleteCurBtnClicked);
            m_completeAllBtn.onClick.AddListener(OnCompleteAllBtnClicked);

            m_lockBtn.onClick.AddListener(OnLockBtnClicked);
            m_unlockBtn.onClick.AddListener(OnUnlockBtnClicked);
            m_completeBtn.onClick.AddListener(OnCompleteBtnClicked);
            m_startBtn.onClick.AddListener(OnStartBtnClicked);
            m_actionBtn.onClick.AddListener(OnActionBtnClicked);
            m_detailBtn.onClick.AddListener(OnDetailBtnClicked);
            m_resetBtn.onClick.AddListener(OnResetBtnClicked);

            m_groupItemPool.Init(OnGroupItemInit);
            m_scroll.onValueChanged.AddListener(OnScrollValueChanged);
        }

        private void OnScrollValueChanged(Vector2 v)
        {
            switch (m_state)
            {
                case EState.Group:
                    m_groupScrollNormalized = m_scroll.verticalNormalizedPosition;
                    break;
                case EState.Step:
                    m_stepScrollNormalized = m_scroll.verticalNormalizedPosition;
                    break;
            }
        }

        private void OnGroupItemInit(GameObjectPool.PoolObjHolder holder)
        {
            GuideGMItemUIView ctrl = holder.AddComponent<GuideGMItemUIView>();
            ctrl.Init(this);
            ctrl.actionOnClicked += OnItemClicked;
        }

        private void OnItemClicked(GuideGMItemUIView ctrl)
        {
            if (m_state == EState.Group)
            {
                GuideGMItemUIView selectedCtrl = GetSelectedCtrlByGroupInfo();
                if (selectedCtrl != null)
                {
                    selectedCtrl.SetHighlight(false);
                }
                m_selectedGroupInfo = ctrl.groupInfo;
            }
            else if (m_state == EState.Step)
            {
                GuideGMItemUIView selectedCtrl = GetSelectedCtrlByStepInfo();
                if (selectedCtrl != null)
                {
                    selectedCtrl.SetHighlight(false);
                }
                m_selectedStepInfo = ctrl.stepInfo;
            }
            ctrl.SetHighlight(true);
            UpdateBtnGroup();
        }

        private void OnReturnBtnClicked()
        {
            if (m_state == EState.Group)
            {
                if (actionOnReturnBtnClicked != null)
                {
                    actionOnReturnBtnClicked();
                }
            }
            else if (m_state == EState.Step)
            {
                UpdateGroupState();
            }
        }

        private void OnCompleteCurBtnClicked()
        {
            if (actionOnCompleteCurBtnClicked != null)
            {
                actionOnCompleteCurBtnClicked();
            }
        }

        private void OnCompleteAllBtnClicked()
        {
            if (actionOnCompleteAllBtnClicked != null)
            {
                actionOnCompleteAllBtnClicked();
            }
        }

        private void OnLockBtnClicked()
        {
            if (actionOnLockBtnClicked != null)
            {
                actionOnLockBtnClicked(m_selectedGroupInfo);
            }
        }

        private void OnUnlockBtnClicked()
        {
            if (actionOnUnlockBtnClicked != null)
            {
                actionOnUnlockBtnClicked(m_selectedGroupInfo);
            }
        }

        private void OnCompleteBtnClicked()
        {
            if (actionOnCompleteBtnClicked != null)
            {
                actionOnCompleteBtnClicked(m_selectedGroupInfo);
            }
        }

        private void OnStartBtnClicked()
        {
            if (actionOnStartBtnClicked != null)
            {
                actionOnStartBtnClicked(m_selectedGroupInfo);
            }
        }

        private void OnActionBtnClicked()
        {
            if (actionOnActionBtnClicked != null)
            {
                actionOnActionBtnClicked(m_selectedGroupInfo, m_selectedStepInfo);
            }
        }

        private void OnResetBtnClicked()
        {
            if (actionOnResetBtnClicked != null)
            {
                actionOnResetBtnClicked(m_selectedGroupInfo);
            }
        }

        private void OnDetailBtnClicked()
        {
            m_state = EState.Step;
            UpdateStepState();
        }

        public void UpdateGroupState()
        {
            m_scroll.enabled = false;
            m_state = EState.Group;
            m_itemCtrlList.Clear();
            m_groupItemPool.ReleaseAll();
            foreach (var info in GuideManager.instance.guideGroupList)
            {
                if (!info.needShowInGmUi)
                {
                    continue;
                }
                GuideGMItemUIView ctrl = m_groupItemPool.FetchComponent<GuideGMItemUIView>();
                ctrl.SetHighlight(false);
                ctrl.SetInfo(info);
                m_itemCtrlList.Add(ctrl);
                if (m_selectedGroupInfo == null)
                {
                    m_selectedGroupInfo = info;
                }
            }
            GuideGMItemUIView selectedCtrl = GetSelectedCtrlByGroupInfo();
            if (selectedCtrl != null)
            {
                selectedCtrl.SetHighlight(true);
            }
            UpdateBtnGroup();
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_groupItemPool.transform as RectTransform);
            m_scroll.verticalNormalizedPosition = m_groupScrollNormalized;
            m_scroll.enabled = true;
        }

        public void UpdateStepState()
        {
            m_scroll.enabled = false;
            m_itemCtrlList.Clear();
            m_groupItemPool.ReleaseAll();
            for (int i = 0; i < m_selectedGroupInfo.stepInfoList.Count; ++i)
            {
                GuideStepInfo info = m_selectedGroupInfo.stepInfoList[i];
                GuideGMItemUIView ctrl = m_groupItemPool.FetchComponent<GuideGMItemUIView>();
                ctrl.SetHighlight(false);
                ctrl.SetInfo(GuideUtility.GetStepUniqueId(m_selectedGroupInfo.id, i), info);
                m_itemCtrlList.Add(ctrl);
            }
            if (m_selectedStepInfo == null)
            {
                m_selectedStepInfo = m_selectedGroupInfo.firstStep;
            }
            GuideGMItemUIView selectedCtrl = GetSelectedCtrlByStepInfo();
            if (selectedCtrl == null)
            {
                m_selectedStepInfo = m_selectedGroupInfo.firstStep;
            }
            selectedCtrl = GetSelectedCtrlByStepInfo();
            if (selectedCtrl != null)
            {
                selectedCtrl.SetHighlight(true);
            }
            UpdateBtnGroup();
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_groupItemPool.transform as RectTransform);
            m_scroll.verticalNormalizedPosition = m_stepScrollNormalized;
            m_scroll.enabled = true;
        }

        private GuideGMItemUIView GetSelectedCtrlByGroupInfo()
        {
            foreach (var ctrl in m_itemCtrlList)
            {
                if (ctrl.groupInfo == m_selectedGroupInfo)
                {
                    return ctrl;
                }
            }
            return null;
        }

        private GuideGMItemUIView GetSelectedCtrlByStepInfo()
        {
            foreach (var ctrl in m_itemCtrlList)
            {
                if (ctrl.stepInfo == m_selectedStepInfo)
                {
                    return ctrl;
                }
            }
            return null;
        }

        private void UpdateBtnGroup()
        {
            if (m_state == EState.Group)
            {
                GuideGMItemUIView ctrl = GetSelectedCtrlByGroupInfo();
                m_actionBtn.SetActiveSafely(false);
                m_detailBtn.SetActiveSafely(true);
                switch (ctrl.state)
                {
                    case GuideGMItemUIView.EState.Normal:
                        m_startBtn.SetActiveSafely(true);
                        m_completeBtn.SetActiveSafely(true);
                        m_lockBtn.SetActiveSafely(true);
                        m_unlockBtn.SetActiveSafely(false);
                        m_resetBtn.SetActiveSafely(false);
                        break;
                    case GuideGMItemUIView.EState.Lock:
                        m_startBtn.SetActiveSafely(true);
                        m_completeBtn.SetActiveSafely(false);
                        m_lockBtn.SetActiveSafely(false);
                        m_unlockBtn.SetActiveSafely(true);
                        m_resetBtn.SetActiveSafely(false);
                        break;
                    case GuideGMItemUIView.EState.Complete:
                        m_startBtn.SetActiveSafely(true);
                        m_completeBtn.SetActiveSafely(false);
                        m_lockBtn.SetActiveSafely(false);
                        m_unlockBtn.SetActiveSafely(false);
                        m_resetBtn.SetActiveSafely(true);
                        break;
                    case GuideGMItemUIView.EState.Running:
                        m_startBtn.SetActiveSafely(false);
                        m_completeBtn.SetActiveSafely(true);
                        m_lockBtn.SetActiveSafely(true);
                        m_unlockBtn.SetActiveSafely(false);
                        m_resetBtn.SetActiveSafely(false);
                        break;
                }
            }
            else if (m_state == EState.Step)
            {
                m_actionBtn.SetActiveSafely(true);
                m_detailBtn.SetActiveSafely(false);
                m_startBtn.SetActiveSafely(false);
                m_lockBtn.SetActiveSafely(false);
                m_unlockBtn.SetActiveSafely(false);
                m_completeBtn.SetActiveSafely(false);
                m_resetBtn.SetActiveSafely(false);
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Text m_titleText;
        private GameObjectPool m_groupItemPool;
        private Button m_returnBtn;
        private Button m_completeCurBtn;
        private Button m_completeAllBtn;
        private Button m_lockAllBtn;
        private Button m_lockBtn;
        private Button m_unlockBtn;
        private Button m_completeBtn;
        private Button m_startBtn;
        private Button m_actionBtn;
        private Button m_detailBtn;
        private Button m_resetBtn;
        private Button m_unsafeBtn;
        private ScrollRect m_scroll;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_titleText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TitleText"), typeof(Text)) as Text;
            m_groupItemPool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("GroupItemPool"), typeof(GameObjectPool)) as GameObjectPool;
            m_returnBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ReturnBtn"), typeof(Button)) as Button;
            m_completeCurBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CompleteCurBtn"), typeof(Button)) as Button;
            m_completeAllBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CompleteAllBtn"), typeof(Button)) as Button;
            m_lockAllBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LockAllBtn"), typeof(Button)) as Button;
            m_lockBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LockBtn"), typeof(Button)) as Button;
            m_unlockBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("UnlockBtn"), typeof(Button)) as Button;
            m_completeBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CompleteBtn"), typeof(Button)) as Button;
            m_startBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("StartBtn"), typeof(Button)) as Button;
            m_actionBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ActionBtn"), typeof(Button)) as Button;
            m_detailBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("DetailBtn"), typeof(Button)) as Button;
            m_resetBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ResetBtn"), typeof(Button)) as Button;
            m_unsafeBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("UnsafeBtn"), typeof(Button)) as Button;
            m_scroll = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Scroll"), typeof(ScrollRect)) as ScrollRect;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
