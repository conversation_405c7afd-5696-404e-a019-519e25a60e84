using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class DialogueWordUIView : UIView
    {
        private Int32 m_currentDialogId;
        private Boolean m_autoNext;
        private ActiveText m_realActiveText;
        public bool IsPlayComplete { get; private set; }


        protected override void OnInit()
        {
            base.OnInit();
            m_realActiveText = m_wordText as ActiveText;
        }

        public void UpdateDialogueWorldUIView(DialogConfigData dialogueConfig)
        {
            String word = dialogueConfig.Text;
            if (word.Contains("\\n"))
            {
                word = word.Replace("\\n", "\n");
            }
            IsPlayComplete = false;
            m_currentDialogId = dialogueConfig.Id;
            m_autoNext = dialogueConfig.PlayMode == DialogPlayModeId.Auto;
            if (String.IsNullOrEmpty(dialogueConfig.Name))
            {
                m_nameGroup.SetActiveSafely(false);
            }
            else
            {
                m_nameText.text = dialogueConfig.Name;
                m_nameGroup.SetActiveSafely(true);
            }
            UpdateDialogWordFrame(dialogueConfig);
            m_nextGameObject.SetActiveSafely(false);
            m_realActiveText.PlaySpeed(word, 15);
            m_realActiveText.m_actionOnFinish = OnTextPlayFinishCallBack;
        }


        public void SetDialogWordCompleted()
        {
            m_realActiveText.Finish(true);
        }

        private void OnTextPlayFinishCallBack()
        {
            IsPlayComplete = true;
            m_nextGameObject.SetActiveSafely(true);
            Broadcast<Int32>((Int32)DialogueUIEventId.WordPlayEndCallBack, m_currentDialogId);
        }

        private void UpdateDialogWordFrame(DialogConfigData dialogueConfig)
        {
            String uiGroupName;
            switch(dialogueConfig.FramePattern)
            {
                case DialogFramePatternId.Normal:
                    uiGroupName = "Normal";
                    break;
                case DialogFramePatternId.Shock:
                    uiGroupName = "Shock";
                    break;
                case DialogFramePatternId.Think:
                    uiGroupName = "Think";
                    break;
                default:
                    uiGroupName = "Normal";
                    break;
            }
            m_wordContentFrame.SetToUIGroup(uiGroupName);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIGroupComponent m_wordContentFrame; // Normal Shock Think 
        private GameObject m_nameGroup;
        private Text m_nameText;
        private Text m_wordText;
        private Image m_nextGameObject;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_wordContentFrame = UIBindUtility.GetBindComponent(group.GetItemInfoByName("WordContentFrame"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_nameGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("NameGroup"));
            m_nameText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("NameText"), typeof(Text)) as Text;
            m_wordText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("WordText"), typeof(Text)) as Text;
            m_nextGameObject = UIBindUtility.GetBindComponent(group.GetItemInfoByName("NextGameObject"), typeof(Image)) as Image;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
