using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class DialogueUIView : UIView
    {
        private GameObject m_dialogEffectObject;
        private Int32 m_dialogEffectId;

        protected override void OnInit()
        {
            base.OnInit();
            AddButtonListener(m_autoStartBtn, (Int32)DialogueUIEventId.AutoPlayStartBtnClick);
            AddButtonListener(m_autoStopBtn, (Int32)DialogueUIEventId.AutoPlayStopBtnClick);
            AddButtonListener(m_skipBtn, (Int32)DialogueUIEventId.SkipBtnClick);
            AddButtonListener(m_nextButton, (Int32)DialogueUIEventId.ContinueBtnClick);
        }

        protected override void OnUnInit()
        {
            TryDestroyDialogEffect();
        }

        public void UpdateDialogueInfo(DialogConfigData dialogueConfig)
        {
            m_backgroundGroup.UpdateDialogBackgroundView(dialogueConfig);
            m_characterGroup.UpdateDialogueCharacterUIView(dialogueConfig);
            m_wordContent.UpdateDialogueWorldUIView(dialogueConfig);

            UpdateDialogEffect(dialogueConfig.EffectId);
        }


        public void UpdateAutoPlayBtnState(Boolean isAutoPlay)
        {
            m_autoGroup.SetToUIGroup(isAutoPlay ? "On" : "Off");
        }

        public void ShowOrHideFunctionButton(Boolean isShow)
        {
            m_btnGroup.SetActiveSafely(isShow);
        }

        private void UpdateDialogEffect(Int32 dialogEffectId)
        {
            if (m_dialogEffectId != dialogEffectId)
            {
                TryDestroyDialogEffect();
                m_dialogEffectId = dialogEffectId;
                DialogEffectConfigData dialogEffectConfigData = ConfigDataManager.instance.GetDialogEffect(dialogEffectId);
                if (dialogEffectConfigData != null)
                {
                    GameObject effectObject = m_owner.GetResource<GameObject>(dialogEffectConfigData.AssetPath);
                    if (effectObject != null)
                    {
                        m_dialogEffectObject = GameObject.Instantiate(effectObject, m_effectRoot.transform);
                    }
                }
            }

        }

        private void TryDestroyDialogEffect()
        {
            if (m_dialogEffectObject != null)
            {
                m_dialogEffectId = 0;
                GameObject.Destroy(m_dialogEffectObject);
                m_dialogEffectObject = null;
            }
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private DialogueBackgroundUIView m_backgroundGroup;
        private DialogueCharacterGroupUIView m_characterGroup;
        private Button m_nextButton;
        private DialogueWordUIView m_wordContent;
        private GameObject m_effectRoot;
        private GameObject m_btnGroup;
        private Button m_skipBtn;
        private UIGroupComponent m_autoGroup; // On Off 
        private Button m_autoStartBtn;
        private Button m_autoStopBtn;
        
        public DialogueBackgroundUIView backgroundGroup
        {
            get { return m_backgroundGroup; }
        }
        public DialogueCharacterGroupUIView characterGroup
        {
            get { return m_characterGroup; }
        }
        public DialogueWordUIView wordContent
        {
            get { return m_wordContent; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("BackgroundGroup"), typeof(DialogueBackgroundUIView));
            AddBindComponent(group.GetItemInfoByName("CharacterGroup"), typeof(DialogueCharacterGroupUIView));
            AddBindComponent(group.GetItemInfoByName("WordContent"), typeof(DialogueWordUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_backgroundGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("BackgroundGroup"), typeof(DialogueBackgroundUIView)) as DialogueBackgroundUIView;
            m_characterGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CharacterGroup"), typeof(DialogueCharacterGroupUIView)) as DialogueCharacterGroupUIView;
            m_nextButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("NextButton"), typeof(Button)) as Button;
            m_wordContent = UIBindUtility.GetBindComponent(group.GetItemInfoByName("WordContent"), typeof(DialogueWordUIView)) as DialogueWordUIView;
            m_effectRoot = UIBindUtility.GetBindObject(group.GetItemInfoByName("EffectRoot"));
            m_btnGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("BtnGroup"));
            m_skipBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkipBtn"), typeof(Button)) as Button;
            m_autoGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AutoGroup"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_autoStartBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AutoStartBtn"), typeof(Button)) as Button;
            m_autoStopBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AutoStopBtn"), typeof(Button)) as Button;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
