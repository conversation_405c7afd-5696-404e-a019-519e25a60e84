using System;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class DialogSelectionItemUIView : UIView
    {
        private Int32 m_selectionId;
        public void UpdateSelectionItemInfo(Int32 selectionId)
        {
            m_selectionId = selectionId;

            SelectionConfigData selectionConfig = ConfigDataManager.instance.GetSelection(m_selectionId);
            if (selectionConfig != null)
            {
                m_itemDesc.text = selectionConfig.Title;
            }
            else
            {
                m_itemDesc.text = "NULL";
            }
        }


        protected override void OnInit()
        {
            base.OnInit();
            m_itemButton.onClick.AddListener(OnClick);
        }


        private void OnClick()
        {
            Broadcast((Int32)DialogueUIEventId.SelectionClick, m_selectionId);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_itemButton;
        private Text m_itemDesc;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_itemButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ItemButton"), typeof(Button)) as Button;
            m_itemDesc = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ItemDesc"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
