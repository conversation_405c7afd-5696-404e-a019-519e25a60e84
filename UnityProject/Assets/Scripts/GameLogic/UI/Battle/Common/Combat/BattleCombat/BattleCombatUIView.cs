using System;
using UnityEngine.UI;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleCombatUIView : UIView
    {
        public void Hide()
        {
            gameObject.SetActiveSafely(false);
        }

        public void UpdateView(Int32 sourceEntityUid, Int32 skillUid, Int32 targetEntityUid)
        {
            gameObject.SetActiveSafely(true);
            EntityView sourceEntity = EntityViewManager.instance.GetEntityView(sourceEntityUid);
            EntityView targetEntity = EntityViewManager.instance.GetEntityView(targetEntityUid);

            if (sourceEntity == null || targetEntity == null)
            {
                return;
            }


            Int32 fightBackSkillUid = 0;
            Skill fightBackSkill = targetEntity.GetSkillBySkillSlotId(SkillSlotId.CounterAttack);
            if (fightBackSkill != null)
            {
                fightBackSkillUid = fightBackSkill.uid;
            }

            Boolean sourceIsHostTeam = BattleShortCut.IsHostOrFriendTeam(sourceEntity.GetTeamUid());
            if (sourceIsHostTeam)
            {
                m_entity1.UpdateView(sourceEntity, skillUid, targetEntity);
                m_entity2.UpdateView(targetEntity, fightBackSkillUid, sourceEntity);
                UpdateEntityElement(sourceEntity, skillUid, targetEntity, 0);
            }
            else
            {
                m_entity1.UpdateView(targetEntity, fightBackSkillUid, sourceEntity);
                m_entity2.UpdateView(sourceEntity, skillUid, targetEntity);
                UpdateEntityElement(targetEntity, 0, sourceEntity, skillUid);
            }
        }


        public void ShowCombatSkillAnnounce(Int32 entityUid, Int32 skillUid, Action onEnd)
        {
            if (entity1.EntityUid == entityUid)
            {
                m_entity1.ShowSkillAnnounce(skillUid, onEnd);
            }
            else if (entity2.EntityUid == entityUid)
            {
                entity2.ShowSkillAnnounce(skillUid, onEnd);
            }
            else
            {
                onEnd?.Invoke();
            }
        }

        private void UpdateEntityElement(EntityView entity1, Int32 entity1SkillUid, EntityView entity2, Int32 entity2SkillUid)
        {
            EntityElementConfigData entity1Element = ConfigDataManager.instance.GetEntityElement(BattleUIUtility.GetEntityElementId(entity1, entity1SkillUid));
            if (entity1Element != null)
            {
                CommonUIUtility.UpdateIconSprite(m_owner, m_element_Left1, entity1Element.IconAtlasName, entity1Element.IconSubName);
                CommonUIUtility.UpdateIconSprite(m_owner, m_element_Left2, entity1Element.IconAtlasName, entity1Element.IconSubName);
                CommonUIUtility.UpdateIconSprite(m_owner, m_element_Left3, entity1Element.IconAtlasName, entity1Element.IconSubName);
            }
            EntityElementConfigData entity2Element = ConfigDataManager.instance.GetEntityElement(BattleUIUtility.GetEntityElementId(entity2, entity2SkillUid));
            if (entity2Element != null)
            {
                CommonUIUtility.UpdateIconSprite(m_owner, m_element_Right1, entity2Element.IconAtlasName, entity2Element.IconSubName);
                CommonUIUtility.UpdateIconSprite(m_owner, m_element_Right2, entity2Element.IconAtlasName, entity2Element.IconSubName);
                CommonUIUtility.UpdateIconSprite(m_owner, m_element_Right3, entity2Element.IconAtlasName, entity2Element.IconSubName);
            }
            UpdateRelationship(entity1, entity1SkillUid, entity2, entity2SkillUid);
        }

        private void UpdateRelationship(EntityView attackEntity, Int32 skillUid1, EntityView defendEntity, Int32 skillUid2)
        {
            m_center.SetActiveSafely(false);
            m_center.SetActiveSafely(true);
            Int32 relationship = BattleUIUtility.GetRelationship(attackEntity, skillUid1, defendEntity, skillUid2);
            if (relationship != 0)
            {
                m_center.SetToUIGroup(relationship > 0 ? "Advantage" : "Inferiority");
            }
            else
            {
                m_center.SetToUIGroup("Parity");
            }
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private BattleCombatEntityUIView m_entity1;
        private BattleCombatEntityUIView m_entity2;
        private UIGroupComponent m_center; // Advantage Inferiority Parity 
        private Image m_element_Left1;
        private Image m_element_Left2;
        private Image m_element_Left3;
        private Image m_element_Right1;
        private Image m_element_Right2;
        private Image m_element_Right3;
        
        public BattleCombatEntityUIView entity1
        {
            get { return m_entity1; }
        }
        public BattleCombatEntityUIView entity2
        {
            get { return m_entity2; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("Entity1"), typeof(BattleCombatEntityUIView));
            AddBindComponent(group.GetItemInfoByName("Entity2"), typeof(BattleCombatEntityUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_entity1 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Entity1"), typeof(BattleCombatEntityUIView)) as BattleCombatEntityUIView;
            m_entity2 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Entity2"), typeof(BattleCombatEntityUIView)) as BattleCombatEntityUIView;
            m_center = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Center"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_element_Left1 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element_Left1"), typeof(Image)) as Image;
            m_element_Left2 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element_Left2"), typeof(Image)) as Image;
            m_element_Left3 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element_Left3"), typeof(Image)) as Image;
            m_element_Right1 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element_Right1"), typeof(Image)) as Image;
            m_element_Right2 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element_Right2"), typeof(Image)) as Image;
            m_element_Right3 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element_Right3"), typeof(Image)) as Image;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
