//using System;
//using System.Collections.Generic;
//using UnityEngine;
//using UnityEngine.UI;
//using Phoenix.Battle;
//using Phoenix.ConfigData;
//using Phoenix.Core;
//using Phoenix.GameLogic.Battle;

//namespace Phoenix.GameLogic.UI
//{
//    public class BattleCompareEntityUIView : UIView
//    {
//        HpController m_hpController;

//        protected override void OnInit()
//        {
//            m_hpController = m_hpGroup.GetComponent<HpController>();
//        }

//        public void UpdateInfo(EntityView attackEntity, EntityView defendEntity, Int32 skillUid, Dictionary<Int32, Int32>  skillPreviewResult)
//        {
//            UpdateEntityInfoInternal(attackEntity, skillPreviewResult);
//            Skill skill = attackEntity.GetSkillBySkillUid(skillUid);
//            UpdateEntitySkillInfoInternal(skill);
//            UpdateSignRelationship(BattleUIUtility.GetRelationship(attackEntity, skillUid, defendEntity));
//            UpdateEntityHelperInternal();
//        }

//        private void UpdateEntityInfoInternal(EntityView entityView, Dictionary<Int32, Int32> skillPreviewResult)
//        {
//            ActorView actorView = (ActorView)entityView;
//            if (actorView != null)
//            {
//                UpdateHeadIconInfo(actorView.configData);
//            }
//            m_levelText.text = BattleShortCut.Level.ToString();
//            Int32 hpValue = entityView.GetHp();
//            Int32 maxHpValue = entityView.GetHpMax();
//            Int32 hpChangeValue = 0;
//            if (skillPreviewResult.TryGetValue(entityView.uid, out Int32 hpPreviewValue))
//            {
//                hpChangeValue = (Int32)hpPreviewValue;
//            }
//            Boolean hostTeam = BattleShortCut.IsHostOrFriendTeam(entityView.GetTeamUid());
//            m_teamBackground.SetToUIGroup(hostTeam ? "Blue" : "Red");
//            m_hpController.UpdateTeamState(hostTeam);
//            m_hpController.UpdateHp(hpValue, maxHpValue, hpChangeValue);
//            m_killedEffect.SetActiveSafely(hpValue + hpChangeValue <= 0);
//            m_attackNum.text = String.Format("{0}", (Int32)entityView.GetAttributeValue(AttributeId.PhysicalAttack));
//            m_defNum.text = String.Format("{0}", (Int32)entityView.GetAttributeValue(AttributeId.PhysicalDefence));
//            m_criticalNum.text = String.Format("{0}%", (Int32)entityView.GetAttributeValue(AttributeId.CriticalRate) / 100);
//        }

//        private void UpdateHeadIconInfo(ActorConfigData actorData)
//        {
//            if (actorData != null)
//            {
//                m_name.text = actorData.Name;
//                EntitySkinConfigData skinConfigData = ConfigDataManager.instance.GetEntitySkin(actorData.SkinId);
//                if (skinConfigData != null)
//                {
//                    CommonUIUtility.UpdateIconSprite(m_owner, m_entityIcon, skinConfigData.RealIconPathB);
//                }

//                EntityElementConfigData entityElement = ConfigDataManager.instance.GetEntityElement(actorData.ElementType);
//                if (entityElement != null)
//                {
//                    CommonUIUtility.UpdateIconSprite(m_owner, m_element, entityElement.IconAtlasName, entityElement.IconSubName);
//                }

//                EntityCareerConfigData entityCareer = ConfigDataManager.instance.GetEntityCareer(actorData.CareerType);
//                if (entityCareer != null)
//                {
//                    CommonUIUtility.UpdateIconSprite(m_owner, m_career, entityCareer.IconAtlasName, entityCareer.IconSubName);
//                }
//            }
//        }

//        private void UpdateEntitySkillInfoInternal(Skill skill)
//        {
//            if (skill == null || !skill.CanDisplay())
//            {
//                m_skillGroup.SetActiveSafely(false);
//                return;
//            }

//            SkillConfigData skillConfig = ConfigDataManager.instance.GetSkill(skill.rid);
//            if (skillConfig != null)
//            {
//                m_skillGroup.SetActiveSafely(true);
//                m_skillName.text = skillConfig.Name;
//                CommonUIUtility.UpdateIconSprite(m_owner, m_skillIcon, skillConfig.IconAtlasPath, skillConfig.IconName);
//            }
//            else
//            {
//                m_skillGroup.SetActiveSafely(false);
//            }
//        }

//        private void UpdateSignRelationship(Int32 relationship)
//        {
//            if (relationship == -1)
//            {
//                m_relationship.SetToUIGroup("Down");
//            }
//            else if (relationship == 1)
//            {
//                m_relationship.SetToUIGroup("Up");
//            }
//            else
//            {
//                m_relationship.SetActiveSafely(false);
//            }
//        }

//        private void UpdateEntityHelperInternal()
//        {
//            m_entityHelper.SetActiveSafely(false);
//        }

//        #region AutoGen
//        ////////------ AutoGen Begin ------////////
//        private Image m_entityIcon;
//        private Image m_element;
//        private Image m_career;
//        private Text m_levelText;
//        private Text m_name;
//        private UIGroupComponent m_teamBackground; // Blue Yellow Red 
//        private GameObject m_hpGroup;
//        private UIGroupComponent m_relationship; // Up Down 
//        private Text m_attackNum;
//        private Text m_defNum;
//        private Text m_criticalNum;
//        private GameObject m_skillGroup;
//        private Image m_skillIcon;
//        private Text m_skillName;
//        private GameObject m_entityHelper;
//        private Text m_entityHelperName;
//        private Image m_entityHelperIcon;
//        private GameObject m_entityHelperHp;
//        private GameObject m_killedEffect;
        
        
//        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
//        {
//            base.OnBindSubGroupsByBindGroup(group);
//        }
        
//        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
//        {
//            base.OnBindViewsByBindGroup(group);
//            m_entityIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityIcon"), typeof(Image)) as Image;
//            m_element = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element"), typeof(Image)) as Image;
//            m_career = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Career"), typeof(Image)) as Image;
//            m_levelText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LevelText"), typeof(Text)) as Text;
//            m_name = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Name"), typeof(Text)) as Text;
//            m_teamBackground = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TeamBackground"), typeof(UIGroupComponent)) as UIGroupComponent;
//            m_hpGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("HpGroup"));
//            m_relationship = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Relationship"), typeof(UIGroupComponent)) as UIGroupComponent;
//            m_attackNum = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AttackNum"), typeof(Text)) as Text;
//            m_defNum = UIBindUtility.GetBindComponent(group.GetItemInfoByName("DefNum"), typeof(Text)) as Text;
//            m_criticalNum = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CriticalNum"), typeof(Text)) as Text;
//            m_skillGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("SkillGroup"));
//            m_skillIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillIcon"), typeof(Image)) as Image;
//            m_skillName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillName"), typeof(Text)) as Text;
//            m_entityHelper = UIBindUtility.GetBindObject(group.GetItemInfoByName("EntityHelper"));
//            m_entityHelperName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityHelperName"), typeof(Text)) as Text;
//            m_entityHelperIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityHelperIcon"), typeof(Image)) as Image;
//            m_entityHelperHp = UIBindUtility.GetBindObject(group.GetItemInfoByName("EntityHelperHp"));
//            m_killedEffect = UIBindUtility.GetBindObject(group.GetItemInfoByName("KilledEffect"));
//        }
//        ////////------ AutoGen End ------////////
//        #endregion AutoGen
//    }
//}
