
using Phoenix.Core;
using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.GameLogic.Battle;
using UnityEngine;
namespace Phoenix.GameLogic.UI
{
    public partial class TerrainUIView : UIView
    {
        private GridPosition m_selectGrid;
        private List<Int32> m_selectTerrainBuffs = new List<Int32>();

        protected override void OnInit()
        {
            //EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(gameObject, BattleUIDefine.BattleGearPanelMask));
            m_terrainEffectContent.Init(OnterrainDetailItemPoolInit);
            HideInternal();
        }

        protected override void OnUnInit()
        {
            //EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(gameObject));
        }

        public void TerrainSelected(GridPosition grid)
        {
            m_selectGrid = grid;
            m_selectTerrainBuffs.Clear();
            if (BattleShortCut.logicBattle.CheckPosValid(grid))
            {
                CollectTerrainEntitys();
                UpdateInfo();
                gameObject.SetActiveSafely(true);
            }
            else
            {
                HideInternal();
            }
        }

        public void Hide()
        {
            HideInternal();
        }

        protected void CollectTerrainEntitys()
        {
            List<EntityView> entityViews = EntityViewManager.instance.GetEntityViewList(m_selectGrid);
            foreach (EntityView entity in entityViews)
            {
                if (entity.entityType == ConfigData.EntityType.TerrainBuff)
                {
                    m_selectTerrainBuffs.Add(entity.uid);
                }
            }
        }


        private void UpdateInfo()
        {
            m_terrainItem.SetActiveSafely(true);
            m_terrainItem.UpdataGridInfo(m_selectGrid, false, OnGridItemClick);
            m_terrainItem.UpdateTerrainPatternState(m_selectTerrainBuffs.Count == 0);
            UpdateDetailInfo(false);
        }


        private void UpdateDetailInfo(Boolean selected)
        {
            if (selected)
            {
                m_terrainDetailItem.UpdataTerrainGridDetailInfo(m_selectGrid);
                if (m_selectTerrainBuffs.Count > 0)
                {
                    m_terrainEffectContent.SetActiveSafely(true);
                    m_terrainEffectContent.ReleaseAll();
                    foreach (Int32 terrainBuffUid in m_selectTerrainBuffs)
                    {
                        TerrainDetailItemUIView terrainItemView = m_terrainEffectContent.FetchComponent<TerrainDetailItemUIView>();
                        terrainItemView.UpdateTerrainBuffDetailInfo(terrainBuffUid);
                    }
                }
                else
                {
                    m_terrainEffectContent.SetActiveSafely(false);
                }
                m_terrainDetailRoot.SetActiveSafely(true);
            }
            else
            {
                m_terrainDetailRoot.SetActiveSafely(false);
            }
        }

        private void HideInternal()
        {
            m_terrainItem.SetActiveSafely(false);
            m_terrainDetailRoot.SetActiveSafely(false);
            m_terrainEffectContent.ReleaseAll();
        }


        private void OnGridItemClick(Boolean selected)
        {
            UpdateDetailInfo(selected);
        }

        private void OnterrainDetailItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            TerrainDetailItemUIView itemView = holder.AddComponent<TerrainDetailItemUIView>();
            itemView.Init(this);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private TerrainItemUIView m_terrainItem;
        private GameObject m_terrainDetailRoot;
        private TerrainDetailItemUIView m_terrainDetailItem;
        private GameObjectPool m_terrainEffectContent;
        
        public TerrainItemUIView terrainItem
        {
            get { return m_terrainItem; }
        }
        public TerrainDetailItemUIView terrainDetailItem
        {
            get { return m_terrainDetailItem; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("TerrainItem"), typeof(TerrainItemUIView));
            AddBindComponent(group.GetItemInfoByName("TerrainDetailItem"), typeof(TerrainDetailItemUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_terrainItem = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TerrainItem"), typeof(TerrainItemUIView)) as TerrainItemUIView;
            m_terrainDetailRoot = UIBindUtility.GetBindObject(group.GetItemInfoByName("TerrainDetailRoot"));
            m_terrainDetailItem = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TerrainDetailItem"), typeof(TerrainDetailItemUIView)) as TerrainDetailItemUIView;
            m_terrainEffectContent = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TerrainEffectContent"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
