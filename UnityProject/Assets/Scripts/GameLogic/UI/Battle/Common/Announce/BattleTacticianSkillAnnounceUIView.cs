using System;
using Phoenix.Core;
using Phoenix.Core.UIEffect;
using UnityEngine;
using UnityEngine.UI;

namespace Phoenix.GameLogic.UI
{
    public class BattleTacticianSkillAnnounceUIView : UIView
    {

        protected override void OnInit()
        {
            base.OnInit();
            EventManager.instance.RegisterListener<Int32>(EventID.BattlePerformance_TacticianSkillAnnounce, OnTacticianSkillAnnounce);
            m_root.SetActiveSafely(false);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            EventManager.instance.UnRegisterListener<Int32>(EventID.BattlePerformance_TacticianSkillAnnounce, OnTacticianSkillAnnounce);
        }

        private void OnTacticianSkillAnnounce(Int32 tacticianUid)
        {
            Boolean result = true;
            m_root.SetActiveSafely(true);
            if (result)
            {
                m_tacticianAnnounce.SetUIGroup("In");
                m_tacticianSkillUIView.SetActiveSafely(false);
                float duration = 1;
                TimerManager.instance.Start(duration, () =>
                {
                    m_root.SetActiveSafely(false);
                    //onEnd?.Invoke();
                });
            }
            else
            {
                m_root.SetActiveSafely(false);
                //onEnd?.Invoke();
            }
        }




        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObject m_root;
        private UIEffectGroup m_tacticianAnnounce; // In Out 
        private Image m_tacticianIcon;
        private BattleTacticianSkillInfoUIView m_tacticianSkillUIView;

        public BattleTacticianSkillInfoUIView tacticianSkillUIView
        {
            get { return m_tacticianSkillUIView; }
        }

        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("TacticianSkillUIView"), typeof(BattleTacticianSkillInfoUIView));
        }

        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_root = UIBindUtility.GetBindObject(group.GetItemInfoByName("Root"));
            m_tacticianAnnounce = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TacticianAnnounce"), typeof(UIEffectGroup)) as UIEffectGroup;
            m_tacticianIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TacticianIcon"), typeof(Image)) as Image;
            m_tacticianSkillUIView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TacticianSkillUIView"), typeof(BattleTacticianSkillInfoUIView)) as BattleTacticianSkillInfoUIView;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
