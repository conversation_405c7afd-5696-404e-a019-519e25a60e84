
using System;
using UnityEngine.UI;
using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleSkillAnnounceUIView : UIView
    {
        protected override void OnInit()
        {
            base.OnInit();
            EventManager.instance.RegisterListener<Int32, Int32, Action>(EventID.BattlePerformance_SceneSkillAnnounce, OnSkillAnnounce);
            gameObject.SetActiveSafely(false);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            EventManager.instance.UnRegisterListener<Int32, Int32, Action>(EventID.BattlePerformance_SceneSkillAnnounce, OnSkillAnnounce);
        }

        private void OnSkillAnnounce(Int32 entityUid, Int32 skillRid, Action onEnd)
        {
            Boolean result = false;
            gameObject.SetActiveSafely(true);
            SkillConfigData skillConfigData = ConfigDataManager.instance.GetSkill(skillRid);
            if (skillConfigData.announceType == SkillAnnounceType.Normal)
            {
                result = true;
                m_skillAnnounceRoot.SetActiveSafely(true);
                m_specialSkillAnnounceRoot.SetActiveSafely(false);
                SkillAnnounce1(entityUid, skillRid);
            }
            else if (skillConfigData.announceType == SkillAnnounceType.Special)
            {
                result = true;
                m_skillAnnounceRoot.SetActiveSafely(false);
                m_specialSkillAnnounceRoot.SetActiveSafely(true);
            }

            if (result)
            {
                // AudioManager.instance.PlayOnce("Assets/Res/Audio/StoryVO/ding.mp3");
                float duration = BattleParamSetting.instance.m_battleUISetting.m_skillAnnounceUITime;
                TimerManager.instance.Start(duration, () =>
                {
                    gameObject.SetActiveSafely(false);
                    onEnd?.Invoke();
                });
            }
            else
            {
                gameObject.SetActiveSafely(false);
                onEnd?.Invoke();
            }
        }

        private void SkillAnnounce1(Int32 entityUid, Int32 skillRid)
        {
            SkillConfigData skillConfigData = ConfigDataManager.instance.GetSkill(skillRid);
            String entityIconPath = String.Empty;
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                EntitySkinConfigData skinConfig = ConfigDataManager.instance.GetEntitySkin(entityView.skinId);
                if (skinConfig != null)
                {
                    entityIconPath = skinConfig.RealIconPathB;
                }
            }
            if (BattleShortCut.IsHostOrFriendTeamWithEntityUid(entityUid))
            {
                m_skillAnnounceRoot.SetToUIGroup("Blue");
            }
            else
            {
                m_skillAnnounceRoot.SetToUIGroup("Red");
            }
            m_text_SkillName.text = skillConfigData.name;
            if (!String.IsNullOrEmpty(entityIconPath))
            {
                CommonUIUtility.UpdateIconSprite(m_owner, m_image_EntityIcon_01, entityIconPath);
                CommonUIUtility.UpdateIconSprite(m_owner, m_image_EntityIcon_02, entityIconPath);
            }
        }




        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIGroupComponent m_skillAnnounceRoot; // Blue Red Yellow 
        private Image m_image_EntityIcon_01;
        private Image m_image_EntityIcon_02;
        private Text m_text_SkillName;
        private GameObject m_specialSkillAnnounceRoot;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_skillAnnounceRoot = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillAnnounceRoot"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_image_EntityIcon_01 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Image_EntityIcon_01"), typeof(Image)) as Image;
            m_image_EntityIcon_02 = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Image_EntityIcon_02"), typeof(Image)) as Image;
            m_text_SkillName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Text_SkillName"), typeof(Text)) as Text;
            m_specialSkillAnnounceRoot = UIBindUtility.GetBindObject(group.GetItemInfoByName("SpecialSkillAnnounceRoot"));
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
