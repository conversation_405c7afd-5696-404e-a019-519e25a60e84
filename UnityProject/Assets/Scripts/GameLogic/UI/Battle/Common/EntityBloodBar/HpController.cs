
using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class HpController : MonoBehaviour
    {
        public UIGroupComponent m_teamStateGroup;
        public Slider m_hpSlider;
        public Slider m_hpPreviewSlider;
        public Text m_hpPreviewText;
        public Text m_hpText;
        public Text m_hpMaxText;

        public void UpdateTeamState(Boolean isHostTeam)
        {
            if(m_teamStateGroup != null)
            {
                m_teamStateGroup.SetToUIGroup(isHostTeam ? "Blue" : "Red");
            }
        }

        /// <summary>
        /// changeValue ��������ֵ
        /// </summary>
        /// <param name="hpValue"></param>
        /// <param name="maxHpValue"></param>
        /// <param name="changeValue"></param>
        public void UpdateHp(Int32 hpValue, Int32 maxHpValue, Int32 changeValue)
        {
            if (changeValue < 0)
            {
                PreviewDamage(hpValue, maxHpValue, -changeValue);
            }
            else
            {
                PreviewHeal(hpValue, maxHpValue, changeValue);
            }
            PlayPreviewAnimation();
        }

        private void PreviewDamage(Int32 hpValue, Int32 maxHpValue, Int32 changeValue)
        {
            Int32 previewHpValue = Math.Max(hpValue - changeValue, 0);

            if (m_hpText != null)
            {
                m_hpText.text = hpValue.ToString();
            }

            if (m_hpMaxText != null)
            {
                m_hpMaxText.text = maxHpValue.ToString();
            }

            if (m_hpSlider != null)
            {
                m_hpSlider.value = previewHpValue * 1f / maxHpValue;
            }

            if (m_hpPreviewSlider != null)
            {
                m_hpPreviewSlider.value = hpValue * 1f / maxHpValue;
            }

            if (m_hpPreviewText != null)
            {
                if (previewHpValue != hpValue)
                {
                    m_hpPreviewText.text = previewHpValue.ToString();
                    m_hpPreviewText.SetActiveSafely(true);
                }
                else
                {
                    m_hpPreviewText.SetActiveSafely(false);
                }
            }
        }

        private void PreviewHeal(Int32 hpValue, Int32 maxHpValue, Int32 changeValue)
        {
            Int32 previewHpValue = Math.Max(hpValue + changeValue, 0);

            if (m_hpText != null)
            {
                m_hpText.text = hpValue.ToString();
            }

            if (m_hpMaxText != null)
            {
                m_hpMaxText.text = maxHpValue.ToString();
            }

            if (m_hpSlider != null)
            {
                m_hpSlider.value = hpValue * 1f / maxHpValue;
            }

            if (m_hpPreviewSlider != null)
            {
                m_hpPreviewSlider.value = previewHpValue * 1f / maxHpValue;
            }

            if (m_hpPreviewText != null)
            {
                if (previewHpValue != hpValue)
                {
                    m_hpPreviewText.text = previewHpValue.ToString();
                    m_hpPreviewText.SetActiveSafely(true);
                }
                else
                {
                    m_hpPreviewText.SetActiveSafely(false);
                }
            }
        }

        private AnimationController previewSliderAnimationController;
        private void PlayPreviewAnimation()
        {
            if (m_hpPreviewSlider == null)
            {
                return;
            }
            //Debug.LogError($"PlayPreviewAnimation  {Time.realtimeSinceStartup}");

            if (previewSliderAnimationController == null)
            {
                previewSliderAnimationController = m_hpPreviewSlider.GetComponentInChildren<AnimationController>();
            }
            if (previewSliderAnimationController != null)
            {
                previewSliderAnimationController.Stop();
                previewSliderAnimationController.Play(0);
            }
        }


    }
}
