using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleEntityDetailUIView : UIView
    {
        private Int32 m_entityUid = 0;
        private PassiveSkill m_talentPassiveSkill;


        private HpController m_hpController;

        protected override void OnInit()
        {
            base.OnInit();
            m_hpController = m_hpGroup.GetComponent<HpController>();
            m_buffGroup.Init(OnBuffItemPoolInit);
            m_skillContent.Init(OnSkillItemPoolInit);
            m_talentSkillBtn.onClick.AddListener(OnTalentSkillClicked);
            gameObject.SetActiveSafely(false);
        }
        public void HideActorDetailInfo()
        {
            m_talentPassiveSkill = null;
            gameObject.SetActiveSafely(false);
        }

        public void ShowActorDetailInfo(Int32 entityUid)
        {
            UpdateActorInfo(entityUid);
            gameObject.SetActiveSafely(true);
            m_entityDetailInfoGroup.Play(0);
        }


        private void UpdateActorInfo(Int32 entityUid)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView == null)
            {
                return;
            }
            m_entityUid = entityUid;
            bool isHostTeam = BattleShortCut.IsHostOrFriendTeam(entityView.GetTeamUid());
            m_teamBackground.SetToUIGroup(isHostTeam ? "Blue" : "Red");
            m_hpController.UpdateHp(entityView.GetHp(), entityView.GetHpMax(), 0);
            UpdateEntityInfo(entityView);
            UpdateSkillInfo(entityView);
            UpdateBuffInfo(entityView);
        }

        private void UpdateEntityInfo(EntityView entityView)
        {
            ActorConfigData actorConfigData = ConfigDataManager.instance.GetActor(entityView.rid);
            if (actorConfigData != null)
            {
                EntitySkinConfigData skinConfigData = ConfigDataManager.instance.GetEntitySkin(actorConfigData.SkinId);
                if (skinConfigData != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, m_iconImage, skinConfigData.RealIconPathB);
                }
                m_name.text = String.Format("{0}<color=green>({1})</color>", actorConfigData.Name, actorConfigData.Id);
                m_levelText.text = BattleShortCut.Level.ToString();
                EntityElementConfigData entityElement = ConfigDataManager.instance.GetEntityElement(actorConfigData.ElementType);
                if (entityElement != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, m_element, entityElement.IconAtlasName, entityElement.IconSubName);
                }
                EntityCareerConfigData entityCareer = ConfigDataManager.instance.GetEntityCareer(actorConfigData.CareerType);
                if (entityCareer != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, m_career, entityCareer.IconAtlasName, entityCareer.IconSubName);
                }
                EntityRaceConfigData entityRace = ConfigDataManager.instance.GetEntityRace(actorConfigData.EntityRace);
                if (entityRace != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, m_raceIcon, entityRace.IconAtlasName, entityRace.IconSubName);
                }

                Skill skill = entityView.GetNormalAttack();
                //TODO: RRR
                //if (skill != null && skill.skillInfo != null &&
                //    skill.skillInfo.logicInfo.targetSelectStepInfoList != null &&
                //    skill.skillInfo.logicInfo.targetSelectStepInfoList.Count > 0)
                //{
                //    Int32 extraSkillSelectRange = (Int32)entityView.GetAttributeValue(AttributeId.ExtraSkillSelectRange);
                //    TargetSelectRangeId targetSelectRange = skill.skillInfo.logicInfo.targetSelectStepInfoList.GetValueSafely(0, default).rangeId;
                //    TargetSelectRangeInfo targetSelectRangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(BattleShortCut.sampleBattle, targetSelectRange, extraSkillSelectRange);
                //    m_attackRangeText.text = String.Format("{0}", targetSelectRangeInfo.range);
                //}
                m_moveRangeText.text = String.Format("{0}格", actorConfigData.MovePoint);
                EntityMoveRuleConfigData moveRule = ConfigDataManager.instance.GetEntityMoveRule(actorConfigData.MoveRuleId);

                m_physicalAttackText.text = String.Format("{0}", (Int32)entityView.GetAttributeValue(AttributeId.PhysicalAttack));
                m_physicalDefenseText.text = String.Format("{0}", (Int32)entityView.GetAttributeValue(AttributeId.PhysicalDefence));
                m_magicAttackText.text = String.Format("{0}", (Int32)entityView.GetAttributeValue(AttributeId.MagicalAttack));
                m_magicDefenseText.text = String.Format("{0}", (Int32)entityView.GetAttributeValue(AttributeId.MagicalDefence));
            }
        }

        private void UpdateSkillInfo(EntityView entityView)
        {
            m_skillContent.ReleaseAll();


            foreach (SkillSlotId skillSlot in BattleUIDefine.DisplaySkillSlotIds)
            {
                Skill skill = entityView.GetSkillBySkillSlotId(skillSlot);
                PassiveSkill passiveSkill = entityView.GetPassiveSkillBySlotId(skillSlot);
                if (skill != null)
                {
                    BattleEntityDetailSkillItemUIView skillItem = m_skillContent.FetchComponent<BattleEntityDetailSkillItemUIView>();
                    skillItem.ShowSkillItem(entityView.uid, skill);
                }
                else if (passiveSkill != null)
                {
                    BattleEntityDetailSkillItemUIView skillItem = m_skillContent.FetchComponent<BattleEntityDetailSkillItemUIView>();
                    skillItem.ShowPassiveSkillItem(entityView.uid, passiveSkill);
                }
            }

            m_talentPassiveSkill = entityView.GetPassiveSkillBySlotId(SkillSlotId.Tallent);
            if (m_talentPassiveSkill != null)
            {
                PassiveSkillConfigData passiveSkillConfigData = ConfigDataManager.instance.GetPassiveSkill(m_talentPassiveSkill.rid);
                if (passiveSkillConfigData != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, m_talentSkillIcon, passiveSkillConfigData.IconAtlasPath, passiveSkillConfigData.IconName);
                    m_talentSkillLabel.text = passiveSkillConfigData.Tag;
                }
                Int32 cdValue = m_talentPassiveSkill.GetLeftCoolTime();
                if (cdValue > 0)
                {
                    m_talentSkillCDText.text = cdValue.ToString();
                    m_talentSkillCDText.SetActiveSafely(true);
                }
                else
                {
                    m_talentSkillCDText.SetActiveSafely(false);
                }
            }
            else
            {
                m_talentSkillIcon.sprite = null;
                m_talentSkillLabel.text = string.Empty;
                m_talentSkillCDText.SetActiveSafely(false);
            }
        }

        private void UpdateBuffInfo(EntityView entityView)
        {
            if (entityView.buffComponent != null)
            {
                m_buffGroup.ReleaseAll();
                List<Buff> buffList = entityView.GetBuffList();
                for (int i = 0; i < buffList.Count; ++i)
                {
                    Buff buff = buffList[i];
                    BuffConfigData buffConfig = ConfigDataManager.instance.GetBuff(buff.buffRid);
                    if (buffConfig != null && buffConfig.isShow)
                    {
                        var view = m_buffGroup.FetchComponent<BattleEntityDetailBuffItemUIView>();
                        view.UpdateBuffDetailItemView(entityView.uid, buff);
                    }
                }
            }

        }

        private void OnTalentSkillClicked()
        {
            if (m_talentPassiveSkill != null)
            {
                SkillDetailContext context = new SkillDetailContext();
                context.m_isUseSkillRid = true;
                context.m_entityUid = m_entityUid;
                context.m_isTalentSkill = true;
                context.m_cdValue = m_talentPassiveSkill.GetLeftCoolTime();
                context.m_skillRid = m_talentPassiveSkill.rid;
                context.m_positionAdaptive = true;
                context.m_screenPosition = Input.mousePosition;
                context.m_isPassiveSkill = true;
                context.m_needCloseBtn = true;
                Broadcast((Int32)BattleEntityInfoUIEventId.SkillItemClick, context);
            }
        }

        private void OnBuffItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<BattleEntityDetailBuffItemUIView>();
            view.Init(this);
        }

        private void OnSkillItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<BattleEntityDetailSkillItemUIView>();
            view.Init(this);
        }



        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private AnimationController m_entityDetailInfoGroup;
        private UIGroupComponent m_teamBackground; // Blue Yellow Red 
        private Image m_iconImage;
        private Text m_levelText;
        private Text m_name;
        private Image m_element;
        private Image m_career;
        private Image m_raceIcon;
        private GameObject m_hpGroup;
        private Text m_shieldNumText;
        private Text m_physicalAttackText;
        private Text m_physicalDefenseText;
        private Text m_magicAttackText;
        private Text m_magicDefenseText;
        private Text m_attackRangeText;
        private Text m_moveRangeText;
        private GameObjectPool m_buffGroup;
        private GameObjectPool m_skillContent;
        private Button m_talentSkillBtn;
        private Image m_talentSkillIcon;
        private Text m_talentSkillLabel;
        private Text m_talentSkillCDText;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_entityDetailInfoGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityDetailInfoGroup"), typeof(AnimationController)) as AnimationController;
            m_teamBackground = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TeamBackground"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_iconImage = UIBindUtility.GetBindComponent(group.GetItemInfoByName("IconImage"), typeof(Image)) as Image;
            m_levelText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LevelText"), typeof(Text)) as Text;
            m_name = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Name"), typeof(Text)) as Text;
            m_element = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Element"), typeof(Image)) as Image;
            m_career = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Career"), typeof(Image)) as Image;
            m_raceIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("RaceIcon"), typeof(Image)) as Image;
            m_hpGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("HpGroup"));
            m_shieldNumText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ShieldNumText"), typeof(Text)) as Text;
            m_physicalAttackText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("PhysicalAttackText"), typeof(Text)) as Text;
            m_physicalDefenseText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("PhysicalDefenseText"), typeof(Text)) as Text;
            m_magicAttackText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("MagicAttackText"), typeof(Text)) as Text;
            m_magicDefenseText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("MagicDefenseText"), typeof(Text)) as Text;
            m_attackRangeText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AttackRangeText"), typeof(Text)) as Text;
            m_moveRangeText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("MoveRangeText"), typeof(Text)) as Text;
            m_buffGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("BuffGroup"), typeof(GameObjectPool)) as GameObjectPool;
            m_skillContent = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillContent"), typeof(GameObjectPool)) as GameObjectPool;
            m_talentSkillBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TalentSkillBtn"), typeof(Button)) as Button;
            m_talentSkillIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TalentSkillIcon"), typeof(Image)) as Image;
            m_talentSkillLabel = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TalentSkillLabel"), typeof(Text)) as Text;
            m_talentSkillCDText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TalentSkillCDText"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
