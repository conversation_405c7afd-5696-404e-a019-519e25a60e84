using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleEntityBuffGroupUIView : UIView
    {
        protected override void OnInit()
        {
            HideBuffDetailGroupView();
            m_buffItemPool.Init(OnBuffItemPoolInit);
        }
        public void HideBuffDetailGroupView()
        {
            if (gameObject != null)
            {
                gameObject.SetActiveSafely(false);
            }
        }

        public void ShowBuffDetailGroupView(Int32 entityUid, Boolean showAll)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView.buffComponent != null)
            {
                m_buffItemPool.ReleaseAll();
                List<Buff> buffList = entityView.GetBuffList();
                for (int i = 0; i < buffList.Count; ++i)
                {
                    Buff buff = buffList[i];
                    BuffConfigData buffConfig = ConfigDataManager.instance.GetBuff(buff.buffRid);
                    if (buffConfig != null && (showAll || buffConfig.isShow))
                    {
                        var view = m_buffItemPool.FetchComponent<BattleEntityBuffDetailItemUIView>();
                        view.ShowBuffDetailView(buff);
                    }
                }
                gameObject.SetActiveSafely(true);
            }
            else
            {
                HideBuffDetailGroupView();
            }
        }


        private void OnBuffItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<BattleEntityBuffDetailItemUIView>();
            view.Init(this);
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObjectPool m_buffItemPool;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_buffItemPool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("BuffItemPool"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
