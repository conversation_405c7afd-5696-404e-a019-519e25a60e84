//using System;
//using UnityEngine;
//using UnityEngine.UI;
//using Phoenix.Battle;
//using Phoenix.ConfigData;
//using Phoenix.Core;
//using Phoenix.GameLogic.Battle;

//namespace Phoenix.GameLogic.UI
//{
//    public class BattleEntityDetailPassiveSkillItemUIView : UIView
//    {
//        private Int32 m_skillRid;
//        protected override void OnInit()
//        {
//            base.OnInit();
//            m_mainBtn.onClick.AddListener(OnClicked);
//            m_angerText.SetActiveSafely(false);
//            m_passiveIndicator.SetActiveSafely(true);
//            gameObject.SetActiveSafely(false);
//        }

//        public void SetEntityView(Int32 entityUid)
//        {
//            EntityView entityView = EntityViewManager.instance.GetView(entityUid);
//            if (entityView != null)
//            {
//                ActorConfigData actorConfig = ConfigDataManager.instance.GetActor(entityView.rid);
//                if (actorConfig != null)
//                {
//                    m_skillRid = actorConfig.PassiveSkillConfigIdList.GetValueSafely(0);
//                }
//            }
//            PassiveSkillConfigData passiveSkill = ConfigDataManager.instance.GetPassiveSkill(m_skillRid);
//            if (passiveSkill != null)
//            {
//                CommonUIUtility.UpdateIconSprite(m_owner, m_icon, passiveSkill.IconAtlasPath, passiveSkill.IconName);
//                m_cDText.text = string.Empty;
//                gameObject.SetActiveSafely(true);
//            }
//            else
//            {
//                gameObject.SetActiveSafely(false);
//            }
//        }

//        private void OnClicked()
//        {
//            SkillDetailContext context = new SkillDetailContext();
//            context.m_isUseSkillRid = true;
//            context.m_skillRid = m_skillRid;
//            context.m_isPassiveSkill = true;
//            context.m_positionAdaptive = true;
//            context.m_screenPosition = Input.mousePosition;
//            context.m_needCloseBtn = true;
//            BattleSkillDetailInfoUI.ShowSkillDetailUI(context);
//        }

//        #region AutoGen
//        ////////------ AutoGen Begin ------////////
//        private Button m_mainBtn;
//        private Image m_icon;
//        private GameObject m_passiveIndicator;
//        private Text m_cDText;
//        private Text m_angerText;
        
        
//        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
//        {
//            base.OnBindSubGroupsByBindGroup(group);
//        }
        
//        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
//        {
//            base.OnBindViewsByBindGroup(group);
//            m_mainBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("MainBtn"), typeof(Button)) as Button;
//            m_icon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Icon"), typeof(Image)) as Image;
//            m_passiveIndicator = UIBindUtility.GetBindObject(group.GetItemInfoByName("PassiveIndicator"));
//            m_cDText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CDText"), typeof(Text)) as Text;
//            m_angerText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AngerText"), typeof(Text)) as Text;
//        }
//        ////////------ AutoGen End ------////////
//        #endregion AutoGen
//    }
//}
