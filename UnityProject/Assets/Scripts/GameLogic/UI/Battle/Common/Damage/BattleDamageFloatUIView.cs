using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public partial class BattleDamageFloatUIView : UIView
    {
        protected override void OnInit()
        {
            base.OnInit();
            m_pool.Init(OnPoolItemInit);
            EventManager.instance.RegisterListener<int, string>(EventID.BattleTextFloat, OnTextFloat);
            EventManager.instance.RegisterListener<int, TextFloatPattern, string>(EventID.BattleTextFloatPro, OnTextFloatPro);
            EventManager.instance.RegisterListener<int, int>(EventID.BattleDamageFloat, OnDamageFloat);
            EventManager.instance.RegisterListener<int, int>(EventID.BattleHealFloat, OnHealFloat);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            EventManager.instance.UnRegisterListener<int, string>(EventID.BattleTextFloat, OnTextFloat);
            EventManager.instance.UnRegisterListener<int, TextFloatPattern, string>(EventID.BattleTextFloatPro, OnTextFloatPro);
            EventManager.instance.UnRegisterListener<int, int>(EventID.BattleDamageFloat, OnDamageFloat);
            EventManager.instance.UnRegisterListener<int, int>(EventID.BattleHealFloat, OnHealFloat);
        }

        private void OnPoolItemInit(GameObjectPool.PoolObjHolder holder)
        {
            BattleDamageFloatItemUIView itemView = holder.AddComponent<BattleDamageFloatItemUIView>();
            itemView.Init(this);
        }

        private void OnTextFloat(int entityUid, string text)
        {
            if (BattleShortCut.InBattleCombatStage)
            {
                return;
            }

            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                Vector2 localPos;
                RectTransform parentRectTrans = transform.parent as RectTransform;
                Vector2 screenPosition = Camera.main.WorldToScreenPoint(entityView.transform.position + Vector3.up * 2);
                if (RectTransformUtility.ScreenPointToLocalPointInRectangle(parentRectTrans, screenPosition, canvas.worldCamera, out localPos))
                {
                    BattleDamageFloatItemUIView itemView = m_pool.FetchComponent<BattleDamageFloatItemUIView>();
                    (itemView.transform as RectTransform).anchoredPosition = localPos;
                    itemView.PlayText(text, () => m_pool.Release(itemView.gameObject));
                }
            }
        }

        private void OnTextFloatPro(int entityUid, TextFloatPattern pattern, string text)
        {
            if (BattleShortCut.InBattleCombatStage)
            {
                return;
            }
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                Vector2 localPos;
                RectTransform parentRectTrans = transform.parent as RectTransform;
                Vector2 screenPosition = Camera.main.WorldToScreenPoint(entityView.transform.position + Vector3.up * 2);
                if (RectTransformUtility.ScreenPointToLocalPointInRectangle(parentRectTrans, screenPosition, canvas.worldCamera, out localPos))
                {
                    BattleDamageFloatItemUIView itemView = m_pool.FetchComponent<BattleDamageFloatItemUIView>();
                    (itemView.transform as RectTransform).anchoredPosition = localPos;
                    itemView.PlayTextFloatPattern(text, pattern, () => m_pool.Release(itemView.gameObject));
                }
            }
        }

        private void OnDamageFloat(int entityUid, int damage)
        {
            OnTextFloatInternal(entityUid, damage, true);
        }

        private void OnHealFloat(int entityUid, int heal)
        {
            OnTextFloatInternal(entityUid, heal, false);
        }

        private void OnTextFloatInternal(int entityUid, int value, bool isDamage)
        {
            if (BattleShortCut.InBattleCombatStage)
            {
                return;
            }
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                Vector2 localPos;
                RectTransform parentRectTrans = transform.parent as RectTransform;
                Vector2 screenPosition = Camera.main.WorldToScreenPoint(entityView.transform.position + Vector3.up * 2);
                if (RectTransformUtility.ScreenPointToLocalPointInRectangle(parentRectTrans, screenPosition, canvas.worldCamera, out localPos))
                {
                    BattleDamageFloatItemUIView itemView = m_pool.FetchComponent<BattleDamageFloatItemUIView>();
                    (itemView.transform as RectTransform).anchoredPosition = localPos;
                    if (isDamage)
                    {
                        itemView.PlayDamage(value, () => m_pool.Release(itemView.gameObject));
                    }
                    else
                    {
                        itemView.PlayHeal(value, () => m_pool.Release(itemView.gameObject));
                    }
                }
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObjectPool m_pool;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_pool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Pool"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
