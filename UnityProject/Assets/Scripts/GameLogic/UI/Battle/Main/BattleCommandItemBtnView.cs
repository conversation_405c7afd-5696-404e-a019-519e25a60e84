using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.World;

namespace Phoenix.GameLogic.UI
{
    public class BattleCommandItemBtnView : UIView
    {
        private TeamDecisionMarkId m_teamDecisionMarkId = TeamDecisionMarkId.None;
        private Transform m_objCommandBtnParent = null;
        BattleTeamDecisionMark m_commandSelect = null;
        bool m_isContainCommand = false;
        bool m_isCommandMore = true;
        protected override void OnInit()
        {
            m_btnNormal.onClick.AddListener(OnClickCommandSelect);
            m_btnSelected.onClick.AddListener(OnClickCommandSelect);
            m_btnCancel.onClick.AddListener(OnClickCommandSelect);
            m_objCommandBtnParent = this.transform.parent.parent;
        }

        public void UpdateView(TeamDecisionMarkConfigData teamDecisionMarkConfigData, bool isCommandMore = true)
        {
            m_isCommandMore = isCommandMore;
            m_teamDecisionMarkId = (TeamDecisionMarkId)teamDecisionMarkConfigData.Id;
            string strIconNormal = $"{teamDecisionMarkConfigData.IconName}_nml";
            string strIconSelected = $"{teamDecisionMarkConfigData.IconName}_sel";
            string strIconPressed = $"{teamDecisionMarkConfigData.IconName}_dwn";
            CommonUIUtility.UpdateBtnSprite(m_owner, m_btnNormal, teamDecisionMarkConfigData.IconAtlas, strIconNormal, strIconSelected, strIconPressed);
            CommonUIUtility.UpdateBtnSprite(m_owner, m_btnSelected, teamDecisionMarkConfigData.IconAtlas, strIconSelected, strIconSelected, strIconPressed);
            m_textCommandName.text = teamDecisionMarkConfigData.Name;

            RefreshStatusView(m_teamDecisionMarkId);
        }

        public void RefreshStatusView(TeamDecisionMarkId teamDecisionMarkId)
        {
            if (m_teamDecisionMarkId != teamDecisionMarkId)
            {
                return;
            }

            m_isContainCommand = false;
            m_imgIcon.SetActiveSafely(false);
            if (m_teamDecisionMarkId != TeamDecisionMarkId.None)
            {
                BattleTeamDecisionMark commandSelect = AutoBattleOpMode.Instance.GetBattleTeamDecisionMark(m_teamDecisionMarkId);
                m_isContainCommand = commandSelect != null;
                if (m_isContainCommand)
                {
                    if (m_teamDecisionMarkId == TeamDecisionMarkId.Move)
                    {
                        Int32 terrainRid = BattleShortCut.sampleBattle.GetTerrainRid(commandSelect.pos);
                        BattleTerrainConfigData battgleTerrainConfig = ConfigDataManager.instance.GetBattleTerrain((BattleTerrainId)terrainRid);
                        if (battgleTerrainConfig != null)
                        {
                            m_imgIcon.SetActiveSafely(true);
                            CommonUIUtility.UpdateIconSprite(m_owner, m_imgIcon, battgleTerrainConfig.IconAtlas, battgleTerrainConfig.IconName);
                        }
                    }
                    else
                    {
                        EntityView entityView = EntityViewManager.instance.GetEntityView(commandSelect.entityUid);
                        if (entityView != null)
                        {
                            ActorConfigData actorConfigData = ConfigDataManager.instance.GetActor(entityView.rid);
                            if (actorConfigData != null)
                            {
                                m_imgIcon.SetActiveSafely(true);
                                CommonUIUtility.UpdateEntity2DSkinSprite(m_owner, m_imgIcon, actorConfigData.SkinId, Entity2DSkinIconType.IconC);
                            }
                        }
                    }

                    TeamDecisionMarkConfigData teamDecisionMarkConfigData = ConfigDataManager.instance.GetTeamDecisionMark(teamDecisionMarkId);
                    if (teamDecisionMarkConfigData != null)
                    {
                        m_txtCancel.text = ConstStringUtility.GetConstString(ConstStringId.AiselectTips6, teamDecisionMarkConfigData.Name);
                    }
                }
            }

            string strStatus = m_isContainCommand ? "Selected" : "Normal";
            m_uiGroupStatus.SetToUIGroup(strStatus);
        }

        public void OnEventCancelCommand(TeamDecisionMarkId teamDecisionMarkId)
        {
            if (m_teamDecisionMarkId != teamDecisionMarkId)
            {
                return;
            }

            //AutoBattleOpMode.Instance.RemoveTeamDecisionMarkCommandSelect(teamDecisionMarkId);
            //EventManager.instance.Broadcast(EventID.BattleCommand_RefreshCommandList);
            m_uiGroupStatus.SetToUIGroup("Normal");
            m_isContainCommand = false;
        }

        public void RefreshStatus(string strStatus)
        {
            if (m_isContainCommand)
            {
                AutoBattleOpMode.Instance.SendCancelTeamDecisionMark(m_teamDecisionMarkId);
                //m_isContaionCommandMove = false;
            }
            //m_uiGroupStatus.SetToUIGroup(strStatus);
        }

        public void ResetStatus()
        {
            if (m_uiGroupStatus.ActiveUIGroup.m_GroupName == "PreActive")
            {
                m_uiGroupStatus.SetToUIGroup("Normal");
            }
        }

        private void OnClickCommandSelect()
        {
            if (m_isContainCommand)
            {
                AutoBattleOpMode.Instance.SendCancelTeamDecisionMark(m_teamDecisionMarkId);
                //EventManager.instance.Broadcast(EventID.BattleCommand_RefreshCommandList);
                m_uiGroupStatus.SetToUIGroup("Normal");
                //m_isContaionCommandMove = false;
            }
            else
            {
                m_uiGroupStatus.SetToUIGroup("PreActive");
                AutoBattleOpMode.Instance.CurTeamDecisionMarkId = m_teamDecisionMarkId;
                AutoBattleOpMode.Instance.IsWatingForSkillPerformance = true;
                AutoBattleOpMode.Instance.BattleControl_BlockPerfomanceAndWait(AutoBattleOpMode.Instance.ChangeToSelectCommand);

                //if (m_isCommandMore && m_objCommandBtnParent != null)
                //{
                //    m_objCommandBtnParent.SetActiveSafely(false);
                //}
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_btnNormal;
        private Button m_btnSelected;
        private Button m_btnCancel;
        private Text m_txtCancel;
        private UIGroupComponent m_uiGroupStatus; // Normal Selected PreActive 
        private Text m_textCommandName;
        private Image m_imgIcon;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_btnNormal = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnNormal"), typeof(Button)) as Button;
            m_btnSelected = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnSelected"), typeof(Button)) as Button;
            m_btnCancel = UIBindUtility.GetBindComponent(group.GetItemInfoByName("btnCancel"), typeof(Button)) as Button;
            m_txtCancel = UIBindUtility.GetBindComponent(group.GetItemInfoByName("txtCancel"), typeof(Text)) as Text;
            m_uiGroupStatus = UIBindUtility.GetBindComponent(group.GetItemInfoByName("uiGroupStatus"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_textCommandName = UIBindUtility.GetBindComponent(group.GetItemInfoByName("textCommandName"), typeof(Text)) as Text;
            m_imgIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("imgIcon"), typeof(Image)) as Image;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
