using System;

using UnityEngine;
using UnityEngine.UI;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleActiveEntityUIView : UIView
    {
        private HpController m_hpController;

        protected override void OnInit()
        {
            m_hpController = m_hpGroup.GetComponent<HpController>();
            Hide();
        }

        public void Show()
        {
            gameObject.SetActiveSafely(true);
        }
        public void Hide()
        {
            gameObject.SetActiveSafely(false);
        }


        public void UpdateView(Int32 entityUid)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {

                Boolean isHostTeam = BattleShortCut.IsHostOrFriendTeam(entityView.GetTeamUid());
                m_teamBackground.SetToUIGroup(isHostTeam ? "Blue" : "Red");
                Int32 hpValue = entityView.GetHp();
                Int32 maxHpValue = entityView.GetHpMax();
                m_hpController.UpdateHp(hpValue, maxHpValue, 0);
                m_levelText.text = BattleShortCut.Level.ToString();
                UpdateIconInfo(entityView);
            }
        }

        private void UpdateIconInfo(EntityView sourceEntity)
        {
            ActorConfigData actorData = ConfigDataManager.instance.GetActor(sourceEntity.rid);
            if (actorData != null)
            {
                m_name.text = actorData.Name;
                EntitySkinConfigData skinConfigData = ConfigDataManager.instance.GetEntitySkin(actorData.SkinId);
                if (skinConfigData != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, m_entityIcon, skinConfigData.RealIconPathB);
                }

                //EntityElementConfigData entityElement = ConfigDataManager.instance.GetEntityElement(actorData.ElementType);
                //if (entityElement != null)
                //{
                //    CommonUIUtility.UpdateIconSprite(m_owner, m_element, entityElement.IconAtlasName, entityElement.IconSubName);
                //}

                //EntityCareerConfigData entityCareer = ConfigDataManager.instance.GetEntityCareer(actorData.CareerType);
                //if (entityCareer != null)
                //{
                //    CommonUIUtility.UpdateIconSprite(m_owner, m_career, entityCareer.IconAtlasName, entityCareer.IconSubName);
                //}
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIGroupComponent m_teamBackground; // Blue Yellow Red 
        private Image m_entityIcon;
        private Text m_name;
        private Text m_levelText;
        private GameObject m_hpGroup;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_teamBackground = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TeamBackground"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_entityIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityIcon"), typeof(Image)) as Image;
            m_name = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Name"), typeof(Text)) as Text;
            m_levelText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LevelText"), typeof(Text)) as Text;
            m_hpGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("HpGroup"));
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
