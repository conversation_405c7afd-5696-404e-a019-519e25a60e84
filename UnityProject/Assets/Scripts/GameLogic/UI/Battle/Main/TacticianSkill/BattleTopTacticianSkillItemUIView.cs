using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Core.UIEffect;
using UnityEngine;
using UnityEngine.UI;

namespace Phoenix.GameLogic.UI
{

    public enum TacticianSkillStage
    {
        Empty,//还没释放
        Prepare,//释放后，准备阶段
        Active,//释放后，生效阶段
        End,//结束
    }


    public class BattleTopTacticianSkillItemUIView : UIView
    {
        private TacticianSkill m_tacticianSkill;
        protected override void OnInit()
        {
            m_cardButton.onClick.RemoveListener(OnCardButtonClick);
            m_cardButton.onClick.AddListener(OnCardButtonClick);
        }

        protected override void OnUnInit()
        {
            m_tacticianSkill = null;
        }


        public void UpdateInfo(TacticianSkill tacticianSkill)
        {
            m_tacticianSkill = tacticianSkill;
            if (m_tacticianSkill != null)
            {
                CommonUIUtility.UpdateIconSprite(m_owner, m_tacticianSkillIcon, m_tacticianSkill.skillInfo.iconName);

                TacticianSkillStage skillStage = GetTacticianSkillStage(m_tacticianSkill);
                switch (skillStage)
                {
                    case TacticianSkillStage.Empty:
                        m_cardStageState.SetUIGroup("Empty");
                        break;
                    case TacticianSkillStage.Prepare:
                        m_cardStageState.SetUIGroup("Prepare");

                        if (tacticianSkill.skillInfo.skillType == TacticianSkillType.Yin)
                        {
                            m_cardBGState.SetUIGroup("Yin");
                            Boolean isHostCampTacticianSkill = StaticTactician.IsHostCampTacticianSkill(m_tacticianSkill);
                            m_cardBackground.SetActiveSafely(!isHostCampTacticianSkill);
                            m_coolDownRoot.SetActiveSafely(false);
                        }
                        else if (tacticianSkill.skillInfo.skillType == TacticianSkillType.Yang)
                        {
                            m_cardBGState.SetUIGroup("Yang");
                            m_cardBackground.SetActiveSafely(false);
                            m_coolDownRoot.SetActiveSafely(true);
                            m_coolDown.text = $"{tacticianSkill.coolTimeLeft}";
                        }
                        break;
                    case TacticianSkillStage.Active:
                        m_cardStageState.SetUIGroup("Active");
                        m_cardBackground.SetActiveSafely(false);
                        m_coolDownRoot.SetActiveSafely(false);
                        break;
                    case TacticianSkillStage.End:
                        m_cardStageState.SetUIGroup("End");
                        break;
                }
            }
        }


        private TacticianSkillStage GetTacticianSkillStage(TacticianSkill tacticianSkill)
        {
            return TacticianSkillStage.Prepare;
        }


        private void OnCardButtonClick()
        {
            TipUI.ShowTip($"OnCardButtonClick {m_tacticianSkill.rid}");
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIEffectGroup m_cardStageState; // Empty Prepare Active End 
        private UIEffectGroup m_cardBGState; // Yin Yang 
        private Image m_tacticianSkillIcon;
        private GameObject m_cardBackground;
        private GameObject m_coolDownRoot;
        private Text m_coolDown;
        private Button m_cardButton;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_cardStageState = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CardStageState"), typeof(UIEffectGroup)) as UIEffectGroup;
            m_cardBGState = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CardBGState"), typeof(UIEffectGroup)) as UIEffectGroup;
            m_tacticianSkillIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TacticianSkillIcon"), typeof(Image)) as Image;
            m_cardBackground = UIBindUtility.GetBindObject(group.GetItemInfoByName("CardBackground"));
            m_coolDownRoot = UIBindUtility.GetBindObject(group.GetItemInfoByName("CoolDownRoot"));
            m_coolDown = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CoolDown"), typeof(Text)) as Text;
            m_cardButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CardButton"), typeof(Button)) as Button;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
