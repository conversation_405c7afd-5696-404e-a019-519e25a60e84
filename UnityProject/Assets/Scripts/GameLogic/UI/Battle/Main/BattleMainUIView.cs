using System;
using System.Text;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleMainUIView : UIView
    {
        protected override void OnInit()
        {
            base.OnInit();
            m_energyGroupView.SetActiveSafely(false);
            m_winConditionGroup.SetActiveSafely(false);

            EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(m_root, BattleUIDefine.BattleMainUIMask));
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(m_energyGroupView, BattleUIDefine.BattleEnergyGroupUIMask));
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(m_winConditionGroup, BattleUIDefine.BattleCommonBtnMask));
        }

        protected override void OnUnInit()
        {
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(m_root));
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(m_energyGroupView));
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(m_winConditionGroup));
        }

        public void UpdateTurnTextInfo(Int32 currentTurn, Int32 maxTurn)
        {
            m_roundNumText.text = String.Format("{0}/{1}", currentTurn + 1, maxTurn);
        }

        public void UpdateWinConditionUI()
        {
            BattleStageInfo stageInfo = BattleShortCut.sampleBattle.GetCurStageInfo();
            if (stageInfo != null)
            {
                StringBuilder sb = new StringBuilder();
                foreach (BattleCampRefereeInfo refereeInfo in stageInfo.stageRefreree.campRefereeList)
                {
                    if (!BattleShortCut.IsHostCamp(refereeInfo.campId))
                    {
                        continue;
                    }
                    foreach (var situation in refereeInfo.winSituationList)
                    {
                        String desc = BattleHelper.GetRefereeDesc(stageInfo, situation);
                        if (!String.IsNullOrEmpty(desc))
                        {
                            sb.Append(desc);
                        }
                        break;
                    }
                    if (sb.Length > 0)
                    {
                        break;
                    }
                }
                m_winConditionText.text = String.IsNullOrEmpty(sb.ToString()) ? "胜利条件不存在" : sb.ToString();
            }
        }

        public void UpdateBattleSpeedBtnState(Int32 speedMode)
        {
            m_commonBtnGroup.UpdateBattleSpeedBtnState(speedMode);
        }

        public void UpdateAutoBattleBtnState(Boolean isOn)
        {
            m_commonBtnGroup.UpdateAutoBattleBtnState(isOn);
        }

        public void UpdateDangerBtnState(Boolean isOn)
        {
            m_commonBtnGroup.UpdateDangerBtnState(isOn);
        }

        public void ShowOrHideRestrainUI(Boolean isShow)
        {
            m_restrainPopup.SetActiveSafely(isShow);
        }

        public void InOrOutSelectCommandState(bool isIn, TeamDecisionMarkId teamDecisionMarkId)
        {
            m_winConditionGroup.SetActiveSafely(!isIn);
            //m_menuBtn.transform.parent.SetActiveSafely(!isIn);
            m_commonBtnGroup.InOrOutSelectCommandState(isIn);
            m_energyGroupView.SetActiveSafely(!isIn);
            m_bindGroupCommand.InOrOutSelectCommandState(isIn, teamDecisionMarkId);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObject m_root;
        private BattleEntityGroupUIView m_entityGroupView;
        private BattleStrategyUIView m_strategyGroup;
        private BattleMainCommonButtonUIView m_commonBtnGroup;
        private BattleMainSimpleInfoUIView m_simpleInfoGroup;
        private BattleStrategySkillUIView m_strategySkillGroup;
        private BattleEnergyUIView m_energyGroupView;
        private BattleRetractUIView m_battleRetractView;
        private GameObject m_winConditionGroup;
        private Text m_winConditionText;
        private Text m_roundNumText;
        private BattleCommandGroupUIView m_bindGroupCommand;
        private GameObject m_restrainPopup;
        
        public BattleEntityGroupUIView entityGroupView
        {
            get { return m_entityGroupView; }
        }
        public BattleStrategyUIView strategyGroup
        {
            get { return m_strategyGroup; }
        }
        public BattleMainCommonButtonUIView commonBtnGroup
        {
            get { return m_commonBtnGroup; }
        }
        public BattleMainSimpleInfoUIView simpleInfoGroup
        {
            get { return m_simpleInfoGroup; }
        }
        public BattleStrategySkillUIView strategySkillGroup
        {
            get { return m_strategySkillGroup; }
        }
        public BattleEnergyUIView energyGroupView
        {
            get { return m_energyGroupView; }
        }
        public BattleRetractUIView battleRetractView
        {
            get { return m_battleRetractView; }
        }
        public BattleCommandGroupUIView bindGroupCommand
        {
            get { return m_bindGroupCommand; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("EntityGroupView"), typeof(BattleEntityGroupUIView));
            AddBindComponent(group.GetItemInfoByName("StrategyGroup"), typeof(BattleStrategyUIView));
            AddBindComponent(group.GetItemInfoByName("CommonBtnGroup"), typeof(BattleMainCommonButtonUIView));
            AddBindComponent(group.GetItemInfoByName("SimpleInfoGroup"), typeof(BattleMainSimpleInfoUIView));
            AddBindComponent(group.GetItemInfoByName("StrategySkillGroup"), typeof(BattleStrategySkillUIView));
            AddBindComponent(group.GetItemInfoByName("EnergyGroupView"), typeof(BattleEnergyUIView));
            AddBindComponent(group.GetItemInfoByName("BattleRetractView"), typeof(BattleRetractUIView));
            AddBindComponent(group.GetItemInfoByName("bindGroupCommand"), typeof(BattleCommandGroupUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_root = UIBindUtility.GetBindObject(group.GetItemInfoByName("Root"));
            m_entityGroupView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntityGroupView"), typeof(BattleEntityGroupUIView)) as BattleEntityGroupUIView;
            m_strategyGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("StrategyGroup"), typeof(BattleStrategyUIView)) as BattleStrategyUIView;
            m_commonBtnGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CommonBtnGroup"), typeof(BattleMainCommonButtonUIView)) as BattleMainCommonButtonUIView;
            m_simpleInfoGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SimpleInfoGroup"), typeof(BattleMainSimpleInfoUIView)) as BattleMainSimpleInfoUIView;
            m_strategySkillGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("StrategySkillGroup"), typeof(BattleStrategySkillUIView)) as BattleStrategySkillUIView;
            m_energyGroupView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyGroupView"), typeof(BattleEnergyUIView)) as BattleEnergyUIView;
            m_battleRetractView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("BattleRetractView"), typeof(BattleRetractUIView)) as BattleRetractUIView;
            m_winConditionGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("WinConditionGroup"));
            m_winConditionText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("WinConditionText"), typeof(Text)) as Text;
            m_roundNumText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("RoundNumText"), typeof(Text)) as Text;
            m_bindGroupCommand = UIBindUtility.GetBindComponent(group.GetItemInfoByName("bindGroupCommand"), typeof(BattleCommandGroupUIView)) as BattleCommandGroupUIView;
            m_restrainPopup = UIBindUtility.GetBindObject(group.GetItemInfoByName("RestrainPopup"));
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
