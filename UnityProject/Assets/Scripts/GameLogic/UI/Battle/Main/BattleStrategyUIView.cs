using Phoenix.Core;
using UnityEngine;

namespace Phoenix.GameLogic.UI
{
    public class BattleStrategyUIView : UIView
    {
        protected override void OnInit()
        {
            gameObject.SetActiveSafely(false);
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(gameObject, BattleUIDefine.BattleCommonBtnMask));
        }

        protected override void OnUnInit()
        {
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(gameObject));
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObject m_group_1;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_group_1 = UIBindUtility.GetBindObject(group.GetItemInfoByName("Group_1"));
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
