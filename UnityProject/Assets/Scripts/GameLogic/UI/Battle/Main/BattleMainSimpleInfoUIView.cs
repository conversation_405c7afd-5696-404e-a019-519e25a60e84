using System;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleMainSimpleInfoUIView : UIView
    {
        private Int32 m_activeEntityUid;

        protected override void OnInit()
        {
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(gameObject, BattleUIDefine.BattleSimpleInfoGroupMask));
            m_entitySimpleItemGroup.SetActiveSafely(false);
            m_entitySimpleItemBtn.onClick.AddListener(OnEntityDetailInfoBtn);
        }

        protected override void OnUnInit()
        {
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(gameObject));
        }

        public void ShowSelectEntityUIView(Int32 sntityUid)
        {
            m_activeEntityUid = sntityUid;
            UpdateIconInfo();
        }

        private void UpdateIconInfo()
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(m_activeEntityUid);
            if (entityView != null)
            {
                ActorConfigData actorData = ConfigDataManager.instance.GetActor(entityView.rid);
                if (actorData != null)
                {
                    EntitySkinConfigData skinConfigData = ConfigDataManager.instance.GetEntitySkin(actorData.SkinId);
                    if (skinConfigData != null)
                    {
                        CommonUIUtility.UpdateIconSprite(m_owner, m_headIcon, skinConfigData.RealIconPathB);
                    }
                }
                //m_entitySimpleItemGroup.SetToUIGroup("Normal");
                m_entitySimpleItemGroup.SetActiveSafely(true);
            }
            else
            {
                m_entitySimpleItemGroup.SetActiveSafely(false);
            }
        }

        private void OnEntityDetailInfoBtn()
        {
            BattleOpModeManager.instance.EventOnBattleUIClick(BattleUIEventID.ShowEntityDetailInfoBtn, m_activeEntityUid);
            //m_entitySimpleItemGroup.SetToUIGroup("Select");
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_entitySimpleItemBtn;
        private UIGroupComponent m_entitySimpleItemGroup; // Normal Select 
        private Image m_headIcon;
        private TerrainUIView m_terrainUIView;
        
        public TerrainUIView terrainUIView
        {
            get { return m_terrainUIView; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("TerrainUIView"), typeof(TerrainUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_entitySimpleItemBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntitySimpleItemBtn"), typeof(Button)) as Button;
            m_entitySimpleItemGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EntitySimpleItemGroup"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_headIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("HeadIcon"), typeof(Image)) as Image;
            m_terrainUIView = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TerrainUIView"), typeof(TerrainUIView)) as TerrainUIView;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
