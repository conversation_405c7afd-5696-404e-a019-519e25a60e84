using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.GameLogic.Battle;
using Phoenix.ConfigData;
using System;

namespace Phoenix.GameLogic.UI
{
    public partial class BattleSkillItemUIView : UIView
    {
        private Int32 m_entityUid;
        private Int32 m_skillUid;
        private Int32 m_skillCdValue;
        private Int32 m_passiveSkillRid;
        private Int32 m_skillResonanceUid;
        private Boolean m_isPassiveSkill;

        private BattleErrorCode m_battleErrorCode;

        protected override void OnInit()
        {
            base.OnInit();
            AddOnClickEventListener(m_skillBtn, OnClicked);
            AddOnLongPressBeginEventListener(m_skillBtn, OnLongPressBegin);
            AddOnLongPressEndEventListener(m_skillBtn, OnLongPressEnd);
            m_name.text = "";
            //AddOnClickEventListener(m_resonanceBtn, OnSkillResonanceBtnClick);
            //AddOnClickEventListener(m_resonanceCancelBtn, OnSkillResonanceCancelBtnClick);
        }

        public void HideSkillItem()
        {
            m_entityUid = 0;
            m_skillUid = 0;
            m_passiveSkillRid = 0;
            m_skillResonanceUid = 0;
            m_isPassiveSkill = false;
            m_name.text = "";
            gameObject.SetActiveSafely(false);
        }

        public void ShowEmpty()
        {
            m_entityUid = 0;
            m_skillUid = 0;
            m_passiveSkillRid = 0;
            m_skillResonanceUid = 0;
            m_isPassiveSkill = false;
            gameObject.SetActiveSafely(true);
            m_name.text = "";
            m_skillRootState.SetToUIGroup("Empty");
        }

        public void ShowSkillItem(Int32 entityUid, Skill skill, Int32 teamSharedEnergy, Int32 selectSkillUid)
        {
            m_isPassiveSkill = false;

            if (selectSkillUid == skill.uid)
            {
                m_skillRootState.SetToUIGroup("NormalSelect");
            }
            else
            {
                m_skillRootState.SetToUIGroup("Normal");
            }
            m_entityUid = entityUid;
            m_skillUid = skill.uid;
            m_battleErrorCode = skill.CanCastSkill();
            UpdateSkillBasicInfo(skill);
            UpdateSkillCoolDown(skill.coolTimeLeft);
            UpdateEnergyCast(skill, teamSharedEnergy);
            m_passiveTag.SetActiveSafely(false);
            gameObject.SetActiveSafely(true);

            if (m_battleErrorCode != BattleErrorCode.Ok)
            {
                m_activeState.SetToUIGroup("Disable");
            }
            else
            {
                m_activeState.SetToUIGroup("Normal");
            }
        }

        public void ShowPassiveSkillItem(Int32 entityUid, PassiveSkill passiveSkill)
        {
            m_isPassiveSkill = true;
            m_skillRootState.SetToUIGroup("Normal");
            m_entityUid = entityUid;
            m_passiveSkillRid = passiveSkill.rid;
            PassiveSkillConfigData skillConfigData = ConfigDataManager.instance.GetPassiveSkill(m_passiveSkillRid);
            if (skillConfigData != null)
            {
                CommonUIUtility.UpdateIconSprite(m_owner, m_skillIcon, skillConfigData.IconAtlasPath, skillConfigData.IconName);
#if !RELEASE
                m_name.text = skillConfigData.Name;
#endif
                m_label.text = skillConfigData.Tag;
                //m_resonanceFrame.SetActiveSafely(false);
                m_energyGroup.SetActiveSafely(false);
            }
            UpdateSkillCoolDown(passiveSkill.GetLeftCoolTime());
            m_activeState.SetToUIGroup("Normal");
            //m_skillIcon_Frame.SetToUIGroup("Null");
            m_passiveTag.SetActiveSafely(true);
            gameObject.SetActiveSafely(true);
        }


        private void UpdateSkillBasicInfo(Skill skill)
        {
#if !RELEASE
            m_name.text = skill.skillInfo.name;
#endif
            m_label.text = skill.skillInfo.tag;
            SkillConfigData skillConfigData = ConfigDataManager.instance.GetSkill(skill.rid);
            if (skillConfigData != null)
            {
                CommonUIUtility.UpdateIconSprite(m_owner, m_skillIcon, SpriteAtlasId.SkillIcon, skillConfigData.iconName);
            }
            else
            {
                m_skillIcon.sprite = null;
            }
        }

        private void UpdateSkillCoolDown(Int32 cdValue)
        {
            m_skillCdValue = cdValue;
            if (cdValue > 0)
            {
                m_coolDownText.text = cdValue.ToString();
                m_coolDownText.SetActiveSafely(true);
            }
            else
            {
                m_coolDownText.SetActiveSafely(false);
            }
        }

        private void UpdateEnergyCast(Skill skill, Int32 teamEnergy)
        {
            Int32 skillEnergyCost = skill.skillInfo.energyCost;
            if (skillEnergyCost >= 0)
            {
                Int32 energyRatio = BattleHelper.GetEnergyRatio();
                Int32 point = energyRatio != 0 ? skillEnergyCost / energyRatio : 0;
                Boolean active = skillEnergyCost <= teamEnergy;
                m_energyPointCount.text = point.ToString();
                //m_resonanceFrame.SetActiveSafely(active);
                m_energyGroup.SetActiveSafely(true);
                m_skillIcon_Frame.SetToUIGroup("Special");
            }
            else
            {
                //m_resonanceFrame.SetActiveSafely(false);
                m_energyGroup.SetActiveSafely(false);
                m_skillIcon_Frame.SetToUIGroup("Normal");
            }
        }

        private void OnClicked()
        {
            if (m_isPassiveSkill)
            {
                SkillDetailContext context = new SkillDetailContext();
                context.m_isUseSkillRid = true;
                context.m_entityUid = m_entityUid;
                context.m_skillRid = m_passiveSkillRid;
                context.m_cdValue = m_skillCdValue;
                context.m_positionAdaptive = true;
                context.m_screenPosition = Input.mousePosition;
                context.m_isPassiveSkill = true;
                context.m_needCloseBtn = true;
                context.m_showDescPatternGroup = false;
                BattleSkillDetailInfoUI.ShowSkillDetailUI(context);
                return;
            }

            switch (m_battleErrorCode)
            {
                case BattleErrorCode.SkillAngerNotEnough:
                    TipUI.ShowTip("SkillAngerNotEnough");
                    return;
                case BattleErrorCode.SkillBan:
                    TipUI.ShowTip("Skill Ban");
                    return;
                case BattleErrorCode.SkillNotCoolDown:
                    TipUI.ShowTip("SkillNotCoolDown");
                    return;
            }
            BattleOpModeManager.instance.EventOnBattleUIClick(BattleUIEventID.SelectSkill, m_skillUid);
        }

        private void OnLongPressBegin()
        {
            SkillDetailContext context = new SkillDetailContext();
            context.m_isUseSkillRid = m_isPassiveSkill;
            context.m_entityUid = m_entityUid;
            context.m_skillUid = m_skillUid;
            context.m_skillRid = m_passiveSkillRid;
            context.m_positionAdaptive = true;
            context.m_screenPosition = Input.mousePosition;
            context.m_isPassiveSkill = m_isPassiveSkill;
            context.m_needCloseBtn = false;
            context.m_showDescPatternGroup = false;
            BattleSkillDetailInfoUI.ShowSkillDetailUI(context);
        }

        private void OnLongPressEnd()
        {
            BattleSkillDetailInfoUI.CloseUI();
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIGroupComponent m_skillRootState; // Empty Normal NormalSelect 
        private UIGroupComponent m_activeState; // Normal Disable 
        private Button m_skillBtn;
        private Image m_skillIcon;
        private Text m_name;
        private Text m_label;
        private GameObject m_energyGroup;
        private Text m_energyPointCount;
        private GameObject m_passiveTag;
        private Text m_coolDownText;
        private UIGroupComponent m_skillIcon_Frame; // Normal Special 
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_skillRootState = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillRootState"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_activeState = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ActiveState"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_skillBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillBtn"), typeof(Button)) as Button;
            m_skillIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillIcon"), typeof(Image)) as Image;
            m_name = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Name"), typeof(Text)) as Text;
            m_label = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Label"), typeof(Text)) as Text;
            m_energyGroup = UIBindUtility.GetBindObject(group.GetItemInfoByName("EnergyGroup"));
            m_energyPointCount = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyPointCount"), typeof(Text)) as Text;
            m_passiveTag = UIBindUtility.GetBindObject(group.GetItemInfoByName("PassiveTag"));
            m_coolDownText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CoolDownText"), typeof(Text)) as Text;
            m_skillIcon_Frame = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillIcon_Frame"), typeof(UIGroupComponent)) as UIGroupComponent;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
