using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic.UI
{
    public class BattleEnergyUIView : UIView
    {
        private bool m_energyTipShowState = false;

        protected override void OnInit()
        {
            m_energyPointGroup.Init(OnEnergyItemPoolInit);
            m_energyBtn.onClick.AddListener(delegate
            {
                m_energyTipShowState = !m_energyTipShowState;
                m_energyTips.SetActiveSafely(m_energyTipShowState);
            });
            m_titleIconBtn.onClick.AddListener(delegate
            {
                m_energyTipShowState = !m_energyTipShowState;
                m_energyTips.SetActiveSafely(m_energyTipShowState);
            });
        }

        public void HideEnergyTip()
        {
            m_energyTipShowState = false;
            m_energyTips.SetActiveSafely(m_energyTipShowState);
        }
        public void HideEnergyUI()
        {
            m_energyPointGroup.SetActiveSafely(false);
        }

        public void UpdateTeamEnergy(Int32 sharedEnergy, Int32 maxEnergy, Int32 consumeEnergy)
        {
            Int32 energyRatio = BattleHelper.GetEnergyRatio();
            Int32 maxEnergyPoint = maxEnergy / energyRatio;
            Int32 currentEnergyPoint = sharedEnergy / energyRatio;
            //Int32 currentEnergy = sharedEnergy % energyRatio;
            //m_currentText.text = currentEnergy.ToString();
            //m_maxText.text = energyRatio.ToString();
            //m_energyFillImage.fillAmount = currentEnergy * 1f / energyRatio;
            Int32 leftEnergyPoint = (sharedEnergy - consumeEnergy) / energyRatio;
            m_currentEnergyCount.text = currentEnergyPoint.ToString();
            UpdateEnergyPointView(leftEnergyPoint, currentEnergyPoint, maxEnergyPoint);
            HideEnergyTip();
        }

        private void UpdateEnergyPointView(Int32 leftCount, Int32 currentCount, Int32 maxCount)
        {
            leftCount = Mathf.Clamp(leftCount, 0, maxCount);
            if (maxCount == 0)
            {
                m_energyPointGroup.SetActiveSafely(false);
                return;
            }
            m_energyPointGroup.SetActiveSafely(true);
            m_energyPointGroup.ReleaseAll();
            for (Int32 i = 0; i < maxCount; i++)
            {
                BattleEnergyItemUIView uiView = m_energyPointGroup.FetchComponent<BattleEnergyItemUIView>();
                if (leftCount < currentCount)
                {
                    if (i < leftCount)
                    {
                        uiView.UpdateEnergyPointView(EnergyItemState.Normal);
                    }
                    else if (i < currentCount)
                    {
                        uiView.UpdateEnergyPointView(EnergyItemState.Decrease);
                    }
                    else
                    {
                        uiView.UpdateEnergyPointView(EnergyItemState.Empty);
                    }
                }
                else
                {
                    if (i < currentCount)
                    {
                        uiView.UpdateEnergyPointView(EnergyItemState.Normal);
                    }
                    else if (i < leftCount)
                    {
                        uiView.UpdateEnergyPointView(EnergyItemState.Increase);
                    }
                    else
                    {
                        uiView.UpdateEnergyPointView(EnergyItemState.Empty);
                    }
                }
            }

            if (leftCount > currentCount)
            {
                m_energyPreviewText.SetActiveSafely(true);
                m_energyPreviewText.text = $"+{leftCount - currentCount}";
            }
            else if (leftCount < currentCount)
            {
                m_energyPreviewText.SetActiveSafely(true);
                m_energyPreviewText.text = $"-{currentCount - leftCount}";
            }
            else
            {
                m_energyPreviewText.SetActiveSafely(false);
            }
        }


        private void OnEnergyItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            BattleEnergyItemUIView view = holder.AddComponent<BattleEnergyItemUIView>();
            view.Init(this);
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_energyBtn;
        private Button m_titleIconBtn;
        private GameObjectPool m_energyPointGroup;
        private Text m_energyPreviewText;
        private GameObject m_energyTips;
        private Text m_currentEnergyCount;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_energyBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyBtn"), typeof(Button)) as Button;
            m_titleIconBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TitleIconBtn"), typeof(Button)) as Button;
            m_energyPointGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyPointGroup"), typeof(GameObjectPool)) as GameObjectPool;
            m_energyPreviewText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyPreviewText"), typeof(Text)) as Text;
            m_energyTips = UIBindUtility.GetBindObject(group.GetItemInfoByName("EnergyTips"));
            m_currentEnergyCount = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CurrentEnergyCount"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
