using Phoenix.Core;
using Phoenix.Core.UIEffect;
using UnityEngine.UI;

namespace Phoenix.GameLogic.UI
{
    public class BattleStrategySkillUIView : UIView
    {
        protected override void OnInit()
        {
            gameObject.SetActiveSafely(false);
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementSubscribe, BattleUIElement.Get(gameObject, BattleUIDefine.BattleCommonBtnMask));
        }
        protected override void OnUnInit()
        {
            EventManager.instance.Broadcast(EventID.BattleMode_UIElementUnsubscribe, BattleUIElement.Get(gameObject));
        }




        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private Button m_skillButton;
        private UIEffectGroup m_effectState; // Enable Disable 
        private Image m_icon;
        private Text m_name;
        private Text m_coolDown;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_skillButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillButton"), typeof(Button)) as Button;
            m_effectState = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EffectState"), typeof(UIEffectGroup)) as UIEffectGroup;
            m_icon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Icon"), typeof(Image)) as Image;
            m_name = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Name"), typeof(Text)) as Text;
            m_coolDown = UIBindUtility.GetBindComponent(group.GetItemInfoByName("CoolDown"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
