
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{

    public enum EnergyItemState
    {
        Normal,
        Empty,
        Decrease,
        Increase
    }

    public class BattleEnergyItemUIView : UIView
    {
        public void UpdateEnergyPointView(EnergyItemState state)
        {
            gameObject.SetActiveSafely(true);
            switch (state)
            {
                case EnergyItemState.Normal:
                    m_energyUIGroup.SetToUIGroup("Normal");
                    m_energyAnimationCtrl.Stop();
                    break;
                case EnergyItemState.Empty:
                    m_energyUIGroup.SetToUIGroup("Empty");
                    m_energyAnimationCtrl.Stop();
                    break;
                case EnergyItemState.Decrease:
                    m_energyUIGroup.SetToUIGroup("Decrease");
                    m_energyAnimationCtrl.Play(0);
                    break;
                case EnergyItemState.Increase:
                    m_energyUIGroup.SetToUIGroup("Increase");
                    m_energyAnimationCtrl.Play(1);
                    break;
            }
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIGroupComponent m_energyUIGroup; // Empty Normal Increase Decrease 
        private AnimationController m_energyAnimationCtrl;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_energyUIGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyUIGroup"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_energyAnimationCtrl = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EnergyAnimationCtrl"), typeof(AnimationController)) as AnimationController;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
