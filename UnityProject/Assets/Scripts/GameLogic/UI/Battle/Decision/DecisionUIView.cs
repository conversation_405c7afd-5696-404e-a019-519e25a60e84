using System;
using System.Collections.Generic;
using UnityEngine.UI;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class DecisionUIView : UIView
    {
        private Boolean m_removeEdit = false;

        protected override void OnInit()
        {
            base.OnInit();
            AddButtonListener(m_exitBtn, (Int32)DecisionUIEventID.CloseBtnClick);

            m_removeEdit = false;
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_exitBtn.onClick.RemoveAllListeners();
        }

        public void UpdateLoadGroupInfo(int formationId)
        {
            m_loadGroup.UpdateInfo(formationId);
        }

        public void UpdateStoreGroupInfo(List<DecisionData> datas)
        {
            m_storeGroup.UpdateInfo(datas);
        }

        public void UpdateLoadDecisionDatas(List<DecisionData> loadDatas)
        {
            m_loadGroup.UpdateLoadDecisionDatas(loadDatas);
            m_storeGroup.UpdateLoadDecisionDatas(loadDatas);
        }

        public void UpdateDecisionData(DecisionData data, Boolean isNew)
        {
            if (isNew)
            {
                m_storeGroup.AddDecisionData(data);
            }
            else
            {
                m_loadGroup.UpdateDecisionData(data);
                m_storeGroup.UpdateDecisionData(data);
            }
        }
        
        public void UpdateDecisionName(Int32 decisionId, String decisionName)
        {
            m_loadGroup.UpdateDecisionName(decisionId, decisionName);
            m_storeGroup.UpdateDecisionName(decisionId, decisionName);
        }

        public void DeleteDecisionData(int decisionId)
        {
            m_loadGroup.RemoveDecisionData(decisionId);
            m_storeGroup.RemoveDecisionData(decisionId);
        }

        public void LoadDecisionData(DecisionData data)
        {
            m_loadGroup.AddDecisionData(data);
            m_storeGroup.LoadDecisionData(data.m_decisionId);
        }

        public void UnLoadDecisionData(DecisionData data)
        {
            m_loadGroup.RemoveDecisionData(data.m_decisionId);
            m_storeGroup.UnLoadDecisionData(data.m_decisionId);
        }

        public void SetStatus(string status)
        {
            if(status == DecisionUIStatusID.Normal)
            {
                m_loadGroup.SetStatus(status);
                m_storeGroup.SetStatus(status);
            }
            else if(status == DecisionUIStatusID.LoadEdit)
            {
                m_storeGroup.SetStatus(status);
                m_loadGroup.SetStatus(status);
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private DecisionLoadUIView m_loadGroup;
        private DecisionStoreUIView m_storeGroup;
        private Button m_exitBtn;
        private Text m_decisionNumText;
        
        public DecisionLoadUIView loadGroup
        {
            get { return m_loadGroup; }
        }
        public DecisionStoreUIView storeGroup
        {
            get { return m_storeGroup; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("LoadGroup"), typeof(DecisionLoadUIView));
            AddBindComponent(group.GetItemInfoByName("StoreGroup"), typeof(DecisionStoreUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_loadGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LoadGroup"), typeof(DecisionLoadUIView)) as DecisionLoadUIView;
            m_storeGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("StoreGroup"), typeof(DecisionStoreUIView)) as DecisionStoreUIView;
            m_exitBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ExitBtn"), typeof(Button)) as Button;
            m_decisionNumText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("DecisionNumText"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
