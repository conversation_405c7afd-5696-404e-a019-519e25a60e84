using System;
using System.Collections.Generic;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class DecisionEditItemUIView : UIView
    {
        //临时数据 后面需要配表
        List<string> tmpDatas = new List<string>()
        {
            "单体攻击", "群体攻击", "单体治疗", "群体治疗", "护卫", "强化效果", "弱化效果", "普通攻击", "召唤", "万鬼幡", "Boss群体"
        };

        private UIToggleGroup m_toggleGroup = new UIToggleGroup();
        private DecisionOrderData m_orderData;
        private int m_index = 0;
        private DecisionEditSelectUIContext m_context;
        private Dictionary<int, DecisionMenuItemBaseData> m_selectedItemData = new Dictionary<int, DecisionMenuItemBaseData>();
        public Action<DecisionOrderData> m_newOrderAction;
        protected override void OnInit()
        {
            base.OnInit();
            m_toggleGroup.ClearToggle();
            m_toggleGroup.AddToggle(m_actorToggle);
            m_toggleGroup.AddToggle(m_skillToggle);
            m_toggleGroup.AddToggle(m_condition1Toggle);
            m_toggleGroup.AddToggle(m_condition2Toggle);
            m_toggleGroup.AddToggle(m_locateToggle);
            m_selectedItemData.Clear();

            m_context = new DecisionEditSelectUIContext(ActionOnSelect, ActionOnSelectEnd);

            m_toggleGroup.actionOnSelectionChanged += ActionOnSelectionChanged;
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_context = null;
            m_toggleGroup.actionOnSelectionChanged -= ActionOnSelectionChanged;
            m_selectedItemData.Clear();
        }

        private void ActionOnSelect(int menuID, DecisionMenuItemBaseData menuData)
        {
            if (m_orderData != null)
                menuData.SetDecisionData(m_orderData);

            switch (menuID)
            {
                case (int)DecisionEditMenuID.Acotr:
                    if (m_orderData == null)
                    {
                        m_orderData = GamePlayerContext.instance.DecisionModule.CreateDecisionOrderData();
                        m_newOrderAction(m_orderData);
                        menuData.SetDecisionData(m_orderData);
                        m_root.SetToUIGroup(m_orderData == null ? "Empty" : "Normal");
                        SetShowInfo();
                    }
                    else
                    {
                        SetActor();
                    }
                    m_toggleGroup.Select(m_skillToggle);
                    break;
                case (int)DecisionEditMenuID.Skill:
                    SetSkill();
                    m_toggleGroup.Select(m_condition1Toggle);
                    break;
                case (int)DecisionEditMenuID.Condition1:
                    SetCondition(1);
                    m_toggleGroup.Select(m_condition2Toggle);
                    break;
                case (int)DecisionEditMenuID.Condition2:
                    SetCondition(2);
                    m_toggleGroup.Select(m_locateToggle);
                    break;
                case (int)DecisionEditMenuID.Locate:
                    SetLocate();
                    Broadcast<Int32>((Int32)DecisionEditUIEventID.OrderFinish, m_index);
                    break;

            }
        }

        private void ActionOnSelectionChanged(int index, IToggleHandler handler)
        {
            m_context.MenuID = index;
            m_context.ActorRid = m_orderData != null ? m_orderData.m_actorRid : 0;
            m_context.Index = m_index;
            m_context.OrderData = m_orderData;
            Broadcast<DecisionEditSelectUIContext>((Int32)DecisionEditUIEventID.SelectBtnClick, m_context);
        }

        private void ActionOnSelectEnd()
        {
            m_toggleGroup.CancelSelect();
        }

        public void SetData(DecisionOrderData data, int index)
        {
            m_index = index;
            m_indexText.text = (index + 1).ToString();
            m_orderData = data;
            SetShowInfo();

            m_toggleGroup.CancelSelect();

        }

        private void SetActor()
        {
            m_actorToggle._showLock = m_orderData == null;
            m_actorToggle.RefreshView();

            if (m_orderData == null)
            {
                m_actorToggle.SetTitle("Any");
                return;
            }

            Image actorIcon = m_actorToggle._backgroundObj.transform.Find("_Icon").GetComponent<Image>();
            Image actorIcon2 = m_actorToggle._selectObj.transform.Find("_Icon").GetComponent<Image>();
            actorIcon.gameObject.SetActive(true);
            actorIcon2.gameObject.SetActive(true);

            if (m_orderData.m_actorRid != 0)
            {
                ActorConfigData actorConfigData = ConfigDataManager.instance.GetActor(m_orderData.m_actorRid);
                EntitySkinConfigData skinConfigData = ConfigDataManager.instance.GetEntitySkin(actorConfigData.SkinId);
                if (skinConfigData != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, actorIcon, skinConfigData.RealIconAtlasC, skinConfigData.RealIconNameC);
                    CommonUIUtility.UpdateIconSprite(m_owner, actorIcon2, skinConfigData.RealIconAtlasC, skinConfigData.RealIconNameC);
                }
                m_actorToggle.SetTitle($"{actorConfigData.Name}");
            }
            else if (m_orderData.m_entityCareerId != 0)
            {
                EntityCareerConfigData entityCareer = ConfigDataManager.instance.GetEntityCareer((EntityCareerId)m_orderData.m_entityCareerId);
                if (entityCareer != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, actorIcon, entityCareer.IconAtlasName, entityCareer.IconSubName);
                    CommonUIUtility.UpdateIconSprite(m_owner, actorIcon2, entityCareer.IconAtlasName, entityCareer.IconSubName);
                }
                m_actorToggle.SetTitle($"所有{entityCareer.Name}");
            }
            else if (m_orderData.m_entityElementId != 0)
            {
                EntityElementConfigData entityElement = ConfigDataManager.instance.GetEntityElement((EntityElementId)m_orderData.m_entityElementId);
                if (entityElement != null)
                {
                    CommonUIUtility.UpdateIconSprite(m_owner, actorIcon, entityElement.IconAtlasName, entityElement.IconSubName);
                    CommonUIUtility.UpdateIconSprite(m_owner, actorIcon2, entityElement.IconAtlasName, entityElement.IconSubName);
                }
                m_actorToggle.SetTitle($"所有{entityElement.Name}");
            }
            else
            {
                actorIcon.gameObject.SetActive(false);
                actorIcon2.gameObject.SetActive(false);
                m_actorToggle.SetTitle("任意英雄");
            }
        }

        private void SetSkill()
        {
            if (m_orderData == null)
                return;

            bool isNull = false;
            if (m_orderData.m_skillSelectType == (int)TeamDecisionSkillSelectType.Priority)
            {
                m_skillToggle.SetTitle("任意技能");
            }
            else if (m_orderData.m_skillSelectType == (int)TeamDecisionSkillSelectType.Rid)
            {
                var skillConfig = ConfigDataManager.instance.GetSkill(m_orderData.m_skillRid);
                m_skillToggle.SetTitle(skillConfig != null ? skillConfig.name : "Error");
            }
            else if (m_orderData.m_skillSelectType == (int)TeamDecisionSkillSelectType.Tag && m_orderData.m_skillTags != null)
            {
                string title = "";
                foreach (var tag in m_orderData.m_skillTags)
                {
                    title += tmpDatas.Count >= tag ? tmpDatas[tag - 1] : "";
                }
                m_skillToggle.SetTitle(title);
            }
            else
            {
                m_skillToggle.SetTitle("");
                isNull = true;
            }

            m_skillToggle._showLock = isNull;
            m_skillToggle.RefreshView();
        }

        private void SetCondition(int conditionIndex)
        {
            if (m_orderData == null)
                return;

            bool isNull = false;
            int conditionId = conditionIndex == 1 ? m_orderData.m_condition1 : m_orderData.m_condition2;
            var conditionToggle = conditionIndex == 1 ? m_condition1Toggle : m_condition2Toggle;
            if (conditionId == 0)
            {
                conditionToggle.SetTitle("");
                isNull = true;
            }
            else
            {
                var conditionConfig = ConfigDataManager.instance.GetTeamDecisionCondition(conditionId);
                conditionToggle.SetTitle(conditionConfig != null ? conditionConfig.Name : "Error");
            }

            conditionToggle._showLock = isNull;
            conditionToggle.RefreshView();
        }

        private void SetLocate()
        {
            if (m_orderData == null)
                return;

            bool isNull = false;
            if (m_orderData.m_locateSelect == 0)
            {
                m_locateToggle.SetTitle("");
                isNull = true;
            }
            else
            {
                var locateConfig = ConfigDataManager.instance.GetTeamDecisionLocateSelect(m_orderData.m_locateSelect);
                m_locateToggle.SetTitle(locateConfig != null ? locateConfig.Name : "Error");
            }

            m_locateToggle._showLock = isNull;
            m_locateToggle.RefreshView();
        }

        public void SetShowInfo()
        {
            m_root.SetToUIGroup(m_orderData == null ? "Empty" : "Normal");
            SetActor();
            SetSkill();
            SetCondition(1);
            SetCondition(2);
            SetLocate();
        }

        public void AutoSelected()
        {
            m_toggleGroup.Select(m_actorToggle);
        }

        public int Index { get { return m_index; } }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIToggle m_actorToggle;
        private UIToggle m_skillToggle;
        private UIToggle m_condition1Toggle;
        private UIToggle m_condition2Toggle;
        private UIToggle m_locateToggle;
        private UIGroupComponent m_root; // Normal Empty 
        private Text m_indexText;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_actorToggle = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ActorToggle"), typeof(UIToggle)) as UIToggle;
            m_skillToggle = UIBindUtility.GetBindComponent(group.GetItemInfoByName("SkillToggle"), typeof(UIToggle)) as UIToggle;
            m_condition1Toggle = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Condition1Toggle"), typeof(UIToggle)) as UIToggle;
            m_condition2Toggle = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Condition2Toggle"), typeof(UIToggle)) as UIToggle;
            m_locateToggle = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LocateToggle"), typeof(UIToggle)) as UIToggle;
            m_root = UIBindUtility.GetBindComponent(group.GetItemInfoByName("Root"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_indexText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("IndexText"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
