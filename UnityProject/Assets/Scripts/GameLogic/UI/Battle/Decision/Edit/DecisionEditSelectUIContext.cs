using System;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public class DecisionEditSelectUIContext
    {
        public Action<int, DecisionMenuItemBaseData> ActionOnSelect {get;}
        public Action ActionOnSelelctEnd {get;}
        public int MenuID {get; set;}
        public int ActorRid {get; set;}
        public int Index {get; set;}
        public DecisionOrderData OrderData {get; set;}

        public DecisionEditSelectUIContext(Action<int, DecisionMenuItemBaseData> actionOnSelect, Action actionOnSelelctEnd)
        {
            ActionOnSelect = actionOnSelect;
            ActionOnSelelctEnd = actionOnSelelctEnd;
        }
    }
}
