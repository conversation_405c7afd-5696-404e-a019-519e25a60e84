using System;
using UnityEngine;
using UnityEngine.Assertions;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public enum DecisionEditMenuID
    {
        Acotr,
        Skill,
        Condition1,
        Condition2,
        Locate,
    }

    public enum DecisionEditUIEventID
    {
        CloseBtnClick,
        SelectBtnClick,
        OrderFinish,
        SaveBtnClick,
        IconBtnClick,
        IconSaveBtnClick,
        NameBtnClick,
        NameSaveBtnClick,
    }

    public class DecisionEditUI : UIBase
    {
        private DecisionData m_oriDecisionData;
        private DecisionData m_decisionData;
        public static void Show(DecisionData decisionData)
        {   
            UICustomContext context = new UICustomContext(typeof(DecisionEditUI));
            context.SetParam("decisionData", decisionData);
            UIManager.instance.Open(context, true);
        }

        protected override void OnInit()
        {
            base.OnInit();

            RegisterListener((Int32)DecisionEditUIEventID.CloseBtnClick, OnCloseBtnClick);
            RegisterListener((Int32)DecisionEditUIEventID.SaveBtnClick, OnSaveBtnClick);
            RegisterListener<DecisionEditSelectUIContext>((Int32)DecisionEditUIEventID.SelectBtnClick, OnEditSelectBtnClick);
            RegisterListener((Int32)DecisionEditUIEventID.IconBtnClick, OnIconBtnClick);
            RegisterListener<Int32>((Int32)DecisionEditUIEventID.IconSaveBtnClick, OnIconSaveBtnClick);
            RegisterListener((Int32)DecisionEditUIEventID.NameBtnClick, OnNameBtnClick);
            RegisterListener<String>((Int32)DecisionEditUIEventID.NameSaveBtnClick, OnNameSaveBtnClick);
            RegisterListener<Int32>((Int32)DecisionEditUIEventID.OrderFinish, OnOrderFinish);

            GamePlayerContext.instance.DecisionModule.EventOnDecisionDataSave += OnDecisionDataSave;

        }

        protected override void OnUnInit()
        {
            base.OnUnInit();

            UnRegisterListener((Int32)DecisionEditUIEventID.CloseBtnClick, OnCloseBtnClick);
            UnRegisterListener((Int32)DecisionEditUIEventID.SaveBtnClick, OnSaveBtnClick);
            UnRegisterListener<DecisionEditSelectUIContext>((Int32)DecisionEditUIEventID.SelectBtnClick, OnEditSelectBtnClick);
            UnRegisterListener((Int32)DecisionEditUIEventID.IconBtnClick, OnIconBtnClick);
            UnRegisterListener<Int32>((Int32)DecisionEditUIEventID.IconSaveBtnClick, OnIconSaveBtnClick);
            UnRegisterListener((Int32)DecisionEditUIEventID.NameBtnClick, OnNameBtnClick);
            UnRegisterListener<String>((Int32)DecisionEditUIEventID.NameSaveBtnClick, OnNameSaveBtnClick);
            UnRegisterListener<Int32>((Int32)DecisionEditUIEventID.OrderFinish, OnOrderFinish);
            
            GamePlayerContext.instance.DecisionModule.EventOnDecisionDataSave -= OnDecisionDataSave;
        }

        protected override void OnRefreshViews()
        {
            base.OnRefreshViews();
            
            var context = m_currentContext as UICustomContext;
            if(context != null)
            {
                object decisionData;
                context.TryGetParam("decisionData", out decisionData);
                m_oriDecisionData = (DecisionData)decisionData;
                m_decisionData = (DecisionData)m_oriDecisionData.Clone();
            }

            Assert.IsTrue(m_decisionData != null, "Decision Edit data is null");

            m_mainView.UpdateInfo(m_decisionData);
        }

        private void OnCloseBtnClick()
        {
            if(m_decisionData != null && (m_decisionData.m_decisionId == 0 && m_decisionData.m_orders.Count > 0 || !m_decisionData.Equals(m_oriDecisionData)))
            {
                MessageBox.Show("有尚未保存的更改内容，退出将会导致未保", "提示", () => {
                    Close();
                });
                return;
            }
            Close();
        }

        private void OnSaveBtnClick()
        {
            GamePlayerContext.instance.DecisionModule.SaveDecisionData(m_decisionData);
        }

        private void OnEditSelectBtnClick(DecisionEditSelectUIContext context)
        {
            m_mainView.selectUIView.UpdateView(context.Index, context.MenuID, context.ActorRid, context.OrderData, context.ActionOnSelect, (bool bClose)=>{
                if(context.ActionOnSelelctEnd != null)
                    context.ActionOnSelelctEnd();
                if(bClose)
                    m_mainView.OnSelectUIViewStatusChange(false);
            });
            m_mainView.OnSelectUIViewStatusChange(true, context.Index);
        }

        private void OnIconBtnClick()
        {
            m_mainView.selectHeadUIView.UpdateInfo(m_decisionData);
        }

        private void OnIconSaveBtnClick(int index)
        {
            m_decisionData.m_headIndex = index;
            m_mainView.selectHeadUIView.SetActive(false);
        }

        private void OnNameBtnClick()
        {
            m_mainView.editNameUIView.UpdateInfo(m_decisionData);
        }

        private void OnNameSaveBtnClick(string name)
        {
            GamePlayerContext.instance.DecisionModule.UpdateDecisionDataName(m_decisionData.m_decisionId, name);
            m_oriDecisionData.m_decisionName = name;
            m_decisionData.m_decisionName = name;
            m_mainView.editNameUIView.SetActive(false);
            m_mainView.UpdateDecisionName(name);
        }

        private void OnOrderFinish(int index)
        {
            m_mainView.AutoChooseOrder(index + 1);
        }
        
        private void OnDecisionDataSave(DecisionData data, Boolean isNew)
        {
            if(m_decisionData != null && data.m_decisionId == m_decisionData.m_decisionId)
            {
                m_oriDecisionData = m_decisionData;
            }
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private DecisionEditUIView m_mainView;
        
        public DecisionEditUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<DecisionEditUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
