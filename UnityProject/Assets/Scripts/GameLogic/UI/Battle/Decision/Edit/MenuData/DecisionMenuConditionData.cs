using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.UI
{
    public class DecisionMenuConditionData : DecisionMenuBaseData
    {
        private int m_dataType;
        private int m_conditionType;
        public DecisionMenuConditionData(string name, int dataType, int conditionType) : base(name)
        {
            m_dataType = dataType;
            m_conditionType = conditionType;
        }

        protected override void OnInit()
        {
            m_menuItems.Add(new DecisionMenuItemCondiotionData("��", 0, null, m_dataType, 0, null));
            foreach(var conf in ConfigDataManager.instance.teamDecisionConditionMap.Values)
            {
                if((int)conf.FuncType == m_conditionType)
                    m_menuItems.Add(new DecisionMenuItemCondiotionData(conf.Name, 0, null, m_dataType, conf.Id, null));
            }
        }
    }
}