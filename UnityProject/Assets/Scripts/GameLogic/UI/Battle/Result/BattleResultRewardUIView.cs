using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public partial class BattleResultRewardUIView : UIView
    {
        protected override void OnInit()
        {
            m_rewardContentPool.Init(OnRewardItemPoolInit);
        }

        public void UpdateRewardView(BattleResultData resultData)
        {
            gameObject.SetActiveSafely(true);
            m_rewardContentPool.ReleaseAll();
            Boolean rewardIsEmpty = true;
            DropGroupConfigData dropGroupConfigData = ConfigDataManager.instance.GetDropGroup(resultData.normalRewardDropId);
            if (dropGroupConfigData != null)
            {
                foreach (RewardConfigData reward in dropGroupConfigData.Rewards)
                {
                    BattleResultRewardItemUIView uiView = m_rewardContentPool.FetchComponent<BattleResultRewardItemUIView>();
                    uiView.UpdateRewardView(reward, ItemRewardType.None);
                    rewardIsEmpty = false;
                }
            }

            if (resultData.newCompletedBattleAchievementInfos.Count > 0)
            {
                foreach (var battleAchievementInfo in resultData.newCompletedBattleAchievementInfos)
                {
                    DropGroupConfigData achievementDropRewards = ConfigDataManager.instance.GetDropGroup(battleAchievementInfo.rewardId);
                    if (achievementDropRewards != null)
                    {
                        foreach (RewardConfigData reward in achievementDropRewards.Rewards)
                        {
                            BattleResultRewardItemUIView uiView = m_rewardContentPool.FetchComponent<BattleResultRewardItemUIView>();
                            uiView.UpdateRewardView(reward, ItemRewardType.Achievement);
                            rewardIsEmpty = false;
                        }
                    }
                }
            }
            m_rewardGroup.SetToUIGroup(rewardIsEmpty ? "NoReward" : "RewardList");
        }

        private void OnRewardItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            BattleResultRewardItemUIView itemView = holder.AddComponent<BattleResultRewardItemUIView>();
            itemView.Init(this);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObjectPool m_rewardContentPool;
        private UIGroupComponent m_rewardGroup; // RewardList NoReward 
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_rewardContentPool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("RewardContentPool"), typeof(GameObjectPool)) as GameObjectPool;
            m_rewardGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("RewardGroup"), typeof(UIGroupComponent)) as UIGroupComponent;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
