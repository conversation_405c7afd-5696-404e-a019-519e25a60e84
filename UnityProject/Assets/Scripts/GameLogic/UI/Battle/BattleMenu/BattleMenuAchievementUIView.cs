using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public partial class BattleMenuAchievementUIView : UIView
    {
        protected override void OnInit()
        {
            m_achievementContent.Init(OnAchievementItemPoolInit);
        }

        public void UpdateView(BattleConfigData battleConfig)
        {
            m_achievementContent.ReleaseAll();
            var stageInfo = BattleShortCut.sampleBattle.GetCurStageInfo();
            if (stageInfo.achievementList != null && stageInfo.achievementList.Count > 0)
            {
                gameObject.SetActiveSafely(true);
                foreach (var achievementInfo in stageInfo.achievementList)
                {
                    var view = m_achievementContent.FetchComponent<BattleMenuAchievementItemUIView>();
                    view.UpdateView(achievementInfo);
                }
            }
            else
            {
                gameObject.SetActiveSafely(false);
            }
        }

        private void OnAchievementItemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<BattleMenuAchievementItemUIView>();
            view.Init(this);
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObjectPool m_achievementContent;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_achievementContent = UIBindUtility.GetBindComponent(group.GetItemInfoByName("AchievementContent"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
