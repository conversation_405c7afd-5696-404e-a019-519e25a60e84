

using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.UI
{
    public interface IFormationEntityDataAdapter
    {
        FixedValue GetAttributeValue(AttributeId attrId);
        ActorConfigData GetActorConfigData();
        Int32 GetNormalAttackRid();
        List<Int32> GetSkillList();
        List<Int32> GetPassiveSkillList();
        Int32 GetTalentSkill();

        Int32 GetMaxHp();
        Int32 GetCurHp();
    }
}
