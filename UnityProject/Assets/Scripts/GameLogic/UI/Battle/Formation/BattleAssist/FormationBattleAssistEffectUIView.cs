using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class FormationBattleAssistEffectUIView : UIView
    {
        private Int32 m_battleAssistEffectId;
        private Boolean[] m_conditionResult;

        protected override void OnInit()
        {
            m_conditionGroup.Init(OnConditiontemPoolInit);
            m_effectButton.onClick.AddListener(delegate
            {
                if (m_battleAssistEffectId != 0)
                {
                    Broadcast((Int32)FormationUIEventId.BattleAssistEffectBtnClick, m_battleAssistEffectId, m_conditionResult);
                }
            });
        }

        public void Hide()
        {
            m_battleAssistEffectId = 0;
            gameObject.SetActiveSafely(false);
        }

        public void ShowAndUpdateView(BattleFormationEffectConfigData battleAssistEffectConfig, Boolean[] conditionResult)
        {
            m_battleAssistEffectId = battleAssistEffectConfig.Id;
            m_conditionResult = conditionResult;
            UpdateBattleAssistEffectIcon(battleAssistEffectConfig);
            UpdateBattleAssistEffectActiveState(battleAssistEffectConfig, conditionResult);
            UpdateConditionView(battleAssistEffectConfig, conditionResult);
            gameObject.SetActiveSafely(true);
        }


        private void UpdateBattleAssistEffectIcon(BattleFormationEffectConfigData battleAssistEffectConfig)
        {
            CommonUIUtility.UpdateIconSprite(m_owner, m_effectIcon, battleAssistEffectConfig.IconAtlasName, battleAssistEffectConfig.IconSubName);
        }

        private void UpdateBattleAssistEffectActiveState(BattleFormationEffectConfigData battleAssistEffectConfig, Boolean[] conditionResult)
        {
            Int32 activeConditionCount = 0;
            for(Int32 index = 0; index < conditionResult.Length; index++)
            {
                if (conditionResult[index])
                {
                    activeConditionCount++;
                }
            }
            if (battleAssistEffectConfig.ConditionIdList.Count == activeConditionCount)
            {
                m_effectStateGroup.SetToUIGroup("Normal");
            }
            else
            {
                m_effectStateGroup.SetToUIGroup("Locked");
            }
        }

        private void UpdateConditionView(BattleFormationEffectConfigData battleAssistEffectConfig, Boolean[] conditionResult)
        {
            m_conditionGroup.ReleaseAll();
            for(Int32 index = 0; index < battleAssistEffectConfig.ConditionIdList.Count; index++)
            {
                BattleAssistConditionItemUIView view = m_conditionGroup.FetchComponent<BattleAssistConditionItemUIView>();
                Int32 conditionId = battleAssistEffectConfig.ConditionIdList[index];
                Boolean active = conditionResult[index];
                view.UpdateView(conditionId, active);
            }
        }

        private void OnConditiontemPoolInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<BattleAssistConditionItemUIView>();
            view.Init(this);
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private UIGroupComponent m_effectStateGroup; // Normal Locked 
        private Button m_effectButton;
        private Image m_effectIcon;
        private GameObjectPool m_conditionGroup;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_effectStateGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EffectStateGroup"), typeof(UIGroupComponent)) as UIGroupComponent;
            m_effectButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EffectButton"), typeof(Button)) as Button;
            m_effectIcon = UIBindUtility.GetBindComponent(group.GetItemInfoByName("EffectIcon"), typeof(Image)) as Image;
            m_conditionGroup = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ConditionGroup"), typeof(GameObjectPool)) as GameObjectPool;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
