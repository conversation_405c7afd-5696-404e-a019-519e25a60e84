using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.UI
{
    public enum Entity2DSkinIconType
    {
        IconA,
        IconB,
        IconC,
    }

    public static class CommonUIUtility
    {
        public static void UpdateBtnSprite(UIBase owner, Button btn, SpriteAtlasId atlasId, String strIconNml, String strIconSel = "", String strIconDwn = "", String strIconDis = "")
        {
            if (btn == null) { return; }

            Image image = btn.GetComponent<Image>();
            if (image == null) { return; }
            image.sprite = null;

            if (owner == null || string.IsNullOrEmpty(strIconNml))
            {
                return;
            }

            Sprite spriteNomal = null;
            Sprite spriteSelected = null;
            Sprite spritePressed = null;
            Sprite spriteDisabled = null;
            SpriteAtlasConfigData atlasConfigData = ConfigDataManager.instance.GetSpriteAtlas(atlasId);
            if (atlasConfigData != null)
            {
                spriteNomal = owner.GetSprite(atlasConfigData.Path, strIconNml);
                spriteSelected = !string.IsNullOrEmpty(strIconSel) ? owner.GetSprite(atlasConfigData.Path, strIconSel) : null;
                spritePressed = !string.IsNullOrEmpty(strIconDwn) ? owner.GetSprite(atlasConfigData.Path, strIconDwn) : null;
                spriteDisabled = !string.IsNullOrEmpty(strIconDis) ? owner.GetSprite(atlasConfigData.Path, strIconDis) : null;
            }

            Sprite spriteTemp = GetTempSprite();
            image.sprite = spriteNomal ?? spriteTemp;
            btn.spriteState = new SpriteState
            {
                pressedSprite = spritePressed ?? spriteTemp,
                selectedSprite = spriteSelected ?? spriteTemp,
                disabledSprite = spriteDisabled ?? spriteTemp
            };
        }

        public static void UpdateBtnSprite(UIBase owner, Button btn, String strPathNml, String strPathSel = "", String strPathDwn = "", String strPathDis = "")
        {
            if (btn == null) { return; }

            Image image = btn.GetComponent<Image>();
            if (image == null) { return; }
            image.sprite = null;

            if (owner == null || string.IsNullOrEmpty(strPathNml))
            {
                return;
            }

            Sprite spriteTemp = GetTempSprite();
            Sprite spriteNomal = owner.GetSprite(strPathNml);
            Sprite spriteSelected = !string.IsNullOrEmpty(strPathSel) ? owner.GetSprite(strPathSel) : null;
            Sprite spritePressed = !string.IsNullOrEmpty(strPathDwn) ? owner.GetSprite(strPathDwn) : null;
            Sprite spriteDisabled = !string.IsNullOrEmpty(strPathDis) ? owner.GetSprite(strPathDis) : null;
            
            image.sprite = spriteNomal ?? spriteTemp;

            btn.spriteState = new SpriteState
            {
                pressedSprite = spritePressed ?? spriteTemp,
                selectedSprite = spriteSelected ?? spriteTemp,
                disabledSprite = spriteDisabled ?? spriteTemp
            };
        }

        public static void UpdateIconSprite(UIBase owner, Image image, SpriteAtlasId atlasId, String spriteName)
        {
            if (image == null)
            {
                return;
            }
            image.sprite = null;
            if (owner == null)
            {
                return;
            }
            Sprite sprite  = null;
            SpriteAtlasConfigData atlasConfigData = ConfigDataManager.instance.GetSpriteAtlas(atlasId);
            if (atlasConfigData != null)
            {
                sprite = owner.GetSprite(atlasConfigData.Path, spriteName);
            }
            image.sprite = sprite ?? GetTempSprite();
        }

        public static void UpdateIconSprite(UIBase owner, Image image, String path)
        {
            if (image == null)
            {
                return;
            }
            image.sprite = null;
            if (owner == null)
            {
                return;
            }

            if (String.IsNullOrEmpty(path))
            {
                return;
            }

            Sprite sprite = owner.GetSprite(path);
            image.sprite = sprite ?? GetTempSprite();
        }

        public static void UpdateItemRaritySprite(UIBase owner, Image image, EntityRarityId rarityId)
        {
            EntityRarityConfigData rarityConfig = ConfigDataManager.instance.GetEntityRarity(rarityId);
            if (rarityConfig != null)
            {
                UpdateIconSprite(owner, image, rarityConfig.IconAtlasName, rarityConfig.ItemIconName);
            }
        }

        public static void UpdateItemInfoRarityBgSprite(UIBase owner, Image image, EntityRarityId rarityId)
        {
            EntityRarityConfigData rarityConfig = ConfigDataManager.instance.GetEntityRarity(rarityId);
            if (rarityConfig != null)
            {
                UpdateIconSprite(owner, image, rarityConfig.ItemInfoBgName);
            }
        }

        public static void UpdateItemInfoRarityLongBgSprite(UIBase owner, Image image, EntityRarityId rarityId)
        {
            EntityRarityConfigData rarityConfig = ConfigDataManager.instance.GetEntityRarity(rarityId);
            if (rarityConfig != null)
            {
                UpdateIconSprite(owner, image, rarityConfig.ItemInfoLongBgName);
            }
        }

        public static void UpdateEntity2DSkinSpriteAByEntityId(UIBase owner, Image image, Int32 entityId)
        {
            WorldEntityConfigData worldEntityConfigData = ConfigDataManager.instance.GetWorldEntity(entityId);
            if (worldEntityConfigData == null)
            {
                Debug.LogError($"UpdateEntitySkinSpriteA:WorldEntityConfigData is null entityId = {entityId}");
                return;
            }

            UpdateEntity2DSkinSprite(owner, image, worldEntityConfigData.EntitySkinId, Entity2DSkinIconType.IconA);
        }

        public static void UpdateEntity2DSkinSprite(UIBase owner, Image image, Int32 skinId, Entity2DSkinIconType iconType)
        {
            EntitySkinConfigData entitySkinConfigData = ConfigDataManager.instance.GetEntitySkin(skinId);
            if (entitySkinConfigData == null)
            {
                Debug.LogError($"entitySkinConfigData is null EntitySkinId = {skinId}");
                return;
            }

            if (iconType == Entity2DSkinIconType.IconC)
            {
                SpriteAtlasId atlasId = entitySkinConfigData.RealIconAtlasC;
                string spriteName = entitySkinConfigData.RealIconNameC;
                if (atlasId == SpriteAtlasId.None || String.IsNullOrEmpty(spriteName))
                {
                    Debug.LogError($"atlasId or spriteName is empty atlasId = {atlasId}, spriteName = {spriteName}, EntitySkinId = {skinId}");
                    return;
                }
                UpdateIconSprite(owner, image, atlasId, spriteName);
                return;
            }

            string entityIconPath = iconType == Entity2DSkinIconType.IconA ? entitySkinConfigData.RealIconPathA : entitySkinConfigData.RealIconPathB;
            if (String.IsNullOrEmpty(entityIconPath))
            {
                Debug.LogError($"entityIcon PathA or PathB is empty skinId = {skinId}");
                return;
            }
            UpdateIconSprite(owner, image, entityIconPath);

        }

        private static Sprite GetTempSprite()
        {
            Sprite sprite = null;
#if UNITY_EDITOR
            String path = "Assets/Res/UIIcon/Common/TempTexture.png";
            sprite = ResourceHandleManager.instance.GetResource<Sprite>(path);
#endif
            return sprite;
        }

        public static Vector2 GetUIElementScreenPosition(Transform transform, SceneLayerCanvas canvas)
        {
            RectTransform rectTransform = canvas.GetComponent<RectTransform>();
            float rateX = rectTransform.rect.width / Screen.width;
            float rateY = rectTransform.rect.height / Screen.height;

            Vector3 posTarget = canvas.worldCamera.WorldToScreenPoint(transform.position);
            Vector2 position = new Vector2(posTarget.x * rateX - rectTransform.rect.width * 0.5f, posTarget.y * rateY - rectTransform.rect.height * 0.5f);
            //Debug.Log($"=====1-0===== Pos0 = {posTarget}");
            //Debug.Log($"=====1-1===== Screen W = {Screen.width}, H = {Screen.height}");
            //Debug.Log($"=====1-2===== Rect W = {rectTransform.rect.width}, H = {rectTransform.rect.height}");
            //Debug.Log($"=====1-3===== Pos1 = {position}");

            return position;
        }
    }
}
