using Phoenix.Battle;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.GameLogic.UI
{
    public class DebugUI : UIBase
    {
        public static void Log(string str)
        {
#if !RELEASE
            var ui = UIManager.instance.GetUIBase<DebugUI>(false);
            if (ui != null)
            {
                ui.LogInternal(str);
            }
#endif
        }

        public static void AnalyzeBattleReport(BattleActionProcedureResult result)
        {
#if !RELEASE
            var ui = UIManager.instance.GetUIBase<DebugUI>(false);
            if (ui != null)
            {
                ui.AnalyzeBattleReportInternal(result);
            }
#endif
        }

        private void LogInternal(string str)
        {
#if !RELEASE
            m_mainView.AddLog(str);
#endif
        }

        private void AnalyzeBattleReportInternal(BattleActionProcedureResult result)
        {
#if !RELEASE
            m_mainView.AnalyzeBattleReport(result);
#endif
        }

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private DebugUIView m_mainView;
        
        public DebugUIView mainView
        {
            get { return m_mainView; }
        }
        
        protected override UIView OnAddView(GameObject viewObj, string name)
        {
            switch (name)
            {
                case "MainView":
                    m_mainView = viewObj.AddComponent<DebugUIView>();
                    return m_mainView;
            }
            return base.OnAddView(viewObj, name);
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
