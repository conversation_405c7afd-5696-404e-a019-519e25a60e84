using MyFramework;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.GameLogic.GameContext;
using System.Text;
using Phoenix.GameLogic.Opening;
using Phoenix.Guide;
using System.IO;
using Newtonsoft.Json;
using Phoenix.Hakoniwa;

namespace Phoenix.GameLogic.UI
{
    public class DebugUIView : UIView
    {
        private static readonly float[] m_timeScales = new float[]
        {
            0.3f,
            1f,
            2f,
            5f,
        };

        private static readonly string[] m_timeScaleStrs = new string[]
        {
            "0.3x",
            "1x",
            "2x",
            "5x",
        };

        private StringBuilder m_sb = new StringBuilder();
        private List<DebugToggleUIView> m_timeScaleToggleList = new List<DebugToggleUIView>();




        private void InitDebugView()
        {
            gameObject.AddComponent<FPSMonitor>();
            m_logText.text = string.Empty;

            var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            m_packageVersionText.text = gameIni.GetString("PackageVersion");

            //Common
            {
                for (int i = 0; i < m_timeScales.Length; ++i)
                {
                    bool isOn = Mathf.Abs(m_timeScales[i] - 1f) < 1e-4;
                    var view = AddToggle(m_timeScaleStrs[i], i, null, isOn, OnTimeScaleToggleChanged);
                    m_timeScaleToggleList.Add(view);
                }

#if UNITY_EDITOR
                AddButton("重载\n数据", null, p =>
                {
                    UnityEditor.AssetDatabase.Refresh();
                    var format = ConfigDataManager.instance.GetFilePathFormatByFolder(ConfigDataSetting.instance.resPath, false);
                    foreach (var key in ConfigDataManager.instance.GetLoaderKeys())
                    {
                        string path = string.Format(format, key);
                        var handle = ResourceManager.instance.LoadSync<TextAsset>(path);
                        var textAsset = handle.GetAssetObject<TextAsset>();
                        ConfigDataManager.instance.Load(key, textAsset.bytes);
                        handle.Release();
                    }
                    ConfigDataManager.instance.AnalyzeAfterLoadAll();
                    if (BattleShortCut.battleExecuter != null)
                    {
                        BattleShortCut.battleExecuter.infoGetter.RefreshAll();
                    }
                    EventManager.instance.Broadcast(EventID.ConfigDataReloaded);
                });
                AddButton("保存\n数据", null, p =>
                {
                    UnityEditor.AssetDatabase.SaveAssets();
                });
                AddButton("导出\n战斗数据", null, p =>
                {
                    var folderPath = @"E:\BattleConfig";
                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                    }
                    foreach(var kv in ConfigDataManager.instance.battleMap)
                    {
                        string path = FilePathUtility.GetPath(folderPath, kv.Key.ToString());
                        var jsonStr = JsonConvert.SerializeObject(kv.Value, Formatting.Indented);
                        using (StreamWriter file = new StreamWriter(path, false, Encoding.UTF8))
                        {
                            file.Write(jsonStr);
                        }
                    }
                });
#endif
                if (SRDebugger.Internal.Service.Trigger != null)
                {
                    SRDebugger.Internal.Service.Trigger.IsEnabled = false;
                }
                AddToggle("SRD", null, false, (flag, p) =>
                {
                    if (SRDebugger.Internal.Service.Trigger != null)
                    {
                        SRDebugger.Internal.Service.Trigger.IsEnabled = flag;
                    }
                });
                AddButton("<color=red><b>返回\n登录</b></color>", null, p => { GameStateHelper.ChangeGameState(EGameState.Login, LoadingFlagId.Login); });
                AddButton("<b>服务器\n列表</b>", null, (p) =>
                {
                    SelectServerUI.ShowUI();
                });
                AddButton("<b>Test\nMsgPack</b>", null, (p) =>
                {
                    TestMsgPackUI.ShowUI();
                });
                AddButton("<b>关卡\n列表</b>", null, (p) =>
                {
                    Loading.Open(LoadingFlagId.Lobby, LoadingViewType.FadeBlack, () =>
                    {
                        GameManager.instance.stateMachine.ChangeState((int)EGameState.Lobby);
                        Loading.Close(LoadingFlagId.Lobby);
                    });
                });
                AddButton("截屏", null, p =>
                {
                    string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                    string path = FilePathUtility.GetPath(desktopPath, string.Format("PhoenixScreenshot_{0}.png", DateTime.Now.ToString("yyyyMMdd_HHmmss")));

                    Vector2Int size = new Vector2Int(1024, 720);
                    RenderTexture rt = new RenderTexture(size.x, size.y, 32);
                    ScreenCapture.CaptureScreenshotIntoRenderTexture(rt);
                    Texture2D t2D = new Texture2D(size.x, size.y, TextureFormat.RGB24, false);
                    var preRt = RenderTexture.active;
                    RenderTexture.active = rt;
                    t2D.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
                    RenderTexture.active = preRt;

                    File.WriteAllBytes(path, t2D.EncodeToPNG());
                    //ScreenCapture.CaptureScreenshot(path, 1);
                });
                AddButton("引导\nGM", null, p =>
                {
                    var gmUI = UIManager.instance.GetUIBase<GuideGMUI>(false);
                    if (gmUI == null || gmUI.state == UIState.Closed)
                    {
                        UIManager.instance.Open<GuideGMUI>(true, true);
                    }
                    else if (gmUI.state == UIState.Opened)
                    {
                        UIManager.instance.Close<GuideGMUI>(true);
                        GuideManager.instance.SetBlockEnableInput(false);
                    }
                });
                AddButton("Pool", null, p => { ClassPoolManager.instance.LogOutPoolUsingCount(); });
                AddButton("<color=green>Skip\n剧情</color>", null, (p) =>
                {
                    EventManager.instance.Broadcast(EventID.Timeline_Skip);
                });
                AddButton("Monitor\nStart", null, p => { Monitor.GOT.MonitorDataUtil.MonitorBegin(); });
                AddButton("Monitor\nEnd", null, p => { Monitor.GOT.MonitorDataUtil.MonitorEnd(); });
                //AddButton("AddTag", null, p => { GPMWrapper.StartTag("Profile1"); });
                //AddButton("StopTag", null, p => { GPMWrapper.StopTag(); });
                //AddButton("卸载闲\n置资源", null, (p) =>
                //{
                //    ResourceManager.instance.UnloadUnusedAssets();
                //});
                //AddButton("-GC-", null, (p) =>
                //{
                //    GC.Collect();
                //});
            }

            //Login
            {
                AddButton("Reset\nContext", new[] { EGameState.Login }, (p) =>
                {
                    GamePlayerContext.instance.ResetPlayerContext();
                    TipUI.ShowTip("Reset GameContext Success.");
                });
            }

            //Lobby
            {
                AddButton("Hot", new[] { EGameState.Lobby }, (p) => { HotDllManager.Instance.TestEntry(); });
                AddButton("OpScn", new[] { EGameState.Lobby }, (p) => { OpenSceneCreator.instance.StartOpeningScene(); });
                AddButton("PreLoad\nCombatScene", new[] { EGameState.Lobby }, (p) => { StaticHakoniwa.PreLoadCombatScene = true; });
            }

            //Battle
            {
                AddToggle("格子", new[] { EGameState.Battle }, false, (flag, p) =>
                {
                    BattleShortCut.battleSceneGridManager.UpdateGridDebugText(flag);
                });
                AddButton("重开", new[] { EGameState.Battle }, p =>
                {
                    BattleRetractManager.instance.StartRetract();
                    BattleRetractManager.instance.FirstStep();
                    BattleRetractManager.instance.ApplyRetract();
                    BattleOpModeManager.instance.ChangeMode(BattleOpModeId.BattleOpMode);
                });
                AddButton("再战", new[] { EGameState.Battle }, p =>
                {
                    BattleShortCut.battleExecuter.RestartBattle();
                });
                AddButton("战前", new[] { EGameState.Battle }, p =>
                {
                    //foreach (var entity in BattleShortCut.logicBattle.GetEntityList())
                    //{
                    //    foreach (var passive in entity.GetPassiveSkillList())
                    //    {
                    //        int cd = passive.GetLeftCoolTime();
                    //        if (cd > 0)
                    //        {
                    //            Debug.LogError(string.Format("Passive:{0},{1}, {2}", entity.rid, passive.slotId, cd));
                    //        }
                    //    }
                    //}
                });
                AddButton("重放", new[] { EGameState.Battle }, p =>
                {
                });
                AddButton("检查\n同步", new[] { EGameState.Battle }, p =>
                {
                    foreach (var kv in EntityViewManager.instance.GetEntityViewMap())
                    {
                        kv.Value.CheckSync();
                    }
                });
                AddButton("赢", new[] { EGameState.Battle }, p =>
                {
                    FrameCommandSendUtility.SendGm(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, (int)GmId.Win, new List<int>());
                });
                AddButton("输", new[] { EGameState.Battle }, p =>
                {
                    FrameCommandSendUtility.SendGm(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, (int)GmId.Lose, new List<int>());
                });
                AddButton("充能", new[] { EGameState.Battle }, p =>
                {
                    FrameCommandSendUtility.SendGm(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, (int)GmId.MaxTeamEnergy, new List<int>());
                });

                AddButton("单步AI", new[] { EGameState.Battle }, p =>
                {
                    BattleShortCut.logicBattle.decisionComponent.MakeTeamDecision(BattleShortCut.logicBattle.GetTeamByIndex(BattleShortCut.logicBattle.GetCurTeamIndex()).uid);
                });
                AddToggle("战报", new[] { EGameState.Battle }, false, (flag, p) =>
                {
                    if (flag)
                    {
                        m_battleReportRoot.Show();
                    }
                    else
                    {
                        m_battleReportRoot.SetActive(false);
                    }
                });
            }

            //World
            {
                AddToggle("摇杆\nDebug", new[] { EGameState.World }, false, (flag, p) =>
                {
                    EventManager.instance.Broadcast(EventID.WorldJoystickDebugStateChange, flag);
                });
                AddButton("AddItems", new[] { EGameState.World }, (p) =>
                {
                    GamePlayerContext.instance.BagModule.AddAllItem4GM(5);
                    TipUI.ShowTip("Add Items *5");
                });

                AddButton("ItemToast", new[] { EGameState.World }, (p) =>
                {
                    EventManager.instance.Broadcast(EventID.WorldUI_ShowToastReward, 77777);

                    GamePlayerContext.instance.BagModule.AddItem(10001, 1);
                    GamePlayerContext.instance.BagModule.AddItem(10002, 2);
                    GamePlayerContext.instance.BagModule.AddItem(10003, 3);
                    GamePlayerContext.instance.BagModule.AddItem(10004, 4);
                    GamePlayerContext.instance.BagModule.AddItem(10005, 5);
                    GamePlayerContext.instance.BagModule.AddItem(10011, 11);
                    GamePlayerContext.instance.BagModule.AddItem(10012, 12);
                    GamePlayerContext.instance.BagModule.AddItem(10013, 13);
                    GamePlayerContext.instance.BagModule.AddItem(10015, 15);

                    //GamePlayerContext.instance.RewardModule.ClearItem();
                    //GamePlayerContext.instance.RewardModule.AddItem(10001, 2, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10002, 3, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10003, 7, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10001, 2, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10002, 3, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10003, 7, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10001, 2, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10002, 3, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10003, 7, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10012, 2, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10013, 3, false);
                    //GamePlayerContext.instance.RewardModule.AddItem(10015, 7, false);
                    //List<RewardItem> listRewardItem = GamePlayerContext.instance.RewardModule.GetItems();
                });
                //if (ToolButton(ref x, y, 2f, "����\n�Ի�"))
                //{
                //    if (Int32.TryParse(m_enterInputStr, out Int32 dialogId))
                //    {
                //        DialogConfigData dialog = ConfigDataManager.instance.GetDialog(dialogId);
                //        if (dialog != null)
                //        {
                //            DialogueUI.ShowDialogue(dialogId, null, null);
                //        }
                //        else
                //        {
                //            TipUI.ShowTip("��������ȷ�ĶԻ�ID");
                //        }
                //    }
                //}
            }
        }

        private void OnTimeScaleToggleChanged(bool flag, object obj)
        {
            int index = (int)obj;
            TimeScaleManager.instance.SetStaticTimeScale(TimeScaleId.GM, m_timeScales[index]);
            for (int i = 0; i < m_timeScaleToggleList.Count; ++i)
            {
                var toggle = m_timeScaleToggleList[i];
                if (i != index)
                {
                    toggle.SetToggle(false, true);
                }
            }
        }

        public void AddLog(string str)
        {
#if UNITY_EDITOR
            m_sb.AppendLine(str);
            m_logText.text = m_sb.ToString();
#endif
        }

        public void AnalyzeBattleReport(BattleActionProcedureResult result)
        {
            m_battleReportRoot.AnalyzeProcedure(result);
        }

        #region Base
        ////////------ Base Begin ------////////

        private bool m_isRootOn;
        private string m_rootSwitchPrefKey = "Debug.RootSwitch";
        private List<DebugButtonUIView> m_buttonViewList = new List<DebugButtonUIView>();
        private List<DebugToggleUIView> m_toggleViewList = new List<DebugToggleUIView>();

        protected override void OnInit()
        {
            base.OnInit();
            m_isRootOn = PlayerPrefs.GetInt(m_rootSwitchPrefKey, 0) == 1;
            WwiseExtendManager.Instance.AddButtonOnClickEventListener(m_debugButton);
            m_debugButton.onClick.AddListener(OnDebugButtonClicked);
            HandleDebugButton();
            m_buttonPool.Init(OnButtonInit);
            m_togglePool.Init(OnToggleInit);
            InitDebugView();
            RefreshObjs();
            GameManager.instance.stateMachine.eventOnStateIndexChanged += OnStateIndexChanged;
        }

        protected override void OnUnInit()
        {
            GameManager.instance.stateMachine.eventOnStateIndexChanged -= OnStateIndexChanged;
            base.OnUnInit();
        }

        private void OnButtonInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<DebugButtonUIView>();
            view.Init(this);
        }

        private void OnToggleInit(GameObjectPool.PoolObjHolder holder)
        {
            var view = holder.AddComponent<DebugToggleUIView>();
            view.Init(this);
        }

        private void OnDebugButtonClicked()
        {
            m_isRootOn = !m_isRootOn;
            PlayerPrefs.SetInt(m_rootSwitchPrefKey, m_isRootOn ? 1 : 0);
            HandleDebugButton();
        }

        private void HandleDebugButton()
        {
            m_root.SetActive(m_isRootOn);
            SetToggle(m_debugButton.transform, m_isRootOn);
        }

        private void SetToggle(Transform trans, bool flag)
        {
            trans.Find("Text").GetComponent<Text>().color = flag ? Color.black : Color.white;
            trans.GetComponent<Image>().color = flag ? Color.white : Color.white / 2f;
        }

        private DebugButtonUIView AddButton(string content, EGameState[] states, Action<object> actionOnClicked)
        {
            var view = m_buttonPool.FetchComponent<DebugButtonUIView>();
            view.SetContent(content);
            view.SetGameStates(states);
            view.param = null;
            view.actionOnClicked = actionOnClicked;
            m_buttonViewList.Add(view);
            return view;
        }

        private DebugToggleUIView AddToggle(string content, EGameState[] states, bool initFlag, Action<bool, object> actionOnValueChanged)
        {
            var view = m_togglePool.FetchComponent<DebugToggleUIView>();
            view.SetContent(content);
            view.SetGameStates(states);
            view.param = null;
            view.actionOnValueChanged = actionOnValueChanged;
            view.SetToggle(initFlag, true);
            m_toggleViewList.Add(view);
            return view;
        }

        private DebugButtonUIView AddButton(string content, object param, EGameState[] states, Action<object> actionOnClicked)
        {
            var view = m_buttonPool.FetchComponent<DebugButtonUIView>();
            view.SetContent(content);
            view.SetGameStates(states);
            view.param = param;
            view.actionOnClicked = actionOnClicked;
            m_buttonViewList.Add(view);
            return view;
        }

        private DebugToggleUIView AddToggle(string content, object param, EGameState[] states, bool initFlag, Action<bool, object> actionOnValueChanged)
        {
            var view = m_togglePool.FetchComponent<DebugToggleUIView>();
            view.SetContent(content);
            view.SetGameStates(states);
            view.param = param;
            view.actionOnValueChanged = actionOnValueChanged;
            view.SetToggle(initFlag, true);
            m_toggleViewList.Add(view);
            return view;
        }

        private void OnStateIndexChanged(int index)
        {
            RefreshObjs();
        }

        private void RefreshObjs()
        {
            var gameStateIndex = GameManager.instance.stateMachine.curStateIndex;
            foreach(var view in m_buttonViewList)
            {
                if (view.gameStateIndexList.Count == 0)
                {
                    view.SetActive(true);
                }
                else
                {
                    view.SetActiveSafely(view.gameStateIndexList.Contains(gameStateIndex));
                }
            }
            foreach (var view in m_toggleViewList)
            {
                if (view.gameStateIndexList.Count == 0)
                {
                    view.SetActive(true);
                }
                else
                {
                    view.SetActiveSafely(view.gameStateIndexList.Contains(gameStateIndex));
                }
            }
        }
        ////////------ Base End ------////////
        #endregion Base

        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObject m_root;
        private Button m_debugButton;
        private GameObjectPool m_buttonPool;
        private GameObjectPool m_togglePool;
        private Text m_logText;
        private DebugBattleReportUIView m_battleReportRoot;
        private Text m_packageVersionText;
        
        public DebugBattleReportUIView battleReportRoot
        {
            get { return m_battleReportRoot; }
        }
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
            AddBindComponent(group.GetItemInfoByName("BattleReportRoot"), typeof(DebugBattleReportUIView));
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_root = UIBindUtility.GetBindObject(group.GetItemInfoByName("Root"));
            m_debugButton = UIBindUtility.GetBindComponent(group.GetItemInfoByName("DebugButton"), typeof(Button)) as Button;
            m_buttonPool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("ButtonPool"), typeof(GameObjectPool)) as GameObjectPool;
            m_togglePool = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TogglePool"), typeof(GameObjectPool)) as GameObjectPool;
            m_logText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("LogText"), typeof(Text)) as Text;
            m_battleReportRoot = UIBindUtility.GetBindComponent(group.GetItemInfoByName("BattleReportRoot"), typeof(DebugBattleReportUIView)) as DebugBattleReportUIView;
            m_packageVersionText = UIBindUtility.GetBindComponent(group.GetItemInfoByName("PackageVersionText"), typeof(Text)) as Text;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
