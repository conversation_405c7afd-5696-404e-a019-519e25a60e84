using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.GameLogic.UI
{
    public class LoadingContext : ClassPoolObj
    {
        public LoadingFlagId flagId;
        public bool isInvoked;
        public Action actionOnEnd;

        public override void OnRelease()
        {
            flagId = default;
            actionOnEnd = null;
            isInvoked = false;
            base.OnRelease();
        }
    }
}
