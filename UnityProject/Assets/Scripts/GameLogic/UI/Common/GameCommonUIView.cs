using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class GameCommonUIView : UIView
    {
        #region AutoGen
        ////////------ <PERSON><PERSON><PERSON> Begin ------////////
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
        }
        ////////------ <PERSON>Gen End ------////////
        #endregion AutoGen
    }
}
