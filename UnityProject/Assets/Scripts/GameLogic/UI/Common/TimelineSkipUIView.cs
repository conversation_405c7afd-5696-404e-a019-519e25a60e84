using System;
using UnityEngine;
using UnityEngine.UI;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.UI
{
    public class TimelineSkipUIView : UIView
    {
        private Int32 m_timelineId;
        private Int32 m_messageBoxId;

        protected override void OnInit()
        {
            ShowOrHideTimelineSkipUI(false);
            m_timelineSkipBtn.onClick.AddListener(OnTimelineSkipBtnClick);
            EventManager.instance.RegisterListener<Int32>(EventID.Timeline_Start, OnTimelineStart);
            EventManager.instance.RegisterListener<Int32>(EventID.Timeline_Stop, OnTimelineStop);
        }

        protected override void OnUnInit()
        {
            EventManager.instance.UnRegisterListener<Int32>(EventID.Timeline_Start, OnTimelineStart);
            EventManager.instance.UnRegisterListener<Int32>(EventID.Timeline_Stop, OnTimelineStop);

        }



        private void OnTimelineStart(Int32 timelineId)
        {
            m_timelineId = timelineId;
            TimelineInfoConfigData timelineInfo = ConfigDataManager.instance.GetTimelineInfo(m_timelineId);
            if (timelineInfo != null && timelineInfo.EnableSkip)
            {
                ShowOrHideTimelineSkipUI(true);
            }

        }

        private void OnTimelineStop(Int32 timelineId)
        {
            m_timelineId = -1;
            ShowOrHideTimelineSkipUI(false);
            if (m_messageBoxId != 0)
            {
                MessageBox.Close(m_messageBoxId);
                m_messageBoxId = 0;
            }
        }


        private void OnTimelineSkipBtnClick()
        {
            TimelineInfoConfigData timelineInfo = ConfigDataManager.instance.GetTimelineInfo(m_timelineId);
            if (timelineInfo != null)
            {
                String title = ConstStringUtility.GetConstString(ConstStringId.SkipTimelineStoryTitleTip);
                m_messageBoxId = MessageBox.Show(timelineInfo.SkipDesc, title, SkipTimelineInternal);
            }
        }

        private void SkipTimelineInternal()
        {
            m_messageBoxId = 0;
            ShowOrHideTimelineSkipUI(false);
            EventManager.instance.Broadcast(EventID.Timeline_Skip);
        }


        private void ShowOrHideTimelineSkipUI(Boolean isShow)
        {
            m_timelineSkipUI.SetActiveSafely(isShow);
        }


        #region AutoGen
        ////////------ AutoGen Begin ------////////
        private GameObject m_timelineSkipUI;
        private Button m_timelineSkipBtn;
        
        
        protected sealed override void OnBindSubGroupsByBindGroup(UIBindGroup group)
        {
            base.OnBindSubGroupsByBindGroup(group);
        }
        
        protected sealed override void OnBindViewsByBindGroup(UIBindGroup group)
        {
            base.OnBindViewsByBindGroup(group);
            m_timelineSkipUI = UIBindUtility.GetBindObject(group.GetItemInfoByName("TimelineSkipUI"));
            m_timelineSkipBtn = UIBindUtility.GetBindComponent(group.GetItemInfoByName("TimelineSkipBtn"), typeof(Button)) as Button;
        }
        ////////------ AutoGen End ------////////
        #endregion AutoGen
    }
}
