using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.GameLogic
{
    public class TimeScaleManager : Singleton<TimeScaleManager>
    {
        protected override void OnInit()
        {
            m_staticMap.Init(OnTimeScaleChanged);
            m_dynamicMap.Init(OnTimeScaleChanged);
            m_overrideStack.Init((int)OverrideTimeScaleStackId.Base, -1f, OnOverrideStackChanged);
            OnTimeScaleChanged();
            m_staticTimerMap.onTimeUp = OnStaticTimeUp;
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        private void OnTimeScaleChanged()
        {
            float timeScale = 1f;
            if (m_overrideStack.IsTop((int)OverrideTimeScaleStackId.Base))
            {
                timeScale *= m_staticMap.timeScale;
                timeScale *= m_dynamicMap.timeScale;
            }
            else
            {
                timeScale = m_overrideStack.topValue;
            }
            m_timeScale = timeScale;
            Time.timeScale = timeScale;
        }

        private void OnOverrideStackChanged(PriorityStack<float> prioritystack)
        {
            OnTimeScaleChanged();
        }

        private void OnStaticTimeUp(int id)
        {
            m_staticMap.ResetTimeScale(id);
        }

        private void OnTick(TimeSlice timeSlice)
        {
            m_staticTimerMap.Tick(timeSlice);
        }

        public void SetStaticTimeScaleDuration(TimeScaleId id, float value, float time)
        {
            m_staticTimerMap.Start((int)id, time);
            SetStaticTimeScale(id, value);
        }

        public void SetStaticTimeScale(TimeScaleId id, float value)
        {
            m_staticMap.SetTimeScale((int)id, value);
        }

        public void ResetStaticTimeScale(TimeScaleId id)
        {
            m_staticMap.ResetTimeScale((int)id);
        }

        public void SetOverrideTimeScale(OverrideTimeScaleStackId id, float value)
        {
            m_overrideStack.AddItem((int)id, value);
        }

        public void ResetOverrideTimeScale(OverrideTimeScaleStackId id)
        {
            m_overrideStack.Remove((int)id);
        }

        public void ClearOverrideTimeScale()
        {
            m_overrideStack.RemoveAll();
        }

        public int SetDynamicTimeScale(float value)
        {
            if (m_dynamicIdCursor < 0)
            {
                m_dynamicIdCursor = 0;
            }
            m_dynamicIdCursor++;
            m_dynamicMap.SetTimeScale(m_dynamicIdCursor, value);
            return m_dynamicIdCursor;
        }

        public void ResetDynamicTimeScale(int id)
        {
            m_dynamicMap.ResetTimeScale(id);
        }

        private TimeScaleItemMap m_staticMap = new TimeScaleItemMap();
        private TimeScaleItemMap m_dynamicMap = new TimeScaleItemMap();
        private PriorityStack<float> m_overrideStack = new PriorityStack<float>();
        private int m_dynamicIdCursor = 0;
        private TimerMap m_staticTimerMap = new TimerMap();
        private float m_timeScale = 1;

        private class TimeScaleItemMap
        {
            private List<TimeScaleInfo> m_list = new List<TimeScaleInfo>();
            private float m_timeScale;
            private Action m_actionOnTimeScaleChanged;

            public float timeScale
            {
                get { return m_timeScale; }
            }

            public void Init(Action actionOnTimeScaleChanged)
            {
                m_actionOnTimeScaleChanged = actionOnTimeScaleChanged;
                UpdateTimeScale();
            }

            public void Clear()
            {
                m_list.Clear();
                UpdateTimeScale();
                InvokeActionOnTimeScaleChanged();
            }

            public void SetTimeScale(int id, float timeScale)
            {
                int index = IndexOf(id);
                if (index < 0)
                {
                    if (!CheckSame(timeScale, 1f))
                    {
                        m_list.Add(new TimeScaleInfo(id, timeScale));
                        UpdateTimeScale();
                        InvokeActionOnTimeScaleChanged();
                    }
                }
                else
                {
                    if (!CheckSame(timeScale, m_list[index].timeScale))
                    {
                        m_list[index] = new TimeScaleInfo(id, timeScale);
                        UpdateTimeScale();
                        InvokeActionOnTimeScaleChanged();
                    }
                }
            }

            public void ResetTimeScale(int id)
            {
                int index = IndexOf(id);
                if (index >= 0)
                {
                    m_list.RemoveAt(index);
                    UpdateTimeScale();
                    InvokeActionOnTimeScaleChanged();
                }
            }

            private bool CheckSame(float a, float b)
            {
                return Mathf.Abs(a - b) < 1e-4;
            }

            private int IndexOf(int id)
            {
                for (int i = 0; i < m_list.Count; ++i)
                {
                    if (m_list[i].id == id)
                    {
                        return i;
                    }
                }
                return -1;
            }

            private void UpdateTimeScale()
            {
                m_timeScale = 1f;
                for (int i = 0; i < m_list.Count; ++i)
                {
                    m_timeScale *= m_list[i].timeScale;
                }
            }

            private void InvokeActionOnTimeScaleChanged()
            {
                if (m_actionOnTimeScaleChanged != null)
                {
                    m_actionOnTimeScaleChanged();
                }
            }

            private struct TimeScaleInfo
            {
                public int id;
                public float timeScale;

                public TimeScaleInfo(int id, float timeScale)
                {
                    this.id = id;
                    this.timeScale = timeScale;
                }

                public void SetTimeScale(float timeScale)
                {
                    this.timeScale = timeScale;
                }
            }
        }
    }

    public enum TimeScaleId
    {
        GM,
        BattleSpeed,
        EngageSpeed,
    }

    public enum OverrideTimeScaleStackId
    {
        Base,
    }
}
