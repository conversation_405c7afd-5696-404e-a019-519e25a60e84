using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public static class ConstStringUtility
    {
        public static string GetConstString(ConstStringId id)
        {
            ConstStringConfigData config = ConfigDataManager.instance.GetConstString(id);
            if(config != null)
            {
                return config.String;
            }
            return string.Empty;
        }

        public static string GetConstString(ConstStringId id, params string[] prms)
        {
            string str = GetConstString(id);
            if (!string.IsNullOrEmpty(str))
            {
                return string.Format(str, prms);
            }
            return str;
        }
    }
}
