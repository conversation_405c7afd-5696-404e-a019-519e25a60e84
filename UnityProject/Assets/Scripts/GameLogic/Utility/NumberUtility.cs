using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public static class NumberUtility
    {
        public static string GetSimplified(int num)
        {
            NumberConfigData config = ConfigDataManager.instance.GetNumber(num);
            if(config != null)
            {
                return config.Simplified;
            }
            return num.ToString();
        }
    }
}
