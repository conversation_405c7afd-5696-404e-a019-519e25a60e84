using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic
{
    public static class LanguageUtility
    {
        public static string GetConstString(ConstStringId id)
        {
            ConstStringConfigData config = ConfigDataManager.instance.GetConstString(id);
            if (config != null)
            {
                return config.String;
            }
            return string.Empty;
        }

        public static string GetConstString(ConstStringId id, params string[] prms)
        {
            string str = GetConstString(id);
            if (!string.IsNullOrEmpty(str))
            {
                return string.Format(str, prms);
            }
            return str;
        }

        public static string GetExploreString(ExploreStringId id)
        {
            ExploreStringConfigData config = ConfigDataManager.instance.GetExploreString(id);
            if (config != null)
            {
                return config.String;
            }
            return string.Empty;
        }

        public static string GetExploreString(ExploreStringId id, params string[] prms)
        {
            string str = GetExploreString(id);
            if (!string.IsNullOrEmpty(str))
            {
                return string.Format(str, prms);
            }
            return str;
        }
    }
}
