


using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;
using Phoenix.Hakoniwa.Logic;

namespace Phoenix.Hakoniwa
{

    public static class StaticHakoniwa
    {
        public static HakoniwaView hakoniwaView { get; set; }
        public static HakoniwaBase hakoniwaBase { get; set; }
        public static HakoMainPlayerHandler mainPlayerHandler { get { return hakoniwaView?.GetHakoniwaHandler<HakoMainPlayerHandler>(); } }

        public static Boolean IsHakoSceneBattle { get; set; }

        public static Boolean PreLoadCombatScene { get; set; } = false;





        /// <summary> 进入箱庭 </summary>
        public static void EnterHakoniwa(Int32 hakoniwaId, Int32 waypointId, HakoLocation location)
        {
            DebugUtility.Log($"[EnterWorld] HakoniwaEnter --> {hakoniwaId}");

            if (StaticHakoniwa.IsHakoSceneBattle)
            {
                EnterHakoniwaInternal(hakoniwaId, waypointId, location);
            }
            else
            {
                LoadingUIContext context = new LoadingUIContext();
                context.viewType = LoadingViewType.Normal;
                context.waitFlagIdList.Add(LoadingFlagId.HakoniwaViewInitializer);
                Loading.Open(LoadingFlagId.HakoniwaEnter, context, () =>
                {
                    EnterHakoniwaInternal(hakoniwaId, waypointId, location);
                    Loading.Close(LoadingFlagId.HakoniwaEnter);
                });
            }
        }

        private static void EnterHakoniwaInternal(Int32 hakoniwaId, Int32 waypointId, HakoLocation location)
        {
            var ctx = ClassPoolManager.instance.Fetch<GameStateContextHakoniwa>();
            ctx.m_hakoniwaId = hakoniwaId;
            ctx.m_waypointId = waypointId;
            ctx.m_hakoLocation = location;
            GameManager.instance.stateMachine.ChangeState((int)EGameState.Hakoniwa, ctx);
        }


        /// <summary> 箱庭【开怪】----进入战斗 </summary>
        public static void BattleWithHakoniwaMonster(Int32 monsterUid, Int32 hpRate = 10000)
        {
            SaveHakoniwaData();
            HakoniwaMonsterConfigData monsterConfig = ConfigDataManager.instance.GetHakoniwaMonster(monsterUid);
            if (monsterConfig == null)
            {
                UnityEngine.Debug.LogError($"Hakoniwa 找不到怪物({monsterUid})");
                return;
            }
            Int32 hakoniwaLevelId = 0;
            HashSet<Int32> connectionMonsterIds = new HashSet<Int32>();
            if (monsterConfig.HateGroup.Count == 0)
            {
                hakoniwaLevelId = monsterConfig.LevelId;
            }
            else
            {
                HakoMonsterView monsterView = hakoniwaView.GetHakoEntityView<HakoMonsterView>(monsterUid);
                MonsterHateGroupConfigData activeHateGroup = monsterView.GetCurrentHateGroup();
                if (activeHateGroup != null)
                {
                    foreach(var item in hakoniwaView.GetAllHakoniwaEntityView())
                    {
                        if (item.EntityType == HakoniwaEntityType.Monster)
                        {
                            HakoniwaMonsterConfigData tempMonsterConfig = ConfigDataManager.instance.GetHakoniwaMonster(item.EntityUid);
                            if (tempMonsterConfig != null && tempMonsterConfig.HateGroup.Contains(activeHateGroup.Id))
                            {
                                connectionMonsterIds.Add(item.EntityUid);
                            }
                        }
                    }
                    hakoniwaLevelId = activeHateGroup.LevelId;
                }
            }

            HakoniwaLevelConfigData hakoniwaLevelConfig = ConfigDataManager.instance.GetHakoniwaLevel(hakoniwaLevelId);
            if (hakoniwaLevelConfig == null)
            {
                UnityEngine.Debug.LogError($"Hakoniwa 找不到怪物({monsterUid})对应的箱庭战斗关卡ID({hakoniwaLevelId})");
                return;
            }

            StaticHakoniwa.IsHakoSceneBattle = hakoniwaLevelConfig.IsHakoSceneBattle;
            GamePlayerContext.instance.BattleModule.SetBattleParam(hakoniwaLevelConfig.BattleId, BattleType.Hakoniwa, hakoniwaLevelId, hakoniwaBase.hakoniwaConfig.Id);

            // 保存当前所有参战Monter
            StaticHakoniwaInterface.SetHakoniwaTempMonsterIds(connectionMonsterIds);

            // 主角初始位置计算
            GridPosition gridPosition = hakoniwaView.GetEntityGridPosition(hakoniwaView.GetHakoPlayerView());
            // 怪物仇恨联动计算
            List<InitActorInfo> initActors = new List<InitActorInfo>();
            connectionMonsterIds.Remove(monsterUid);

            if(hpRate > 0)
            {
                InitActorInfo monster = new InitActorInfo(monsterUid);
                monster.m_hpRate = hpRate;
                monster.m_grid = hakoniwaView.GetEntityGridPosition(hakoniwaView.GetHakoEntityView(monsterUid));
                initActors.Add(monster);
            }

            foreach (Int32 uid in connectionMonsterIds)
            {
                InitActorInfo initActor = new InitActorInfo(uid);
                initActor.m_grid = hakoniwaView.GetEntityGridPosition(hakoniwaView.GetHakoEntityView(uid));
                initActors.Add(initActor);
            }

            GameStateContextHakoniwaBattle context = ClassPoolManager.instance.Fetch<GameStateContextHakoniwaBattle>();
            context.battleId = hakoniwaLevelConfig.BattleId;
            context.playerInitPosition = gridPosition;
            context.actors.AddRange(hakoniwaBase.hakoniwaConfig.Actors);
            context.enemys.AddRange(initActors);

            BattleLaunchUtility.StartHakoniwaBattle(context);
        }



        public static void SaveHakoniwaData()
        {
            Int32 hakoniwaId = hakoniwaBase.hakoniwaConfig.Id;
            Int32 waypointId = hakoniwaBase.waypointConfig.Id;
            Int32 sceneId = hakoniwaBase.sceneConfig.Id;
            HakoLocation location = hakoniwaView.GetMainPlayerLocation();
            StaticHakoniwaInterface.SetHakoniwaData(hakoniwaId, waypointId, sceneId, location);
        }

        public static void SaveHakoniwaTempMonsterData(ICollection<Int32> enemys)
        {
        }
    }
}

