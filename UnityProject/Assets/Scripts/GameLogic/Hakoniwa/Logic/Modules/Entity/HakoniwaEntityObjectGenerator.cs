using System;
using Phoenix.ConfigData;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// Entity辅助工具类
    /// 提供便捷的Entity创建和Uid管理方法
    /// </summary>
    public static class HakoniwaEntityObjectGenerator
    {
        #region Entity Creation Helpers

        /// <summary>
        /// 创建一个新的HakoniwaEntity并自动分配Uid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="skinId">皮肤ID</param>
        /// <param name="position">位置</param>
        /// <param name="defaultAnimation">默认动画名称</param>
        /// <returns>创建的HakoniwaEntity实例</returns>
        public static HakoniwaEntityObject Gen(HakoniwaEntityType entityType, Int32 skinId, HakoLocation position)
        {
            Int32 uid = IdGeneratorCore.GenerateId(IdType.EntityUid);
            return Gen(entityType, uid, skinId, position);
        }
        public static HakoniwaEntityObject Gen(HakoniwaEntityConfigData hakoEntityConfig)
        {
            Int32 uid = hakoEntityConfig.Id;
            HakoniwaEntityType entityType = hakoEntityConfig.EntityType;
            Int32 skinId = hakoEntityConfig.SkinId;
            HakoLocation hakoLocation = hakoEntityConfig.Location;
            HakoniwaEntityObject entity = Gen(entityType, uid, skinId, hakoLocation);
            entity.Name = hakoEntityConfig.Name;
            //entity.Rotation = new Vector3(0, hakoEntityConfig.Direction, 0);
            foreach (Int32 id in hakoEntityConfig.Interactions)
            {
                HakoniwaEntityInteractionConfigData config = ConfigDataManager.instance.GetHakoniwaEntityInteraction(id);
                EntityInteraction interaction = HakoEntityInteractionGenerator.Gen(config);
                entity.EntityInteractionInfoAdd(interaction);
            }

            return entity;
        }


        public static HakoniwaEntityObject Gen(HakoniwaEntityType entityType, Int32 uid, Int32 skinId, HakoLocation position, String defaultAnimation = "NormalIdle")
        {
            HakoniwaEntityObject hakoniwaEntity = new HakoniwaEntityObject();//ClassPoolManager.instance.Fetch<HakoniwaEntity>();
            hakoniwaEntity.Uid = uid;
            hakoniwaEntity.SkinId = skinId;
            hakoniwaEntity.EntityType = entityType;
            hakoniwaEntity.Position = position;
            hakoniwaEntity.DefaultAnimation = defaultAnimation;
            hakoniwaEntity.PreInit();
            return hakoniwaEntity;
        }

        #endregion

        #region Uid Management Helpers

        /// <summary>
        /// 为现有Entity分配新的Uid
        /// </summary>
        /// <param name="entity">要分配Uid的Entity</param>
        /// <param name="useTypeBasedGeneration">是否使用基于类型的生成策略</param>
        /// <returns>分配的新Uid</returns>
        public static Int32 AssignNewUid(HakoniwaEntityObject entity)
        {
            if (entity == null)
            {
                Debug.LogError("Entity不能为null");
                return 0;
            }

            // 如果Entity已有Uid，先释放它
            if (entity.Uid > 0)
            {
                EntityUidGenerator.ReleaseUid(entity.Uid);
            }

            // 生成新的Uid
            Int32 newUid = EntityUidGenerator.GenerateUid();

            entity.Uid = newUid;
            return newUid;
        }

        /// <summary>
        /// 验证Entity的Uid是否有效且唯一
        /// </summary>
        /// <param name="entity">要验证的Entity</param>
        /// <returns>验证结果</returns>
        public static UidValidationResult ValidateEntityUid(HakoniwaEntityObject entity)
        {
            if (entity == null)
            {
                return new UidValidationResult(false, "Entity不能为null");
            }

            if (entity.Uid <= 0)
            {
                return new UidValidationResult(false, "Uid必须大于0");
            }

            if (EntityUidGenerator.IsUidUsed(entity.Uid))
            {
                return new UidValidationResult(false, $"Uid {entity.Uid} 已被使用");
            }

            return new UidValidationResult(true, "Uid有效");
        }

        /// <summary>
        /// 批量注册已存在的Entity Uid（用于加载存档数据）
        /// </summary>
        /// <param name="entities">Entity数组</param>
        /// <returns>成功注册的数量</returns>
        public static Int32 RegisterExistingEntities(HakoniwaEntityObject[] entities)
        {
            if (entities == null || entities.Length == 0)
            {
                return 0;
            }

            Int32 successCount = 0;
            foreach (var entity in entities)
            {
                if (entity != null && entity.Uid > 0)
                {
                    if (EntityUidGenerator.RegisterUsedUid(entity.Uid))
                    {
                        successCount++;
                    }
                    else
                    {
                        Debug.LogWarning($"Entity Uid {entity.Uid} 注册失败，可能已存在重复");
                    }
                }
            }

            Debug.Log($"批量注册Entity Uid完成，成功注册 {successCount}/{entities.Length} 个");
            return successCount;
        }

        /// <summary>
        /// 清理Entity的Uid（当Entity被销毁时调用）
        /// </summary>
        /// <param name="entity">要清理的Entity</param>
        /// <returns>清理是否成功</returns>
        public static bool CleanupEntityUid(HakoniwaEntityObject entity)
        {
            if (entity == null || entity.Uid <= 0)
            {
                return false;
            }

            bool success = EntityUidGenerator.ReleaseUid(entity.Uid);
            if (success)
            {
                entity.Uid = 0; // 重置Entity的Uid
            }

            return success;
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// 获取Entity类型的统计信息
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <returns>统计信息</returns>
        public static EntityTypeStats GetEntityTypeStats(HakoniwaEntityType entityType)
        {
            Int32 availableCount = EntityUidGenerator.GetAvailableUidCount();
            Int32 totalUsedCount = EntityUidGenerator.GetUsedUidCount();

            return new EntityTypeStats
            {
                EntityType = entityType,
                AvailableUidCount = availableCount,
                TotalUsedUidCount = totalUsedCount
            };
        }

        /// <summary>
        /// 检查是否可以创建指定类型的Entity
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="requiredCount">需要创建的数量</param>
        /// <returns>是否可以创建</returns>
        public static bool CanCreateEntities(Int32 requiredCount = 1)
        {
            Int32 availableCount = EntityUidGenerator.GetAvailableUidCount();
            return availableCount >= requiredCount;
        }

        /// <summary>
        /// 预分配指定数量的Uid（用于批量创建Entity）
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="count">需要预分配的数量</param>
        /// <returns>预分配的Uid数组</returns>
        public static Int32[] PreAllocateUids(Int32 count)
        {
            if (count <= 0)
            {
                return new Int32[0];
            }

            if (!CanCreateEntities(count))
            {
                Debug.LogError($"无法为Entity预分配 {count} 个Uid，可用数量不足");
                return new Int32[0];
            }

            Int32[] uids = new Int32[count];
            for (Int32 i = 0; i < count; i++)
            {
                uids[i] = EntityUidGenerator.GenerateUid();
            }

            return uids;
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// Uid验证结果
        /// </summary>
        public class UidValidationResult
        {
            public bool IsValid { get; }
            public string Message { get; }

            public UidValidationResult(bool isValid, string message)
            {
                IsValid = isValid;
                Message = message;
            }
        }

        /// <summary>
        /// Entity类型统计信息
        /// </summary>
        public class EntityTypeStats
        {
            public HakoniwaEntityType EntityType { get; set; }
            public Int32 AvailableUidCount { get; set; }
            public Int32 TotalUsedUidCount { get; set; }

            public override string ToString()
            {
                return $"EntityType: {EntityType}, Available: {AvailableUidCount}, TotalUsed: {TotalUsedUidCount}";
            }
        }

        #endregion
    }


    /// <summary>
    /// EntityInteraction辅助工具类
    /// </summary>
    public static class HakoEntityInteractionGenerator
    {

        #region EntityInteraction Creation Helpers

        public static EntityInteraction Gen(EntityInteractionType type, String name, Int32 p1)
        {
            Int32 uid = IdGeneratorCore.GenerateId(IdType.InteractionUid);
            return CreateEntityInteraction(uid, name, type, p1, 0);
        }

        public static EntityInteraction Gen(EntityInteractionType type, Int32 p1)
        {
            Int32 uid = IdGeneratorCore.GenerateId(IdType.InteractionUid);
            return CreateEntityInteraction(uid, String.Empty, type, p1, 0);
        }

        public static EntityInteraction Gen(HakoniwaEntityInteractionConfigData config)
        {
            return CreateEntityInteraction(config.Id, config.Name, config.InteractionType, config.P1, config.P2);
        }

        public static EntityInteraction Gen(EntityInteraction interaction)
        {
            Int32 uid = IdGeneratorCore.GenerateId(IdType.InteractionUid);
            return CreateEntityInteraction(uid, interaction.Name, interaction.Type, interaction.P1, interaction.P2);
        }

        public static EntityInteraction CreateEntityInteraction(Int32 uid, String name, EntityInteractionType type, Int32 p1, Int32 p2)
        {
            EntityInteraction entityInteraction = new EntityInteraction();//ClassPoolManager.instance.Fetch<EntityInteraction>();
            entityInteraction.Uid = uid;
            entityInteraction.Name = name;
            entityInteraction.Type = type;
            entityInteraction.P1 = p1;
            entityInteraction.P2 = p2;
            return entityInteraction;
        }

        #endregion


    }
}
