# EntityUid自动生成工具

这是一个为Hakoniwa项目设计的EntityUid自动生成工具，提供多种ID生成策略，确保EntityUid的唯一性和线程安全性。

## 功能特性

### 🎯 核心功能
- **多种生成策略**: 递增ID、基于类型的ID、时间戳ID、GUID ID
- **线程安全**: 支持多线程环境下的安全ID生成
- **类型范围管理**: 为不同Entity类型分配不同的ID范围
- **重复检测**: 自动避免ID重复
- **内存管理**: 支持ID的注册、释放和重用

### 🛠️ 辅助工具
- **便捷创建**: 提供Entity快速创建方法
- **批量操作**: 支持批量ID预分配和Entity注册
- **验证功能**: ID有效性验证和统计信息
- **编辑器工具**: Unity编辑器可视化管理界面

## 快速开始

### 基础用法

```csharp
using Phoenix.Hakoniwa.Logic;

// 1. 生成递增ID
int uid1 = EntityUidGenerator.GenerateNextUid();

// 2. 根据Entity类型生成ID
int playerUid = EntityUidGenerator.GenerateUidByType(HakoEntityType.Player);
int npcUid = EntityUidGenerator.GenerateUidByType(HakoEntityType.Npc);

// 3. 生成时间戳ID
int timestampUid = EntityUidGenerator.GenerateTimestampUid();

// 4. 生成GUID ID
int guidUid = EntityUidGenerator.GenerateGuidUid();
```

### Entity创建

```csharp
// 使用辅助类快速创建Entity
var player = EntityUidHelper.CreateEntity(
    HakoEntityType.Player, 
    skinId: 1001, 
    position: Vector3.zero, 
    defaultAnimation: "Idle"
);

// 使用不同的ID生成策略
var npc = EntityUidHelper.CreateEntityWithTimestampUid(
    HakoEntityType.Npc, 
    skinId: 2001
);
```

## 详细使用指南

### 1. ID生成策略

#### 递增ID生成
```csharp
// 生成连续递增的ID: 1, 2, 3, 4...
int uid = EntityUidGenerator.GenerateNextUid();
```

#### 基于类型的ID生成
```csharp
// 不同类型使用不同的ID范围
// Player: 1-999
// NPC: 1000-9999  
// Monster: 10000-99999
// Object: 100000-999999
int playerUid = EntityUidGenerator.GenerateUidByType(HakoEntityType.Player);
```

#### 时间戳ID生成
```csharp
// 基于当前时间戳生成ID，适合分布式环境
int timestampUid = EntityUidGenerator.GenerateTimestampUid();
```

#### GUID ID生成
```csharp
// 基于GUID生成ID，保证全局唯一性
int guidUid = EntityUidGenerator.GenerateGuidUid();
```

### 2. ID管理

#### 检查ID是否已使用
```csharp
bool isUsed = EntityUidGenerator.IsUidUsed(1001);
```

#### 注册已存在的ID
```csharp
// 用于加载存档数据时注册已有ID
bool success = EntityUidGenerator.RegisterUsedUid(1001);
```

#### 释放ID
```csharp
// 当Entity被销毁时释放ID
bool released = EntityUidGenerator.ReleaseUid(1001);
```

### 3. 配置管理

#### 设置递增起始值
```csharp
EntityUidGenerator.SetIncrementalStartValue(1000);
```

#### 设置Entity类型ID范围
```csharp
EntityUidGenerator.SetEntityTypeRange(
    HakoEntityType.Player, 
    minValue: 1, 
    maxValue: 500
);
```

#### 重置生成器
```csharp
EntityUidGenerator.Reset();
```

### 4. 批量操作

#### 预分配ID
```csharp
// 预分配5个NPC ID
int[] npcUids = EntityUidHelper.PreAllocateUids(HakoEntityType.Npc, 5);
```

#### 批量注册Entity
```csharp
HakoniwaEntity[] entities = LoadEntitiesFromSave();
int registeredCount = EntityUidHelper.RegisterExistingEntities(entities);
```

#### 检查可用性
```csharp
bool canCreate = EntityUidHelper.CanCreateEntities(HakoEntityType.Monster, 10);
```

### 5. 验证和统计

#### 验证Entity ID
```csharp
var result = EntityUidHelper.ValidateEntityUid(entity);
if (!result.IsValid)
{
    Debug.LogError(result.Message);
}
```

#### 获取统计信息
```csharp
var stats = EntityUidHelper.GetEntityTypeStats(HakoEntityType.Player);
Debug.Log($"Player可用ID数量: {stats.AvailableUidCount}");
```

## 编辑器工具

在Unity编辑器中，可以通过菜单 `Tools > Hakoniwa > EntityUid Generator` 打开可视化管理界面。

### 功能包括：
- 实时查看ID使用状态
- 可视化生成各种类型的ID
- 快速创建Entity实例
- 配置ID范围和起始值
- 批量操作和测试
- 调试信息输出

## 最佳实践

### 1. 选择合适的生成策略
- **递增ID**: 适合单机游戏，简单高效
- **类型ID**: 适合需要按类型管理的场景
- **时间戳ID**: 适合分布式或多客户端环境
- **GUID ID**: 适合需要绝对唯一性的场景

### 2. 内存管理
```csharp
// 创建Entity时
var entity = EntityUidHelper.CreateEntity(HakoEntityType.Player);

// 销毁Entity时记得清理ID
EntityUidHelper.CleanupEntityUid(entity);
```

### 3. 加载存档数据
```csharp
// 游戏启动时注册已有Entity的ID
HakoniwaEntity[] savedEntities = LoadFromSave();
EntityUidHelper.RegisterExistingEntities(savedEntities);
```

### 4. 错误处理
```csharp
try
{
    int uid = EntityUidGenerator.GenerateUidByType(HakoEntityType.Player);
}
catch (InvalidOperationException ex)
{
    Debug.LogError($"ID生成失败: {ex.Message}");
    // 使用备用策略
    int fallbackUid = EntityUidGenerator.GenerateNextUid();
}
```

## 性能考虑

- 所有操作都是线程安全的，但会有轻微的性能开销
- 大量ID生成时建议使用批量预分配
- 定期清理不再使用的ID以节省内存
- 避免频繁的重置操作

## 调试和测试

### 调试信息
```csharp
// 打印当前状态
EntityUidGenerator.PrintDebugInfo();

// 获取使用统计
int usedCount = EntityUidGenerator.GetUsedUidCount();
```

### 单元测试
项目包含完整的示例和测试代码：
- `EntityUidGeneratorExample.cs`: 使用示例和测试
- `EntityUidGeneratorEditor.cs`: 编辑器工具

## 注意事项

1. **线程安全**: 所有方法都是线程安全的，可以在多线程环境中使用
2. **内存使用**: 工具会记录所有已使用的ID，大量ID时注意内存使用
3. **ID范围**: 确保不同Entity类型的ID范围不重叠
4. **持久化**: 工具本身不提供持久化功能，需要在游戏保存时处理
5. **重置影响**: 重置操作会清除所有记录，谨慎使用

## 扩展和自定义

如需扩展功能，可以：
1. 添加新的ID生成策略
2. 自定义Entity类型范围配置
3. 实现ID持久化存储
4. 添加更多的验证规则

## 版本历史

- v1.0.0: 初始版本，包含基础ID生成功能
- 支持多种生成策略和线程安全操作
- 提供完整的辅助工具和编辑器界面
