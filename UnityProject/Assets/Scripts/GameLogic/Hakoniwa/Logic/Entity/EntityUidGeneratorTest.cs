using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// EntityUidGenerator简单测试脚本
    /// 用于验证ID生成工具的基本功能
    /// </summary>
    public class EntityUidGeneratorTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool autoRunTest = true;
        [SerializeField] private int testCount = 10;
        
        private void Start()
        {
            if (autoRunTest)
            {
                RunBasicTest();
            }
        }
        
        /// <summary>
        /// 运行基础测试
        /// </summary>
        [ContextMenu("运行基础测试")]
        public void RunBasicTest()
        {
            Debug.Log("=== EntityUidGenerator 基础测试开始 ===");
            
            // 重置生成器
            EntityUidGenerator.Reset();
            
            // 测试递增ID生成
            Debug.Log("--- 测试递增ID生成 ---");
            for (int i = 0; i < 5; i++)
            {
                int uid = EntityUidGenerator.GenerateNextUid();
                Debug.Log($"递增ID {i + 1}: {uid}");
            }
            
            // 测试基于类型的ID生成
            Debug.Log("--- 测试基于类型的ID生成 ---");
            var entityTypes = new[] { HakoEntityType.Player, HakoEntityType.Npc, HakoEntityType.Monster };
            foreach (var entityType in entityTypes)
            {
                int uid = EntityUidGenerator.GenerateUidByType(entityType);
                Debug.Log($"{entityType} ID: {uid}");
            }
            
            // 测试Entity创建
            Debug.Log("--- 测试Entity创建 ---");
            var player = HakoEntityGenerator.CreateEntity(HakoEntityType.Player, 1001, Vector3.zero, "Idle");
            Debug.Log($"创建Player: {player}");
            
            var npc = HakoEntityGenerator.CreateEntity(HakoEntityType.Npc, 2001, Vector3.one, "Stand");
            Debug.Log($"创建NPC: {npc}");
            
            // 测试ID验证
            Debug.Log("--- 测试ID验证 ---");
            var validationResult = HakoEntityGenerator.ValidateEntityUid(player);
            Debug.Log($"Player ID验证: {validationResult.IsValid} - {validationResult.Message}");
            
            // 打印统计信息
            Debug.Log("--- 统计信息 ---");
            int usedCount = EntityUidGenerator.GetUsedUidCount();
            Debug.Log($"已使用ID数量: {usedCount}");
            
            foreach (var entityType in entityTypes)
            {
                var stats = HakoEntityGenerator.GetEntityTypeStats(entityType);
                Debug.Log($"{entityType} 统计: 可用 {stats.AvailableUidCount}");
            }
            
            Debug.Log("=== EntityUidGenerator 基础测试完成 ===");
        }
        
        /// <summary>
        /// 运行性能测试
        /// </summary>
        [ContextMenu("运行性能测试")]
        public void RunPerformanceTest()
        {
            Debug.Log("=== EntityUidGenerator 性能测试开始 ===");
            
            EntityUidGenerator.Reset();
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 生成大量ID
            for (int i = 0; i < testCount; i++)
            {
                EntityUidGenerator.GenerateNextUid();
            }
            
            stopwatch.Stop();
            
            Debug.Log($"生成 {testCount} 个ID 耗时: {stopwatch.ElapsedMilliseconds}ms");
            Debug.Log($"平均每个ID耗时: {(float)stopwatch.ElapsedMilliseconds / testCount:F3}ms");
            
            int usedCount = EntityUidGenerator.GetUsedUidCount();
            Debug.Log($"最终使用ID数量: {usedCount}");
            
            Debug.Log("=== EntityUidGenerator 性能测试完成 ===");
        }
        
        /// <summary>
        /// 测试批量操作
        /// </summary>
        [ContextMenu("测试批量操作")]
        public void TestBatchOperations()
        {
            Debug.Log("=== 批量操作测试开始 ===");
            
            EntityUidGenerator.Reset();
            
            // 预分配ID
            int[] playerUids = HakoEntityGenerator.PreAllocateUids(HakoEntityType.Player, 5);
            Debug.Log($"预分配Player ID: [{string.Join(", ", playerUids)}]");
            
            int[] npcUids = HakoEntityGenerator.PreAllocateUids(HakoEntityType.Npc, 3);
            Debug.Log($"预分配NPC ID: [{string.Join(", ", npcUids)}]");
            
            // 检查可用性
            bool canCreatePlayers = HakoEntityGenerator.CanCreateEntities(HakoEntityType.Player, 10);
            Debug.Log($"可以创建10个Player: {canCreatePlayers}");
            
            bool canCreateNpcs = HakoEntityGenerator.CanCreateEntities(HakoEntityType.Npc, 100);
            Debug.Log($"可以创建100个NPC: {canCreateNpcs}");
            
            Debug.Log("=== 批量操作测试完成 ===");
        }
        
        /// <summary>
        /// 测试配置功能
        /// </summary>
        [ContextMenu("测试配置功能")]
        public void TestConfiguration()
        {
            Debug.Log("=== 配置功能测试开始 ===");
            
            EntityUidGenerator.Reset();
            
            // 设置自定义范围
            EntityUidGenerator.SetEntityTypeRange(HakoEntityType.Player, 1, 10);
            Debug.Log("设置Player ID范围为 [1, 10]");
            
            // 生成Player ID测试范围限制
            for (int i = 0; i < 12; i++)
            {
                try
                {
                    int uid = EntityUidGenerator.GenerateUidByType(HakoEntityType.Player);
                    Debug.Log($"Player ID {i + 1}: {uid}");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"生成Player ID失败: {ex.Message}");
                    break;
                }
            }
            
            // 设置起始值
            EntityUidGenerator.SetIncrementalStartValue(1000);
            Debug.Log("设置递增起始值为 1000");
            
            int incrementalUid = EntityUidGenerator.GenerateNextUid();
            Debug.Log($"递增ID: {incrementalUid}");
            
            Debug.Log("=== 配置功能测试完成 ===");
        }
    }
}
