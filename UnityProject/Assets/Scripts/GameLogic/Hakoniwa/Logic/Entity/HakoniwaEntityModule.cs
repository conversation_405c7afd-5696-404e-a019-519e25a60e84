



using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    public class HakoniwaEntityModule : HakoniwaModule
    {
        public List<HakoniwaEntity> entities = new List<HakoniwaEntity>();

        protected override void OnInit()
        {
            entities.Add(HakoniwaEntity.Gen(HakoEntityType.Player, 1001, 400001, new Vector3(0, 0, -10), "NormalIdle"));
            entities.Add(HakoniwaEntity.Gen(HakoEntityType.Player, 1002, 400102, new Vector3(0, 0, -10), "NormalIdle"));
            entities.Add(HakoniwaEntity.Gen(HakoEntityType.Npc, 2001, 400101, new Vector3(0, 1, -3), "NormalIdle"));
            entities.Add(HakoniwaEntity.Gen(HakoEntityType.Npc, 2002, 400101, new Vector3(0, 1, 3), "NormalIdle"));
            entities.Add(HakoniwaEntity.Gen(HakoEntityType.Npc, 2003, 400102, new Vector3(-3, 1, 0), "NormalIdle"));
            entities.Add(HakoniwaEntity.Gen(HakoEntityType.Npc, 2004, 400103, new Vector3(3, 1, 0), "NormalIdle"));
        }

        protected override void OnUnInit()
        {
            entities.Clear();
        }

    }
}

