



using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Hakoniwa.Logic
{
    public class HakoniwaEntityModule : HakoniwaModule
    {
        protected Dictionary<Int32, HakoniwaEntity> m_entityDic = new Dictionary<Int32, HakoniwaEntity>();

        protected override void OnInit()
        {
            Int32 playerSkinId = Owner.hakoniwaConfig.PlayerSkinId;
            HakoniwaEntity mainPlayer = HakoEntityGenerator.Gen(HakoniwaEntityType.Player, playerSkinId, Owner.location);
            HokoniwaEntityAddInternal(mainPlayer);

            Int32 sceneId = Owner.sceneConfig.Id;
            var entityMap = ConfigDataManager.instance.hakoniwaEntityMap;
            foreach(var item in entityMap)
            {
                if(item.Value.SceneId == sceneId)
                {
                    if (StaticHakoniwaInterface.IsHakoniwaMonsterDead(item.Key))
                    {
                        continue;
                    }

                    HakoniwaEntity entity = HakoEntityGenerator.Gen(item.Value);
                    HokoniwaEntityAddInternal(entity);
                }
            }
        }

        protected override void OnUnInit()
        {
            List<HakoniwaEntity> entities = GetAllHakoniwaEntity();
            foreach (HakoniwaEntity entity in entities)
            {
                HokoniwaEntityRemoveInternal(entity.Uid);
            }
        }



        public void HokoEntityAdd(HakoniwaEntity hakoniwaEntity)
        {
            HokoniwaEntityAddInternal(hakoniwaEntity);
        }

        public void HakoEntityRemove(Int32 entityUid)
        {
            HokoniwaEntityRemoveInternal(entityUid);
        }

        public HakoniwaEntity GetHakoniwaEntity(Int32 entityUid)
        {
            if (m_entityDic.TryGetValue(entityUid, out HakoniwaEntity entity))
            {
                return entity;
            }
            return null;
        }

        public List<HakoniwaEntity> GetAllHakoniwaEntity()
        {
            List<HakoniwaEntity> entities = new List<HakoniwaEntity>();
            foreach(var item in m_entityDic)
            {
                entities.Add(item.Value);
            }
            return entities;
        }



        protected void HokoniwaEntityAddInternal(HakoniwaEntity entity)
        {
            if (!m_entityDic.ContainsKey(entity.Uid))
            {
                EventManager.instance.Broadcast(EventID.HakoEntityViewAdd, entity);
                m_entityDic[entity.Uid] = entity;
            }
        }

        protected void HokoniwaEntityRemoveInternal(Int32 entityUid)
        {
            EventManager.instance.Broadcast(EventID.HakoEntityViewRemove, entityUid);
            if (m_entityDic.TryGetValue(entityUid, out HakoniwaEntity entity))
            {
                entity.Release();
                m_entityDic.Remove(entityUid);
            }
        }
    }
}

