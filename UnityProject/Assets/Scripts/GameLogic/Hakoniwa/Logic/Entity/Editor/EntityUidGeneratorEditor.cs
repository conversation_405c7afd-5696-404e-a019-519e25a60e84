using UnityEngine;
using UnityEditor;

namespace Phoenix.Hakoniwa.Logic.Editor
{
    /// <summary>
    /// EntityUidGenerator的编辑器工具窗口
    /// 提供可视化的Uid生成和管理界面
    /// </summary>
    public class EntityUidGeneratorEditor : EditorWindow
    {
        #region Private Fields
        
        private Vector2 scrollPosition;
        private HakoEntityType selectedEntityType = HakoEntityType.Player;
        private int batchCount = 5;
        private int customStartValue = 1;
        private int customMinValue = 1;
        private int customMaxValue = 999;
        
        // 生成结果显示
        private string lastGeneratedUids = "";
        private string statusMessage = "";
        
        #endregion
        
        #region Menu Items
        
        [MenuItem("Tools/Hakoniwa/EntityUid Generator")]
        public static void ShowWindow()
        {
            var window = GetWindow<EntityUidGeneratorEditor>("EntityUid Generator");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }
        
        #endregion
        
        #region Unity Callbacks
        
        private void OnGUI()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            DrawHeader();
            DrawCurrentStatus();
            DrawUidGeneration();
            DrawEntityCreation();
            DrawConfiguration();
            DrawBatchOperations();
            DrawDebugTools();
            
            EditorGUILayout.EndScrollView();
        }
        
        #endregion
        
        #region GUI Drawing Methods
        
        private void DrawHeader()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("EntityUid Generator", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("用于生成和管理HakoniwaEntity的唯一ID", EditorStyles.helpBox);
            EditorGUILayout.Space();
        }
        
        private void DrawCurrentStatus()
        {
            EditorGUILayout.LabelField("当前状态", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.VerticalScope("box"))
            {
                int usedCount = EntityUidGenerator.GetUsedUidCount();
                EditorGUILayout.LabelField($"已使用Uid数量: {usedCount}");
                
                EditorGUILayout.Space();
                
                // 显示各类型的统计信息
                var entityTypes = new[] { HakoEntityType.Player, HakoEntityType.Npc, HakoEntityType.Monster, HakoEntityType.Object };
                foreach (var entityType in entityTypes)
                {
                    var stats = HakoEntityGenerator.GetEntityTypeStats(entityType);
                    EditorGUILayout.LabelField($"{entityType}: 可用 {stats.AvailableUidCount}");
                }
                
                if (!string.IsNullOrEmpty(statusMessage))
                {
                    EditorGUILayout.Space();
                    EditorGUILayout.HelpBox(statusMessage, MessageType.Info);
                }
            }
            
            EditorGUILayout.Space();
        }
        
        private void DrawUidGeneration()
        {
            EditorGUILayout.LabelField("Uid生成", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.VerticalScope("box"))
            {
                selectedEntityType = (HakoEntityType)EditorGUILayout.EnumPopup("Entity类型", selectedEntityType);
                
                EditorGUILayout.Space();
                
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("生成递增Uid"))
                    {
                        int uid = EntityUidGenerator.GenerateNextUid();
                        lastGeneratedUids = uid.ToString();
                        statusMessage = $"生成递增Uid: {uid}";
                    }
                    
                    if (GUILayout.Button("生成类型Uid"))
                    {
                        int uid = EntityUidGenerator.GenerateUidByType(selectedEntityType);
                        lastGeneratedUids = uid.ToString();
                        statusMessage = $"生成{selectedEntityType} Uid: {uid}";
                    }
                }
                
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("生成时间戳Uid"))
                    {
                        int uid = EntityUidGenerator.GenerateTimestampUid();
                        lastGeneratedUids = uid.ToString();
                        statusMessage = $"生成时间戳Uid: {uid}";
                    }
                    
                    if (GUILayout.Button("生成GUID Uid"))
                    {
                        int uid = EntityUidGenerator.GenerateGuidUid();
                        lastGeneratedUids = uid.ToString();
                        statusMessage = $"生成GUID Uid: {uid}";
                    }
                }
                
                if (!string.IsNullOrEmpty(lastGeneratedUids))
                {
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("最近生成的Uid:", lastGeneratedUids);
                }
            }
            
            EditorGUILayout.Space();
        }
        
        private void DrawEntityCreation()
        {
            EditorGUILayout.LabelField("Entity创建", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.VerticalScope("box"))
            {
                EditorGUILayout.LabelField("快速创建HakoniwaEntity实例");
                
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("创建Player"))
                    {
                        var entity = HakoEntityGenerator.CreateEntity(HakoEntityType.Player, 1001, Vector3.zero, "Idle");
                        statusMessage = $"创建Player Entity, Uid: {entity.Uid}";
                        Debug.Log($"创建Player: {entity.Uid}");
                    }
                    
                    if (GUILayout.Button("创建NPC"))
                    {
                        var entity = HakoEntityGenerator.CreateEntity(HakoEntityType.Npc, 2001, Vector3.zero, "Stand");
                        statusMessage = $"创建NPC Entity, Uid: {entity.Uid}";
                        Debug.Log($"创建NPC: {entity.Uid}");
                    }
                }
                
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("创建Monster"))
                    {
                        var entity = HakoEntityGenerator.CreateEntity(HakoEntityType.Monster, 3001, Vector3.zero, "Patrol");
                        statusMessage = $"创建Monster Entity, Uid: {entity.Uid}";
                        Debug.Log($"创建Monster: {entity.Uid}");
                    }
                    
                    if (GUILayout.Button("创建Object"))
                    {
                        var entity = HakoEntityGenerator.CreateEntity(HakoEntityType.Object, 4001, Vector3.zero, "Static");
                        statusMessage = $"创建Object Entity, Uid: {entity.Uid}";
                        Debug.Log($"创建Object: {entity.Uid}");
                    }
                }
            }
            
            EditorGUILayout.Space();
        }
        
        private void DrawConfiguration()
        {
            EditorGUILayout.LabelField("配置设置", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.VerticalScope("box"))
            {
                // 设置起始值
                using (new EditorGUILayout.HorizontalScope())
                {
                    customStartValue = EditorGUILayout.IntField("递增起始值", customStartValue);
                    if (GUILayout.Button("设置", GUILayout.Width(60)))
                    {
                        EntityUidGenerator.SetIncrementalStartValue(customStartValue);
                        statusMessage = $"设置递增起始值为: {customStartValue}";
                    }
                }
                
                EditorGUILayout.Space();
                
                // 设置类型范围
                EditorGUILayout.LabelField("设置Entity类型ID范围:");
                selectedEntityType = (HakoEntityType)EditorGUILayout.EnumPopup("Entity类型", selectedEntityType);
                
                using (new EditorGUILayout.HorizontalScope())
                {
                    customMinValue = EditorGUILayout.IntField("最小值", customMinValue);
                    customMaxValue = EditorGUILayout.IntField("最大值", customMaxValue);
                }
                
                if (GUILayout.Button("设置类型范围"))
                {
                    if (customMinValue < customMaxValue)
                    {
                        EntityUidGenerator.SetEntityTypeRange(selectedEntityType, customMinValue, customMaxValue);
                        statusMessage = $"设置{selectedEntityType}范围为: [{customMinValue}, {customMaxValue}]";
                    }
                    else
                    {
                        statusMessage = "错误: 最小值必须小于最大值";
                    }
                }
            }
            
            EditorGUILayout.Space();
        }
        
        private void DrawBatchOperations()
        {
            EditorGUILayout.LabelField("批量操作", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.VerticalScope("box"))
            {
                batchCount = EditorGUILayout.IntField("批量数量", batchCount);
                
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("批量生成Uid"))
                    {
                        var uids = HakoEntityGenerator.PreAllocateUids(selectedEntityType, batchCount);
                        lastGeneratedUids = string.Join(", ", uids);
                        statusMessage = $"批量生成{batchCount}个{selectedEntityType} Uid";
                    }
                    
                    if (GUILayout.Button("检查可用性"))
                    {
                        bool canCreate = HakoEntityGenerator.CanCreateEntities(selectedEntityType, batchCount);
                        statusMessage = canCreate 
                            ? $"可以创建{batchCount}个{selectedEntityType}" 
                            : $"无法创建{batchCount}个{selectedEntityType}，可用数量不足";
                    }
                }
            }
            
            EditorGUILayout.Space();
        }
        
        private void DrawDebugTools()
        {
            EditorGUILayout.LabelField("调试工具", EditorStyles.boldLabel);
            
            using (new EditorGUILayout.VerticalScope("box"))
            {
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button("打印调试信息"))
                    {
                        EntityUidGenerator.PrintDebugInfo();
                        statusMessage = "调试信息已输出到Console";
                    }
                    
                    if (GUILayout.Button("重置生成器"))
                    {
                        EntityUidGenerator.Reset();
                        lastGeneratedUids = "";
                        statusMessage = "EntityUidGenerator已重置";
                    }
                }
                
                EditorGUILayout.Space();
                
                if (GUILayout.Button("运行完整测试"))
                {
                    RunCompleteTest();
                }
            }
        }
        
        #endregion
        
        #region Helper Methods
        
        private void RunCompleteTest()
        {
            Debug.Log("=== EntityUidGenerator 编辑器测试开始 ===");
            
            // 重置
            EntityUidGenerator.Reset();
            
            // 生成各种类型的Uid
            var testResults = new System.Text.StringBuilder();
            testResults.AppendLine("测试结果:");
            
            // 测试递增生成
            for (int i = 0; i < 3; i++)
            {
                int uid = EntityUidGenerator.GenerateNextUid();
                testResults.AppendLine($"递增Uid: {uid}");
            }
            
            // 测试类型生成
            var entityTypes = new[] { HakoEntityType.Player, HakoEntityType.Npc, HakoEntityType.Monster };
            foreach (var entityType in entityTypes)
            {
                int uid = EntityUidGenerator.GenerateUidByType(entityType);
                testResults.AppendLine($"{entityType} Uid: {uid}");
            }
            
            // 测试Entity创建
            var player = HakoEntityGenerator.CreateEntity(HakoEntityType.Player, 1001);
            testResults.AppendLine($"创建Player Entity: {player.Uid}");
            
            lastGeneratedUids = testResults.ToString();
            statusMessage = "完整测试完成，结果已显示";
            
            EntityUidGenerator.PrintDebugInfo();
            Debug.Log("=== EntityUidGenerator 编辑器测试完成 ===");
        }
        
        #endregion
    }
}
