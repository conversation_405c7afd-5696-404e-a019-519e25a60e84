using System;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// EntityUid自动生成工具类
    /// 提供多种ID生成策略，确保EntityUid的唯一性
    /// </summary>
    public static class EntityUidGenerator
    {
        #region Private Fields
        
        /// <summary>
        /// 递增ID计数器，线程安全
        /// </summary>
        private static int s_incrementalCounter = 1;
        
        /// <summary>
        /// 不同Entity类型的ID范围配置
        /// </summary>
        private static readonly Dictionary<HakoEntityType, EntityUidRange> s_entityTypeRanges = 
            new Dictionary<HakoEntityType, EntityUidRange>
            {
                { HakoEntityType.Player, new EntityUidRange(1, 999) },
                { HakoEntityType.Npc, new EntityUidRange(1000, 9999) },
                { HakoEntityType.Monster, new EntityUidRange(10000, 99999) },
                { HakoEntityType.Object, new EntityUidRange(100000, 999999) }
            };
        
        /// <summary>
        /// 各类型Entity的当前计数器
        /// </summary>
        private static readonly Dictionary<HakoEntityType, int> s_typeCounters = 
            new Dictionary<HakoEntityType, int>();
        
        /// <summary>
        /// 已使用的ID集合，用于避免重复
        /// </summary>
        private static readonly HashSet<int> s_usedIds = new HashSet<int>();
        
        /// <summary>
        /// 线程锁
        /// </summary>
        private static readonly object s_lock = new object();
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// 生成下一个递增的EntityUid
        /// </summary>
        /// <returns>唯一的EntityUid</returns>
        public static int GenerateNextUid()
        {
            lock (s_lock)
            {
                int uid;
                do
                {
                    uid = Interlocked.Increment(ref s_incrementalCounter);
                } while (s_usedIds.Contains(uid));
                
                s_usedIds.Add(uid);
                return uid;
            }
        }
        
        /// <summary>
        /// 根据Entity类型生成EntityUid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <returns>该类型范围内的唯一EntityUid</returns>
        public static int GenerateUidByType(HakoEntityType entityType)
        {
            lock (s_lock)
            {
                if (!s_entityTypeRanges.TryGetValue(entityType, out EntityUidRange range))
                {
                    // 如果没有配置该类型的范围，使用默认递增生成
                    Debug.LogWarning($"EntityType {entityType} 没有配置ID范围，使用默认递增生成");
                    return GenerateNextUid();
                }
                
                if (!s_typeCounters.TryGetValue(entityType, out int currentCounter))
                {
                    currentCounter = range.MinValue;
                    s_typeCounters[entityType] = currentCounter;
                }
                
                int uid;
                int attempts = 0;
                const int maxAttempts = 1000; // 防止无限循环
                
                do
                {
                    uid = currentCounter;
                    currentCounter++;
                    
                    // 如果超出范围，从最小值重新开始
                    if (currentCounter > range.MaxValue)
                    {
                        currentCounter = range.MinValue;
                    }
                    
                    attempts++;
                    if (attempts > maxAttempts)
                    {
                        throw new InvalidOperationException($"无法为EntityType {entityType} 生成唯一ID，范围内ID已用尽");
                    }
                    
                } while (s_usedIds.Contains(uid));
                
                s_typeCounters[entityType] = currentCounter;
                s_usedIds.Add(uid);
                return uid;
            }
        }
        
        /// <summary>
        /// 生成基于时间戳的EntityUid
        /// </summary>
        /// <returns>基于时间戳的唯一EntityUid</returns>
        public static int GenerateTimestampUid()
        {
            lock (s_lock)
            {
                // 使用Unix时间戳的后8位 + 随机数确保唯一性
                long timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                int baseUid = (int)(timestamp % 100000000); // 取后8位
                
                int uid = baseUid;
                int counter = 0;
                
                while (s_usedIds.Contains(uid))
                {
                    uid = baseUid + counter;
                    counter++;
                    
                    // 防止无限循环
                    if (counter > 1000)
                    {
                        uid = GenerateNextUid();
                        break;
                    }
                }
                
                s_usedIds.Add(uid);
                return uid;
            }
        }
        
        /// <summary>
        /// 生成基于GUID的EntityUid
        /// </summary>
        /// <returns>基于GUID的唯一EntityUid</returns>
        public static int GenerateGuidUid()
        {
            lock (s_lock)
            {
                int uid;
                do
                {
                    // 使用GUID的HashCode作为基础，确保分布均匀
                    uid = Math.Abs(Guid.NewGuid().GetHashCode());
                    
                    // 确保为正数且在合理范围内
                    if (uid <= 0)
                    {
                        uid = Math.Abs(uid) + 1;
                    }
                    
                } while (s_usedIds.Contains(uid));
                
                s_usedIds.Add(uid);
                return uid;
            }
        }
        
        /// <summary>
        /// 检查指定的Uid是否已被使用
        /// </summary>
        /// <param name="uid">要检查的Uid</param>
        /// <returns>如果已被使用返回true，否则返回false</returns>
        public static bool IsUidUsed(int uid)
        {
            lock (s_lock)
            {
                return s_usedIds.Contains(uid);
            }
        }
        
        /// <summary>
        /// 手动注册一个已使用的Uid（用于加载现有数据时）
        /// </summary>
        /// <param name="uid">要注册的Uid</param>
        /// <returns>注册是否成功（如果已存在则返回false）</returns>
        public static bool RegisterUsedUid(int uid)
        {
            lock (s_lock)
            {
                if (s_usedIds.Contains(uid))
                {
                    return false;
                }
                
                s_usedIds.Add(uid);
                
                // 更新递增计数器，确保不会生成重复的ID
                if (uid >= s_incrementalCounter)
                {
                    s_incrementalCounter = uid + 1;
                }
                
                return true;
            }
        }
        
        /// <summary>
        /// 释放一个Uid（当Entity被销毁时调用）
        /// </summary>
        /// <param name="uid">要释放的Uid</param>
        /// <returns>释放是否成功</returns>
        public static bool ReleaseUid(int uid)
        {
            lock (s_lock)
            {
                return s_usedIds.Remove(uid);
            }
        }
        
        /// <summary>
        /// 重置所有计数器和已使用ID记录
        /// </summary>
        public static void Reset()
        {
            lock (s_lock)
            {
                s_incrementalCounter = 1;
                s_typeCounters.Clear();
                s_usedIds.Clear();
                
                Debug.Log("EntityUidGenerator 已重置");
            }
        }
        
        /// <summary>
        /// 设置递增计数器的起始值
        /// </summary>
        /// <param name="startValue">起始值</param>
        public static void SetIncrementalStartValue(int startValue)
        {
            lock (s_lock)
            {
                if (startValue <= 0)
                {
                    Debug.LogError("起始值必须大于0");
                    return;
                }
                
                s_incrementalCounter = startValue;
                Debug.Log($"递增计数器起始值设置为: {startValue}");
            }
        }
        
        /// <summary>
        /// 设置指定Entity类型的ID范围
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="minValue">最小值</param>
        /// <param name="maxValue">最大值</param>
        public static void SetEntityTypeRange(HakoEntityType entityType, int minValue, int maxValue)
        {
            lock (s_lock)
            {
                if (minValue >= maxValue)
                {
                    Debug.LogError($"无效的ID范围: {minValue} >= {maxValue}");
                    return;
                }
                
                s_entityTypeRanges[entityType] = new EntityUidRange(minValue, maxValue);
                
                // 重置该类型的计数器
                if (s_typeCounters.ContainsKey(entityType))
                {
                    s_typeCounters[entityType] = minValue;
                }
                
                Debug.Log($"EntityType {entityType} ID范围设置为: [{minValue}, {maxValue}]");
            }
        }
        
        /// <summary>
        /// 获取当前已使用的Uid数量
        /// </summary>
        /// <returns>已使用的Uid数量</returns>
        public static int GetUsedUidCount()
        {
            lock (s_lock)
            {
                return s_usedIds.Count;
            }
        }
        
        /// <summary>
        /// 获取指定Entity类型的可用ID数量
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <returns>可用ID数量</returns>
        public static int GetAvailableUidCount(HakoEntityType entityType)
        {
            lock (s_lock)
            {
                if (!s_entityTypeRanges.TryGetValue(entityType, out EntityUidRange range))
                {
                    return int.MaxValue; // 如果没有范围限制，返回最大值
                }
                
                int totalRange = range.MaxValue - range.MinValue + 1;
                int usedInRange = 0;
                
                foreach (int uid in s_usedIds)
                {
                    if (uid >= range.MinValue && uid <= range.MaxValue)
                    {
                        usedInRange++;
                    }
                }
                
                return totalRange - usedInRange;
            }
        }
        
        #endregion
        
        #region Helper Classes
        
        /// <summary>
        /// Entity类型的ID范围配置
        /// </summary>
        private class EntityUidRange
        {
            public int MinValue { get; }
            public int MaxValue { get; }
            
            public EntityUidRange(int minValue, int maxValue)
            {
                MinValue = minValue;
                MaxValue = maxValue;
            }
        }
        
        #endregion
        
        #region Debug Methods
        
        /// <summary>
        /// 打印当前状态信息（仅在Debug模式下可用）
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public static void PrintDebugInfo()
        {
            lock (s_lock)
            {
                Debug.Log("=== EntityUidGenerator Debug Info ===");
                Debug.Log($"当前递增计数器: {s_incrementalCounter}");
                Debug.Log($"已使用ID数量: {s_usedIds.Count}");
                
                foreach (var kvp in s_entityTypeRanges)
                {
                    var entityType = kvp.Key;
                    var range = kvp.Value;
                    var currentCounter = s_typeCounters.TryGetValue(entityType, out int counter) ? counter : range.MinValue;
                    var availableCount = GetAvailableUidCount(entityType);
                    
                    Debug.Log($"{entityType}: 范围[{range.MinValue}, {range.MaxValue}], 当前计数器: {currentCounter}, 可用数量: {availableCount}");
                }
                
                Debug.Log("=====================================");
            }
        }
        
        #endregion
    }
}
