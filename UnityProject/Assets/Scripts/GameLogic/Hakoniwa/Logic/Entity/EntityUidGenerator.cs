using System;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// EntityUid生成器 V2
    /// 基于IdGeneratorCore重构，支持自定义区间和自动生成区间
    /// </summary>
    public static class EntityUidGenerator
    {

        #region Public Methods - 基础ID生成

        /// <summary>
        /// 生成下一个递增的EntityUid（使用默认自动生成区间）
        /// </summary>
        /// <returns>唯一的EntityUid</returns>
        public static int GenerateNextUid()
        {
            return IdGeneratorCore.GenerateId(IdType.EntityUid, IdGenerationStrategy.Incremental);
        }

        /// <summary>
        /// 根据Entity类型生成EntityUid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="strategy">生成策略</param>
        /// <returns>该类型范围内的唯一EntityUid</returns>
        public static int GenerateUid(IdGenerationStrategy strategy = IdGenerationStrategy.Incremental)
        {
            return IdGeneratorCore.GenerateId(IdType.EntityUid, strategy);
        }


        /// <summary>
        /// 生成时间戳EntityUid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="useCustomRange">是否使用自定义区间</param>
        /// <returns>基于时间戳的唯一EntityUid</returns>
        public static int GenerateTimestampUid()
        {
            return IdGeneratorCore.GenerateId(IdType.EntityUid, IdGenerationStrategy.Timestamp);
        }

        /// <summary>
        /// 生成GUID EntityUid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="useCustomRange">是否使用自定义区间</param>
        /// <returns>基于GUID的唯一EntityUid</returns>
        public static int GenerateGuidUid()
        {
            return IdGeneratorCore.GenerateId(IdType.EntityUid, IdGenerationStrategy.Guid);
        }

        /// <summary>
        /// 生成随机EntityUid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="useCustomRange">是否使用自定义区间</param>
        /// <returns>随机的唯一EntityUid</returns>
        public static int GenerateRandomUid()
        {
            return IdGeneratorCore.GenerateId(IdType.EntityUid, IdGenerationStrategy.Random);
        }

        #endregion

        #region Public Methods - ID管理

        /// <summary>
        /// 检查指定的EntityUid是否已被使用
        /// </summary>
        /// <param name="uid">要检查的Uid</param>
        /// <returns>如果已被使用返回true，否则返回false</returns>
        public static bool IsUidUsed(int uid)
        {
            return IdGeneratorCore.IsIdUsed(IdType.EntityUid, uid);
        }

        /// <summary>
        /// 手动注册一个已使用的EntityUid
        /// </summary>
        /// <param name="uid">要注册的Uid</param>
        /// <returns>注册是否成功</returns>
        public static bool RegisterUsedUid(int uid)
        {
            return IdGeneratorCore.RegisterUsedId(IdType.EntityUid, uid);
        }

        /// <summary>
        /// 释放一个EntityUid
        /// </summary>
        /// <param name="uid">要释放的Uid</param>
        /// <returns>释放是否成功</returns>
        public static bool ReleaseUid(int uid)
        {
            return IdGeneratorCore.ReleaseId(IdType.EntityUid, uid);
        }

        /// <summary>
        /// 批量注册已使用的EntityUid
        /// </summary>
        /// <param name="uids">要注册的Uid数组</param>
        /// <returns>成功注册的数量</returns>
        public static int RegisterUsedUids(int[] uids)
        {
            if (uids == null || uids.Length == 0)
                return 0;

            int successCount = 0;
            foreach (int uid in uids)
            {
                if (RegisterUsedUid(uid))
                {
                    successCount++;
                }
            }

            return successCount;
        }

        #endregion

        #region Public Methods - 配置管理

        /// <summary>
        /// 设置Entity类型的自定义ID范围
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="minValue">最小值</param>
        /// <param name="maxValue">最大值</param>
        /// <param name="description">描述</param>
        public static void SetCustomEntityTypeRange(HakoEntityType entityType, int minValue, int maxValue, string description = "")
        {
            var config = new IdRangeConfig(minValue, maxValue, string.IsNullOrEmpty(description) ? $"{entityType}自定义ID区间" : description);
            IdGeneratorCore.SetIdRangeConfig(IdType.EntityUid, config);
        }

        /// <summary>
        /// 设置Entity类型的自动生成ID范围
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="minValue">最小值</param>
        /// <param name="maxValue">最大值</param>
        /// <param name="description">描述</param>
        public static void SetAutoEntityTypeRange(HakoEntityType entityType, int minValue, int maxValue, string description = "")
        {
            var config = new IdRangeConfig(minValue, maxValue, string.IsNullOrEmpty(description) ? $"{entityType}自动生成ID区间" : description);
            IdGeneratorCore.SetIdRangeConfig(IdType.EntityUid, config);
        }

        /// <summary>
        /// 设置自定义ID范围
        /// </summary>
        /// <param name="rangeKey">范围键</param>
        /// <param name="minValue">最小值</param>
        /// <param name="maxValue">最大值</param>
        /// <param name="description">描述</param>
        public static void SetCustomRange(string rangeKey, int minValue, int maxValue, string description = "")
        {
            var config = new IdRangeConfig(minValue, maxValue, description);
            IdGeneratorCore.SetIdRangeConfig(IdType.EntityUid, config);
        }

        /// <summary>
        /// 获取Entity类型的ID范围配置
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="useCustomRange">是否获取自定义区间</param>
        /// <returns>ID范围配置</returns>
        public static IdRangeConfig GetEntityTypeRange(HakoEntityType entityType, bool useCustomRange = false)
        {
            return IdGeneratorCore.GetIdRangeConfig(IdType.EntityUid);
        }

        /// <summary>
        /// 获取所有EntityUid范围配置
        /// </summary>
        /// <returns>所有范围配置</returns>
        public static Dictionary<string, IdRangeConfig> GetAllRangeConfigs()
        {
            return IdGeneratorCore.GetAllIdRangeConfigs(IdType.EntityUid);
        }

        #endregion

        #region Public Methods - 统计信息

        /// <summary>
        /// 获取已使用的EntityUid数量
        /// </summary>
        /// <returns>已使用的Uid数量</returns>
        public static int GetUsedUidCount()
        {
            return IdGeneratorCore.GetUsedIdCount(IdType.EntityUid);
        }

        /// <summary>
        /// 获取指定Entity类型的可用ID数量
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="useCustomRange">是否检查自定义区间</param>
        /// <returns>可用ID数量</returns>
        public static int GetAvailableUidCount(HakoEntityType entityType)
        {
            return IdGeneratorCore.GetAvailableIdCount(IdType.EntityUid);
        }

        /// <summary>
        /// 获取指定范围的可用ID数量
        /// </summary>
        /// <param name="rangeKey">范围键</param>
        /// <returns>可用ID数量</returns>
        public static int GetAvailableUidCount()
        {
            return IdGeneratorCore.GetAvailableIdCount(IdType.EntityUid);
        }

        /// <summary>
        /// 检查是否可以创建指定数量的Entity
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="requiredCount">需要创建的数量</param>
        /// <returns>是否可以创建</returns>
        public static bool CanCreateEntities(HakoEntityType entityType, int requiredCount = 1)
        {
            int availableCount = GetAvailableUidCount();
            return availableCount >= requiredCount;
        }

        #endregion

        #region Public Methods - 批量操作

        /// <summary>
        /// 预分配指定数量的EntityUid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="count">需要预分配的数量</param>
        /// <param name="useCustomRange">是否使用自定义区间</param>
        /// <param name="strategy">生成策略</param>
        /// <returns>预分配的Uid数组</returns>
        public static int[] PreAllocateUids(HakoEntityType entityType, int count, IdGenerationStrategy strategy = IdGenerationStrategy.Incremental)
        {
            if (count <= 0)
                return new int[0];

            if (!CanCreateEntities(entityType, count))
            {
                Debug.LogError($"无法为EntityType {entityType} 预分配 {count} 个Uid，可用数量不足");
                return new int[0];
            }

            int[] uids = new int[count];
            for (int i = 0; i < count; i++)
            {
                uids[i] = GenerateUid(strategy);
            }

            return uids;
        }

        #endregion

        #region Public Methods - 重置

        /// <summary>
        /// 重置EntityUid生成器
        /// </summary>
        public static void Reset()
        {
            IdGeneratorCore.Reset(IdType.EntityUid);
        }

        /// <summary>
        /// 重置所有生成器
        /// </summary>
        public static void ResetAll()
        {
            IdGeneratorCore.Reset();
        }

        #endregion

        #region Public Methods - 调试

        /// <summary>
        /// 打印调试信息
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public static void PrintDebugInfo()
        {
            Debug.Log("=== EntityUidGeneratorV2 Debug Info ===");
            Debug.Log($"已使用EntityUid数量: {GetUsedUidCount()}");
            
            var configs = GetAllRangeConfigs();
            foreach (var kvp in configs)
            {
                var availableCount = GetAvailableUidCount();
                Debug.Log($"{kvp.Key}: {kvp.Value}, 可用: {availableCount}");
            }
            
            Debug.Log("=====================================");
        }

        #endregion

        #region Private Helper Methods

        #endregion
    }
}
