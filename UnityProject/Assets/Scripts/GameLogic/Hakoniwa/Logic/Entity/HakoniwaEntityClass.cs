using System;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// Hakoniwa实体类
    /// 表示箱庭世界中的一个实体对象
    /// </summary>
    [Serializable]
    public class HakoniwaEntity
    {
        #region Properties
        
        /// <summary>
        /// 实体的唯一标识符
        /// </summary>
        public int Uid { get; set; }
        
        /// <summary>
        /// 皮肤/外观ID
        /// </summary>
        public int SkinId { get; set; }
        
        /// <summary>
        /// 实体类型
        /// </summary>
        public HakoEntityType EntityType { get; set; }
        
        /// <summary>
        /// 实体在世界中的位置
        /// </summary>
        public Vector3 Position { get; set; }
        
        /// <summary>
        /// 实体的朝向（欧拉角）
        /// </summary>
        public Vector3 Rotation { get; set; }
        
        /// <summary>
        /// 默认动画名称
        /// </summary>
        public string DefaultAnimationName { get; set; }
        
        /// <summary>
        /// 实体名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 实体描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        
        #endregion
        
        #region Constructors
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public HakoniwaEntity()
        {
            Uid = 0;
            SkinId = 0;
            EntityType = HakoEntityType.None;
            Position = Vector3.zero;
            Rotation = Vector3.zero;
            DefaultAnimationName = string.Empty;
            Name = string.Empty;
            Description = string.Empty;
            IsActive = true;
            CreateTime = DateTime.Now;
        }
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="uid">实体ID</param>
        /// <param name="entityType">实体类型</param>
        /// <param name="skinId">皮肤ID</param>
        /// <param name="position">位置</param>
        /// <param name="defaultAnimation">默认动画</param>
        public HakoniwaEntity(int uid, HakoEntityType entityType, int skinId = 0, Vector3 position = default, string defaultAnimation = "")
        {
            Uid = uid;
            EntityType = entityType;
            SkinId = skinId;
            Position = position;
            Rotation = Vector3.zero;
            DefaultAnimationName = defaultAnimation ?? string.Empty;
            Name = string.Empty;
            Description = string.Empty;
            IsActive = true;
            CreateTime = DateTime.Now;
        }

        #endregion

        #region Public Methods

        public static HakoniwaEntity Gen(HakoEntityType entityType, int uid, int skinId, Vector3 position, string defaultAnimationName)
        {
            return new HakoniwaEntity(uid, entityType, skinId, position, defaultAnimationName);
        }

        /// <summary>
        /// 克隆实体
        /// </summary>
        /// <returns>克隆的实体</returns>
        public HakoniwaEntity Clone()
        {
            return new HakoniwaEntity
            {
                Uid = this.Uid,
                SkinId = this.SkinId,
                EntityType = this.EntityType,
                Position = this.Position,
                Rotation = this.Rotation,
                DefaultAnimationName = this.DefaultAnimationName,
                Name = this.Name,
                Description = this.Description,
                IsActive = this.IsActive,
                CreateTime = this.CreateTime
            };
        }
        
        /// <summary>
        /// 重置实体状态
        /// </summary>
        public void Reset()
        {
            Uid = 0;
            SkinId = 0;
            EntityType = HakoEntityType.None;
            Position = Vector3.zero;
            Rotation = Vector3.zero;
            DefaultAnimationName = string.Empty;
            Name = string.Empty;
            Description = string.Empty;
            IsActive = true;
            CreateTime = DateTime.Now;
        }
        
        /// <summary>
        /// 验证实体数据的有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return Uid > 0 && EntityType != HakoEntityType.None;
        }
        
        /// <summary>
        /// 设置位置和旋转
        /// </summary>
        /// <param name="position">位置</param>
        /// <param name="rotation">旋转</param>
        public void SetTransform(Vector3 position, Vector3 rotation)
        {
            Position = position;
            Rotation = rotation;
        }
        
        /// <summary>
        /// 设置位置和旋转（四元数）
        /// </summary>
        /// <param name="position">位置</param>
        /// <param name="rotation">旋转四元数</param>
        public void SetTransform(Vector3 position, Quaternion rotation)
        {
            Position = position;
            Rotation = rotation.eulerAngles;
        }
        
        /// <summary>
        /// 获取旋转四元数
        /// </summary>
        /// <returns>旋转四元数</returns>
        public Quaternion GetRotationQuaternion()
        {
            return Quaternion.Euler(Rotation);
        }
        
        #endregion
        
        #region Overrides
        
        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"HakoniwaEntity[Uid:{Uid}, Type:{EntityType}, SkinId:{SkinId}, Name:{Name}]";
        }
        
        /// <summary>
        /// 重写Equals方法
        /// </summary>
        /// <param name="obj">比较对象</param>
        /// <returns>是否相等</returns>
        public override bool Equals(object obj)
        {
            if (obj is HakoniwaEntity other)
            {
                return Uid == other.Uid;
            }
            return false;
        }
        
        /// <summary>
        /// 重写GetHashCode方法
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            return Uid.GetHashCode();
        }
        
        #endregion
    }
}
