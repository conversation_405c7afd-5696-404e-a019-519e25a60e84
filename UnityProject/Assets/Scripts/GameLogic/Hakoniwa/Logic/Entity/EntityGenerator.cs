using System;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// EntityUid辅助工具类
    /// 提供便捷的Entity创建和Uid管理方法
    /// </summary>
    public static class EntityGenerator
    {
        #region Entity Creation Helpers
        
        /// <summary>
        /// 创建一个新的HakoniwaEntity并自动分配Uid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="skinId">皮肤ID</param>
        /// <param name="position">位置</param>
        /// <param name="defaultAnimation">默认动画名称</param>
        /// <returns>创建的HakoniwaEntity实例</returns>
        public static HakoniwaEntity CreateEntity(HakoEntityType entityType, int skinId, Vector3 position = default, string defaultAnimation = "")
        {
            int uid = EntityUidGenerator.GenerateUidByType(entityType);
            
            return new HakoniwaEntity
            {
                Uid = uid,
                SkinId = skinId,
                EntityType = entityType,
                Position = position,
                DefaultAnimationName = defaultAnimation
            };
        }
        
        /// <summary>
        /// 创建一个新的HakoniwaEntity并使用递增Uid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="skinId">皮肤ID</param>
        /// <param name="position">位置</param>
        /// <param name="defaultAnimation">默认动画名称</param>
        /// <returns>创建的HakoniwaEntity实例</returns>
        public static HakoniwaEntity CreateEntityWithIncrementalUid(HakoEntityType entityType, int skinId, Vector3 position = default, string defaultAnimation = "")
        {
            int uid = EntityUidGenerator.GenerateNextUid();
            
            return new HakoniwaEntity
            {
                Uid = uid,
                SkinId = skinId,
                EntityType = entityType,
                Position = position,
                DefaultAnimationName = defaultAnimation
            };
        }
        
        /// <summary>
        /// 创建一个新的HakoniwaEntity并使用时间戳Uid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="skinId">皮肤ID</param>
        /// <param name="position">位置</param>
        /// <param name="defaultAnimation">默认动画名称</param>
        /// <returns>创建的HakoniwaEntity实例</returns>
        public static HakoniwaEntity CreateEntityWithTimestampUid(HakoEntityType entityType, int skinId, Vector3 position = default, string defaultAnimation = "")
        {
            int uid = EntityUidGenerator.GenerateTimestampUid();
            
            return new HakoniwaEntity
            {
                Uid = uid,
                SkinId = skinId,
                EntityType = entityType,
                Position = position,
                DefaultAnimationName = defaultAnimation
            };
        }
        
        /// <summary>
        /// 创建一个新的HakoniwaEntity并使用GUID Uid
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="skinId">皮肤ID</param>
        /// <param name="position">位置</param>
        /// <param name="defaultAnimation">默认动画名称</param>
        /// <returns>创建的HakoniwaEntity实例</returns>
        public static HakoniwaEntity CreateEntityWithGuidUid(HakoEntityType entityType, int skinId, Vector3 position = default, string defaultAnimation = "")
        {
            int uid = EntityUidGenerator.GenerateGuidUid();
            
            return new HakoniwaEntity
            {
                Uid = uid,
                SkinId = skinId,
                EntityType = entityType,
                Position = position,
                DefaultAnimationName = defaultAnimation
            };
        }
        
        #endregion
        
        #region Uid Management Helpers
        
        /// <summary>
        /// 为现有Entity分配新的Uid
        /// </summary>
        /// <param name="entity">要分配Uid的Entity</param>
        /// <param name="useTypeBasedGeneration">是否使用基于类型的生成策略</param>
        /// <returns>分配的新Uid</returns>
        public static int AssignNewUid(HakoniwaEntity entity, bool useTypeBasedGeneration = true)
        {
            if (entity == null)
            {
                Debug.LogError("Entity不能为null");
                return 0;
            }
            
            // 如果Entity已有Uid，先释放它
            if (entity.Uid > 0)
            {
                EntityUidGenerator.ReleaseUid(entity.Uid);
            }
            
            // 生成新的Uid
            int newUid = useTypeBasedGeneration 
                ? EntityUidGenerator.GenerateUidByType(entity.EntityType)
                : EntityUidGenerator.GenerateNextUid();
            
            entity.Uid = newUid;
            return newUid;
        }
        
        /// <summary>
        /// 验证Entity的Uid是否有效且唯一
        /// </summary>
        /// <param name="entity">要验证的Entity</param>
        /// <returns>验证结果</returns>
        public static UidValidationResult ValidateEntityUid(HakoniwaEntity entity)
        {
            if (entity == null)
            {
                return new UidValidationResult(false, "Entity不能为null");
            }
            
            if (entity.Uid <= 0)
            {
                return new UidValidationResult(false, "Uid必须大于0");
            }
            
            if (EntityUidGenerator.IsUidUsed(entity.Uid))
            {
                return new UidValidationResult(false, $"Uid {entity.Uid} 已被使用");
            }
            
            return new UidValidationResult(true, "Uid有效");
        }
        
        /// <summary>
        /// 批量注册已存在的Entity Uid（用于加载存档数据）
        /// </summary>
        /// <param name="entities">Entity数组</param>
        /// <returns>成功注册的数量</returns>
        public static int RegisterExistingEntities(HakoniwaEntity[] entities)
        {
            if (entities == null || entities.Length == 0)
            {
                return 0;
            }
            
            int successCount = 0;
            foreach (var entity in entities)
            {
                if (entity != null && entity.Uid > 0)
                {
                    if (EntityUidGenerator.RegisterUsedUid(entity.Uid))
                    {
                        successCount++;
                    }
                    else
                    {
                        Debug.LogWarning($"Entity Uid {entity.Uid} 注册失败，可能已存在重复");
                    }
                }
            }
            
            Debug.Log($"批量注册Entity Uid完成，成功注册 {successCount}/{entities.Length} 个");
            return successCount;
        }
        
        /// <summary>
        /// 清理Entity的Uid（当Entity被销毁时调用）
        /// </summary>
        /// <param name="entity">要清理的Entity</param>
        /// <returns>清理是否成功</returns>
        public static bool CleanupEntityUid(HakoniwaEntity entity)
        {
            if (entity == null || entity.Uid <= 0)
            {
                return false;
            }
            
            bool success = EntityUidGenerator.ReleaseUid(entity.Uid);
            if (success)
            {
                entity.Uid = 0; // 重置Entity的Uid
            }
            
            return success;
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// 获取Entity类型的统计信息
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <returns>统计信息</returns>
        public static EntityTypeStats GetEntityTypeStats(HakoEntityType entityType)
        {
            int availableCount = EntityUidGenerator.GetAvailableUidCount(entityType);
            int totalUsedCount = EntityUidGenerator.GetUsedUidCount();
            
            return new EntityTypeStats
            {
                EntityType = entityType,
                AvailableUidCount = availableCount,
                TotalUsedUidCount = totalUsedCount
            };
        }
        
        /// <summary>
        /// 检查是否可以创建指定类型的Entity
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="requiredCount">需要创建的数量</param>
        /// <returns>是否可以创建</returns>
        public static bool CanCreateEntities(HakoEntityType entityType, int requiredCount = 1)
        {
            int availableCount = EntityUidGenerator.GetAvailableUidCount(entityType);
            return availableCount >= requiredCount;
        }
        
        /// <summary>
        /// 预分配指定数量的Uid（用于批量创建Entity）
        /// </summary>
        /// <param name="entityType">Entity类型</param>
        /// <param name="count">需要预分配的数量</param>
        /// <returns>预分配的Uid数组</returns>
        public static int[] PreAllocateUids(HakoEntityType entityType, int count)
        {
            if (count <= 0)
            {
                return new int[0];
            }
            
            if (!CanCreateEntities(entityType, count))
            {
                Debug.LogError($"无法为EntityType {entityType} 预分配 {count} 个Uid，可用数量不足");
                return new int[0];
            }
            
            int[] uids = new int[count];
            for (int i = 0; i < count; i++)
            {
                uids[i] = EntityUidGenerator.GenerateUidByType(entityType);
            }
            
            return uids;
        }
        
        #endregion
        
        #region Helper Classes
        
        /// <summary>
        /// Uid验证结果
        /// </summary>
        public class UidValidationResult
        {
            public bool IsValid { get; }
            public string Message { get; }
            
            public UidValidationResult(bool isValid, string message)
            {
                IsValid = isValid;
                Message = message;
            }
        }
        
        /// <summary>
        /// Entity类型统计信息
        /// </summary>
        public class EntityTypeStats
        {
            public HakoEntityType EntityType { get; set; }
            public int AvailableUidCount { get; set; }
            public int TotalUsedUidCount { get; set; }
            
            public override string ToString()
            {
                return $"EntityType: {EntityType}, Available: {AvailableUidCount}, TotalUsed: {TotalUsedUidCount}";
            }
        }
        
        #endregion
    }
}
