using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// EntityUidGenerator使用示例
    /// 展示如何使用EntityUid自动生成工具类
    /// </summary>
    public class EntityUidGeneratorExample : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool runTestOnStart = false;
        [SerializeField] private int testEntityCount = 10;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                RunAllTests();
            }
        }
        
        /// <summary>
        /// 运行所有测试示例
        /// </summary>
        [ContextMenu("运行所有测试")]
        public void RunAllTests()
        {
            Debug.Log("=== EntityUidGenerator 测试开始 ===");
            
            // 重置生成器状态
            EntityUidGenerator.Reset();
            
            TestBasicUidGeneration();
            TestTypeBasedUidGeneration();
            TestTimestampUidGeneration();
            TestGuidUidGeneration();
            TestEntityCreationHelpers();
            TestUidManagement();
            TestBatchOperations();
            
            // 打印最终状态
            EntityUidGenerator.PrintDebugInfo();
            
            Debug.Log("=== EntityUidGenerator 测试完成 ===");
        }
        
        /// <summary>
        /// 测试基础Uid生成
        /// </summary>
        [ContextMenu("测试基础Uid生成")]
        public void TestBasicUidGeneration()
        {
            Debug.Log("--- 测试基础Uid生成 ---");
            
            for (int i = 0; i < 5; i++)
            {
                int uid = EntityUidGenerator.GenerateNextUid();
                Debug.Log($"生成递增Uid: {uid}");
            }
        }
        
        /// <summary>
        /// 测试基于类型的Uid生成
        /// </summary>
        [ContextMenu("测试基于类型的Uid生成")]
        public void TestTypeBasedUidGeneration()
        {
            Debug.Log("--- 测试基于类型的Uid生成 ---");
            
            // 为不同类型生成Uid
            var entityTypes = new[] { HakoEntityType.Player, HakoEntityType.Npc, HakoEntityType.Monster, HakoEntityType.Object };
            
            foreach (var entityType in entityTypes)
            {
                for (int i = 0; i < 3; i++)
                {
                    int uid = EntityUidGenerator.GenerateUidByType(entityType);
                    Debug.Log($"生成{entityType} Uid: {uid}");
                }
            }
        }
        
        /// <summary>
        /// 测试时间戳Uid生成
        /// </summary>
        [ContextMenu("测试时间戳Uid生成")]
        public void TestTimestampUidGeneration()
        {
            Debug.Log("--- 测试时间戳Uid生成 ---");
            
            for (int i = 0; i < 3; i++)
            {
                int uid = EntityUidGenerator.GenerateTimestampUid();
                Debug.Log($"生成时间戳Uid: {uid}");
            }
        }
        
        /// <summary>
        /// 测试GUID Uid生成
        /// </summary>
        [ContextMenu("测试GUID Uid生成")]
        public void TestGuidUidGeneration()
        {
            Debug.Log("--- 测试GUID Uid生成 ---");
            
            for (int i = 0; i < 3; i++)
            {
                int uid = EntityUidGenerator.GenerateGuidUid();
                Debug.Log($"生成GUID Uid: {uid}");
            }
        }
        
        /// <summary>
        /// 测试Entity创建辅助方法
        /// </summary>
        [ContextMenu("测试Entity创建辅助方法")]
        public void TestEntityCreationHelpers()
        {
            Debug.Log("--- 测试Entity创建辅助方法 ---");
            
            // 使用不同的创建方法
            var player = EntityGenerator.CreateEntity(HakoEntityType.Player, 1001, Vector3.zero, "Idle");
            Debug.Log($"创建Player: Uid={player.Uid}, SkinId={player.SkinId}, Type={player.EntityType}");
            
            var npc = EntityGenerator.CreateEntityWithIncrementalUid(HakoEntityType.Npc, 2001, Vector3.one, "Stand");
            Debug.Log($"创建NPC: Uid={npc.Uid}, SkinId={npc.SkinId}, Type={npc.EntityType}");
            
            var monster = EntityGenerator.CreateEntityWithTimestampUid(HakoEntityType.Monster, 3001, Vector3.up, "Patrol");
            Debug.Log($"创建Monster: Uid={monster.Uid}, SkinId={monster.SkinId}, Type={monster.EntityType}");
            
            var obj = EntityGenerator.CreateEntityWithGuidUid(HakoEntityType.Object, 4001, Vector3.down, "Static");
            Debug.Log($"创建Object: Uid={obj.Uid}, SkinId={obj.SkinId}, Type={obj.EntityType}");
        }
        
        /// <summary>
        /// 测试Uid管理功能
        /// </summary>
        [ContextMenu("测试Uid管理功能")]
        public void TestUidManagement()
        {
            Debug.Log("--- 测试Uid管理功能 ---");
            
            // 创建一个Entity
            var entity = EntityGenerator.CreateEntity(HakoEntityType.Player, 1001);
            Debug.Log($"创建Entity，Uid: {entity.Uid}");
            
            // 验证Uid
            var validationResult = EntityGenerator.ValidateEntityUid(entity);
            Debug.Log($"Uid验证结果: {validationResult.IsValid}, 消息: {validationResult.Message}");
            
            // 重新分配Uid
            int oldUid = entity.Uid;
            int newUid = EntityGenerator.AssignNewUid(entity);
            Debug.Log($"重新分配Uid: {oldUid} -> {newUid}");
            
            // 清理Uid
            bool cleanupSuccess = EntityGenerator.CleanupEntityUid(entity);
            Debug.Log($"清理Uid结果: {cleanupSuccess}, Entity Uid: {entity.Uid}");
        }
        
        /// <summary>
        /// 测试批量操作
        /// </summary>
        [ContextMenu("测试批量操作")]
        public void TestBatchOperations()
        {
            Debug.Log("--- 测试批量操作 ---");
            
            // 预分配Uid
            int[] preAllocatedUids = EntityGenerator.PreAllocateUids(HakoEntityType.Npc, 5);
            Debug.Log($"预分配了 {preAllocatedUids.Length} 个NPC Uid: [{string.Join(", ", preAllocatedUids)}]");
            
            // 创建Entity数组用于批量注册测试
            var entities = new HakoniwaEntity[]
            {
                new HakoniwaEntity { Uid = 50001, EntityType = HakoEntityType.Player },
                new HakoniwaEntity { Uid = 50002, EntityType = HakoEntityType.Npc },
                new HakoniwaEntity { Uid = 50003, EntityType = HakoEntityType.Monster }
            };
            
            // 批量注册
            int registeredCount = EntityGenerator.RegisterExistingEntities(entities);
            Debug.Log($"批量注册结果: {registeredCount}/{entities.Length}");
            
            // 获取统计信息
            foreach (var entityType in new[] { HakoEntityType.Player, HakoEntityType.Npc, HakoEntityType.Monster })
            {
                var stats = EntityGenerator.GetEntityTypeStats(entityType);
                Debug.Log($"统计信息: {stats}");
            }
        }
        
        /// <summary>
        /// 测试配置功能
        /// </summary>
        [ContextMenu("测试配置功能")]
        public void TestConfiguration()
        {
            Debug.Log("--- 测试配置功能 ---");
            
            // 设置自定义范围
            EntityUidGenerator.SetEntityTypeRange(HakoEntityType.Player, 1, 100);
            Debug.Log("设置Player ID范围为 [1, 100]");
            
            // 设置起始值
            EntityUidGenerator.SetIncrementalStartValue(1000);
            Debug.Log("设置递增起始值为 1000");
            
            // 生成一些Uid测试
            for (int i = 0; i < 3; i++)
            {
                int playerUid = EntityUidGenerator.GenerateUidByType(HakoEntityType.Player);
                int incrementalUid = EntityUidGenerator.GenerateNextUid();
                Debug.Log($"Player Uid: {playerUid}, Incremental Uid: {incrementalUid}");
            }
        }
        
        /// <summary>
        /// 压力测试
        /// </summary>
        [ContextMenu("压力测试")]
        public void StressTest()
        {
            Debug.Log("--- 压力测试开始 ---");
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 生成大量Uid
            for (int i = 0; i < testEntityCount; i++)
            {
                EntityUidGenerator.GenerateNextUid();
            }
            
            stopwatch.Stop();
            Debug.Log($"生成 {testEntityCount} 个Uid 耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            // 检查唯一性
            var usedCount = EntityUidGenerator.GetUsedUidCount();
            Debug.Log($"当前已使用Uid数量: {usedCount}");
            
            EntityUidGenerator.PrintDebugInfo();
        }
        
        /// <summary>
        /// 重置生成器
        /// </summary>
        [ContextMenu("重置生成器")]
        public void ResetGenerator()
        {
            EntityUidGenerator.Reset();
            Debug.Log("EntityUidGenerator 已重置");
        }
    }
}
