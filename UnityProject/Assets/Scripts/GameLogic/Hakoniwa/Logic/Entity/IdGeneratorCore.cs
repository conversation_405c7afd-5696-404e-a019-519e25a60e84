using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// ID类型枚举
    /// </summary>
    public enum IdType
    {
        EntityUid,      // 实体ID
        InteractionUid  // 交互行为ID
    }

    /// <summary>
    /// ID区间类型
    /// </summary>
    public enum IdRangeType
    {
        Custom,     // 自定义区间（开发人员手动配置）
        Auto        // 自动生成区间（系统自动分配）
    }

    /// <summary>
    /// ID范围配置
    /// </summary>
    [Serializable]
    public class IdRangeConfig
    {
        public int MinValue { get; set; }
        public int MaxValue { get; set; }
        public IdRangeType RangeType { get; set; }
        public string Description { get; set; }

        public IdRangeConfig(int minValue, int maxValue, IdRangeType rangeType, string description = "")
        {
            MinValue = minValue;
            MaxValue = maxValue;
            RangeType = rangeType;
            Description = description;
        }

        public int GetRangeSize()
        {
            return MaxValue - MinValue + 1;
        }

        public bool IsInRange(int id)
        {
            return id >= MinValue && id <= MaxValue;
        }

        public override string ToString()
        {
            return $"[{MinValue}, {MaxValue}] ({RangeType}) - {Description}";
        }
    }

    /// <summary>
    /// ID生成策略
    /// </summary>
    public enum IdGenerationStrategy
    {
        Incremental,    // 递增
        Timestamp,      // 时间戳
        Guid,          // GUID
        Random         // 随机
    }

    /// <summary>
    /// ID生成器核心类
    /// 提供统一的ID生成和管理功能
    /// </summary>
    public static class IdGeneratorCore
    {
        #region Private Fields

        /// <summary>
        /// 线程锁
        /// </summary>
        private static readonly object s_lock = new object();

        /// <summary>
        /// 已使用的ID集合
        /// </summary>
        private static readonly Dictionary<IdType, HashSet<int>> s_usedIds = 
            new Dictionary<IdType, HashSet<int>>();

        /// <summary>
        /// ID范围配置
        /// </summary>
        private static readonly Dictionary<IdType, Dictionary<object, IdRangeConfig>> s_idRangeConfigs = 
            new Dictionary<IdType, Dictionary<object, IdRangeConfig>>();

        /// <summary>
        /// 当前计数器
        /// </summary>
        private static readonly Dictionary<string, int> s_counters = 
            new Dictionary<string, int>();

        /// <summary>
        /// 随机数生成器
        /// </summary>
        private static readonly System.Random s_random = new System.Random();

        #endregion

        #region Initialization

        /// <summary>
        /// 静态构造函数，初始化默认配置
        /// </summary>
        static IdGeneratorCore()
        {
            InitializeDefaultConfigs();
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        private static void InitializeDefaultConfigs()
        {
            // 初始化EntityUid配置
            s_idRangeConfigs[IdType.EntityUid] = new Dictionary<object, IdRangeConfig>();
            s_usedIds[IdType.EntityUid] = new HashSet<int>();

            // 初始化InteractionUid配置
            s_idRangeConfigs[IdType.InteractionUid] = new Dictionary<object, IdRangeConfig>();
            s_usedIds[IdType.InteractionUid] = new HashSet<int>();

            // 设置默认的EntityUid范围
            SetDefaultEntityUidRanges();

            // 设置默认的InteractionUid范围
            SetDefaultInteractionUidRanges();
        }

        /// <summary>
        /// 设置默认的EntityUid范围
        /// </summary>
        private static void SetDefaultEntityUidRanges()
        {
            // 自定义区间（开发人员手动配置）
            s_idRangeConfigs[IdType.EntityUid]["Player_Custom"] = 
                new IdRangeConfig(1, 999, IdRangeType.Custom, "玩家自定义ID区间");
            s_idRangeConfigs[IdType.EntityUid]["Npc_Custom"] = 
                new IdRangeConfig(1000, 9999, IdRangeType.Custom, "NPC自定义ID区间");
            s_idRangeConfigs[IdType.EntityUid]["Monster_Custom"] = 
                new IdRangeConfig(10000, 99999, IdRangeType.Custom, "怪物自定义ID区间");
            s_idRangeConfigs[IdType.EntityUid]["Object_Custom"] = 
                new IdRangeConfig(100000, 999999, IdRangeType.Custom, "物体自定义ID区间");

            // 自动生成区间（系统自动分配）
            s_idRangeConfigs[IdType.EntityUid]["Player_Auto"] = 
                new IdRangeConfig(1000000, 1999999, IdRangeType.Auto, "玩家自动生成ID区间");
            s_idRangeConfigs[IdType.EntityUid]["Npc_Auto"] = 
                new IdRangeConfig(2000000, 2999999, IdRangeType.Auto, "NPC自动生成ID区间");
            s_idRangeConfigs[IdType.EntityUid]["Monster_Auto"] = 
                new IdRangeConfig(3000000, 3999999, IdRangeType.Auto, "怪物自动生成ID区间");
            s_idRangeConfigs[IdType.EntityUid]["Object_Auto"] = 
                new IdRangeConfig(4000000, 4999999, IdRangeType.Auto, "物体自动生成ID区间");
        }

        /// <summary>
        /// 设置默认的InteractionUid范围
        /// </summary>
        private static void SetDefaultInteractionUidRanges()
        {
            // 自定义区间（开发人员手动配置）
            s_idRangeConfigs[IdType.InteractionUid]["Talk_Custom"] = 
                new IdRangeConfig(1, 9999, IdRangeType.Custom, "对话交互自定义ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Battle_Custom"] = 
                new IdRangeConfig(10000, 19999, IdRangeType.Custom, "战斗交互自定义ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Trade_Custom"] = 
                new IdRangeConfig(20000, 29999, IdRangeType.Custom, "交易交互自定义ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Quest_Custom"] = 
                new IdRangeConfig(30000, 39999, IdRangeType.Custom, "任务交互自定义ID区间");

            // 自动生成区间（系统自动分配）
            s_idRangeConfigs[IdType.InteractionUid]["Talk_Auto"] = 
                new IdRangeConfig(100000, 199999, IdRangeType.Auto, "对话交互自动生成ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Battle_Auto"] = 
                new IdRangeConfig(200000, 299999, IdRangeType.Auto, "战斗交互自动生成ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Trade_Auto"] = 
                new IdRangeConfig(300000, 399999, IdRangeType.Auto, "交易交互自动生成ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Quest_Auto"] = 
                new IdRangeConfig(400000, 499999, IdRangeType.Auto, "任务交互自动生成ID区间");
            s_idRangeConfigs[IdType.InteractionUid]["Dynamic_Auto"] = 
                new IdRangeConfig(500000, 999999, IdRangeType.Auto, "动态交互自动生成ID区间");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 生成指定类型和范围的ID
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="rangeKey">范围键</param>
        /// <param name="strategy">生成策略</param>
        /// <returns>生成的ID</returns>
        public static int GenerateId(IdType idType, string rangeKey, IdGenerationStrategy strategy = IdGenerationStrategy.Incremental)
        {
            lock (s_lock)
            {
                if (!s_idRangeConfigs.TryGetValue(idType, out var ranges) ||
                    !ranges.TryGetValue(rangeKey, out var range))
                {
                    throw new ArgumentException($"未找到ID类型 {idType} 的范围配置 {rangeKey}");
                }

                return GenerateIdInRange(idType, range, strategy);
            }
        }

        /// <summary>
        /// 在指定范围内生成ID
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="range">ID范围</param>
        /// <param name="strategy">生成策略</param>
        /// <returns>生成的ID</returns>
        private static int GenerateIdInRange(IdType idType, IdRangeConfig range, IdGenerationStrategy strategy)
        {
            var usedIds = s_usedIds[idType];
            int id;
            int attempts = 0;
            const int maxAttempts = 1000;

            do
            {
                id = strategy switch
                {
                    IdGenerationStrategy.Incremental => GenerateIncrementalId(idType, range),
                    IdGenerationStrategy.Timestamp => GenerateTimestampId(range),
                    IdGenerationStrategy.Guid => GenerateGuidId(range),
                    IdGenerationStrategy.Random => GenerateRandomId(range),
                    _ => GenerateIncrementalId(idType, range)
                };

                attempts++;
                if (attempts > maxAttempts)
                {
                    throw new InvalidOperationException($"无法在范围 {range} 内生成唯一ID，已尝试 {maxAttempts} 次");
                }

            } while (usedIds.Contains(id));

            usedIds.Add(id);
            return id;
        }

        /// <summary>
        /// 生成递增ID
        /// </summary>
        private static int GenerateIncrementalId(IdType idType, IdRangeConfig range)
        {
            string counterKey = $"{idType}_{range.MinValue}_{range.MaxValue}";
            
            if (!s_counters.TryGetValue(counterKey, out int currentCounter))
            {
                currentCounter = range.MinValue;
            }

            int id = currentCounter;
            currentCounter++;

            if (currentCounter > range.MaxValue)
            {
                currentCounter = range.MinValue;
            }

            s_counters[counterKey] = currentCounter;
            return id;
        }

        /// <summary>
        /// 生成时间戳ID
        /// </summary>
        private static int GenerateTimestampId(IdRangeConfig range)
        {
            long timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            int baseId = (int)(timestamp % (range.MaxValue - range.MinValue + 1)) + range.MinValue;
            return Math.Max(range.MinValue, Math.Min(range.MaxValue, baseId));
        }

        /// <summary>
        /// 生成GUID ID
        /// </summary>
        private static int GenerateGuidId(IdRangeConfig range)
        {
            int guidHash = Math.Abs(Guid.NewGuid().GetHashCode());
            int id = (guidHash % (range.MaxValue - range.MinValue + 1)) + range.MinValue;
            return id;
        }

        /// <summary>
        /// 生成随机ID
        /// </summary>
        private static int GenerateRandomId(IdRangeConfig range)
        {
            return s_random.Next(range.MinValue, range.MaxValue + 1);
        }

        /// <summary>
        /// 检查ID是否已被使用
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="id">要检查的ID</param>
        /// <returns>是否已被使用</returns>
        public static bool IsIdUsed(IdType idType, int id)
        {
            lock (s_lock)
            {
                return s_usedIds.TryGetValue(idType, out var usedIds) && usedIds.Contains(id);
            }
        }

        /// <summary>
        /// 注册已使用的ID
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="id">要注册的ID</param>
        /// <returns>注册是否成功</returns>
        public static bool RegisterUsedId(IdType idType, int id)
        {
            lock (s_lock)
            {
                if (!s_usedIds.TryGetValue(idType, out var usedIds))
                {
                    usedIds = new HashSet<int>();
                    s_usedIds[idType] = usedIds;
                }

                return usedIds.Add(id);
            }
        }

        /// <summary>
        /// 释放ID
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="id">要释放的ID</param>
        /// <returns>释放是否成功</returns>
        public static bool ReleaseId(IdType idType, int id)
        {
            lock (s_lock)
            {
                return s_usedIds.TryGetValue(idType, out var usedIds) && usedIds.Remove(id);
            }
        }

        /// <summary>
        /// 设置ID范围配置
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="rangeKey">范围键</param>
        /// <param name="config">范围配置</param>
        public static void SetIdRangeConfig(IdType idType, string rangeKey, IdRangeConfig config)
        {
            lock (s_lock)
            {
                if (!s_idRangeConfigs.TryGetValue(idType, out var ranges))
                {
                    ranges = new Dictionary<object, IdRangeConfig>();
                    s_idRangeConfigs[idType] = ranges;
                }

                ranges[rangeKey] = config;
                Debug.Log($"设置ID范围配置: {idType}.{rangeKey} = {config}");
            }
        }

        /// <summary>
        /// 获取ID范围配置
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="rangeKey">范围键</param>
        /// <returns>范围配置</returns>
        public static IdRangeConfig GetIdRangeConfig(IdType idType, string rangeKey)
        {
            lock (s_lock)
            {
                if (s_idRangeConfigs.TryGetValue(idType, out var ranges) &&
                    ranges.TryGetValue(rangeKey, out var config))
                {
                    return config;
                }
                return null;
            }
        }

        /// <summary>
        /// 获取所有ID范围配置
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <returns>所有范围配置</returns>
        public static Dictionary<string, IdRangeConfig> GetAllIdRangeConfigs(IdType idType)
        {
            lock (s_lock)
            {
                var result = new Dictionary<string, IdRangeConfig>();
                
                if (s_idRangeConfigs.TryGetValue(idType, out var ranges))
                {
                    foreach (var kvp in ranges)
                    {
                        if (kvp.Key is string key)
                        {
                            result[key] = kvp.Value;
                        }
                    }
                }
                
                return result;
            }
        }

        /// <summary>
        /// 获取已使用ID数量
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <returns>已使用ID数量</returns>
        public static int GetUsedIdCount(IdType idType)
        {
            lock (s_lock)
            {
                return s_usedIds.TryGetValue(idType, out var usedIds) ? usedIds.Count : 0;
            }
        }

        /// <summary>
        /// 获取指定范围内的可用ID数量
        /// </summary>
        /// <param name="idType">ID类型</param>
        /// <param name="rangeKey">范围键</param>
        /// <returns>可用ID数量</returns>
        public static int GetAvailableIdCount(IdType idType, string rangeKey)
        {
            lock (s_lock)
            {
                if (!s_idRangeConfigs.TryGetValue(idType, out var ranges) ||
                    !ranges.TryGetValue(rangeKey, out var range))
                {
                    return 0;
                }

                if (!s_usedIds.TryGetValue(idType, out var usedIds))
                {
                    return range.GetRangeSize();
                }

                int usedInRange = 0;
                foreach (int id in usedIds)
                {
                    if (range.IsInRange(id))
                    {
                        usedInRange++;
                    }
                }

                return range.GetRangeSize() - usedInRange;
            }
        }

        /// <summary>
        /// 重置所有数据
        /// </summary>
        public static void Reset()
        {
            lock (s_lock)
            {
                s_usedIds.Clear();
                s_counters.Clear();
                InitializeDefaultConfigs();
                Debug.Log("IdGeneratorCore 已重置");
            }
        }

        /// <summary>
        /// 重置指定类型的数据
        /// </summary>
        /// <param name="idType">ID类型</param>
        public static void Reset(IdType idType)
        {
            lock (s_lock)
            {
                if (s_usedIds.ContainsKey(idType))
                {
                    s_usedIds[idType].Clear();
                }

                // 清除相关计数器
                var keysToRemove = new List<string>();
                foreach (var key in s_counters.Keys)
                {
                    if (key.StartsWith(idType.ToString()))
                    {
                        keysToRemove.Add(key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    s_counters.Remove(key);
                }

                Debug.Log($"IdGeneratorCore {idType} 已重置");
            }
        }

        /// <summary>
        /// 打印调试信息
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public static void PrintDebugInfo()
        {
            lock (s_lock)
            {
                Debug.Log("=== IdGeneratorCore Debug Info ===");
                
                foreach (var idType in Enum.GetValues(typeof(IdType)).Cast<IdType>())
                {
                    Debug.Log($"--- {idType} ---");
                    Debug.Log($"已使用ID数量: {GetUsedIdCount(idType)}");
                    
                    var configs = GetAllIdRangeConfigs(idType);
                    foreach (var kvp in configs)
                    {
                        var availableCount = GetAvailableIdCount(idType, kvp.Key);
                        Debug.Log($"{kvp.Key}: {kvp.Value}, 可用: {availableCount}");
                    }
                }
                
                Debug.Log("=====================================");
            }
        }

        #endregion
    }
}
