using Phoenix.ConfigData.QuestGraph;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 对话完成条件 V2
    /// 需要与指定NPC进行对话
    /// </summary>
    public class CompleteConditionTalkV2 : CompleteConditionV2
    {
        #region Private Fields

        /// <summary>
        /// 对话条件配置
        /// </summary>
        private ConfigDataQuestCompleteConditionTalk m_talkConfig;

        /// <summary>
        /// 目标NPC ID
        /// </summary>
        private int m_targetNpcId;

        /// <summary>
        /// 需要对话次数
        /// </summary>
        private int m_requiredTalkCount;

        /// <summary>
        /// 当前对话次数
        /// </summary>
        private int m_currentTalkCount = 0;

        #endregion

        #region Properties

        /// <summary>
        /// 目标NPC ID
        /// </summary>
        public int TargetNpcId => m_targetNpcId;

        /// <summary>
        /// 需要对话次数
        /// </summary>
        public int RequiredTalkCount => m_requiredTalkCount;

        /// <summary>
        /// 当前对话次数
        /// </summary>
        public int CurrentTalkCount => m_currentTalkCount;

        #endregion

        #region CompleteConditionV2 Override

        protected override void OnInitialize()
        {
            m_talkConfig = m_conditionConfig as ConfigDataQuestCompleteConditionTalk;
            if (m_talkConfig == null)
            {
                Debug.LogError("[CompleteConditionTalkV2] 无效的对话条件配置");
                return;
            }

            m_targetNpcId = m_talkConfig.NpcId;
            m_requiredTalkCount = Mathf.Max(1, 1);
            m_maxProgress = m_requiredTalkCount;
            m_currentProgress = 0;

            m_description = $"与NPC {m_targetNpcId} 对话 {m_requiredTalkCount} 次";

            // 注册对话事件监听
            RegisterTalkEventListener();

            Debug.Log($"[CompleteConditionTalkV2] 初始化对话条件: {m_description}");
        }

        protected override void OnCleanup()
        {
            // 注销对话事件监听
            UnregisterTalkEventListener();
        }

        protected override void OnTick(float deltaTime)
        {
            // 对话条件通常不需要每帧更新，主要通过事件驱动
        }

        protected override void OnProgressChanged(int oldProgress, int newProgress)
        {
            base.OnProgressChanged(oldProgress, newProgress);
            m_currentTalkCount = newProgress;
        }

        protected override void OnCompleted()
        {
            base.OnCompleted();
            Debug.Log($"[CompleteConditionTalkV2] 对话条件完成: 与NPC {m_targetNpcId} 对话了 {m_currentTalkCount} 次");
        }

        public override string GetProgressDescription()
        {
            return $"{m_currentTalkCount}/{m_requiredTalkCount} 次对话";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 处理对话事件
        /// </summary>
        /// <param name="npcId">NPC ID</param>
        /// <param name="dialogId">对话ID</param>
        public void OnTalkEvent(int npcId, int dialogId = -1)
        {
            if (m_isCompleted || npcId != m_targetNpcId)
                return;

            // 检查是否需要特定对话ID
            if (m_talkConfig.DialogueId > 0 && dialogId != m_talkConfig.DialogueId)
                return;

            AddProgress(1);
            Debug.Log($"[CompleteConditionTalkV2] 与NPC {npcId} 对话，进度: {m_currentTalkCount}/{m_requiredTalkCount}");
        }

        /// <summary>
        /// 检查是否为目标NPC
        /// </summary>
        /// <param name="npcId">NPC ID</param>
        /// <returns>是否为目标NPC</returns>
        public bool IsTargetNpc(int npcId)
        {
            return npcId == m_targetNpcId;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 注册对话事件监听
        /// </summary>
        private void RegisterTalkEventListener()
        {
            // 这里应该注册到游戏的对话事件系统
            // 例如：GameEventManager.RegisterListener(GameEventType.NpcTalk, OnGameTalkEvent);
            Debug.Log($"[CompleteConditionTalkV2] 注册对话事件监听: NPC {m_targetNpcId}");
        }

        /// <summary>
        /// 注销对话事件监听
        /// </summary>
        private void UnregisterTalkEventListener()
        {
            // 这里应该从游戏的对话事件系统注销
            // 例如：GameEventManager.UnregisterListener(GameEventType.NpcTalk, OnGameTalkEvent);
            Debug.Log($"[CompleteConditionTalkV2] 注销对话事件监听: NPC {m_targetNpcId}");
        }

        /// <summary>
        /// 游戏对话事件回调
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnGameTalkEvent(object eventArgs)
        {
            // 解析事件参数并调用OnTalkEvent
            // 这里需要根据实际的事件系统来实现
        }

        #endregion
    }
}
