using Phoenix.ConfigData.QuestGraph;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 到达位置完成条件 V2
    /// 需要到达指定位置
    /// </summary>
    public class CompleteConditionReachV2 : CompleteConditionV2
    {
        #region Private Fields

        /// <summary>
        /// 到达条件配置
        /// </summary>
        private ConfigDataQuestCompleteConditionReach m_reachConfig;

        /// <summary>
        /// 目标位置
        /// </summary>
        private Vector3 m_targetPosition;

        /// <summary>
        /// 到达范围（半径）
        /// </summary>
        private float m_reachRadius;

        /// <summary>
        /// 玩家当前位置
        /// </summary>
        private Vector3 m_playerPosition;

        /// <summary>
        /// 检查间隔时间
        /// </summary>
        private float m_checkInterval = 0.5f;

        /// <summary>
        /// 上次检查时间
        /// </summary>
        private float m_lastCheckTime = 0f;

        #endregion

        #region Properties

        /// <summary>
        /// 目标位置
        /// </summary>
        public Vector3 TargetPosition => m_targetPosition;

        /// <summary>
        /// 到达范围
        /// </summary>
        public float ReachRadius => m_reachRadius;

        /// <summary>
        /// 当前距离
        /// </summary>
        public float CurrentDistance => Vector3.Distance(m_playerPosition, m_targetPosition);

        #endregion

        #region CompleteConditionV2 Override

        protected override void OnInitialize()
        {
            m_reachConfig = m_conditionConfig as ConfigDataQuestCompleteConditionReach;
            if (m_reachConfig == null)
            {
                Debug.LogError("[CompleteConditionReachV2] 无效的到达条件配置");
                return;
            }

            m_targetPosition = new Vector3(m_reachConfig.PositionX, m_reachConfig.PositionY, m_reachConfig.PositionZ);
            m_reachRadius = Mathf.Max(0.1f, m_reachConfig.ReachRadius);
            m_maxProgress = 1; // 到达条件只有完成/未完成两种状态
            m_currentProgress = 0;

            m_description = $"到达位置 ({m_targetPosition.x:F1}, {m_targetPosition.y:F1}, {m_targetPosition.z:F1})";

            // 获取玩家初始位置
            UpdatePlayerPosition();

            Debug.Log($"[CompleteConditionReachV2] 初始化到达条件: {m_description}, 范围: {m_reachRadius}");
        }

        protected override void OnTick(float deltaTime)
        {
            // 定期检查玩家位置
            m_lastCheckTime += deltaTime;
            if (m_lastCheckTime >= m_checkInterval)
            {
                m_lastCheckTime = 0f;
                CheckPlayerPosition();
            }
        }

        protected override void OnCompleted()
        {
            base.OnCompleted();
            Debug.Log($"[CompleteConditionReachV2] 到达条件完成: 玩家到达了目标位置 {m_targetPosition}");
        }

        public override string GetProgressDescription()
        {
            if (m_isCompleted)
                return "已到达";

            float distance = CurrentDistance;
            return $"距离: {distance:F1}m (需要: {m_reachRadius:F1}m)";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 手动更新玩家位置
        /// </summary>
        /// <param name="playerPosition">玩家位置</param>
        public void UpdatePlayerPosition(Vector3 playerPosition)
        {
            m_playerPosition = playerPosition;
            CheckReachCondition();
        }

        /// <summary>
        /// 检查是否在目标范围内
        /// </summary>
        /// <returns>是否在范围内</returns>
        public bool IsInRange()
        {
            return CurrentDistance <= m_reachRadius;
        }

        /// <summary>
        /// 获取到目标的方向向量
        /// </summary>
        /// <returns>方向向量</returns>
        public Vector3 GetDirectionToTarget()
        {
            return (m_targetPosition - m_playerPosition).normalized;
        }

        /// <summary>
        /// 获取到目标的距离百分比（基于初始距离）
        /// </summary>
        /// <returns>距离百分比</returns>
        public float GetDistancePercentage()
        {
            // 这里可以基于初始距离计算百分比
            // 暂时返回简单的范围内/范围外状态
            return IsInRange() ? 100f : 0f;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 更新玩家位置
        /// </summary>
        private void UpdatePlayerPosition()
        {
            // 这里应该从游戏系统获取玩家位置
            // 例如：从PlayerController、GameManager等获取
            // 暂时使用默认位置
            m_playerPosition = Vector3.zero;

            // 实际实现示例：
            // var player = GameManager.Instance.GetPlayer();
            // if (player != null)
            // {
            //     m_playerPosition = player.transform.position;
            // }
        }

        /// <summary>
        /// 检查玩家位置
        /// </summary>
        private void CheckPlayerPosition()
        {
            UpdatePlayerPosition();
            CheckReachCondition();
        }

        /// <summary>
        /// 检查到达条件
        /// </summary>
        private void CheckReachCondition()
        {
            if (m_isCompleted)
                return;

            if (IsInRange())
            {
                SetProgress(1);
            }
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 在Scene视图中绘制调试信息
        /// </summary>
        public void DrawDebugGizmos()
        {
            if (!m_initialized)
                return;

            // 绘制目标位置
            Gizmos.color = m_isCompleted ? Color.green : Color.yellow;
            Gizmos.DrawWireSphere(m_targetPosition, m_reachRadius);

            // 绘制玩家位置
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(m_playerPosition, Vector3.one * 0.5f);

            // 绘制连线
            Gizmos.color = IsInRange() ? Color.green : Color.red;
            Gizmos.DrawLine(m_playerPosition, m_targetPosition);

            // 绘制距离文本
            #if UNITY_EDITOR
            var midPoint = (m_playerPosition + m_targetPosition) * 0.5f;
            UnityEditor.Handles.Label(midPoint, $"距离: {CurrentDistance:F1}m");
            #endif
        }

        #endregion
    }
}
