using Phoenix.ConfigData.QuestGraph;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 击杀完成条件 V2
    /// 需要击杀指定数量的敌人
    /// </summary>
    public class CompleteConditionKillV2 : CompleteConditionV2
    {
        #region Private Fields

        /// <summary>
        /// 击杀条件配置
        /// </summary>
        private ConfigDataQuestCompleteConditionKillMonster m_killConfig;

        /// <summary>
        /// 目标敌人ID（-1表示任意敌人）
        /// </summary>
        private int m_targetEnemyId;

        /// <summary>
        /// 需要击杀数量
        /// </summary>
        private int m_requiredKillCount;

        /// <summary>
        /// 当前击杀数量
        /// </summary>
        private int m_currentKillCount = 0;

        /// <summary>
        /// 是否只计算特定敌人
        /// </summary>
        private bool m_specificEnemyOnly;

        #endregion

        #region Properties

        /// <summary>
        /// 目标敌人ID
        /// </summary>
        public int TargetEnemyId => m_targetEnemyId;

        /// <summary>
        /// 需要击杀数量
        /// </summary>
        public int RequiredKillCount => m_requiredKillCount;

        /// <summary>
        /// 当前击杀数量
        /// </summary>
        public int CurrentKillCount => m_currentKillCount;

        /// <summary>
        /// 是否只计算特定敌人
        /// </summary>
        public bool SpecificEnemyOnly => m_specificEnemyOnly;

        #endregion

        #region CompleteConditionV2 Override

        protected override void OnInitialize()
        {
            m_killConfig = m_conditionConfig as ConfigDataQuestCompleteConditionKillMonster;
            if (m_killConfig == null)
            {
                Debug.LogError("[CompleteConditionKillV2] 无效的击杀条件配置");
                return;
            }

            m_targetEnemyId = m_killConfig.MonsterEntityId;
            m_requiredKillCount = 1;
            m_specificEnemyOnly = m_targetEnemyId > 0;
            m_maxProgress = m_requiredKillCount;
            m_currentProgress = 0;

            if (m_specificEnemyOnly)
            {
                m_description = $"击杀敌人 {m_targetEnemyId} × {m_requiredKillCount}";
            }
            else
            {
                m_description = $"击杀任意敌人 × {m_requiredKillCount}";
            }

            // 注册击杀事件监听
            RegisterKillEventListener();

            Debug.Log($"[CompleteConditionKillV2] 初始化击杀条件: {m_description}");
        }

        protected override void OnCleanup()
        {
            // 注销击杀事件监听
            UnregisterKillEventListener();
        }

        protected override void OnTick(float deltaTime)
        {
            // 击杀条件通常不需要每帧更新，主要通过事件驱动
        }

        protected override void OnProgressChanged(int oldProgress, int newProgress)
        {
            base.OnProgressChanged(oldProgress, newProgress);
            m_currentKillCount = newProgress;
        }

        protected override void OnCompleted()
        {
            base.OnCompleted();
            
            if (m_specificEnemyOnly)
            {
                Debug.Log($"[CompleteConditionKillV2] 击杀条件完成: 击杀了 {m_currentKillCount} 个敌人 {m_targetEnemyId}");
            }
            else
            {
                Debug.Log($"[CompleteConditionKillV2] 击杀条件完成: 击杀了 {m_currentKillCount} 个敌人");
            }
        }

        public override string GetProgressDescription()
        {
            return $"{m_currentKillCount}/{m_requiredKillCount} 击杀";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 处理击杀事件
        /// </summary>
        /// <param name="enemyId">敌人ID</param>
        /// <param name="killerId">击杀者ID</param>
        public void OnKillEvent(int enemyId, int killerId = -1)
        {
            if (m_isCompleted)
                return;

            // 检查是否为目标敌人
            if (m_specificEnemyOnly && enemyId != m_targetEnemyId)
                return;

            AddProgress(1);
            Debug.Log($"[CompleteConditionKillV2] 击杀敌人 {enemyId}，进度: {m_currentKillCount}/{m_requiredKillCount}");
        }

        /// <summary>
        /// 批量处理击杀事件
        /// </summary>
        /// <param name="enemyId">敌人ID</param>
        /// <param name="killCount">击杀数量</param>
        /// <param name="killerId">击杀者ID</param>
        public void OnBatchKillEvent(int enemyId, int killCount, int killerId = -1)
        {
            if (m_isCompleted || killCount <= 0)
                return;

            // 检查是否为目标敌人
            if (m_specificEnemyOnly && enemyId != m_targetEnemyId)
                return;

            AddProgress(killCount);
            Debug.Log($"[CompleteConditionKillV2] 批量击杀敌人 {enemyId} × {killCount}，进度: {m_currentKillCount}/{m_requiredKillCount}");
        }

        /// <summary>
        /// 检查是否为目标敌人
        /// </summary>
        /// <param name="enemyId">敌人ID</param>
        /// <returns>是否为目标敌人</returns>
        public bool IsTargetEnemy(int enemyId)
        {
            return !m_specificEnemyOnly || enemyId == m_targetEnemyId;
        }

        /// <summary>
        /// 获取剩余击杀数量
        /// </summary>
        /// <returns>剩余击杀数量</returns>
        public int GetRemainingKillCount()
        {
            return Mathf.Max(0, m_requiredKillCount - m_currentKillCount);
        }


        #endregion

        #region Private Methods

        /// <summary>
        /// 检查是否为玩家击杀
        /// </summary>
        /// <param name="killerId">击杀者ID</param>
        /// <returns>是否为玩家击杀</returns>
        private bool IsPlayerKill(int killerId)
        {
            // 这里应该检查击杀者是否为玩家
            // 例如：return GameManager.Instance.IsPlayer(killerId);
            // 暂时返回true，表示所有击杀都算作玩家击杀
            return true;
        }

        /// <summary>
        /// 注册击杀事件监听
        /// </summary>
        private void RegisterKillEventListener()
        {
            // 这里应该注册到游戏的击杀事件系统
            // 例如：GameEventManager.RegisterListener(GameEventType.EnemyKilled, OnGameKillEvent);
            Debug.Log($"[CompleteConditionKillV2] 注册击杀事件监听: 敌人 {m_targetEnemyId}");
        }

        /// <summary>
        /// 注销击杀事件监听
        /// </summary>
        private void UnregisterKillEventListener()
        {
            // 这里应该从游戏的击杀事件系统注销
            // 例如：GameEventManager.UnregisterListener(GameEventType.EnemyKilled, OnGameKillEvent);
            Debug.Log($"[CompleteConditionKillV2] 注销击杀事件监听: 敌人 {m_targetEnemyId}");
        }

        /// <summary>
        /// 游戏击杀事件回调
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnGameKillEvent(object eventArgs)
        {
            // 解析事件参数并调用OnKillEvent
            // 这里需要根据实际的事件系统来实现
            // 例如：
            // if (eventArgs is KillEventArgs killArgs)
            // {
            //     OnKillEvent(killArgs.EnemyId, killArgs.KillerId);
            // }
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 获取详细的击杀统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public string GetKillStatistics()
        {
            var stats = $"击杀统计:\n";
            stats += $"  目标: {(m_specificEnemyOnly ? $"敌人 {m_targetEnemyId}" : "任意敌人")}\n";
            stats += $"  进度: {m_currentKillCount}/{m_requiredKillCount}\n";
            stats += $"  剩余: {GetRemainingKillCount()}\n";
            stats += $"  完成率: {GetProgressPercentage():F1}%\n";
            stats += $"  状态: {(m_isCompleted ? "已完成" : "进行中")}";
            
            return stats;
        }

        #endregion
    }
}
