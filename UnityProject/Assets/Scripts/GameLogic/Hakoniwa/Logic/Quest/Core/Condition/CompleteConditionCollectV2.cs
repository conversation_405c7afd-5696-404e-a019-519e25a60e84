using Phoenix.ConfigData.QuestGraph;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 收集完成条件 V2
    /// 需要收集指定数量的物品
    /// </summary>
    public class CompleteConditionCollectV2 : CompleteConditionV2
    {
        #region Private Fields

        /// <summary>
        /// 收集条件配置
        /// </summary>
        //private ConfigDataQuestCompleteConditionCollect m_collectConfig;

        /// <summary>
        /// 目标物品ID
        /// </summary>
        private int m_targetItemId;

        /// <summary>
        /// 需要收集数量
        /// </summary>
        private int m_requiredCollectCount;

        /// <summary>
        /// 当前收集数量
        /// </summary>
        private int m_currentCollectCount = 0;

        /// <summary>
        /// 是否消耗物品（完成任务时是否从背包中移除）
        /// </summary>
        private bool m_consumeItem;

        /// <summary>
        /// 检查间隔时间
        /// </summary>
        private float m_checkInterval = 1f;

        /// <summary>
        /// 上次检查时间
        /// </summary>
        private float m_lastCheckTime = 0f;

        #endregion

        #region Properties

        /// <summary>
        /// 目标物品ID
        /// </summary>
        public int TargetItemId => m_targetItemId;

        /// <summary>
        /// 需要收集数量
        /// </summary>
        public int RequiredCollectCount => m_requiredCollectCount;

        /// <summary>
        /// 当前收集数量
        /// </summary>
        public int CurrentCollectCount => m_currentCollectCount;

        /// <summary>
        /// 是否消耗物品
        /// </summary>
        public bool ConsumeItem => m_consumeItem;

        #endregion

        #region CompleteConditionV2 Override

        protected override void OnInitialize()
        {
            //m_collectConfig = m_conditionConfig as ConfigDataQuestCompleteConditionCollect;
            //if (m_collectConfig == null)
            //{
            //    Debug.LogError("[CompleteConditionCollectV2] 无效的收集条件配置");
            //    return;
            //}

            //m_targetItemId = m_collectConfig.TargetItemId;
            //m_requiredCollectCount = Mathf.Max(1, m_collectConfig.CollectCount);
            //m_consumeItem = m_collectConfig.ConsumeItem;
            m_maxProgress = m_requiredCollectCount;
            m_currentProgress = 0;

            m_description = $"收集物品 {m_targetItemId} × {m_requiredCollectCount}";
            if (m_consumeItem)
            {
                m_description += " (消耗)";
            }

            // 注册物品事件监听
            RegisterItemEventListener();

            // 初始检查背包中的物品数量
            CheckInventoryItems();

            Debug.Log($"[CompleteConditionCollectV2] 初始化收集条件: {m_description}");
        }

        protected override void OnCleanup()
        {
            // 注销物品事件监听
            UnregisterItemEventListener();
        }

        protected override void OnTick(float deltaTime)
        {
            // 定期检查背包中的物品数量
            m_lastCheckTime += deltaTime;
            if (m_lastCheckTime >= m_checkInterval)
            {
                m_lastCheckTime = 0f;
                CheckInventoryItems();
            }
        }

        protected override void OnProgressChanged(int oldProgress, int newProgress)
        {
            base.OnProgressChanged(oldProgress, newProgress);
            m_currentCollectCount = newProgress;
        }

        protected override void OnCompleted()
        {
            base.OnCompleted();
            
            // 如果需要消耗物品，从背包中移除
            if (m_consumeItem)
            {
                ConsumeItemsFromInventory();
            }
            
            Debug.Log($"[CompleteConditionCollectV2] 收集条件完成: 收集了 {m_currentCollectCount} 个物品 {m_targetItemId}");
        }

        public override string GetProgressDescription()
        {
            return $"{m_currentCollectCount}/{m_requiredCollectCount} 个物品";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 处理物品获得事件
        /// </summary>
        /// <param name="itemId">物品ID</param>
        /// <param name="count">获得数量</param>
        /// <param name="playerId">玩家ID</param>
        public void OnItemGainEvent(int itemId, int count, int playerId = -1)
        {
            if (itemId != m_targetItemId || count <= 0)
                return;

            Debug.Log($"[CompleteConditionCollectV2] 获得物品 {itemId} × {count}");
            
            // 重新检查背包中的物品数量
            CheckInventoryItems();
        }

        /// <summary>
        /// 处理物品失去事件
        /// </summary>
        /// <param name="itemId">物品ID</param>
        /// <param name="count">失去数量</param>
        /// <param name="playerId">玩家ID</param>
        public void OnItemLoseEvent(int itemId, int count, int playerId = -1)
        {
            if (itemId != m_targetItemId || count <= 0)
                return;

            Debug.Log($"[CompleteConditionCollectV2] 失去物品 {itemId} × {count}");
            
            // 重新检查背包中的物品数量
            CheckInventoryItems();
        }

        /// <summary>
        /// 手动更新收集进度
        /// </summary>
        /// <param name="currentCount">当前数量</param>
        public void UpdateCollectProgress(int currentCount)
        {
            SetProgress(Mathf.Min(currentCount, m_requiredCollectCount));
        }

        /// <summary>
        /// 检查是否为目标物品
        /// </summary>
        /// <param name="itemId">物品ID</param>
        /// <returns>是否为目标物品</returns>
        public bool IsTargetItem(int itemId)
        {
            return itemId == m_targetItemId;
        }

        /// <summary>
        /// 获取剩余需要收集的数量
        /// </summary>
        /// <returns>剩余收集数量</returns>
        public int GetRemainingCollectCount()
        {
            return Mathf.Max(0, m_requiredCollectCount - m_currentCollectCount);
        }

        /// <summary>
        /// 获取当前背包中的物品数量
        /// </summary>
        /// <returns>背包中的物品数量</returns>
        public int GetInventoryItemCount()
        {
            // 这里应该从游戏的背包系统获取物品数量
            // 例如：return InventoryManager.Instance.GetItemCount(m_targetItemId);
            // 暂时返回当前进度
            return m_currentCollectCount;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 检查背包中的物品数量
        /// </summary>
        private void CheckInventoryItems()
        {
            if (m_isCompleted)
                return;

            // 这里应该从游戏的背包系统获取实际的物品数量
            int inventoryCount = GetInventoryItemCountFromSystem();
            
            // 更新进度（不能超过需要的数量）
            int newProgress = Mathf.Min(inventoryCount, m_requiredCollectCount);
            if (newProgress != m_currentProgress)
            {
                SetProgress(newProgress);
            }
        }

        /// <summary>
        /// 从游戏系统获取背包中的物品数量
        /// </summary>
        /// <returns>物品数量</returns>
        private int GetInventoryItemCountFromSystem()
        {
            // 这里应该调用实际的背包系统
            // 例如：
            // var inventory = GameManager.Instance.GetPlayerInventory();
            // return inventory.GetItemCount(m_targetItemId);
            
            // 暂时返回当前进度（模拟）
            return m_currentCollectCount;
        }

        /// <summary>
        /// 从背包中消耗物品
        /// </summary>
        private void ConsumeItemsFromInventory()
        {
            if (!m_consumeItem || m_requiredCollectCount <= 0)
                return;

            // 这里应该调用实际的背包系统来移除物品
            // 例如：
            // var inventory = GameManager.Instance.GetPlayerInventory();
            // inventory.RemoveItem(m_targetItemId, m_requiredCollectCount);
            
            Debug.Log($"[CompleteConditionCollectV2] 消耗物品 {m_targetItemId} × {m_requiredCollectCount}");
        }

        /// <summary>
        /// 注册物品事件监听
        /// </summary>
        private void RegisterItemEventListener()
        {
            // 这里应该注册到游戏的物品事件系统
            // 例如：
            // GameEventManager.RegisterListener(GameEventType.ItemGain, OnGameItemGainEvent);
            // GameEventManager.RegisterListener(GameEventType.ItemLose, OnGameItemLoseEvent);
            Debug.Log($"[CompleteConditionCollectV2] 注册物品事件监听: 物品 {m_targetItemId}");
        }

        /// <summary>
        /// 注销物品事件监听
        /// </summary>
        private void UnregisterItemEventListener()
        {
            // 这里应该从游戏的物品事件系统注销
            // 例如：
            // GameEventManager.UnregisterListener(GameEventType.ItemGain, OnGameItemGainEvent);
            // GameEventManager.UnregisterListener(GameEventType.ItemLose, OnGameItemLoseEvent);
            Debug.Log($"[CompleteConditionCollectV2] 注销物品事件监听: 物品 {m_targetItemId}");
        }

        /// <summary>
        /// 游戏物品获得事件回调
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnGameItemGainEvent(object eventArgs)
        {
            // 解析事件参数并调用OnItemGainEvent
            // 这里需要根据实际的事件系统来实现
        }

        /// <summary>
        /// 游戏物品失去事件回调
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnGameItemLoseEvent(object eventArgs)
        {
            // 解析事件参数并调用OnItemLoseEvent
            // 这里需要根据实际的事件系统来实现
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 获取详细的收集统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public string GetCollectStatistics()
        {
            var stats = $"收集统计:\n";
            stats += $"  目标物品: {m_targetItemId}\n";
            stats += $"  需要数量: {m_requiredCollectCount}\n";
            stats += $"  当前进度: {m_currentCollectCount}\n";
            stats += $"  背包数量: {GetInventoryItemCount()}\n";
            stats += $"  剩余需要: {GetRemainingCollectCount()}\n";
            stats += $"  完成率: {GetProgressPercentage():F1}%\n";
            stats += $"  消耗物品: {(m_consumeItem ? "是" : "否")}\n";
            stats += $"  状态: {(m_isCompleted ? "已完成" : "进行中")}";
            
            return stats;
        }

        #endregion
    }
}
