using System;
using System.Collections.Generic;
using System.Linq;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 任务模板 V2
    /// 重构后的任务实例类，提供完整的任务逻辑处理
    /// </summary>
    public class QuestTemplateV2 : ClassPoolObj
    {
        #region Private Fields

        /// <summary>
        /// 任务配置
        /// </summary>
        private ConfigDataQuestNodeTemplate m_questConfig;

        /// <summary>
        /// 任务模块引用
        /// </summary>
        private HakoniwaQuestModuleV2 m_questModule;

        /// <summary>
        /// 完成条件列表
        /// </summary>
        private List<CompleteConditionV2> m_conditions = new List<CompleteConditionV2>();

        /// <summary>
        /// 条件进度数组
        /// </summary>
        private int[] m_conditionProgress;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        private bool m_initialized = false;

        /// <summary>
        /// 是否已完成
        /// </summary>
        private bool m_isCompleted = false;

        /// <summary>
        /// 是否强制完成
        /// </summary>
        private bool m_forceCompleted = false;

        /// <summary>
        /// 任务开始时间
        /// </summary>
        private DateTime m_startTime;

        /// <summary>
        /// 任务完成时间
        /// </summary>
        private DateTime m_completeTime;

        #endregion

        #region Properties

        /// <summary>
        /// 任务ID
        /// </summary>
        public int QuestId => m_questConfig?.QuestId ?? -1;

        /// <summary>
        /// 任务名称
        /// </summary>
        public string QuestName => m_questConfig?.QuestName ?? "Unknown Quest";

        /// <summary>
        /// 任务描述
        /// </summary>
        public string QuestDescription => m_questConfig?.QuestDesc ?? "";

        /// <summary>
        /// 任务配置
        /// </summary>
        public ConfigDataQuestNodeTemplate QuestConfig => m_questConfig;

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted => m_isCompleted;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => m_initialized;

        /// <summary>
        /// 完成条件数量
        /// </summary>
        public int ConditionCount => m_conditions.Count;

        /// <summary>
        /// 任务运行时长（秒）
        /// </summary>
        public double RunningTime
        {
            get
            {
                if (!m_initialized) return 0;
                var endTime = m_isCompleted ? m_completeTime : DateTime.Now;
                return (endTime - m_startTime).TotalSeconds;
            }
        }

        #endregion

        #region ClassPoolObj Override

        public override void OnRelease()
        {
            // 清理完成条件
            foreach (var condition in m_conditions)
            {
                condition?.Release();
            }
            m_conditions.Clear();

            // 重置状态
            m_questConfig = null;
            m_questModule = null;
            m_conditionProgress = null;
            m_initialized = false;
            m_isCompleted = false;
            m_forceCompleted = false;
            m_startTime = default;
            m_completeTime = default;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 初始化任务
        /// </summary>
        /// <param name="config">任务配置</param>
        /// <param name="questModule">任务模块</param>
        public void Init(ConfigDataQuestNodeTemplate config, HakoniwaQuestModuleV2 questModule)
        {
            if (m_initialized)
            {
                Debug.LogWarning($"[QuestTemplateV2] 任务已初始化: {QuestId}");
                return;
            }

            m_questConfig = config;
            m_questModule = questModule;
            m_startTime = DateTime.Now;

            // 初始化完成条件
            InitializeConditions();

            // 初始化进度数组
            m_conditionProgress = new int[m_conditions.Count];

            m_initialized = true;

            Debug.Log($"[QuestTemplateV2] 任务初始化完成: {QuestName} ({QuestId})");
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        public void Tick(float deltaTime)
        {
            if (!m_initialized || m_isCompleted)
                return;

            // 更新所有完成条件
            for (int i = 0; i < m_conditions.Count; i++)
            {
                var condition = m_conditions[i];
                condition.Tick(deltaTime);

                // 检查条件进度是否有变化
                int newProgress = condition.GetCurrentProgress();
                if (newProgress != m_conditionProgress[i])
                {
                    int oldProgress = m_conditionProgress[i];
                    m_conditionProgress[i] = newProgress;

                    // 触发进度更新事件
                    OnConditionProgressChanged(i, oldProgress, newProgress);
                }
            }

            // 检查任务是否完成
            CheckQuestCompletion();
        }

        /// <summary>
        /// 更新指定条件的进度
        /// </summary>
        /// <param name="conditionIndex">条件索引</param>
        /// <param name="progress">进度值</param>
        public void UpdateConditionProgress(int conditionIndex, int progress)
        {
            if (conditionIndex < 0 || conditionIndex >= m_conditions.Count)
            {
                Debug.LogWarning($"[QuestTemplateV2] 无效的条件索引: {conditionIndex}");
                return;
            }

            var condition = m_conditions[conditionIndex];
            condition.SetProgress(progress);

            int oldProgress = m_conditionProgress[conditionIndex];
            m_conditionProgress[conditionIndex] = progress;

            // 触发进度更新事件
            OnConditionProgressChanged(conditionIndex, oldProgress, progress);

            // 检查任务是否完成
            CheckQuestCompletion();
        }

        /// <summary>
        /// 强制完成任务
        /// </summary>
        public void ForceComplete()
        {
            if (m_isCompleted)
                return;

            m_forceCompleted = true;
            
            // 强制完成所有条件
            for (int i = 0; i < m_conditions.Count; i++)
            {
                var condition = m_conditions[i];
                condition.ForceComplete();
                m_conditionProgress[i] = condition.GetMaxProgress();
            }

            CompleteQuest();
            Debug.Log($"[QuestTemplateV2] 强制完成任务: {QuestName} ({QuestId})");
        }

        /// <summary>
        /// 获取任务进度信息
        /// </summary>
        /// <returns>进度信息</returns>
        public QuestProgressInfo GetProgressInfo()
        {
            int completedConditions = 0;
            int totalConditions = m_conditions.Count;

            for (int i = 0; i < m_conditions.Count; i++)
            {
                var condition = m_conditions[i];
                if (condition.IsCompleted())
                {
                    completedConditions++;
                }
            }

            var progressInfo = new QuestProgressInfo
            {
                QuestId = QuestId,
                QuestName = QuestName,
                TotalConditions = totalConditions,
                CompletedConditions = completedConditions,
                ProgressPercentage = totalConditions > 0 ? (float)completedConditions / totalConditions * 100f : 0f
            };

            // 填充条件进度详情
            for (int i = 0; i < m_conditionProgress.Length; i++)
            {
                progressInfo.ConditionProgress[i] = m_conditionProgress[i];
            }

            return progressInfo;
        }

        /// <summary>
        /// 获取指定条件的进度
        /// </summary>
        /// <param name="conditionIndex">条件索引</param>
        /// <returns>进度值</returns>
        public int GetConditionProgress(int conditionIndex)
        {
            if (conditionIndex < 0 || conditionIndex >= m_conditionProgress.Length)
                return 0;

            return m_conditionProgress[conditionIndex];
        }

        /// <summary>
        /// 获取指定条件的最大进度
        /// </summary>
        /// <param name="conditionIndex">条件索引</param>
        /// <returns>最大进度值</returns>
        public int GetConditionMaxProgress(int conditionIndex)
        {
            if (conditionIndex < 0 || conditionIndex >= m_conditions.Count)
                return 0;

            return m_conditions[conditionIndex].GetMaxProgress();
        }

        /// <summary>
        /// 检查指定条件是否完成
        /// </summary>
        /// <param name="conditionIndex">条件索引</param>
        /// <returns>是否完成</returns>
        public bool IsConditionCompleted(int conditionIndex)
        {
            if (conditionIndex < 0 || conditionIndex >= m_conditions.Count)
                return false;

            return m_conditions[conditionIndex].IsCompleted();
        }

        /// <summary>
        /// 获取任务完成百分比
        /// </summary>
        /// <returns>完成百分比 (0-100)</returns>
        public float GetCompletionPercentage()
        {
            if (m_conditions.Count == 0)
                return 100f;

            int completedCount = m_conditions.Count(c => c.IsCompleted());
            return (float)completedCount / m_conditions.Count * 100f;
        }

        /// <summary>
        /// 获取任务状态描述
        /// </summary>
        /// <returns>状态描述</returns>
        public string GetStatusDescription()
        {
            if (!m_initialized)
                return "未初始化";

            if (m_isCompleted)
                return m_forceCompleted ? "强制完成" : "已完成";

            var progressInfo = GetProgressInfo();
            return $"进行中 ({progressInfo.CompletedConditions}/{progressInfo.TotalConditions})";
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 初始化完成条件
        /// </summary>
        private void InitializeConditions()
        {
            if (m_questConfig?.CompleteConditions == null)
                return;

            foreach (var conditionConfig in m_questConfig.CompleteConditions)
            {
                var condition = CompleteConditionV2.CreateCondition(conditionConfig, this);
                if (condition != null)
                {
                    m_conditions.Add(condition);
                }
                else
                {
                    Debug.LogError($"[QuestTemplateV2] 无法创建完成条件: {conditionConfig.GetType().Name}");
                }
            }

            Debug.Log($"[QuestTemplateV2] 初始化了 {m_conditions.Count} 个完成条件");
        }

        /// <summary>
        /// 检查任务完成状态
        /// </summary>
        private void CheckQuestCompletion()
        {
            if (m_isCompleted)
                return;

            bool isCompleted = false;

            // 根据条件关系模式检查完成状态
            switch (m_questConfig.QuestConditionMode)
            {
                case LogicalMode.And:
                    // 所有条件都必须完成
                    isCompleted = m_conditions.All(c => c.IsCompleted());
                    break;

                case LogicalMode.Or:
                    // 任意一个条件完成即可
                    isCompleted = m_conditions.Any(c => c.IsCompleted());
                    break;

                default:
                    // 默认使用AND模式
                    isCompleted = m_conditions.All(c => c.IsCompleted());
                    break;
            }

            if (isCompleted)
            {
                CompleteQuest();
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        private void CompleteQuest()
        {
            if (m_isCompleted)
                return;

            m_isCompleted = true;
            m_completeTime = DateTime.Now;

            Debug.Log($"[QuestTemplateV2] 任务完成: {QuestName} ({QuestId}), 耗时: {RunningTime:F1}秒");
        }

        /// <summary>
        /// 条件进度变化回调
        /// </summary>
        /// <param name="conditionIndex">条件索引</param>
        /// <param name="oldProgress">旧进度</param>
        /// <param name="newProgress">新进度</param>
        private void OnConditionProgressChanged(int conditionIndex, int oldProgress, int newProgress)
        {
            // 触发任务模块的进度更新事件
            m_questModule?.UpdateQuestProgress(QuestId, conditionIndex, newProgress);

            Debug.Log($"[QuestTemplateV2] 任务 {QuestName} 条件 {conditionIndex} 进度更新: {oldProgress} -> {newProgress}");
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 打印调试信息
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void PrintDebugInfo()
        {
            Debug.Log($"=== QuestTemplateV2 Debug Info: {QuestName} ({QuestId}) ===");
            Debug.Log($"状态: {GetStatusDescription()}");
            Debug.Log($"完成百分比: {GetCompletionPercentage():F1}%");
            Debug.Log($"运行时长: {RunningTime:F1}秒");
            Debug.Log($"条件数量: {m_conditions.Count}");

            for (int i = 0; i < m_conditions.Count; i++)
            {
                var condition = m_conditions[i];
                var progress = m_conditionProgress[i];
                var maxProgress = condition.GetMaxProgress();
                var isCompleted = condition.IsCompleted();

                Debug.Log($"  条件 {i}: {condition.GetType().Name} - {progress}/{maxProgress} {(isCompleted ? "[完成]" : "[未完成]")}");
            }

            Debug.Log("=====================================");
        }

        #endregion
    }
}
