using Phoenix.ConfigData.QuestGraph;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 战斗完成条件 V2
    /// 需要完成指定的战斗
    /// </summary>
    public class CompleteConditionBattleV2 : CompleteConditionV2
    {
        #region Private Fields

        /// <summary>
        /// 战斗条件配置
        /// </summary>
        private ConfigDataQuestCompleteConditionBattle m_battleConfig;

        /// <summary>
        /// 目标战斗ID
        /// </summary>
        private int m_targetBattleId;

        /// <summary>
        /// 需要胜利次数
        /// </summary>
        private int m_requiredWinCount;

        /// <summary>
        /// 当前胜利次数
        /// </summary>
        private int m_currentWinCount = 0;

        /// <summary>
        /// 是否要求必须胜利
        /// </summary>
        private bool m_mustWin;

        /// <summary>
        /// 是否允许重复战斗
        /// </summary>
        private bool m_allowRepeat;

        #endregion

        #region Properties

        /// <summary>
        /// 目标战斗ID
        /// </summary>
        public int TargetBattleId => m_targetBattleId;

        /// <summary>
        /// 需要胜利次数
        /// </summary>
        public int RequiredWinCount => m_requiredWinCount;

        /// <summary>
        /// 当前胜利次数
        /// </summary>
        public int CurrentWinCount => m_currentWinCount;

        /// <summary>
        /// 是否要求必须胜利
        /// </summary>
        public bool MustWin => m_mustWin;

        #endregion

        #region CompleteConditionV2 Override

        protected override void OnInitialize()
        {
            m_battleConfig = m_conditionConfig as ConfigDataQuestCompleteConditionBattle;
            if (m_battleConfig == null)
            {
                Debug.LogError("[CompleteConditionBattleV2] 无效的战斗条件配置");
                return;
            }

            m_targetBattleId = m_battleConfig.LevelId;
            //m_requiredWinCount = Mathf.Max(1, m_battleConfig.WinCount);
            //m_mustWin = m_battleConfig.MustWin;
            m_allowRepeat = true;
            m_maxProgress = m_requiredWinCount;
            m_currentProgress = 0;

            if (m_mustWin)
            {
                m_description = $"胜利战斗 {m_targetBattleId} × {m_requiredWinCount}";
            }
            else
            {
                m_description = $"参与战斗 {m_targetBattleId} × {m_requiredWinCount}";
            }

            // 注册战斗事件监听
            RegisterBattleEventListener();

            Debug.Log($"[CompleteConditionBattleV2] 初始化战斗条件: {m_description}");
        }

        protected override void OnCleanup()
        {
            // 注销战斗事件监听
            UnregisterBattleEventListener();
        }

        protected override void OnTick(float deltaTime)
        {
            // 战斗条件通常不需要每帧更新，主要通过事件驱动
        }

        protected override void OnProgressChanged(int oldProgress, int newProgress)
        {
            base.OnProgressChanged(oldProgress, newProgress);
            m_currentWinCount = newProgress;
        }

        protected override void OnCompleted()
        {
            base.OnCompleted();
            Debug.Log($"[CompleteConditionBattleV2] 战斗条件完成: 完成了 {m_currentWinCount} 次战斗 {m_targetBattleId}");
        }

        public override string GetProgressDescription()
        {
            string progressType = m_mustWin ? "胜利" : "参与";
            return $"{m_currentWinCount}/{m_requiredWinCount} 次{progressType}";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 处理战斗开始事件
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="playerId">玩家ID</param>
        public void OnBattleStartEvent(int battleId, int playerId = -1)
        {
            if (battleId != m_targetBattleId)
                return;

            Debug.Log($"[CompleteConditionBattleV2] 战斗开始: {battleId}");
        }

        /// <summary>
        /// 处理战斗结束事件
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="isWin">是否胜利</param>
        /// <param name="playerId">玩家ID</param>
        public void OnBattleEndEvent(int battleId, bool isWin, int playerId = -1)
        {
            if (m_isCompleted || battleId != m_targetBattleId)
                return;

            // 如果要求必须胜利，但实际失败了，则不计入进度
            if (m_mustWin && !isWin)
            {
                Debug.Log($"[CompleteConditionBattleV2] 战斗失败，不计入进度: {battleId}");
                return;
            }

            // 如果不允许重复，检查是否已经完成过这个战斗
            if (!m_allowRepeat && m_currentWinCount > 0)
            {
                Debug.Log($"[CompleteConditionBattleV2] 不允许重复战斗: {battleId}");
                return;
            }

            AddProgress(1);
            
            string result = isWin ? "胜利" : "失败";
            Debug.Log($"[CompleteConditionBattleV2] 战斗{result}: {battleId}，进度: {m_currentWinCount}/{m_requiredWinCount}");
        }

        /// <summary>
        /// 检查是否为目标战斗
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <returns>是否为目标战斗</returns>
        public bool IsTargetBattle(int battleId)
        {
            return battleId == m_targetBattleId;
        }

        /// <summary>
        /// 获取剩余需要的战斗次数
        /// </summary>
        /// <returns>剩余战斗次数</returns>
        public int GetRemainingBattleCount()
        {
            return Mathf.Max(0, m_requiredWinCount - m_currentWinCount);
        }

        /// <summary>
        /// 获取战斗要求描述
        /// </summary>
        /// <returns>要求描述</returns>
        public string GetBattleRequirement()
        {
            string requirement = $"战斗 {m_targetBattleId}";
            
            if (m_mustWin)
                requirement += " (必须胜利)";
            
            if (!m_allowRepeat)
                requirement += " (不可重复)";
                
            return requirement;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 注册战斗事件监听
        /// </summary>
        private void RegisterBattleEventListener()
        {
            // 这里应该注册到游戏的战斗事件系统
            // 例如：
            // GameEventManager.RegisterListener(GameEventType.BattleStart, OnGameBattleStartEvent);
            // GameEventManager.RegisterListener(GameEventType.BattleEnd, OnGameBattleEndEvent);
            Debug.Log($"[CompleteConditionBattleV2] 注册战斗事件监听: 战斗 {m_targetBattleId}");
        }

        /// <summary>
        /// 注销战斗事件监听
        /// </summary>
        private void UnregisterBattleEventListener()
        {
            // 这里应该从游戏的战斗事件系统注销
            // 例如：
            // GameEventManager.UnregisterListener(GameEventType.BattleStart, OnGameBattleStartEvent);
            // GameEventManager.UnregisterListener(GameEventType.BattleEnd, OnGameBattleEndEvent);
            Debug.Log($"[CompleteConditionBattleV2] 注销战斗事件监听: 战斗 {m_targetBattleId}");
        }

        /// <summary>
        /// 游戏战斗开始事件回调
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnGameBattleStartEvent(object eventArgs)
        {
            // 解析事件参数并调用OnBattleStartEvent
            // 这里需要根据实际的事件系统来实现
        }

        /// <summary>
        /// 游戏战斗结束事件回调
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnGameBattleEndEvent(object eventArgs)
        {
            // 解析事件参数并调用OnBattleEndEvent
            // 这里需要根据实际的事件系统来实现
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 获取详细的战斗统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public string GetBattleStatistics()
        {
            var stats = $"战斗统计:\n";
            stats += $"  目标战斗: {m_targetBattleId}\n";
            stats += $"  要求: {GetBattleRequirement()}\n";
            stats += $"  进度: {m_currentWinCount}/{m_requiredWinCount}\n";
            stats += $"  剩余: {GetRemainingBattleCount()}\n";
            stats += $"  完成率: {GetProgressPercentage():F1}%\n";
            stats += $"  状态: {(m_isCompleted ? "已完成" : "进行中")}";
            
            return stats;
        }

        #endregion
    }
}
