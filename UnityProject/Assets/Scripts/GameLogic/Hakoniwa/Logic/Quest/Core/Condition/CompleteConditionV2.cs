using System;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// 任务完成条件基类 V2
    /// 重构后的完成条件系统，提供统一的条件处理接口
    /// </summary>
    public abstract class CompleteConditionV2 : ClassPoolObj
    {
        #region Static Factory

        /// <summary>
        /// 创建完成条件实例
        /// </summary>
        /// <param name="conditionConfig">条件配置</param>
        /// <param name="quest">所属任务</param>
        /// <returns>条件实例</returns>
        public static CompleteConditionV2 CreateCondition(QuestCompleteCondition conditionConfig, QuestTemplateV2 quest)
        {
            CompleteConditionV2 condition = null;

            try
            {
                switch (conditionConfig)
                {
                    case ConfigDataQuestCompleteConditionTalk talkConfig:
                        condition = ClassPoolManager.instance.Fetch<CompleteConditionTalkV2>();
                        break;

                    case ConfigDataQuestCompleteConditionReach reachConfig:
                        condition = ClassPoolManager.instance.Fetch<CompleteConditionReachV2>();
                        break;

                    case ConfigDataQuestCompleteConditionBattle battleConfig:
                        condition = ClassPoolManager.instance.Fetch<CompleteConditionBattleV2>();
                        break;

                    case ConfigDataQuestCompleteConditionSubmit submitConfig:
                        condition = ClassPoolManager.instance.Fetch<CompleteConditionSubmitV2>();
                        break;

                    case ConfigDataQuestCompleteConditionKillMonster killConfig:
                        condition = ClassPoolManager.instance.Fetch<CompleteConditionKillV2>();
                        break;

                    default:
                        Debug.LogError($"[CompleteConditionV2] 未支持的条件类型: {conditionConfig.GetType().Name}");
                        break;
                }

                if (condition != null)
                {
                    condition.Initialize(conditionConfig, quest);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CompleteConditionV2] 创建条件失败: {ex}");
                condition?.Release();
                condition = null;
            }

            return condition;
        }

        #endregion

        #region Protected Fields

        /// <summary>
        /// 条件配置
        /// </summary>
        protected QuestCompleteCondition m_conditionConfig;

        /// <summary>
        /// 所属任务
        /// </summary>
        protected QuestTemplateV2 m_quest;

        /// <summary>
        /// 当前进度
        /// </summary>
        protected int m_currentProgress = 0;

        /// <summary>
        /// 最大进度
        /// </summary>
        protected int m_maxProgress = 1;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        protected bool m_initialized = false;

        /// <summary>
        /// 是否已完成
        /// </summary>
        protected bool m_isCompleted = false;

        /// <summary>
        /// 是否强制完成
        /// </summary>
        protected bool m_forceCompleted = false;

        /// <summary>
        /// 条件描述
        /// </summary>
        protected string m_description = "";

        #endregion

        #region Properties

        /// <summary>
        /// 条件类型名称
        /// </summary>
        public virtual string ConditionTypeName => GetType().Name;

        /// <summary>
        /// 条件描述
        /// </summary>
        public virtual string Description => m_description;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => m_initialized;

        /// <summary>
        /// 所属任务
        /// </summary>
        public QuestTemplateV2 Quest => m_quest;

        #endregion

        #region ClassPoolObj Override

        public override void OnRelease()
        {
            // 清理资源
            OnCleanup();

            // 重置状态
            m_conditionConfig = null;
            m_quest = null;
            m_currentProgress = 0;
            m_maxProgress = 1;
            m_initialized = false;
            m_isCompleted = false;
            m_forceCompleted = false;
            m_description = "";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 初始化条件
        /// </summary>
        /// <param name="conditionConfig">条件配置</param>
        /// <param name="quest">所属任务</param>
        public void Initialize(QuestCompleteCondition conditionConfig, QuestTemplateV2 quest)
        {
            if (m_initialized)
            {
                Debug.LogWarning($"[CompleteConditionV2] 条件已初始化: {ConditionTypeName}");
                return;
            }

            m_conditionConfig = conditionConfig;
            m_quest = quest;

            try
            {
                // 调用子类初始化
                OnInitialize();
                m_initialized = true;

                Debug.Log($"[CompleteConditionV2] 条件初始化完成: {ConditionTypeName} - {Description}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CompleteConditionV2] 条件初始化失败: {ex}");
                m_initialized = false;
            }
        }

        /// <summary>
        /// 更新条件
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        public void Tick(float deltaTime)
        {
            if (!m_initialized || m_isCompleted)
                return;

            try
            {
                OnTick(deltaTime);
                
                // 检查是否完成
                CheckCompletion();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CompleteConditionV2] 条件更新异常: {ex}");
            }
        }

        /// <summary>
        /// 获取当前进度
        /// </summary>
        /// <returns>当前进度</returns>
        public int GetCurrentProgress()
        {
            return m_currentProgress;
        }

        /// <summary>
        /// 获取最大进度
        /// </summary>
        /// <returns>最大进度</returns>
        public int GetMaxProgress()
        {
            return m_maxProgress;
        }

        /// <summary>
        /// 设置进度
        /// </summary>
        /// <param name="progress">进度值</param>
        public virtual void SetProgress(int progress)
        {
            int oldProgress = m_currentProgress;
            m_currentProgress = Mathf.Clamp(progress, 0, m_maxProgress);

            if (oldProgress != m_currentProgress)
            {
                OnProgressChanged(oldProgress, m_currentProgress);
                CheckCompletion();
            }
        }

        /// <summary>
        /// 增加进度
        /// </summary>
        /// <param name="amount">增加数量</param>
        public virtual void AddProgress(int amount = 1)
        {
            SetProgress(m_currentProgress + amount);
        }

        /// <summary>
        /// 检查是否完成
        /// </summary>
        /// <returns>是否完成</returns>
        public bool IsCompleted()
        {
            return m_isCompleted;
        }

        /// <summary>
        /// 强制完成条件
        /// </summary>
        public virtual void ForceComplete()
        {
            if (m_isCompleted)
                return;

            m_forceCompleted = true;
            m_currentProgress = m_maxProgress;
            m_isCompleted = true;

            OnForceComplete();
            Debug.Log($"[CompleteConditionV2] 强制完成条件: {ConditionTypeName}");
        }

        /// <summary>
        /// 获取进度百分比
        /// </summary>
        /// <returns>进度百分比 (0-100)</returns>
        public float GetProgressPercentage()
        {
            if (m_maxProgress <= 0)
                return 100f;

            return (float)m_currentProgress / m_maxProgress * 100f;
        }

        /// <summary>
        /// 获取进度描述
        /// </summary>
        /// <returns>进度描述</returns>
        public virtual string GetProgressDescription()
        {
            return $"{m_currentProgress}/{m_maxProgress}";
        }

        /// <summary>
        /// 获取详细状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public virtual string GetStatusInfo()
        {
            string status = m_isCompleted ? "已完成" : "进行中";
            if (m_forceCompleted)
                status += " (强制)";

            return $"{ConditionTypeName}: {Description} - {GetProgressDescription()} [{status}]";
        }

        #endregion

        #region Protected Virtual Methods

        /// <summary>
        /// 子类初始化方法
        /// </summary>
        protected virtual void OnInitialize()
        {
            // 子类重写此方法进行特定初始化
            m_maxProgress = GetConfigMaxProgress();
            m_description = GetConfigDescription();
        }

        /// <summary>
        /// 子类更新方法
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        protected virtual void OnTick(float deltaTime)
        {
            // 子类重写此方法进行特定更新逻辑
        }

        /// <summary>
        /// 子类清理方法
        /// </summary>
        protected virtual void OnCleanup()
        {
            // 子类重写此方法进行特定清理
        }

        /// <summary>
        /// 进度变化回调
        /// </summary>
        /// <param name="oldProgress">旧进度</param>
        /// <param name="newProgress">新进度</param>
        protected virtual void OnProgressChanged(int oldProgress, int newProgress)
        {
            // 子类可重写此方法处理进度变化
            Debug.Log($"[CompleteConditionV2] {ConditionTypeName} 进度变化: {oldProgress} -> {newProgress}");
        }

        /// <summary>
        /// 条件完成回调
        /// </summary>
        protected virtual void OnCompleted()
        {
            // 子类可重写此方法处理完成逻辑
            Debug.Log($"[CompleteConditionV2] {ConditionTypeName} 已完成: {Description}");
        }

        /// <summary>
        /// 强制完成回调
        /// </summary>
        protected virtual void OnForceComplete()
        {
            // 子类可重写此方法处理强制完成逻辑
        }

        /// <summary>
        /// 获取配置中的最大进度
        /// </summary>
        /// <returns>最大进度</returns>
        protected virtual int GetConfigMaxProgress()
        {
            // 子类重写此方法从配置中获取最大进度
            return 1;
        }

        /// <summary>
        /// 获取配置中的描述
        /// </summary>
        /// <returns>描述</returns>
        protected virtual string GetConfigDescription()
        {
            // 子类重写此方法从配置中获取描述
            return ConditionTypeName;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 检查完成状态
        /// </summary>
        private void CheckCompletion()
        {
            if (m_isCompleted)
                return;

            bool shouldComplete = m_currentProgress >= m_maxProgress;

            if (shouldComplete)
            {
                m_isCompleted = true;
                OnCompleted();
            }
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 打印调试信息
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void PrintDebugInfo()
        {
            Debug.Log($"=== CompleteConditionV2 Debug Info ===");
            Debug.Log($"类型: {ConditionTypeName}");
            Debug.Log($"描述: {Description}");
            Debug.Log($"进度: {GetProgressDescription()}");
            Debug.Log($"百分比: {GetProgressPercentage():F1}%");
            Debug.Log($"状态: {(m_isCompleted ? "已完成" : "进行中")}");
            Debug.Log($"强制完成: {m_forceCompleted}");
            Debug.Log($"所属任务: {m_quest?.QuestName ?? "None"}");
            Debug.Log("=====================================");
        }

        #endregion
    }
}
