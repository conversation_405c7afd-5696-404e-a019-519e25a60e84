using System;
using System.Collections.Generic;
using System.Linq;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.Hakoniwa.Logic
{
    /// <summary>
    /// Hakoniwa任务模块 V2
    /// 重构后的任务系统核心模块，提供完整的任务管理功能
    /// </summary>
    public class HakoniwaQuestModuleV2 : HakoniwaModule
    {
        #region Private Fields

        /// <summary>
        /// 任务图配置
        /// </summary>
        private ConfigDataQuestGraph m_questGraph;

        /// <summary>
        /// 运行中的任务列表
        /// </summary>
        private List<QuestTemplateV2> m_runningQuests = new List<QuestTemplateV2>();

        /// <summary>
        /// 已完成的任务ID列表
        /// </summary>
        private HashSet<int> m_completedQuestIds = new HashSet<int>();

        /// <summary>
        /// 任务状态字典
        /// </summary>
        private Dictionary<int, QuestStatus> m_questStatusMap = new Dictionary<int, QuestStatus>();

        /// <summary>
        /// 任务事件监听器
        /// </summary>
        private Dictionary<int, List<Action<QuestEventArgs>>> m_questEventListeners = 
            new Dictionary<int, List<Action<QuestEventArgs>>>();

        #endregion

        #region Properties

        /// <summary>
        /// 运行中的任务数量
        /// </summary>
        public int RunningQuestCount => m_runningQuests.Count;

        /// <summary>
        /// 已完成的任务数量
        /// </summary>
        public int CompletedQuestCount => m_completedQuestIds.Count;

        /// <summary>
        /// 所有任务的总数
        /// </summary>
        public int TotalQuestCount => m_questGraph?.Nodes?.Count ?? 0;

        #endregion

        #region HakoniwaModule Override

        protected override void OnInit()
        {
            // 获取任务图配置
            m_questGraph = Owner.hakoniwaConfig.QuestGraphConfig.QuestGraph;
            
            if (m_questGraph == null)
            {
                Debug.LogError("[HakoniwaQuestModuleV2] 任务图配置为空");
                return;
            }

            // 初始化任务
            InitializeQuests();

            // 注册事件监听
            RegisterEventListeners();

            Debug.Log($"[HakoniwaQuestModuleV2] 初始化完成，共加载 {m_runningQuests.Count} 个任务");
        }

        protected override void OnTick(float deltaTime)
        {
            // 更新所有运行中的任务
            for (int i = m_runningQuests.Count - 1; i >= 0; i--)
            {
                var quest = m_runningQuests[i];
                quest.Tick(deltaTime);

                // 检查任务是否完成
                if (quest.IsCompleted && !m_completedQuestIds.Contains(quest.QuestId))
                {
                    CompleteQuest(quest);
                }
            }
        }

        protected override void OnUnInit()
        {
            // 清理任务
            foreach (var quest in m_runningQuests)
            {
                quest.Release();
            }
            m_runningQuests.Clear();

            // 清理数据
            m_completedQuestIds.Clear();
            m_questStatusMap.Clear();
            m_questEventListeners.Clear();

            // 注销事件监听
            UnregisterEventListeners();

            Debug.Log("[HakoniwaQuestModuleV2] 清理完成");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 检查任务是否正在运行
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>是否正在运行</returns>
        public bool IsQuestRunning(int questId)
        {
            return m_runningQuests.Any(q => q.QuestId == questId);
        }

        /// <summary>
        /// 检查任务是否已完成
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>是否已完成</returns>
        public bool IsQuestCompleted(int questId)
        {
            return m_completedQuestIds.Contains(questId);
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>任务状态</returns>
        public QuestStatus GetQuestStatus(int questId)
        {
            return m_questStatusMap.TryGetValue(questId, out var status) ? status : QuestStatus.None;
        }

        /// <summary>
        /// 获取运行中的任务
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>任务实例</returns>
        public QuestTemplateV2 GetRunningQuest(int questId)
        {
            return m_runningQuests.FirstOrDefault(q => q.QuestId == questId);
        }

        /// <summary>
        /// 获取所有运行中的任务
        /// </summary>
        /// <returns>任务列表</returns>
        public List<QuestTemplateV2> GetAllRunningQuests()
        {
            return new List<QuestTemplateV2>(m_runningQuests);
        }

        /// <summary>
        /// 开始任务
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>是否成功开始</returns>
        public bool StartQuest(int questId)
        {
            if (IsQuestRunning(questId) || IsQuestCompleted(questId))
            {
                Debug.LogWarning($"[HakoniwaQuestModuleV2] 任务 {questId} 已在运行或已完成");
                return false;
            }

            var questConfig = m_questGraph.GetQuestNodeTemplate(questId);
            if (questConfig == null)
            {
                Debug.LogError($"[HakoniwaQuestModuleV2] 找不到任务配置: {questId}");
                return false;
            }

            var quest = ClassPoolManager.instance.Fetch<QuestTemplateV2>();
            quest.Init(questConfig, this);
            m_runningQuests.Add(quest);

            SetQuestStatus(questId, QuestStatus.InProgress);
            
            // 触发任务开始事件
            TriggerQuestEvent(questId, QuestEventType.Started, new QuestEventArgs
            {
                QuestId = questId,
                EventType = QuestEventType.Started
            });

            Debug.Log($"[HakoniwaQuestModuleV2] 开始任务: {questConfig.QuestName} ({questId})");
            return true;
        }

        /// <summary>
        /// 强制完成任务
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>是否成功完成</returns>
        public bool ForceCompleteQuest(int questId)
        {
            var quest = GetRunningQuest(questId);
            if (quest == null)
            {
                Debug.LogWarning($"[HakoniwaQuestModuleV2] 任务 {questId} 未在运行中");
                return false;
            }

            quest.ForceComplete();
            return true;
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>是否成功取消</returns>
        public bool CancelQuest(int questId)
        {
            var quest = GetRunningQuest(questId);
            if (quest == null)
            {
                Debug.LogWarning($"[HakoniwaQuestModuleV2] 任务 {questId} 未在运行中");
                return false;
            }

            m_runningQuests.Remove(quest);
            quest.Release();

            SetQuestStatus(questId, QuestStatus.Cancelled);

            // 触发任务取消事件
            TriggerQuestEvent(questId, QuestEventType.Cancelled, new QuestEventArgs
            {
                QuestId = questId,
                EventType = QuestEventType.Cancelled
            });

            Debug.Log($"[HakoniwaQuestModuleV2] 取消任务: {questId}");
            return true;
        }

        /// <summary>
        /// 注册任务事件监听器
        /// </summary>
        /// <param name="questId">任务ID，-1表示监听所有任务</param>
        /// <param name="listener">监听器</param>
        public void RegisterQuestEventListener(int questId, Action<QuestEventArgs> listener)
        {
            if (!m_questEventListeners.TryGetValue(questId, out var listeners))
            {
                listeners = new List<Action<QuestEventArgs>>();
                m_questEventListeners[questId] = listeners;
            }

            if (!listeners.Contains(listener))
            {
                listeners.Add(listener);
            }
        }

        /// <summary>
        /// 注销任务事件监听器
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <param name="listener">监听器</param>
        public void UnregisterQuestEventListener(int questId, Action<QuestEventArgs> listener)
        {
            if (m_questEventListeners.TryGetValue(questId, out var listeners))
            {
                listeners.Remove(listener);
                if (listeners.Count == 0)
                {
                    m_questEventListeners.Remove(questId);
                }
            }
        }

        /// <summary>
        /// 触发任务进度更新
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <param name="conditionIndex">条件索引</param>
        /// <param name="progress">进度值</param>
        public void UpdateQuestProgress(int questId, int conditionIndex, int progress)
        {
            var quest = GetRunningQuest(questId);
            if (quest != null)
            {
                quest.UpdateConditionProgress(conditionIndex, progress);
            }
        }

        /// <summary>
        /// 获取任务进度信息
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <returns>进度信息</returns>
        public QuestProgressInfo GetQuestProgress(int questId)
        {
            var quest = GetRunningQuest(questId);
            return quest?.GetProgressInfo() ?? new QuestProgressInfo { QuestId = questId };
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 初始化任务
        /// </summary>
        private void InitializeQuests()
        {
            if (m_questGraph?.Nodes == null)
                return;

            foreach (var node in m_questGraph.Nodes.Values)
            {
                if (node is ConfigDataQuestNodeTemplate questConfig)
                {
                    // 初始化任务状态
                    SetQuestStatus(questConfig.QuestId, QuestStatus.NotStarted);

                    // 检查是否应该自动开始
                    if (ShouldAutoStartQuest(questConfig))
                    {
                        StartQuest(questConfig.QuestId);
                    }
                }
            }
        }

        /// <summary>
        /// 检查任务是否应该自动开始
        /// </summary>
        /// <param name="questConfig">任务配置</param>
        /// <returns>是否应该自动开始</returns>
        private bool ShouldAutoStartQuest(ConfigDataQuestNodeTemplate questConfig)
        {
            // 这里可以添加自动开始任务的逻辑
            // 例如：检查前置任务、玩家等级、特定条件等
            return false;
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="quest">任务实例</param>
        private void CompleteQuest(QuestTemplateV2 quest)
        {
            int questId = quest.QuestId;
            
            // 从运行列表中移除
            m_runningQuests.Remove(quest);
            
            // 添加到完成列表
            m_completedQuestIds.Add(questId);
            
            // 更新状态
            SetQuestStatus(questId, QuestStatus.Completed);

            // 执行任务奖励
            ExecuteQuestRewards(quest);

            // 触发完成事件
            TriggerQuestEvent(questId, QuestEventType.Completed, new QuestEventArgs
            {
                QuestId = questId,
                EventType = QuestEventType.Completed,
                QuestName = quest.QuestName
            });

            // 检查后续任务
            CheckFollowUpQuests(quest);

            // 释放任务资源
            quest.Release();

            Debug.Log($"[HakoniwaQuestModuleV2] 完成任务: {quest.QuestName} ({questId})");
        }

        /// <summary>
        /// 执行任务奖励
        /// </summary>
        /// <param name="quest">任务实例</param>
        private void ExecuteQuestRewards(QuestTemplateV2 quest)
        {
            // 这里可以添加奖励发放逻辑
            // 例如：经验、金币、道具等
            Debug.Log($"[HakoniwaQuestModuleV2] 发放任务奖励: {quest.QuestName}");
        }

        /// <summary>
        /// 检查后续任务
        /// </summary>
        /// <param name="completedQuest">已完成的任务</param>
        private void CheckFollowUpQuests(QuestTemplateV2 completedQuest)
        {
            // 这里可以添加检查后续任务的逻辑
            // 例如：自动开启下一个任务
        }

        /// <summary>
        /// 设置任务状态
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <param name="status">状态</param>
        private void SetQuestStatus(int questId, QuestStatus status)
        {
            var oldStatus = GetQuestStatus(questId);
            m_questStatusMap[questId] = status;

            if (oldStatus != status)
            {
                // 触发状态变化事件
                TriggerQuestEvent(questId, QuestEventType.StatusChanged, new QuestEventArgs
                {
                    QuestId = questId,
                    EventType = QuestEventType.StatusChanged,
                    OldStatus = oldStatus,
                    NewStatus = status
                });
            }
        }

        /// <summary>
        /// 触发任务事件
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <param name="eventType">事件类型</param>
        /// <param name="args">事件参数</param>
        private void TriggerQuestEvent(int questId, QuestEventType eventType, QuestEventArgs args)
        {
            // 触发特定任务的监听器
            if (m_questEventListeners.TryGetValue(questId, out var specificListeners))
            {
                foreach (var listener in specificListeners)
                {
                    try
                    {
                        listener?.Invoke(args);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[HakoniwaQuestModuleV2] 任务事件监听器异常: {ex}");
                    }
                }
            }

            // 触发全局监听器（questId = -1）
            if (m_questEventListeners.TryGetValue(-1, out var globalListeners))
            {
                foreach (var listener in globalListeners)
                {
                    try
                    {
                        listener?.Invoke(args);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[HakoniwaQuestModuleV2] 全局任务事件监听器异常: {ex}");
                    }
                }
            }
        }

        /// <summary>
        /// 注册事件监听
        /// </summary>
        private void RegisterEventListeners()
        {
            // 这里可以注册游戏内的事件监听
            // 例如：玩家行为、NPC交互等
        }

        /// <summary>
        /// 注销事件监听
        /// </summary>
        private void UnregisterEventListeners()
        {
            // 注销事件监听
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 打印调试信息
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void PrintDebugInfo()
        {
            Debug.Log("=== HakoniwaQuestModuleV2 Debug Info ===");
            Debug.Log($"运行中任务数量: {RunningQuestCount}");
            Debug.Log($"已完成任务数量: {CompletedQuestCount}");
            Debug.Log($"总任务数量: {TotalQuestCount}");

            Debug.Log("运行中的任务:");
            foreach (var quest in m_runningQuests)
            {
                Debug.Log($"  - {quest.QuestName} ({quest.QuestId}): {quest.GetProgressInfo()}");
            }

            Debug.Log("已完成的任务:");
            foreach (var questId in m_completedQuestIds)
            {
                Debug.Log($"  - 任务ID: {questId}");
            }

            Debug.Log("=====================================");
        }

        #endregion
    }

    #region Helper Classes

    /// <summary>
    /// 任务状态枚举
    /// </summary>
    public enum QuestStatus
    {
        None,           // 无状态
        NotStarted,     // 未开始
        InProgress,     // 进行中
        Completed,      // 已完成
        Cancelled,      // 已取消
        Failed          // 已失败
    }

    /// <summary>
    /// 任务事件类型
    /// </summary>
    public enum QuestEventType
    {
        Started,        // 开始
        Completed,      // 完成
        Cancelled,      // 取消
        Failed,         // 失败
        StatusChanged,  // 状态变化
        ProgressUpdated // 进度更新
    }

    /// <summary>
    /// 任务事件参数
    /// </summary>
    public class QuestEventArgs
    {
        public int QuestId { get; set; }
        public string QuestName { get; set; }
        public QuestEventType EventType { get; set; }
        public QuestStatus OldStatus { get; set; }
        public QuestStatus NewStatus { get; set; }
        public int ConditionIndex { get; set; }
        public int Progress { get; set; }
        public object ExtraData { get; set; }
    }

    /// <summary>
    /// 任务进度信息
    /// </summary>
    public class QuestProgressInfo
    {
        public int QuestId { get; set; }
        public string QuestName { get; set; }
        public int TotalConditions { get; set; }
        public int CompletedConditions { get; set; }
        public float ProgressPercentage { get; set; }
        public Dictionary<int, int> ConditionProgress { get; set; } = new Dictionary<int, int>();

        public override string ToString()
        {
            return $"进度: {CompletedConditions}/{TotalConditions} ({ProgressPercentage:F1}%)";
        }
    }

    #endregion
}
