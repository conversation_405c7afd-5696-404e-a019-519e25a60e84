

using Phoenix.ConfigData.QuestGraph;

namespace Phoenix.Hakoniwa.Logic
{
    public class HakoniwaQuestModule : HakoniwaModule
    {
        public ConfigDataQuestGraph m_questGraph;




        protected override void OnInit()
        {
            var nodes = Owner.hakoniwaConfig.QuestGraphConfig.QuestGraph.Nodes;

            foreach (var node in nodes)
            {


            }
        }

        protected override void OnTick(float dt)
        {

        }

        protected override void OnUnInit()
        {

        }

    }
}

