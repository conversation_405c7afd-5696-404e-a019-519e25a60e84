# Hakoniwa Quest System V2 - 代码整理文档

本文档描述了重构后的Hakoniwa任务系统的完整架构和使用方法。

## 📁 代码结构整理

### 重构后的文件结构
```
Assets/Scripts/GameLogic/Hakoniwa/Logic/Quest/
├── Core/                           # 核心模块
│   ├── HakoniwaQuestModuleV2.cs   # 任务模块主类
│   └── QuestTemplateV2.cs         # 任务模板类
├── Condition/                      # 完成条件
│   ├── CompleteConditionV2.cs     # 条件基类
│   ├── CompleteConditionTalkV2.cs # 对话条件
│   ├── CompleteConditionReachV2.cs# 到达条件
│   ├── CompleteConditionKillV2.cs # 击杀条件
│   ├── CompleteConditionBattleV2.cs# 战斗条件
│   └── CompleteConditionCollectV2.cs# 收集条件
└── README_QuestSystemV2.md        # 本文档
```

### 原有代码保留
- **配置数据**: ConfigData相关类保持不变
- **图形编辑器**: QuestGraph相关编辑器工具保持不变
- **Action系统**: QuestAction相关类保持不变

## 🎯 核心架构

### 1. HakoniwaQuestModuleV2 - 任务管理核心

#### 主要功能
- **任务生命周期管理**: 开始、完成、取消任务
- **事件系统**: 任务状态变化、进度更新事件
- **状态追踪**: 运行中、已完成、已取消任务状态
- **配置加载**: 从QuestGraph配置加载任务

#### 核心API
```csharp
// 任务控制
bool StartQuest(int questId)
bool ForceCompleteQuest(int questId)
bool CancelQuest(int questId)

// 状态查询
bool IsQuestRunning(int questId)
bool IsQuestCompleted(int questId)
QuestStatus GetQuestStatus(int questId)

// 进度管理
void UpdateQuestProgress(int questId, int conditionIndex, int progress)
QuestProgressInfo GetQuestProgress(int questId)

// 事件系统
void RegisterQuestEventListener(int questId, Action<QuestEventArgs> listener)
void UnregisterQuestEventListener(int questId, Action<QuestEventArgs> listener)
```

### 2. QuestTemplateV2 - 任务实例

#### 主要功能
- **条件管理**: 初始化和更新完成条件
- **进度追踪**: 实时监控任务完成进度
- **状态控制**: 管理任务的完成状态
- **时间统计**: 记录任务运行时间

#### 核心API
```csharp
// 初始化和生命周期
void Init(ConfigDataQuestNodeTemplate config, HakoniwaQuestModuleV2 questModule)
void Tick(float deltaTime)

// 进度管理
void UpdateConditionProgress(int conditionIndex, int progress)
QuestProgressInfo GetProgressInfo()
float GetCompletionPercentage()

// 状态控制
void ForceComplete()
bool IsConditionCompleted(int conditionIndex)
```

### 3. CompleteConditionV2 - 完成条件基类

#### 设计模式
- **工厂模式**: 通过静态方法创建具体条件实例
- **模板方法**: 定义条件处理的标准流程
- **事件驱动**: 通过游戏事件更新条件进度

#### 核心API
```csharp
// 工厂方法
static CompleteConditionV2 CreateCondition(QuestCompleteCondition config, QuestTemplateV2 quest)

// 生命周期
void Initialize(QuestCompleteCondition config, QuestTemplateV2 quest)
void Tick(float deltaTime)

// 进度管理
void SetProgress(int progress)
void AddProgress(int amount = 1)
int GetCurrentProgress()
int GetMaxProgress()

// 状态查询
bool IsCompleted()
float GetProgressPercentage()
string GetProgressDescription()
```

## 🛠️ 具体条件类型

### 1. CompleteConditionTalkV2 - 对话条件
```csharp
// 特有功能
void OnTalkEvent(int npcId, int dialogId = -1)
bool IsTargetNpc(int npcId)

// 配置参数
int TargetNpcId        // 目标NPC ID
int RequiredTalkCount  // 需要对话次数
int RequiredDialogId   // 特定对话ID（可选）
```

### 2. CompleteConditionReachV2 - 到达条件
```csharp
// 特有功能
void UpdatePlayerPosition(Vector3 playerPosition)
bool IsInRange()
Vector3 GetDirectionToTarget()

// 配置参数
Vector3 TargetPosition // 目标位置
float ReachRadius      // 到达范围
```

### 3. CompleteConditionKillV2 - 击杀条件
```csharp
// 特有功能
void OnKillEvent(int enemyId, int killerId = -1)
void OnBatchKillEvent(int enemyId, int killCount, int killerId = -1)
bool IsTargetEnemy(int enemyId)

// 配置参数
int TargetEnemyId      // 目标敌人ID（-1表示任意）
int RequiredKillCount  // 需要击杀数量
bool PlayerKillOnly    // 是否只计算玩家击杀
```

### 4. CompleteConditionBattleV2 - 战斗条件
```csharp
// 特有功能
void OnBattleStartEvent(int battleId, int playerId = -1)
void OnBattleEndEvent(int battleId, bool isWin, int playerId = -1)
bool IsTargetBattle(int battleId)

// 配置参数
int TargetBattleId     // 目标战斗ID
int RequiredWinCount   // 需要胜利次数
bool MustWin          // 是否必须胜利
bool AllowRepeat      // 是否允许重复
```

### 5. CompleteConditionCollectV2 - 收集条件
```csharp
// 特有功能
void OnItemGainEvent(int itemId, int count, int playerId = -1)
void OnItemLoseEvent(int itemId, int count, int playerId = -1)
void UpdateCollectProgress(int currentCount)

// 配置参数
int TargetItemId       // 目标物品ID
int RequiredCollectCount // 需要收集数量
bool ConsumeItem       // 是否消耗物品
```

## 🚀 使用指南

### 1. 基础使用

#### 初始化任务系统
```csharp
// 在HakoniwaLogic中初始化
var questModule = GetModule<HakoniwaQuestModuleV2>();

// 注册全局任务事件监听
questModule.RegisterQuestEventListener(-1, OnQuestEvent);
```

#### 开始任务
```csharp
// 开始指定任务
bool success = questModule.StartQuest(1001);
if (success)
{
    Debug.Log("任务开始成功");
}
```

#### 查询任务状态
```csharp
// 检查任务状态
if (questModule.IsQuestRunning(1001))
{
    var progress = questModule.GetQuestProgress(1001);
    Debug.Log($"任务进度: {progress.ProgressPercentage:F1}%");
}
```

### 2. 事件处理

#### 注册任务事件
```csharp
void OnQuestEvent(QuestEventArgs args)
{
    switch (args.EventType)
    {
        case QuestEventType.Started:
            Debug.Log($"任务开始: {args.QuestName}");
            break;
            
        case QuestEventType.Completed:
            Debug.Log($"任务完成: {args.QuestName}");
            break;
            
        case QuestEventType.ProgressUpdated:
            Debug.Log($"任务进度更新: {args.QuestId}");
            break;
    }
}
```

#### 触发条件事件
```csharp
// 对话事件
var talkCondition = quest.GetCondition<CompleteConditionTalkV2>(0);
talkCondition?.OnTalkEvent(npcId, dialogId);

// 击杀事件
var killCondition = quest.GetCondition<CompleteConditionKillV2>(1);
killCondition?.OnKillEvent(enemyId, playerId);

// 物品获得事件
var collectCondition = quest.GetCondition<CompleteConditionCollectV2>(2);
collectCondition?.OnItemGainEvent(itemId, count, playerId);
```

### 3. 高级功能

#### 自定义条件类型
```csharp
public class CompleteConditionCustomV2 : CompleteConditionV2
{
    protected override void OnInitialize()
    {
        // 自定义初始化逻辑
        base.OnInitialize();
    }
    
    protected override void OnTick(float deltaTime)
    {
        // 自定义更新逻辑
    }
    
    // 添加到工厂方法中
    // case ConfigDataQuestCompleteConditionCustom customConfig:
    //     condition = ClassPoolManager.instance.Fetch<CompleteConditionCustomV2>();
    //     break;
}
```

#### 任务链管理
```csharp
// 监听任务完成事件，自动开启后续任务
void OnQuestCompleted(QuestEventArgs args)
{
    if (args.QuestId == 1001)
    {
        // 完成任务1001后，自动开启任务1002
        questModule.StartQuest(1002);
    }
}
```

## 🔧 集成指南

### 1. 与现有系统集成

#### 对话系统集成
```csharp
// 在对话系统中触发任务事件
public class DialogSystem
{
    void OnDialogComplete(int npcId, int dialogId)
    {
        // 通知所有相关的对话条件
        var questModule = HakoniwaLogic.Instance.GetModule<HakoniwaQuestModuleV2>();
        var runningQuests = questModule.GetAllRunningQuests();
        
        foreach (var quest in runningQuests)
        {
            for (int i = 0; i < quest.ConditionCount; i++)
            {
                if (quest.GetCondition(i) is CompleteConditionTalkV2 talkCondition)
                {
                    talkCondition.OnTalkEvent(npcId, dialogId);
                }
            }
        }
    }
}
```

#### 战斗系统集成
```csharp
// 在战斗系统中触发任务事件
public class BattleSystem
{
    void OnBattleEnd(int battleId, bool playerWin)
    {
        // 通知击杀条件
        NotifyKillConditions(battleId, playerWin);
        
        // 通知战斗条件
        NotifyBattleConditions(battleId, playerWin);
    }
}
```

#### 背包系统集成
```csharp
// 在背包系统中触发任务事件
public class InventorySystem
{
    void OnItemAdd(int itemId, int count)
    {
        // 通知收集条件
        NotifyCollectConditions(itemId, count, true);
    }
    
    void OnItemRemove(int itemId, int count)
    {
        // 通知收集条件
        NotifyCollectConditions(itemId, count, false);
    }
}
```

### 2. 性能优化建议

#### 事件系统优化
```csharp
// 使用事件池避免频繁分配
public class QuestEventPool
{
    private static Queue<QuestEventArgs> s_eventPool = new Queue<QuestEventArgs>();
    
    public static QuestEventArgs Get()
    {
        return s_eventPool.Count > 0 ? s_eventPool.Dequeue() : new QuestEventArgs();
    }
    
    public static void Return(QuestEventArgs eventArgs)
    {
        eventArgs.Reset();
        s_eventPool.Enqueue(eventArgs);
    }
}
```

#### 条件更新优化
```csharp
// 只更新相关的条件，避免全量检查
public void UpdateSpecificConditions<T>(System.Action<T> updateAction) where T : CompleteConditionV2
{
    foreach (var quest in m_runningQuests)
    {
        for (int i = 0; i < quest.ConditionCount; i++)
        {
            if (quest.GetCondition(i) is T condition)
            {
                updateAction(condition);
            }
        }
    }
}
```

## 📊 调试和监控

### 1. 调试工具

#### 任务状态监控
```csharp
[System.Diagnostics.Conditional("UNITY_EDITOR")]
public void PrintQuestSystemStatus()
{
    var questModule = HakoniwaLogic.Instance.GetModule<HakoniwaQuestModuleV2>();
    questModule.PrintDebugInfo();
    
    var runningQuests = questModule.GetAllRunningQuests();
    foreach (var quest in runningQuests)
    {
        quest.PrintDebugInfo();
    }
}
```

#### 条件进度可视化
```csharp
// 在Unity编辑器中显示任务进度
[CustomEditor(typeof(HakoniwaQuestModuleV2))]
public class QuestModuleEditor : Editor
{
    public override void OnInspectorGUI()
    {
        var questModule = target as HakoniwaQuestModuleV2;
        
        EditorGUILayout.LabelField($"运行中任务: {questModule.RunningQuestCount}");
        EditorGUILayout.LabelField($"已完成任务: {questModule.CompletedQuestCount}");
        
        // 显示每个任务的详细进度
        var runningQuests = questModule.GetAllRunningQuests();
        foreach (var quest in runningQuests)
        {
            var progress = quest.GetProgressInfo();
            EditorGUILayout.ProgressBar(progress.ProgressPercentage / 100f, 
                $"{quest.QuestName}: {progress.ProgressPercentage:F1}%");
        }
    }
}
```

### 2. 性能监控

#### 内存使用监控
```csharp
public class QuestSystemProfiler
{
    public static void LogMemoryUsage()
    {
        var questModule = HakoniwaLogic.Instance.GetModule<HakoniwaQuestModuleV2>();
        var runningQuests = questModule.GetAllRunningQuests();
        
        int totalConditions = 0;
        foreach (var quest in runningQuests)
        {
            totalConditions += quest.ConditionCount;
        }
        
        Debug.Log($"任务系统内存使用: {runningQuests.Count} 个任务, {totalConditions} 个条件");
    }
}
```

## 🔄 迁移指南

### 从旧版本迁移

#### 1. 模块替换
```csharp
// 旧版本
// var questModule = GetModule<HakoniwaQuestModule>();

// 新版本
var questModule = GetModule<HakoniwaQuestModuleV2>();
```

#### 2. API适配
```csharp
// 旧版本的API调用需要适配到新版本
// 大部分API保持兼容，但建议使用新的事件系统
```

#### 3. 配置兼容性
- 现有的QuestGraph配置文件无需修改
- ConfigData类保持不变
- 只需要更新代码引用

## 📝 总结

### 重构优势

1. **更清晰的架构**: 分离了核心逻辑和具体实现
2. **更好的扩展性**: 易于添加新的条件类型
3. **更强的事件系统**: 统一的事件处理机制
4. **更完善的调试**: 丰富的调试和监控工具
5. **更好的性能**: 优化了内存使用和更新频率

### 使用建议

1. **渐进式迁移**: 可以与旧系统并存，逐步迁移
2. **事件驱动**: 充分利用事件系统，减少轮询检查
3. **性能监控**: 定期检查任务系统的性能表现
4. **扩展开发**: 根据游戏需求添加自定义条件类型

这个重构后的任务系统提供了更加完善和灵活的任务管理功能，能够很好地支持复杂的游戏任务需求。
