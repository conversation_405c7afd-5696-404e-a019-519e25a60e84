

using System;
using System.Diagnostics;
using Phoenix.ConfigData;
using Phoenix.ConfigData.QuestGraph;

namespace Phoenix.Hakoniwa.Logic
{
    public class CompleteConditionReach : CompleteCondition
    {
        protected ConfigDataQuestCompleteConditionReach m_reachCondition;
        protected Int32  m_entityUid = -1;


        protected override void OnInit()
        {
            base.OnInit();
            m_reachCondition = m_completeCondition as ConfigDataQuestCompleteConditionReach;

            if (m_reachCondition.SceneId == StaticHakoniwa.hakoniwaBase.sceneConfig.Id)
            {
                HakoLocation location = new HakoLocation(m_reachCondition.PositionX, m_reachCondition.PositionY, m_reachCondition.PositionZ);
                HakoniwaEntity entity = HakoEntityGenerator.Gen(HakoniwaEntityType.Npc, m_reachCondition.EntitySkinId, location);
                StaticHakoniwa.hakoniwaBase.HokoEntityAdd(entity);
                m_entityUid = entity.Uid;
            }
        }

        protected override void OnTick(float dt)
        {
            if (m_entityUid > 0)
            {
                // Todo 执行到达检测逻辑
                // ...
                HakoEntityView hakoEntityView = StaticHakoniwa.hakoniwaView.GetHakoEntityView(m_entityUid);

                if (hakoEntityView != null && hakoEntityView.SqrDistance < 0.6f)
                {
                    //GameLogic.UI.TipUI.ShowTip($"抵达目标点 {hakoEntityView.HakoEntity.Position}");
                    RemoveHakoEntity();
                }
            }
        }

        public override void OnRelease()
        {
            base.OnRelease();
            RemoveHakoEntity();
        }

        private void RemoveHakoEntity()
        {
            StaticHakoniwa.hakoniwaBase.HakoEntityRemove(m_entityUid);
            m_entityUid = -1;
            m_reachCondition = null;
        }

    }
}

