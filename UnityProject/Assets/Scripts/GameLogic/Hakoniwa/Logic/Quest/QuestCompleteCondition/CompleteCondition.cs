

using Phoenix.Core;
using Phoenix.ConfigData.QuestGraph;
using System;

namespace Phoenix.Hakoniwa.Logic
{
    public class CompleteCondition : ClassPoolObj
    {
        #region Instance Generator

        public static CompleteCondition Generator(QuestTemplate owner, QuestCompleteCondition completeCondition)
        {
            CompleteCondition result = null;
            if (completeCondition is ConfigDataQuestCompleteConditionTalk)
            {
                result = ClassPoolManager.instance.Fetch<CompleteConditionTalk>();
            }
            else if (completeCondition is ConfigDataQuestCompleteConditionReach)
            {
                result = ClassPoolManager.instance.Fetch<CompleteConditionReach>();
            }
            else if (completeCondition is ConfigDataQuestCompleteConditionBattle)
            {
                result = ClassPoolManager.instance.Fetch<CompleteConditionBattle>();
            }
            else
            {
                DebugUtility.LogError($"{completeCondition.GetType().FullName} 类型找不到");
            }
            if (result != null)
            {
                result.Init(owner, completeCondition);
            }
            return result;
        }

        #endregion



        protected QuestTemplate m_quest;
        protected QuestCompleteCondition m_completeCondition;
        protected Boolean m_inited;



        public override void OnRelease()
        {
            m_quest = null;
            m_completeCondition = null;
            m_inited = false;
        }

        public void Init(QuestTemplate owner, QuestCompleteCondition completeCondition)
        {
            m_quest = owner;
            m_completeCondition = completeCondition;
            OnInit();
            m_inited = true;
        }

        public void Tick(Single dt)
        {
            if (m_inited)
            {
                OnTick(dt);
            }
        }


        protected virtual void OnInit() { }
        protected virtual void OnTick(Single dt) { }

    }
}

