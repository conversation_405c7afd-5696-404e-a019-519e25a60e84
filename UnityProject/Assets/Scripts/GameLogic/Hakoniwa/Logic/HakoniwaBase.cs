
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Hakoniwa.Logic
{
    public interface IHakoniwa { }

    public class HakoniwaBase : IHakoniwa
    {
        public PhoenixHakoniwaConfigData hakoniwaConfig;
        public HakoniwaWaypointConfigData waypointConfig;
        public HakoniwaSceneConfigData sceneConfig;
        public HakoLocation location;


        public HakoniwaEntityModule HakoniwaEntityModule { get; private set; }
        public HakoniwaSceneModule HakoniwaSceneModule { get; private set; }
        public HakoniwaQuestModule HakoniwaQuestModule { get; private set; }


        protected List<HakoniwaModule> m_modules = new List<HakoniwaModule>();


        public void Init(HakoniwaInfo initInfo)
        {
            hakoniwaConfig = initInfo.m_hakoniwaConfig;
            waypointConfig = initInfo.m_waypointConfig;
            sceneConfig = initInfo.m_hakoniwaSceneConfig;
            location = initInfo.m_location;
            InitModules();
        }

        public void Tick(TimeSlice ts)
        {
            foreach (HakoniwaModule module in m_modules)
            {
                module.Tick(ts.deltaTime);
            }
        }

        public void UnInit()
        {
            UnInitModules();
        }

        protected void InitModules()
        {
            HakoniwaEntityModule = CreateModule<HakoniwaEntityModule>();
            HakoniwaSceneModule = CreateModule<HakoniwaSceneModule>();
            HakoniwaQuestModule = CreateModule<HakoniwaQuestModule>();

            foreach (HakoniwaModule module in m_modules)
            {
                module.Init();
            }
        }


        protected void UnInitModules()
        {
            foreach (HakoniwaModule module in m_modules)
            {
                module.UnInit();
            }
            m_modules.Clear();
        }


        protected T CreateModule<T>() where T : HakoniwaModule, new()
        {
            T module = new T();
            module.SetOwner(this);
            m_modules.Add(module);
            return module;
        }



        #region Public

        public List<HakoniwaEntity> GetAllHakoniwaEntity()
        {
            return HakoniwaEntityModule.GetAllHakoniwaEntity();
        }
        public HakoniwaEntity GetHakoniwaEntity(Int32 uid)
        {
            return HakoniwaEntityModule.GetHakoniwaEntity(uid);
        }

        public void HokoEntityAdd(HakoniwaEntity hakoniwaEntity)
        {
            HakoniwaEntityModule.HokoEntityAdd(hakoniwaEntity);
        }

        public void HakoEntityRemove(Int32 entityUid)
        {
            HakoniwaEntityModule.HakoEntityRemove(entityUid);
        }


        #endregion
    }
}

