
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Hakoniwa.Logic
{
    public interface IHakoniwa { }

    public class HakoniwaBase : IHakoniwa
    {
        public PhoenixHakoniwaConfigData hakoniwaConfig;
        public HakoniwaSceneConfigData hakoniwaSceneConfig;
        public SceneLocationConfigData location;


        public HakoniwaEntityModule HakoniwaEntityModule { get; private set; }
        public HakoniwaSceneModule HakoniwaSceneModule { get; private set; }
        public HakoniwaQuestModule HakoniwaQuestModule { get; private set; }


        protected List<HakoniwaModule> m_modules = new List<HakoniwaModule>();


        public void Init(HakoniwaInfo initInfo)
        {
            hakoniwaConfig = initInfo.m_hakoniwaConfig;
            hakoniwaSceneConfig = initInfo.m_hakoniwaSceneConfig;
            InitModules();
        }

        public void UnInit()
        {
            UnInitModules();
        }

        protected void InitModules()
        {
            HakoniwaEntityModule = CreateModule<HakoniwaEntityModule>();
            HakoniwaSceneModule = CreateModule<HakoniwaSceneModule>();
            HakoniwaQuestModule = CreateModule<HakoniwaQuestModule>();

            foreach (HakoniwaModule module in m_modules)
            {
                module.Init();
            }
        }


        protected void UnInitModules()
        {
            foreach (HakoniwaModule module in m_modules)
            {
                module.UnInit();
            }
            m_modules.Clear();
        }


        protected T CreateModule<T>() where T : HakoniwaModule, new()
        {
            T module = new T();
            module.SetOwner(this);
            m_modules.Add(module);
            return module;
        }



        #region Public

        public List<HakoniwaEntity> GetHakoniwaEntities()
        {
            return HakoniwaEntityModule.entities;
        }

        #endregion
    }
}

