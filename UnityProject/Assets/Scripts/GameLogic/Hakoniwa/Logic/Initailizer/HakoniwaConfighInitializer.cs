using System;
using System.Collections;
using Newtonsoft.Json.Linq;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.Core;
using UnityEngine;
using YooAsset;

namespace Phoenix.Hakoniwa.Logic
{
    public class HakoniwaConfighInitializer : Initializer
    {
        private HakoniwaInfo m_hakoniwaInfo;


        public HakoniwaConfighInitializer(HakoniwaInfo info)
        {
            m_hakoniwaInfo = info;


        }

        protected override IEnumerator OnProcess()
        {
            String questGraphName = m_hakoniwaInfo.m_hakoniwaConfig.QuestGraphConfig.QuestGraphFile;
            String path = $"Assets/Res/ConfigData/QuestGraphData/{questGraphName}.json";
            yield return LoadQuestGraphConfig(path);
        }

        private IEnumerator LoadQuestGraphConfig(String path)
        {
            AssetHandle assetHandle = ResourceManager.instance.LoadAsync<UnityEngine.Object>(path);
            while (assetHandle.IsDone == false)
            {
                yield return null;
            }
            TextAsset questGraphAsset = assetHandle.AssetObject as TextAsset;
            if (questGraphAsset != null)
            {
                JObject jo = JObject.Parse(questGraphAsset.text);
                m_hakoniwaInfo.m_hakoniwaConfig.QuestGraphConfig.QuestGraph = DeserializeUtility.DeserializeNodeGraph(jo);
            }
            assetHandle.Release();
            ResourceManager.instance.TryUnloadUnusedAsset(path);
        }
    }
}

