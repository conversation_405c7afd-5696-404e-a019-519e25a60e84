
using System;

namespace Phoenix.Hakoniwa
{
    public interface IHakoniwaComponent<T>
    {
        public T Owner { get; set; }
        public void SetOwner(T owner);
        public void Tick(Single dt);
    }

    public abstract class HakoniwaComponent<T> : IHakoniwaComponent<T>
    {
        protected Boolean m_inited;

        public T Owner { get; set; }

        public void SetOwner(T owner)
        {
            Owner = owner;
        }

        public void Init()
        {
            OnInit();
            m_inited = true;
        }


        public void Tick(Single dt)
        {
            if (m_inited)
            {
                OnTick(dt);
            }
        }
        public void UnInit()
        {
            m_inited = false;
            OnUnInit();
        }


        protected virtual void OnInit() { }
        protected virtual void OnTick(Single dt) { }
        protected virtual void OnUnInit() { }
    }

}

