
using System;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Hakoniwa;
using Phoenix.Hakoniwa.Logic;

namespace Phoenix.GameLogic
{

    /// <summary>
    /// 箱庭状态现场类
    /// </summary>
    public class GameStateContextHakoniwa : StateContext
    {
        public Int32 m_hakoniwaId;
        public Int32 m_waypointId; // 记录点Id
        public HakoLocation m_hakoLocation;

        public override void OnRelease()
        {
            m_hakoniwaId = 0;
            m_waypointId = 0;
            m_hakoLocation = HakoLocation.invalid;
            base.OnRelease();
        }
    }


    /// <summary>
    /// 箱庭状态类
    /// </summary>
    public class GameStateHakoniwa : State
    {
        public override int stateIndex => (int)EGameState.Hakoniwa;

        private Boolean m_ready;

        private HakoniwaInfo m_hakoniwaInfo;
        private HakoniwaBase m_hakoniwaBase;
        private HakoniwaView m_hakoniwaView;




        protected override void OnEnter(StateContext context)
        {
            m_ready = false;
            GameStateContextHakoniwa hakoniwaContext = context as GameStateContextHakoniwa;
            if (hakoniwaContext == null)
            {
                return;
            }
            CollectHakoniwaInitInfo(hakoniwaContext);
            LoadConfigData();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            if (!m_ready)
            {
                return;
            }
            m_hakoniwaBase?.Tick(timeSlice);
            m_hakoniwaView?.Tick(timeSlice);
        }

        protected override void OnExit()
        {
            if (!m_ready)
            {
                return;
            }
            m_hakoniwaView.UnInit();
            m_hakoniwaBase?.UnInit();
            StaticHakoniwa.hakoniwaView = null;
            StaticHakoniwa.hakoniwaBase = null;
        }

        /// <summary> 收集箱庭初始化数据 </summary>
        private void CollectHakoniwaInitInfo(GameStateContextHakoniwa context)
        {
            PhoenixHakoniwaConfigData hakoniwaConfig = ConfigDataManager.instance.GetPhoenixHakoniwa(context.m_hakoniwaId);
            Int32 waypointId = context.m_waypointId == 0 ? hakoniwaConfig.InitWaypoint : context.m_waypointId;
            HakoniwaWaypointConfigData waypointConfig = ConfigDataManager.instance.GetHakoniwaWaypoint(waypointId);
            HakoniwaSceneConfigData hakoniwaSceneConfig = ConfigDataManager.instance.GetHakoniwaScene(waypointConfig.HakoSceneId);
            HakoLocation locationConfig = context.m_hakoLocation.isValid ? context.m_hakoLocation : waypointConfig.Location;

            m_hakoniwaInfo = new HakoniwaInfo();
            m_hakoniwaInfo.m_hakoniwaConfig = hakoniwaConfig;
            m_hakoniwaInfo.m_waypointConfig = waypointConfig;
            m_hakoniwaInfo.m_hakoniwaSceneConfig = hakoniwaSceneConfig;
            m_hakoniwaInfo.m_location = locationConfig;
        }

        /// <summary> 加载箱庭配置数据 </summary>
        private void LoadConfigData()
        {
            var questGraphInitializer = new HakoniwaConfighInitializer(m_hakoniwaInfo);
            questGraphInitializer.StartAsync((initializer) =>
            {
                if (m_isRunning == false)
                {
                    return;
                }
                InitHakoniwaInternal();
            });
        }


        private void InitHakoniwaInternal()
        {
            m_hakoniwaBase = new HakoniwaBase();
            m_hakoniwaView = new HakoniwaView();
            StaticHakoniwa.hakoniwaView = m_hakoniwaView;
            StaticHakoniwa.hakoniwaBase = m_hakoniwaBase;
            m_hakoniwaBase.Init(m_hakoniwaInfo);
            m_hakoniwaView.Init(m_hakoniwaBase);
            m_ready = true;
        }


        /// <summary> 箱庭内场景跳转 </summary>
        private void HakoniwaSceneJumpInternal()
        {

        }

    }
}
