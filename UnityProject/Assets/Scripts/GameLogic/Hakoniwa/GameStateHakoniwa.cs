
using System;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using Phoenix.Hakoniwa;
using Phoenix.Hakoniwa.Logic;

namespace Phoenix.GameLogic
{

    public class GameStateContextHakoniwa : StateContext
    {
        public Int32 m_hakoniwaId;
        public Int32 m_hakoniwaLocationId;

        public override void OnRelease()
        {
            m_hakoniwaId = 0;
            m_hakoniwaLocationId = 0;
            base.OnRelease();
        }
    }


    public class GameStateHakoniwa : State
    {
        public override int stateIndex => (int)EGameState.Hakoniwa;

        private Boolean m_ready;

        private HakoniwaInfo m_hakoniwaInfo;
        private HakoniwaBase m_hakoniwaBase;
        private HakoniwaView m_hakoniwaView;




        protected override void OnEnter(StateContext context)
        {
            m_ready = false;
            GameStateContextHakoniwa hakoniwaContext = context as GameStateContextHakoniwa;
            if (hakoniwaContext == null)
            {
                return;
            }
            CollectHakoniwaInitInfo(hakoniwaContext);
            LoadConfigData();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            if (!m_ready)
            {
                return;
            }

            m_hakoniwaView?.Tick(timeSlice);
        }

        protected override void OnExit()
        {
            if (!m_ready)
            {
                return;
            }
            m_hakoniwaView.UnInit();
            m_hakoniwaBase?.UnInit();
            StaticHakoniwa.hakoniwaView = null;
        }

        /// <summary> 收集箱庭初始化数据 </summary>
        private void CollectHakoniwaInitInfo(GameStateContextHakoniwa hakoniwaContext)
        {
            m_hakoniwaInfo = new HakoniwaInfo();
            m_hakoniwaInfo.m_hakoniwaConfig = ConfigDataManager.instance.GetPhoenixHakoniwa(hakoniwaContext.m_hakoniwaId);
            HakoniwaLocationConfigData hakoniwaLocation = ConfigDataManager.instance.GetHakoniwaLocation(hakoniwaContext.m_hakoniwaLocationId);
            m_hakoniwaInfo.m_hakoniwaSceneConfig = ConfigDataManager.instance.GetHakoniwaScene(hakoniwaLocation.HakoSceneId);
        }

        /// <summary> 加载箱庭配置数据 </summary>
        private void LoadConfigData()
        {
            var questGraphInitializer = new HakoniwaConfighInitializer(m_hakoniwaInfo);
            questGraphInitializer.StartAsync((initializer) =>
            {
                if (m_isRunning == false)
                {
                    return;
                }
                InitHakoniwaInternal();
            });
        }


        private void InitHakoniwaInternal()
        {
            m_hakoniwaBase = new HakoniwaBase();
            m_hakoniwaBase.Init(m_hakoniwaInfo);
            m_hakoniwaView = new HakoniwaView();
            m_hakoniwaView.Init(m_hakoniwaBase);
            StaticHakoniwa.hakoniwaView = m_hakoniwaView;
            m_ready = true;
        }


        /// <summary> 箱庭内场景跳转 </summary>
        private void HakoniwaSceneJumpInternal()
        {

        }

    }
}
