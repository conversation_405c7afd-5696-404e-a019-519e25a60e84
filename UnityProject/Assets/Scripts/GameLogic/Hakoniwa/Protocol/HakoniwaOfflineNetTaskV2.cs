using Cysharp.Threading.Tasks;
using Phoenix.MsgPackLogic.Protocol;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    /// <summary>
    /// Hakoniwa离线网络任务处理类 V2
    /// 模拟网络请求，用于离线模式或测试
    /// </summary>
    public class HakoniwaOfflineNetTaskV2 : HakoniwaNetTaskV2
    {
        #region Configuration

        /// <summary>
        /// 模拟网络延迟（毫秒）
        /// </summary>
        public int SimulatedNetworkDelay { get; set; } = 100;

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public bool EnableVerboseLogging { get; set; } = true;

        #endregion

        #region Enter/Exit Hakoniwa

        public override async UniTaskVoid SendHakoniwaEnter(int hakoniwaId, int waypointId)
        {
            LogOfflineOperation(nameof(SendHakoniwaEnter), hakoniwaId, waypointId);
            
            await SimulateNetworkDelay();
            
            try
            {
                // 模拟成功进入Hakoniwa
                StaticHakoniwa.EnterHakoniwa(hakoniwaId, waypointId, HakoLocation.invalid);
                
                // 模拟服务器响应
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    // 创建模拟的响应数据
                    var mockResponse = CreateMockHakoniwaEnterAck(hakoniwaId, waypointId);
                    comp.OnHakoniwaEnterAck(mockResponse);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaEnter));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaEnter), ex.Message);
            }
        }

        public override async UniTaskVoid SendHakoniwaGiveUp()
        {
            LogOfflineOperation(nameof(SendHakoniwaGiveUp));
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    comp.OnGiveUpHakoniwa();
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaGiveUp));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaGiveUp), ex.Message);
            }
        }

        #endregion

        #region Position Sync

        public override async UniTaskVoid SendHakoniwaPosSync(int waypointId, PosInfo posInfo)
        {
            if (EnableVerboseLogging)
            {
                LogOfflineOperation(nameof(SendHakoniwaPosSync), waypointId);
            }
            
            await SimulateNetworkDelay(50); // 位置同步使用更短的延迟
            
            try
            {
                StaticHakoniwa.hakoniwaBase?.SetPlayerPosInfo(waypointId, posInfo);
                
                if (EnableVerboseLogging)
                {
                    LogOfflineSuccess(nameof(SendHakoniwaPosSync));
                }
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaPosSync), ex.Message);
            }
        }

        #endregion

        #region Quest Related

        public override async UniTaskVoid SendHakoniwaReachPoint(int questId, int questCondIndex)
        {
            LogOfflineOperation(nameof(SendHakoniwaReachPoint), questId, questCondIndex);
            
            await SimulateNetworkDelay();
            
            try
            {
                StaticHakoniwa.hakoniwaBase?.OnHakoniwaReachPoint(questId, questCondIndex);
                
                // 模拟服务器响应
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    var mockResponse = CreateMockReachPointAck(questId, questCondIndex);
                    comp.OnReachPoint(mockResponse);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaReachPoint));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaReachPoint), ex.Message);
            }
        }

        #endregion

        #region Dialog Related

        public override async UniTaskVoid SendHakoniwaDialogComplete(int dialogId)
        {
            LogOfflineOperation(nameof(SendHakoniwaDialogComplete), dialogId);
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    var mockResponse = CreateMockDialogCompleteAck(dialogId);
                    comp.OnDialogComplete(mockResponse);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaDialogComplete));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaDialogComplete), ex.Message);
            }
        }

        #endregion

        #region Battle Related

        public override async UniTaskVoid SendHakoniwaBattleStart(int levelId)
        {
            LogOfflineOperation(nameof(SendHakoniwaBattleStart), levelId);
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    var mockBattleInfo = CreateMockBattleProcessInfo(levelId);
                    comp.OnStartHakoniwaBattle(mockBattleInfo);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaBattleStart));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaBattleStart), ex.Message);
            }
        }

        public override async UniTaskVoid SendHakoniwaBattleFinish(HakoniwaBattleProcessInfo battleProcessInfo)
        {
            LogOfflineOperation(nameof(SendHakoniwaBattleFinish));
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    var mockResponse = CreateMockBattleFinishAck(battleProcessInfo);
                    comp.OnHakoniwaBattleFinishAck(mockResponse);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaBattleFinish));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaBattleFinish), ex.Message);
            }
        }

        #endregion

        #region Monster Related

        public override async UniTaskVoid SendHakoniwaMonsterOneHit(int monsterId)
        {
            LogOfflineOperation(nameof(SendHakoniwaMonsterOneHit), monsterId);
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    // 模拟一击必杀成功
                    comp.OnMonsterOneHit(monsterId);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaMonsterOneHit));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaMonsterOneHit), ex.Message);
            }
        }

        #endregion

        #region Treasure Related

        public override async UniTaskVoid SendHakoniwaTreasureBoxOpen(int treasureBoxId)
        {
            LogOfflineOperation(nameof(SendHakoniwaTreasureBoxOpen), treasureBoxId);
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    comp.OnOpenHakoniwaTreasureBox(treasureBoxId);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaTreasureBoxOpen));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaTreasureBoxOpen), ex.Message);
            }
        }

        #endregion

        #region Entity Gear Related

        public override async UniTaskVoid SendHakoniwaEntityGearSave(int gearId, int state)
        {
            LogOfflineOperation(nameof(SendHakoniwaEntityGearSave), gearId, state);
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    comp.OnEntityGearSave(gearId, state);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaEntityGearSave));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaEntityGearSave), ex.Message);
            }
        }

        public override async UniTaskVoid SendHakoniwaQuit()
        {
            LogOfflineOperation(nameof(SendHakoniwaQuit));
            
            await SimulateNetworkDelay();
            
            try
            {
                var comp = GetHakoniwaComponent();
                if (comp != null)
                {
                    var currentHakoniwa = comp.GetCurrHakoniwa();
                    int hakoniwaId = currentHakoniwa?.HakoniwaId ?? 0;
                    comp.OnQuitHakoniwa(hakoniwaId);
                }
                
                LogOfflineSuccess(nameof(SendHakoniwaQuit));
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendHakoniwaQuit), ex.Message);
            }
        }

        #endregion

        #region Mock Response Creation

        /// <summary>
        /// 创建模拟的进入Hakoniwa响应
        /// </summary>
        private HakoniwaEnterAck CreateMockHakoniwaEnterAck(int hakoniwaId, int waypointId)
        {
            return new HakoniwaEnterAck
            {
                ErrCode = 0, // 成功
                HakoniwaId = hakoniwaId,
                EnterWayPointId = waypointId
            };
        }

        /// <summary>
        /// 创建模拟的到达点响应
        /// </summary>
        private HakoniwaReachPointAck CreateMockReachPointAck(int questId, int questCondIndex)
        {
            return new HakoniwaReachPointAck
            {
                ErrCode = 0, // 成功
                QuestId = questId,
                QuestCondIndex = questCondIndex
            };
        }

        /// <summary>
        /// 创建模拟的对话完成响应
        /// </summary>
        private HakoniwaDialogCompleteAck CreateMockDialogCompleteAck(int dialogId)
        {
            return new HakoniwaDialogCompleteAck
            {
                ErrCode = 0, // 成功
                DialogId = dialogId
            };
        }

        /// <summary>
        /// 创建模拟的战斗过程信息
        /// </summary>
        private HakoniwaBattleProcessInfo CreateMockBattleProcessInfo(int levelId)
        {
            return new HakoniwaBattleProcessInfo
            {
                LevelId = levelId,
                // 可以添加更多模拟数据
            };
        }

        /// <summary>
        /// 创建模拟的战斗结束响应
        /// </summary>
        private HakoniwaBattleFinishAck CreateMockBattleFinishAck(HakoniwaBattleProcessInfo battleInfo)
        {
            return new HakoniwaBattleFinishAck
            {
                ErrCode = 0, // 成功
                BattleProcessInfo = battleInfo,
                // 可以添加击杀的怪物列表等
            };
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// 模拟网络延迟
        /// </summary>
        /// <param name="delayMs">延迟毫秒数，如果不指定则使用默认值</param>
        private async UniTask SimulateNetworkDelay(int? delayMs = null)
        {
            int delay = delayMs ?? SimulatedNetworkDelay;
            if (delay > 0)
            {
                await UniTask.Delay(delay);
            }
        }

        /// <summary>
        /// 记录离线操作日志
        /// </summary>
        private void LogOfflineOperation(string operation, params object[] parameters)
        {
            if (!EnableVerboseLogging && operation.Contains("PosSync"))
                return; // 位置同步日志太多，可选择性关闭

            string paramStr = parameters != null && parameters.Length > 0 ? string.Join(", ", parameters) : "";
            Debug.Log($"[HakoniwaOfflineNetTaskV2] {operation}({paramStr}) - OFFLINE MODE");
        }

        /// <summary>
        /// 记录离线操作成功日志
        /// </summary>
        private void LogOfflineSuccess(string operation)
        {
            if (!EnableVerboseLogging && operation.Contains("PosSync"))
                return;

            Debug.Log($"[HakoniwaOfflineNetTaskV2] {operation} - SUCCESS (OFFLINE)");
        }

        /// <summary>
        /// 记录离线操作错误日志
        /// </summary>
        private void LogOfflineError(string operation, string error)
        {
            Debug.LogError($"[HakoniwaOfflineNetTaskV2] {operation} - ERROR: {error}");
        }

        /// <summary>
        /// 检查离线模式是否可用
        /// </summary>
        protected override bool IsNetworkConnected()
        {
            return true; // 离线模式总是"连接"的
        }

        /// <summary>
        /// 获取离线模式状态信息
        /// </summary>
        public override string GetCurrentHakoniwaStatus()
        {
            string baseStatus = base.GetCurrentHakoniwaStatus();
            return $"{baseStatus} [OFFLINE MODE]";
        }

        /// <summary>
        /// 设置模拟网络延迟
        /// </summary>
        /// <param name="delayMs">延迟毫秒数</param>
        public void SetSimulatedDelay(int delayMs)
        {
            SimulatedNetworkDelay = Mathf.Max(0, delayMs);
            Debug.Log($"[HakoniwaOfflineNetTaskV2] Simulated network delay set to {SimulatedNetworkDelay}ms");
        }

        /// <summary>
        /// 启用/禁用详细日志
        /// </summary>
        /// <param name="enable">是否启用</param>
        public void SetVerboseLogging(bool enable)
        {
            EnableVerboseLogging = enable;
            Debug.Log($"[HakoniwaOfflineNetTaskV2] Verbose logging {(enable ? "enabled" : "disabled")}");
        }

        #endregion

        #region Batch Operations (Offline Optimized)

        /// <summary>
        /// 批量发送位置同步请求（离线优化版本）
        /// </summary>
        public override async UniTaskVoid SendBatchPosSync(System.Collections.Generic.List<(int waypointId, PosInfo posInfo)> syncData)
        {
            if (syncData == null || syncData.Count == 0)
                return;

            try
            {
                // 离线模式可以批量处理，无需延迟
                foreach (var (waypointId, posInfo) in syncData)
                {
                    StaticHakoniwa.hakoniwaBase?.SetPlayerPosInfo(waypointId, posInfo);
                }

                if (EnableVerboseLogging)
                {
                    Debug.Log($"[HakoniwaOfflineNetTaskV2] Batch position sync completed: {syncData.Count} items");
                }
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendBatchPosSync), ex.Message);
            }
        }

        /// <summary>
        /// 批量发送任务到达点请求（离线优化版本）
        /// </summary>
        public override async UniTaskVoid SendBatchReachPoint(System.Collections.Generic.List<(int questId, int questCondIndex)> reachData)
        {
            if (reachData == null || reachData.Count == 0)
                return;

            try
            {
                // 离线模式可以快速处理
                foreach (var (questId, questCondIndex) in reachData)
                {
                    StaticHakoniwa.hakoniwaBase?.OnHakoniwaReachPoint(questId, questCondIndex);
                    
                    // 模拟服务器响应
                    var comp = GetHakoniwaComponent();
                    if (comp != null)
                    {
                        var mockResponse = CreateMockReachPointAck(questId, questCondIndex);
                        comp.OnReachPoint(mockResponse);
                    }
                    
                    // 添加小延迟避免处理过快
                    await UniTask.Delay(10);
                }

                Debug.Log($"[HakoniwaOfflineNetTaskV2] Batch reach point completed: {reachData.Count} items");
            }
            catch (System.Exception ex)
            {
                LogOfflineError(nameof(SendBatchReachPoint), ex.Message);
            }
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 打印离线模式调试信息
        /// </summary>
        public override void PrintDebugInfo()
        {
            Debug.Log("=== HakoniwaOfflineNetTaskV2 Debug Info ===");
            Debug.Log($"Mode: OFFLINE");
            Debug.Log($"Simulated Delay: {SimulatedNetworkDelay}ms");
            Debug.Log($"Verbose Logging: {EnableVerboseLogging}");
            Debug.Log($"Hakoniwa Status: {GetCurrentHakoniwaStatus()}");
            Debug.Log("===========================================");
        }

        /// <summary>
        /// 测试所有离线协议
        /// </summary>
        public async UniTaskVoid TestAllOfflineProtocols()
        {
            Debug.Log("[HakoniwaOfflineNetTaskV2] Starting offline protocol test...");

            try
            {
                // 测试进入Hakoniwa
                await SendHakoniwaEnter(1001, 1);
                await UniTask.Delay(200);

                // 测试位置同步
                var posInfo = new PosInfo { X = 10, Y = 0, Z = 10 };
                await SendHakoniwaPosSync(1, posInfo);
                await UniTask.Delay(200);

                // 测试对话完成
                await SendHakoniwaDialogComplete(2001);
                await UniTask.Delay(200);

                // 测试到达点
                await SendHakoniwaReachPoint(3001, 0);
                await UniTask.Delay(200);

                // 测试战斗开始
                await SendHakoniwaBattleStart(4001);
                await UniTask.Delay(200);

                // 测试宝箱开启
                await SendHakoniwaTreasureBoxOpen(5001);
                await UniTask.Delay(200);

                Debug.Log("[HakoniwaOfflineNetTaskV2] Offline protocol test completed successfully!");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[HakoniwaOfflineNetTaskV2] Offline protocol test failed: {ex.Message}");
            }
        }

        #endregion
    }
}
