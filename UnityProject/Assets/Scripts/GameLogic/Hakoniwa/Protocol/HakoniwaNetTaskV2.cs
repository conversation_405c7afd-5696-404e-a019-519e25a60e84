using Cysharp.Threading.Tasks;
using Phoenix.Common.Network;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.CommonDefine;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    /// <summary>
    /// Hakoniwa网络任务处理类 V2
    /// 负责处理所有Hakoniwa相关的网络请求和响应
    /// 修复了与NetworkClient返回值类型不匹配的问题
    /// </summary>
    public class HakoniwaNetTaskV2
    {
        #region Enter/Exit Hakoniwa

        /// <summary>
        /// 发送进入Hakoniwa请求
        /// </summary>
        /// <param name="hakoniwaId">Hakoniwa ID</param>
        /// <param name="waypointId">进入点ID</param>
        public virtual async UniTaskVoid SendHakoniwaEnter(int hakoniwaId, int waypointId)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaEnter), hakoniwaId, waypointId);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaEnter), "Network not connected");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaEnterReq(hakoniwaId, waypointId);
                if (success)
                {
                    Debug.Log($"[HakoniwaNetTaskV2] Successfully sent HakoniwaEnter request: HakoniwaId={hakoniwaId}, WaypointId={waypointId}");
                    // 服务器响应会通过OnServerMessage(HakoniwaEnterAck)处理
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaEnter), $"Request failed for HakoniwaId={hakoniwaId}, WaypointId={waypointId}");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaEnter), ex.Message);
            }
        }

        /// <summary>
        /// 发送放弃Hakoniwa请求
        /// </summary>
        public virtual async UniTaskVoid SendHakoniwaGiveUp()
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaGiveUp));
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaGiveUp), "Network not connected");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaGiveUpReq();
                if (success)
                {
                    Debug.Log("[HakoniwaNetTaskV2] Successfully sent HakoniwaGiveUp request");
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaGiveUp), "Request failed");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaGiveUp), ex.Message);
            }
        }

        #endregion

        #region Position Sync

        /// <summary>
        /// 发送位置同步请求
        /// </summary>
        /// <param name="waypointId">路径点ID</param>
        /// <param name="posInfo">位置信息</param>
        public virtual async UniTaskVoid SendHakoniwaPosSync(int waypointId, PosInfo posInfo)
        {
            try
            {
                if (!IsNetworkConnected())
                {
                    // 位置同步失败时不记录错误，避免日志过多
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaPosSyncReq(waypointId, posInfo);
                if (success)
                {
                    // 位置同步成功，通常不需要特别处理
                    Debug.Log($"[HakoniwaNetTaskV2] Successfully synced position: WaypointId={waypointId}");
                }
                else
                {
                    Debug.LogWarning($"[HakoniwaNetTaskV2] Failed to sync position: WaypointId={waypointId}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[HakoniwaNetTaskV2] SendHakoniwaPosSync exception: {ex.Message}");
            }
        }

        #endregion

        #region Quest Related

        /// <summary>
        /// 发送到达点请求
        /// </summary>
        /// <param name="questId">任务ID</param>
        /// <param name="questCondIndex">任务条件索引</param>
        public virtual async UniTaskVoid SendHakoniwaReachPoint(int questId, int questCondIndex)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaReachPoint), questId, questCondIndex);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaReachPoint), "Network not connected");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaReachPointReq(questId, questCondIndex);
                if (success)
                {
                    Debug.Log($"[HakoniwaNetTaskV2] Successfully sent ReachPoint request: QuestId={questId}, CondIndex={questCondIndex}");
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaReachPoint), $"Request failed for QuestId={questId}, CondIndex={questCondIndex}");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaReachPoint), ex.Message);
            }
        }

        #endregion

        #region Dialog Related

        /// <summary>
        /// 发送对话完成请求
        /// </summary>
        /// <param name="dialogId">对话ID</param>
        public virtual async UniTaskVoid SendHakoniwaDialogComplete(int dialogId)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaDialogComplete), dialogId);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaDialogComplete), "Network not connected");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaDialogCompleteReq(dialogId);
                if (success)
                {
                    Debug.Log($"[HakoniwaNetTaskV2] Successfully sent DialogComplete request: DialogId={dialogId}");
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaDialogComplete), $"Request failed for DialogId={dialogId}");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaDialogComplete), ex.Message);
            }
        }

        #endregion

        #region Battle Related

        /// <summary>
        /// 发送战斗开始请求
        /// </summary>
        /// <param name="levelId">关卡ID</param>
        public virtual async UniTaskVoid SendHakoniwaBattleStart(int levelId)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaBattleStart), levelId);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaBattleStart), "Network not connected");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaBattleStartReq(levelId);
                if (success)
                {
                    Debug.Log($"[HakoniwaNetTaskV2] Successfully sent BattleStart request: LevelId={levelId}");
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaBattleStart), $"Request failed for LevelId={levelId}");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaBattleStart), ex.Message);
            }
        }

        /// <summary>
        /// 发送战斗结束请求
        /// </summary>
        /// <param name="battleProcessInfo">战斗过程信息</param>
        public virtual async UniTaskVoid SendHakoniwaBattleFinish(HakoniwaBattleProcessInfo battleProcessInfo)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaBattleFinish));
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaBattleFinish), "Network not connected");
                    return;
                }

                if (battleProcessInfo == null)
                {
                    LogNetworkError(nameof(SendHakoniwaBattleFinish), "BattleProcessInfo is null");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaBattleFinishReq(battleProcessInfo);
                if (success)
                {
                    Debug.Log("[HakoniwaNetTaskV2] Successfully sent BattleFinish request");
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaBattleFinish), "Request failed");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaBattleFinish), ex.Message);
            }
        }

        #endregion

        #region Treasure Related

        /// <summary>
        /// 发送宝箱开启请求
        /// </summary>
        /// <param name="treasureBoxId">宝箱ID</param>
        public virtual async UniTaskVoid SendHakoniwaTreasureBoxOpen(int treasureBoxId)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaTreasureBoxOpen), treasureBoxId);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaTreasureBoxOpen), "Network not connected");
                    return;
                }

                bool success = await NetworkClient.instance.SendHakoniwaTreasureBoxOpenReq(treasureBoxId);
                if (success)
                {
                    Debug.Log($"[HakoniwaNetTaskV2] Successfully sent TreasureBoxOpen request: TreasureBoxId={treasureBoxId}");
                }
                else
                {
                    LogNetworkError(nameof(SendHakoniwaTreasureBoxOpen), $"Request failed for TreasureBoxId={treasureBoxId}");
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaTreasureBoxOpen), ex.Message);
            }
        }

        #endregion

        #region Missing Protocol Methods (需要在NetworkClient中添加)

        /// <summary>
        /// 发送怪物一击必杀请求
        /// 注意：此方法需要在NetworkClient中添加对应的实现
        /// </summary>
        /// <param name="monsterId">怪物ID</param>
        public virtual async UniTaskVoid SendHakoniwaMonsterOneHit(int monsterId)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaMonsterOneHit), monsterId);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaMonsterOneHit), "Network not connected");
                    return;
                }

                // TODO: 需要在NetworkClient中添加SendHakoniwaMonsterOneHitReq方法
                Debug.LogWarning($"[HakoniwaNetTaskV2] SendHakoniwaMonsterOneHit not implemented in NetworkClient: MonsterId={monsterId}");
                await UniTask.Delay(1); // 占位符
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaMonsterOneHit), ex.Message);
            }
        }

        /// <summary>
        /// 发送实体装备保存请求
        /// 注意：此方法需要在NetworkClient中添加对应的实现
        /// </summary>
        /// <param name="gearId">装备ID</param>
        /// <param name="state">状态</param>
        public virtual async UniTaskVoid SendHakoniwaEntityGearSave(int gearId, int state)
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaEntityGearSave), gearId, state);
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaEntityGearSave), "Network not connected");
                    return;
                }

                // TODO: 需要在NetworkClient中添加SendHakoniwaEntityGearSaveReq方法
                Debug.LogWarning($"[HakoniwaNetTaskV2] SendHakoniwaEntityGearSave not implemented in NetworkClient: GearId={gearId}, State={state}");
                await UniTask.Delay(1); // 占位符
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaEntityGearSave), ex.Message);
            }
        }

        /// <summary>
        /// 发送退出Hakoniwa请求
        /// 注意：此方法需要在NetworkClient中添加对应的实现
        /// </summary>
        public virtual async UniTaskVoid SendHakoniwaQuit()
        {
            try
            {
                LogNetworkRequest(nameof(SendHakoniwaQuit));
                
                if (!IsNetworkConnected())
                {
                    LogNetworkError(nameof(SendHakoniwaQuit), "Network not connected");
                    return;
                }

                // TODO: 需要在NetworkClient中添加SendHakoniwaQuitReq方法
                Debug.LogWarning("[HakoniwaNetTaskV2] SendHakoniwaQuit not implemented in NetworkClient");
                await UniTask.Delay(1); // 占位符
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendHakoniwaQuit), ex.Message);
            }
        }

        #endregion

        #region Batch Operations

        /// <summary>
        /// 批量发送位置同步请求
        /// </summary>
        /// <param name="syncData">同步数据列表</param>
        public virtual async UniTaskVoid SendBatchPosSync(System.Collections.Generic.List<(int waypointId, PosInfo posInfo)> syncData)
        {
            if (syncData == null || syncData.Count == 0)
                return;

            try
            {
                foreach (var (waypointId, posInfo) in syncData)
                {
                    await SendHakoniwaPosSync(waypointId, posInfo);
                    // 添加小延迟避免请求过于频繁
                    await UniTask.Delay(50);
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendBatchPosSync), ex.Message);
            }
        }

        /// <summary>
        /// 批量发送任务到达点请求
        /// </summary>
        /// <param name="reachData">到达点数据列表</param>
        public virtual async UniTaskVoid SendBatchReachPoint(System.Collections.Generic.List<(int questId, int questCondIndex)> reachData)
        {
            if (reachData == null || reachData.Count == 0)
                return;

            try
            {
                foreach (var (questId, questCondIndex) in reachData)
                {
                    await SendHakoniwaReachPoint(questId, questCondIndex);
                    // 添加小延迟避免请求过于频繁
                    await UniTask.Delay(100);
                }
            }
            catch (System.Exception ex)
            {
                LogNetworkError(nameof(SendBatchReachPoint), ex.Message);
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// 检查网络连接状态
        /// </summary>
        /// <returns>是否连接</returns>
        protected virtual bool IsNetworkConnected()
        {
            return NetworkClient.instance != null && NetworkClient.instance.IsConnected;
        }

        /// <summary>
        /// 获取当前Hakoniwa组件
        /// </summary>
        /// <returns>Hakoniwa组件</returns>
        protected virtual GamePlayerCompHakoniwa GetHakoniwaComponent()
        {
            return StaticHakoniwa.GamePlayer?.CompHakoniwa;
        }

        /// <summary>
        /// 记录网络请求日志
        /// </summary>
        /// <param name="methodName">方法名</param>
        /// <param name="parameters">参数</param>
        protected virtual void LogNetworkRequest(string methodName, params object[] parameters)
        {
            string paramStr = parameters != null && parameters.Length > 0 ? string.Join(", ", parameters) : "";
            Debug.Log($"[HakoniwaNetTaskV2] {methodName}({paramStr})");
        }

        /// <summary>
        /// 记录网络错误日志
        /// </summary>
        /// <param name="methodName">方法名</param>
        /// <param name="error">错误信息</param>
        protected virtual void LogNetworkError(string methodName, string error)
        {
            Debug.LogError($"[HakoniwaNetTaskV2] {methodName} failed: {error}");
        }

        /// <summary>
        /// 验证请求参数
        /// </summary>
        /// <param name="methodName">方法名</param>
        /// <param name="parameters">参数数组</param>
        /// <returns>是否有效</returns>
        protected virtual bool ValidateParameters(string methodName, params object[] parameters)
        {
            if (parameters == null)
                return true;

            foreach (var param in parameters)
            {
                if (param == null)
                {
                    LogNetworkError(methodName, "Parameter is null");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取当前Hakoniwa状态
        /// </summary>
        /// <returns>状态描述</returns>
        public virtual string GetCurrentHakoniwaStatus()
        {
            var comp = GetHakoniwaComponent();
            if (comp == null)
                return "HakoniwaComponent is null";

            var hakoniwa = comp.GetCurrHakoniwa();
            if (hakoniwa == null)
                return "Current Hakoniwa is null";

            return $"HakoniwaId: {hakoniwa.HakoniwaId}, IsActive: {hakoniwa.IsActive}";
        }

        /// <summary>
        /// 检查是否在Hakoniwa中
        /// </summary>
        /// <returns>是否在Hakoniwa中</returns>
        public virtual bool IsInHakoniwa()
        {
            var comp = GetHakoniwaComponent();
            return comp?.GetCurrHakoniwa()?.IsActive ?? false;
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 打印调试信息
        /// </summary>
        public virtual void PrintDebugInfo()
        {
            Debug.Log("=== HakoniwaNetTaskV2 Debug Info ===");
            Debug.Log($"Network Connected: {IsNetworkConnected()}");
            Debug.Log($"In Hakoniwa: {IsInHakoniwa()}");
            Debug.Log($"Hakoniwa Status: {GetCurrentHakoniwaStatus()}");
            Debug.Log("=====================================");
        }

        #endregion
    }
}
