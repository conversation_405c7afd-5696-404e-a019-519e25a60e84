using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public class MovePlusTask
    {
        private Single m_speed;
        private MoveWithDirectionTask m_moveTask = new MoveWithDirectionTask();
        private List<Vector3> m_pathList = new List<Vector3>();
        private Action<Boolean> m_onMoveEnd;

        public Boolean IsMoving => m_moveTask.IsMoving;

        public List<Vector3> m_remainPaths = new List<Vector3>();
        /// <summary> 剩余的路径点 </summary>
        public List<Vector3> RemainPaths
        {
            get
            {
                m_remainPaths.Clear();
                if (m_moveTask.IsMoving)
                {
                    m_remainPaths.Add(m_moveTask.CurPosition);
                }
                m_remainPaths.AddRange(m_pathList);
                return m_remainPaths;
            }
        }


        public void Start(Vector3 startPos, Vector3 endPos, Single speed, Action<Boolean> moveEnd)
        {
            m_pathList.Clear();
            m_pathList.Add(startPos);
            m_pathList.Add(endPos);
            m_speed = speed;
            m_onMoveEnd = moveEnd;
            if (Vector3.SqrMagnitude(startPos - endPos) > 1e-5)
            {
                MoveNext(true);
            }
            else
            {
                StopInternal(true);
            }
        }
        public void Start(Vector3[] paths, Single speed, Action<Boolean> moveEnd)
        {
            Start(paths.ToList(), speed, moveEnd);
        }
        public void Start(List<Vector3> paths, Single speed, Action<Boolean> moveEnd)
        {
            StartInternal(paths, speed, moveEnd);
        }

        public void Tick(Single dt, ref Vector3 currentPos, ref Quaternion quaternion)
        {
            if (m_moveTask.IsMoving)
            {
                m_moveTask.Tick(dt, ref currentPos, ref quaternion);
            }
        }
        public void Stop()
        {
            m_moveTask.Stop();
        }



        private void StartInternal(List<Vector3> pathList, Single speed, Action<Boolean> moveEnd)
        {
            m_pathList.Clear();
            m_pathList.AddRange(pathList);
            m_speed = speed;
            m_onMoveEnd = moveEnd;
            MoveNext(true);
        }
        private void MoveNext(Boolean completed)
        {
            if (!completed || m_pathList.Count <= 1)
            {
                StopInternal(true);
                return;
            }

            Vector3 startPos = m_pathList[0];
            Vector3 endPos = m_pathList[1];
            m_pathList.RemoveAt(0);
            m_moveTask.Start(startPos, endPos, m_speed, MoveNext);
        }
        private void StopInternal(Boolean completed)
        {
            m_pathList.Clear();
            m_onMoveEnd?.Invoke(completed);
            m_onMoveEnd = null;
        }

    }


    /// <summary> 固定速度，起点到目标点Lerp </summary>
    public class MoveWithLerpTask
    {
        public Boolean IsMoving { get; private set; }
        private Vector3 m_startPos, m_endPos;
        private Single m_time, m_totalTime;
        private Action<Boolean> m_onMoveEnd;

        public Vector3 CurPosition => Vector3.Lerp(m_startPos, m_endPos, m_time / m_totalTime);

        public void Start(Vector3 startPos, Vector3 endPos, Single speed, Action<Boolean> moveEnd)
        {
            m_startPos = startPos;
            m_endPos = endPos;
            m_time = 0;
            m_totalTime = Vector3.Distance(startPos, endPos) / speed;
            m_onMoveEnd = moveEnd;
            IsMoving = true;
        }

        public void Tick(Single dt, ref Vector3 currentPos, ref Quaternion quaternion)
        {
            if (!IsMoving)
            {
                return;
            }
            m_time += dt;
            currentPos = Vector3.Lerp(m_startPos, m_endPos, m_time / m_totalTime);
            Vector3 dir = m_endPos - currentPos;
            if (dir.sqrMagnitude > 1e-5)
            {
                quaternion = Quaternion.LookRotation(dir.normalized);
            }
            if (m_time >= m_totalTime)
            {
                StopInternal(true);
            }
        }

        public void Stop()
        {
            StopInternal(false);
        }

        public void StopInternal(Boolean completed)
        {
            IsMoving = false;
            m_time = m_totalTime = 0;
            Action<Boolean> temp = m_onMoveEnd;
            m_onMoveEnd = null;
            temp?.Invoke(completed);
        }

    }


    /// <summary> 使用方向增量去移动 </summary>
    public class MoveWithDirectionTask
    {
        public Boolean IsMoving { get; private set; }
        private Vector3 m_startPos, m_endPos, m_direction;
        private Quaternion m_quaternion;
        private Single m_speed;
        private Action<Boolean> m_onMoveEnd;

        public Vector3 CurPosition => m_startPos;

        public void Start(Vector3 startPos, Vector3 endPos, Single speed, Action<Boolean> moveEnd)
        {
            m_startPos = startPos;
            m_endPos = endPos;
            m_direction = (endPos - startPos).normalized;
            m_direction.y = 0;
            m_quaternion = Quaternion.LookRotation(m_direction);
            m_speed = speed;// Vector3.Distance(startPos, endPos) / speed;
            m_onMoveEnd = moveEnd;
            IsMoving = true;
        }

        public void Tick(Single dt, ref Vector3 currentPos, ref Quaternion quaternion)
        {
            if (!IsMoving)
            {
                return;
            }
            quaternion = m_quaternion;
            Vector3 deltaPosition = m_speed * dt * m_direction;
            currentPos = m_startPos + deltaPosition;

            if (Vector3.Dot(m_startPos - currentPos, m_endPos -  currentPos) > 0)
            {
                currentPos = m_endPos;
                StopInternal(true);
            }
            else
            {
                m_startPos = currentPos;
            }
        }

        public void Stop()
        {
            StopInternal(false);
        }

        public void StopInternal(Boolean completed)
        {
            IsMoving = false;
            m_speed = 0;
            Action<Boolean> temp = m_onMoveEnd;
            m_onMoveEnd = null;
            temp?.Invoke(completed);
        }

    }
}

