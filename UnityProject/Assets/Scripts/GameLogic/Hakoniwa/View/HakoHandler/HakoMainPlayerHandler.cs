
using System.Collections.Generic;
using System;
using UnityEngine;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Hakoniwa
{
    public interface IHakoMainPlayer
    {
        Boolean IsMoving { get; }
        Vector3 MainPlayerPosition { get; }
        Quaternion MainPlayerRotation { get; }
    }


    public class HakoMainPlayerHandler : <PERSON><PERSON><PERSON><PERSON><PERSON>, IHakoMainPlayer
    {
        private HakoniwaSkillConfigData m_skillConfig;
        private HakoCameraHandler m_hakoCameraHandler;
        private InteractDetection m_interactDetection = new InteractDetection();
        private MovePlusTask m_movePlusTask = new MovePlusTask();

        private List<HakoEntityView> m_interactableEntityViews = new List<HakoEntityView>();
        private HakoEntityView m_interactEntityView;
        private Int32 m_interactEntityViewUid;
        private Single m_tickTimer = 0;


        public Boolean IsMoving { get; private set; }
        public Vector3 MainPlayerPosition { get; private set; }
        public Quaternion MainPlayerRotation { get; private set; }
        public HakoEntityView InteractEntityView
        {
            get
            {
                return m_interactEntityView;
            }
            set
            {
                m_interactEntityViewUid = value == null ? 0 : value.EntityUid;
                m_interactEntityView = value;
            }
        }




        public void SetMovePath(Vector3[] paths, Action<Boolean> moveEnd = null)
        {
            m_movePlusTask.Stop();
            m_movePlusTask.Start(paths, StaticHakoniwaData.EntityRunSpeed, moveEnd);
        }

        public void SetDestination(HakoLocation location, Boolean immediately = false, Action<Boolean> moveEnd = null)
        {
            if (immediately)
            {
                if (m_movePlusTask.IsMoving)
                {
                    m_movePlusTask.Stop();
                }
                SetPositionInternal(location);
                moveEnd?.Invoke(true);
            }
            else
            {
                m_movePlusTask.Stop();
                m_movePlusTask.Start(MainPlayerPosition, location, StaticHakoniwaData.EntityRunSpeed, moveEnd);
            }
        }

        public void StopMove()
        {
            m_movePlusTask.Stop();
        }

        public void SetDirection(Vector3 lookPos)
        {
            Quaternion quaternion = Quaternion.LookRotation((MainPlayerPosition - lookPos).normalized);
            SetRotationInternal(quaternion);
        }




        protected override void OnInit()
        {
            m_hakoCameraHandler = Owner.GetHakoniwaHandler<HakoCameraHandler>();
            m_interactDetection.Init();
            m_interactDetection.SetOwner(this);
            m_skillConfig = ConfigDataManager.instance.GetHakoniwaSkill(Owner.HakoniwaBase.hakoniwaConfig.SkillId);

            EventManager.instance.RegisterListener<Int32>(EventID.HakoEntityViewRemove, EventOnHakoEntityViewRemove);
            EventManager.instance.RegisterListener(EventID.HakoniwaUISwitchInteractEntity, EventOnSwitchInteractEntity);

            EventManager.instance.RegisterListener<Vector2>(EventID.HakoInputMoveBegin, EventOnHakoInputMoveBegin);
            EventManager.instance.RegisterListener<Vector2>(EventID.HakoInputMoving, EventOnHakoInputMoving);
            EventManager.instance.RegisterListener(EventID.HakoInputMoveEnd, EventOnHakoInputMoveEnd);
            EventManager.instance.RegisterListener<Vector3>(EventID.HakoCameraTouchTap, EventOnHakoCameraTouchTap);


            SetDestination(Owner.HakoniwaBase.location, true);
            m_hakoCameraHandler.SetCameraFollowPosition(MainPlayerPosition, true);
        }
        protected override void OnTick(Single dt)
        {
            Tick4MainPlayerMoving(dt);
            Tick4SelectHakoEntityView(dt);
        }
        protected override void OnUnInit()
        {
            m_interactDetection.UnInit();
            m_hakoCameraHandler = null;
            m_skillConfig = null;

            EventManager.instance.UnRegisterListener<Int32>(EventID.HakoEntityViewRemove, EventOnHakoEntityViewRemove);
            EventManager.instance.UnRegisterListener(EventID.HakoniwaUISwitchInteractEntity, EventOnSwitchInteractEntity);

            EventManager.instance.UnRegisterListener<Vector2>(EventID.HakoInputMoveBegin, EventOnHakoInputMoveBegin);
            EventManager.instance.UnRegisterListener<Vector2>(EventID.HakoInputMoving, EventOnHakoInputMoving);
            EventManager.instance.UnRegisterListener(EventID.HakoInputMoveEnd, EventOnHakoInputMoveEnd);
            EventManager.instance.UnRegisterListener<Vector3>(EventID.HakoCameraTouchTap, EventOnHakoCameraTouchTap);
        }

        protected void Tick4MainPlayerMoving(Single dt)
        {
            if (IsMoving = m_movePlusTask.IsMoving)
            {
                Vector3 position = MainPlayerPosition;
                Quaternion rotation = MainPlayerRotation;
                m_movePlusTask.Tick(dt, ref position, ref rotation);
                SetPositionInternal(position);
                SetRotationInternal(rotation);
                m_hakoCameraHandler.SetCameraFollowPosition(MainPlayerPosition);
            }
        }
        protected void Tick4SelectHakoEntityView(Single dt)
        {
            m_tickTimer += dt;
            if (m_tickTimer < StaticHakoniwaData.InteractDetectionTickInternal)
            {
                // 移动时忽略检测间隔
                return;
            }
            m_tickTimer = 0;

            Single radius = GetInteractDetectionRadius();
            List<HakoEntityView> entityViews = m_interactDetection.GetActiveInteractable(radius, InteractCheckFunc);
            Boolean dirty = false;

            // 1 尝试重新填充可交互角色列表，计算脏标记
            if (IsMoving || entityViews.Count != m_interactableEntityViews.Count)
            {
                dirty = true;
                m_interactableEntityViews.Clear();
                m_interactableEntityViews.AddRange(entityViews);
            }
            else
            {
                for (Int32 i = 0; i < m_interactableEntityViews.Count; i++)
                {
                    if (!entityViews.Contains(m_interactableEntityViews[i]))
                    {
                        dirty = true;
                        break;
                    }
                }
                if (dirty)
                {
                    m_interactableEntityViews.Clear();
                    m_interactableEntityViews.AddRange(entityViews);
                }
            }

            // 2 检测当前可交互角色是否还在列表中
            HakoEntityView tempEntityView = m_interactableEntityViews.Count > 0 ? m_interactableEntityViews[0] : null;
            if (IsMoving || InteractEntityView == null || !m_interactableEntityViews.Contains(InteractEntityView))
            {
                InteractEntityView = tempEntityView;
                dirty = true;
            }

            // 3 根据脏标记状态，切换当前切换角色
            if (dirty)
            {
                EventManager.instance.Broadcast<HakoEntityView, Int32>(EventID.HakoniwaInteractable2Entity, InteractEntityView, m_interactableEntityViews.Count);
            }
        }



        protected void EventOnSwitchInteractEntity()
        {
            if (m_interactableEntityViews.Count > 1)
            {
                HakoEntityView entityView = m_interactableEntityViews[0];
                m_interactableEntityViews.RemoveAt(0);
                m_interactableEntityViews.Add(entityView);
                InteractEntityView = m_interactableEntityViews[0];
                EventManager.instance.Broadcast<HakoEntityView, Int32>(EventID.HakoniwaInteractable2Entity, InteractEntityView, m_interactableEntityViews.Count);
            }
        }

        protected void EventOnHakoEntityViewRemove(Int32 entityUid)
        {
            if (m_interactEntityViewUid != 0 && m_interactEntityViewUid == entityUid)
            {
                InteractEntityView = null;
                EventManager.instance.Broadcast<HakoEntityView, Int32>(EventID.HakoniwaInteractable2Entity, null, m_interactableEntityViews.Count);
            }
        }

        protected void EventOnHakoInputMoveBegin(Vector2 factor)
        {

        }

        protected void EventOnHakoInputMoving(Vector2 factor)
        {
            MoveInternal(factor);
        }

        protected void EventOnHakoInputMoveEnd()
        {
            StopMove();
        }

        protected void EventOnHakoCameraTouchTap(Vector3 targetPos)
        {
            if (NavigationHelper.CalculatePath(MainPlayerPosition, targetPos, out Vector3[] paths))
            {
                SetMovePath(paths);
            }
        }


        private void MoveInternal(Vector2 factor)
        {
            Vector3 forward = Owner.CameraRotation * Vector3.forward * factor.y;
            Vector3 right = Owner.CameraRotation * Vector3.right * factor.x;
            Vector3 direction = new Vector3(forward.x + right.x, 0, forward.z + right.z);
            Vector3 deltaPosition = direction.normalized * StaticHakoniwaData.EntityRunSpeed * Time.deltaTime;
            Vector3 targetPosition = MainPlayerPosition + deltaPosition;
            if (NavigationHelper.SamplePosition(ref targetPosition))
            {
                SetDestination(targetPosition);
            }
        }

        private void SetPositionInternal(Vector3 position)
        {
            HakoSceneHandler.RepairPosition(ref position);
            MainPlayerPosition = position;
        }

        private void SetRotationInternal(Quaternion quaternion)
        {
            MainPlayerRotation = quaternion;
        }

        private Single GetInteractDetectionRadius()
        {
            if (m_skillConfig == null)
            {
                return StaticHakoniwaData.InteractDetectionRadius;
            }
            return Mathf.Max(StaticHakoniwaData.InteractDetectionRadius, m_skillConfig.Range);
        }

        private Boolean InteractCheckFunc(HakoEntityView entityView)
        {
            Boolean result = false;
            if (entityView.CanInteract)
            {
                Single entitySqrDistance = entityView.SqrDistance;
                if (m_skillConfig != null)
                {
                    Single sqrSkillRange = m_skillConfig.Range * m_skillConfig.Range;
                    if (entitySqrDistance < sqrSkillRange && entityView.HakoEntity.EntityType == m_skillConfig.InterestEntityType)
                    {
                        result = true;
                    }
                }
                if (result == false)
                {
                    Single sqrRadius = StaticHakoniwaData.InteractDetectionRadius * StaticHakoniwaData.InteractDetectionRadius;
                    if (entitySqrDistance < sqrRadius)
                    {
                        result = true;
                    }
                }
            }
            return result;
        }

    }


    /// <summary> 交互检测逻辑模块 </summary>
    public class InteractDetection : HakoniwaComponent<IHakoMainPlayer>
    {
        /// <summary> 场景角色缓存列表【InstanceId --> HakoEntityView】 </summary>
        private Dictionary<Int32, HakoEntityView> _cached4HakoEntityViews = new Dictionary<Int32, HakoEntityView>();
        /// <summary> Physics.OverlapSphereNonAlloc 辅助列表 </summary>
        private Collider[] _tempColliderBuffer = new Collider[10];
        private List<Collider> _tempColliderList = new List<Collider>();

        /// <summary> 可被交互的角色缓存列表 </summary>
        private List<HakoEntityView> _cachedActivableEntity = new List<HakoEntityView>(8);
        /// <summary> 逻辑判断辅助列表 </summary>
        private List<HakoEntityView> _tempHakoEntityList = new List<HakoEntityView>();


        protected override void OnInit()
        {
            _cachedActivableEntity.Clear();
            _cached4HakoEntityViews.Clear();
            _tempHakoEntityList.Clear();
            _tempColliderList.Clear();
        }


        public List<HakoEntityView> GetActiveInteractable(Single radius, Func<HakoEntityView, Boolean> interactCheckFunc)
        {
            _tempColliderList.Clear();
            _tempHakoEntityList.Clear();
            Int32 layerMask = 1 << LayerMask.NameToLayer("Character");
            if (TryGetOverlapSphereEntity(ref _tempColliderList, Owner.MainPlayerPosition, radius, layerMask))
            {
                foreach (Collider collider in _tempColliderList)
                {
                    Int32 instanceId = collider.GetInstanceID();
                    if (!_cached4HakoEntityViews.TryGetValue(instanceId, out HakoEntityView hakoActorView))
                    {
                        hakoActorView = collider.gameObject.GetComponent<HakoEntityView>();
                        _cached4HakoEntityViews[instanceId] = hakoActorView;
                    }
                    if (hakoActorView != null && interactCheckFunc(hakoActorView))
                    {
                        _tempHakoEntityList.Add(hakoActorView);
                    }
                }
                //// 1 不在outerCircle里面的要移除
                //for (Int32 i = 0; i < _cachedActivableEntity.Count;)
                //{
                //    HakoEntityView hakoActorView = _cachedActivableEntity[i];
                //    if (_tempHakoEntityList.Contains(hakoActorView))
                //    {
                //        i++;
                //    }
                //    else
                //    {
                //        _cachedActivableEntity.RemoveAt(i);
                //    }
                //}

                //// 2 在innerCircle里面的要添加
                //foreach (HakoEntityView entityView in _tempHakoEntityList)
                //{
                //    if (interactCheckFunc(entityView) && !_cachedActivableEntity.Contains(entityView))
                //    {
                //        _cachedActivableEntity.Add(entityView);
                //    }
                //}
                _cachedActivableEntity.Clear();
                _cachedActivableEntity.AddRange(_tempHakoEntityList);
                _cachedActivableEntity.Sort(SortByDistance);
            }
            else
            {
                _cachedActivableEntity.Clear();
            }
            return _cachedActivableEntity;
        }

        private Int32 SortByDistance(HakoEntityView h1, HakoEntityView h2)
        {
            return h1.SqrDistance.CompareTo(h2.SqrDistance);
        }

        private Boolean TryGetOverlapSphereEntity(ref List<Collider> interactables, Vector3 position, Single radius, Int32 layerMask)
        {
            Boolean result = false;
            interactables.Clear();
            Int32 count = Physics.OverlapSphereNonAlloc(position, radius, _tempColliderBuffer, layerMask);
            for (Int32 i = 0; i < count; i++)
            {
                _tempColliderBuffer[i].GetInstanceID();
                interactables.Add(_tempColliderBuffer[i]);
                result = true;
            }
            return result;
        }

    }

}

