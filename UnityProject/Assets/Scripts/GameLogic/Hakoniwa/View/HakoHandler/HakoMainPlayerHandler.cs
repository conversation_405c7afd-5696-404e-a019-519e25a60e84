
using System.Collections.Generic;
using System;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Hakoniwa
{
    public interface IHakoMainPlayer
    {
        Boolean IsMoving { get; }
        Vector3 MainPlayerPosition { get; }
        Quaternion MainPlayerRotation { get; }
    }


    public class HakoMainPlayerHandler : <PERSON><PERSON><PERSON><PERSON><PERSON>, IHakoMainPlayer
    {
        protected static Single MoveSpeed = 0.1f;
        private const Single InteractDetectionRadius = 2;


        private HakoCameraHandler m_hakoCameraHandler;
        private InteractDetection m_interactDetection = new InteractDetection();
        private MovePlusTask m_movePlusTask = new MovePlusTask();
        private HakoEntityView m_activeEntityView;

        public Boolean IsMoving { get; private set; }
        public Vector3 MainPlayerPosition { get; private set; }
        public Quaternion MainPlayerRotation { get; private set; }


        public void SetMovePath(Vector3[] paths, Action<Boolean> moveEnd = null)
        {
            m_movePlusTask.Stop();
            m_movePlusTask.Start(paths, MoveSpeed, moveEnd);
        }

        public void SetDestination(Vector3 destination, Boolean immediately = false, Action<Boolean> moveEnd = null)
        {
            if (immediately)
            {
                if (m_movePlusTask.IsMoving)
                {
                    m_movePlusTask.Stop();
                }
                SetPositionInternal(destination);
                moveEnd?.Invoke(true);
            }
            else
            {
                m_movePlusTask.Stop();
                m_movePlusTask.Start(MainPlayerPosition, destination, MoveSpeed, moveEnd);
            }
        }

        public void StopMove()
        {
            m_movePlusTask.Stop();
        }
        public void SetDirection(Vector3 lookPos)
        {
            Quaternion quaternion = Quaternion.LookRotation((MainPlayerPosition - lookPos).normalized);
            SetRotationInternal(quaternion);
        }




        protected override void OnInit()
        {
            m_hakoCameraHandler = Owner.GetHakoniwaHandler<HakoCameraHandler>();
            m_interactDetection.Init();
            m_interactDetection.SetOwner(this);


            EventManager.instance.RegisterListener<Vector2>(EventID.HakoInputMoveBegin, EventOnHakoInputMoveBegin);
            EventManager.instance.RegisterListener<Vector2>(EventID.HakoInputMoving, EventOnHakoInputMoving);
            EventManager.instance.RegisterListener(EventID.HakoInputMoveEnd, EventOnHakoInputMoveEnd);
            EventManager.instance.RegisterListener<Vector3>(EventID.HakoCameraTouchTap, EventOnHakoCameraTouchTap);


            SetDestination(new Vector3(0, 0, -10), true);
            m_hakoCameraHandler.SetCameraFollowPosition(MainPlayerPosition, true);
        }
        protected override void OnTick(float dt)
        {
            Tick4MainPlayerMoving(dt);
            Tick4SelectHakoEntityView();
        }
        protected void Tick4MainPlayerMoving(float dt)
        {
            if (IsMoving = m_movePlusTask.IsMoving)
            {
                Vector3 position = MainPlayerPosition;
                Quaternion rotation = MainPlayerRotation;
                m_movePlusTask.Tick(dt, ref position, ref rotation);
                SetPositionInternal(position);
                SetRotationInternal(rotation);
                m_hakoCameraHandler.SetCameraFollowPosition(MainPlayerPosition);
            }
        }
        protected void Tick4SelectHakoEntityView()
        {
            HakoEntityView tempActorView = m_interactDetection.GetActiveInteractable(InteractDetectionRadius);
            if (m_activeEntityView != tempActorView)
            {
                if (m_activeEntityView != null)
                {
                    m_activeEntityView = null;
                    EventManager.instance.Broadcast<HakoEntityView>(EventID.HakoniwaInteractable2Entity, null);
                }
                if (tempActorView != null)
                {
                    m_activeEntityView = tempActorView;
                    EventManager.instance.Broadcast(EventID.HakoniwaInteractable2Entity, m_activeEntityView);
                }
            }
        }
        protected override void OnUnInit()
        {
            m_interactDetection.UnInit();
            m_hakoCameraHandler = null;


            EventManager.instance.UnRegisterListener<Vector2>(EventID.HakoInputMoveBegin, EventOnHakoInputMoveBegin);
            EventManager.instance.UnRegisterListener<Vector2>(EventID.HakoInputMoving, EventOnHakoInputMoving);
            EventManager.instance.UnRegisterListener(EventID.HakoInputMoveEnd, EventOnHakoInputMoveEnd);
            EventManager.instance.UnRegisterListener<Vector3>(EventID.HakoCameraTouchTap, EventOnHakoCameraTouchTap);
        }



        protected void EventOnHakoInputMoveBegin(Vector2 factor)
        {

        }

        protected void EventOnHakoInputMoving(Vector2 factor)
        {
            MoveInternal(factor);
        }

        protected void EventOnHakoInputMoveEnd()
        {
            StopMove();
        }

        protected void EventOnHakoCameraTouchTap(Vector3 targetPos)
        {
            if (NavigationHelper.CalculatePath(MainPlayerPosition, targetPos, out Vector3[] paths))
            {
                SetMovePath(paths);
            }
        }


        private void MoveInternal(Vector2 factor)
        {
            Vector3 forward = Owner.CameraRotation * Vector3.forward * factor.y;
            Vector3 right = Owner.CameraRotation * Vector3.right * factor.x;
            Vector3 direction = new Vector3(forward.x + right.x, 0, forward.z + right.z);
            Vector3 deltaPosition = direction.normalized * MoveSpeed;
            Vector3 targetPosition = MainPlayerPosition + deltaPosition;
            if (NavigationHelper.SamplePosition(ref targetPosition))
            {
                SetDestination(targetPosition);
            }
        }

        private void SetPositionInternal(Vector3 position)
        {
            HakoSceneHandler.RepairPosition(ref position);
            MainPlayerPosition = position;
        }

        private void SetRotationInternal(Quaternion quaternion)
        {
            MainPlayerRotation = quaternion;
        }

    }


    /// <summary> 交互检测逻辑模块 </summary>
    public class InteractDetection : HakoniwaComponent<IHakoMainPlayer>
    {
        /// <summary> 场景角色缓存列表【InstanceId --> HakoEntityView】 </summary>
        private Dictionary<Int32, HakoEntityView> _cached4HakoEntityViews = new Dictionary<Int32, HakoEntityView>();
        /// <summary> Physics.OverlapSphereNonAlloc 辅助列表 </summary>
        private Collider[] _tempColliderBuffer = new Collider[10];
        private List<Collider> _tempColliderList = new List<Collider>();

        /// <summary> 可被交互的角色缓存列表 </summary>
        private List<HakoEntityView> _cachedActivableEntity = new List<HakoEntityView>(8);
        /// <summary> 逻辑判断辅助列表 </summary>
        private List<HakoEntityView> _tempHakoEntityList = new List<HakoEntityView>();


        protected override void OnInit()
        {
            _cachedActivableEntity.Clear();
            _cached4HakoEntityViews.Clear();
            _tempHakoEntityList.Clear();
            _tempColliderList.Clear();
        }


        public HakoEntityView GetActiveInteractable(Single radius, Comparison<HakoEntityView> specialSortRule = null)
        {
            _tempColliderList.Clear();
            _tempHakoEntityList.Clear();
            Int32 layerMask = 1 << LayerMask.NameToLayer("Character");
            if (TryGetOverlapSphereEntity(ref _tempColliderList, Owner.MainPlayerPosition, radius, layerMask))
            {
                foreach (Collider collider in _tempColliderList)
                {
                    Int32 instanceId = collider.GetInstanceID();
                    if (!_cached4HakoEntityViews.TryGetValue(instanceId, out HakoEntityView hakoActorView))
                    {
                        hakoActorView = collider.gameObject.GetComponent<HakoEntityView>();
                        _cached4HakoEntityViews[instanceId] = hakoActorView;
                    }
                    _tempHakoEntityList.Add(hakoActorView);
                }
                // 1 不在outerCircle里面的要移除
                for (Int32 i = 0; i < _cachedActivableEntity.Count;)
                {
                    HakoEntityView hakoActorView = _cachedActivableEntity[i];
                    if (_tempHakoEntityList.Contains(hakoActorView))
                    {
                        i++;
                    }
                    else
                    {
                        _cachedActivableEntity.RemoveAt(i);
                    }
                }

                // 2 在innerCircle里面的要添加
                foreach (HakoEntityView actorView in _tempHakoEntityList)
                {
                    if (actorView.SqrDistance <= radius * radius && !_cachedActivableEntity.Contains(actorView))
                    {
                        _cachedActivableEntity.Add(actorView);
                    }
                }
                specialSortRule ??= SortByDistance;
                _cachedActivableEntity.Sort(specialSortRule);
            }
            else
            {
                _cachedActivableEntity.Clear();
            }
            return _cachedActivableEntity.Count > 0 ? _cachedActivableEntity[0] : null;
        }

        private Int32 SortByDistance(HakoEntityView h1, HakoEntityView h2)
        {
            return h1.SqrDistance.CompareTo(h2.SqrDistance);
        }

        private Boolean TryGetOverlapSphereEntity(ref List<Collider> interactables, Vector3 position, Single radius, Int32 layerMask)
        {
            Boolean result = false;
            interactables.Clear();
            Int32 count = Physics.OverlapSphereNonAlloc(position, radius, _tempColliderBuffer, layerMask);
            for (Int32 i = 0; i < count; i++)
            {
                _tempColliderBuffer[i].GetInstanceID();
                interactables.Add(_tempColliderBuffer[i]);
                result = true;
            }
            return result;
        }

    }

}

