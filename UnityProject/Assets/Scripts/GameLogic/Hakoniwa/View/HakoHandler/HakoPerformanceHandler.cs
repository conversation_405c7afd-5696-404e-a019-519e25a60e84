
using System;
using Phoenix.Core;
using Phoenix.StoryGraphModule;

namespace Phoenix.Hakoniwa
{
    public class HakoPerformanceHandler : <PERSON><PERSON>Handler
    {
        private StoryGraphOwner m_storyGraphOwner;
        private PerformancePlayer m_performancePlayer;


        public void StartPerformance(Performance perform, Boolean immediately = false)
        {
            if (perform == null)
            {
                return;
            }
            m_performancePlayer.StartPerformance(perform, immediately);
        }




        protected override void OnInit()
        {
            m_storyGraphOwner = Owner.SceneEntityRoot.AddComponent<StoryGraphOwner>();
            m_performancePlayer = new PerformancePlayer();
            EventManager.instance.RegisterListener<String>(EventID.HakoStoryPerform, OnHakoStoryPerform);
        }

        protected override void OnTick(Single dt)
        {
            if (m_performancePlayer != null && m_performancePlayer.IsRunning)
            {
                m_performancePlayer.Tick(dt);
            }
        }


        protected override void OnUnInit()
        {
            UnityEngine.Object.DestroyImmediate(m_storyGraphOwner);
            m_performancePlayer = null;
            EventManager.instance.UnRegisterListener<String>(EventID.HakoStoryPerform, OnHakoStoryPerform);
        }


        protected void OnHakoStoryPerform(String path)
        {
            path = @"Assets/Res/WorldConfigData/StoryData/StoryGraphData.asset";
            Performance performance = StoryGraphPerformance.Create(m_storyGraphOwner, path);
            StartPerformance(performance);
        }
    }

}

