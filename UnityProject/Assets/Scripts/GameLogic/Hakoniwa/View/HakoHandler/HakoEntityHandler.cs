
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.Hakoniwa.Logic;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public interface IHakoEntityHandler
    {
        GameObject Root { get; }
        void AddHakoEntityView(HakoEntityView view);
        HakoEntityView GetHakoEntityView(Int32 uid);
        void RemoveHakoEntityView(Int32 uid);
    }


    public class HakoEntityHandler : <PERSON><PERSON><PERSON><PERSON><PERSON>, IHakoEntityHandler
    {
        public HakoEntityViewLoader hakoEntityViewLoader = new HakoEntityViewLoader();
        public Dictionary<Int32, HakoEntityView> hakoEntityViews = new Dictionary<Int32, HakoEntityView>();

        private GameObject m_selectedEffect;


        public GameObject Root { get; private set; }


        protected override void OnInit()
        {
            Root = Owner.SceneEntityRoot;
            hakoEntityViewLoader.Init();
            hakoEntityViewLoader.SetOwner(this);

            m_selectedEffect = GameObject.CreatePrimitive(PrimitiveType.Cube);
            m_selectedEffect.transform.localScale = Vector3.one * 0.3f;
            m_selectedEffect.SetParent(Root);
            m_selectedEffect.SetActiveSafely(false);



            EventManager.instance.RegisterListener<HakoniwaEntity>(EventID.HakoEntityViewAdd, OnHakoEntityAdd);
            EventManager.instance.RegisterListener<Int32>(EventID.HakoEntityViewRemove, OnHakoEntityRemove);
            EventManager.instance.RegisterListener<HakoEntityView>(EventID.HakoniwaInteractable2Entity, OnInteractable2Entity);
        }
        protected override void OnTick(float dt)
        {
            foreach (var item in hakoEntityViews)
            {
                item.Value.Tick(dt);
            }
            Test();
        }
        protected override void OnUnInit()
        {
            Root = null;
            hakoEntityViewLoader.UnInit();
            GameObject.DestroyImmediate(m_selectedEffect);
            EventManager.instance.UnRegisterListener<HakoniwaEntity>(EventID.HakoEntityViewAdd, OnHakoEntityAdd);
            EventManager.instance.UnRegisterListener<Int32>(EventID.HakoEntityViewRemove, OnHakoEntityRemove);
            EventManager.instance.UnRegisterListener<HakoEntityView>(EventID.HakoniwaInteractable2Entity, OnInteractable2Entity);
        }


        protected void OnHakoEntityAdd(HakoniwaEntity hakoEntity)
        {
            if (hakoEntityViews.ContainsKey(hakoEntity.Uid))
            {
                return;
            }

            hakoEntityViewLoader.HakoEntityAdd(hakoEntity);
        }
        protected void OnHakoEntityRemove(Int32 uid)
        {
            hakoEntityViewLoader.HakoEntityRemove(uid);
        }

        void IHakoEntityHandler.AddHakoEntityView(HakoEntityView view)
        {
            hakoEntityViews.Add(view.EntityUid, view);
        }
        public HakoEntityView GetHakoEntityView(Int32 uid)
        {
            HakoEntityView hakoEntityView = null;
            if (!hakoEntityViews.TryGetValue(uid, out hakoEntityView))
            {
                Debug.Log($"[Hakoniwa] 获取的角色不存在 uid={uid}");
            }
            return hakoEntityView;
        }
        void IHakoEntityHandler.RemoveHakoEntityView(Int32 uid)
        {
            if (hakoEntityViews.ContainsKey(uid))
            {
                hakoEntityViews.Remove(uid);
            }
        }

        private void OnInteractable2Entity(HakoEntityView entityView)
        {
            if (entityView != null)
            {
                Vector3 tempPos = entityView.transform.position;
                m_selectedEffect.transform.position = tempPos + Vector3.up * 2;
                m_selectedEffect.SetActiveSafely(true);
            }
            else
            {
                m_selectedEffect.SetActiveSafely(false);
            }
        }

        #region TestRegion

        public void Test()
        {
            if (Input.GetKeyDown(KeyCode.Alpha0))
            {
                List<Int32> uids = new List<Int32>();
                foreach (var item in hakoEntityViews)
                {
                    uids.Add(item.Key);
                }
                foreach (Int32 uid in uids)
                {
                    EventManager.instance.Broadcast(EventID.HakoEntityViewRemove, uid);
                }
            }
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                List<HakoniwaEntity> entities = Owner.HakoniwaBase.GetHakoniwaEntities();
                foreach (HakoniwaEntity hakoEntity in entities)
                {
                    EventManager.instance.Broadcast(EventID.HakoEntityViewAdd, hakoEntity);
                }
            }
        }

        #endregion
    }


    public static class HakoEntityViewHelper
    {
        /// <summary>
        /// 资源已经就绪的情况下，创建箱庭角色View
        /// </summary>
        /// <param name="hakoEntity"></param>
        /// <returns></returns>
        public static HakoEntityView Spawn(HakoniwaEntity hakoEntity, GameObject parent)
        {
            HakoEntityView hakoEntityView;
            EntitySkinConfigData skinData = ConfigDataManager.instance.GetEntitySkin(hakoEntity.SkinId);
            GameObject go = ResourceHandleManager.instance.SpawnGameObject(skinData.PrefabPath, parent);
            switch (hakoEntity.EntityType)
            {
                case HakoEntityType.Player:
                    hakoEntityView = go.AddComponent<HakoPlayerView>();
                    break;
                case HakoEntityType.Npc:
                    hakoEntityView = go.AddComponent<HakoNpcView>();
                    break;
                default:
                    hakoEntityView = go.AddComponent<HakoEntityView>();
                    break;
            }
            hakoEntityView.Init(hakoEntity);
            return hakoEntityView;
        }

        /// <summary>
        /// 回收箱庭角色View
        /// </summary>
        /// <param name="hakoEntityView"></param>
        public static void Despawn(HakoEntityView hakoEntityView)
        {
            if (hakoEntityView == null)
            {
                return;
            }
            GameObject viewObject = hakoEntityView.gameObject;
            hakoEntityView.UnInit();
            GameObject.DestroyImmediate(hakoEntityView);
            ResourceHandleManager.instance.DespawnGameObject(viewObject);
        }
    }


    public class HakoEntityViewLoader : HakoniwaComponent<IHakoEntityHandler>
    {
        private readonly List<CommandInfo> m_commands = new List<CommandInfo>();
        private Boolean m_isResourceLoading;


        protected void Excute()
        {
            if (m_isResourceLoading)
            {
                return;
            }
            if (m_commands.Count > 0)
            {
                CommandInfo command = m_commands[0];
                m_commands.RemoveAt(0);
                switch (command.CommandType)
                {
                    case LoadCommandType.Add:
                        m_isResourceLoading = true;
                        HakoEntityViewLoaderContext context = ClassPoolManager.instance.Fetch<HakoEntityViewLoaderContext>();
                        context.CollectionResourcePath(command.HakoEntity);
                        ResourceHandleManager.instance.StartLoad(context, true, (LoaderProviderBase provider) =>
                        {
                            m_isResourceLoading = false;
                            AddHakoEntityView(command.HakoEntity);
                            Excute();
                        });
                        break;
                    case LoadCommandType.Remove:
                        RemoveHakoEntityView(command.EntityUid);
                        ResourceHandleManager.instance.ReleaseEntity(command.EntityUid);
                        Excute();
                        break;
                }
            }
        }


        protected void AddHakoEntityView(HakoniwaEntity hakoEntity)
        {
            HakoEntityView hakoEntityView = HakoEntityViewHelper.Spawn(hakoEntity, Owner.Root);
            Owner.AddHakoEntityView(hakoEntityView);
        }
        protected void RemoveHakoEntityView(Int32 entityUid)
        {
            HakoEntityView hakoEntityView = Owner.GetHakoEntityView(entityUid);
            Owner.RemoveHakoEntityView(entityUid);
            HakoEntityViewHelper.Despawn(hakoEntityView);
        }



        public void HakoEntityAdd(HakoniwaEntity hakoEntity)
        {
            m_commands.Add(new CommandInfo(LoadCommandType.Add, hakoEntity));
            Excute();
        }
        public void HakoEntityRemove(Int32 uid)
        {
            m_commands.Add(new CommandInfo(LoadCommandType.Remove, uid));
            Excute();
        }



        internal enum LoadCommandType : byte
        {
            Add,
            Remove,
        }
        internal class CommandInfo
        {
            public CommandInfo(LoadCommandType commandType, HakoniwaEntity hakoEntity)
            {
                CommandType = commandType;
                HakoEntity = hakoEntity;
            }
            public CommandInfo(LoadCommandType commandType, Int32 entityUid)
            {
                CommandType = commandType;
                EntityUid = entityUid;
            }
            public LoadCommandType CommandType { get; private set; }
            public HakoniwaEntity HakoEntity { get; private set; }
            public Int32 EntityUid { get; private set; }
        }
        internal class HakoEntityViewLoaderContext : LoaderContext
        {
            public void CollectionResourcePath(HakoniwaEntity hakoEntity)
            {
                Initialize(hakoEntity.Uid);
                EntitySkinConfigData skinData = ConfigDataManager.instance.GetEntitySkin(hakoEntity.SkinId);
                AddResourcePath(skinData.PrefabPath, AssetType.Prefab);

                ActorAnimationConfig animConfig = ResourceHandleManager.instance.LoadAssetSync<ActorAnimationConfig>(
                    skinData.AnimationConfigPath, String.Empty, hakoEntity.Uid, AssetType.Asset);
                String defaultAnimPath = animConfig.collection.GetPath(hakoEntity.DefaultAnimationName);
                AddResourcePath(defaultAnimPath, AssetType.Animation);
            }
        }
    }
}

