
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.Hakoniwa.Logic;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public interface IHakoEntityHandler
    {
        GameObject Root { get; }
        void AddHakoEntityView(HakoEntityView view);
        HakoEntityView GetHakoEntityView(Int32 uid);
        T GetHakoEntityView<T>(Int32 uid) where T : HakoEntityView;
        void RemoveHakoEntityView(Int32 uid);
    }


    public class HakoEntityHandler : <PERSON><PERSON><PERSON>and<PERSON>, IHakoEntityHandler
    {
        public HakoEntityViewLoader hakoEntityViewLoader = new HakoEntityViewLoader();
        public Dictionary<Int32, HakoEntityView> hakoEntityViews = new Dictionary<Int32, HakoEntityView>();

        private GameObject m_selectedEffect;


        public GameObject Root { get; private set; }


        protected override void OnInit()
        {
            Root = Owner.SceneEntityRoot;
            hakoEntityViewLoader.Init();
            hakoEntityViewLoader.SetOwner(this);

            m_selectedEffect = GameObject.CreatePrimitive(PrimitiveType.Cube);
            m_selectedEffect.transform.localScale = Vector3.one * 0.3f;
            m_selectedEffect.SetParent(Root);
            m_selectedEffect.SetActiveSafely(false);



            EventManager.instance.RegisterListener<HakoniwaEntityObject>(EventID.HakoEntityViewAdd, OnHakoEntityAdd);
            EventManager.instance.RegisterListener<Int32>(EventID.HakoEntityViewRemove, OnHakoEntityRemove);
            EventManager.instance.RegisterListener<HakoEntityView, Int32>(EventID.HakoniwaInteractable2Entity, OnInteractable2Entity);



            List<HakoniwaEntityObject> entities = Owner.HakoniwaBase.GetAllHakoniwaEntity();
            foreach (HakoniwaEntityObject hakoEntity in entities)
            {
                hakoEntityViewLoader.HakoEntityAdd(hakoEntity);
            }
        }
        protected override void OnTick(float dt)
        {
            foreach (var item in hakoEntityViews)
            {
                item.Value.Tick(dt);
            }
            Test();
        }
        protected override void OnUnInit()
        {
            Root = null;
            hakoEntityViewLoader.UnInit();
            GameObject.DestroyImmediate(m_selectedEffect);
            EventManager.instance.UnRegisterListener<HakoniwaEntityObject>(EventID.HakoEntityViewAdd, OnHakoEntityAdd);
            EventManager.instance.UnRegisterListener<Int32>(EventID.HakoEntityViewRemove, OnHakoEntityRemove);
            EventManager.instance.UnRegisterListener<HakoEntityView, Int32>(EventID.HakoniwaInteractable2Entity, OnInteractable2Entity);

            List<Int32> hakoniwaEntityUids = new List<Int32>();
            hakoniwaEntityUids.AddRange(hakoEntityViews.Keys);
            foreach (Int32 entityUid in hakoniwaEntityUids)
            {
                hakoEntityViewLoader.HakoEntityRemove(entityUid);
            }
        }


        protected void OnHakoEntityAdd(HakoniwaEntityObject hakoEntity)
        {
            if (hakoEntityViews.ContainsKey(hakoEntity.Uid))
            {
                return;
            }

            hakoEntityViewLoader.HakoEntityAdd(hakoEntity);
        }
        protected void OnHakoEntityRemove(Int32 uid)
        {
            if (uid < 0)
            {
                return;
            }

            hakoEntityViewLoader.HakoEntityRemove(uid);
        }

        void IHakoEntityHandler.AddHakoEntityView(HakoEntityView view)
        {
            hakoEntityViews.Add(view.EntityUid, view);
        }
        public HakoEntityView GetHakoEntityView(Int32 uid)
        {
            HakoEntityView hakoEntityView = null;
            if (!hakoEntityViews.TryGetValue(uid, out hakoEntityView))
            {
                //Debug.Log($"[Hakoniwa] 获取的角色不存在 uid={uid}");
            }
            return hakoEntityView;
        }
        public T GetHakoEntityView<T>(int uid) where T : HakoEntityView
        {
            HakoEntityView hakoEntityView = null;
            if (!hakoEntityViews.TryGetValue(uid, out hakoEntityView))
            {
                //Debug.Log($"[Hakoniwa] 获取的角色不存在 uid={uid}");
            }
            return hakoEntityView as T;
        }
        void IHakoEntityHandler.RemoveHakoEntityView(Int32 uid)
        {
            if (hakoEntityViews.ContainsKey(uid))
            {
                hakoEntityViews.Remove(uid);
            }
        }

        public HakoEntityView GetHakoEntityViewWithType(HakoniwaEntityType entityType)
        {
            HakoEntityView hakoEntityView = null;
            foreach (var item in hakoEntityViews)
            {
                if (item.Value.EntityType == entityType)
                {
                    hakoEntityView = item.Value;
                    break;
                }
            }
            return hakoEntityView;
        }

        public List<HakoEntityView> GetAllHakoniwaEntityView()
        {
            List<HakoEntityView> entities = new List<HakoEntityView>();
            foreach (var item in hakoEntityViews)
            {
                entities.Add(item.Value);
            }
            return entities;
        }

        private void OnInteractable2Entity(HakoEntityView entityView, Int32 interactableEntityCount)
        {
            if (entityView != null)
            {
                Vector3 tempPos = entityView.transform.position;
                m_selectedEffect.transform.position = tempPos + Vector3.up * 2;
                m_selectedEffect.SetActiveSafely(true);
            }
            else
            {
                m_selectedEffect.SetActiveSafely(false);
            }
        }

        #region TestRegion

        public void Test()
        {
            if (Input.GetKeyDown(KeyCode.Alpha0))
            {
                List<Int32> uids = new List<Int32>();
                foreach (var item in hakoEntityViews)
                {
                    uids.Add(item.Key);
                }
                foreach (Int32 uid in uids)
                {
                    StaticHakoniwa.hakoniwaBase.HakoEntityRemove(uid);
                }
            }
        }


        #endregion
    }


    public static class HakoEntityViewHelper
    {
        /// <summary>
        /// 资源已经就绪的情况下，创建箱庭角色View
        /// </summary>
        /// <param name="hakoEntity"></param>
        /// <returns></returns>
        public static HakoEntityView Spawn(HakoniwaEntityObject hakoEntity, GameObject parent)
        {
            GameObject go = null;
            switch (hakoEntity.EntityType)
            {
                case HakoniwaEntityType.Player:
                case HakoniwaEntityType.Npc:
                case HakoniwaEntityType.Monster:
                    go = ResourceHandleManager.instance.SpawnGameObject(hakoEntity.EntitySkinConfigData.PrefabPath, parent);
                    break;
                case HakoniwaEntityType.Gear:
                    if (hakoEntity.EntitySkinConfigData != null)
                    {
                        go = ResourceHandleManager.instance.SpawnGameObject(hakoEntity.EntitySkinConfigData.PrefabPath, parent);
                    }
                    else
                    {
                        go = new GameObject(hakoEntity.Name);
                        go.SetParent(parent);
                    }
                    break;
                default:
                    go = new GameObject(hakoEntity.Name);
                    go.SetParent(parent);
                    break;
            }
            HakoEntityView hakoEntityView = hakoEntity.EntityType switch
            {
                HakoniwaEntityType.Player => go.AddComponent<HakoPlayerView>(),
                HakoniwaEntityType.Npc => go.AddComponent<HakoNpcView>(),
                HakoniwaEntityType.Monster => go.AddComponent<HakoMonsterView>(),
                HakoniwaEntityType.Gear => go.AddComponent<HakoGearView>(),
                _ => go.AddComponent<HakoEntityEmptyView>()  // `_` 表示默认情况
            };
            hakoEntityView.Init(hakoEntity);
            return hakoEntityView;
        }

        /// <summary>
        /// 回收箱庭角色View
        /// </summary>
        /// <param name="hakoEntityView"></param>
        public static void Despawn(HakoEntityView hakoEntityView)
        {
            if (hakoEntityView == null)
            {
                return;
            }
            EntitySkinConfigData entitySkin = hakoEntityView.HakoEntity.EntitySkinConfigData;
            HakoniwaEntityType entityType = hakoEntityView.HakoEntity.EntityType;
            GameObject go = hakoEntityView.gameObject;
            hakoEntityView.UnInit();
            GameObject.DestroyImmediate(hakoEntityView);
            switch (entityType)
            {
                case HakoniwaEntityType.Player:
                case HakoniwaEntityType.Npc:
                case HakoniwaEntityType.Monster:
                    ResourceHandleManager.instance.DespawnGameObject(go);
                    break;
                case HakoniwaEntityType.Gear:
                    if (entitySkin != null)
                    {
                        ResourceHandleManager.instance.DespawnGameObject(go);
                    }
                    else
                    {
                        GameObject.DestroyImmediate(go);
                    }
                    break;
                default:
                    GameObject.DestroyImmediate(go);
                    break;
            }
        }
    }


    public class HakoEntityViewLoader : HakoniwaComponent<IHakoEntityHandler>
    {
        private readonly List<CommandInfo> m_commands = new List<CommandInfo>();
        private Boolean m_isResourceLoading;


        protected void Excute()
        {
            if (m_isResourceLoading)
            {
                return;
            }
            if (m_commands.Count > 0)
            {
                CommandInfo command = m_commands[0];
                m_commands.RemoveAt(0);
                switch (command.CommandType)
                {
                    case LoadCommandType.Add:
                        m_isResourceLoading = true;
                        HakoEntityViewLoaderContext context = ClassPoolManager.instance.Fetch<HakoEntityViewLoaderContext>();
                        context.CollectionResourcePath(command.HakoEntity);
                        ResourceHandleManager.instance.StartLoad(context, true, (LoaderProviderBase provider) =>
                        {
                            m_isResourceLoading = false;
                            AddHakoEntityView(command.HakoEntity);
                            Excute();
                        });
                        break;
                    case LoadCommandType.Remove:
                        RemoveHakoEntityView(command.EntityUid);
                        ResourceHandleManager.instance.ReleaseEntity(command.EntityUid);
                        Excute();
                        break;
                }
            }
        }


        protected void AddHakoEntityView(HakoniwaEntityObject hakoEntity)
        {
            HakoEntityView hakoEntityView = HakoEntityViewHelper.Spawn(hakoEntity, Owner.Root);
            Owner.AddHakoEntityView(hakoEntityView);
        }
        protected void RemoveHakoEntityView(Int32 entityUid)
        {
            HakoEntityView hakoEntityView = Owner.GetHakoEntityView(entityUid);
            Owner.RemoveHakoEntityView(entityUid);
            HakoEntityViewHelper.Despawn(hakoEntityView);
        }



        public void HakoEntityAdd(HakoniwaEntityObject hakoEntity)
        {
            m_commands.Add(new CommandInfo(LoadCommandType.Add, hakoEntity));
            Excute();
        }
        public void HakoEntityRemove(Int32 uid)
        {
            m_commands.Add(new CommandInfo(LoadCommandType.Remove, uid));
            Excute();
        }



        internal enum LoadCommandType : byte
        {
            Add,
            Remove,
        }
        internal class CommandInfo
        {
            public CommandInfo(LoadCommandType commandType, HakoniwaEntityObject hakoEntity)
            {
                CommandType = commandType;
                HakoEntity = hakoEntity;
            }
            public CommandInfo(LoadCommandType commandType, Int32 entityUid)
            {
                CommandType = commandType;
                EntityUid = entityUid;
            }
            public LoadCommandType CommandType { get; private set; }
            public HakoniwaEntityObject HakoEntity { get; private set; }
            public Int32 EntityUid { get; private set; }
        }

    }

    internal class HakoEntityViewLoaderContext : LoaderContext
    {
        public void CollectionResourcePath(HakoniwaEntityObject hakoEntity)
        {
            Initialize(hakoEntity.Uid);
            EntitySkinConfigData skinData = ConfigDataManager.instance.GetEntitySkin(hakoEntity.SkinId);
            if (skinData != null)
            {
                AddResourcePath(skinData.PrefabPath, AssetType.Prefab);

                ActorAnimationConfig animConfig = ResourceHandleManager.instance.LoadAssetSync<ActorAnimationConfig>(
                    skinData.AnimationConfigPath, String.Empty, hakoEntity.Uid, AssetType.Asset);
                String defaultAnimPath = animConfig.collection.GetPath(hakoEntity.DefaultAnimation);
                AddResourcePath(defaultAnimPath, AssetType.Animation);
            }
        }
    }
}

