
using Phoenix.Core;
using System;
using Phoenix.GameLogic;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public class HakoTimelineHandler : HakoHandler
    {
        private TimelinePlayer m_timelinePlayer = new TimelinePlayer();
        private String m_timelinePath;
        private GameObject m_timelineGo;
        private Action<String> m_timelinePlayEnd;
        private Boolean m_isPlaying;


        protected override void OnInit()
        {
            m_timelinePlayer.Init(MainCameraManager.instance.MainCamera);

            EventManager.instance.RegisterListener<String, Action<String>>(EventID.HakoSceneTimelinePlay, OnHakoTimelinePlay);
        }


        protected override void OnUnInit()
        {
            m_timelinePlayer.UnInit();
            EventManager.instance.UnRegisterListener<String, Action<String>>(EventID.HakoSceneTimelinePlay, OnHakoTimelinePlay);
        }

        protected override void OnTick(float dt)
        {
            if (m_isPlaying)
            {
                m_timelinePlayer.Tick(dt);
            }
        }


        protected void OnHakoTimelinePlay(String timelinePath, Action<String> onEnd)
        {
            if (!String.IsNullOrEmpty(m_timelinePath) || m_isPlaying)
            {
                m_timelinePlayer.StopTimeline();
            }
            m_timelinePath = timelinePath;
            m_timelinePlayEnd = onEnd;
            LoadAsync(LoadEnd);
        }

        private void LoadAsync(Action<Initializer> loadEnd)
        {
            TimelineInitializer timelineInitializer = new TimelineInitializer(m_timelinePath);
            timelineInitializer.StartAsync(loadEnd);
        }

        private void LoadEnd(Initializer initializer)
        {
            TimelineInitializer timelineInitializer = initializer as TimelineInitializer;
            if (timelineInitializer != null)
            {
                if (timelineInitializer.m_path == m_timelinePath)
                {
                    GameObject timeline = ResourceHandleManager.instance.SpawnGameObject(m_timelinePath);
                    PlayTimelineWithGameObject(timeline);
                }
                else
                {
                    // 新的Timeline文件已经覆盖旧的
                }
            }
        }


        private void PlayTimelineWithGameObject(GameObject go)
        {
            if (go == null)
            {
                FireTimelinePlayEnd();
            }
            else
            {
                m_timelineGo = go;
                m_timelinePlayer.PlayTimeline(go, OnTimelinePlayEnd);
                m_isPlaying = true;
            }
        }

        private void OnTimelinePlayEnd()
        {
            m_isPlaying = false;
            ResourceHandleManager.instance.DespawnGameObject(m_timelineGo);
            m_timelineGo = null;
            FireTimelinePlayEnd();
        }

        private void FireTimelinePlayEnd()
        {
            Action<String> tempAction = m_timelinePlayEnd;
            m_timelinePlayEnd = null;
            if (tempAction != null)
            {
                tempAction.Invoke(m_timelinePath);
            }
            m_timelinePath = null;
        }

    }
}

