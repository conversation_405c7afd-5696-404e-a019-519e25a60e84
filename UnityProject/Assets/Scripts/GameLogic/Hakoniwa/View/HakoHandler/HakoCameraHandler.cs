
using System;
using Cinemachine;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.TouchEvent;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public enum CameraUpdateMode
    {
        Follow,
        Custom
    }

    public interface IHakoniwaCamera
    {
        Quaternion CameraRotation { get; }
    }

    public class HakoCameraHandler : <PERSON><PERSON><PERSON><PERSON><PERSON>, IHakoniwaCamera
    {
        private Boolean m_dirty;
        private GameObject m_mainCameraGameObject;
        private GameObject m_virtualCameraGameObject;

        private CinemachineBrain m_cinemachineBrain;
        private CinemachineVirtualCamera m_virtualCameraComp;
        private CinemachineTrackedDolly m_virtualCameraDollyCartComp;


        private ScreenTouchHandler m_screenTouchHandler;
        private CameraUpdateMode m_cameraModeMode;
        private Action m_onCameraMoveEnd;

        private Vector3 m_position;
        private Vector3 m_followPosition, m_followVelocity;
        private Vector3 m_autoMoveStartPosition, m_autoMoveTargetPosition;
        private Single m_smoothMoveTime, m_smoothMoveDuration;
        private Single m_currentZoomValue, m_downValue = 1, m_upValue = 2;
        public Quaternion CameraRotation { get; private set; }

        #region Public

        public void SetCameraMoveMode(CameraUpdateMode mode)
        {
            ChangeCameraMoveModeInternal(mode);
        }

        public void SetCameraFollowPosition(Vector3 position, bool immediately = false)
        {
            m_followPosition = position;
            if (immediately)
            {
                m_position = m_followPosition;
                ApplyPosition2Camera(m_position);
            }
        }


        public void SetCameraPositionWithSpeed(Vector3 target, float speed, Action onEnd = null)
        {
            Single duration = Vector3.Distance(m_position, target) / speed;
            SetCameraPositionWithTime(target, duration, onEnd);
        }

        public void SetCameraPositionWithTime(Vector3 target, float duration, Action onEnd = null)
        {
            OnCameraSmoothMoveEnd();
            m_onCameraMoveEnd = onEnd;
            m_autoMoveStartPosition = m_position;
            m_autoMoveTargetPosition = target;

            m_smoothMoveTime = 0;
            m_smoothMoveDuration = duration;

            if (m_smoothMoveDuration < 1e-3)
            {
                m_position = m_autoMoveTargetPosition;
                OnCameraSmoothMoveEnd();
            }
        }

        #endregion

        protected override void OnInit()
        {
            HakoSceneHandler hakoSceneHandler = Owner.GetHakoniwaHandler<HakoSceneHandler>();
            GameObject parent = hakoSceneHandler.SceneCameraRoot;

            if (StaticHakoniwa.IsHakoSceneBattle)
            {
                m_mainCameraGameObject = MainCameraManager.instance.Root;
            }
            else
            {
                m_mainCameraGameObject = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.physicsCameraRoot.path);
                MainCameraManager.RegisterMainCamera(m_mainCameraGameObject);
            }
            m_cinemachineBrain = MainCameraManager.instance.MainCamera.GetComponent<CinemachineBrain>();
            m_cinemachineBrain.m_UpdateMethod = CinemachineBrain.UpdateMethod.ManualUpdate;
            m_cinemachineBrain.m_CustomBlends = null;

            m_virtualCameraGameObject = ResourceHandleManager.instance.SpawnGameObject(Owner.HakoniwaBase.sceneConfig.VirtualCameraPath, parent);
            m_virtualCameraComp = m_virtualCameraGameObject.GetComponentInChildren<CinemachineVirtualCamera>();
            m_virtualCameraDollyCartComp = m_virtualCameraComp.GetCinemachineComponent<CinemachineTrackedDolly>();
            //m_virtualCameraGameObject.SetActiveSafely(false);

            if (StaticHakoniwa.IsHakoSceneBattle)
            {
                m_cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition { m_Style = CinemachineBlendDefinition.Style.EaseInOut, m_Time = 0.6f };
            }
            else
            {
                m_cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition { m_Style = CinemachineBlendDefinition.Style.Cut };
            }

            m_screenTouchHandler = new ScreenTouchHandler();
            m_screenTouchHandler.EventOnPinchSpread += EventOnTouchZoom;
            m_screenTouchHandler.EventOnTouchTap += EventOnTouchTap;
            CameraRotation = m_virtualCameraDollyCartComp.m_Path.transform.rotation;
            m_dirty = true;
        }


        protected override void OnUnInit()
        {
            if (StaticHakoniwa.IsHakoSceneBattle == false)
            {
                MainCameraManager.UnRegisterMainCamera();
                ResourceHandleManager.instance.DespawnGameObject(m_mainCameraGameObject);
            }
            ResourceHandleManager.instance.DespawnGameObject(m_virtualCameraGameObject);

            m_screenTouchHandler.EventOnPinchSpread -= EventOnTouchZoom;
            m_screenTouchHandler.EventOnTouchTap -= EventOnTouchTap;
            m_screenTouchHandler = null;
            m_cinemachineBrain = null;
        }


        protected override void OnTick(Single deltaTime)
        {
            m_screenTouchHandler?.Tick(deltaTime);
            switch (m_cameraModeMode)
            {
                case CameraUpdateMode.Follow:
                    TickSmoothFollow(deltaTime, ref m_position, ref m_dirty);
                    break;
                case CameraUpdateMode.Custom:
                    TickSmoothAutoMove(deltaTime, ref m_position, ref m_dirty);
                    break;
            }
            if (m_dirty)
            {
                ApplyPosition2Camera(m_position);
            }
            m_cinemachineBrain?.ManualUpdate();
        }

        private void ApplyPosition2Camera(Vector3 position)
        {
            if (m_inited == false)
            {
                return;
            }
            m_virtualCameraGameObject.transform.position = position;
        }

        private void TickSmoothFollow(Single dt, ref Vector3 position, ref Boolean dirty)
        {
            if (Vector3.SqrMagnitude(position - m_followPosition) > 0)
            {
                Single followFactor = WorldConfigSetting.instance.m_worldCameraFollowFactor;
                position = Vector3.SmoothDamp(position, m_followPosition, ref m_followVelocity, 0.1f, 1000, dt * followFactor);
                dirty = true;
            }
        }

        private void TickSmoothAutoMove(Single dt, ref Vector3 position, ref Boolean dirty)
        {
            if (IsSmoothMoving() == false)
            {
                return;
            }
            m_smoothMoveTime += dt;
            float a = Mathf.Clamp01(m_smoothMoveTime / m_smoothMoveDuration);
            position = Vector3.LerpUnclamped(m_autoMoveStartPosition, m_autoMoveTargetPosition, a);
            dirty = true;
            if (!IsSmoothMoving())
            {
                OnCameraSmoothMoveEnd();
            }
        }

        protected bool IsSmoothMoving()
        {
            return m_smoothMoveDuration > 0 && m_smoothMoveTime < m_smoothMoveDuration;
        }


        private void OnCameraSmoothMoveEnd()
        {
            if (m_onCameraMoveEnd != null)
            {
                Action action = m_onCameraMoveEnd;
                m_onCameraMoveEnd = null;
                action();
            }
        }
        private void ChangeCameraMoveModeInternal(CameraUpdateMode mode)
        {
            if (mode != m_cameraModeMode)
            {
                m_cameraModeMode = mode;
            }
        }


        private void EventOnTouchZoom(float deltaMoveValue)
        {
            if (Mathf.Abs(deltaMoveValue) > 0)
            {
                m_currentZoomValue = Mathf.Clamp(m_currentZoomValue * deltaMoveValue, m_downValue, m_upValue);
                m_virtualCameraDollyCartComp.m_PathPosition = m_currentZoomValue - m_downValue;
            }
        }
        private void EventOnTouchTap(Vector2 position)
        {
            Ray ray = MainCameraManager.instance.MainCamera.ScreenPointToRay(position);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, Mathf.Infinity, UnityLayerUtility.GetLayerMask(ELayerMaskType.TerrainTouch)))
            {
                Vector3 point = hit.point;
                EventManager.instance.Broadcast(EventID.HakoCameraTouchTap, point);
            }
        }
    }
}

