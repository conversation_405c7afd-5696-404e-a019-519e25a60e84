
using System;
using UnityEngine.AI;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public static class NavigationHelper
    {
        private static NavMeshProcessor m_processor = new NavMeshProcessor();

        /// <summary>
        /// 生成完整路径，O(n)复杂度，单次调用耗时0.5~5ms，不建议每帧调用（每秒5次以内）
        /// </summary>
        /// <param name="sourcePos"></param>
        /// <param name="targetPos"></param>
        /// <param name="corners"></param>
        /// <param name="optimize"></param>
        /// <returns></returns>
        public static Boolean CalculatePath(Vector3 sourcePos, Vector3 targetPos, out Vector3[] corners, Boolean optimize = true)
        {
            return m_processor.CalculatePath(sourcePos, targetPos, out corners, optimize);
        }

        /// <summary> 修正目标点位置，O(1)复杂度，单次调用耗时0.01~0.1ms </summary>
        public static Boolean SamplePosition(ref Vector3 position, Single maxDistance = 0.2f)
        {
            Boolean result = false;
            if (NavMesh.SamplePosition(position, out NavMeshHit hit, maxDistance, NavigationDefine.WalkableMask))
            {
                result = true;
                position = hit.position;
            }
            else
            {
                //返回原点或者可以返回一个无效值标识找不到有效点
                Debug.LogWarning("No valid point found on NavMesh within the given distance");
            }
            return result;
        }
    }

}

