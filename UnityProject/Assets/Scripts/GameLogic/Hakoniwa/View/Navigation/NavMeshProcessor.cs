
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

namespace Phoenix.Hakoniwa
{

    public class NavMeshProcessor
    {
        public NavMeshPath navMeshPath = new NavMeshPath();
        public List<Vector3> tempPositions = new List<Vector3>(128);

        public Boolean CalculatePath(Vector3 sourcePosition, Vector3 targetPosition, out Vector3[] corners, Boolean optimize)
        {
            Boolean result = NavMesh.CalculatePath(sourcePosition, targetPosition, NavigationDefine.WalkableMask, navMeshPath);
            if (result && navMeshPath.status != NavMeshPathStatus.PathInvalid)
            {
                if (optimize)
                {
                    SamplePathPoints(navMeshPath.corners, out corners);
                }
                else
                {
                    corners = new Vector3[navMeshPath.corners.Length];
                    for (int i = 0; i < corners.Length; i++)
                    {
                        corners[i] = navMeshPath.corners[i];
                    }
                }
                return true;
            }
            corners = null;
            return false;
        }

        private void SamplePathPoints(Vector3[] originCorners, out Vector3[] optimizeCorners)
        {
            tempPositions.Clear();
            tempPositions.Add(originCorners[0]);
            float accumulatedDistance = 0.0f;
            for (int i = 0; i < originCorners.Length - 1;)
            {
                float segmentDistance = Vector3.Distance(originCorners[i], originCorners[i + 1]);
                if (accumulatedDistance + segmentDistance >= NavigationDefine.NavPathSampleDistance)
                {
                    float t = (NavigationDefine.NavPathSampleDistance - accumulatedDistance) / segmentDistance;
                    Vector3 tempPosition = Vector3.Lerp(originCorners[i], originCorners[i + 1], t);
                    tempPosition = FindNearestPositionOnNavMesh(tempPosition);
                    tempPositions.Add(tempPosition);
                    accumulatedDistance = 0.0f;
                    originCorners[i] = tempPosition; // �������Ϊ�µĲ�����
                }
                else
                {
                    accumulatedDistance += segmentDistance;
                    i++;
                }
            }
            tempPositions.Add(originCorners[originCorners.Length - 1]);
            optimizeCorners = tempPositions.ToArray();
        }

        private Vector3 FindNearestPositionOnNavMesh(Vector3 origin)
        {
            NavMeshHit hit;
            if (NavMesh.FindClosestEdge(origin, out hit, NavMesh.AllAreas) && hit.distance < NavigationDefine.NavPathEdgeBetterDistance)
            {
                origin = hit.position + hit.normal * NavigationDefine.NavPathEdgeBetterDistance;
            }
            if (NavMesh.SamplePosition(origin, out hit, NavigationDefine.NavPathSampleDistance, NavMesh.AllAreas))
            {
                return hit.position;
            }
            else
            {
                Debug.LogWarning("No valid point found on NavMesh within the given distance");
                return origin; // ����ԭ����߿��Է���һ����Чֵ��ʶ�Ҳ�����Ч��
            }
        }
    }

}

