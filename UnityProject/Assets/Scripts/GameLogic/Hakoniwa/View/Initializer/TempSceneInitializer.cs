
using System;
using Phoenix.Core;
using System.Collections;

namespace Phoenix.Hakoniwa
{
    public class TempSceneInitializer : Initializer
    {
        public String m_path;

        public TempSceneInitializer(String path)
        {
            m_path = path;
        }

        protected override IEnumerator OnProcess()
        {
            yield return LoadScene();
        }


        private IEnumerator LoadScene()
        {
            //UnityEngine.Application.backgroundLoadingPriority = UnityEngine.ThreadPriority.High;
            RuntimeSceneLoadOperation operation = RuntimeSceneManager.instance.AddScene(m_path, null, null);
            while (operation != null && !operation.isDone)
            {
                yield return null;
            }
        }
    }
}

