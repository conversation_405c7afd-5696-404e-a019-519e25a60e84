
using System;
using Phoenix.Core;
using Phoenix.GameLogic;
using System.Collections;
using Phoenix.Hakoniwa.Logic;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public class HakoniwaViewInitializer : Initializer
    {
        private HakoniwaBase m_hakoniwaBase;

        public HakoniwaViewInitializer(HakoniwaBase hakoniwaBase)
        {
            m_hakoniwaBase = hakoniwaBase;
        }

        protected override IEnumerator OnProcess()
        {
            yield return LoadScene(m_hakoniwaBase.sceneConfig.ScenePath, true);
            if (StaticHakoniwa.PreLoadCombatScene)
            {
                yield return LoadScene(m_hakoniwaBase.sceneConfig.CombatScenePath, false);
            }
            yield return LoadPrefab();
            yield return LoadEntityResource();
            yield return null;
        }

        private IEnumerator LoadScene(String scenePath, Boolean isMainScene)
        {
            if (!String.IsNullOrEmpty(scenePath))
            {
                RuntimeScene scene = RuntimeSceneManager.instance.GetScene(scenePath);
                if (scene == null)
                {
                    RuntimeSceneLoadOperation operation = null;
                    if (isMainScene)
                    {
                        operation = RuntimeSceneManager.instance.ChangeScene(scenePath, true, null);
                    }
                    else
                    {
                        operation = RuntimeSceneManager.instance.AddScene(scenePath, null, null);
                    }
                    while (operation != null && !operation.isDone)
                    {
                        yield return null;
                    }
                }
            }
            else
            {
                if (isMainScene)
                {
                    Debug.LogError($"ScenePath == null, HakoniwaSceneId: {m_hakoniwaBase.sceneConfig.Id}.");
                }
            }
        }

        private IEnumerator LoadPrefab()
        {
            if (m_hakoniwaBase != null)
            {
                LoaderContext loaderContext = ClassPoolManager.instance.Fetch<LoaderContext>();
                loaderContext.Initialize(0);
                loaderContext.AddResourcePath(CommonPrefabPathSetting.instance.physicsCameraRoot.path, AssetType.Prefab);
                loaderContext.AddResourcePath(CommonPrefabPathSetting.instance.losPrefab.path, AssetType.Prefab);
                loaderContext.AddResourcePath(CommonPrefabPathSetting.instance.worldQuestTrackLine.path, AssetType.Prefab);
                loaderContext.AddResourcePath(m_hakoniwaBase.sceneConfig.VirtualCameraPath, AssetType.Prefab);
                LoaderProvider provider = ResourceHandleManager.instance.StartLoad(loaderContext, true, null);
                while (!provider.IsDone)
                {
                    yield return null;
                }
            }
        }

        private IEnumerator LoadEntityResource()
        {
            if (m_hakoniwaBase != null)
            {
                List<LoaderContext> contextList = new List<LoaderContext>();
                foreach (HakoniwaEntity entity in m_hakoniwaBase.GetAllHakoniwaEntity())
                {
                    HakoEntityViewLoaderContext context = ClassPoolManager.instance.Fetch<HakoEntityViewLoaderContext>();
                    context.CollectionResourcePath(entity);
                    contextList.Add(context);
                }
                if (contextList.Count > 0)
                {
                    LoaderProvider provider = ResourceHandleManager.instance.StartLoad(contextList, true, null);
                    while (!provider.IsDone)
                    {
                        yield return null;
                    }
                }
                yield return null;
            }
        }
    }
}

