
using System;
using NodeCanvas.Framework;
using Phoenix.Core;
using Phoenix.StoryGraphModule;

namespace Phoenix.Hakoniwa
{

    public class StoryGraphPerformance : Performance
    {
        public static Performance Create(StoryGraphOwner owner, String path)
        {
            StoryGraphPerformance performance = new StoryGraphPerformance();
            performance.Init(owner, path);
            return performance;
        }



        private StoryGraphOwner m_owner;
        private String m_storyGraphPath;

        public void Init(StoryGraphOwner owner, String path)
        {
            m_owner = owner;
            m_storyGraphPath = path;
        }

        protected override void OnExecute()
        {
            StoryGraph graph = ResourceHandleManager.instance.GetResource<StoryGraph>(m_storyGraphPath);
            if (graph != null)
            {
                m_owner.StartBehaviour(graph, Graph.UpdateMode.Manual, OnExecuteEnd);
            }
        }

        protected override void OnTick(Single deltaTime)
        {
            if (m_owner != null && m_owner.isRunning)
            {
                m_owner.UpdateBehaviour();
            }
        }


        protected void OnExecuteEnd(Boolean success)
        {
            SetRunningState(false);
        }
    }
}

