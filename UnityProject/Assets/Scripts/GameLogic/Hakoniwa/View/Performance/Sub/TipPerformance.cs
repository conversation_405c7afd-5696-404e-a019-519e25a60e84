
using System;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.Hakoniwa
{

    public class TipPerformance : Performance
    {
        public static Performance Create(String tipText)
        {
            TipPerformance performance = new TipPerformance();
            performance.Init(tipText);
            return performance; 
        }


        
        private String m_tipText;

        public void Init(String tipText)
        {
            m_tipText = tipText;
        }

        protected override void OnExecute()
        {
            TipUI.ShowTip(m_tipText);
            TimerManager.instance.Start(1, () =>
            {
                SetRunningState(false);
            });
        }
    }
}

