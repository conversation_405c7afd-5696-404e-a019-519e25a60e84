

using UnityEngine;

namespace Phoenix.Hakoniwa
{

    public class HakoActorView : HakoEntityView
    {
        protected AnimatorComponent m_animatorComponent;
        protected override void OnCollectComponent()
        {
            base.OnCollectComponent();
            m_viewComponents.Add(m_animatorComponent = CreateComponent<AnimatorComponent>());
            m_viewComponents.Add(CreateComponent<MoveComponent>());
        }


        protected override void OnUnInit()
        {
            m_animatorComponent = null;
        }

    }
}

