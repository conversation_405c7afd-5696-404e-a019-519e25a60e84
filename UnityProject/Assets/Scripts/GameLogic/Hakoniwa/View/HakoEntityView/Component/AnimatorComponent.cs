using System;
using System.Collections.Generic;
using Animancer;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    /// <summary>
    /// 
    /// 动画暂停和恢复，通过设置AnimancerState.Speed的值来实现效果
    /// 
    /// </summary>

    public class AnimatorComponent : HakoEntityViewComponent, IAnimationPathGetter
    {
        private AnimationClipLoader m_clipLoader = new AnimationClipLoader();

        private ActorAnimationConfig m_animationConfig;
        private Dictionary<String, AnimationClip> m_cachedAnimationClips = new Dictionary<String, AnimationClip>();
        private AnimancerComponent m_animancer;


        private String m_defaultAnimationName;
        private String m_animationName;
        private event Action OnPlayEnd;
        private Int32 m_timerUid;




        public void PlayAnimationOnce(String animationName, Single fadeDuration = 0.25f, Action onPlayEnd = null)
        {
            PlayInternal(animationName, fadeDuration, () =>
            {
                onPlayEnd?.Invoke();
                PlayDefaultAnimation();
            });
        }

        public void PlayAnimation(String animationName, Single fadeDuration = 0.25f, Action onPlayEnd = null)
        {
            PlayInternal(animationName, fadeDuration, onPlayEnd);
        }

        public void PlayDefaultAnimation(Single fadeDuration = 0.25f)
        {
            PlayInternal(m_defaultAnimationName, fadeDuration);
        }


        protected override void OnInit()
        {
            if (Owner.GetComponent<Animator>() == null)
            {
                Debug.Log($"HakoActorAnimatorComponent Animator == null, {Owner.EntityUid}");
                m_inited = false;
                return;
            }
            m_animancer = Owner.gameObject.GetComponent<AnimancerComponent>() ?? Owner.gameObject.AddComponent<AnimancerComponent>();
            m_animancer.InitializePlayable();
            m_clipLoader.Init();
            m_clipLoader.SetOwner(this);

            EntitySkinConfigData skinData = ConfigDataManager.instance.GetEntitySkin(Owner.HakoEntity.SkinId);
            m_animationConfig = ResourceHandleManager.instance.GetResource<ActorAnimationConfig>(skinData.AnimationConfigPath);
            m_defaultAnimationName = Owner.HakoEntity.DefaultAnimation;
            PlayInternal(m_defaultAnimationName, 0, null);
        }

        protected override void OnTick(float dt)
        {
            m_clipLoader.Tick(dt);
        }

        protected override void OnUnInit()
        {
            if (m_animancer != null)
            {
                UnityEngine.Object.DestroyImmediate(m_animancer);
                m_animancer = null;
            }
            m_clipLoader.UnInit();
            m_animationConfig = null;
        }


        private void PlayInternal(String animationName, Single fadeDuration, Action onPlayEnd = null)
        {
            if (m_timerUid != 0)
            {
                TimerManager.instance.Stop(m_timerUid);
            }
            m_animationName = animationName;
            OnPlayEnd = onPlayEnd;
            if (m_cachedAnimationClips.TryGetValue(animationName, out AnimationClip clip))
            {
                PlayAnimationInternal(clip, fadeDuration);
            }
            else
            {
                // 启动加载流程
                m_clipLoader.LoadAsync(Owner.EntityUid, animationName, fadeDuration, OnAnimationClipLoadEnd);
            }
        }

        private void OnAnimationClipLoadEnd(AnimationHandle handle)
        {
            if (handle.animationClip != null)
            {
                m_cachedAnimationClips[handle.clipName] = handle.animationClip;
                PlayAnimationInternal(handle.animationClip, handle.fadeDuration);
            }
        }

        private void PlayAnimationInternal(AnimationClip clip, Single fadeDuration = 0.25f)
        {
            if (m_animationName != clip.name)
            {
                return;
            }

            if (m_animancer == null)
            {
                OnAnimationPlayEnd();
                return;
            }

            AnimancerState state = m_animancer.Play(clip, fadeDuration);
            m_timerUid = TimerManager.instance.Start(state.Duration, OnAnimationPlayEnd);
        }

        private void OnAnimationPlayEnd()
        {
            Action temp = OnPlayEnd;
            OnPlayEnd = null;
            temp?.Invoke();
        }



        String IAnimationPathGetter.GetAnimationAssetPath(String clipName)
        {
            String assetPath = null;
            if (m_animationConfig != null && m_animationConfig.collection != null)
            {
                assetPath = m_animationConfig.collection.GetPath(clipName);
            }
            return assetPath;
        }
    }

    public struct AnimationHandle
    {
        public String clipName;
        public AnimationClip animationClip;
        public Single fadeDuration;
    }

    public class AnimationClipLoader : HakoniwaComponent<IAnimationPathGetter>
    {
        private List<LoadCommand> m_commands = new List<LoadCommand>();

        public void LoadAsync(Int32 entityUid, String clipName, Single fadeDuration, Action<AnimationHandle> onEnd)
        {
            LoadCommand command = new LoadCommand()
            {
                entityUid = entityUid,
                clipName = clipName,
                fadeDuration = fadeDuration,
                assetPath = Owner.GetAnimationAssetPath(clipName),
                onEnd = onEnd,
            };
            m_commands.Add(command);
        }



        protected override void OnInit()
        {
        }

        protected override void OnTick(Single dt)
        {
            if (m_commands.Count > 0)
            {
                LoadCommand command = m_commands[0];
                m_commands.RemoveAt(0);
                StartLoad(command);
            }
        }

        protected override void OnUnInit()
        {
            m_commands.Clear();
        }

        private void StartLoad(LoadCommand command)
        {
            LoaderContext context = ClassPoolManager.instance.Fetch<LoaderContext>();
            context.Initialize(command.entityUid);
            context.AddResourcePath(command.assetPath, AssetType.Animation);
            ResourceHandleManager.instance.StartLoad(context, true, (LoaderProviderBase provider) =>
            {
                AnimationClip animationClip = provider.GetResource<AnimationClip>(command.assetPath, command.clipName);
                AnimationHandle handle = new AnimationHandle()
                {
                    clipName = command.clipName,
                    animationClip = animationClip,
                    fadeDuration = command.fadeDuration
                };
                command.onEnd?.Invoke(handle);
            });
        }

        internal struct LoadCommand
        {
            public Int32 entityUid;
            public String clipName;
            public String assetPath;
            public Single fadeDuration;
            public Action<AnimationHandle> onEnd;
        }

    }
}

