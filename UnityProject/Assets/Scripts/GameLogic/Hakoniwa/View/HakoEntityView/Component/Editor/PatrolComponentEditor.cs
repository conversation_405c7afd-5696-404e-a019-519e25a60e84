//using UnityEngine;
//using UnityEditor;

//namespace Phoenix.Hakoniwa.Editor
//{
//    /// <summary>
//    /// PatrolComponent的编辑器扩展
//    /// 提供可视化的巡逻组件调试和配置界面
//    /// </summary>
//    [CustomEditor(typeof(PatrolComponent))]
//    public class PatrolComponentEditor : UnityEditor.Editor
//    {
//        private PatrolComponent m_target;
//        private bool m_showDebugInfo = true;
//        private bool m_showStateInfo = true;
//        private bool m_showAlertInfo = true;
//        private bool m_showPatrolInfo = true;

//        private void OnEnable()
//        {
//            m_target = target as PatrolComponent;
//        }

//        public override void OnInspectorGUI()
//        {
//            if (m_target == null)
//                return;

//            EditorGUILayout.Space();
//            EditorGUILayout.LabelField("Patrol Component Debug", EditorStyles.boldLabel);
//            EditorGUILayout.Space();

//            // 状态信息
//            DrawStateInfo();
//            EditorGUILayout.Space();

//            // 警戒信息
//            DrawAlertInfo();
//            EditorGUILayout.Space();

//            // 巡逻信息
//            DrawPatrolInfo();
//            EditorGUILayout.Space();

//            // 调试控制
//            DrawDebugControls();
//            EditorGUILayout.Space();

//            // 实时数据
//            if (Application.isPlaying)
//            {
//                DrawRuntimeInfo();
//            }

//            // 强制重绘
//            if (Application.isPlaying)
//            {
//                Repaint();
//            }
//        }

//        /// <summary>
//        /// 绘制状态信息
//        /// </summary>
//        private void DrawStateInfo()
//        {
//            m_showStateInfo = EditorGUILayout.Foldout(m_showStateInfo, "状态信息", true);
//            if (!m_showStateInfo)
//                return;

//            using (new EditorGUILayout.VerticalScope("box"))
//            {
//                if (Application.isPlaying)
//                {
//                    // 当前状态
//                    EditorGUILayout.LabelField("当前状态", m_target.CurrentState.ToString());
                    
//                    // 状态颜色指示
//                    Color stateColor = GetStateColor(m_target.CurrentState);
//                    var oldColor = GUI.backgroundColor;
//                    GUI.backgroundColor = stateColor;
//                    EditorGUILayout.LabelField("", GUILayout.Height(5));
//                    GUI.backgroundColor = oldColor;

//                    // 状态描述
//                    string stateDescription = GetStateDescription(m_target.CurrentState);
//                    EditorGUILayout.HelpBox(stateDescription, MessageType.Info);
//                }
//                else
//                {
//                    EditorGUILayout.HelpBox("需要运行时才能显示状态信息", MessageType.Warning);
//                }
//            }
//        }

//        /// <summary>
//        /// 绘制警戒信息
//        /// </summary>
//        private void DrawAlertInfo()
//        {
//            m_showAlertInfo = EditorGUILayout.Foldout(m_showAlertInfo, "警戒信息", true);
//            if (!m_showAlertInfo)
//                return;

//            using (new EditorGUILayout.VerticalScope("box"))
//            {
//                if (Application.isPlaying)
//                {
//                    // 警戒值进度条
//                    float alertLevel = m_target.AlertLevel;
//                    EditorGUILayout.LabelField($"警戒程度: {alertLevel:F1}/100");
                    
//                    Rect progressRect = EditorGUILayout.GetControlRect(false, 20);
//                    EditorGUI.ProgressBar(progressRect, alertLevel / 100f, $"{alertLevel:F1}%");

//                    // 警戒值颜色指示
//                    Color alertColor = GetAlertColor(alertLevel);
//                    var oldColor = GUI.backgroundColor;
//                    GUI.backgroundColor = alertColor;
//                    EditorGUILayout.LabelField("", GUILayout.Height(3));
//                    GUI.backgroundColor = oldColor;

//                    // 警戒衰减速度
//                    EditorGUILayout.LabelField("衰减速度", $"{m_target.AlertDecayRate:F1}/秒");

//                    // 阈值信息
//                    EditorGUILayout.Space();
//                    EditorGUILayout.LabelField("阈值设置:", EditorStyles.miniBoldLabel);
//                    EditorGUILayout.LabelField("  低警戒阈值: 30");
//                    EditorGUILayout.LabelField("  高警戒阈值: 70");
//                }
//                else
//                {
//                    EditorGUILayout.HelpBox("需要运行时才能显示警戒信息", MessageType.Warning);
//                }
//            }
//        }

//        /// <summary>
//        /// 绘制巡逻信息
//        /// </summary>
//        private void DrawPatrolInfo()
//        {
//            m_showPatrolInfo = EditorGUILayout.Foldout(m_showPatrolInfo, "巡逻信息", true);
//            if (!m_showPatrolInfo)
//                return;

//            using (new EditorGUILayout.VerticalScope("box"))
//            {
//                if (Application.isPlaying)
//                {
//                    EditorGUILayout.LabelField("巡逻状态", m_target.IsPatrolling ? "进行中" : "已停止");
//                    EditorGUILayout.LabelField("是否警戒", m_target.IsAlert ? "是" : "否");
//                    EditorGUILayout.LabelField("是否可疑", m_target.IsSuspicious ? "是" : "否");
//                }
//                else
//                {
//                    EditorGUILayout.HelpBox("需要运行时才能显示巡逻信息", MessageType.Warning);
//                }
//            }
//        }

//        /// <summary>
//        /// 绘制调试控制
//        /// </summary>
//        private void DrawDebugControls()
//        {
//            m_showDebugInfo = EditorGUILayout.Foldout(m_showDebugInfo, "调试控制", true);
//            if (!m_showDebugInfo)
//                return;

//            using (new EditorGUILayout.VerticalScope("box"))
//            {
//                if (Application.isPlaying)
//                {
//                    EditorGUILayout.LabelField("状态控制:", EditorStyles.miniBoldLabel);
                    
//                    using (new EditorGUILayout.HorizontalScope())
//                    {
//                        if (GUILayout.Button("强制巡逻"))
//                        {
//                            m_target.ForceChangeState(PatrolStateMachine.Patrol);
//                        }
//                        if (GUILayout.Button("强制可疑"))
//                        {
//                            m_target.ForceChangeState(PatrolStateMachine.Suspicious);
//                        }
//                        if (GUILayout.Button("强制警戒"))
//                        {
//                            m_target.ForceChangeState(PatrolStateMachine.Alert);
//                        }
//                    }

//                    EditorGUILayout.Space();
//                    EditorGUILayout.LabelField("警戒值控制:", EditorStyles.miniBoldLabel);
                    
//                    using (new EditorGUILayout.HorizontalScope())
//                    {
//                        if (GUILayout.Button("增加警戒 +20"))
//                        {
//                            m_target.IncreaseAlert(20f);
//                        }
//                        if (GUILayout.Button("增加警戒 +50"))
//                        {
//                            m_target.IncreaseAlert(50f);
//                        }
//                        if (GUILayout.Button("清零警戒"))
//                        {
//                            m_target.SetAlertLevel(0f);
//                        }
//                    }

//                    EditorGUILayout.Space();
//                    EditorGUILayout.LabelField("巡逻控制:", EditorStyles.miniBoldLabel);
                    
//                    using (new EditorGUILayout.HorizontalScope())
//                    {
//                        if (GUILayout.Button("停止巡逻"))
//                        {
//                            m_target.StopPatrol();
//                        }
//                        if (GUILayout.Button("恢复巡逻"))
//                        {
//                            m_target.ResumePatrol();
//                        }
//                    }
//                }
//                else
//                {
//                    EditorGUILayout.HelpBox("需要运行时才能使用调试控制", MessageType.Info);
//                }
//            }
//        }

//        /// <summary>
//        /// 绘制运行时信息
//        /// </summary>
//        private void DrawRuntimeInfo()
//        {
//            using (new EditorGUILayout.VerticalScope("box"))
//            {
//                EditorGUILayout.LabelField("实时数据", EditorStyles.boldLabel);
                
//                EditorGUILayout.LabelField("组件状态", m_target.enabled ? "启用" : "禁用");
//                EditorGUILayout.LabelField("Owner", m_target.Owner != null ? m_target.Owner.name : "null");
                
//                if (m_target.Owner != null)
//                {
//                    Vector3 position = m_target.Owner.transform.position;
//                    EditorGUILayout.LabelField("当前位置", $"({position.x:F2}, {position.y:F2}, {position.z:F2})");
//                }
//            }
//        }

//        /// <summary>
//        /// 获取状态对应的颜色
//        /// </summary>
//        /// <param name="state">状态</param>
//        /// <returns>颜色</returns>
//        private Color GetStateColor(PatrolStateMachine state)
//        {
//            return state switch
//            {
//                PatrolStateMachine.Patrol => Color.green,
//                PatrolStateMachine.Suspicious => Color.yellow,
//                PatrolStateMachine.Alert => Color.red,
//                _ => Color.gray
//            };
//        }

//        /// <summary>
//        /// 获取警戒值对应的颜色
//        /// </summary>
//        /// <param name="alertLevel">警戒值</param>
//        /// <returns>颜色</returns>
//        private Color GetAlertColor(float alertLevel)
//        {
//            if (alertLevel >= 70f)
//                return Color.red;
//            else if (alertLevel >= 30f)
//                return Color.yellow;
//            else
//                return Color.green;
//        }

//        /// <summary>
//        /// 获取状态描述
//        /// </summary>
//        /// <param name="state">状态</param>
//        /// <returns>描述</returns>
//        private string GetStateDescription(PatrolStateMachine state)
//        {
//            return state switch
//            {
//                PatrolStateMachine.Patrol => "正在按照设定路径进行巡逻，警戒值较低",
//                PatrolStateMachine.Suspicious => "发现可疑情况，正在调查中，警戒值中等",
//                PatrolStateMachine.Alert => "高度警戒状态，正在积极搜索目标",
//                PatrolStateMachine.None => "未激活状态",
//                _ => "未知状态"
//            };
//        }

//        /// <summary>
//        /// 在Scene视图中绘制调试信息
//        /// </summary>
//        private void OnSceneGUI()
//        {
//            if (m_target == null || !Application.isPlaying)
//                return;

//            // 在Scene视图中显示状态信息
//            Vector3 position = m_target.Owner.transform.position + Vector3.up * 3f;
            
//            Handles.BeginGUI();
            
//            Vector2 screenPos = HandleUtility.WorldToGUIPoint(position);
            
//            GUIStyle style = new GUIStyle(GUI.skin.box);
//            style.normal.textColor = Color.white;
//            style.fontSize = 12;
            
//            string info = $"状态: {m_target.CurrentState}\n警戒: {m_target.AlertLevel:F1}";
//            Vector2 size = style.CalcSize(new GUIContent(info));
            
//            GUI.Box(new Rect(screenPos.x - size.x / 2, screenPos.y - size.y / 2, size.x, size.y), info, style);
            
//            Handles.EndGUI();
//        }
//    }
//}
