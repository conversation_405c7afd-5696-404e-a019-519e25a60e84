using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;
using Phoenix.ConfigData;

namespace Phoenix.Hakoniwa
{
    /// <summary>
    /// 巡逻状态枚举
    /// </summary>
    public enum PatrolStateMachine
    {
        None,           // 无状态
        Patrol,         // 巡逻状态
        Suspicious,     // 低警戒状态
        Alert           // 高警戒状态
    }

    /// <summary>
    /// 巡逻组件
    /// 实现怪物的巡逻状态机，支持巡逻、低警戒、高警戒状态
    /// </summary>
    public class PatrolComponent : HakoEntityViewComponent
    {
        #region Private Fields

        /// <summary>
        /// 移动组件
        /// </summary>
        private MoveComponent m_moveComponent;

        /// <summary>
        /// 动画组件
        /// </summary>
        private AnimatorComponent m_animatorComponent;

        /// <summary>
        /// 巡逻路径点列表
        /// </summary>
        private List<Vector3> m_patrolPoints = new List<Vector3>();

        /// <summary>
        /// 当前巡逻点索引
        /// </summary>
        private int m_currentPatrolIndex = 0;

        /// <summary>
        /// 巡逻方向（1为正向，-1为反向）
        /// </summary>
        private int m_patrolDirection = 1;

        /// <summary>
        /// 当前状态机状态
        /// </summary>
        private PatrolStateMachine m_currentState = PatrolStateMachine.None;

        /// <summary>
        /// 上一个状态
        /// </summary>
        private PatrolStateMachine m_previousState = PatrolStateMachine.None;

        /// <summary>
        /// 警戒程度 (0-100)
        /// </summary>
        private float m_alertLevel = 0f;

        /// <summary>
        /// 警戒值自然下降速度 (每秒下降的数值)
        /// </summary>
        private float m_alertDecayRate = 10f;

        /// <summary>
        /// 低警戒阈值
        /// </summary>
        private float m_suspiciousThreshold = 30f;

        /// <summary>
        /// 高警戒阈值
        /// </summary>
        private float m_alertThreshold = 70f;

        /// <summary>
        /// 巡逻移动速度
        /// </summary>
        private float m_patrolSpeed = 2f;

        /// <summary>
        /// 警戒移动速度
        /// </summary>
        private float m_alertSpeed = 4f;

        /// <summary>
        /// 在巡逻点的等待时间（毫秒）
        /// </summary>
        private int m_waitTimeAtPoint = 2000;

        /// <summary>
        /// 是否正在等待
        /// </summary>
        private bool m_isWaiting = false;

        /// <summary>
        /// 是否正在移动
        /// </summary>
        private bool m_isMoving = false;

        /// <summary>
        /// 可疑目标位置
        /// </summary>
        private Vector3 m_suspiciousPosition = Vector3.zero;

        /// <summary>
        /// 最后已知的玩家位置
        /// </summary>
        private Vector3 m_lastKnownPlayerPosition = Vector3.zero;

        /// <summary>
        /// 状态切换时间
        /// </summary>
        private float m_stateChangeTime = 0f;

        /// <summary>
        /// 是否启用巡逻
        /// </summary>
        private bool m_patrolEnabled = false;

        #endregion

        #region Properties

        /// <summary>
        /// 当前状态
        /// </summary>
        public PatrolStateMachine CurrentState => m_currentState;

        /// <summary>
        /// 当前警戒程度
        /// </summary>
        public float AlertLevel => m_alertLevel;

        /// <summary>
        /// 警戒值自然下降速度
        /// </summary>
        public float AlertDecayRate
        {
            get => m_alertDecayRate;
            set => m_alertDecayRate = Mathf.Max(0f, value);
        }

        /// <summary>
        /// 是否正在巡逻
        /// </summary>
        public bool IsPatrolling => m_currentState == PatrolStateMachine.Patrol;

        /// <summary>
        /// 是否处于警戒状态
        /// </summary>
        public bool IsAlert => m_currentState == PatrolStateMachine.Alert;

        /// <summary>
        /// 是否处于可疑状态
        /// </summary>
        public bool IsSuspicious => m_currentState == PatrolStateMachine.Suspicious;

        #endregion

        #region HakoEntityViewComponent Override

        protected override void OnInit()
        {
            // 获取必要的组件
            m_moveComponent = Owner.GetHakoComponent<MoveComponent>();
            m_animatorComponent = Owner.GetHakoComponent<AnimatorComponent>();

            // 初始化状态
            m_currentState = PatrolStateMachine.None;
            m_alertLevel = 0f;
            m_stateChangeTime = Time.time;

            Debug.Log($"[PatrolComponent] 初始化完成 - {Owner.name}");
        }

        protected override void OnUnInit()
        {
            // 停止所有移动
            if (m_moveComponent != null)
            {
                m_moveComponent.StopMove();
            }

            // 清理数据
            m_patrolPoints.Clear();
            m_moveComponent = null;
            m_animatorComponent = null;
            m_patrolEnabled = false;

            Debug.Log($"[PatrolComponent] 清理完成 - {Owner.name}");
        }

        protected override void OnTick(float deltaTime)
        {
            if (!m_patrolEnabled)
                return;

            // 更新警戒值衰减
            UpdateAlertDecay(deltaTime);

            // 更新状态机
            UpdateStateMachine(deltaTime);

            // 执行当前状态的行为
            ExecuteCurrentState(deltaTime);
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 设置巡逻路径
        /// </summary>
        /// <param name="patrolPoints">巡逻点列表</param>
        public void SetPatrolPath(IList<Vector3> patrolPoints)
        {
            if (patrolPoints == null || patrolPoints.Count == 0)
            {
                Debug.LogWarning($"[PatrolComponent] 巡逻路径为空 - {Owner.name}");
                return;
            }

            m_patrolPoints.Clear();
            
            // 添加当前位置作为起始点
            m_patrolPoints.Add(Owner.transform.position);
            
            // 添加配置的巡逻点
            foreach (var point in patrolPoints)
            {
                m_patrolPoints.Add(point);
            }

            m_currentPatrolIndex = 0;
            m_patrolDirection = 1;
            m_patrolEnabled = true;

            // 开始巡逻
            ChangeState(PatrolStateMachine.Patrol);

            Debug.Log($"[PatrolComponent] 设置巡逻路径完成，共{m_patrolPoints.Count}个点 - {Owner.name}");
        }

        /// <summary>
        /// 设置巡逻路径（从配置数据）
        /// </summary>
        /// <param name="configPaths">配置路径列表</param>
        public void SetPatrolPath(IList<SceneLocationConfigData> configPaths)
        {
            if (configPaths == null || configPaths.Count == 0)
            {
                Debug.LogWarning($"[PatrolComponent] 配置巡逻路径为空 - {Owner.name}");
                return;
            }

            var points = new List<Vector3>();
            foreach (var config in configPaths)
            {
                points.Add(new Vector3(config.X, config.Y, config.Z));
            }

            SetPatrolPath(points);
        }

        /// <summary>
        /// 增加警戒值
        /// </summary>
        /// <param name="amount">增加的数值</param>
        /// <param name="suspiciousPosition">可疑位置</param>
        public void IncreaseAlert(float amount, Vector3 suspiciousPosition = default)
        {
            m_alertLevel = Mathf.Clamp(m_alertLevel + amount, 0f, 100f);
            
            if (suspiciousPosition != default)
            {
                m_suspiciousPosition = suspiciousPosition;
                m_lastKnownPlayerPosition = suspiciousPosition;
            }

            Debug.Log($"[PatrolComponent] 警戒值增加 {amount}，当前: {m_alertLevel} - {Owner.name}");
        }

        /// <summary>
        /// 设置警戒值
        /// </summary>
        /// <param name="level">警戒值 (0-100)</param>
        public void SetAlertLevel(float level)
        {
            m_alertLevel = Mathf.Clamp(level, 0f, 100f);
            Debug.Log($"[PatrolComponent] 设置警戒值: {m_alertLevel} - {Owner.name}");
        }

        /// <summary>
        /// 设置警戒参数
        /// </summary>
        /// <param name="decayRate">警戒值衰减速度</param>
        /// <param name="suspiciousThreshold">低警戒阈值</param>
        /// <param name="alertThreshold">高警戒阈值</param>
        public void SetAlertParameters(float decayRate, float suspiciousThreshold = 30f, float alertThreshold = 70f)
        {
            m_alertDecayRate = Mathf.Max(0f, decayRate);
            m_suspiciousThreshold = Mathf.Clamp(suspiciousThreshold, 0f, 100f);
            m_alertThreshold = Mathf.Clamp(alertThreshold, suspiciousThreshold, 100f);

            Debug.Log($"[PatrolComponent] 设置警戒参数 - 衰减:{m_alertDecayRate}, 低警戒:{m_suspiciousThreshold}, 高警戒:{m_alertThreshold} - {Owner.name}");
        }

        /// <summary>
        /// 设置移动速度
        /// </summary>
        /// <param name="patrolSpeed">巡逻速度</param>
        /// <param name="alertSpeed">警戒速度</param>
        public void SetMovementSpeeds(float patrolSpeed, float alertSpeed)
        {
            m_patrolSpeed = Mathf.Max(0.1f, patrolSpeed);
            m_alertSpeed = Mathf.Max(0.1f, alertSpeed);

            Debug.Log($"[PatrolComponent] 设置移动速度 - 巡逻:{m_patrolSpeed}, 警戒:{m_alertSpeed} - {Owner.name}");
        }

        /// <summary>
        /// 强制切换到指定状态
        /// </summary>
        /// <param name="newState">新状态</param>
        public void ForceChangeState(PatrolStateMachine newState)
        {
            ChangeState(newState);
        }

        /// <summary>
        /// 停止巡逻
        /// </summary>
        public void StopPatrol()
        {
            m_patrolEnabled = false;
            
            if (m_moveComponent != null)
            {
                m_moveComponent.StopMove();
            }

            ChangeState(PatrolStateMachine.None);
            Debug.Log($"[PatrolComponent] 停止巡逻 - {Owner.name}");
        }

        /// <summary>
        /// 恢复巡逻
        /// </summary>
        public void ResumePatrol()
        {
            if (m_patrolPoints.Count > 0)
            {
                m_patrolEnabled = true;
                ChangeState(PatrolStateMachine.Patrol);
                Debug.Log($"[PatrolComponent] 恢复巡逻 - {Owner.name}");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 更新警戒值衰减
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void UpdateAlertDecay(float deltaTime)
        {
            if (m_alertLevel > 0f)
            {
                m_alertLevel = Mathf.Max(0f, m_alertLevel - m_alertDecayRate * deltaTime);
            }
        }

        /// <summary>
        /// 更新状态机
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void UpdateStateMachine(float deltaTime)
        {
            PatrolStateMachine newState = m_currentState;

            // 根据警戒值决定状态
            if (m_alertLevel >= m_alertThreshold)
            {
                newState = PatrolStateMachine.Alert;
            }
            else if (m_alertLevel >= m_suspiciousThreshold)
            {
                newState = PatrolStateMachine.Suspicious;
            }
            else
            {
                newState = PatrolStateMachine.Patrol;
            }

            // 如果状态发生变化，切换状态
            if (newState != m_currentState)
            {
                ChangeState(newState);
            }
        }

        /// <summary>
        /// 切换状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void ChangeState(PatrolStateMachine newState)
        {
            if (m_currentState == newState)
                return;

            // 退出当前状态
            ExitState(m_currentState);

            // 记录状态变化
            m_previousState = m_currentState;
            m_currentState = newState;
            m_stateChangeTime = Time.time;

            // 进入新状态
            EnterState(newState);

            Debug.Log($"[PatrolComponent] 状态切换: {m_previousState} -> {m_currentState} (警戒值: {m_alertLevel:F1}) - {Owner.name}");
        }

        /// <summary>
        /// 进入状态
        /// </summary>
        /// <param name="state">状态</param>
        private void EnterState(PatrolStateMachine state)
        {
            switch (state)
            {
                case PatrolStateMachine.Patrol:
                    EnterPatrolState();
                    break;
                case PatrolStateMachine.Suspicious:
                    EnterSuspiciousState();
                    break;
                case PatrolStateMachine.Alert:
                    EnterAlertState();
                    break;
            }
        }

        /// <summary>
        /// 退出状态
        /// </summary>
        /// <param name="state">状态</param>
        private void ExitState(PatrolStateMachine state)
        {
            // 停止当前移动
            if (m_moveComponent != null)
            {
                m_moveComponent.StopMove();
            }

            m_isWaiting = false;
            m_isMoving = false;
        }

        /// <summary>
        /// 执行当前状态的行为
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void ExecuteCurrentState(float deltaTime)
        {
            switch (m_currentState)
            {
                case PatrolStateMachine.Patrol:
                    ExecutePatrolState(deltaTime);
                    break;
                case PatrolStateMachine.Suspicious:
                    ExecuteSuspiciousState(deltaTime);
                    break;
                case PatrolStateMachine.Alert:
                    ExecuteAlertState(deltaTime);
                    break;
            }
        }

        #endregion

        #region State Implementations

        /// <summary>
        /// 进入巡逻状态
        /// </summary>
        private void EnterPatrolState()
        {
            // 播放巡逻动画
            if (m_animatorComponent != null)
            {
                m_animatorComponent.PlayAnimation("Walk");
            }

            // 开始移动到下一个巡逻点
            if (m_patrolPoints.Count > 1)
            {
                MoveToNextPatrolPoint();
            }
        }

        /// <summary>
        /// 执行巡逻状态
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void ExecutePatrolState(float deltaTime)
        {
            // 如果没有在移动且没有在等待，开始移动到下一个点
            if (!m_isMoving && !m_isWaiting && m_patrolPoints.Count > 1)
            {
                MoveToNextPatrolPoint();
            }
        }

        /// <summary>
        /// 进入可疑状态
        /// </summary>
        private void EnterSuspiciousState()
        {
            // 播放警戒动画
            if (m_animatorComponent != null)
            {
                m_animatorComponent.PlayAnimation("Alert");
            }

            // 如果有可疑位置，移动过去查看
            if (m_suspiciousPosition != Vector3.zero)
            {
                MoveToPosition(m_suspiciousPosition, m_alertSpeed, OnSuspiciousInvestigationComplete);
            }
        }

        /// <summary>
        /// 执行可疑状态
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void ExecuteSuspiciousState(float deltaTime)
        {
            // 在可疑状态下，如果没有在移动，就在原地警戒
            if (!m_isMoving)
            {
                // 可以添加左右张望的行为
                LookAround(deltaTime);
            }
        }

        /// <summary>
        /// 进入警戒状态
        /// </summary>
        private void EnterAlertState()
        {
            // 播放高警戒动画
            if (m_animatorComponent != null)
            {
                m_animatorComponent.PlayAnimation("Run");
            }

            // 快速移动到最后已知的玩家位置
            if (m_lastKnownPlayerPosition != Vector3.zero)
            {
                MoveToPosition(m_lastKnownPlayerPosition, m_alertSpeed, OnAlertInvestigationComplete);
            }
        }

        /// <summary>
        /// 执行警戒状态
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void ExecuteAlertState(float deltaTime)
        {
            // 在警戒状态下，积极搜索目标
            if (!m_isMoving)
            {
                // 可以添加主动搜索的行为
                ActiveSearch(deltaTime);
            }
        }

        #endregion

        #region Movement Methods

        /// <summary>
        /// 移动到下一个巡逻点
        /// </summary>
        private void MoveToNextPatrolPoint()
        {
            if (m_patrolPoints.Count <= 1)
                return;

            // 计算下一个巡逻点索引
            int nextIndex = m_currentPatrolIndex + m_patrolDirection;

            // 检查是否需要反转方向
            if (nextIndex >= m_patrolPoints.Count)
            {
                m_patrolDirection = -1;
                nextIndex = m_patrolPoints.Count - 2;
            }
            else if (nextIndex < 0)
            {
                m_patrolDirection = 1;
                nextIndex = 1;
            }

            m_currentPatrolIndex = nextIndex;
            Vector3 targetPoint = m_patrolPoints[m_currentPatrolIndex];

            MoveToPosition(targetPoint, m_patrolSpeed, OnPatrolPointReached);
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="position">目标位置</param>
        /// <param name="speed">移动速度</param>
        /// <param name="onComplete">完成回调</param>
        private void MoveToPosition(Vector3 position, float speed, Action<bool> onComplete)
        {
            if (m_moveComponent == null)
                return;

            m_isMoving = true;
            m_moveComponent.MoveTo(position, speed, false, onComplete);
        }

        /// <summary>
        /// 巡逻点到达回调
        /// </summary>
        /// <param name="completed">是否成功到达</param>
        private void OnPatrolPointReached(bool completed)
        {
            m_isMoving = false;

            if (completed && m_currentState == PatrolStateMachine.Patrol)
            {
                // 在端点停留一段时间
                if (m_currentPatrolIndex == 0 || m_currentPatrolIndex == m_patrolPoints.Count - 1)
                {
                    WaitAtPatrolPoint().Forget();
                }
                else
                {
                    // 继续移动到下一个点
                    MoveToNextPatrolPoint();
                }
            }
        }

        /// <summary>
        /// 可疑调查完成回调
        /// </summary>
        /// <param name="completed">是否成功到达</param>
        private void OnSuspiciousInvestigationComplete(bool completed)
        {
            m_isMoving = false;
            
            if (completed)
            {
                // 在可疑位置搜索一段时间
                InvestigateArea().Forget();
            }
        }

        /// <summary>
        /// 警戒调查完成回调
        /// </summary>
        /// <param name="completed">是否成功到达</param>
        private void OnAlertInvestigationComplete(bool completed)
        {
            m_isMoving = false;
            
            if (completed)
            {
                // 在警戒位置积极搜索
                ActiveSearchArea().Forget();
            }
        }

        /// <summary>
        /// 在巡逻点等待
        /// </summary>
        private async UniTaskVoid WaitAtPatrolPoint()
        {
            m_isWaiting = true;
            
            // 播放待机动画
            if (m_animatorComponent != null)
            {
                m_animatorComponent.PlayAnimation("Idle");
            }

            await UniTask.Delay(m_waitTimeAtPoint);

            m_isWaiting = false;

            // 如果仍在巡逻状态，继续移动
            if (m_currentState == PatrolStateMachine.Patrol)
            {
                MoveToNextPatrolPoint();
            }
        }

        /// <summary>
        /// 调查区域
        /// </summary>
        private async UniTaskVoid InvestigateArea()
        {
            // 在可疑位置停留并搜索
            await UniTask.Delay(3000);
            
            // 清除可疑位置
            m_suspiciousPosition = Vector3.zero;
        }

        /// <summary>
        /// 积极搜索区域
        /// </summary>
        private async UniTaskVoid ActiveSearchArea()
        {
            // 在警戒位置积极搜索
            await UniTask.Delay(5000);
            
            // 清除最后已知位置
            m_lastKnownPlayerPosition = Vector3.zero;
        }

        /// <summary>
        /// 左右张望
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void LookAround(float deltaTime)
        {
            // 可以实现左右转头的逻辑
            float lookTime = Time.time - m_stateChangeTime;
            float lookAngle = Mathf.Sin(lookTime * 2f) * 30f; // 左右30度摆动
            
            Vector3 lookDirection = Quaternion.Euler(0, lookAngle, 0) * Owner.transform.forward;
            Owner.transform.rotation = Quaternion.LookRotation(lookDirection);
        }

        /// <summary>
        /// 主动搜索
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        private void ActiveSearch(float deltaTime)
        {
            // 可以实现更积极的搜索行为
            float searchTime = Time.time - m_stateChangeTime;
            float searchAngle = searchTime * 90f; // 快速旋转搜索
            
            Vector3 searchDirection = Quaternion.Euler(0, searchAngle, 0) * Vector3.forward;
            Owner.transform.rotation = Quaternion.LookRotation(searchDirection);
        }

        #endregion

        #region Debug Methods

        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (!Application.isPlaying || m_patrolPoints == null)
                return;

            // 绘制巡逻路径
            Gizmos.color = Color.blue;
            for (int i = 0; i < m_patrolPoints.Count - 1; i++)
            {
                Gizmos.DrawLine(m_patrolPoints[i], m_patrolPoints[i + 1]);
            }

            // 绘制巡逻点
            for (int i = 0; i < m_patrolPoints.Count; i++)
            {
                Gizmos.color = (i == m_currentPatrolIndex) ? Color.red : Color.yellow;
                Gizmos.DrawWireSphere(m_patrolPoints[i], 0.5f);
            }

            // 绘制可疑位置
            if (m_suspiciousPosition != Vector3.zero)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(m_suspiciousPosition, 1f);
            }

            // 绘制最后已知玩家位置
            if (m_lastKnownPlayerPosition != Vector3.zero)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(m_lastKnownPlayerPosition, 1.5f);
            }
        }

        #endregion
    }
}
