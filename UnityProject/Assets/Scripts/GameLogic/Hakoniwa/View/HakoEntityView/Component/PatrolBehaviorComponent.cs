
using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Phoenix.ConfigData;

namespace Phoenix.Hakoniwa
{

    public enum PatrolState
    {
        None,
        Standing,
        Moving,
        Alert,
    }


    /// <summary> 巡逻组件 </summary>
    public class PatrolBehaviorComponent : HakoEntityViewComponent
    {
        protected MoveComponent m_moveComponent;
        protected List<HakoLocation> m_points = new List<HakoLocation>();
        protected Int32 mIndex, mStep;
        protected HakoLocation m_currentLocation;
        protected PatrolState m_state;



        public void SetPatrolPath(IList<SceneLocationConfigData> paths)
        {
            if (paths == null || paths.Count == 0)
            {
                return;
            }

            m_points.Clear();
            m_points.Add(Owner.HakoEntity.Position);
            foreach (SceneLocationConfigData location in paths)
            {
                m_points.Add(location);
            }
            m_state = PatrolState.Standing;
            mIndex = 0;
            mStep = 1;
            StartPatrolMoving(mStep);
        }
        
        public void AlertBegin()
        {
            if (m_state == PatrolState.None)
            {
                return;
            }

            m_moveComponent.StopMove();
            m_state = PatrolState.Alert;
        }

        public void AlertEnd()
        {
            if (m_state == PatrolState.None)
            {
                return;
            }

            m_state = PatrolState.Standing;
            StartPatrolMoving(0);
        }


        protected override void OnInit()
        {
            m_moveComponent = Owner.GetHakoComponent<MoveComponent>();
            mIndex = 0;
            mStep = 1;
            m_state = PatrolState.None;
        }

        protected override void OnUnInit()
        {
            mIndex = 0;
            mStep = 1;
            m_points.Clear();
            m_moveComponent = null;
        }

        private void CaculaterIndex(Int32 step)
        {
            if (step != 0)
            {
                Int32 index = mIndex + step;

                if (index < 0 || index >= m_points.Count) mStep = step *= -1;

                mIndex = index = mIndex + step;
            }
        }

        private void StartPatrolMoving(Int32 step)
        {
            UnityEngine.Debug.Log($"StartPatrolMoving --> {Owner.name}, step = {step}");
            CaculaterIndex(step);
            m_state = PatrolState.Moving;
            m_moveComponent.MoveTo(m_points[mIndex], StaticHakoniwaData.EntityWalkSpeed, false, MovingPatrolEnd);
        }

        private void MovingPatrolEnd(Boolean completed)
        {
            if (completed && m_state == PatrolState.Moving)
            {
                if (mIndex == 0 ||  mIndex == m_points.Count - 1)
                {
                    // 到达端点位置，触发延迟
                    StandingPatrol(2000).Forget();
                }
                else
                {
                    StartPatrolMoving(mStep);
                }
            }
        }

        private async UniTaskVoid StandingPatrol(Int32 millisecondsDelay)
        {
            m_state = PatrolState.Standing;
            await UniTask.Delay(millisecondsDelay);
            if (m_inited && m_state == PatrolState.Standing)
            {
                StartPatrolMoving(mStep);
            }
        }

    }
}

