using System;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public class MoveComponent : HakoEntityViewComponent
    {
        protected AnimatorComponent m_animatorComponent;

        protected Single m_speed;
        protected Vector3 m_position;
        protected Quaternion m_rotation;
        protected Boolean m_isMoving;

        private MovePlusTask m_movePlusTask = new MovePlusTask();

        public void Move(Vector3[] paths, Single speed, Action<Boolean> moveEnd = null)
        {
            m_movePlusTask.Stop();
            m_speed = speed;
            m_movePlusTask.Start(paths, m_speed, moveEnd);
        }
        
        public void MoveTo(Vector3 destination, Single speed, Boolean immediately = false, Action<Boolean> moveEnd = null)
        {
            HakoSceneHandler.RepairPosition(ref destination);
            if (NavigationHelper.SamplePosition(ref destination) == false)
            {
                Debug.LogError($"{Owner.HakoEntity.Name}({Owner.EntityUid})位置异常(OutOfNavRange) --> {destination}");
                return;
            }
            if (immediately)
            {
                m_movePlusTask.Stop();
                SetPositionInternal(destination);
                moveEnd?.Invoke(true);
            }
            else
            {
                m_movePlusTask.Stop();
                m_speed = speed;
                m_movePlusTask.Start(m_position, destination, m_speed, moveEnd);
            }
        }

        public void StopMove()
        {
            m_movePlusTask.Stop();
        }

        public void LookAt(Vector3 lookPos)
        {
            Quaternion quaternion = Quaternion.LookRotation((lookPos - m_position).normalized);
            SetRotationInternal(quaternion);
        }

        
        protected override void OnInit()
        {
            m_isMoving = false;
            m_animatorComponent = Owner.GetHakoComponent<AnimatorComponent>();
            m_position = Owner.HakoEntity.Position;
            HakoSceneHandler.RepairPosition(ref m_position);
            m_rotation = Quaternion.Euler(Owner.HakoEntity.Rotation);
        }

        protected override void OnUnInit()
        {
            m_isMoving = false;
            m_animatorComponent = null;
        }

        protected override void OnTick(float dt)
        {
            if (m_movePlusTask.IsMoving)
            {
                if (m_isMoving == false)
                {
                    m_isMoving = true;
                    if(m_speed > StaticHakoniwaData.EntityAverageMoveSpeed)
                    {
                        m_animatorComponent.PlayAnimation("Move");
                    }
                    else
                    {
                        m_animatorComponent.PlayAnimation("Move");//Walk
                    }
                }
                m_movePlusTask.Tick(dt, ref m_position, ref m_rotation);
                SetPositionInternal(m_position);
                SetRotationInternal(m_rotation);
            }
            else if (m_isMoving)
            {
                m_isMoving = false;
                m_animatorComponent.PlayDefaultAnimation(0.1f);
            }
        }

        private void SetPositionInternal(Vector3 position)
        {
            m_position = position;
            Owner.SetPosition(position);
        }

        private void SetRotationInternal(Quaternion rotation)
        {
            m_rotation = rotation;
            Owner.SetRotation(rotation);
        }

    }
}

