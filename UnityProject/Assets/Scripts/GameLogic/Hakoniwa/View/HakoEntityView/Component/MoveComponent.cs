using System;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public class MoveComponent : HakoEntityViewComponent
    {
        protected static Single MoveSpeed = 0.1f;

        protected AnimatorComponent m_animatorComponent;

        protected Vector3 m_position;
        protected Quaternion m_rotation;
        protected Boolean m_isMoving;

        private MovePlusTask m_movePlusTask = new MovePlusTask();

        public void Move(Vector3[] paths, Action<Boolean> moveEnd = null)
        {
            m_movePlusTask.Stop();
            m_movePlusTask.Start(paths, MoveSpeed, moveEnd);
        }
        
        public void MoveTo(Vector3 destination, Boolean immediately = false, Action<Boolean> moveEnd = null)
        {
            HakoSceneHandler.RepairPosition(ref destination);
            if (NavigationHelper.SamplePosition(ref destination) == false)
            {
                return;
            }
            if (immediately)
            {
                m_movePlusTask.Stop();
                SetPositionInternal(destination);
                moveEnd?.Invoke(true);
            }
            else
            {
                m_movePlusTask.Stop();
                m_movePlusTask.Start(m_position, destination, MoveSpeed, moveEnd);
            }
        }

        public void LookAt(Vector3 lookPos)
        {
            Quaternion quaternion = Quaternion.LookRotation((lookPos - m_position).normalized);
            SetRotationInternal(quaternion);
        }

        
        protected override void OnInit()
        {
            m_isMoving = false;
            m_animatorComponent = Owner.GetHakoComponent<AnimatorComponent>();
        }

        protected override void OnUnInit()
        {
            m_isMoving = false;
            m_animatorComponent = null;
        }

        protected override void OnTick(float dt)
        {
            if (m_movePlusTask.IsMoving)
            {
                if (m_isMoving == false)
                {
                    m_isMoving = true;
                    m_animatorComponent.PlayAnimation("Move");
                }
                m_movePlusTask.Tick(dt, ref m_position, ref m_rotation);
                SetPositionInternal(m_position);
                SetRotationInternal(m_rotation);
            }
            else if (m_isMoving)
            {
                m_isMoving = false;
                m_animatorComponent.PlayDefaultAnimation(0.1f);
            }
        }

        private void SetPositionInternal(Vector3 position)
        {
            m_position = position;
            Owner.SetPosition(position);
        }

        private void SetRotationInternal(Quaternion rotation)
        {
            m_rotation = rotation;
            Owner.SetRotation(rotation);
        }

    }
}

