
using System;
using UnityEngine;

namespace Phoenix.Hakoniwa
{
    public class FollowComponent : HakoEntityViewComponent
    {
        protected AnimatorComponent m_animatorComponent;
        private IHakoMainPlayer m_hakoMainPlayer;
        protected Boolean m_isMoving;


        protected override void OnInit()
        {
            base.OnInit();
            m_animatorComponent = Owner.GetHakoComponent<AnimatorComponent>();
            m_hakoMainPlayer = StaticHakoniwa.hakoniwaView.GetHakoniwaHandler<HakoMainPlayerHandler>();
            SetPositionInternal(m_hakoMainPlayer.MainPlayerPosition);
            SetRotationInternal(m_hakoMainPlayer.MainPlayerRotation);
        }

        protected override void OnTick(float dt)
        {
            if (m_hakoMainPlayer != null)
            {
                if (m_hakoMainPlayer.IsMoving)
                {
                    if (m_isMoving == false)
                    {
                        m_isMoving = true;
                        m_animatorComponent.PlayAnimation("Move");
                    }
                    SetPositionInternal(m_hakoMainPlayer.MainPlayerPosition);
                    SetRotationInternal(m_hakoMainPlayer.MainPlayerRotation);
                }
                else if (m_isMoving)
                {
                    m_isMoving = false;
                    m_animatorComponent.PlayDefaultAnimation();
                }
            }
        }

        protected override void OnUnInit()
        {
            m_animatorComponent = null;
            m_hakoMainPlayer = null;
        }


        private void SetPositionInternal(Vector3 position)
        {
            Owner.SetPosition(position);
        }

        private void SetRotationInternal(Quaternion rotation)
        {
            Owner.SetRotation(rotation);
        }

    }
}

