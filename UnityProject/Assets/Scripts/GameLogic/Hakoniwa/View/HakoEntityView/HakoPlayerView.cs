

using System;
using Phoenix.Core;
using Phoenix.LOS2D;
using UnityEngine;

namespace Phoenix.Hakoniwa
{

    public class HakoPlayerView : HakoActorView
    {
        private LOSTarget m_losTarget;

        public override Boolean CanInteract => false;

        protected override void OnCollectComponent()
        {
            base.OnCollectComponent();
            m_viewComponents.Add(CreateComponent<FollowComponent>());
            m_viewComponents.Add(CreateComponent<InteractableComponent>());
        }

        protected override void OnInit()
        {
            base.OnInit();
            m_losTarget = gameObject.GetOrAddComponent<LOSTarget>();
        }
        protected override void UpdateView()
        {

        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            GameObject.DestroyImmediate(m_losTarget);
            m_losTarget = null;
        }


        //private void Move(bool completed)
        //{
        //    if (!completed)
        //    {
        //        return;
        //    }

        //    Single x = UnityEngine.Random.Range(-10, 10);
        //    Single z = UnityEngine.Random.Range(-10, 10);
        //    moveComponent.SetDestination(new Vector3(x, 0, z), false, Move);
        //}

    }
}

