
using System;
using System.Collections.Generic;
using Phoenix.Hakoniwa.Logic;
using UnityEngine;

namespace Phoenix.Hakoniwa
{

    // HakoPlayer, HakoNpc, <PERSON><PERSON>Partner, HakoMoster, HakoObject, etc.

    public abstract class HakoEntityView : MonoBehaviour
    {
        protected HakoniwaEntity m_hakoEntity;
        protected List<HakoEntityViewComponent> m_viewComponents = new List<HakoEntityViewComponent>();

        public Int32 EntityUid => m_hakoEntity.Uid;
        public HakoniwaEntity HakoEntity => m_hakoEntity;
        public virtual Single SqrDistance => 0;


        public void Init(HakoniwaEntity hakoEntity)
        {
            m_hakoEntity = hakoEntity;
            OnCollectComponent();
            InitComponent();
            OnInit();
            OnPostInit();
            UpdateView();
        }

        public void Tick(Single dt)
        {
            OnTick(dt);
            foreach (HakoEntityViewComponent component in m_viewComponents)
            {
                component.Tick(dt);
            }
        }

        public void UnInit()
        {
            OnUnInit();
            foreach (HakoEntityViewComponent component in m_viewComponents)
            {
                component.UnInit();
            }
            m_viewComponents.Clear();
            m_hakoEntity = null;
        }

        public T GetHakoComponent<T>() where T : HakoEntityViewComponent, new()
        {
            foreach (var component in m_viewComponents)
            {
                if (component.GetType() == typeof(T))
                {
                    return component as T;
                }
            }
            return null;
        }

        public void SetPosition(Vector3 position)
        {
            transform.position = position;
        }

        public void SetRotation(Quaternion rotation)
        {
            transform.rotation = rotation;
        }

        #region Internal

        protected T CreateComponent<T>() where T : HakoEntityViewComponent, new()
        {
            T component = new T();
            component.SetOwner(this);
            return component;
        }

        protected void InitComponent()
        {
            foreach (HakoEntityViewComponent component in m_viewComponents)
            {
                component.Init();
            }
        }

        #endregion

        #region AnimationEvent

        protected void PlaySound(AnimationEvent e)
        {
        }

        #endregion

        protected virtual void OnCollectComponent() { }
        protected virtual void OnInit() { }
        protected virtual void OnPostInit() { }
        protected virtual void UpdateView() { }
        protected virtual void OnTick(Single dt) { }
        protected virtual void OnUnInit() { }
    }
}

