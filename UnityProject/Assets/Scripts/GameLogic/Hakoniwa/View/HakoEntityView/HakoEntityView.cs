
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.GameLogic.UI;
using Phoenix.Hakoniwa.Logic;
using UnityEngine;

namespace Phoenix.Hakoniwa
{

    // HakoPlayer, HakoNpc, HakoPartner, HakoMoster, HakoObject, etc.

    public abstract class HakoEntityView : MonoBehaviour
    {
        private String m_prefabName;
        protected HakoniwaEntity m_hakoEntity;
        protected List<HakoEntityViewComponent> m_viewComponents = new List<HakoEntityViewComponent>();

        public Int32 EntityUid => m_hakoEntity.Uid;

        public HakoniwaEntityType EntityType => m_hakoEntity.EntityType;

        public HakoLocation Location => transform.position;

        public HakoniwaEntity HakoEntity => m_hakoEntity;

        public virtual Single SqrDistance => Single.MaxValue;

        /// <summary> 是否可以被交互 </summary>
        public virtual Boolean CanInteract => m_hakoEntity.EntityinteractionInfos.Count > 0;


        public void Init(HakoniwaEntity hakoEntity)
        {
            m_hakoEntity = hakoEntity;
            OnCollectComponent();
            InitComponent();
            OnInit();
            OnPostInit();
            UpdateView();


#if UNITY_EDITOR
            m_prefabName = gameObject.name;
            gameObject.name = $"{m_prefabName}_[{m_hakoEntity.Name}][{EntityUid}]";
#endif
        }

        public void Tick(Single dt)
        {
            OnTick(dt);
            foreach (HakoEntityViewComponent component in m_viewComponents)
            {
                component.Tick(dt);
            }
        }

        public void UnInit()
        {
#if UNITY_EDITOR
            gameObject.name = m_prefabName;
#endif

            OnUnInit();
            foreach (HakoEntityViewComponent component in m_viewComponents)
            {
                component.UnInit();
            }
            m_viewComponents.Clear();
            m_hakoEntity = null;
        }

        public T GetHakoComponent<T>() where T : HakoEntityViewComponent, new()
        {
            foreach (var component in m_viewComponents)
            {
                if (component.GetType() == typeof(T))
                {
                    return component as T;
                }
            }
            return null;
        }

        public void SetPosition(Vector3 position)
        {
            transform.position = position;
        }

        public void SetRotation(Quaternion rotation)
        {
            transform.rotation = rotation;
        }

        public void LookAt(HakoEntityView view)
        {
            Vector3 direction = (view.Location - Location).normalized;
            Vector3 xzDirection = new Vector3(direction.x, 0, direction.z);
            Quaternion rotateion = Quaternion.LookRotation(xzDirection);
            SetRotation(rotateion);
        }

        /// <summary> 开始交互 </summary>
        public void StartInteract(HakoEntityView sourceEntityView)
        {
            OnStartInteract(sourceEntityView);
        }

        #region Internal

        protected T CreateComponent<T>() where T : HakoEntityViewComponent, new()
        {
            T component = new T();
            component.SetOwner(this);
            return component;
        }

        protected void InitComponent()
        {
            foreach (HakoEntityViewComponent component in m_viewComponents)
            {
                component.Init();
            }
        }

        #endregion

        #region AnimationEvent

        protected void PlaySound(AnimationEvent e)
        {
        }
        protected void PlayStepSound(AnimationEvent e)
        {
        }

        #endregion

        protected virtual void UpdateView()
        {
            Vector3 position = m_hakoEntity.Position;
            HakoSceneHandler.RepairPosition(ref position);
            SetPosition(position);
            SetRotation(Quaternion.Euler(m_hakoEntity.Rotation));
        }

        protected virtual void OnStartInteract(HakoEntityView sourceEntityView) { HakoEntityInteractionUI.Show(); }
        protected virtual void OnCollectComponent() { }
        protected virtual void OnInit() { }
        protected virtual void OnPostInit() { }
        protected virtual void OnTick(Single dt) { }
        protected virtual void OnUnInit() { }
    }
}

