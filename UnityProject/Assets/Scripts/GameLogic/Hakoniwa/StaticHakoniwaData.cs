


using System;

namespace Phoenix.Hakoniwa
{
    public static class StaticHakoniwaData
    {
        /// <summary> 箱庭角色跑速度 （单位：米/秒）</summary>
        public const Single EntityRunSpeed = 4.5f;
        /// <summary> 箱庭角色走速度 （单位：米/秒）</summary>
        public const Single EntityWalkSpeed = 2f;
        /// <summary> 箱庭角色平均速度，用于动画播放判定 </summary>
        public const Single EntityAverageMoveSpeed = (EntityRunSpeed + EntityWalkSpeed) / 2f;


        /// <summary> 箱庭主角交互检测半径 （单位：米）</summary>
        public const Single InteractDetectionRadius = 1.5f;
        /// <summary> 箱庭主角交互检测时间间隔 （单位：秒）</summary>
        public const Single InteractDetectionTickInternal = 0.3f;
    }
}

