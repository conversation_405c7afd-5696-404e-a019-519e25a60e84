
using Cinemachine;
using System;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public class CameraShakeSystem : Singleton<CameraShakeSystem>
    {
        public static void Shake(ShakeParam param, Action onEnd = null)
        {
            Shake(param.m_duration, param.m_amplitude, param.m_frequency, onEnd);
        }

        public static void Shake(float duration, float amplitude = 1, float frequency = 2, Action onEnd = null)
        {
            instance.OnStart(duration, amplitude, frequency, onEnd);
        }

        public static void Stop()
        {
            instance.OnStop();
        }

        /// <summary> 初始化操作 </summary>
        protected override void OnInit()
        {
            m_noiseDic = new Dictionary<ICinemachineCamera, CinemachineBasicMultiChannelPerlin>();
            m_isShaking = false;
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        /// <summary> 清理操作 </summary>
        protected override void OnUnInit()
        {
            m_noiseDic.Clear();
            m_isShaking = false;
            TickManager.instance.UnRegisterGlobalTick(OnTick);
        }

        protected void OnTick(TimeSlice ts)
        {
            if (!m_isShaking)
            {
                return;
            }
            if (m_duration > 0)
            {
                m_duration -= ts.deltaTime;
                return;
            }
            OnStop();
        }

        /// <summary> 震动启动 </summary>
        private void OnStart(float duration, float amplitude = 1, float frequency = 2, Action onEnd = null)
        {
            OnStop();
            m_noiseComponent = GetCurrentActiveVirtualCameraPerlinComponent();
            if (m_noiseComponent != null )
            {
                m_duration = duration;
                m_noiseComponent.m_AmplitudeGain = amplitude;
                m_noiseComponent.m_FrequencyGain = frequency;
                if (m_noiseComponent.m_NoiseProfile == null)
                {
                    //Debug.LogWarningFormat("[CameraShake] 当前VirtualCamera NoiseProfile不存在, Name={0}, ，Time={1}", Instance.CinemachineBrain.ActiveVirtualCamera.Name, Time.realtimeSinceStartup);
                    //var noiseSetting = LoaderManager.Instance.GetResource<NoiseSettings>(ConstDefine.ProjectUBattleCameraShakeAsset);
                    //if (noiseSetting == null)
                    //    Debug.LogError("[CameraShake] 加载NoiseSettings Fail, NoiseSettingName = ProjectUBattleCameraShake");
                    //else
                    //    CurrentNoiseComponent.m_NoiseProfile = noiseSetting;
                }
                m_onEnd = onEnd;
                m_isShaking = true;
            }
            else
            {
                onEnd?.Invoke();
            }
        }

        /// <summary> 震动停止 </summary>
        private void OnStop()
        {
            m_duration = 0f;
            m_isShaking = false;
            if (m_noiseComponent != null)
            {
                m_noiseComponent.m_AmplitudeGain = 0f;
                m_noiseComponent.m_FrequencyGain = 0f;
            }
            if (m_onEnd != null)
            {
                m_onEnd.Invoke();
                m_onEnd = null;
            }
        }

        /// <summary> 获取震动噪音组件 </summary>
        private CinemachineBasicMultiChannelPerlin GetCurrentActiveVirtualCameraPerlinComponent()
        {
            CinemachineBrain brain = CinemachineCore.Instance.GetActiveBrain(0);
            if (brain == null)
            {
                return null;
            }
            ICinemachineCamera cinemachineCamera = brain.ActiveVirtualCamera;
            CinemachineBasicMultiChannelPerlin noise;
            if (!m_noiseDic.TryGetValue(cinemachineCamera, out noise))
            {
                CinemachineVirtualCamera virtualCamera = (CinemachineVirtualCamera)cinemachineCamera;
                noise = virtualCamera.GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>();
                //if (noise == null)
                //    noise = virtualCamera.AddCinemachineComponent<CinemachineBasicMultiChannelPerlin>();
                if(noise != null)
                {
                    m_noiseDic.Add(cinemachineCamera, noise);
                }
            }
            return noise;
        }

        #region Field

        private bool m_isShaking;
        private float m_duration;
        private Action m_onEnd;

        private CinemachineBasicMultiChannelPerlin m_noiseComponent { get; set; }
        private Dictionary<ICinemachineCamera, CinemachineBasicMultiChannelPerlin> m_noiseDic;
        #endregion
    }

    public struct ShakeParam
    {
        public Single m_duration;
        public Single m_amplitude;
        public Single m_frequency;

        public ShakeParam(Single duration, Single amplitude, Single frequency)
        {
            m_duration = duration;
            m_amplitude = amplitude;
            m_frequency = frequency;
        }
        public static ShakeParam Small = new ShakeParam(0.2f, 1.2f, 1.5f);
        public static ShakeParam Middle = new ShakeParam(0.3f, 2.2f, 2f);
        public static ShakeParam Large = new ShakeParam(0.35f, 2.5f, 2f);
    }
}
