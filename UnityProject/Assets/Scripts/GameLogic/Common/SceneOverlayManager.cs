using System;
using System.Collections.Generic;
using MyFramework.Common;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Rendering.Scene
{
    public class SceneOverlayManager : Singleton<SceneOverlayManager>
    {
        private PriorityList<SceneOverlay> sceneOverlays = new PriorityList<SceneOverlay>();

        public void Add(SceneOverlay sceneOverlay)
        {
            sceneOverlays.Add(sceneOverlay, sceneOverlay.mPriority);
            var topScene = sceneOverlays.Peek();
            topScene.UpdateSceneGlobalParam();
            SetCurrentSceneOverlayRunning();
            EventManager.instance.Broadcast(EventID.SceneOverlay_Changed);
        }

        public void Remove(SceneOverlay sceneOverlay)
        {
            if(sceneOverlays.Count > 0)
            {
                bool bRemove = sceneOverlays.Remove(sceneOverlay);
                if(bRemove)
                {
                    if (sceneOverlays.Count > 0)
                    {
                        var topScene = sceneOverlays.Peek();
                        topScene.UpdateSceneGlobalParam();
                        SetCurrentSceneOverlayRunning();
                        EventManager.instance.Broadcast(EventID.SceneOverlay_Changed);
                    }
                }
            }           
        }

        public SceneOverlay GetCurrentSceneOverlay()
        {
            return sceneOverlays.Peek();
        }

        public Transform GetCurrentLightTrm()
        {
            if (sceneOverlays.Count > 0)
            {
                return sceneOverlays.Peek()?.mLightTrm;
            }
            return null;
        }

        void SetCurrentSceneOverlayRunning()
        {
            for (int i = 0; i < sceneOverlays.Count; i++)
            {
                if (i == 0)
                    sceneOverlays[i].Running = true;
                else
                    sceneOverlays[i].Running = false;
            }
        }

        //private void LateUpdate()
        //{
        //    if (sceneOverlays.Count > 0)
        //    {
        //        sceneOverlays.Peek() ?.UpdateSceneGlobalParam();
        //    }
        //}
    }
}