
using System;
using UnityEngine;

namespace Phoenix.GameLogic
{
    public class CharacterLightingModifier : MonoBehaviour
    {
        public String m_LightDirPropertyName = "_Global_LightDir";
        public Vector3 m_LightDir = new Vector3(1.0f,1.0f,0.0f);
        public String m_LightColorPropertyName = "_Global_LightColor";
        public Color m_LightColor = new Color(1.0f,0.0f,0.0f,1.0f);
        public String m_LightIntensityPropertyName = "_Global_LightIntensity";
        public Single m_LightIntensity=2;

        public String m_Shadow1StepPropertyName = "_Global_Shadow1Step";
        public Single m_Shadow1Step=0.6f;

        public String m_Shadow1ColorPropertyName = "_Global_Shadow1Color";
        public Color m_Shadow1Color = new Color(0.5f,0.5f,0.5f,1.0f);

        public String m_SDFShadowColorPropertyName = "_Global_SDFShadowColor";
        public Color m_SDFShadowColor = new Color(0.5f,0.5f,0.5f,1.0f);

        private void OnValidate()
        {
            Debug.Log("OnValidate UpdateCharacterLight");
            UpdateCharacterLight();
        }

        private void UpdateCharacterLight()
        {
            Shader.SetGlobalVector(m_LightDirPropertyName, m_LightDir.normalized);
            Shader.SetGlobalColor(m_LightColorPropertyName, m_LightColor);
            Shader.SetGlobalFloat(m_LightIntensityPropertyName, m_LightIntensity);
            Shader.SetGlobalFloat(m_Shadow1StepPropertyName, m_Shadow1Step);
            Shader.SetGlobalColor(m_Shadow1ColorPropertyName, m_Shadow1Color);
            Shader.SetGlobalColor(m_SDFShadowColorPropertyName, m_SDFShadowColor);
        }
    }
}
