using Phoenix.Core;

namespace Phoenix.GameLogic.Common
{
    public class TaskBase
    {
        public enum EState
        {
            E_INIT,
            E_LOAD,
            E_LOADING,
            E_UPDATE,
            E_END,
        }

        public TaskBase(string sName = "") { mName = sName; }
        public EState State { get; set; } = EState.E_INIT;

        protected string mName;

        public bool Running { get; set; } = true;

        public void Start()
        {
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        public void Stop()
        {
            TickManager.instance.UnRegisterGlobalTick(OnTick);
            OnStop();
        }

        protected virtual void OnInit() 
        {
        }

        protected virtual void OnLoad()
        {
        }

        protected virtual bool OnLoadEnd()
        {
            return true;
        }

        protected virtual void OnUpdate(TimeSlice dt)
        {
        }

        protected virtual void OnStop()
        {
        }

        private void OnTick(TimeSlice dt)
        {
            if(Running)
            {
                switch (State)
                { 
                    case EState.E_INIT:
                        OnInit();     
                        State = EState.E_LOAD;
                    break;      

                    case EState.E_LOAD:
                        OnLoad(); 
                        State = EState.E_LOADING;
                    break;

                    case EState.E_LOADING:
                        if(OnLoadEnd())
                        {
                            State = EState.E_UPDATE;
                        }
                    break;

                    case EState.E_UPDATE:
                        OnUpdate(dt); 
                    break;

                    case EState.E_END:
                        OnStop(); 
                    break;

                    default:
                        break;
                
                }
            }
        }
    }
}