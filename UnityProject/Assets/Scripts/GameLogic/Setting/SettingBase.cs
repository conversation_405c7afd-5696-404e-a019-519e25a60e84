using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public class SettingBase<T> : ScriptableObject where T : SettingBase<T>
    {
        private static T m_instance;

        public static T instance
        {
            get
            {
#if UNITY_EDITOR
                if (!Application.isPlaying)
                {
                    if (m_instance == null)
                    {
                        m_instance = UnityEditor.AssetDatabase.LoadAssetAtPath<T>(path);
                    }
                }
#endif
                return m_instance;
            }
            set { m_instance = value; }
        }

        public static string path
        {
            get { return string.Format("Assets/Res/Setting/{0}.asset", typeof(T).Name); }
        }
    }
}
