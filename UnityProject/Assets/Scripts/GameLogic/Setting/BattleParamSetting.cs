using System;
using UnityEngine;

namespace Phoenix.GameLogic
{
    [CreateAssetMenu(fileName = "BattleParamSetting", menuName = "Setting Asset/BattleParamSetting")]
    public class BattleParamSetting : SettingBase<BattleParamSetting>
    {
        [Header("���Ӵ�С")]
        public float gridSize;
        [Header("��ɫ�ƶ��ٶ�")]
        public float entityMoveSpeed;
        [Header("��ս��ʼ���̵ȴ�ʱ��")]
        public float combatEnterWaitTime;
        [Header("��ս�������̵ȴ�ʱ��")]
        public float combatExitWaitTime;
        [<PERSON>er("��ս��ʼ���̵ȴ�ʱ��(ǿ�Ƴ���)")]
        public float combatEnterWaitTime2;
        [<PERSON>er("��ս�������̵ȴ�ʱ��(ǿ�Ƴ���)")]
        public float combatExitWaitTime2;
        [<PERSON>er("������϶�ȴ�ʱ��(ǿ�Ƴ���)")]
        public float combatSkillIntervalWaitTime2;
        [Header("ʩ�ż������ƶ��󹥻�ǰ�ȴ�ʱ��")]
        public float castSkillBeginWaitTime;
        [Header("Buff����ȴ�ʱ��")]
        public float buffTriggerWaitTime;
        [Header("����Buff����ȴ�ʱ��")]
        public float terrainBuffTriggerWaitTime;
        [Header("��ս�׶��ٶ�")]
        public float engageSpeed = 1f;
        [Header("Ĭ��Bgm·��")]
        public string defaultBgmPath;
        public BattleUISetting m_battleUISetting;
        public BattleCameraSetting m_battleCameraSetting;
        public Color m_grayColor = new Color(.5f, .5f, .5f, 1);
    }


    [Serializable]
    public class BattleUISetting
    {
        public float m_battleBeginWinConditionUITime = 1f;
        public float m_battleTurnSwitchUITime = 1f;
        public float m_skillAnnounceUITime = 1f;
        [Tooltip("���ܹ���ģʽ����")]
        public bool m_skillAutoResonance = false;
        [Tooltip("��ս���ﵥ���˺���ʾʱ��")]
        public float m_battleCombatSingleDamageShowTime = 0.6f;
        [Header("ս���ٶ�X1")]
        public float m_timeScaleBattleSpeedX1 = 1f;
        [Header("ս���ٶ�X2")]
        public float m_timeScaleBattleSpeedX2 = 1.7f;


        [Header("�غϿ�ʼ�����ָ�������ʱ��")]
        public float m_energyUpdateTime = 0.8f;
        [Header("�غϿ�ʼ�����ָ������ӳ���ʾʱ��")]
        public float m_energyUpdateDelayShowTime = 0.5f;
        [Header("ս����������ʾʱ��")]
        public float m_dialogBubbleShowTime = 2f;
    }

    [Serializable]
    public class BattleCameraSetting
    {
        [Header("Zoom�ٶ�ϵ��")]
        public float m_cameraZoomSpeedFactor = 2f;
    }
}
