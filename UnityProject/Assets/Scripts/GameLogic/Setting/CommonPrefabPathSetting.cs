using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    [CreateAssetMenu(fileName = "CommonPrefabPathSetting", menuName = "Setting Asset/CommonPrefabPathSetting")]
    public class CommonPrefabPathSetting : SettingBase<CommonPrefabPathSetting>
    {
        public AssetWeakRef deadFxRef;
        public AssetWeakRef commonOnHitDamageFxRef;
        public AssetWeakRef physicsCameraRoot;
        public AssetWeakRef virtualCameraRoot;
        public AssetWeakRef worldVirtualCameraRoot;
        public AssetWeakRef normalGridMaterial;
        public AssetWeakRef dangerGridMaterial;
        public AssetWeakRef movePathGridMaterial;
        public AssetWeakRef moveRangeGridMaterial;
        public AssetWeakRef dashedLineRangeGridMaterial;
        public AssetWeakRef gridFxOfSetupRed;
        public AssetWeakRef gridFxOfSetupGreen;
        public AssetWeakRef gridFxOfActive;
        public AssetWeakRef gridFxOfActivable;
        public AssetWeakRef gridFxOfSkillRed;
        public AssetWeakRef gridFxOfSkillRedBold;
        public AssetWeakRef gridFxOfSkillGreen;
        public AssetWeakRef gridFxOfSkillGreenBold;
        public AssetWeakRef gridFxOfSkillBlue;
        public AssetWeakRef gridFxOfSkillBlueBold;
        public AssetWeakRef gridFxOfSkillYellow;
        public AssetWeakRef gridFxOfSkillDirArrow;
        public AssetWeakRef gridFxOfTargetRed;
        public AssetWeakRef gridFxOfTargetBlue;
        public AssetWeakRef gridFxOfSkillPredict;
        public AssetWeakRef worldQuestTrackLine;
        public AssetWeakRef skillPreviewSourceGo;
        public AssetWeakRef skillPreviewTargetGo;
        public AssetWeakRef transparentMaterial;
        public AssetWeakRef worldBaseScene;
    }
}
