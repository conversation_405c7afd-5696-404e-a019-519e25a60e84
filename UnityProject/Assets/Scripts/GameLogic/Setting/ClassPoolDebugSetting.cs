using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core
{
    [Serializable]
    public class ClassPoolDebugSetting : ScriptableObject, IClassPoolDebugHelper
    {
        public bool needDebug;
        public List<string> debugTypeNameList = new List<string>();

        public bool IsDebugMode()
        {
            return needDebug;
        }

        public bool CheckNeedDebug(string typeName)
        {
            if (needDebug)
            {
                foreach(var debugTypeName in debugTypeNameList)
                {
                    if (typeName == debugTypeName)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
