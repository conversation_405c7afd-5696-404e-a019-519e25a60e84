using Cysharp.Threading.Tasks;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.SceneManagement;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.Common;


namespace Phoenix.GameLogic.Opening
{
    public class TimelineSceneTask : TaskBase
    {

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scnName"></param>
        public void AddScene(string scnName)
        {
            if (!msSceneList.Contains(scnName))
            {
                msSceneList.Add(scnName);
            }
        }

        public void ForceStop()
        {
            if (actionOnEnd != null)
            {
                actionOnEnd();
            }
            ChangeScene(NextBattleId);
            miCurrentStep = -1;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        public TimelineSceneTask(string name) : base(name)
        {
        }


        #region Overrides of TaskBase

        /// <summary>
        /// 当task启动
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        protected override void OnInit()
        {
            RegisterEvent();
            mCameraList = new Camera[msSceneList.Count];
            mTimelineList = new PlayableDirector[msSceneList.Count];
            base.OnInit();
        }

        protected override void OnLoad()
        {
            mSceneLoadOperation = RuntimeSceneManager.instance.ChangeScene(msSceneList[0], true, null);
            if(msSceneList.Count > 1)
            {
                for (int i = 1; i < msSceneList.Count; i++)
                {
                    mSceneLoadOperation = RuntimeSceneManager.instance.AddScene(msSceneList[i], null);
                }
            }
            base.OnLoad();
        }

        protected override bool OnLoadEnd()
        {
            if(mSceneLoadOperation.isDone)
            {
                OnLoadStaticResCompleted();
                ActiveScene(0);
            }
            return mSceneLoadOperation.isDone;
        }

        /// <summary>
        /// 
        /// </summary>
        protected override void OnUpdate(TimeSlice dt)
        {
            //start
            if (miCurrentStep == -1)
            {
                return;
            }
            //last step
            else if (miCurrentStep == msSceneList.Count - 1)
            {
                if (mTimelineList[miCurrentStep] == null || mTimelineList[miCurrentStep].state != PlayState.Playing)
                {
                    if (actionOnEnd != null)
                    {
                        actionOnEnd();
                    }
                    ChangeScene(NextBattleId);
                    miCurrentStep = -1;
                }
            }
            //normal
            else
            {
                if (mTimelineList[miCurrentStep] == null || (mTimelineList[miCurrentStep] != null && mTimelineList[miCurrentStep].state != PlayState.Playing))
                {
                    ActiveScene(miCurrentStep + 1);
                }
            }
        }


        /// <summary>
        /// 当task停止
        /// </summary>
        protected override void OnStop()
        {
            UnRegisterEvent();
        }
        #endregion

        /// <summary>
        /// 当静态资源加载完成
        /// </summary>
        protected void OnLoadStaticResCompleted()
        {
            //每个scene加载完成后先关闭相机，防止一开始穿帮
            for(miLoadSceneCount = 0; miLoadSceneCount < msSceneList.Count; miLoadSceneCount++)
            {
                Scene scnLayer = mSceneLoadOperation.GetScene(msSceneList[miLoadSceneCount]).unityScene;
                if (scnLayer != null)
                {
                    foreach (var scnLayerUnitySceneRootObj in scnLayer.GetRootGameObjects())
                    {
                        if (scnLayerUnitySceneRootObj.name.Equals("Camera"))
                        {
                            Camera cam = scnLayerUnitySceneRootObj.GetComponent<Camera>();
                            if (cam != null)
                            {
                                cam.enabled = false;
                                mCameraList[miLoadSceneCount] = cam;
                            }
                        }
                        else if (scnLayerUnitySceneRootObj.name.ToLower().Contains("timeline"))
                        {
                            PlayableDirector timeline = scnLayerUnitySceneRootObj.GetComponent<PlayableDirector>();
                            if (timeline != null)
                            {
                                mTimelineList[miLoadSceneCount] = timeline;
                            }
                        }
                        else
                        {
                            var timeline = scnLayerUnitySceneRootObj.GetComponent<PlayableDirector>();
                            if (timeline != null)
                            {
                                mTimelineList[miLoadSceneCount] = timeline;
                            }
                        }
                        scnLayerUnitySceneRootObj.SetActive(false);
                    }
                    m_scnArray.Add(scnLayer);
                }
            }
        }


        /// <summary>
        /// 一帧内硬切会导致timeline还没来得及执行，但是camera渲染已经开始，第一帧不会被黑幕遮掉
        /// 步骤如下
        /// 1，当前场景除相机都关闭，下一场景除相机都开启，执行下一场景timeline
        /// 2，关闭当前场景相机，开启下一场景相机，关闭当前场景timeline
        /// </summary>
        /// <param name="idx"></param>
        /// <returns></returns>
        private async UniTaskVoid ActiveSceneInternal(int idx)
        {
            int currentIdx = idx - 1;
            if (currentIdx >= 0)
            {
                Scene currentlayer = m_scnArray[currentIdx];
                if (currentlayer != null)
                {
                    foreach (var layerUnitySceneRootObj in currentlayer.GetRootGameObjects())
                    {
                        if(layerUnitySceneRootObj != mCameraList[currentIdx].gameObject)
                            layerUnitySceneRootObj.SetActive(false);
                    }
                }
            }
            Scene nextlayer = m_scnArray[idx];
            if (nextlayer != null)
            {
                foreach (var layerUnitySceneRootObj in nextlayer.GetRootGameObjects())
                {
                    layerUnitySceneRootObj.SetActive(true);
                }
                UnityEngine.SceneManagement.SceneManager.SetActiveScene(nextlayer);
            }
            if(mTimelineList[idx] != null)
                mTimelineList[idx].Play();
            miCurrentStep = idx;
            await UniTask.Yield();
            if (currentIdx >= 0)
            {
                if(mTimelineList[currentIdx] != null)
                    mTimelineList[currentIdx].Pause();
                mCameraList[currentIdx].gameObject.SetActive(false);
            }
            if(mCameraList[idx] != null)
                mCameraList[idx].enabled = true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idx"></param>
        private void ActiveScene(int idx)
        {
            if (mActionOnTimelineStart != null)
            {
                mActionOnTimelineStart(idx);
            }
            ActiveSceneInternal(idx).Forget();
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        private void ChangeScene(int id)
        {
            //world map
            if (id == 0)
            {
                //WorldMapTask.StartTask(); ;
            }
            //into battle
            else if(id > 0)
            {
                Debug.LogFormat("[OP End] Into battle {0}", id);
                BattleLaunchUtility.StartBattle(id);
            }
            Stop();
        }
        
        /// <summary>
        /// 
        /// </summary>
        private void RegisterEvent()
        {

        }

        /// <summary>
        /// 
        /// </summary>
        private void UnRegisterEvent()
        {

        }

        protected List<Scene> SceneArray
        {
            get
            {
                return m_scnArray;
            }
        }

        List<Scene> m_scnArray = new List<Scene>();

        public Action actionOnEnd;
        //每次timeline开始的时候，比如timeline0开始
        public Action<int> mActionOnTimelineStart;
        private List<string> msSceneList = new List<string>();
        private Camera[] mCameraList;
        private PlayableDirector[] mTimelineList;
        private int miLoadSceneCount = 0;
        private int miCurrentStep = -1;
        RuntimeSceneLoadOperation mSceneLoadOperation;
        public int NextBattleId { get; set; }
    }
}

