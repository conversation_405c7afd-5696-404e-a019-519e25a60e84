using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using UnityEngine;

namespace Phoenix.GameLogic
{
    public static class StaticDataShortCut
    {
        //public static string GetResourcePath(ConstResourceId id)
        //{
        //    ConstResourceConfigData configData = ConfigDataManager.instance.GetConstResource(id);
        //    if (configData != null)
        //    {
        //        return configData.Path;
        //    }
        //    return string.Empty;
        //}
    }
}
