using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.UI;
using UnityEngine;
using Phoenix.GameLogic.World;
using System;
using Phoenix.ConfigData;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic
{
    public static class BattleShortCut
    {

        public static void RestBattleShortCut()
        {
            InBattleCombatStage = false;
        }

        public static GameStateBattle battleState
        {
            get { return GameManager.instance.stateMachine.GetState((int)EGameState.Battle) as GameStateBattle; }
        }

        public static ulong hostPlayerId
        {
            get
            {
                if (battleExecuter != null)
                {
                    return battleExecuter.hostPlayerId;
                }
                return 0;
            }
        }

        public static Int32 hostTeamUid
        {
            get
            {
                Int32 hostTeamUid = 0;
                BattlePlayer player = sampleBattle.GetPlayerByPlayerId(hostPlayerId);
                if (player != null)
                {
                    foreach (var team in sampleBattle.GetTeamList())
                    {
                        if (team.controlledPlayerSlotId == player.slotId)
                        {
                            hostTeamUid = team.uid;
                            break;
                        }
                    }
                }
                return hostTeamUid;
            }
        }

        public static BattleScene battleScene
        {
            get { return battleState.battleScene; }
        }

        public static BattleSceneGridPlusHandler battleSceneGridManager
        {
            get
            {
                if (battleState == null || battleState.battleScene == null)
                {
                    return null;
                }
                return battleScene.sceneGridHandler;
            }
        }

        public static IBattle sampleBattle
        {
            get { return battleExecuter.sampleBattle; }
        }

        public static IBattle logicBattle
        {
            get { return battleExecuter.battle; }
        }

        public static BattleExecuterClient battleExecuter
        {
            get
            {
                GameStateBattle state = battleState;
                if (state != null)
                {
                    return state.battleExecuter;
                }
                return null;
            }
        }

        public static IBattleInfoGetter infoGetter
        {
            get { return logicBattle.infoGetter; }
        }

        public static GameObject sceneEntityRoot
        {
            get
            {
                BattleScene scene = battleScene;
                if (scene != null)
                {
                    return scene.sceneEntityRoot;
                }
                return null;
            }
        }


        public static Vector2 GetScreenPositionByWorldPosition(Vector3 worldPosition)
        {
            BattleScene scene = battleScene;
            if (scene != null)
            {
                return scene.GetScreenPositionByWorldPosition(worldPosition);
            }
            return Vector2.zero;
        }

        public static GridPosition GetGridPositionByScreenPosition(Vector2 screenPos)
        {
            BattleScene scene = battleScene;
            if (scene != null)
            {
                return scene.GetGridPositionByScreenPosition(screenPos);
            }
            return GridPosition.invalid;
        }

        public static Vector3 GetGridWorldPosition(GridPosition gridPos)
        {
            return battleSceneGridManager.GetGridWorldPosition(gridPos);
        }

        public static void ChangeBattleState(BattleStateId stateId)
        {
            battleExecuter.stateMachine.ChangeState((int)stateId);
        }

        public static BattleTeam activeTeam
        {
            get
            {
                return sampleBattle.GetTeamByIndex(sampleBattle.GetCurTeamIndex());
            }
        }

        public static bool IsHostCamp(int campId)
        {
            BattleTeam hostTeam = GetTeamLookAsHost();
            return hostTeam != null && hostTeam.campId == campId;
        }

        public static bool IsHostPlayerTeam(int teamUid)
        {
            BattlePlayer player = sampleBattle.GetPlayerByPlayerId(hostPlayerId);
            BattleTeam team = sampleBattle.GetTeamByUid(teamUid);
            return player != null && team != null && team.controlledPlayerSlotId == player.slotId;
        }

        public static bool IsHostOrFriendTeamWithEntityUid(int entityUid)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                BattleTeam hostTeam = GetTeamLookAsHost();
                if (hostTeam != null)
                {
                    BattleTeam team = sampleBattle.GetTeamByUid(entityView.GetTeamUid());
                    if (team != null)
                    {
                        return hostTeam.campId == team.campId;
                    }
                }
            }
            return false;
        }


        public static bool IsHostOrFriendTeam(int teamUid)
        {
            BattleTeam hostTeam = GetTeamLookAsHost();
            if (hostTeam != null)
            {
                BattleTeam team = sampleBattle.GetTeamByUid(teamUid);
                if (team != null)
                {
                    return hostTeam.campId == team.campId;
                }
            }
            return false;
        }

        public static BattleTeam GetTeamLookAsHost()
        {
            BattlePlayer player = sampleBattle.GetPlayerByPlayerId(hostPlayerId);
            if (player != null)
            {
                foreach (var team in sampleBattle.GetTeamList())
                {
                    if (team.controlledPlayerSlotId == player.slotId)
                    {
                        return team;
                    }
                }
            }
            return null;
        }
        public static BattleTeam GetHostBattleTeam()
        {
            BattlePlayer player = sampleBattle.GetPlayerByPlayerId(hostPlayerId);
            if (player != null)
            {
                foreach (var team in sampleBattle.GetTeamList())
                {
                    if (team.controlledPlayerSlotId == player.slotId)
                    {
                        return team;
                    }
                }
            }
            return null;
        }

        public static SkillForcastResult ForcastSkill(IEntity entity, GridPosition movePos, int skillUid, TargetSelectParamContainer paramContainer)
        {
            BattleExecuterForcast executer = new BattleExecuterForcast();
            executer.Init(battleExecuter);
            var result = executer.ForcastSkill(logicBattle, entity, movePos, skillUid, paramContainer);
            paramContainer.Release();
            executer.UnInit();
            return result;
        }

        public static Boolean InBattleCombatStage { get; set; }
        public static Boolean ForceSimplePerformance { get; set; }


        public static Int32 Level = 30;
    }
}
