using System;
using System.Collections;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class BattleInitializer : Initializer
    {
        public const string scenePath = @"Assets/Res/Scene/Basic/BattleScene.unity";

        public int battleRid;

        protected override IEnumerator OnProcess()
        {
            ResourceHandleCollection collection = new ResourceHandleCollection();
            BattleConfigData battleConfigData = ConfigDataManager.instance.GetBattle(battleRid);

            collection.ReadyLoad(CommonPrefabPathSetting.instance.normalGridMaterial.path, 0, AssetType.Asset);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.dangerGridMaterial.path, 0, AssetType.Asset);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.movePathGridMaterial.path, 0, AssetType.Asset);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.moveRangeGridMaterial.path, 0, AssetType.Asset);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.dashedLineRangeGridMaterial.path, 0, AssetType.Asset);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSetupRed.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSetupGreen.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfActive.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfActivable.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillRed.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillRedBold.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillGreen.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillGreenBold.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillBlue.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillBlueBold.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillYellow.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillDirArrow.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.gridFxOfSkillPredict.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.skillPreviewSourceGo.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.skillPreviewTargetGo.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.transparentMaterial.path, 0, AssetType.Asset);
            if (m_initialType == EInitialType.Sync)
            {
                collection.StartLoadSync();
            }
            else
            {
                collection.StartLoad(null);
                while (!collection.isLoadEnd)
                {
                    yield return null;
                }
            }
            collection.Clear();

            //HashSet<string> staheScenePaths = new HashSet<string>();

            //foreach (var stageData in battleConfigData.stageList)
            //{
            //    collection.ReadyLoad(stageData.sceneAssetPath, 0, AssetType.Asset);
            //    if (!string.IsNullOrEmpty(stageData.sceneAssetPath))
            //    {
            //        staheScenePaths.Add(stageData.sceneAssetPath);
            //    }
            //    if (!string.IsNullOrEmpty(stageData.sceneCombatAssetPath))
            //    {
            //        staheScenePaths.Add(stageData.sceneCombatAssetPath);
            //    }
            //}

            //if (m_initialType == EInitialType.Sync)
            //{
            //    collection.StartLoadSync();
            //}
            //else
            //{
            //    collection.StartLoad(null);
            //    while (!collection.isLoadEnd)
            //    {
            //        yield return null;
            //    }
            //}
            
            var battleScene = RuntimeSceneManager.instance.GetScene(scenePath);
            RuntimeSceneLoadOperation sceneOperation = null;
            if (battleScene == null)
            {
                Boolean changeSceneFlag = IsNeedChangeScene();
                if (changeSceneFlag)
                {
                    sceneOperation = RuntimeSceneManager.instance.ChangeScene(scenePath, true, null/*, staheScenePaths.ToList()*/);
                }
                else
                {
                    sceneOperation = RuntimeSceneManager.instance.AddScene(scenePath, null, null);
                }
            }
            while (sceneOperation != null && !sceneOperation.isDone)
            {
                yield return null;
            }

            UIManager.instance.Open<BattleCommonUI>(false);
        }

        private Boolean IsNeedChangeScene()
        {
            BattleConfigData battleConfigData = ConfigDataManager.instance.GetBattle(battleRid);
            BattleStageConfigData stageInfo = battleConfigData.stageList.GetValueSafely(0);
            if (stageInfo == null)
            {
                return false;
            }
            var scene = RuntimeSceneManager.instance.GetScene(stageInfo.sceneAssetPath);
            if (scene != null)
            {
                return false;
            }
            return true;
        }
    }
}