using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class PrepareBattleState : State
    {
        private int m_teamId;

        public override int stateIndex
        {
            get { return (int)BattleStateId.Prepare; }
        }

        public int teamId
        {
            get { return m_teamId; }
        }

        protected override void OnEnter(StateContext context)
        {
            UpdatePrepareSetupGridEffect();
            if (!BattleRetractManager.instance.IsRetracting)
            {
                UIManager.instance.Open<FormationUI>(false);
                UIManager.instance.Open<FormationEntityInfoUI>(false);

                float zoomPercentValue = CinemachineCameraSetting.instance.SettingData.m_prepareCameraZoomPercentValue;
                float fovValue = CinemachineCameraSetting.instance.SettingData.m_prepareCameraFieldOfView;
                BattleShortCut.battleScene.sceneCameraHandler.SetCameraZoomPercentAndFov(zoomPercentValue, fovValue, true);
            }
        }

        protected override void OnExit()
        {
            if (BattleShortCut.battleSceneGridManager != null)
            {
                BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Setup_Red);
                BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Setup_Green);
            }
            UIManager.instance.Close(typeof(FormationUI), true, true);
            UIManager.instance.Close(typeof(FormationEntityInfoUI), true, true);
        }

        protected void UpdatePrepareSetupGridEffect()
        {
            List<BattleStageDispositionInfo> dispositions = BattleShortCut.sampleBattle.GetCurStageDispositionInfoList();
            if (dispositions != null && dispositions.Count > 0)
            {
                foreach (BattleStageDispositionInfo info in dispositions)
                {
                    if (BattleShortCut.IsHostPlayerTeam(info.teamId))
                    {
                        BattleShortCut.battleSceneGridManager.AddGirdEffect(info.position, GridEffectPattern.Setup_Green);
                    }
                    else
                    {
                        BattleShortCut.battleSceneGridManager.AddGirdEffect(info.position, GridEffectPattern.Setup_Red);
                    }
                }
            }
        }
    }
}
