using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.GameLogic.Battle
{
    public class ResultBattleState : State
    {
        private int m_stateIndex;

        public override int stateIndex
        {
            get { return (int)BattleStateId.Result; }
        }

        protected override void OnEnter(StateContext context)
        {
            base.OnEnter(context);
        }
    }
}