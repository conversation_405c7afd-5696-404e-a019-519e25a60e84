using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class MainBattleState : State
    {
        public override int stateIndex
        {
            get { return (int)BattleStateId.Main; }
        }

        protected override void OnEnter(StateContext context)
        {
            base.OnEnter(context);
            if (!BattleRetractManager.instance.IsRetracting)
            {
                UIManager.instance.Open<BattleMainUI>(false);
                UIManager.instance.Open<BattleRoundUI>(false);
                UIManager.instance.Open<BattleEntityInfoUI>(false);

                float zoomPercentValue = CinemachineCameraSetting.instance.SettingData.m_defaultZoomPercentValue;
                float fovValue = CinemachineCameraSetting.instance.SettingData.m_battleCameraFieldOfView;
                BattleShortCut.battleScene.sceneCameraHandler.SetCameraZoomPercentAndFov(zoomPercentValue, fovValue, false);
            }
        }

        protected override void OnExit()
        {
            if (!BattleRetractManager.instance.IsRetracting)
            {
                UIManager.instance.Close<BattleMainUI>(false, true);
                UIManager.instance.Close<BattleRoundUI>(false, true);
                UIManager.instance.Close<BattleEntityInfoUI>(false, true);
                BattleOpModeManager.instance.ChangeMode(BattleOpModeId.Default);
            }
        }
    }
}
