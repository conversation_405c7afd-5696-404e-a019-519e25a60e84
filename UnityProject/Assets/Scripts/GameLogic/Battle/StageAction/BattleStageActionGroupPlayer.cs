using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class BattleStageActionGroupPlayer
    {
        private BattleStageActionGroup m_groupInfo;

        public void StartGroup(BattleStageActionGroup groupInfo)
        {
            m_groupInfo = groupInfo;

        }

        private void SS(List<BattleStageActionInfo> actionInfoList)
        {
            for (int i = 0; i < actionInfoList.Count; ++i)
            {
                BattleStageActionInfo info = actionInfoList[i];
                if(info.actionType == BattleStageActionType.Parallel)
                {
                    //BattleStageActionNodeParallel parrlel
                }
            }
        }
    }
}
