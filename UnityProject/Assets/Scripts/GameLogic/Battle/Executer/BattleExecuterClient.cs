using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.UI;

namespace Phoenix.Battle
{
    public abstract class BattleExecuterClient : BattleExecuter
    {
        protected BattleBase m_sampleBattle;
        protected StateMachine m_stateMachine = new StateMachine();
        protected ulong m_hostPlayerId = 12345;//可搬

        protected BattlePerformancePlayer m_performancePlayer = new BattlePerformancePlayer();
        protected bool m_isSkipping;
        protected bool m_isBlockPerformance;
        protected List<Action> actionOnWaitBlockPerformanceList = new List<Action>();


        public int IsWaitAllTeamUid { get { return m_waitAllTeamUid; } }

        public ulong hostPlayerId
        {
            get { return m_hostPlayerId; }
        }

        public BattleBase sampleBattle
        {
            get { return m_sampleBattle; }
        }

        public StateMachine stateMachine
        {
            get { return m_stateMachine; }
        }

        protected override void OnInit()
        {
            base.OnInit();
            m_performancePlayer.Init();
            m_performancePlayer.actionOnPerformanceEnd = OnPerformanceEnd;
            m_sampleBattle = CreateBattle(BattleInstanceType.ShowCopy);
            m_sampleBattle.CopyFrom(m_battle, CreatePoolMap());
            m_stateMachine.Init(
                new PrepareBattleState(),
                new MainBattleState()
            );
            EventManager.instance.RegisterListener<bool>(EventID.BattleControl_BlockPerformance, OnBlockPerformance);
            EventManager.instance.RegisterListener<Action>(EventID.BattleControl_BlockPerformanceAndWait, OnBlockPerformanceAndWait);
            EventManager.instance.RegisterListener<int>(EventID.BattleControl_WaitAll, OnWaitAllEntity);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_stateMachine.UnInit();
            m_sampleBattle.UnInit();
            actionOnWaitBlockPerformanceList.Clear();
            EventManager.instance.UnRegisterListener<bool>(EventID.BattleControl_BlockPerformance, OnBlockPerformance);
            EventManager.instance.UnRegisterListener<Action>(EventID.BattleControl_BlockPerformanceAndWait, OnBlockPerformanceAndWait);
            EventManager.instance.UnRegisterListener<int>(EventID.BattleControl_WaitAll, OnWaitAllEntity);
        }

        protected override void OnStart()
        {
            base.OnStart();
            var performance = m_battle.stageManageComponent.EnterFirstStage();
            m_performancePlayer.StartPerformance(performance);
        }

        private void OnBlockPerformance(bool flag)
        {
            m_isBlockPerformance = flag;
            if (!flag)
            {
                actionOnWaitBlockPerformanceList.Clear();
            }
        }

        private void OnPerformanceEnd()
        {
            if (m_isBlockPerformance)
            {
                foreach (var action in actionOnWaitBlockPerformanceList)
                {
                    action.InvokeSafely();
                }
                actionOnWaitBlockPerformanceList.Clear();
            }
        }

        private void OnBlockPerformanceAndWait(Action action)
        {
            m_isBlockPerformance = true;
            actionOnWaitBlockPerformanceList.Add(action);
        }

        private void OnWaitAllEntity(int teamUid)
        {
            m_waitAllTeamUid = teamUid;
        }

        public void SetHostPlayerId(ulong playerId)
        {
            m_hostPlayerId = playerId;
        }


        public override void FormationHandlerSetup(IEntity entity)
        {
            if (m_isSkipping)
            {
                return;
            }
            Entity sampleEntity = (entity as Entity).Copy(BattleShortCut.sampleBattle);
            sampleEntity.GetTeam().AddEntity(sampleEntity, false);
            BattleShortCut.sampleBattle.AddEntity(sampleEntity);
            EventManager.instance.Broadcast(EventID.BattlePrepare_EntityViewAdd, sampleEntity as IEntity);
        }

        public override void FormationHandlerExchange(IEntity srcEntity, IEntity destEntity, GridPosition srcPosition, GridPosition destPosition)
        {
            if (m_isSkipping)
            {
                return;
            }
            BattleShortCut.sampleBattle.GetEntityByUid(srcEntity.uid).SetLocation(destPosition);
            BattleShortCut.sampleBattle.GetEntityByUid(destEntity.uid).SetLocation(srcPosition);
            EventManager.instance.Broadcast(EventID.BattlePrepare_EntityViewExchange, srcEntity, destEntity, srcPosition, destPosition);
        }

        public override void FormationHandlerMove(IEntity entity, GridPosition grid)
        {
            if (m_isSkipping)
            {
                return;
            }
            BattleShortCut.sampleBattle.GetEntityByUid(entity.uid).SetLocation(grid);
            EventManager.instance.Broadcast(EventID.BattlePrepare_EntityViewMove, entity, grid);
        }

        public override void FormationHandlerRemove(int entityUid)
        {
            if (m_isSkipping)
            {
                return;
            }
            BattleShortCut.sampleBattle.DestroyEntityById(entityUid);
            EventManager.instance.Broadcast(EventID.BattlePrepare_EntityViewRemove, entityUid);
        }

        public override void BattleFormationChanged(int teamUid, int formationId, int formationEntityUid)
        {
            if (m_isSkipping)
            {
                return;
            }
            var team = BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
            team.formationId = formationId;
            team.formationEntityUid = formationEntityUid;
            EventManager.instance.Broadcast(EventID.BattlePrepare_BattleFormationChanged, teamUid);
        }

        public override void ChangeTeamAutoState(int teamUid, bool isAuto)
        {
            var team = BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
            if (team != null)
            {
                team.controlledPlayer.isAuto = isAuto;
                EventManager.instance.Broadcast(EventID.BattleTeam_AutoState_Changed, teamUid, isAuto);
            }
        }

        public override void ChangeTeamDecisionId(int teamUid, int decisionIndex)
        {
            var team = BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
            if (team != null)
            {
                //TODO:先这么处理，以后区分玩家自定义和原有的
                team.decisionIndex = decisionIndex + 2;
                EventManager.instance.Broadcast(EventID.BattleTeam_DecisionId_Changed, teamUid, decisionIndex);
            }
        }

        public override void SetTeamDecisionMark(int teamUid, TeamDecisionMarkId markId, GridPosition pos, int entityUid)
        {
            var team = BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
            if (team != null)
            {
                team.AddDecisionMark(markId, pos, entityUid);
                EventManager.instance.Broadcast(EventID.BattleTeam_DecisionMark_Added, teamUid, markId);
            }
        }

        public override void CancelTeamDecisionMark(int teamUid, TeamDecisionMarkId markId)
        {
            var team = BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
            if (team != null)
            {
                team.RemoveMark(markId);
                EventManager.instance.Broadcast(EventID.BattleTeam_DecisionMark_Removed, teamUid, markId);
            }
        }

        public abstract void StartBattleByRecord(BattleRecordMsg battleRecord, bool skipPerformance);
        public abstract void RefreshBattleByFrameCutList(List<FrameCut> frameCutList);
        public abstract void RestartBattle();
        public abstract List<BattleRetractTurnInfo> GetTurnInfoList();

    }
}
