using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.UI;

namespace Phoenix.Battle
{
    public class BattleExecuterClientLocal : BattleExecuterClient
    {
        private Queue<FrameCut> m_frameCutQueue = new Queue<FrameCut>();
        private List<BattleRetractTurnInfo> m_retractTurnInfoList = new List<BattleRetractTurnInfo>();

        protected override void OnUnInit()
        {
            m_frameCutQueue.Clear();
            m_retractTurnInfoList.Clear();
            base.OnUnInit();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            if (!m_performancePlayer.isPerformanceEnd)
            {
                m_performancePlayer.Tick(timeSlice);
            }
            else if (m_isBlockPerformance)
            {

            }
            else if (m_battle.IsWaitInputCommand())
            {
                if (m_frameCutQueue.TryPeek(out FrameCut cut))
                {
                    var errorCode = m_battle.CheckFrameCut(cut);
                    if (errorCode == BattleErrorCode.Ok)
                    {
                        m_frameCutQueue.Dequeue();
                        m_battle.ExecuteFrameCut(cut);
                        var performance = m_battle.GetPerformance();
                        if (performance != null)
                        {
                            m_performancePlayer.StartPerformance(performance);
                            AddRetractByPerformance(performance);
                        }
                    }
                }
                else
                {
                    var waitInputCmd = m_battle.GetWaitInputCommandTypeList().GetValueSafely(0);
                    if (waitInputCmd == FrameCommandType.DialogSelect)
                    {

                    }
                    else
                    {
                        TickAI();
                    }
                }
            }
            else if (m_battle.IsWaitPrepareEnd())
            {
                if (m_frameCutQueue.TryPeek(out FrameCut cut))
                {
                    var errorCode = m_battle.CheckFrameCut(cut);
                    if (errorCode == BattleErrorCode.Ok)
                    {
                        m_frameCutQueue.Dequeue();
                        m_battle.ExecuteFrameCut(cut);
                        var performance = m_battle.GetPerformance();
                        if (performance != null)
                        {
                            m_performancePlayer.StartPerformance(performance);
                            AddRetractByPerformance(performance);
                        }
                    }
                }
            }
            else
            {
                TickBattleLogic();
            }
        }

        private void TickBattleLogic()
        {
            var performance = m_battle.TickLogic();
            if (performance != null)
            {
                m_performancePlayer.StartPerformance(performance);
                AddRetractByPerformance(performance);
                if (performance.performanceType == BattlePerformanceType.TeamStart)
                {
                    m_waitAllTeamUid = 0;
                }
            }
        }

        public override void Send(FrameCommandWrap cmdWrap)
        {
            var cut = CreateFrameCut(cmdWrap.cmd, cmdWrap.playerId);
            if (m_battle.CheckFrameCommandNeedWaitPerformance(cmdWrap.cmd.commandType))
            {
                m_frameCutQueue.Enqueue(cut);
            }
            else
            {
                m_battle.ExecuteFrameCut(cut);
            }
        }

        public override void StartBattleByRecord(BattleRecordMsg battleRecord, bool skipPerformance)
        {
            var cutList = FrameSyncConvertUtility.ConvertToFrameCutList(battleRecord.frameCutList);
#if true
            TickLogicToLastFrame(cutList);
            RefreshSampleBattle();
#else
            TickLogicToFirstFrameOrFormation();
            RefreshSampleBattle(() =>
            {
                foreach (var cut in cutList)
                {
                    m_frameCutQueue.Enqueue(cut);
                }
            });
#endif
        }

        public override void RestartBattle()
        {
            Reset();

            m_battle = CreateBattleAndInit(BattleInstanceType.Origin);

            TickLogicToFirstFrameOrFormation();
            RefreshSampleBattle();
        }

        public override void RefreshBattleByFrameCutList(List<FrameCut> frameCutList)
        {
            Reset();
            m_battle = CreateBattleAndInit(BattleInstanceType.Origin);
            TickLogicToLastFrame(frameCutList);

            m_sampleBattle = CreateBattle(BattleInstanceType.ShowCopy);
            m_sampleBattle.CopyFrom(m_battle, CreatePoolMap());
            BattleStageInitializer stageInitializer = new BattleStageInitializer();
            stageInitializer.Init(m_sampleBattle.battleInfo, m_sampleBattle.GetCurStageIndex());
            stageInitializer.StartSync();
            PostRefreshAfterSkipPerformance();
            EventManager.instance.Broadcast(EventID.BattlePerformance_RefreshBattleAfterSkip);
        }

        public void TickLogicToLastFrame(List<FrameCut> frameCutList)
        {
            m_isSkipping = true;
            var performance = m_battle.stageManageComponent.EnterFirstStage();
            performance.Release();
            TickLogicToWaitInputCommandAndAddRetract();
            for (int i = 0; i < frameCutList.Count; ++i)
            {
                var cut = frameCutList[i];
                var errorCode = m_battle.CheckFrameCut(cut);
                if (errorCode != BattleErrorCode.Ok)
                {
                    throw new Exception("[BattleExecuterCheck] Error Occured:" + errorCode);
                }
                m_battle.ExecuteFrameCut(cut);
                performance = m_battle.GetPerformance();
                AddRetractByPerformance(performance);
                performance.Release();
                TickLogicToWaitInputCommandAndAddRetract();
            }
            m_isSkipping = false;
        }

        public void TickLogicToFirstFrameOrFormation()
        {
            m_isSkipping = true;
            var performance = m_battle.stageManageComponent.EnterFirstStage();
            performance.Release();
            TickLogicToWaitInputCommandAndAddRetract_Restart();
            m_isSkipping = false;
        }

        private void RefreshSampleBattle(Action actionOnEnd = null)
        {
            if (m_sampleBattle != null)
            {
                m_sampleBattle.UnInit();
            }
            m_sampleBattle = CreateBattle(BattleInstanceType.ShowCopy);
            m_sampleBattle.CopyFrom(m_battle, CreatePoolMap());
            Loading.Open(LoadingFlagId.BattleStageInitializer, LoadingViewType.FadeBlack);
            BattleStageInitializer stageInitializer = new BattleStageInitializer();
            stageInitializer.Init(m_sampleBattle.battleInfo, m_sampleBattle.GetCurStageIndex());
            stageInitializer.StartAsync((i) =>
            {
                PostRefreshAfterSkipPerformance();
                PostRefreshOpModeAfterSkipPerformance();
                BattlePerformanceUtility.RefreshBgm();
                EventManager.instance.Broadcast(EventID.BattlePerformance_RefreshBattleAfterSkip);
                Loading.Close(LoadingFlagId.BattleStageInitializer);
                actionOnEnd?.Invoke();
            });
        }

        private void Reset()
        {
            m_isBlockPerformance = false;
            m_waitAllTeamUid = default;
            m_performancePlayer.Clear();
            EntityViewManager.instance.DestroyAllEntityView();
            m_stateMachine.ExitCurState();
            m_stateMachine.Reset();
            m_frameCutQueue.Clear();
            m_retractTurnInfoList.Clear();
            if (m_battle != null)
            {
                m_battle.UnInit();
            }
        }

        private void TickLogicToWaitInputCommandAndAddRetract()
        {
            bool Once()
            {
                if (m_battle.IsWaitInputCommand() || m_battle.IsWaitPrepareEnd() || m_battle.IsBattleEnd())
                {
                    return false;
                }
                var performance = m_battle.TickLogic();
                AddRetractByPerformance(performance);
                performance.Release();
                return true;
            }

            LoopSafely(Once);
        }

        private void TickLogicToWaitInputCommandAndAddRetract_Restart()
        {
            bool Once()
            {
                if (m_battle.IsWaitInputCommand() || m_battle.IsWaitPrepareEnd() || m_battle.IsBattleEnd())
                {
                    return false;
                }
                var performance = m_battle.TickLogic();
                bool result = true;
                if (performance != null)
                {
                    if (performance.performanceType == BattlePerformanceType.TurnStart)
                    {
                        AddRetractTurnInfo((performance as BattlePerformanceTurnStart).turnIndex, m_battle.GetCurStageIndex());
                    }
                    else if (performance.performanceType == BattlePerformanceType.FormationStart)
                    {
                        result = false;
                    }
                    else if (performance.performanceType == BattlePerformanceType.FormationEnd)
                    {
                        result = false;
                    }
                }
                performance.Release();
                return result;
            }

            LoopSafely(Once);
        }

        private void PostRefreshAfterSkipPerformance()
        {
            if (m_sampleBattle.CheckPrepareState())
            {
                m_stateMachine.ChangeState((int)BattleStateId.Prepare);
            }
            else
            {
                m_stateMachine.ChangeState((int)BattleStateId.Main);
                var entityView = BattlePerformanceUtility.GetFocusEntityView();
                if (entityView != null)
                {
                    BattleShortCut.battleScene.sceneCameraHandler.SetCameraGridPositionWithSpeed(entityView.GetLocatedPosition(), 50, true);
                    EventManager.instance.Broadcast(EventID.BattlePerformance_StepStart_SelectEntityEnd, entityView.uid);
                    BattleOpModeManager.instance.EventOnGridClick(entityView.GetLocatedPosition());
                }
            }
        }

        private void PostRefreshOpModeAfterSkipPerformance()
        {
            if (m_sampleBattle.CheckPrepareState())
            {
                BattleOpModeManager.instance.ChangeMode(BattleOpModeId.PrepareOpMode);
            }
            else
            {
                BattleOpModeManager.instance.ChangeMode(BattleOpModeId.BattleOpMode);
            }
        }

        private void AddRetractByPerformance(BattlePerformance performance)
        {
            if (performance != null)
            {
                if (performance.performanceType == BattlePerformanceType.EntityCastSkill)
                {
                    var result = (performance as BattlePerformanceEntityCastSkill).procedureResult;
                    AddRetractStepInfo(battle.GetFrameCutCount() - 1, result.teamUid);
                }
                else if (performance.performanceType == BattlePerformanceType.EntityWait)
                {
                    var result = (performance as BattlePerformanceEntityWait).procedureResult;
                    AddRetractStepInfo(battle.GetFrameCutCount() - 1, result.teamUid);
                }
                else if (performance.performanceType == BattlePerformanceType.TurnStart)
                {
                    AddRetractTurnInfo((performance as BattlePerformanceTurnStart).turnIndex, battle.GetCurStageIndex());
                }
            }
        }

        public override List<BattleRetractTurnInfo> GetTurnInfoList()
        {
            return m_retractTurnInfoList;
        }

        private void AddRetractTurnInfo(int turnIndex, int stageIndex)
        {
            var turnInfo = new BattleRetractTurnInfo();
            turnInfo.turnIndex = turnIndex;
            turnInfo.stageIndex = stageIndex;
            m_retractTurnInfoList.Add(turnInfo);
        }

        private void AddRetractStepInfo(int cmdIndex, int teamUid)
        {
            var turnInfo = m_retractTurnInfoList.GetLastValueSafely();
            var stepInfo = new BattleRetractStepInfo();
            stepInfo.cmdIndex = cmdIndex;
            stepInfo.stepIndex = turnInfo.stepList.Count;
            stepInfo.teamUid = teamUid;
            turnInfo.stepList.Add(stepInfo);
        }
    }
}