using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.Battle
{
    public class BattleLogger : ILogger
    {
        public void Log(string str)
        {
            UnityEngine.Debug.Log(str);
        }

        public void Log<T1>(string str, T1 p1)
        {
            UnityEngine.Debug.Log(string.Format(str, p1));
        }

        public void Log<T1, T2>(string str, T1 p1, T2 p2)
        {
            UnityEngine.Debug.Log(string.Format(str, p1, p2));
        }

        public void Log<T1, T2, T3>(string str, T1 p1, T2 p2, T3 p3)
        {
            UnityEngine.Debug.Log(string.Format(str, p1, p2, p3));
        }

        public void Log<T1, T2, T3, T4>(string str, T1 p1, T2 p2, T3 p3, T4 p4)
        {
            UnityEngine.Debug.Log(string.Format(str, p1, p2, p3, p4));
        }

        public void Log<T1, T2, T3, T4, T5>(string str, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5)
        {
            UnityEngine.Debug.Log(string.Format(str, p1, p2, p3, p4, p5));
        }

        public void LogWarning(string str)
        {
            UnityEngine.Debug.LogWarning(str);
        }

        public void LogWarning<T1>(string str, T1 p1)
        {
            UnityEngine.Debug.LogWarning(string.Format(str, p1));
        }

        public void LogWarning<T1, T2>(string str, T1 p1, T2 p2)
        {
            UnityEngine.Debug.LogWarning(string.Format(str, p1, p2));
        }

        public void LogWarning<T1, T2, T3>(string str, T1 p1, T2 p2, T3 p3)
        {
            UnityEngine.Debug.LogWarning(string.Format(str, p1, p2, p3));
        }

        public void LogWarning<T1, T2, T3, T4>(string str, T1 p1, T2 p2, T3 p3, T4 p4)
        {
            UnityEngine.Debug.LogWarning(string.Format(str, p1, p2, p3, p4));
        }

        public void LogWarning<T1, T2, T3, T4, T5>(string str, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5)
        {
            UnityEngine.Debug.LogWarning(string.Format(str, p1, p2, p3, p4, p5));
        }

        public void LogError(string str)
        {
            UnityEngine.Debug.LogError(str);
        }

        public void LogError<T1>(string str, T1 p1)
        {
            UnityEngine.Debug.LogError(string.Format(str, p1));
        }

        public void LogError<T1, T2>(string str, T1 p1, T2 p2)
        {
            UnityEngine.Debug.LogError(string.Format(str, p1, p2));
        }

        public void LogError<T1, T2, T3>(string str, T1 p1, T2 p2, T3 p3)
        {
            UnityEngine.Debug.LogError(string.Format(str, p1, p2, p3));
        }

        public void LogError<T1, T2, T3, T4>(string str, T1 p1, T2 p2, T3 p3, T4 p4)
        {
            UnityEngine.Debug.LogError(string.Format(str, p1, p2, p3, p4));
        }

        public void LogError<T1, T2, T3, T4, T5>(string str, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5)
        {
            UnityEngine.Debug.LogError(string.Format(str, p1, p2, p3, p4, p5));
        }

        public void LogShout(string str)
        {
            DebugUI.Log(str);
        }

        public void LogShout<T1>(string str, T1 p1)
        {
            DebugUI.Log(string.Format(str, p1));
        }

        public void LogShout<T1, T2>(string str, T1 p1, T2 p2)
        {
            DebugUI.Log(string.Format(str, p1, p2));
        }

        public void LogShout<T1, T2, T3>(string str, T1 p1, T2 p2, T3 p3)
        {
            DebugUI.Log(string.Format(str, p1, p2, p3));
        }

        public void LogShout<T1, T2, T3, T4>(string str, T1 p1, T2 p2, T3 p3, T4 p4)
        {
            DebugUI.Log(string.Format(str, p1, p2, p3, p4));
        }

        public void LogShout<T1, T2, T3, T4, T5>(string str, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5)
        {
            DebugUI.Log(string.Format(str, p1, p2, p3, p4, p5));
        }
    }
}
