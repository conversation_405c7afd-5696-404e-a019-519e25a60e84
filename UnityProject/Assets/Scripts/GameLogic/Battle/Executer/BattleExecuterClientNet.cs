using Phoenix.MsgPackLogic.Protocol;
using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Common.Network;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.Battle
{
    public class BattleExecuterClientNet : BattleExecuterClient
    {
        public Action<FrameCommandWrap> actionOnSendFrameCmdWrap;
        private FrameCutReceiveQueue m_frameCutQueue = new FrameCutReceiveQueue();

        protected override void OnInit()
        {
            base.OnInit();
            NetworkClient.instance.RegisterCallback<MsgPack_BattleCommand_Ntf>(OnBattleCommandNtf);
            NetworkClient.instance.RegisterCallback<MsgPack_DuelBattleSessionEnd_Ntf>(OnBattleSessionEndNtf);
            
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            NetworkClient.instance.UnRegisterCallback<MsgPack_BattleCommand_Ntf>();
            NetworkClient.instance.UnRegisterCallback<MsgPack_DuelBattleSessionEnd_Ntf>();

        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            if (!m_performancePlayer.isPerformanceEnd)
            {
                m_performancePlayer.Tick(timeSlice);
            }
            else if (m_isBlockPerformance)
            {

            }
            else if (m_battle.IsWaitInputCommand() || m_battle.IsWaitPrepareEnd())
            {
                if (m_frameCutQueue.TryDequeue(out FrameCut cut))
                {
                    if (m_battle.CheckFrameCut(cut) == BattleErrorCode.Ok)
                    {
                        m_battle.ExecuteFrameCut(cut);
                        var performance = m_battle.GetPerformance();
                        if (performance != null)
                        {
                            m_performancePlayer.StartPerformance(performance);
                        }
                    }
                }
            }
            else
            {
                var performance = m_battle.TickLogic();
                if (performance != null)
                {
                    m_performancePlayer.StartPerformance(performance);
                    if (performance.performanceType == BattlePerformanceType.TeamStart)
                    {
                        m_waitAllTeamUid = 0;
                    }
                }
            }
        }

        public override async void Send(FrameCommandWrap cmdWrap)
        {
            if (actionOnSendFrameCmdWrap != null)
            {
                actionOnSendFrameCmdWrap(cmdWrap);
                return;
            }
            var sendMsg = new MsgPack_BattleCommand_Req();
            sendMsg.Command = FrameSyncConvertUtility.ConvertToBuffer(cmdWrap);
            sendMsg.Init();
            NetworkTaskBase netTask = new NetworkTaskBase(sendMsg);
            await netTask.SendAndReceiveAsync();
            var ackMsg = netTask.ResponseMsg as MsgPack_BattleCommand_Ack;
            if (ackMsg != null)
            {
                var errorCode = (BattleErrorCode)ackMsg.ErrCode;
                if (errorCode != BattleErrorCode.Ok)
                {
                    GameLogic.UI.TipUI.ShowTip(errorCode.ToString());
                }
            }
        }

        public void Receive(FrameCut cut)
        {
            m_frameCutQueue.Enqueue(cut);
        }

        private void OnBattleCommandNtf(MsgPackStructBase baseMsg)
        {
            var ntf = baseMsg as MsgPack_BattleCommand_Ntf;
            var cut = FrameSyncConvertUtility.ConvertToFrameCut(ntf.Command);
            m_frameCutQueue.Enqueue(cut);
        }

        private void OnBattleSessionEndNtf(MsgPackStructBase baseMsg)
        {
            var ntf = baseMsg as MsgPack_DuelBattleSessionEnd_Ntf;
            DebugUtility.Log(string.Format("OnBattleSessionEndNtf:{0}", ntf.WinnerUid));
        }

        public override void StartBattleByRecord(BattleRecordMsg battleRecord, bool skipPerformance)
        {
            throw new NotImplementedException();
        }

        public override void RefreshBattleByFrameCutList(List<FrameCut> frameCutList)
        {
            throw new NotImplementedException();
        }

        public override void RestartBattle()
        {
            throw new NotImplementedException();
        }

        public override List<BattleRetractTurnInfo> GetTurnInfoList()
        {
            throw new NotImplementedException();
        }
    }
}