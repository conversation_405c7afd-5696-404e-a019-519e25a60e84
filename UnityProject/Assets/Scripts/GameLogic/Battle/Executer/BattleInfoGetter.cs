using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleInfoGetter : BattleInfoGetterBase
    {
        protected override BattleGroundStaticData GetGroundStaticData(string path)
        {
            TextAsset textAsset = ResourceHandleManager.instance.GetResource<TextAsset>(path);
            if (textAsset == null)
            {
                return null;
            }
            return BattleGroundStaticData.Parser.ParseFrom(textAsset.bytes);
        }

        protected override ActorConfigDataAnalyzer GetActorInfoInternal_Player(ulong playerId, int actorRid)
        {
            return null;
        }

        protected override BattleTeamDecisionInfo GetTeamDecisionInfoInternal_Player(ulong playerId, int id)
        {
            return null;
        }
    }
}
