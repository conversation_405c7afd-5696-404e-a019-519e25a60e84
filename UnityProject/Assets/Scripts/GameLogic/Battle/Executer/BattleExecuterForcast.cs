using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleExecuterForcast : BattleExecuter
    {
        public SkillForcastResult ForcastSkill(IBattle battle, IEntity entity, GridPosition movePos, int skillUid, TargetSelectParamContainer paramContainer)
        {
            BattlePerformance performance;
            performance = m_battle.stageManageComponent.EnterFirstStage();
            performance.Release();
            TickLogicToWaitInputCommand();
            var cutList = battle.GetFrameCutList();
            for (int i = 0; i < cutList.Count; ++i)
            {
                var cut = cutList[i];
                var errorCode = m_battle.CheckFrameCut(cut);
                if (errorCode != BattleErrorCode.Ok)
                {
                    throw new Exception("[BattleExecuterCheck] Error Occured:" + errorCode);
                }
                m_battle.ExecuteFrameCut(cut);
                performance = m_battle.GetPerformance();
                performance.Release();
                TickLogicToWaitInputCommand();
            }

            SkillForcastResult result = entity.FetchObj<SkillForcastResult>();
            if (entity != null)
            {
                List<int> effectedEntityUidList = new List<int>();
                var playerId = entity.GetTeam().controlledPlayer.playerId;
                var cut = CreateCutForcast(entity, movePos, skillUid, paramContainer);

                if (m_battle.CheckFrameCut(cut) == BattleErrorCode.Ok)
                {
                    m_battle.disableRandom = true;
                    m_battle.ExecuteFrameCut(cut);
                    performance = m_battle.GetPerformance();
                    if (performance.performanceType == BattlePerformanceType.EntityCastSkill)
                    {
                        var procedureResult = (performance as BattlePerformanceEntityCastSkill).procedureResult;
                        for (int i = 0; i < procedureResult.subResultList.Count; ++i)
                        {
                            BattleActionResult actionResult = procedureResult.subResultList[i];
                            if (actionResult is SkillEngageResult)
                            {
                                AddResult(actionResult as SkillEngageResult, result, effectedEntityUidList);
                            }
                            else if (actionResult is TerrainTriggerResult)
                            {
                                AddResult(actionResult as TerrainTriggerResult, result, effectedEntityUidList);
                            }
                            else if (actionResult is BuffTriggerResult)
                            {
                                AddResult(actionResult as BuffTriggerResult, result, effectedEntityUidList);
                            }
                        }
                    }
                    performance.Release();
                    foreach (var effectedEntityUid in effectedEntityUidList)
                    {
                        var entityResult = ClassPoolManager.instance.Fetch<SkillEffectForcastEntityResult>();
                        entityResult.entityUid = effectedEntityUid;
                        var effectedEntity = m_battle.GetEntityByUid(effectedEntityUid);
                        if (effectedEntity == null)
                        {
                            entityResult.destroyed = true;
                        }
                        else
                        {
                            entityResult.destroyed = false;
                            entityResult.curHp = effectedEntity.GetCurHp();
                            entityResult.curHpRate = effectedEntity.GetCurHpRate();
                        }
                        result.effectEntityResultList.Add(entityResult);
                    }
                }
            }
            return result;
        }

        private FrameCut CreateCutForcast(IEntity entity, GridPosition movePos, int skillUid, TargetSelectParamContainer paramContainer)
        {
            var cmd = new EntityCastSkillFrameCommand();
            cmd.entityUid = entity.uid;
            cmd.skillUid = skillUid;
            cmd.movePosX = (byte)movePos.x;
            cmd.movePosY = (byte)movePos.y;
            cmd.paramList.Clear();
            paramContainer.ToList(cmd.paramList);

            FrameCut cut = new FrameCut();
            cut.cmd = cmd;
            return cut;
        }

        private void AddResult(SkillEngageResult result, SkillForcastResult forcastResult, List<int> effectedEntityUidList)
        {
            foreach (var castResult in result.skillCastResultList)
            {
                AddResult(castResult, forcastResult, effectedEntityUidList);
            }
        }

        private void AddResult(TerrainTriggerResult result, SkillForcastResult forcastResult, List<int> effectedEntityUidList)
        {
            if (result.triggerResult != null)
            {
                foreach (var triggerResult in result.triggerResult.effectResultList)
                {
                    AddResult(triggerResult, forcastResult, effectedEntityUidList);
                }
            }
        }

        private void AddResult(SkillBaseResult result, SkillForcastResult forcastResult, List<int> effectedEntityUidList)
        {
            foreach (var effectResult in result.effectResultList)
            {
                AddResult(effectResult, forcastResult, effectedEntityUidList);
            }
        }

        private void AddResult(BattleActionEffectResult result, SkillForcastResult forcastResult, List<int> effectedEntityUidList)
        {
            foreach (var clip in result.clipList)
            {
                if (clip.effectType == BattleActionEffectType.DamageEntity)
                {
                    var c = clip as BattleActionEffectDamageEntityResultClip;
                    var r = ClassPoolManager.instance.Fetch<SkillEffectForcastDamageEntityResult>();
                    r.entityUid = c.entityUid;
                    r.damage = c.expectDamage;
                    effectedEntityUidList.AddNotContains(c.entityUid);
                    forcastResult.effectResultList.Add(r);
                }
                else if (clip.effectType == BattleActionEffectType.HealEntity)
                {
                    var c = clip as BattleActionEffectHealEntityResultClip;
                    var r = ClassPoolManager.instance.Fetch<SkillEffectForcastHealEntityResult>();
                    r.entityUid = c.entityUid;
                    r.heal = c.expectHeal;
                    effectedEntityUidList.AddNotContains(c.entityUid);
                    forcastResult.effectResultList.Add(r);
                }
                else if (clip.effectType == BattleActionEffectType.MoveEntity)
                {
                    var c = clip as BattleActionEffectMoveEntityResultClip;
                    foreach (var er in c.effectResultList)
                    {
                        AddResult(er, forcastResult, effectedEntityUidList);
                    }
                }
            }
        }

        public override void BattleFormationChanged(int teamUid, int formationId, int formationEntityUid) { }
        public override void CancelTeamDecisionMark(int teamUid, TeamDecisionMarkId markId) { }
        public override void ChangeTeamAutoState(int teamUid, bool isAuto) { }
        public override void ChangeTeamDecisionId(int teamUid, int teamDecisionIndex) { }
        public override void FormationHandlerExchange(IEntity srcEntity, IEntity destEntity, GridPosition srcPosition, GridPosition destPosition) { }
        public override void FormationHandlerMove(IEntity entity, GridPosition grid) { }
        public override void FormationHandlerRemove(int entityUid) { }
        public override void FormationHandlerSetup(IEntity entity) { }
        public override void Send(FrameCommandWrap cmdWrap) { }
        public override void SetTeamDecisionMark(int teamUid, TeamDecisionMarkId markId, GridPosition pos, int entityUid) { }
    }
}
