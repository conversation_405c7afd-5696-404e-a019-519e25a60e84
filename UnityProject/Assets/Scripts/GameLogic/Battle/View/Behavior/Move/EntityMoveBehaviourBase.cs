using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;

namespace Phoenix.GameLogic
{
    public class EntityMoveBehaviourBase : EntityBehaviourBase
    {
        private delegate bool DelegateTick(Vector3 gridPos, EDir dir, float time, out Vector3 curPos, out Vector3 curDir, out float leftTime);

        private Action m_actionOnEnd;
        private float m_gridSize;
        private float m_gridHalfSize;
        private float m_moveSpeed;
        private List<MoveTickTask> m_taskList = new List<MoveTickTask>();
        private bool m_isStart;
        private bool m_isMoveBack;
        private int m_curIndex;

        protected override void OnInit()
        {
            base.OnInit();
            m_moveSpeed = BattleParamSetting.instance.entityMoveSpeed;
            m_gridSize = BattleParamSetting.instance.gridSize;
            m_gridHalfSize = m_gridSize / 2f;
            TickManager.instance.RegisterGlobalTick((int)TickGlobalId.EntityMoveBehaviorTick, OnGlobalTick);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_actionOnEnd = null;
            m_taskList.ReleaseAll();
            TickManager.instance.UnRegisterGlobalTick((int)TickGlobalId.EntityMoveBehaviorTick, OnGlobalTick);
        }

        public void StartMoveGridPosList(List<GridPosition> gridPosList, bool isMoveBack, Action actionOnEnd)
        {
            m_actionOnEnd = actionOnEnd;
            m_isStart = true;
            m_isMoveBack = isMoveBack;
            m_curIndex = 0;
            if (gridPosList.Count > 1)
            {
                for (int i = 0; i < gridPosList.Count; ++i)
                {
                    GridPosition curGridPos;
                    EDir dir;
                    DelegateTick delegateTick;
                    GetTaskInfo(gridPosList, i, out curGridPos, out dir, out delegateTick);
                    if (dir == EDir.None || delegateTick == null)
                    {
                        FinishMove();
                        return;
                    }
                    MoveTickTask info = ClassPoolManager.instance.Fetch<MoveTickTask>();
                    info.dir = dir;
                    info.actionOnTick = delegateTick;
                    info.gridPos = curGridPos;
                    info.gridCenterPos = GetPositionByGridPos(curGridPos);
                    info.curTime = 0f;
                    m_taskList.Add(info);
                }
            }
            StartPlayMove();
        }

        private void FinishMove()
        {
            StopPlayMove();
            m_isStart = false;
            m_taskList.ReleaseAll();
            Action temp = m_actionOnEnd;
            m_actionOnEnd = null;
            temp.InvokeSafely();
            EventManager.instance.Broadcast(EventID.EntityView_OnMoveSectionEnd);
        }

        private void StartPlayMove()
        {
            var playMoveBehaviour = m_entityView.GetBehaviour<EntityPlayMoveBehaviour>();
            if (playMoveBehaviour != null)
            {
                if (m_isMoveBack)
                {
                    playMoveBehaviour.PlayMoveBack(false);
                }
                else
                {
                    playMoveBehaviour.PlayMove(false);
                }
            }
        }

        private void StopPlayMove()
        {
            var playMoveBehaviour = m_entityView.GetBehaviour<EntityPlayMoveBehaviour>();
            if (playMoveBehaviour != null)
            {
                if (m_isMoveBack)
                {
                    playMoveBehaviour.StopMoveBack();
                }
            }
            var playIdleBehaviour = m_entityView.GetBehaviour<EntityPlayIdleBehaviour>();
            if (playIdleBehaviour != null)
            {
                playIdleBehaviour.PlaySceneIdle(false);
            }
        }

        private void OnGlobalTick(TimeSlice timeSlice)
        {
            if (!m_isStart)
            {
                return;
            }
            float leftTime = timeSlice.deltaTime;
            bool hasTicked = false;
            Vector3 curPos = Vector3.zero;
            Vector3 curDir = Vector3.forward;
            while (m_curIndex < m_taskList.Count)
            {
                MoveTickTask tickInfo = m_taskList[m_curIndex];
                if (tickInfo.actionOnTick == null)
                {
                    continue;
                }
                hasTicked = true;
                bool needContinueTick = tickInfo.Tick(leftTime, out curPos, out curDir, out leftTime);
                if (needContinueTick)
                {
                    m_curIndex++;
                }
                else
                {
                    break;
                }
            }
            if (hasTicked)
            {
                SetPosition(curPos);
                if (m_isMoveBack)
                {
                    SetDirection(-curDir);
                }
                else
                {
                    SetDirection(curDir);
                }
            }
            if (m_curIndex >= m_taskList.Count)
            {
                FinishMove();
            }
        }

        private bool TickMoveHalfLineStart(Vector3 gridPos, EDir dir, float time, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            float maxTime = m_gridHalfSize / m_moveSpeed;
            Vector3 moveVecNormalized = GetMoveVecNormalized(dir);
            Vector3 startPos = gridPos;
            Vector3 endPos = gridPos + moveVecNormalized * m_gridHalfSize;
            return TickMoveLine(startPos, endPos, time, maxTime, out curPos, out curDir, out leftTime);
        }

        private bool TickMoveHalfLineEnd(Vector3 gridPos, EDir dir, float time, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            float maxTime = m_gridHalfSize / m_moveSpeed;
            Vector3 moveVecNormalized = GetMoveVecNormalized(dir);
            Vector3 startPos = gridPos - moveVecNormalized * m_gridHalfSize;
            Vector3 endPos = gridPos;
            return TickMoveLine(startPos, endPos, time, maxTime, out curPos, out curDir, out leftTime);
        }

        private bool TickMoveWholeLine(Vector3 gridPos, EDir dir, float time, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            float maxTime = m_gridSize / m_moveSpeed;
            Vector3 moveVecNormalized = GetMoveVecNormalized(dir);
            Vector3 startPos = gridPos - moveVecNormalized * m_gridHalfSize;
            Vector3 endPos = gridPos + moveVecNormalized * m_gridHalfSize;
            return TickMoveLine(startPos, endPos, time, maxTime, out curPos, out curDir, out leftTime);
        }

        private bool TickMoveClock(Vector3 gridPos, EDir dir, float time, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            float maxTime = 2f * Mathf.PI * m_gridHalfSize / 4f / m_moveSpeed;
            Vector3 centerOffset;
            float startRad;
            float endRad;
            GetClockParam(dir, out centerOffset, out startRad, out endRad);
            return TickMoveCircle(gridPos + centerOffset, startRad, endRad, time, maxTime, true, out curPos, out curDir, out leftTime);
        }

        private bool TickMoveAntiClock(Vector3 gridPos, EDir dir, float time, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            float maxTime = 2f * Mathf.PI * m_gridHalfSize / 4f / m_moveSpeed;
            Vector3 centerOffset;
            float startRad;
            float endRad;
            GetAntiClockParam(dir, out centerOffset, out startRad, out endRad);
            return TickMoveCircle(gridPos + centerOffset, startRad, endRad, time, maxTime, false, out curPos, out curDir, out leftTime);
        }

        private bool TickMoveCircle(Vector3 centerPos, float startRad, float endRad, float time, float maxTime, bool isClock, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            leftTime = time - maxTime;
            float f = Mathf.Clamp01(time / maxTime);
            float rad = Mathf.Lerp(startRad, endRad, f);
            float dirRad = rad;
            if (isClock)
            {
                dirRad -= Mathf.PI / 2f;
            }
            else
            {
                dirRad += Mathf.PI / 2f;
            }
            curPos = centerPos + new Vector3(Mathf.Cos(rad), 0f, Mathf.Sin(rad)) * m_gridHalfSize;
            curDir = new Vector3(Mathf.Cos(dirRad), 0f, Mathf.Sin(dirRad));
            if (leftTime < 0f)
            {
                leftTime = 0f;
                return false;
            }
            return true;
        }

        private bool TickMoveLine(Vector3 startPos, Vector3 endPos, float time, float maxTime, out Vector3 curPos, out Vector3 curDir, out float leftTime)
        {
            leftTime = time - maxTime;
            float f = Mathf.Clamp01(time / maxTime);
            curPos = Vector3.Lerp(startPos, endPos, f);
            curDir = endPos - startPos;
            if (leftTime < 0f)
            {
                leftTime = 0f;
                return false;
            }
            return true;
        }

        private EDir GetDir(GridPosition preGridPos, GridPosition nextGridPos)
        {
            if (preGridPos.x == nextGridPos.x)
            {
                if (nextGridPos.y == preGridPos.y + 1)
                {
                    return EDir.PositiveY;
                }
                if (nextGridPos.y == preGridPos.y - 1)
                {
                    return EDir.NegativeY;
                }
            }
            else if (preGridPos.y == nextGridPos.y)
            {
                if (nextGridPos.x == preGridPos.x + 1)
                {
                    return EDir.PositiveX;
                }
                if (nextGridPos.x == preGridPos.x - 1)
                {
                    return EDir.NegativeX;
                }
            }
            return EDir.None;
        }

        private Vector3 GetMoveVecNormalized(EDir dir)
        {
            Vector3 moveVecNormalized = Vector3.zero;
            bool isX = (int)dir % 2 == 0;
            bool isPositive = (int)dir / 2 == 0;
            if (isX)
            {
                moveVecNormalized.x = isPositive ? 1 : -1;
            }
            else
            {
                moveVecNormalized.z = isPositive ? 1 : -1;
            }
            return moveVecNormalized;
        }

        private void GetClockParam(EDir dir, out Vector3 centerOffset, out float startRad, out float endRad)
        {
            centerOffset = Vector3.zero;
            startRad = 0f;
            endRad = 0f;
            switch (dir)
            {
                case EDir.PositiveX:
                    centerOffset = new Vector3(-m_gridSize, 0f, -m_gridSize);
                    startRad = 0.5f;
                    endRad = 0f;
                    break;
                case EDir.PositiveY:
                    centerOffset = new Vector3(m_gridSize, 0f, -m_gridSize);
                    startRad = 1f;
                    endRad = 0.5f;
                    break;
                case EDir.NegativeX:
                    centerOffset = new Vector3(m_gridSize, 0f, m_gridSize);
                    startRad = 1.5f;
                    endRad = 1f;
                    break;
                case EDir.NegativeY:
                    centerOffset = new Vector3(-m_gridSize, 0f, m_gridSize);
                    startRad = 2f;
                    endRad = 1.5f;
                    break;
            }
            centerOffset /= 2f;
            startRad *= Mathf.PI;
            endRad *= Mathf.PI;
        }

        private void GetAntiClockParam(EDir dir, out Vector3 centerOffset, out float startRad, out float endRad)
        {
            centerOffset = Vector3.zero;
            startRad = 0f;
            endRad = 0f;
            switch (dir)
            {
                case EDir.PositiveX:
                    centerOffset = new Vector3(-m_gridSize, 0f, m_gridSize);
                    startRad = 1.5f;
                    endRad = 2f;
                    break;
                case EDir.PositiveY:
                    centerOffset = new Vector3(-m_gridSize, 0f, -m_gridSize);
                    startRad = 0f;
                    endRad = 0.5f;
                    break;
                case EDir.NegativeX:
                    centerOffset = new Vector3(m_gridSize, 0f, -m_gridSize);
                    startRad = 0.5f;
                    endRad = 1f;
                    break;
                case EDir.NegativeY:
                    centerOffset = new Vector3(m_gridSize, 0f, m_gridSize);
                    startRad = 1f;
                    endRad = 1.5f;
                    break;
            }
            centerOffset /= 2f;
            startRad *= Mathf.PI;
            endRad *= Mathf.PI;
        }

        private void GetTaskInfo(List<GridPosition> gridPosList, int index, out GridPosition curGridPos, out EDir dir, out DelegateTick delegateTick)
        {
            curGridPos = gridPosList[index];
            bool hasPreGridPos = index > 0;
            bool hasNextGridPos = index < gridPosList.Count - 1;
            GridPosition nextGridPos = default;
            GridPosition preGridPos = default;
            if (hasPreGridPos)
            {
                preGridPos = gridPosList[index - 1];
            }
            if (hasNextGridPos)
            {
                nextGridPos = gridPosList[index + 1];
            }
            delegateTick = null;
            dir = EDir.None;
            if (!hasPreGridPos)
            {
                if (hasNextGridPos)
                {
                    dir = GetDir(curGridPos, nextGridPos);
                    delegateTick = TickMoveHalfLineStart;
                }
            }
            else
            {
                if (hasNextGridPos)
                {
                    dir = GetDir(preGridPos, curGridPos);
                    EDir nextDir = GetDir(curGridPos, nextGridPos);
                    if (nextDir == dir)
                    {
                        delegateTick = TickMoveWholeLine;
                    }
                    else if ((int)nextDir == ((int)dir + 1) % (int)EDir.Max)
                    {
                        delegateTick = TickMoveAntiClock;
                    }
                    else if (((int)nextDir + 1) % (int)EDir.Max == (int)dir)
                    {
                        delegateTick = TickMoveClock;
                    }
                }
                else
                {
                    dir = GetDir(preGridPos, curGridPos);
                    delegateTick = TickMoveHalfLineEnd;
                }
            }
        }

        private void SetPosition(Vector3 pos)
        {
            m_entityView.SetWorldPosition(pos);
        }

        private void SetDirection(Vector3 dir)
        {
            m_entityView.SetDirectionImmediately(dir);
        }

        private Vector3 GetPositionByGridPos(GridPosition gridPos)
        {
            Vector3 position = BattleShortCut.battleSceneGridManager.GetGridWorldPosition(gridPos);
            return position;
        }

        private enum EDir
        {
            None = -1,
            PositiveX,
            PositiveY,
            NegativeX,
            NegativeY,

            Max,
        }

        private class MoveTickTask : ClassPoolObj
        {
            public DelegateTick actionOnTick;
            public GridPosition gridPos;
            public Vector3 gridCenterPos;
            public EDir dir;
            public float curTime;

            public bool Tick(float deltaTime, out Vector3 curPos, out Vector3 curDir, out float leftTime)
            {
                if (actionOnTick == null)
                {
                    curPos = Vector3.zero;
                    curDir = Vector3.zero;
                    leftTime = 0f;
                    return false;
                }
                curTime += deltaTime;
                return actionOnTick(gridCenterPos, dir, curTime, out curPos, out curDir, out leftTime);
            }
        }

    }
}
