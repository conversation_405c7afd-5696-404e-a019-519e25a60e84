using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityAnimatorBehaviour : EntityBehaviourBase
    {
        protected AnimationCollection m_animationCollection;
        private bool m_rootMotionEnabled;
        protected AnimatorPlayController m_animatorPlayController = new AnimatorPlayController();
        private PriorityStack<float> m_animSpeedStack = new PriorityStack<float>();

        public float rootMotionRatioXZ = 1f;
        public float rootMotionRatioY = 1f;

        public virtual bool rootMotionEnabled
        {
            get { return m_rootMotionEnabled; }
            set { m_rootMotionEnabled = value; }
        }

        public Vector3 rootMotionDeltaPos
        {
            get { return m_animatorPlayController.rootMotionDeltaPos; }
        }

        public Quaternion rootMotionTargetRotation
        {
            get { return m_animatorPlayController.rootMotionTargetRotation; }
        }

        public virtual float speed
        {
            get { return m_animSpeedStack.topValue; }
        }

        protected override void OnInit()
        {
            m_animSpeedStack.Init((int)EntityAnimatorSpeedPriority.Base, 1f, OnAnimSpeedStackTopChanged);
        }

        protected override void OnUnInit()
        {
            m_rootMotionEnabled = false;
            m_animSpeedStack.RemoveAll();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            m_animatorPlayController.Tick(timeSlice);
        }

        public void InitAnimator(Animator animator)
        {
            m_animatorPlayController.SetAnimator(animator);
            m_animatorPlayController.AddStateGroup(0, AnimatorStateType.Once, "Once", "OnceExtra");
            m_animatorPlayController.AddStateGroup(0, AnimatorStateType.Loop, "Loop", "LoopExtra");
        }

        public void InitAnimationCollection(AnimationCollection animationCollection)
        {
            m_animationCollection = animationCollection;
            if (m_animationCollection != null)
            {
                m_animatorPlayController.SetAnimationClipGetter(m_animationCollection);
            }
        }

        public string GetAnimationPath(string name)
        {
            return m_animationCollection.GetPath(name);
        }

        public Vector3 GetAnimationFinalRootOffset(string name)
        {
            return m_animationCollection.GetFinalRootOffset(name);
        }

        public void PlayAnimationLoop(string name, float fadeTime)
        {
            m_animatorPlayController.Play(AnimatorPlayCommand.Create(AnimatorStateType.Loop, name, fadeTime));
        }

        public void PlayAnimationOnce(string name, float fadeTime, Action<bool> actionOnEnd)
        {
            m_animatorPlayController.Play(AnimatorPlayCommand.Create(AnimatorStateType.Once, name, fadeTime, actionOnEnd));
        }

        public void InterruptAnimation()
        {
            m_animatorPlayController.Play(null);
        }

        public void ResetRootMotionRatio()
        {
            rootMotionRatioXZ = 1f;
            rootMotionRatioY = 1f;
        }

        public void SetSpeed(EntityAnimatorSpeedPriority priority, float value)
        {
            m_animSpeedStack.AddItem((int)priority, value);
        }

        public void ResetSpeed(EntityAnimatorSpeedPriority priority)
        {
            m_animSpeedStack.Remove((int)priority);
        }

        public bool CanPlayAnimation(string name)
        {
            return m_animatorPlayController.CanPlay(name);
        }

        private void OnAnimSpeedStackTopChanged(PriorityStack<float> priorityStack)
        {
            OnRefreshSpeed();
        }

        protected virtual void OnRefreshSpeed()
        {
            m_animatorPlayController.SetSpeed(m_animSpeedStack.topValue);
        }
    }
}
