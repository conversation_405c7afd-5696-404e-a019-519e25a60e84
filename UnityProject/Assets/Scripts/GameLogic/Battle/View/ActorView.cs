using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.Core;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Drama;
using YooAsset;

namespace Phoenix.GameLogic.Battle
{
    public class ActorView : EntityView
    {
        protected ActorConfigData m_configData;
        protected ActorAnimationConfig m_animConfig;
        protected EntitySkinConfigData m_skinConfigData;

        protected EntityAnimatorBehaviour m_animatorBehaviour;
        protected EntitySelectAnnounceBehaviour m_selectAnnounceBehaviour;

        public override EntityType entityType
        {
            get { return EntityType.Actor; }
        }

        public ActorConfigData configData
        {
            get { return m_configData; }
        }

        public ActorAnimationConfig animationConfig
        {
            get { return m_animConfig; }
        }

        public EntitySkinConfigData skinConfigData
        {
            get { return m_skinConfigData; }
        }

        protected override void OnCreate()
        {
            base.OnCreate();
            m_animatorBehaviour = AddBehaviour<EntityAnimatorBehaviour>();
            m_selectAnnounceBehaviour = AddBehaviour<EntitySelectAnnounceBehaviour>();
            AddBehaviour<EntityMoveBehaviourDefault>();
            AddBehaviour<ActorPlayIdleBehaviour>();
            AddBehaviour<ActorPlayMoveBehaviour>();
            AddBehaviour<EntityRotateBehaviour>();
        }

        protected override void OnInit()
        {
            m_configData = ConfigDataManager.instance.GetActor(rid);
            m_skinConfigData = ConfigDataManager.instance.GetEntitySkin(entityType, rid);
            m_animConfig = ResourceHandleManager.instance.GetResource<ActorAnimationConfig>(m_skinConfigData.AnimationConfigPath);
            m_animatorBehaviour.InitAnimator(GetComponentInChildren<Animator>());
            m_animatorBehaviour.InitAnimationCollection(m_animConfig.collection);
            m_dramaCollection = ResourceHandleManager.instance.GetResource<DramaTimelineDataCollection>(m_skinConfigData.DramaCollectionPath);
            base.OnInit();
        }

        protected virtual void OnAnimatorMove()
        {
            if (!m_animatorBehaviour.rootMotionEnabled)
            {
                return;
            }
            Vector3 deltaPos = m_animatorBehaviour.rootMotionDeltaPos * m_animatorBehaviour.rootMotionRatioXZ;
            deltaPos = deltaPos / m_skinConfigData.ModelScale;
            transform.position += deltaPos;
            RaycastHit hit;
            if (Physics.Raycast(transform.position + Vector3.up * 10, Vector3.down, out hit, 100, UnityLayerUtility.GetLayerMask(ELayerMaskType.BattleGroundTouch)))
            {
                transform.position = hit.point;
            }
        }

        public void PlaySelectAnnounce()
        {
            m_selectAnnounceBehaviour.Play();
        }

        public void PlaySound(AnimationEvent animationEvent)
        {

        }

        public void PlayStepSound(AnimationEvent animationEvent)
        {

        }

        public void AddDramaKey(AnimationEvent animationEvent)
        {

        }

        public override float GetModelScale()
        {
            return m_skinConfigData.ModelScale;
        }

        public override float GetParticleScale()
        {
            return m_skinConfigData.ParticleScale;
        }
    }
}
