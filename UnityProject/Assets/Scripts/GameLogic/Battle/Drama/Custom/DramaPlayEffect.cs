using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using UnityEngine;

namespace Phoenix.Drama
{
    public class DramaPlayEffect : DramaBase
    {
        private List<ParticleRuntimeInfo> m_particleRuntimeInfoList = new List<ParticleRuntimeInfo>();

        public override void OnRelease()
        {
            m_particleRuntimeInfoList.Clear();
            base.OnRelease();
        }

        protected override void OnStart()
        {
            DramaPlayEffectData data = m_data as DramaPlayEffectData;
            switch (data.attachType.value)
            {
                case SkillObjectAttachType.SourceEntity:
                    CreateEffect4SourceEntity(data);
                    break;
                case SkillObjectAttachType.TargetEntity:
                    CreateEffect4TargetEntity(data);
                    break;
                case SkillObjectAttachType.TargetGrid:
                    CreateEffect4TargetGrid(data);
                    break;
            }

            if (data.waitEnd && m_particleRuntimeInfoList.Count > 0)
            {
                foreach(var runtimeInfo in m_particleRuntimeInfoList)
                {
                    runtimeInfo.actionOnEnd = OnParticleRuntimeEnd;
                }
            }
            else
            {
                End();
            }
        }

        private void OnParticleRuntimeEnd(ParticleRuntimeInfo info)
        {
            m_particleRuntimeInfoList.Remove(info);
            if(m_particleRuntimeInfoList.Count == 0)
            {
                End();
            }
        }

        private void CreateEffect4SourceEntity(DramaPlayEffectData data)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(m_context.originEntityUid);
            GameObject bindPointObj = entityView.GetBindPointObj(data.bindPointId);
            ParticleRuntimeInfo particleRuntimeInfo = SpawnParticleRuntimeInfo(data.efx.path, data.isFollow, data.useAttachScale, bindPointObj);
            if (particleRuntimeInfo != null)
            {
                m_particleRuntimeInfoList.Add(particleRuntimeInfo);
            }

        }
        private void CreateEffect4TargetEntity(DramaPlayEffectData data)
        {
            var effectResult = m_context.GetEffectResult(data.skillEffectId);
            if (effectResult != null)
            {
                List<int> entityUidList = new List<int>();
                effectResult.CollectEntityUid(entityUidList);
                foreach (var entityUid in entityUidList)
                {
                    EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                    if(entityView == null)
                    {
                        continue;
                    }
                    GameObject bindPointObj = entityView.GetBindPointObj(data.bindPointId);
                    ParticleRuntimeInfo particleRuntimeInfo = SpawnParticleRuntimeInfo(data.efx.path, data.isFollow, data.useAttachScale, bindPointObj);
                    if (particleRuntimeInfo != null)
                    {
                        m_particleRuntimeInfoList.Add(particleRuntimeInfo);
                    }
                }
            }
        }
        private void CreateEffect4TargetGrid(DramaPlayEffectData data)
        {
            ParticleRuntimeInfo particleRuntimeInfo = SpawnParticleRuntimeInfo(data.efx.path, false, false, null);
            if (particleRuntimeInfo != null)
            {
                if (particleRuntimeInfo.go != null)
                {
                    Vector3 position = BattleShortCut.battleSceneGridManager.GetGridWorldPosition(m_context.targetPosition);
                    particleRuntimeInfo.go.transform.position = position;
                    particleRuntimeInfo.go.transform.localScale = Vector3.one;
                }
                m_particleRuntimeInfoList.Add(particleRuntimeInfo);
            }
        }

        /// <summary>
        /// ������ȡ��Ч����
        /// </summary>
        /// <param name="path"></param>
        /// <param name="isFollow"></param>
        /// <param name="useAttachScale"></param>
        /// <param name="parent"></param>
        /// <returns></returns>
        private ParticleRuntimeInfo SpawnParticleRuntimeInfo(String path, bool isFollow, bool useAttachScale, GameObject parent)
        {
            return ParticleManager.instance.Spawn(path, parent, isFollow, useAttachScale, 1f);
        }
    }
}
