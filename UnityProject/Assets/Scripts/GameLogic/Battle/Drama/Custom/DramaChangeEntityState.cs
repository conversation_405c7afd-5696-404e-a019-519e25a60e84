using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Drama
{
    public class DramaChangeEntityState : DramaBase
    {
        protected override void OnStart()
        {
            DramaChangeEntityStateData data = m_data as DramaChangeEntityStateData;
            EntityView entityView = EntityViewManager.instance.GetEntityView(m_context.originEntityUid);
            if(entityView != null)
            {
                if (data.changeActiveState)
                {
                    entityView.SetActiveSafely(data.activeFlag);
                }
            }
            End();
        }
    }
}
