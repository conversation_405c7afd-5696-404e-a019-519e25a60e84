using AK.Wwise;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using YooAsset;

namespace Phoenix.Drama
{
    public class DramaPlaySound : DramaBase
    {
        protected override void OnStart()
        {
            if (m_data is not DramaPlaySoundData dramaPlaySoundData)
            {
                return;
            }

            string wwiseEventReferencePath = dramaPlaySoundData.wwiseEventReference.path;
            if (!string.IsNullOrEmpty(wwiseEventReferencePath))
            {
                AssetHandle handle = ResourceManager.instance.LoadSync<WwiseEventReference>(wwiseEventReferencePath);
                if (handle == null)
                {
                    return;
                }

                var wwiseEventReference = handle.AssetObject as WwiseEventReference;
                if (!wwiseEventReference)
                {
                    return;
                }

                EntityView entityView = EntityViewManager.instance.GetEntityView(m_context.originEntityUid);
                if (entityView && entityView.gameObject)
                {
                    WwiseAudioManager.Instance.PlaySound(wwiseEventReference.DisplayName, entityView.gameObject, "Skill");
                }
            }
            /*
            else
            {
                AudioManager.instance.PlayOnce(dramaPlaySoundData.name);
            }
            */

            End();
        }
    }
}