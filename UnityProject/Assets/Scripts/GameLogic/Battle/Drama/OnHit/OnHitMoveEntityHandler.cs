using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;
using UnityEngine;

namespace Phoenix.Drama
{
    public class OnHitMoveEntityHandler : OnHitHandler
    {
        private StaticLerpVec3 m_moveLerp = new StaticLerpVec3();
        private DramaTimelineProcess m_dramaProcess;

        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Move;
            if(resultClip.resultType == SkillEffectResultType.Immune)
            {
                EventManager.instance.Broadcast(EventID.BattleTextFloat, resultClip.entityUid, "免疫");
                m_isEnd = true;
                return;
            }
            DramaOnHitMoveEntityData detailData = m_dramaData.moveEntityData;
            EntityView entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if(entityView != null)
            {
                m_moveLerp.actionOnTick = OnLerpTick;
                m_moveLerp.actionOnFinish = OnLerpFinish;
                m_moveLerp.Start(entityView.transform.position, BattleShortCut.GetGridWorldPosition(resultClip.movePosition), detailData.moveTime, EBaseLerpFuncType.Linear);
            }
        }

        private void OnLerpTick(AbstractStaticLerp<Vector3> lerp)
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Move;
            EntityView entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (entityView != null)
            {
                entityView.SetWorldPosition(lerp.curValue);
            }
        }

        private void OnLerpFinish(AbstractStaticLerp<Vector3> lerp)
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Move;
            EntityView entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (entityView != null)
            {
                entityView.SetPosition(resultClip.movePosition, true);
            }
            if (resultClip.effectResultList.Count > 0)
            {
                m_dramaProcess = CreateDramaProcess();
                if (!m_dramaProcess.InitAndStartAndCheckRunning())
                {
                    m_isEnd = true;
                }
            }
            else
            {
                m_isEnd = true;
            }
        }

        private DramaTimelineProcess CreateDramaProcess()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Move;
            DramaContext dramaContext = new DramaContext();
            dramaContext.dramaPath = string.Empty;
            dramaContext.originEntityUid = resultClip.entityUid;
            dramaContext.scenePerformance = true;
            dramaContext.simplePerformance = m_dramaContext.simplePerformance;
            dramaContext.InitEffectResultList(resultClip.effectResultList);
            return DramaTimelineProcess.Create(dramaContext);
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            if (m_moveLerp.isStart)
            {
                m_moveLerp.Tick(timeSlice.deltaTime);
            }
            if (m_dramaProcess != null && m_dramaProcess.isStarted)
            {
                m_dramaProcess.Tick(timeSlice);
                if (!m_dramaProcess.isStarted)
                {
                    m_dramaProcess.Release();
                    m_dramaProcess = null;
                    m_isEnd = true;
                }
            }
        }
    }
}
