using System;
using AK.Wwise;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using UnityEngine;

namespace Phoenix.Drama
{
    public class OnHitDamageEntityHandler : OnHitHandler
    {
        public FixedValue damage;
        public FixedValue damageReflect;
        public FixedValue healthSteal;
        public bool needDamageFloat;
        public FixedValue curHp;
        public FixedValue curHpAttacker;
        public float moveLength;
        public bool isFirst;
        public bool isLast;

        private EntityView m_entityView;
        private EntityView m_entityViewAttacker;
        private EntityAnimatorBehaviour m_animBehaviour;
        private StaticLerpVec3 m_lerpVec3 = new StaticLerpVec3();

        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Damage;
            DramaOnHitDamageEntityData detailData = m_dramaData.damageEntityData;
            int entityUid = resultClip.entityUid;
            BuffPerformanceUtility.PerformanceChangeActiveList(resultClip.srcBuffChangeActiveResultList);
            BuffPerformanceUtility.PerformanceChangeActiveList(resultClip.targetBuffChangeActiveResultList);
            BuffPerformanceUtility.PerformanceBuffChange(resultClip.buffChangeResultList);

            m_entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (m_entityView == null)
            {
                DebugUtility.LogError(string.Format("[OnHitDamageEntityHandler] 受击者{0}(uid)不存在", resultClip.entityUid));
                m_isEnd = true;
                return;
            }

            var actorView = m_entityView as ActorView;
            if (actorView == null)
            {
                DebugUtility.LogError(string.Format("[OnHitDamageEntityHandler] 受击者{0}(uid)不是角色，类型是{1}", resultClip.entityUid, m_entityView.entityType.ToString()));
                m_isEnd = true;
                return;
            }

            if (actorView.animationConfig == null)
            {
                m_isEnd = true;
                return;
            }

            HandleOnHitAudio();

            ActorAnimationConfig animationConfig = actorView.animationConfig;
            var animBehaviour = m_entityView.GetBehaviour<EntityAnimatorBehaviour>();
            if (needDamageFloat)
            {
                EventManager.instance.Broadcast(EventID.BattleDamageFloat, resultClip.entityUid, (int) damage);
            }

            if (resultClip.resultType == SkillEffectResultType.Guard)
            {
                if (needDamageFloat)
                {
                    EventManager.instance.Broadcast(EventID.BattleTextFloat, resultClip.entityUid, "格挡");
                }

                m_isEnd = true;
            }
            else if (resultClip.resultType == SkillEffectResultType.Dodge)
            {
                if (needDamageFloat)
                {
                    EventManager.instance.Broadcast(EventID.BattleTextFloat, resultClip.entityUid, "闪避");
                }
            }
            else
            {
                m_entityView.SetCurHp(curHp);
                EventManager.instance.Broadcast(EventID.Entity_CurHp_Changed, resultClip.entityUid);
            }

            if (m_dramaContext.scenePerformance)
            {
                animBehaviour.rootMotionEnabled = false;
                AttachOnHitEfx(m_entityView);
                if (isLast && resultClip.isDead)
                {
                    animBehaviour.PlayAnimationOnce(animationConfig.sceneDie, 0f, OnDeadAnimEnd);
                }
                else
                {
                    animBehaviour.PlayAnimationOnce(animationConfig.sceneOnHit1, 0f, OnHitAnimEnd);
                }
            }
            else
            {
                animBehaviour.rootMotionRatioXZ = detailData.moveLength;
                AttachOnHitEfx(m_entityView);
                if (isLast)
                {
                    if (resultClip.isDead)
                    {
                        animBehaviour.rootMotionRatioXZ = 1f;
                        animBehaviour.PlayAnimationOnce(animationConfig.combatDie, 0f, OnDeadAnimEnd);
                    }
                    else if (m_dramaContext.isNextCounterAttack && !m_dramaContext.scenePerformance && !actorView.BanAnimation())
                    {
                        string animName = animationConfig.combatNormalCounter;
                        if (resultClip.resultType == SkillEffectResultType.Dodge)
                        {
                            if (!string.IsNullOrEmpty(animationConfig.combatDodgeCounter))
                            {
                                animName = animationConfig.combatDodgeCounter;
                            }
                        }

                        animBehaviour.rootMotionRatioXZ = moveLength / -animBehaviour.GetAnimationFinalRootOffset(animName).z;
                        animBehaviour.PlayAnimationOnce(animName, 0f, OnHitAnimEnd);
                    }
                    else
                    {
                        string animName = animationConfig.combatNormalOnHit1;
                        if (resultClip.resultType == SkillEffectResultType.Dodge)
                        {
                            if (!string.IsNullOrEmpty(animationConfig.combatDodge1))
                            {
                                animName = animationConfig.combatDodge1;
                            }
                        }

                        animBehaviour.rootMotionRatioXZ = moveLength;
                        animBehaviour.PlayAnimationOnce(animName, 0f, OnHitAnimEnd);
                    }
                }
                else
                {
                    string animName = animationConfig.combatNormalOnHit1;
                    if (resultClip.resultType == SkillEffectResultType.Dodge)
                    {
                        if (!string.IsNullOrEmpty(animationConfig.combatDodge1))
                        {
                            animName = animationConfig.combatDodge1;
                        }
                    }

                    animBehaviour.rootMotionRatioXZ = moveLength;
                    animBehaviour.PlayAnimationOnce(animName, 0f, OnHitAnimEnd);
                }

                if (!actorView.configData.CanHitBack)
                {
                    animBehaviour.rootMotionEnabled = false;
                }
                else
                {
                    animBehaviour.rootMotionEnabled = true;
                }
            }

            m_entityViewAttacker = EntityViewManager.instance.GetEntityView(resultClip.entityUidAttacker);
            var actorViewAttacker = m_entityViewAttacker as ActorView;
            if (actorViewAttacker != null)
            {
                actorViewAttacker.SetCurHp(curHpAttacker);
                EventManager.instance.Broadcast(EventID.Entity_CurHp_Changed, resultClip.entityUidAttacker);
                ActorAnimationConfig animationConfigAttacker = actorViewAttacker.animationConfig;
                var animBehaviourAttacker = actorViewAttacker.GetBehaviour<EntityAnimatorBehaviour>();
                if (animationConfigAttacker != null && animBehaviourAttacker != null)
                {
                    if (isFirst)
                    {
                        if (!actorView.configData.CanHitBack)
                        {
                            animBehaviourAttacker.rootMotionEnabled = false;
                        }
                    }
                    else if (isLast)
                    {
                        if (resultClip.isDeadAttacker)
                        {
                            if (m_dramaContext.scenePerformance)
                            {
                                animBehaviourAttacker.rootMotionEnabled = false;
                                animBehaviourAttacker.PlayAnimationOnce(animationConfigAttacker.sceneDie, 0f, OnDeadAnimEndAttacker);
                            }
                            else
                            {
                                animBehaviourAttacker.rootMotionRatioXZ = 1f;
                                animBehaviourAttacker.PlayAnimationOnce(animationConfigAttacker.combatDie, 0f, OnDeadAnimEndAttacker);
                                animBehaviourAttacker.rootMotionEnabled = true;
                            }
                        }
                    }
                }
            }
        }

        private void OnDeadAnimEnd(bool interrupt)
        {
            if (m_entityView != null)
            {
                var animBehaviour = m_entityView.GetBehaviour<EntityAnimatorBehaviour>();
                if (animBehaviour != null)
                {
                    animBehaviour.rootMotionEnabled = false;
                }

                m_entityView.SetDying();
            }

            m_isEnd = true;
        }

        private void OnDeadAnimEndAttacker(bool interrupt)
        {
            if (m_entityViewAttacker != null)
            {
                var animBehaviour = m_entityViewAttacker.GetBehaviour<EntityAnimatorBehaviour>();
                if (animBehaviour != null)
                {
                    animBehaviour.rootMotionEnabled = false;
                }

                m_entityViewAttacker.SetDying();
            }

            m_isEnd = true;
        }

        private void OnHitAnimEnd(bool interrupt)
        {
            if (m_entityView != null)
            {
                var animBehaviour = m_entityView.GetBehaviour<EntityAnimatorBehaviour>();
                if (animBehaviour != null)
                {
                    animBehaviour.rootMotionEnabled = false;
                }
            }

            if (!interrupt)
            {
                PlayIdle();
            }

            m_isEnd = true;
        }

        private void PlayIdle()
        {
            if (m_entityView == null)
            {
                return;
            }

            var idleBehaviour = m_entityView.GetBehaviour<EntityPlayIdleBehaviour>();
            if (idleBehaviour != null)
            {
                if (m_dramaContext.engageType == SkillEngageType.Combat)
                {
                    idleBehaviour.PlayCombatIdle(false);
                }
                else
                {
                    idleBehaviour.PlaySceneIdle(false);
                }
            }
        }

        private void AttachOnHitEfx(EntityView entityView)
        {
            GameObject attachRoot = entityView.GetBindPointObj(EntityBindPointId.Chest);
            ParticleRuntimeInfo info = ParticleManager.instance.Spawn(CommonPrefabPathSetting.instance.commonOnHitDamageFxRef.path, attachRoot, true, true, entityView.GetParticleScale());
            info.go.transform.localPosition = Vector3.zero;
            info.go.transform.localEulerAngles = Vector3.zero;
        }

        private void PlayDummyOnHit(Action<bool> actionOnEnd)
        {
            if (m_entityView == null)
            {
                actionOnEnd.InvokeSafely(true);
                return;
            }

            float scale = 1f;
            if (m_entityView.GetComponent<EntityViewConfig>() != null)
            {
                scale = m_entityView.GetComponent<EntityViewConfig>().scale;
            }

            Vector3 start = Vector3.one * scale;
            Vector3 end = start * 0.9f;
            m_lerpVec3.actionOnTick = (l) => m_entityView.transform.localScale = l.curValue;
            m_lerpVec3.actionOnFinish = (l) =>
            {
                m_lerpVec3.actionOnTick = (l) => m_entityView.transform.localScale = l.curValue;
                m_lerpVec3.actionOnFinish = (l) => actionOnEnd.InvokeSafely(false);
                m_lerpVec3.Reset();
                m_lerpVec3.Start(end, start, 0.03f, EBaseLerpFuncType.Linear);
            };
            m_lerpVec3.Start(start, end, 0.05f, EBaseLerpFuncType.Linear);
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            m_lerpVec3.Tick(timeSlice.deltaTime);
            base.OnTick(timeSlice);
        }


        /// <summary>
        /// 处理受击声音
        /// </summary>
        private void HandleOnHitAudio()
        {
            if (m_resultClip is not SkillEffectResultClip_Damage resultClip)
            {
                return;
            }

            m_entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (!m_entityView)
            {
                return;
            }

            var actorView = m_entityView as ActorView;
            if (!actorView || !actorView.gameObject)
            {
                return;
            }

            ArmourType armourType = actorView.skinConfigData.ArmourType;

            DramaOnHitDamageEntityData dramaOnHitDamageEntityData = m_dramaData.damageEntityData;
            EnumString<BattleActionEffectOnHitWeaponType> weaponType = dramaOnHitDamageEntityData.audioWeaponType;
            EnumString<BattleActionEffectOnHitAttackType> attackType = dramaOnHitDamageEntityData.audioAttackType;
            EnumString<BattleActionEffectOnHitDamageLevel> damageLevel = dramaOnHitDamageEntityData.audioDamageLevel;
            switch (resultClip.resultType)
            {
                case SkillEffectResultType.Guard:
                {
                    m_isEnd = true;
                    break;
                }
                default:
                {
                    string onHitEventName = $"CH_General_OnHit_{weaponType}_{attackType}_{damageLevel}";
                    if (WwiseAudioManager.Instance.IsEventValid(onHitEventName, false))
                    {
                        WwiseAudioManager.Instance.PlaySound(onHitEventName, actorView.gameObject, "OnHit");
                    }

                    string onHitMaterialEventName = $"CH_General_OnHitMaterial_{weaponType}_{armourType}";
                    if (WwiseAudioManager.Instance.IsEventValid(onHitMaterialEventName, false))
                    {
                        WwiseAudioManager.Instance.PlaySound(onHitMaterialEventName, actorView.gameObject, "OnHitMaterial");
                    }

                    break;
                }
            }
        }
    }
}