using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitReplaceEntityHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            //var resultClip = m_resultClip as BattleActionEffectReplaceEntityResultClip;
            //DramaOnHitReplaceEntityData detailData = m_dramaData.replaceEntityData;
            //var srcEntityView = EntityViewManager.instance.GetEntityView(resultClip.srcEntityUid);
            //var targetEntityView = EntityViewManager.instance.GetEntityView(resultClip.targetEntityUid);
            //var srcPos = srcEntityView.GetLocatedPosition();
            //var targetPos = targetEntityView.GetLocatedPosition();
            //srcEntityView.SetPosition(targetPos);
            //targetEntityView.SetPosition(srcPos);
            m_isEnd = true;
        }
    }
}
