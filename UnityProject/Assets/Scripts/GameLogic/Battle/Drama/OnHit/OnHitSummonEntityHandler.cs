using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitSummonEntityHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Summon;
            if (resultClip.entity == null)
            {
                m_isEnd = true;
                return;
            }
            IBattle battle = BattleShortCut.sampleBattle;
            Entity entity = resultClip.entity.Copy(battle);
            entity.SetSummonerUid(resultClip.summonerUid);
            if (entity.HasActionChance())
            {
                BattleShortCut.sampleBattle.SetEntityMustAct(entity.uid);
            }
            if (resultClip.destroyEntityUid > 0)
            {
                //var destroyEntity = battle.GetEntityByUid(resultClip.destroyEntityUid);
                //if (destroyEntity != null)
                //{
                //    if (destroyEntity.entityType == EntityType.Actor)
                //    {

                //    }
                //}
                battle.DestroyEntityById(resultClip.destroyEntityUid);
                EntityViewManager.instance.DestroyEntityView(resultClip.destroyEntityUid);
            }
            battle.AddEntity(entity);
            BattleTeam team = battle.GetTeamByUid(entity.GetTeamUid());
            if (team != null)
            {
                team.AddEntity(entity, false);
            }
            EntityViewManager.instance.CreateAndAddEntityView(entity);
            foreach (var buffAttachResult in resultClip.buffAttachResultList)
            {
                BuffPerformanceUtility.PerformanceBuffAttach(buffAttachResult);
            }
            m_isEnd = true;
        }
    }
}
