using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitEntityExtraMoveHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_ExtraMove;
            DramaOnHitEntityExtraMoveData detailData = m_dramaData.entityExtraMoveData;
            EntityView view = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (view != null)
            {
                view.SetExtraMoveChance(resultClip.extraMoveDist, resultClip.source);
                BattleShortCut.sampleBattle.SetEntityMustAct(resultClip.entityUid);
            }
            m_isEnd = true;
        }
    }
}
