using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;

namespace Phoenix.Drama
{
    public class OnHitDetachBuffHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_DetachBuff;
            for (int i = 0; i < resultClip.detachResultList.Count; ++i)
            {
                BuffDetachResult detachResult = resultClip.detachResultList[i];
                BuffPerformanceUtility.PerformanceBuffDetach(detachResult);
            }
            m_isEnd = true;
        }
    }
}
