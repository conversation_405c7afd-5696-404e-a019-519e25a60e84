using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;

namespace Phoenix.Drama
{
    public class OnHitEntityExtraActionHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_ExtraAction;
            DramaOnHitEntityExtraMoveData detailData = m_dramaData.entityExtraMoveData;
            EntityView view = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (view != null)
            {
                view.SetExtraActionChance(resultClip.source);
                BattleShortCut.sampleBattle.SetEntityMustAct(resultClip.entityUid);
            }
            m_isEnd = true;
        }
    }
}
