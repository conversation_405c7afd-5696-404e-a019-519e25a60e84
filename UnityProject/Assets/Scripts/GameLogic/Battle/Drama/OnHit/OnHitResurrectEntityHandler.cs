using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitResurrectEntityHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            //var resultClip = m_resultClip as clip;
            //DramaOnHitResurrectEntityData detailData = m_dramaData.resurrectEntityData;

            //EntityView entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);

            //entityView.Resurrect();
            //var playIdleBehaviour = entityView.GetBehaviour<EntityPlayIdleBehaviour>();
            //if (playIdleBehaviour != null)
            //{
            //    playIdleBehaviour.PlaySceneIdle(true);
            //}
            //m_isEnd = true;
        }
    }
}
