using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.ConfigData;

namespace Phoenix.Drama
{
    public class OnHitAttachBuffHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_AttachBuff;

            if (resultClip.resultType == SkillEffectResultType.Immune)
            {
                EventManager.instance.Broadcast(EventID.BattleTextFloat, resultClip.entityUid, "免疫");
                m_isEnd = true;
                return;
            }
            for (int i = 0; i < resultClip.attachResultList.Count; ++i)
            {
                BuffAttachResult attachResult = resultClip.attachResultList[i];
                BuffPerformanceUtility.PerformanceBuffAttach(attachResult);
            }
            m_isEnd = true;
        }
    }
}
