

using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.Battle;
using System;
using UnityEngine;

namespace Phoenix.Drama
{
    public class OnHitChangeTeamEnergyHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_ChangeTeamEnergy;
            DramaOnHitChangeTeamEnergyData detailData = m_dramaData.changeTeamEnergyData;
            var team = BattleShortCut.sampleBattle.GetTeamByUid(resultClip.teamUid);
            if (team != null)
            {
                Int32 changeValue = resultClip.updateTeamEnergy - team.sharedEnergy;
                team.sharedEnergy = resultClip.updateTeamEnergy;
                EventManager.instance.Broadcast(EventID.BattleTeam_SharedEnergy_Changed, resultClip.teamUid, resultClip.updateTeamEnergy);

                //if (changeValue != 0)
                //{
                //    string tip = changeValue > 0 ? $"+{Mathf.Abs(changeValue)}" : $"-{Mathf.Abs(changeValue)}";
                //    EventManager.instance.Broadcast(EventID.BattleTextFloatPro, resultClip.entityUid, GameLogic.UI.TextFloatPattern.EnergyUpdate, tip);
                //}
            }
            m_isEnd = true;
        }
    }
}
