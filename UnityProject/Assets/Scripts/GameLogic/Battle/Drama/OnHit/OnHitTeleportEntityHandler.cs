using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitTeleportEntityHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Teleport;
            if (resultClip.resultType == SkillEffectResultType.Immune)
            {
                EventManager.instance.Broadcast(EventID.BattleTextFloat, resultClip.entityUid, "免疫");
                m_isEnd = true;
                return;
            }
            EntityView entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            entityView.SetPosition(resultClip.teleportPos, true);
            m_isEnd = true;
        }
    }
}
