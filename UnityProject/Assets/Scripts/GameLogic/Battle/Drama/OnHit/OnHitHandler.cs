using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Drama
{
    public abstract class OnHitHandler
    {
        protected bool m_isEnd;
        protected DramaContext m_dramaContext;
        protected DramaOnHitData m_dramaData;
        protected SkillEffectResultClip m_resultClip;

        public bool isEnd
        {
            get { return m_isEnd; }
        }

        public void Init(SkillEffectResultClip resultClip, DramaOnHitData dramaData, DramaContext dramaContext)
        {
            m_resultClip = resultClip;
            m_dramaData = dramaData;
            m_dramaContext = dramaContext;
        }

        public void Start()
        {
            OnStart();
        }

        protected abstract void OnStart();

        protected virtual void OnTick(TimeSlice timeSlice) { }

        public void Tick(TimeSlice timeSlice)
        {
            OnTick(timeSlice);
        }
    }
}
