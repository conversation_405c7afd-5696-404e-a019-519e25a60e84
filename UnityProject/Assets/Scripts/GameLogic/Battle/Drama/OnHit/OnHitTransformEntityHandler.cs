using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitTransformEntityHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Transform;
            IBattle battle = BattleShortCut.sampleBattle;
            var targetActor = battle.GetEntityByUid(resultClip.entityUid) as Actor;
            var targetActorView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            var animPlayCtrl = targetActorView.GetBehaviour<EntityAnimatorBehaviour>();
            animPlayCtrl.InterruptAnimation();
            var transformActor = resultClip.entity.Copy(battle) as Actor;
            targetActor.transformActor = transformActor;
            EntityViewManager.instance.DestroyEntityView(targetActor.uid);
            EntityViewManager.instance.CreateAndAddEntityView(targetActor);
            m_isEnd = true;
        }
    }
}
