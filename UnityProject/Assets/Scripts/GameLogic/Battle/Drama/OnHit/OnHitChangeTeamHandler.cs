using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitChangeTeamHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Control;
            DramaOnHitChangeTeamData detailData = m_dramaData.changeTeamData;
            if (resultClip.resultType == SkillEffectResultType.Immune)
            {
                EventManager.instance.Broadcast(EventID.BattleTextFloat, resultClip.entityUid, "免疫");
                m_isEnd = true;
                return;
            }
            var entityView = EntityViewManager.instance.GetEntityView(resultClip.entityUid);
            if (entityView != null)
            {
                var curTeam = BattleShortCut.sampleBattle.GetTeamByUid(resultClip.curTeamUid);
                var preTeam = BattleShortCut.sampleBattle.GetTeamByUid(resultClip.preTeamUid);
                curTeam.AddEntityFrom(entityView, preTeam, false);
                entityView.SetActionChance(true);

                var targetPos = entityView.GetLocation();
                int occupySize = entityView.GetOccupySize();
                int occupyOffset = (occupySize - 1) / 2;
                for (int i = -occupyOffset; i <= occupyOffset; ++i)
                {
                    for (int j = -occupyOffset; j <= occupyOffset; ++j)
                    {
                        BattleShortCut.sampleBattle.GetFieldSummaryOfOccupyEntity().SetDependSummaryDirty(targetPos + new GridPosition(i, j));
                    }
                }
                EventManager.instance.Broadcast(EventID.Entity_Team_Changed, resultClip.entityUid, resultClip.curTeamUid);
            }
            m_isEnd = true;
        }
    }
}
