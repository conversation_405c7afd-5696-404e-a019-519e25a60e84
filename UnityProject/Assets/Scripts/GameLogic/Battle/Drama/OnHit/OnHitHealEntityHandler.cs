using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class OnHitHealEntityHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_Heal;
            DramaOnHitHealEntityData detailData = m_dramaData.healEntityData;

            BuffPerformanceUtility.PerformanceChangeActiveList(resultClip.buffChangeActiveResultList);

            EventManager.instance.Broadcast(EventID.BattleHealFloat, resultClip.entityUid, (int)resultClip.expectHeal);
            EntityViewManager.instance.GetEntityView(resultClip.entityUid).AddCurHp(resultClip.finalHeal);
            EventManager.instance.Broadcast(EventID.Entity_CurHp_Changed, resultClip.entityUid);
            m_isEnd = true;
        }
    }
}
