

using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.Battle;
using System;
using UnityEngine;

namespace Phoenix.Drama
{
    public class OnHitChangeSkillCdHandler : OnHitHandler
    {
        protected override void OnStart()
        {
            var resultClip = m_resultClip as SkillEffectResultClip_ChangeSkillCd;
            //DramaOnHitChangeSkillCdData detailData = m_dramaData.changeTeamEnergyData;
            var entity = BattleShortCut.sampleBattle.GetEntityByUid(resultClip.entityUid);
            if (entity != null)
            {
                var skill = entity.GetSkillBySkillUid(resultClip.skillUid);
                if (skill != null)
                {
                    skill.SetCoolTime(resultClip.updateCd);
                    //EventManager.instance.Broadcast(EventID.skillcd, resultClip.teamUid, resultClip.updateTeamEnergy);
                }
            }
            m_isEnd = true;
        }
    }
}
