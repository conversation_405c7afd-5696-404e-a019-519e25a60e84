using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.Drama
{
    public abstract class DramaBase : ClassPoolObj
    {
        protected DramaData m_data;
        private Timer m_timer = new Timer();
        private State m_state;
        protected DramaContext m_context;

        public bool isMute;

        public bool isEnd
        {
            get { return m_state == State.End; }
        }

        public DramaData data
        {
            get { return m_data; }
        }

        public float startFrame
        {
            get { return m_data.startFrame; }
        }

        public override void OnRelease()
        {
            base.OnRelease();
            m_data = null;
            m_timer.Reset();
            m_state = State.None;
            m_context = default;
            isMute = default;
        }

        public void Init(DramaData data)
        {
            m_data = data;
        }

        public void InitContext(DramaContext context)
        {
            m_context = context;
        }

        public void Start()
        {
            m_state = State.Wait;
            if (isMute)
            {
                m_timer.Start(0f, End);
            }
            else
            {
                m_timer.Start(m_data.startFrame / 30f, OnTimeUp);
            }
        }

        private void OnTimeUp()
        {
            m_state = State.Start;
            OnStart();
        }

        public void Tick(TimeSlice timeSlice)
        {
            if(m_state== State.Wait)
            {
                m_timer.Tick(timeSlice);
            }
            else if(m_state == State.Start)
            {
                OnTick(timeSlice);
            }
        }

        public void End()
        {
            m_state = State.End;
            OnEnd();
        }

        protected virtual void OnStart(){ }
        protected virtual void OnEnd() { }
        protected virtual void OnTick(TimeSlice timeSlice) { }

        private enum State
        {
            None,
            Wait,
            Start,
            End,
        }
    }
}
