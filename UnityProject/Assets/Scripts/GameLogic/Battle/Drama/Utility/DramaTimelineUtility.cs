using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.GameLogic.Battle;

namespace Phoenix.Drama
{
    public static partial class DramaTimelineUtility
    {
        public static void Collect(DramaContext dramaContext, ResourceHandleCollection collection)
        {
            DramaTimelineData dramaTimeLineData = null;
            if (!string.IsNullOrEmpty(dramaContext.dramaPath))
            {
                dramaTimeLineData = ResourceHandleManager.instance.GetResource<DramaTimelineData>(dramaContext.dramaPath);
            }
            if (dramaTimeLineData != null)
            {
                foreach (var dramaData in dramaTimeLineData.dramaList)
                {
                    switch (dramaData.dramaType)
                    {
                        case DramaType.PlayAnimation:
                            {
                                DramaPlayAnimationData dramaPlayAnimationData = dramaData as DramaPlayAnimationData;
                                int entityUid = dramaContext.originEntityUid;
                                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                                string path = entityView?.GetBehaviour<EntityAnimatorBehaviour>()?.GetAnimationPath(dramaPlayAnimationData.animationName);
                                if (!string.IsNullOrEmpty(path))
                                {
                                    collection.ReadyLoad(path, entityUid, AssetType.Animation);
                                }
                            }
                            break;
                        case DramaType.PlayEffect:
                            DramaPlayEffectData dramaPlayEffectData = dramaData as DramaPlayEffectData;
                            switch (dramaPlayEffectData.attachType.value)
                            {
                                case SkillObjectAttachType.SourceEntity:
                                    collection.ReadyLoad(dramaPlayEffectData.efx.path, dramaContext.originEntityUid, AssetType.Prefab);
                                    break;
                                case SkillObjectAttachType.TargetEntity:
                                    //var effectResult = dramaContext.GetEffectResult(dramaPlayEffectData.skillEffectGroupId);
                                    //if (effectResult != null)
                                    //{
                                    //    foreach (var targetSelectInfoWrap in effectResult.selectResult.targetSelectInfoWrapList)
                                    //    {
                                    //        collection.ReadyLoad(dramaPlayEffectData.efx.path, targetSelectInfoWrap.entityUid, AssetType.Prefab);
                                    //    }
                                    //}
                                    break;
                                case SkillObjectAttachType.TargetGrid:
                                    collection.ReadyLoad(dramaPlayEffectData.efx.path, 0, AssetType.Prefab);
                                    break;
                            }
                            break;
                        case DramaType.OnHit:
                            {
                                DramaOnHitData dramaOnHitData = dramaData as DramaOnHitData;
                                int entityUid = dramaContext.originEntityUid;
                                switch (dramaOnHitData.effectType.value)
                                {
                                    case SkillEffectType.Summon:
                                        var effectResult = dramaContext.effectResultList.GetValueSafely(dramaOnHitData.skillEffectId);
                                        if (effectResult != null)
                                        {
                                            foreach (var effectClip in effectResult.clipList)
                                            {
                                                var clip = effectClip as SkillEffectResultClip_Summon;
                                                if (clip == null)
                                                {
                                                    continue;
                                                }
                                                var entityResConfig = ConfigDataManager.instance.GetEntitySkin(clip.entity.entityType, clip.entity.rid);
                                                collection.ReadyLoad(entityResConfig.PrefabPath, clip.entity.uid, AssetType.Prefab);
                                            }
                                        }
                                        break;
                                }
                            }
                            break;
                        case DramaType.PlayUnityTimeline:
                            {
                                DramaPlayUnityTimelineData dramaPlayUnityTimelineData = dramaData as DramaPlayUnityTimelineData;
                                int entityUid = dramaContext.originEntityUid;
                                collection.ReadyLoad(dramaPlayUnityTimelineData.timeline.path, entityUid, AssetType.Prefab);
                            }
                            break;

                    }
                }
            }
        }

        public static float GetAdjustDistance(string path)
        {
            DramaTimelineData dramaTimelineData = ResourceHandleManager.instance.GetResource<DramaTimelineData>(path);
            if(dramaTimelineData != null)
            {
                return dramaTimelineData.adjustDistance;
            }
            return 0f;
        }
    }
}
