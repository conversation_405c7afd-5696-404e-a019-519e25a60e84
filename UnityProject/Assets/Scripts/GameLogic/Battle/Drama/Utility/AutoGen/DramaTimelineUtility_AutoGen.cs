using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public static partial class DramaTimelineUtility
    {
        public static DramaBase CreateDrama(DramaType dramaType)
        {
            switch (dramaType)
            {
                case DramaType.OnHit:
                    return ClassPoolManager.instance.Fetch<DramaOnHit>();
                case DramaType.PlayAnimation:
                    return ClassPoolManager.instance.Fetch<DramaPlayAnimation>();
                case DramaType.PlayEffect:
                    return ClassPoolManager.instance.Fetch<DramaPlayEffect>();
                case DramaType.PlaySound:
                    return ClassPoolManager.instance.Fetch<DramaPlaySound>();
                case DramaType.ChangeEntityState:
                    return ClassPoolManager.instance.Fetch<DramaChangeEntityState>();
                case DramaType.PlayUnityTimeline:
                    return ClassPoolManager.instance.Fetch<DramaPlayUnityTimeline>();
            }
            return null;
        }
        
        public static DramaData CreateDramaData(DramaType dramaType)
        {
            switch (dramaType)
            {
                case DramaType.OnHit:
                    return new DramaOnHitData();
                case DramaType.PlayAnimation:
                    return new DramaPlayAnimationData();
                case DramaType.PlayEffect:
                    return new DramaPlayEffectData();
                case DramaType.PlaySound:
                    return new DramaPlaySoundData();
                case DramaType.ChangeEntityState:
                    return new DramaChangeEntityStateData();
                case DramaType.PlayUnityTimeline:
                    return new DramaPlayUnityTimelineData();
            }
            return null;
        }
        
        public static DramaType GetDramaTypeByClassName(string name)
        {
            switch(name)
            {
                case "DramaOnHitData":
                    return DramaType.OnHit;
                case "DramaPlayAnimationData":
                    return DramaType.PlayAnimation;
                case "DramaPlayEffectData":
                    return DramaType.PlayEffect;
                case "DramaPlaySoundData":
                    return DramaType.PlaySound;
                case "DramaChangeEntityStateData":
                    return DramaType.ChangeEntityState;
                case "DramaPlayUnityTimelineData":
                    return DramaType.PlayUnityTimeline;
            }
            return default;
        }
        
        public static OnHitHandler CreateOnHitHandler(SkillEffectType effectType)
        {
            switch (effectType)
            {
                case SkillEffectType.AttachBuff:
                    return new OnHitAttachBuffHandler();
                case SkillEffectType.DetachBuff:
                    return new OnHitDetachBuffHandler();
                case SkillEffectType.Damage:
                    return new OnHitDamageEntityHandler();
                case SkillEffectType.Heal:
                    return new OnHitHealEntityHandler();
                case SkillEffectType.Summon:
                    return new OnHitSummonEntityHandler();
                case SkillEffectType.Move:
                    return new OnHitMoveEntityHandler();
                case SkillEffectType.Teleport:
                    return new OnHitTeleportEntityHandler();
                case SkillEffectType.Transform:
                    return new OnHitTransformEntityHandler();
                case SkillEffectType.ExtraMove:
                    return new OnHitEntityExtraMoveHandler();
                case SkillEffectType.ExtraAction:
                    return new OnHitEntityExtraActionHandler();
                case SkillEffectType.ChangeTeamEnergy:
                    return new OnHitChangeTeamEnergyHandler();
                case SkillEffectType.ChangeSkillCd:
                    return new OnHitChangeSkillCdHandler();
                case SkillEffectType.Control:
                    return new OnHitChangeTeamHandler();
            }
            return null;
        }
    }
}
