using Phoenix.Core;
using Phoenix.ConfigData;
using Phoenix.Battle;

namespace Phoenix.Drama
{
    public class DramaOnHitData : DramaData
    {
        public int skillEffectId;
        
        public EnumString<SkillEffectType> effectType = new EnumString<SkillEffectType>();
        
        public override DramaType dramaType
        {
            get {{ return DramaType.OnHit; }}
        }
        
        [InspectorHide("effectType", SkillEffectType.AttachBuff)]
        public DramaOnHitAttachBuffData attachBuffData = new DramaOnHitAttachBuffData();
        
        [InspectorHide("effectType", SkillEffectType.DetachBuff)]
        public DramaOnHitDetachBuffData detachBuffData = new DramaOnHitDetachBuffData();
        
        [InspectorHide("effectType", SkillEffectType.Damage)]
        public DramaOnHitDamageEntityData damageEntityData = new DramaOnHitDamageEntityData();
        
        [InspectorHide("effectType", SkillEffectType.Heal)]
        public DramaOnHitHealEntityData healEntityData = new DramaOnHitHealEntityData();
        
        [InspectorHide("effectType", SkillEffectType.Summon)]
        public DramaOnHitSummonEntityData summonEntityData = new DramaOnHitSummonEntityData();
        
        [InspectorHide("effectType", SkillEffectType.Move)]
        public DramaOnHitMoveEntityData moveEntityData = new DramaOnHitMoveEntityData();
        
        [InspectorHide("effectType", SkillEffectType.Teleport)]
        public DramaOnHitTeleportEntityData teleportEntityData = new DramaOnHitTeleportEntityData();
        
        [InspectorHide("effectType", SkillEffectType.Transform)]
        public DramaOnHitTransformEntityData transformEntityData = new DramaOnHitTransformEntityData();
        
        [InspectorHide("effectType", SkillEffectType.ExtraMove)]
        public DramaOnHitEntityExtraMoveData entityExtraMoveData = new DramaOnHitEntityExtraMoveData();
        
        [InspectorHide("effectType", SkillEffectType.ExtraAction)]
        public DramaOnHitEntityExtraActionData entityExtraActionData = new DramaOnHitEntityExtraActionData();
        
        [InspectorHide("effectType", SkillEffectType.ChangeTeamEnergy)]
        public DramaOnHitChangeTeamEnergyData changeTeamEnergyData = new DramaOnHitChangeTeamEnergyData();
        
        [InspectorHide("effectType", SkillEffectType.Control)]
        public DramaOnHitChangeTeamData changeTeamData = new DramaOnHitChangeTeamData();
        
        [InspectorHide("effectType", SkillEffectType.ChangeSkillCd)]
        public DramaOnHitReduceCoolTimeData reduceCoolTimeData = new DramaOnHitReduceCoolTimeData();
    }
}
