using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.Drama
{
    [Serializable]
    public class DramaOnHitDamageEntityData : DramaOnHitDetailData
    {
        public float moveLength;
        public float damagePercent;

        /// <summary>
        /// 受击的武器类型, 用于声音
        /// </summary>
        public EnumString<BattleActionEffectOnHitWeaponType> audioWeaponType = new EnumString<BattleActionEffectOnHitWeaponType>();

        /// <summary>
        /// 受击的攻击招式类型, 用于声音
        /// </summary>
        public EnumString<BattleActionEffectOnHitAttackType> audioAttackType = new EnumString<BattleActionEffectOnHitAttackType>();

        /// <summary>
        /// 受击的伤害程度类型, 用于声音
        /// </summary>
        public EnumString<BattleActionEffectOnHitDamageLevel> audioDamageLevel = new EnumString<BattleActionEffectOnHitDamageLevel>();
    }
}