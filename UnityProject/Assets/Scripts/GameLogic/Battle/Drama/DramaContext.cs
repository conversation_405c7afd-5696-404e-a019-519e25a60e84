using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Drama
{
    public struct DramaContext
    {
        public string dramaPath;
        public int originEntityUid;
        public int combatTargetEntityUid;
        public GridPosition targetPosition;
        public bool simplePerformance;
        public bool scenePerformance;
        public bool isNextCounterAttack;
        public float deltaAdjustDistance;
        public bool skipPerformance;
        public SkillEngageType engageType;
        public List<SkillEffectResult> effectResultList;

        public void InitEffectResultList(List<SkillEffectResult> list)
        {
            if (effectResultList == null)
            {
                effectResultList = new List<SkillEffectResult>();
            }
            effectResultList.AddRange(list);
        }

        public void InitSingleEffectResult(SkillEffectResult result)
        {
            if (effectResultList == null)
            {
                effectResultList = new List<SkillEffectResult>();
            }
            effectResultList.Add(result);
        }

        public SkillEffectResult GetEffectResult(int id)
        {
            for (int i = 0; i < effectResultList.Count; ++i)
            {
                if (effectResultList[i].id == id)
                {
                    return effectResultList[i];
                }
            }
            return null;
        }
    }
}
