using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleSceneEffectHandler
    {
        private readonly Dictionary<long, List<EffectView>> m_effectDic = new Dictionary<long, List<EffectView>>();
        private readonly List<long> m_tempIdList = new List<long>();

        #region Public

        public void Init(GameObject root)
        {

        }

        public void OnTick(TimeSlice ts)
        {
            m_tempIdList.Clear();
            foreach (var kvp in m_effectDic)
            {
                foreach (var effect in kvp.Value)
                {
                    effect.Tick();
                }

                if (!m_tempIdList.Contains(kvp.Key) && IsDead(kvp.Key))
                {
                    m_tempIdList.Add(kvp.Key);
                }
            }
            foreach (long effectId in m_tempIdList)
            {
                RemoveEffect(effectId);
            }
            m_tempIdList.Clear();
        }

        public void UnInit()
        {

        }

        public bool IsDead(long instanceId)
        {
            if (!m_effectDic.ContainsKey(instanceId))
            {
                return false;
            }

            if (m_effectDic[instanceId] == null || m_effectDic[instanceId].Count == 0)
            {
                return false;
            }

            foreach (var instanceInfo in m_effectDic[instanceId])
            {
                if (instanceInfo.IsSurvival())
                {
                    return true;
                }
            }

            return false;
        }

        public void AddEffect2Grid(String effectPath, GridPosition gridPosition, Vector3 offset)
        {
            AddEffectInternal(effectPath, gridPosition, offset);
        }

        public void RemoveEffect(Int64 instanceId)
        {
            RemoveEffectInternal(instanceId);
        }

        public void RemoveAllEffect()
        {
            m_tempIdList.Clear();
            foreach (var kvp in m_effectDic)
            {
                m_tempIdList.Add(kvp.Key);
            }

            foreach (long effectId in m_tempIdList)
            {
                RemoveEffect(effectId);
            }
            m_effectDic.Clear();
        }

        #endregion



        #region Private

        private void AddEffectInternal(String effectPath, GridPosition gridPosition, Vector3 offset)
        {
            if (!m_effectDic.ContainsKey(0))
            {
                m_effectDic.Add(0, new List<EffectView>());
            }

            var newEffect = ClassPoolManager.instance.Fetch<EffectView>();
            newEffect.Initialize(effectPath);
            m_effectDic[0].Add(newEffect);
        }

        public void RemoveEffectInternal(Int64 instanceId)
        {
            if (m_effectDic.ContainsKey(instanceId))
            {
                foreach (var instanceInfo in m_effectDic[instanceId])
                {
                    instanceInfo.Release();
                }

                m_effectDic.Remove(instanceId);
            }
        }

        #endregion


    }

    public class EffectView : ClassPoolObj
    {
        private Boolean m_loop;
        private Single m_duration;
        private Single m_time;
        private Boolean m_enable;

        public String EffectPath { get; private set; }
        public Transform Effect { get; private set; }

        public void Initialize(String effectPath)
        {
            EffectPath = effectPath;
            m_loop = false;
            m_duration = 1;
            m_time = 0;
            m_enable = true;
        }
        public override void OnRelease()
        {
            base.OnRelease();
            m_enable = false;
            if (Effect != null)
            {
                Effect = null;
            }
        }

        public void Tick()
        {
            m_time += Time.deltaTime;
        }

        public bool IsSurvival()
        {
            if (m_loop)
                return true;

            return m_time <= m_duration;
        }

        public void ShowOrHideEffect(Boolean enable)
        {
            m_enable = enable;
            if (Effect != null)
            {
                Effect.SetActiveSafely(m_enable);
                if (!m_enable && !m_loop)
                {
                    m_time = m_duration;
                }
            }
        }

        #region Private


        #endregion
    }
}
