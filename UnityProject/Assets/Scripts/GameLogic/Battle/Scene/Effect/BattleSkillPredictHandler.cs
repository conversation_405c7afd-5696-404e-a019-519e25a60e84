using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleSkillPredictHandler
    {
        internal class PredictNode
        {
            public Int32 m_entityUid;
            public List<GameObject> m_effects;

            public PredictNode(Int32 entityUid, List<GameObject> effects)
            {
                m_entityUid = entityUid;
                m_effects = effects;
            }
            public void UnInit()
            {
                foreach (GameObject go in m_effects)
                {
                    ResourceHandleManager.instance.DespawnGameObject(go);
                }
                m_effects.Clear();
            }

            public void ShowOrHide(bool b)
            {
                foreach (GameObject go in m_effects)
                {
                    go.SetActiveSafely(b);
                }
            }
        }





        private readonly Dictionary<Int32, PredictNode> m_entitySkillPredictMap = new Dictionary<Int32, PredictNode>();





        public void Reset()
        {
            foreach (var item in m_entitySkillPredictMap)
            {
                item.Value.UnInit();
            }
            m_entitySkillPredictMap.Clear();
        }


        public void SkillPredictBegin(Int32 entityUid, Int32 skillUid, List<GridPosition> grids)
        {
            if (IsValid(entityUid))
            {
                List<GameObject> effects = new List<GameObject>();
                Vector3 offset = new Vector3(0, 0.1f, 0);
                foreach (var grid in grids)
                {
                    Vector3 position = BattleShortCut.GetGridWorldPosition(grid);
                    GameObject go = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.gridFxOfSkillPredict.path);
                    go.transform.position = position + offset;
                    effects.Add(go);
                }
                PredictNode newNode = new PredictNode(entityUid, effects);
                m_entitySkillPredictMap[entityUid] = newNode;
            }
        }
        public void SkillPredictEnd(Int32 entityUid, Int32 skillUid)
        {
            SkillPredictRemoveInternal(entityUid);
        }
        public void ShowOrHideSkillPredictEffect(Boolean isShow)
        {
            foreach (var predict in m_entitySkillPredictMap)
            {
                predict.Value.ShowOrHide(isShow);
            }
        }



        private bool IsValid(Int32 entityUid)
        {
            if (m_entitySkillPredictMap.ContainsKey(entityUid))
            {
                Debug.LogError(String.Format("[Error] skill predict entityUid {0} has exist", entityUid));
                return false;
            }
            return true;
        }
        private void SkillPredictRemoveInternal(Int32 entityUid)
        {
            if (m_entitySkillPredictMap.ContainsKey(entityUid))
            {
                var node = m_entitySkillPredictMap[entityUid];
                node.UnInit();
                m_entitySkillPredictMap.Remove(entityUid);
            }
        }

    }
}
