using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

namespace Phoenix.GameLogic.Battle
{
    public class BattleSceneConfig : MonoBehaviour
    {
        public int width;
        public int height;
        public Vector2 offset;
        public GameObject gridRoot;

        public bool showLine;
        public Color lineColor;
        public List<Collider> colliderList = new List<Collider>();

        public (Int32 x, Int32 y) GetGrid(Vector3 worldPosition, Single gridSize)
        {
            Single posX = worldPosition.x - offset.x + gridSize / 2;
            Single posY = worldPosition.z - offset.y + gridSize / 2;
            Int32 gridX = Mathf.FloorToInt(posX / gridSize);
            Int32 gridY = Mathf.FloorToInt(posY / gridSize);
            return (gridX, gridY);
        }

#if UNITY_EDITOR
        private Vector3[] m_tempTriangle = new Vector3[3];
        private Vector3[] m_tempLine = new Vector3[2];
        private List<List<Vector3>> m_drawLineList = new List<List<Vector3>>();
        private bool m_isDirty;

        public Vector2 realOffset
        {
            get { return new Vector2(offset.x - 0.5f, offset.y - 0.5f); }
        }

        private void OnValidate()
        {
            m_isDirty = true;
        }

        private void OnDrawGizmos()
        {
            if (!showLine)
            {
                return;
            }
            if (m_isDirty)
            {
                m_isDirty = false;

                m_drawLineList.Clear();
                for (int i = 0; i <= width; ++i)
                {
                    List<Vector3> linePosList = GetVerticalClipLinePosList(colliderList, realOffset.x + i, realOffset.y, realOffset.y + height);
                    m_drawLineList.Add(linePosList);
                }
                for (int i = 0; i <= height; ++i)
                {
                    List<Vector3> linePosList = GetHorizontalClipLinePosList(colliderList, realOffset.y + i, realOffset.x, realOffset.x + width);
                    m_drawLineList.Add(linePosList);
                }
            }
            Gizmos.color = lineColor;
            foreach (var linePosList in m_drawLineList)
            {
                DrawLine(linePosList);
            }
        }

        private void DrawLine(List<Vector3> linePosList)
        {
            for (int i = 0; i < linePosList.Count; i += 2)
            {
                Gizmos.DrawLine(linePosList[i], linePosList[i + 1]);
            }
        }

        private List<Vector3> GetVerticalClipLinePosList(List<Collider> colliderList, float xPos, float minY, float maxY)
        {
            Plane plane = new Plane(Vector3.right, Vector3.right * xPos);
            Plane planeMin = new Plane(Vector3.forward, Vector3.forward * minY);
            Plane planeMax = new Plane(Vector3.back, Vector3.forward * maxY);
            return GetClipLinePosList(colliderList, plane, planeMin, planeMax);
        }

        private List<Vector3> GetHorizontalClipLinePosList(List<Collider> colliderList, float yPos, float minX, float maxX)
        {
            Plane plane = new Plane(Vector3.forward, Vector3.forward * yPos);
            Plane planeMin = new Plane(Vector3.right, Vector3.right * minX);
            Plane planeMax = new Plane(Vector3.left, Vector3.right * maxX);
            return GetClipLinePosList(colliderList, plane, planeMin, planeMax);
        }

        private List<Vector3> GetClipLinePosList(List<Collider> colliderList, Plane lineClipPlan, Plane minLinePlan, Plane maxLinePlan)
        {
            List<Vector3> linePosList = new List<Vector3>();

            foreach (Collider collider in colliderList)
            {
                if(collider == null)
                {
                    continue;
                }
                Mesh colliderMesh = GetMeshByCollider(collider);
                if (colliderMesh == null)
                {
                    continue;
                }
                Matrix4x4 matrix = collider.transform.localToWorldMatrix;
                int[] triangles = colliderMesh.triangles;
                Vector3[] vertexList = colliderMesh.vertices;
                for (int i = 0; i < triangles.Length; i += 3)
                {
                    int triangleId1 = triangles[i];
                    int triangleId2 = triangles[i + 1];
                    int triangleId3 = triangles[i + 2];
                    Vector3 trianglePos1 = matrix.MultiplyPoint(vertexList[triangleId1]);
                    Vector3 trianglePos2 = matrix.MultiplyPoint(vertexList[triangleId2]);
                    Vector3 trianglePos3 = matrix.MultiplyPoint(vertexList[triangleId3]);

                    ClipPolygonToLine(lineClipPlan, minLinePlan, maxLinePlan, trianglePos1, trianglePos2, trianglePos3, linePosList);
                }
            }
            return linePosList;
        }

        private Mesh GetMeshByCollider(Collider collider)
        {
            MeshCollider meshCollider = collider as MeshCollider;
            if (meshCollider != null)
            {
                return meshCollider.sharedMesh;
            }
            return null;
        }

        private void ClipPolygonToLine(Plane lineClipPlan, Plane minLinePlan, Plane maxLinePlan, Vector3 trianglePos1, Vector3 trianglePos2, Vector3 trianglePos3, List<Vector3> linePosList)
        {
            m_tempTriangle[0] = trianglePos1;
            m_tempTriangle[1] = trianglePos2;
            m_tempTriangle[2] = trianglePos3;
            bool validLine = false;
            for (int i = 0; i < m_tempTriangle.Length; i++)
            {
                Vector3 linePos1 = m_tempTriangle[i];
                Vector3 linePos2 = m_tempTriangle[(i + 1) % m_tempTriangle.Length];

                if (lineClipPlan.GetSide(linePos1) != lineClipPlan.GetSide(linePos2))
                {
                    Ray ray = new Ray(linePos1, (linePos2 - linePos1).normalized);
                    float distance;
                    if (lineClipPlan.Raycast(ray, out distance))
                    {
                        Vector3 hitPos = ray.GetPoint(distance);
                        if (!validLine)
                        {
                            m_tempLine[0] = hitPos;
                            validLine = true;
                        }
                        else
                        {
                            m_tempLine[1] = hitPos;
                        }
                    }
                }
            }
            if (validLine)
            {
                Vector3 inPos1 = m_tempLine[0];
                Vector3 inPos2 = m_tempLine[1];
                Vector3 outPos1;
                Vector3 outPos2;
                if (ClipLine(minLinePlan, inPos1, inPos2, out outPos1, out outPos2))
                {
                    inPos1 = outPos1;
                    inPos2 = outPos2;
                    if (ClipLine(maxLinePlan, inPos1, inPos2, out outPos1, out outPos2))
                    {
                        linePosList.Add(outPos1);
                        linePosList.Add(outPos2);
                    }
                }

            }
        }

        private bool ClipLine(Plane plane, Vector3 inPos1, Vector3 inPos2, out Vector3 outPos1, out Vector3 outPos2)
        {
            if (plane.GetSide(inPos1) && plane.GetSide(inPos2))
            {
                outPos1 = inPos1;
                outPos2 = inPos2;
                return true;
            }
            if (plane.GetSide(inPos1) != plane.GetSide(inPos2))
            {
                Ray ray = new Ray(inPos1, (inPos2 - inPos1).normalized);
                float distance;
                if (plane.Raycast(ray, out distance))
                {
                    Vector3 hitPos = ray.GetPoint(distance);
                    if (plane.GetSide(inPos1))
                    {
                        outPos1 = inPos1;
                        outPos2 = hitPos;
                    }
                    else
                    {
                        outPos1 = hitPos;
                        outPos2 = inPos2;
                    }
                    return true;
                }
            }
            outPos1 = Vector3.zero;
            outPos2 = Vector3.zero;
            return false;
        }
#endif
    }
}
