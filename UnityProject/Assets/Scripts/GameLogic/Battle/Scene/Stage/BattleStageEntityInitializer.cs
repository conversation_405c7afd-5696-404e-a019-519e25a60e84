using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class BattleStageEntityInitializer : Initializer
    {
        protected override IEnumerator OnProcess()
        {
            ResourceHandleCollection collection = new ResourceHandleCollection();
            ReadyLoadEntityView(collection);
            if (m_initialType == EInitialType.Sync)
            {
                collection.StartLoadSync();
            }
            else
            {
                collection.StartLoad(null);
                int tickCount = 1000;
                while (!collection.isLoadEnd)
                {
                    if (tickCount-- < 0)
                    {
                        break;
                    }
                    yield return null;
                }
            }
            collection.Clear();

            foreach (Entity entity in BattleShortCut.sampleBattle.GetEntityList())
            {
                var resConfigData = ConfigDataManager.instance.GetEntitySkin(entity.entityType, entity.rid);
                if (resConfigData == null)
                {
                    DebugUI.Log(string.Format("角色模型不存在：角色类型：{0}，角色Id：{1}", entity.entityType, entity.rid));
                    continue;
                }
                if (entity.entityType == EntityType.Actor)
                {
                    ActorAnimationConfig actorAnimationConfig = ResourceHandleManager.instance.GetResource<ActorAnimationConfig>(resConfigData.AnimationConfigPath);
                    foreach (var animationName in actorAnimationConfig.GetNecessaryAnimationNameList())
                    {
                        string animationPath = actorAnimationConfig.collection.GetPath(animationName);
                        if (string.IsNullOrEmpty(animationPath))
                        {
                            continue;
                        }
                        collection.ReadyLoad(animationPath, entity.uid, AssetType.Animation);
                    }
                    foreach (var skill in entity.GetSkillList())
                    {
                        if (skill == null)
                        {
                            continue;
                        }
                        AddSkillToCollection(collection, skill.uid, entity.uid);
                    }
                    ActorConfigData actorConfigData = ConfigDataManager.instance.GetActor(entity.rid);
                    if (actorConfigData != null)
                    {
                        AddSkillToCollection(collection, actorConfigData.NormalAttackId, entity.uid);
                        AddSkillToCollection(collection, actorConfigData.CounterAttackId, entity.uid);
                    }
                }
            }
            if (m_initialType == EInitialType.Sync)
            {
                collection.StartLoadSync();
            }
            else
            {
                collection.StartLoad(null);
                while (!collection.isLoadEnd)
                {
                    yield return null;
                }
            }
            while (!collection.isLoadEnd)
            {
                yield return null;
            }
            CreateEntityView();
        }

        private void ReadyLoadEntityView(ResourceHandleCollection collection)
        {
            foreach (Entity entity in BattleShortCut.sampleBattle.GetEntityList())
            {
                var resConfigData = ConfigDataManager.instance.GetEntitySkin(entity.entityType, entity.rid);
                if (resConfigData == null)
                {
                    continue;
                }
                collection.ReadyLoad(resConfigData.PrefabPath, entity.uid, AssetType.Prefab);
                collection.ReadyLoad(resConfigData.AnimationConfigPath, entity.uid, AssetType.Asset);
                collection.ReadyLoad(resConfigData.DramaCollectionPath, entity.uid, AssetType.Asset);
            }
        }

        private void AddSkillToCollection(ResourceHandleCollection collection, int skillRid, int actorId)
        {
        }

        private void CreateEntityView()
        {
            foreach (Entity entity in BattleShortCut.sampleBattle.GetEntityList())
            {
                var entityView = EntityViewManager.instance.CreateAndAddEntityView(entity);
                if (entityView == null)
                {
                    continue;
                }
                foreach (var buff in entityView.GetBuffList())
                {
                    var buffConfig = ConfigDataManager.instance.GetBuff(buff.buffRid);
                    if (buffConfig != null)
                    {
                        if (buffConfig.loopEffectId > 0)
                        {
                            var particleConfig = ConfigDataManager.instance.GetParticle(buffConfig.loopEffectId);
                            var obj = entityView.GetBindPointObj(particleConfig.BindPointId);
                            var particleInfo = ParticleManager.instance.Spawn(particleConfig.ResPath, obj, true, particleConfig.UseEntityModelScale, particleConfig.UseEntityParticleScale ? entityView.GetParticleScale() : 1f);
                            if (particleInfo != null)
                            {
                                entityView.buffParticleMap.Add(buff.buffUid, particleInfo.go);
                            }
                        }
                        if (!string.IsNullOrEmpty(buffConfig.loopAnimName))
                        {
                            var idleBehaviour = entityView.GetBehaviour<EntityPlayIdleBehaviour>();
                            if (idleBehaviour != null)
                            {
                                idleBehaviour.OverrideIdleName(buffConfig.loopAnimName);
                                idleBehaviour.PlaySceneIdle(false);
                            }
                        }
                    }
                }
            }
        }
    }
}
