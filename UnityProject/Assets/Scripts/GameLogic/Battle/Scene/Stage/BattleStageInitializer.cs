using System.Collections;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.SceneManagement;
using Phoenix.GameLogic.UI;
using YooAsset;

namespace Phoenix.GameLogic.Battle
{
    public class BattleStageInitializer : Initializer
    {
        private BattleInfo m_battleInfo;
        private int m_stageIndex;

        public void Init(BattleInfo battleInfo, int stageIndex)
        {
            m_battleInfo = battleInfo;
            m_stageIndex = stageIndex;
            AddSubInitializer(new BattleStageEntityInitializer());
        }

        protected override void OnReset()
        {
            m_battleInfo = null;
            m_stageIndex = default;
            base.OnReset();
        }

        protected override IEnumerator OnProcess()
        {
            var stageInfo = m_battleInfo.stageList.GetValueSafely(m_stageIndex);
            string preMainScenePath = BattleShortCut.battleScene.mainScenePath;
            string preCombatScenePath = BattleShortCut.battleScene.combatScenePath;
            RuntimeSceneUnloadOperation mainSceneUnloadOperation = null;
            RuntimeSceneUnloadOperation combatSceneUnloadOperation = null;
            RuntimeSceneLoadOperation mainSceneLoadOperation = null;
            RuntimeSceneLoadOperation combatSceneLoadOperation = null;
            bool needLoadScene = true;
            if (preMainScenePath == stageInfo.sceneAssetPath
                && preCombatScenePath == stageInfo.sceneCombatAssetPath)
            {
                needLoadScene = false;
            }

            List<string> preScenePaths = new List<string>() { preMainScenePath, preCombatScenePath };
            List<string> newScenePaths = new List<string>() { stageInfo.sceneAssetPath, stageInfo.sceneCombatAssetPath };
            var haveIntersection = false;

            if (needLoadScene)
            {
                BattleShortCut.battleScene.Reset();
                EntityViewManager.instance.DestroyAllEntityView();

                haveIntersection = ResourceManager.instance.AssetsIntersectionReference(preScenePaths, newScenePaths);

                if (!string.IsNullOrEmpty(preMainScenePath))
                {
                    mainSceneUnloadOperation = RuntimeSceneManager.instance.RemoveScene(preMainScenePath, null);
                }
                if (!string.IsNullOrEmpty(preCombatScenePath))
                {
                    combatSceneUnloadOperation = RuntimeSceneManager.instance.RemoveScene(preCombatScenePath, null);
                }
                mainSceneLoadOperation = RuntimeSceneManager.instance.AddScene(stageInfo.sceneAssetPath, typeof(BattleMainScene), null);
                combatSceneLoadOperation = RuntimeSceneManager.instance.AddScene(stageInfo.sceneCombatAssetPath, typeof(BattleCombatScene), null);
            }
            while (mainSceneUnloadOperation != null && !mainSceneUnloadOperation.isDone
                || combatSceneUnloadOperation != null && !combatSceneUnloadOperation.isDone
                || mainSceneLoadOperation != null && !mainSceneLoadOperation.isDone
                || combatSceneLoadOperation != null && !combatSceneLoadOperation.isDone)
            {
                if (m_initialType == EInitialType.Sync)
                {
                    Debug.LogError("不该走进这里");
                    break;
                }
                yield return null;
            }

            if (haveIntersection)
            {
                ResourceManager.instance.AssetsIntersectionRelease(preScenePaths, newScenePaths);
            }

            ResourceHandleCollection collection = new ResourceHandleCollection();
            collection.ReadyLoad(CommonPrefabPathSetting.instance.physicsCameraRoot.path, 0, AssetType.Prefab);
            collection.ReadyLoad(CommonPrefabPathSetting.instance.virtualCameraRoot.path, 0, AssetType.Prefab);
            if (m_initialType == EInitialType.Sync)
            {
                collection.StartLoadSync();
            }
            else
            {
                collection.StartLoad(null);
                int tickCount = 1000;
                while (!collection.isLoadEnd)
                {
                    if (tickCount-- < 0)
                    {
                        break;
                    }
                    yield return null;
                }
            }
            var mainScene = RuntimeSceneManager.instance.GetScene(stageInfo.sceneAssetPath).unityScene;
            var combatScene = RuntimeSceneManager.instance.GetScene(stageInfo.sceneCombatAssetPath).unityScene;
            BattleShortCut.battleScene.mainScenePath = stageInfo.sceneAssetPath;
            BattleShortCut.battleScene.combatScenePath = stageInfo.sceneCombatAssetPath;
            BattleShortCut.battleScene.UpdateScene(mainScene, SceneType.Main);
            BattleShortCut.battleScene.UpdateScene(combatScene, SceneType.Combat);
            BattleShortCut.battleScene.ToggleScene(SceneType.Main);
            InitSceneCamera(stageInfo);
            InitSceneGridHandler(mainScene, combatScene);
            InitSceneCameraSettings();
            BattleShortCut.battleScene.Refresh();
            yield return null;
        }

        protected override void OnTick()
        {
            base.OnTick();
        }

        private void InitSceneCamera(BattleStageInfo stageInfo)
        {
            GameObject battleCameraObject = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.physicsCameraRoot.path);
            GameObject virualCameraObject = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.virtualCameraRoot.path);
            if (battleCameraObject != null && virualCameraObject != null)
            {
                BattleShortCut.battleScene.InitializeSceneCamera(battleCameraObject, virualCameraObject, stageInfo.initCameraRotateOffset);
            }
        }

        private void InitSceneGridHandler(Scene mainScene, Scene combatScene)
        {
            GameObject sceneConfigRoot = mainScene.FindGameObject("SceneConfigRoot");
            GameObject combatConfigRoot = combatScene.FindGameObject("SceneConfigRoot");
            BattleShortCut.battleScene.InitSceneGridHandler(sceneConfigRoot, combatConfigRoot);
        }

        private void InitSceneCameraSettings()
        {
            BattleStageInfo stageInfo = BattleShortCut.sampleBattle.GetCurStageInfo();
            BattleShortCut.battleScene.sceneCameraHandler.SetCameraGridPositionWithSpeed(stageInfo.initCameraPos, 0, true);


            if (stageInfo.initCameraPos.x < 0 || stageInfo.initCameraPos.x >= stageInfo.groundInfo.width ||
                stageInfo.initCameraPos.y < 0 || stageInfo.initCameraPos.y >= stageInfo.groundInfo.height)
            {
                DebugUI.Log(string.Format("战役阶段初始相机位置超出范围：({0}, {1}), 场景长：{2}，宽：{3}",
                    stageInfo.initCameraPos.x, stageInfo.initCameraPos.y, stageInfo.groundInfo.width, stageInfo.groundInfo.height));
            }


            if (!BattleRetractManager.instance.IsRetracting)
            {
                List<BattleStageDispositionInfo> dispositions = BattleShortCut.sampleBattle.GetCurStageDispositionInfoList();
                if (dispositions != null && dispositions.Count > 0)
                {
                    float zoomPercentValue = CinemachineCameraSetting.instance.SettingData.m_prepareCameraZoomPercentValue;
                    float fovValue = CinemachineCameraSetting.instance.SettingData.m_prepareCameraFieldOfView;
                    BattleShortCut.battleScene.sceneCameraHandler.SetCameraZoomPercentAndFov(zoomPercentValue, fovValue, true);
                }
                else
                {
                    float zoomPercentValue = CinemachineCameraSetting.instance.SettingData.m_defaultZoomPercentValue;
                    float fovValue = CinemachineCameraSetting.instance.SettingData.m_battleCameraFieldOfView;
                    BattleShortCut.battleScene.sceneCameraHandler.SetCameraZoomPercentAndFov(zoomPercentValue, fovValue, true);
                }
            }
        }
    }
}
