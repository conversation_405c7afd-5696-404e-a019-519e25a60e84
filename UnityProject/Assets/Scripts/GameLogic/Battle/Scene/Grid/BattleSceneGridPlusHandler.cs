using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleSceneGridPlusHandler
    {
        private const Single DELAYTIMEFACTOR = 0.05f;
        private Int32 m_width, m_height;
        private Vector2 m_offset;

        private Dictionary<Int32, SceneGridView> m_gridTable = new Dictionary<Int32, SceneGridView>(400);
        private List<SceneGridEffect> m_activeEffects = new List<SceneGridEffect>();
        private Dictionary<GridPosition, GridDataNode> m_activeGrids = new Dictionary<GridPosition, GridDataNode>();


        private Boolean[,] m_gridRangeMatrix;
        private List<TextMesh> m_debugMeshList = new List<TextMesh>();



        private Material m_normalGridMaterial;
        private Material m_dangerGridMaterial;
        private Material m_movePathGridMaterial;
        private Material m_moveRangeGridMaterial;
        private Material m_dashedLineRangeGridMaterial;

        private SceneGridSelected m_selectedGrid;
        private SceneGridSelected m_selectedCommandGrid;



        #region Public Init

        public void Init(GameObject root)
        {
            InternalInitGridView(root);
            InternalInitGridMaterial();
            InternalInitSelectedGrid(root);
        }

        public void OnTick(TimeSlice ts)
        {
            foreach (var grid in m_gridTable)
            {
                grid.Value.OnTick(ts);
            }
            for (int i = 0; i < m_activeEffects.Count;)
            {
                SceneGridEffect gridEffect = m_activeEffects[i];
                if (gridEffect.Invalid)
                {
                    gridEffect.Release();
                    m_activeEffects.Remove(gridEffect);
                }
                else
                {
                    gridEffect.OnTick(ts);
                    ++i;
                }
            }
        }

        public void UnInit()
        {
            m_gridRangeMatrix = null;
            m_activeGrids.Clear();
            foreach (var grid in m_gridTable)
            {
                grid.Value.UnInit();
            }
            m_gridTable.Clear();

            foreach (var gridEffect in m_activeEffects)
            {
                gridEffect.Release();
            }
            m_activeEffects.Clear();

            m_normalGridMaterial = null;
            m_dangerGridMaterial = null;
            m_movePathGridMaterial = null;
            m_moveRangeGridMaterial = null;

            UnityEngine.Object.Destroy(m_selectedGrid.GameObject);
            m_selectedGrid = null;
            UnityEngine.Object.Destroy(m_selectedCommandGrid.GameObject);
            m_selectedCommandGrid = null;

            foreach (var mesh in m_debugMeshList)
            {
                UnityEngine.Object.Destroy(mesh.gameObject);
            }
            m_debugMeshList.Clear();
        }

        #endregion


        #region Internal Init

        private void InternalInitGridView(GameObject root)
        {
            BattleSceneConfig sceneConfig = root.GetComponent<BattleSceneConfig>();
            if (sceneConfig == null)
            {
                return;
            }

            m_width = sceneConfig.width;
            m_height = sceneConfig.height;
            m_offset = sceneConfig.offset;

            BattleGridConfig[] configs = sceneConfig.gridRoot.GetComponentsInChildren<BattleGridConfig>();
            for (int i = 0; i < configs.Length; ++i)
            {
                SceneGridView view = configs[i].GetOrAddComponent<SceneGridView>();
                view.Init();
                Int32 gridIndex = GetGridIndex(view.GridPosition);
                m_gridTable[gridIndex] = view;
            }
            m_gridRangeMatrix = new Boolean[m_width, m_height];
        }

        private void InternalInitGridMaterial()
        {
            m_normalGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.normalGridMaterial.path);
            m_dangerGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.dangerGridMaterial.path);
            m_movePathGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.movePathGridMaterial.path);
            m_moveRangeGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.moveRangeGridMaterial.path);
            m_dashedLineRangeGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.dashedLineRangeGridMaterial.path);
        }

        private void InternalInitSelectedGrid(GameObject root)
        {
            if (m_selectedGrid == null)
            {
                GameObject go = new GameObject("SelectItemDecal");
                m_selectedGrid = new SceneGridSelected(go);
                m_selectedGrid.SetParent(root.transform);
                m_selectedGrid.Hide();
            }

            if (m_selectedCommandGrid == null)
            {
                GameObject go = new GameObject("SelectMoveDecal");
                m_selectedCommandGrid = new SceneGridSelected(go);
                m_selectedCommandGrid.SetParent(root.transform);
                m_selectedCommandGrid.Hide();
            }
        }

        #endregion


        #region Public Method

        public SceneGridView GetGridViewByGridPosition(GridPosition gridPosition)
        {
            return GetGridViewByPosition(gridPosition.x, gridPosition.y);
        }
        public SceneGridView GetGridViewByPosition(Int32 x, Int32 y)
        {
            Int32 gridIndex = GetGridIndex(x, y);
            if (m_gridTable.TryGetValue(gridIndex, out SceneGridView value))
            {
                return value;
            }
            return null;
        }
        public GridPosition GetGridPosition(Vector3 worldPosition)
        {
            GridPosition result;
            Single gridSize = BattleParamSetting.instance.gridSize;
            Single posX = worldPosition.x - m_offset.x + gridSize / 2;
            Single posY = worldPosition.z - m_offset.y + gridSize / 2;
            Int32 gridX = Mathf.FloorToInt(posX / gridSize);
            Int32 gridY = Mathf.FloorToInt(posY / gridSize);
            SceneGridView gridView = GetGridViewByPosition(gridX, gridY);
            if (gridView)
            {
                result = gridView.GridPosition;
            }
            else
            {
                result = GridPosition.invalid;
            }
            return result;
        }
        public Vector3 GetGridWorldPosition(GridPosition gridPos)
        {
            return GetGridWorldPosition(gridPos.x, gridPos.y);
        }
        public Vector3 GetGridWorldPosition(Int32 x, Int32 y)
        {
            SceneGridView gridView = GetGridViewByPosition(x, y);
            if (gridView != null)
            {
                return gridView.Position;
            }
            Debug.LogError(string.Format("[BattleSceneGridPlusHandler] GetGridWorldPosition: 格子不存在({0},{1})", x, y));
            return Vector3.one * -1;
        }

        #endregion


        #region GridEffect

        public void AddGirdEffect(GridPosition grid, GridEffectPattern efxType, Single delayTime = 0, Int32 rotation = 0)
        {
            SceneGridEffect gridEffect = ClassPoolManager.instance.Fetch<SceneGridEffect>();
            gridEffect.Init();
            Vector3 position = GetGridWorldPosition(grid);
            gridEffect.UpdateGridEffectInfo(grid, position, efxType, delayTime, rotation);
            m_activeEffects.Add(gridEffect);
        }

        public void RemoveGridEffect(GridPosition grid, GridEffectPattern efxType)
        {
            foreach (var gridEffect in m_activeEffects)
            {
                if (gridEffect.GridPosition == grid)
                {
                    gridEffect.RemoveGridEffect(efxType);
                }
            }
        }

        public void RemoveAllGridEffect(GridEffectPattern efxType)
        {
            foreach (var tempGridEffect in m_activeEffects)
            {
                tempGridEffect.RemoveGridEffect(efxType);
            }
        }

        public void RemoveAllGridAllEffect()
        {
            foreach (var tempGridEffect in m_activeEffects)
            {
                tempGridEffect.RemoveGridEffect(~GridEffectPattern.None);
            }
        }

        #endregion


        #region GridShowLogic

        public void HideAllTypeGrid(Boolean immediately = false)
        {
            ShowOrHideSelectGrid(false, GridPosition.invalid);
            ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
            HideAllNormalGridInternal(immediately);
            for (Int32 i = 1; i < (Int32)GridType.Max; i++)
            {
                HideAllGridByTypeInternal((GridType)i, immediately);
            }
        }

        public void HideAllGridByType(GridType gridType, Boolean immediately = false)
        {
            if (gridType == GridType.Normal)
            {
                HideAllNormalGridInternal(immediately);
                HideAllGridByTypeInternal(GridType.MultiGridEntityRange, true);
            }
            else
            {
                HideAllGridByTypeInternal(gridType, immediately);
            }
        }

        public void ShowOrHideSelectGrid(bool isShow, GridPosition gridPos)
        {
            if (isShow && gridPos.isValid)
            {
                SceneGridView gridView = GetGridViewByGridPosition(gridPos);
                Vector3 position = GetGridWorldPosition(gridPos);
                m_selectedGrid.UpdateGridMeshAndPosition(gridView.GridMesh, m_normalGridMaterial, position, SceneGridUtility.GetSelectedGridVertexColor());
                m_selectedGrid.Show();
            }
            else
            {
                m_selectedGrid.Hide();
            }
        }

        public void ShowOrHideSelectCommandGrid(bool isShow, GridPosition gridPos)
        {
            if (isShow && gridPos.isValid)
            {
                SceneGridView gridView = GetGridViewByGridPosition(gridPos);
                Vector3 position = GetGridWorldPosition(gridPos);
                Color vertexColor = AutoBattleOpMode.Instance.IsProcessCommandMove ? SceneGridUtility.GetCommandSelectedGridVertexColor() : SceneGridUtility.GetSelectedGridVertexColor();
                m_selectedCommandGrid.UpdateGridMeshAndPosition(gridView.GridMesh, m_normalGridMaterial, position, vertexColor);
                m_selectedCommandGrid.Show();
            }
            else
            {
                m_selectedCommandGrid.Hide();
            }
        }


        /// <summary> 普通技能预览 (预览模式使用，非技能释放流程) </summary>
        public void ShowNormalSkillMoveRange(GridPosition originGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> skillSelectRange, Boolean immediately = false)
        {
            HideAllNormalGridInternal(true);
            CollectMoveRangeGridInternal(originGrid, moveRange, null, immediately);
            CollectNormalSkillSelectGridInternal(originGrid, skillSelectRange, MoveRangeFilter, immediately);
            ShowGridInternal(immediately);
        }

        public void ShowMove_MaxSkillRange(GridPosition originGrid, GridPosition targetGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> maxRange, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, targetGrid, moveRange, null, maxRange, null, immediately);
        }
        public void ShowMove_MinSkillRange(GridPosition originGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> minRange, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, GridPosition.invalid, moveRange, minRange, null, null, immediately);
        }
        public void ShowMove_MinMaxSkillRange(GridPosition originGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> minRange, List<SkillGridPosition> maxRange, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, GridPosition.invalid, moveRange, minRange, maxRange, null, immediately);
        }
        public void ShowMove_MinMaxEffectSkillRange(GridPosition originGrid, GridPosition targetGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> minRange, List<SkillGridPosition> maxRange, List<SkillGridPosition> skillEffect, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, targetGrid, moveRange, minRange, maxRange, skillEffect, immediately);
        }
        public void ShowMove_EffectSkillRange(GridPosition originGrid, GridPosition targetGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> skillEffect, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, targetGrid, moveRange, null, null, skillEffect, immediately);
        }
        public void ShowMove_Max_EffectSkillRange(GridPosition originGrid, GridPosition targetGrid, List<MoveGridPosition> moveRange, List<SkillGridPosition> maxRange, List<SkillGridPosition> skillEffect, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, targetGrid, moveRange, null, maxRange, skillEffect, immediately);
        }
        public void ShowMin_EffectSkillRange(GridPosition originGrid, List<SkillGridPosition> minRange, List<SkillGridPosition> skillEffect, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, GridPosition.invalid, null, minRange, null, skillEffect, immediately);
        }
        public void ShowMax_EffectSkillRange(GridPosition originGrid, GridPosition targetGrid, List<SkillGridPosition> maxRange, List<SkillGridPosition> skillEffect, Boolean immediately = false)
        {
            ShowSkillRange(originGrid, targetGrid, null, null, maxRange, skillEffect, immediately);
        }

        public void ShowSkillRange(
            GridPosition originGrid,
            GridPosition targetGrid,
            List<MoveGridPosition> moveRange,
            List<SkillGridPosition> minSkillRange,
            List<SkillGridPosition> maxSkillRange,
            List<SkillGridPosition> skillEffectRange,
            Boolean immediately)
        {
            HideAllNormalGridInternal(true);
            CollectMoveRangeGridInternal(originGrid, moveRange, null, immediately);
            CollectMinSkillSelectGridInternal(originGrid, minSkillRange, null, immediately);
            CollectMaxSkillSelectGridAndEffectInternal(originGrid, targetGrid, maxSkillRange, immediately);
            CollectSkillEffectRangeGridInternal(originGrid, skillEffectRange, null, immediately);
            ShowGridInternal(immediately);
        }

        /// <summary> 占用多格子的角色 - 边框绘制 </summary>
        public void ShowMultiGridEntityRange(List<EntityView> entityViews)
        {
            List<GridPosition> posList = new List<GridPosition>();
            foreach (EntityView entityView in entityViews)
            {
                posList.Clear();
                entityView.CollectOccupiedPos(posList);
                ResetGridRangeMatrix();
                foreach (GridPosition grid in posList)
                {
                    GridPosition gridPos = grid;
                    SetGridRangeMatrixValueSafely(gridPos.x, gridPos.y, true);
                }
                foreach (GridPosition grid in posList)
                {
                    GridPosition gridPos = grid;
                    SceneGridView gridView = GetGridViewByGridPosition(gridPos);
                    if (gridView != null)
                    {
                        Boolean forward = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y + 1);
                        Boolean back = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y - 1);
                        Boolean left = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 0);
                        Boolean right = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 0);
                        Boolean forwardRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 1);
                        Boolean forwardLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 1);
                        Boolean backRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y - 1);
                        Boolean backLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y - 1);
                        SceneGridUtility.CalculateRangeGridPatternAndRotation(forward, back, left, right,
                            forwardRight, forwardLeft, backRight, backLeft,
                            out Single rotate, out RangeGridPattern pattern);
                        Color vertexColor = SceneGridUtility.GetMoveRangeGridVertexColor(pattern);
                        gridView.ShowOrHideMultiGridRangeGrid(true, m_dashedLineRangeGridMaterial, vertexColor, rotate);
                    }
                }
            }
        }

        public void ShowDangerRange(List<GridPosition> dangerGrids)
        {
            foreach (GridPosition grid in dangerGrids)
            {
                if (m_activeGrids.TryGetValue(grid, out GridDataNode gridData))
                {
                    if (gridData.IsExistGridPattern(GridPattern.Move) || gridData.IsExistGridPattern(GridPattern.MoveJump))
                    {
                        SceneGridView gridView = GetGridViewByGridPosition(grid);
                        gridView.ShowOrHideDangerGrid(true, m_normalGridMaterial, SceneGridUtility.GetDangerRangeGridVertexColor(), 0);
                    }
                }
            }
        }

        public void ShowMovePathGrid(List<GridPosition> movePathGrids)
        {
            HideAllGridByType(GridType.MovePath, true);
            if (movePathGrids == null || movePathGrids.Count <= 1)
            {
                return;
            }

            ResetGridRangeMatrix();
            foreach (GridPosition gridPos in movePathGrids)
            {
                SetGridRangeMatrixValueSafely(gridPos.x, gridPos.y, true);
            }
            for (Int32 i = 0; i < movePathGrids.Count; i++)
            {
                GridPosition grid = movePathGrids[i];
                SceneGridView gridView = GetGridViewByGridPosition(grid);
                if (gridView != null)
                {
                    Boolean forward = GetGridRangeMatrixValueSafely(grid.x + 0, grid.y + 1);
                    Boolean back = GetGridRangeMatrixValueSafely(grid.x + 0, grid.y - 1);
                    Boolean left = GetGridRangeMatrixValueSafely(grid.x - 1, grid.y + 0);
                    Boolean right = GetGridRangeMatrixValueSafely(grid.x + 1, grid.y + 0);
                    SceneGridUtility.CalculateMovePathGridPatternAndRotation(i, forward, back, left, right, out Single rotate, out MovePathGridPattern pattern);
                    gridView.ShowOrHideMovePathGrid(true, m_movePathGridMaterial, SceneGridUtility.GetPathGridVertexColor(pattern), rotate);
                }
            }
        }

        #endregion



        #region Private Method

        /// <summary> 最终显示格子 </summary>
        private void ShowGridInternal(Boolean immediately = false)
        {
            List<GridDataNode> moveRanges = new List<GridDataNode>();
            foreach (var item in m_activeGrids)
            {
                GridPosition gridPos = item.Key;
                GridDataNode gridData = item.Value;
                if (gridData.IsExistGridPattern(GridPattern.MoveRange))
                {
                    moveRanges.Add(gridData);
                }
                SceneGridView gridView = GetGridViewByGridPosition(gridPos);
                if (gridView != null)
                {
                    gridView.ShowNormalGrid(m_normalGridMaterial, GetNormalGridVertexColor(gridData), gridData.DelayShowTime, immediately);
                }
            }

            if (moveRanges.Count > 0)
            {
                ResetGridRangeMatrix();
                foreach (GridDataNode grid in moveRanges)
                {
                    GridPosition gridPos = grid.m_grid;
                    SetGridRangeMatrixValueSafely(gridPos.x, gridPos.y, true);
                }
                foreach (GridDataNode grid in moveRanges)
                {
                    GridPosition gridPos = grid.m_grid;
                    SceneGridView gridView = GetGridViewByGridPosition(gridPos);
                    if (gridView != null)
                    {
                        Boolean forward = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y + 1);
                        Boolean back = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y - 1);
                        Boolean left = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 0);
                        Boolean right = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 0);
                        Boolean forwardRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 1);
                        Boolean forwardLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 1);
                        Boolean backRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y - 1);
                        Boolean backLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y - 1);
                        SceneGridUtility.CalculateRangeGridPatternAndRotation(forward, back, left, right,
                            forwardRight, forwardLeft, backRight, backLeft,
                            out Single rotate, out RangeGridPattern pattern);
                        Color vertexColor = SceneGridUtility.GetMoveRangeGridVertexColor(pattern);
                        gridView.ShowOrHideMoveRangeGrid(true, m_moveRangeGridMaterial, vertexColor, rotate, grid.DelayShowTime, immediately);
                    }
                }
            }
        }

        /// <summary> 获取纹理顶点色 </summary>
        private Color GetNormalGridVertexColor(GridDataNode gridData)
        {
            Color vertexColor = new Color(0, 0, 0);
            if (gridData.IsExistGridPattern(GridPattern.SkillHurtRange))
            {
                vertexColor = new Color(2, 1, 0);
            }
            else if (gridData.IsExistGridPattern(GridPattern.SkillCureRange))
            {
                vertexColor = new Color(1, 1, 0);
            }
            else if (gridData.IsExistGridPattern(GridPattern.SkillMoveRange))
            {
                vertexColor = new Color(1, 1, 0);
            }
            else if (gridData.IsExistGridPattern(GridPattern.Skill))
            {
                vertexColor = new Color(0, 1, 0);
            }
            else if (gridData.IsExistGridPattern(GridPattern.Attack))
            {
                vertexColor = new Color(2, 1, 0);
            }
            else if (gridData.IsExistGridPattern(GridPattern.Move))
            {
                vertexColor = new Color(2, 0, 0);
            }
            else if (gridData.IsExistGridPattern(GridPattern.MoveJump))
            {
                vertexColor = new Color(1, 0, 0);
            }
            return vertexColor;
        }

        private void HideAllGridByTypeInternal(GridType gridType, Boolean immediately = false)
        {
            foreach (var item in m_gridTable)
            {
                SceneGridView gridView = GetGridViewByGridPosition(item.Value.GridPosition);
                switch (gridType)
                {
                    case GridType.DangerRange:
                        gridView?.ShowOrHideDangerGrid(false, null, Color.black, 0);
                        break;
                    case GridType.MovePath:
                        gridView?.ShowOrHideMovePathGrid(false, null, Color.black, 0);
                        break;
                    case GridType.MoveRange:
                        gridView?.ShowOrHideMoveRangeGrid(false, null, Color.black, 0, 0, false);
                        break;
                    case GridType.MultiGridEntityRange:
                        gridView?.ShowOrHideMultiGridRangeGrid(false, null, Color.black, 0);
                        break;
                }
            }
        }

        private void HideAllNormalGridInternal(Boolean immediately = false, Boolean removeEffect = true)
        {
            if (removeEffect)
            {
                RemoveAllGridEffect(GridEffectPattern.SkillEffect);
            }

            foreach (var item in m_activeGrids)
            {
                SceneGridView gridView = GetGridViewByGridPosition(item.Value.m_grid);
                if (gridView)
                {
                    gridView.HideNormalGrid(item.Value.DelayHideTime, immediately);
                    gridView.ShowOrHideMoveRangeGrid(false, null, Color.black, 0, item.Value.DelayHideTime);
                }
            }
            m_activeGrids.Clear();
            GridData.ResetMaxDelayShowTime();
        }

        /// <summary> 移动范围过滤器 </summary>
        private Boolean MoveRangeFilter(GridPosition grid)
        {
            if (m_activeGrids.TryGetValue(grid, out var gridData) && gridData.IsExistGridPattern(GridPattern.MoveRange))
            {
                return false;
            }
            return true;
        } 

        /// <summary> 移动范围 </summary>
        private void CollectMoveRangeGridInternal(GridPosition originGrid, List<MoveGridPosition> moveRange, Func<GridPosition, Boolean> filter, Boolean immediately)
        {
            if (moveRange == null || moveRange.Count == 0)
            {
                return;
            }

            for (int i = 0; i < moveRange.Count; ++i)
            {
                if (filter != null && !filter(moveRange[i].m_grid))
                {
                    continue;
                }
                GridPosition grid = moveRange[i].m_grid;
                GridPattern pattern = moveRange[i].m_stayFlag ? GridPattern.Move : GridPattern.MoveJump;
                if (!m_activeGrids.TryGetValue(grid, out GridDataNode result))
                {
                    Single delayShowTime = immediately ? 0 : originGrid.DistanceTo(grid) * DELAYTIMEFACTOR;
                    result = new GridDataNode(grid, pattern, delayShowTime);
                    m_activeGrids.Add(grid, result);
                }
                else
                {
                    result.AddGridPattern(pattern);
                }
            }
        }

        /// <summary> 普攻技能范围 (预览模式使用，非技能释放流程)  </summary>
        private void CollectNormalSkillSelectGridInternal(GridPosition originGrid, List<SkillGridPosition> maxSkillSelectRange, Func<GridPosition, Boolean> filter, Boolean immediately)
        {
            if (maxSkillSelectRange == null || maxSkillSelectRange.Count == 0)
            {
                return;
            }
            for (int i = 0; i < maxSkillSelectRange.Count; ++i)
            {
                GridPosition grid = maxSkillSelectRange[i].m_grid;
                if (filter != null && !filter(grid))
                {
                    continue;
                }
                GridPattern gridPattern = GridPattern.Attack;// GetSkillSelectGridPattern(skillSelectRange[i].m_indicatorType);
                if (!m_activeGrids.TryGetValue(grid, out GridDataNode result))
                {
                    Single delayShowTime = immediately ? 0 : originGrid.DistanceTo(grid) * DELAYTIMEFACTOR;
                    result = new GridDataNode(grid, gridPattern, delayShowTime);
                    m_activeGrids.Add(grid, result);
                }
                else
                {
                    result.AddGridPattern(gridPattern);
                }
            }
        }

        /// <summary> 最小技能选择范围 (地面黄底贴片) </summary>
        private void CollectMinSkillSelectGridInternal(GridPosition originGrid, List<SkillGridPosition> minSkillSelectRange, Func<GridPosition, Boolean> filter, Boolean immediately)
        {
            if (minSkillSelectRange == null || minSkillSelectRange.Count == 0)
            {
                return;
            }

            for (int i = 0; i < minSkillSelectRange.Count; ++i)
            {
                GridPosition grid = minSkillSelectRange[i].m_grid;
                if (filter != null && !filter(grid))
                {
                    continue;
                }
                //GridPattern gridPattern = GetSkillSelectGridPattern(minSkillSelectRange[i].m_indicatorType);
                if (!m_activeGrids.TryGetValue(grid, out GridDataNode result))
                {
                    Single delayShowTime = immediately ? 0 : originGrid.DistanceTo(grid) * DELAYTIMEFACTOR;
                    result = new GridDataNode(grid, GridPattern.Skill, delayShowTime);
                    m_activeGrids.Add(grid, result);
                }
                else
                {
                    result.AddGridPattern(GridPattern.Skill);
                }
            }
        }

        /// <summary> 最大技能选择范围 (地面黄底贴片) </summary>
        private void CollectMaxSkillSelectGridInternal(GridPosition originGrid, List<SkillGridPosition> maxSkillSelectRange, Func<GridPosition, Boolean> filter, Boolean immediately)
        {
            if (maxSkillSelectRange == null || maxSkillSelectRange.Count == 0)
            {
                return;
            }
            for (int i = 0; i < maxSkillSelectRange.Count; ++i)
            {
                GridPosition grid = maxSkillSelectRange[i].m_grid;
                if (filter != null && !filter(grid))
                {
                    continue;
                }
                if (m_activeGrids.TryGetValue(grid, out GridDataNode result))
                {
                    result.AddGridPattern(GridPattern.Skill);
                }
            }
        }

        /// <summary> 最大技能选择范围-可选目标特效 </summary>
        private void CollectMaxSkillSelectGridAndEffectInternal(GridPosition originGrid, GridPosition targetPosition, List<SkillGridPosition> maxSkillSelectRange, Boolean immediately)
        {
            if (maxSkillSelectRange == null || maxSkillSelectRange.Count == 0)
            {
                return;
            }

            for (int i = 0; i < maxSkillSelectRange.Count; ++i)
            {
                if (maxSkillSelectRange[i].m_selectFlag)
                {
                    GridPosition grid = maxSkillSelectRange[i].m_grid;
                    GridEffectPattern effectPattern = GetSkillSelectEffectPattern(maxSkillSelectRange[i].m_indicatorType, grid == targetPosition);
                    Single delayShowTime = immediately ? 0 : originGrid.DistanceTo(grid) * DELAYTIMEFACTOR;
                    Int32 rotation = GetSkillGridEffectRotation(maxSkillSelectRange[i], originGrid);
                    AddGirdEffect(grid, effectPattern, delayShowTime, rotation);
                }

            }
        }

        /// <summary> 技能作用范围 </summary>
        private void CollectSkillEffectRangeGridInternal(GridPosition originGrid, List<SkillGridPosition> skillFinalRange, Func<GridPosition, Boolean> filter, Boolean immediately)
        {
            if (skillFinalRange == null || skillFinalRange.Count == 0)
            {
                return;
            }

            for (int i = 0; i < skillFinalRange.Count; ++i)
            {
                GridPosition grid = skillFinalRange[i].m_grid;
                if (filter != null && !filter(grid))
                {
                    continue;
                }
                GridPattern gridPattern = GetSkillFinalGridPattern(skillFinalRange[i].m_indicatorType);
                if (!m_activeGrids.TryGetValue(grid, out GridDataNode result))
                {
                    Single delayShowTime = immediately ? 0 : originGrid.DistanceTo(grid) * DELAYTIMEFACTOR;
                    result = new GridDataNode(grid, gridPattern, delayShowTime);
                    m_activeGrids.Add(grid, result);
                }
                else
                {
                    result.AddGridPattern(gridPattern);
                }
            }
        }



        /// <summary> 技能目标选择-特效旋转角度 </summary>
        private Int32 GetSkillGridEffectRotation(SkillGridPosition skillGrid, GridPosition originGrid)
        {
            Int32 rotation = 0;
            if (skillGrid.m_indicatorType == SkillIndicatorType.Dir)
            {
                GridPosition dir = originGrid - skillGrid.m_grid;
                rotation = dir.x == 0 ? dir.y > 0 ? 0 : 180 : dir.x > 0 ? 90 : 270;
            }
            return rotation;
        }

        /// <summary> 技能目标选择-特效样式 </summary>
        private GridEffectPattern GetSkillSelectEffectPattern(SkillIndicatorType skillEffectType, Boolean bold = false)
        {
            GridEffectPattern gridEffectPattern = skillEffectType switch
            {
                SkillIndicatorType.Helpful => bold ? GridEffectPattern.SkillEffectGreenBold : GridEffectPattern.SkillEffectGreen,
                SkillIndicatorType.Harmful => bold ? GridEffectPattern.SkillEffectRedBold : GridEffectPattern.SkillEffectRed,
                SkillIndicatorType.Dir => GridEffectPattern.SkillEffectDir,
                SkillIndicatorType.Other => bold ? GridEffectPattern.SkillEffectBlueBold : GridEffectPattern.SkillEffectBlue,
                _ => GridEffectPattern.None,
            };
            return gridEffectPattern;
        }

        /// <summary> 技能目标选择-格子样式 </summary>
        private GridPattern GetSkillSelectGridPattern(SkillIndicatorType skillEffectType)
        {
            GridPattern gridPattern = skillEffectType switch
            {
                SkillIndicatorType.Helpful => GridPattern.SkillCureRange,
                SkillIndicatorType.Harmful => GridPattern.SkillHurtRange,
                SkillIndicatorType.Dir => GridPattern.Skill,
                SkillIndicatorType.Other => GridPattern.SkillMoveRange,
                _ => GridPattern.None
            };
            return gridPattern;
        }

        /// <summary> 技能作用范围-格子样式 </summary>
        private GridPattern GetSkillFinalGridPattern(SkillIndicatorType skillEffectType)
        {
            GridPattern gridPattern = skillEffectType switch
            {
                SkillIndicatorType.Helpful => GridPattern.SkillCureRange,
                SkillIndicatorType.Harmful => GridPattern.SkillHurtRange,
                SkillIndicatorType.Dir => GridPattern.SkillHurtRange,
                SkillIndicatorType.Other => GridPattern.SkillMoveRange,
                _ => GridPattern.None
            };
            return gridPattern;
        }

        private Int32 GetGridIndex(GridPosition gridPos)
        {
            return GetGridIndex(gridPos.x, gridPos.y);
        }

        private Int32 GetGridIndex(Int32 x, Int32 y)
        {
            return x * 1000 + y;
        }

        private void ResetGridRangeMatrix()
        {
            for (Int32 i = 0; i < m_width; i++)
            {
                for (Int32 j = 0; j < m_height; j++)
                {
                    m_gridRangeMatrix[i, j] = false;
                }
            }
        }

        private void SetGridRangeMatrixValueSafely(Int32 x, Int32 y, Boolean value)
        {
            if (x >= 0 && x < m_width && y >= 0 && y < m_height)
            {
                m_gridRangeMatrix[x, y] = value;
            }
        }

        private Boolean GetGridRangeMatrixValueSafely(Int32 x, Int32 y, Boolean defaultValue = false)
        {
            if (x < 0 || x >= m_width || y < 0 || y >= m_height)
            {
                return defaultValue;
            }
            return m_gridRangeMatrix[x, y];
        }

        #endregion


        #region Debug

        public void UpdateGridDebugText(bool isShow)
        {
            if (m_debugMeshList.Count == 0)
            {
                foreach (var grid in m_gridTable)
                {
                    GameObject go = new GameObject($"DebugGrid{grid.Value.GridPosition}", typeof(TextMesh));
                    TextMesh textMesh = go.GetComponent<TextMesh>();
                    textMesh.text = string.Format("{0},{1}", grid.Value.GridPosition.x, grid.Value.GridPosition.y);
                    textMesh.characterSize = 0.1f;
                    textMesh.fontSize = 30;
                    textMesh.color = Color.black;
                    textMesh.anchor = TextAnchor.MiddleCenter;
                    go.SetParent(grid.Value.gameObject);
                    go.transform.localPosition = Vector3.up * 0.05f;
                    go.transform.localEulerAngles = new Vector3(90, 0, 0);
                    m_debugMeshList.Add(textMesh);
                }
            }
            foreach (TextMesh textMesh in m_debugMeshList)
            {
                textMesh.SetActiveSafely(isShow);
            }
        }

        #endregion
    }
}
