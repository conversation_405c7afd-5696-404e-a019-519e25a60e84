using System;
using System.Collections.Generic;
using Phoenix.Core;
using UnityEngine;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class SceneGridEffect : ClassPoolObj
    {
        protected Boolean m_enable;
        protected Boolean m_dirty;
        protected GridPosition m_gridPosition;
        protected Vector3 m_position;
        protected GridEffectPattern m_gridEffect;
        protected Single m_delayTime;
        protected Int32 m_rotation;
        protected Dictionary<String, GameObject> m_effects = new Dictionary<String, GameObject>();
        protected Vector3 Offset { get { return new Vector3(0, 0.08f, 0); } }

        public GridPosition GridPosition { get { return m_gridPosition; } }
        public Boolean Invalid { get { return m_gridEffect == GridEffectPattern.None; } }

        public override void OnRelease()
        {
            m_dirty = false;
            m_enable = false;
            m_gridPosition = GridPosition.invalid;
            m_gridEffect = GridEffectPattern.None;
            m_delayTime = 0;
            m_rotation = 0;

            foreach (var item in m_effects)
            {
                ResourceHandleManager.instance.DespawnGameObject(item.Value);
            }
            m_effects.Clear();
        }
        public void Init()
        {
        }

        public void UpdateGridEffectInfo(GridPosition gridPosition, Vector3 position, GridEffectPattern gridEffect, float delayTime = 0, int rotation = 0)
        {
            m_dirty = true;
            m_gridPosition = gridPosition;
            m_position = position;
            m_gridEffect = gridEffect;
            m_delayTime = delayTime;
            m_rotation = rotation;
        }
        public void AddGridEffect(GridEffectPattern gridEffect) { m_gridEffect |= gridEffect; m_dirty = true; }
        public void RemoveGridEffect(GridEffectPattern gridEffect) { m_gridEffect &= ~gridEffect; m_dirty = true; }
        public void OnTick(TimeSlice ts)
        {
            if (m_dirty)
            {
                m_delayTime -= ts.deltaTime;
                if (m_delayTime <= 0)
                {
                    InternalShow();
                }
            }
        }


        private void InternalShow()
        {
            m_dirty = false;
            InternalUpdate(GridEffectPattern.Actived, IsShow(m_gridEffect, GridEffectPattern.Actived));
            InternalUpdate(GridEffectPattern.Activable, IsShow(m_gridEffect, GridEffectPattern.Activable));
            InternalUpdate(GridEffectPattern.SkillEffectRed, IsShow(m_gridEffect, GridEffectPattern.SkillEffectRed));
            InternalUpdate(GridEffectPattern.SkillEffectRedBold, IsShow(m_gridEffect, GridEffectPattern.SkillEffectRedBold));
            InternalUpdate(GridEffectPattern.SkillEffectGreen, IsShow(m_gridEffect, GridEffectPattern.SkillEffectGreen));
            InternalUpdate(GridEffectPattern.SkillEffectGreenBold, IsShow(m_gridEffect, GridEffectPattern.SkillEffectGreenBold));
            InternalUpdate(GridEffectPattern.SkillEffectBlue, IsShow(m_gridEffect, GridEffectPattern.SkillEffectBlue));
            InternalUpdate(GridEffectPattern.SkillEffectBlueBold, IsShow(m_gridEffect, GridEffectPattern.SkillEffectBlueBold));
            InternalUpdate(GridEffectPattern.SkillEffectYellow, IsShow(m_gridEffect, GridEffectPattern.SkillEffectYellow));
            InternalUpdate(GridEffectPattern.SkillEffectDir, IsShow(m_gridEffect, GridEffectPattern.SkillEffectDir));
            InternalUpdate(GridEffectPattern.Setup_Red, IsShow(m_gridEffect, GridEffectPattern.Setup_Red));
            InternalUpdate(GridEffectPattern.Setup_Green, IsShow(m_gridEffect, GridEffectPattern.Setup_Green));
            InternalUpdate(GridEffectPattern.Setup_Red, IsShow(m_gridEffect, GridEffectPattern.Setup_Red));
        }
        private void InternalUpdate(GridEffectPattern gridEffect, Boolean isShow)
        {
            String assetPath = GetGridEffectPath(gridEffect);
            if (String.IsNullOrEmpty(assetPath))
            {
                return;
            }
            String effectKey = String.Format("{0}_{1}", gridEffect, assetPath);
            if (isShow)
            {
                if (!m_effects.TryGetValue(effectKey, out GameObject go))
                {
                    go = ResourceHandleManager.instance.SpawnGameObject(assetPath);
                    m_effects[effectKey] = go;
                }
                go.transform.position = m_position + Offset;
                go.transform.rotation = Quaternion.Euler(new Vector3(0, m_rotation, 0));
            }
            else
            {
                if (m_effects.TryGetValue(effectKey, out GameObject go))
                {
                    ResourceHandleManager.instance.DespawnGameObject(go);
                    m_effects.Remove(effectKey);
                }
            }
        }
        private Boolean IsShow(GridEffectPattern gridEffect, GridEffectPattern targetGridEffect)
        {
            return (gridEffect & targetGridEffect) == targetGridEffect;
        }
        private String GetGridEffectPath(GridEffectPattern gridEffect)
        {
            String path = String.Empty;
            switch (gridEffect)
            {
                case GridEffectPattern.Setup_Red:
                    path = CommonPrefabPathSetting.instance.gridFxOfSetupRed.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Setup_Red.prefab";
                    break;
                case GridEffectPattern.Setup_Green:
                    path = CommonPrefabPathSetting.instance.gridFxOfSetupGreen.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Setup_Green.prefab";
                    break;
                case GridEffectPattern.Actived:
                    path = CommonPrefabPathSetting.instance.gridFxOfActive.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Activable.prefab";
                    break;
                case GridEffectPattern.Activable:
                    path = CommonPrefabPathSetting.instance.gridFxOfActivable.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Setup_Green.prefab";
                    break;
                case GridEffectPattern.SkillEffectRed:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillRed.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Red.prefab";
                    break;
                case GridEffectPattern.SkillEffectRedBold:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillRedBold.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Red.prefab";
                    break;
                case GridEffectPattern.SkillEffectGreen:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillGreen.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Blue.prefab";
                    break;
                case GridEffectPattern.SkillEffectGreenBold:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillGreenBold.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Blue.prefab";
                    break;
                case GridEffectPattern.SkillEffectBlue:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillBlue.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Blue.prefab";
                    break;
                case GridEffectPattern.SkillEffectBlueBold:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillBlueBold.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Blue.prefab";
                    break;
                case GridEffectPattern.SkillEffectYellow:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillYellow.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Blue.prefab";
                    break;
                case GridEffectPattern.SkillEffectDir:
                    path = CommonPrefabPathSetting.instance.gridFxOfSkillDirArrow.path;
                    //assetPath = "Assets/Res/Fx/GridEffect/Skill_Blue.prefab";
                    break;
            }
            return path;
        }
    }
}
