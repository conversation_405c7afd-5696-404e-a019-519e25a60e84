//using System;
//using System.Collections.Generic;
//using UnityEngine;
//using Phoenix.Battle;
//using Phoenix.ConfigData;
//using Phoenix.Core;

//namespace Phoenix.GameLogic.Battle
//{
//    public class BattleSceneGridHandler
//    {
//        private const Single DELAYTIMEFACTOR = 0.05f;
//        private Int32 m_width, m_height;
//        private Vector2 m_offset;

//        private Dictionary<Int32, SceneGridView> m_gridContainer = new Dictionary<Int32, SceneGridView>(400);
//        private Dictionary<GridPosition, GridData> m_currentActiveGrids = new Dictionary<GridPosition, GridData>();
//        private List<SceneGridEffect> m_currentActiveGridEffects = new List<SceneGridEffect>();
//        private Boolean[,] m_gridRangeMatrix;


//        private List<TextMesh> m_debugMeshList = new List<TextMesh>();
//        private List<TextMesh> m_hitBlockMeshList = new List<TextMesh>();


//        private Material m_normalGridMaterial;
//        private Material m_dangerGridMaterial;
//        private Material m_movePathGridMaterial;
//        private Material m_moveRangeGridMaterial;
//        private Material m_dashedLineRangeGridMaterial;
//        private SceneGridSelected m_selectedGrid;
//        private SceneGridSelected m_selectedCommandGrid;


//        #region Public Init

//        public void Init(GameObject root)
//        {
//            InternalInitGridView(root);
//            InternalInitGridMaterial();
//            InternalInitSelectedGrid(root);
//            InitHitPointMesh();
//            EventManager.instance.RegisterListener(EventID.Entity_BlockHitPoint_Changed, UpdateHitPointMesh);
//        }

//        public void OnTick(TimeSlice ts)
//        {
//            foreach (var grid in m_gridContainer)
//            {
//                grid.Value.OnTick(ts);
//            }
//            for (int i = 0; i < m_currentActiveGridEffects.Count;)
//            {
//                SceneGridEffect gridEffect = m_currentActiveGridEffects[i];
//                if (gridEffect.Invalid)
//                {
//                    gridEffect.Release();
//                    m_currentActiveGridEffects.Remove(gridEffect);
//                }
//                else
//                {
//                    gridEffect.OnTick(ts);
//                    ++i;
//                }
//            }
//        }

//        public void UnInit()
//        {
//            EventManager.instance.UnRegisterListener(EventID.Entity_BlockHitPoint_Changed, UpdateHitPointMesh);
//            m_gridRangeMatrix = null;
//            m_currentActiveGrids.Clear();
//            foreach (var grid in m_gridContainer)
//            {
//                grid.Value.UnInit();
//            }
//            m_gridContainer.Clear();

//            foreach (var gridEffect in m_currentActiveGridEffects)
//            {
//                gridEffect.Release();
//            }
//            m_currentActiveGridEffects.Clear();

//            m_normalGridMaterial = null;
//            m_dangerGridMaterial = null;
//            m_movePathGridMaterial = null;
//            m_moveRangeGridMaterial = null;

//            UnityEngine.Object.Destroy(m_selectedGrid.GameObject);
//            m_selectedGrid = null;
//            UnityEngine.Object.Destroy(m_selectedCommandGrid.GameObject);
//            m_selectedCommandGrid = null;

//            foreach (var mesh in m_debugMeshList)
//            {
//                UnityEngine.Object.Destroy(mesh.gameObject);
//            }
//            m_debugMeshList.Clear();

//            foreach (var mesh in m_hitBlockMeshList)
//            {
//                UnityEngine.Object.Destroy(mesh.gameObject);
//            }
//            m_hitBlockMeshList.Clear();
//        }

//        #endregion


//        #region Internal Init

//        private void InternalInitGridView(GameObject root)
//        {
//            BattleSceneConfig sceneConfig = root.GetComponent<BattleSceneConfig>();
//            if (sceneConfig == null)
//            {
//                return;
//            }

//            m_width = sceneConfig.width;
//            m_height = sceneConfig.height;
//            m_offset = sceneConfig.offset;

//            BattleGridConfig[] configs = sceneConfig.gridRoot.GetComponentsInChildren<BattleGridConfig>();
//            for (int i = 0; i < configs.Length; ++i)
//            {
//                SceneGridView view = configs[i].GetOrAddComponent<SceneGridView>();
//                view.Init();
//                Int32 gridIndex = GetGridIndex(view.GridPosition);
//                m_gridContainer[gridIndex] = view;
//            }
//            m_gridRangeMatrix = new Boolean[m_width, m_height];
//        }

//        private void InternalInitGridMaterial()
//        {
//            m_normalGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.normalGridMaterial.path);
//            m_dangerGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.dangerGridMaterial.path);
//            m_movePathGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.movePathGridMaterial.path);
//            m_moveRangeGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.moveRangeGridMaterial.path);
//            m_dashedLineRangeGridMaterial = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.dashedLineRangeGridMaterial.path);
//        }

//        private void InternalInitSelectedGrid(GameObject root)
//        {
//            if (m_selectedGrid == null)
//            {
//                GameObject go = new GameObject("SelectItemDecal");
//                m_selectedGrid = new SceneGridSelected(go);
//                m_selectedGrid.SetParent(root.transform);
//                m_selectedGrid.Hide();
//            }

//            if (m_selectedCommandGrid == null)
//            {
//                GameObject go = new GameObject("SelectMoveDecal");
//                m_selectedCommandGrid = new SceneGridSelected(go);
//                m_selectedCommandGrid.SetParent(root.transform);
//                m_selectedCommandGrid.Hide();
//            }
//        }

//        #endregion


//        #region Public Method

//        public SceneGridView GetGridViewByGridPosition(GridPosition gridPosition)
//        {
//            return GetGridViewByPosition(gridPosition.x, gridPosition.y);
//        }

//        public SceneGridView GetGridViewByPosition(Int32 x, Int32 y)
//        {
//            Int32 gridIndex = GetGridIndex(x, y);
//            if (m_gridContainer.TryGetValue(gridIndex, out SceneGridView value))
//            {
//                return value;
//            }
//            return null;
//        }

//        public GridPosition GetGridPosition(Vector3 worldPosition)
//        {
//            GridPosition result;
//            Single gridSize = BattleParamSetting.instance.gridSize;
//            Single posX = worldPosition.x - m_offset.x + gridSize / 2;
//            Single posY = worldPosition.z - m_offset.y + gridSize / 2;
//            Int32 gridX = Mathf.FloorToInt(posX / gridSize);
//            Int32 gridY = Mathf.FloorToInt(posY / gridSize);
//            SceneGridView gridView = GetGridViewByPosition(gridX, gridY);
//            if (gridView)
//            {
//                result = gridView.GridPosition;
//            }
//            else
//            {
//                result = GridPosition.invalid;
//            }
//            return result;
//        }

//        public Vector3 GetGridWorldPosition(GridPosition gridPos)
//        {
//            return GetGridWorldPosition(gridPos.x, gridPos.y);
//        }
//        public Vector3 GetGridWorldPosition(Int32 x, Int32 y)
//        {
//            SceneGridView gridView = GetGridViewByPosition(x, y);
//            if (gridView != null)
//            {
//                return gridView.Position;
//            }
//            Debug.LogError(string.Format("[BattleSceneGridHandler] GetGridWorldPosition: 格子不存在({0},{1})", x, y));
//            return Vector3.one * -1;
//        }

//        #endregion

//        #region GridEffect

//        public void AddGirdEffect(GridPosition grid, GridEffectPattern efxType, Single delayTime = 0, Int32 rotation = 0)
//        {
//            SceneGridEffect gridEffect = ClassPoolManager.instance.Fetch<SceneGridEffect>();
//            gridEffect.Init();
//            Vector3 position = GetGridWorldPosition(grid);
//            gridEffect.UpdateGridEffectInfo(grid, position, efxType, delayTime, rotation);
//            m_currentActiveGridEffects.Add(gridEffect);
//        }

//        public void RemoveGridEffect(GridPosition grid, GridEffectPattern efxType)
//        {
//            foreach (var gridEffect in m_currentActiveGridEffects)
//            {
//                if (gridEffect.GridPosition == grid)
//                {
//                    gridEffect.RemoveGridEffect(efxType);
//                }
//            }
//        }

//        public void RemoveAllGridEffect(GridEffectPattern efxType)
//        {
//            foreach (var tempGridEffect in m_currentActiveGridEffects)
//            {
//                tempGridEffect.RemoveGridEffect(efxType);
//            }
//        }

//        public void RemoveAllGridAllEffect()
//        {
//            foreach (var tempGridEffect in m_currentActiveGridEffects)
//            {
//                tempGridEffect.RemoveGridEffect(~GridEffectPattern.None);
//            }
//        }

//        #endregion

//        #region GridShowLogic
//        public void HideAllTypeGrid(Boolean immediately = false)
//        {
//            ShowOrHideSelectGrid(false, GridPosition.invalid);
//            ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
//            HideAllNormalGridInternal(immediately);
//            for(Int32 i = 1; i < (Int32)GridType.Max; i++)
//            {
//                HideAllGridByTypeInternal((GridType)i, immediately);
//            }
//        }

//        public void HideAllGridByType(GridType gridType, Boolean immediately = false)
//        {
//            if (gridType == GridType.Normal)
//            {
//                HideAllNormalGridInternal(immediately);
//                HideAllGridByTypeInternal(GridType.MultiGridEntityRange, true);
//            }
//            else
//            {
//                HideAllGridByTypeInternal(gridType, immediately);
//            }
//        }

//        public void ShowOrHideSelectGrid(bool isShow, GridPosition gridPos)
//        {
//            if (isShow && gridPos.isValid)
//            {
//                SceneGridView gridView = GetGridViewByGridPosition(gridPos);
//                Vector3 position = GetGridWorldPosition(gridPos);
//                m_selectedGrid.UpdateGridMeshAndPosition(gridView.GridMesh, m_normalGridMaterial, position, SceneGridUtility.GetSelectedGridVertexColor());
//                m_selectedGrid.Show();
//            }
//            else
//            {
//                m_selectedGrid.Hide();
//            }
//        }

//        public void ShowOrHideSelectCommandGrid(bool isShow, GridPosition gridPos)
//        {
//            if (isShow && gridPos.isValid)
//            {
//                SceneGridView gridView = GetGridViewByGridPosition(gridPos);
//                Vector3 position = GetGridWorldPosition(gridPos);
//                Color vertexColor = AutoBattleOpMode.Instance.IsProcessCommandMove ? SceneGridUtility.GetCommandSelectedGridVertexColor() : SceneGridUtility.GetSelectedGridVertexColor();
//                m_selectedCommandGrid.UpdateGridMeshAndPosition(gridView.GridMesh, m_normalGridMaterial, position, vertexColor);
//                m_selectedCommandGrid.Show();
//            }
//            else
//            {
//                m_selectedCommandGrid.Hide();
//            }
//        }


//        public void ShowMoveRange(GridPosition originPosition, List<MoveGridPosition> moveRange, Boolean immediately = false)
//        {
//            HideAllNormalGridInternal(immediately);
//            Int32 moveRangeCount = moveRange.Count;
//            for (int i = 0; i < moveRangeCount; ++i)
//            {
//                GridPosition gridPos = moveRange[i].m_grid;
//                Boolean stayFlag = moveRange[i].m_stayFlag;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                m_currentActiveGrids.Add(gridPos, gridData);
//            }
//            ShowNormalGridRangeInternal(immediately);
//        }

//        public void ShowMoveRange(GridPosition originPosition, List<MoveGridPosition> moveRange, List<SkillGridPosition> attackRange, Boolean immediately = false)
//        {
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < attackRange.Count; ++i)
//            {
//                GridPosition gridPos = attackRange[i].m_grid;
//                //GridData gridData = new GridData();
//                //gridData.m_gridPosition = gridPos;
//                //gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                //gridData.m_pattern2 = GridPattern.Attack;
//                //m_currentActiveGrids.Add(gridPos, gridData);
//                if (attackRange[i].m_selectFlag)
//                {
//                    AddGirdEffect(gridPos, GridEffectPattern.SkillEffectRed, immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR);
//                }
//            }
//            Int32 moveRangeCount = moveRange.Count;
//            for (int i = 0; i < moveRangeCount; ++i)
//            {
//                GridPosition gridPos = moveRange[i].m_grid;
//                Boolean stayFlag = moveRange[i].m_stayFlag;
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    gridData.m_pattern2 = gridData.m_moveRangePattern;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    gridData.m_pattern2 = gridData.m_moveRangePattern;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }
//            ShowNormalGridRangeInternal(immediately);
//        }

//        public void ShowSkillMoveRange(GridPosition originPosition, List<MoveGridPosition> moveRange, List<SkillGridPosition> attackRange, Boolean immediately)
//        {
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < attackRange.Count; ++i)
//            {
//                GridPosition gridPos = attackRange[i].m_grid;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                GetSkillEffectPattern(attackRange[i].m_indicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                gridData.m_pattern2 = GridPattern.Skill;
//                m_currentActiveGrids.Add(gridPos, gridData);
//                if (attackRange[i].m_selectFlag)
//                {
//                    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime);
//                }
//            }
//            Int32 moveRangeCount = moveRange.Count;
//            for (int i = 0; i < moveRangeCount; ++i)
//            {
//                GridPosition gridPos = moveRange[i].m_grid;
//                Boolean stayFlag = moveRange[i].m_stayFlag;
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_pattern2 = GridPattern.None;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    gridData.m_pattern2 = gridData.m_moveRangePattern;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    gridData.m_pattern2 = gridData.m_moveRangePattern;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }
//            ShowNormalGridRangeInternal(immediately);
//        }

//        public void ShowSkillMoveEffectRange(GridPosition originPosition, GridPosition targetGridPosition,
//            List<MoveGridPosition> moveRange, List<SkillGridPosition> attackRange, List<SkillGridPosition> skillRange, Boolean immediately)
//        {
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < attackRange.Count; ++i)
//            {
//                GridPosition gridPos = attackRange[i].m_grid;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                GetSkillEffectPattern(attackRange[i].m_indicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                gridData.m_pattern2 = GridPattern.Skill;
//                m_currentActiveGrids.Add(gridPos, gridData);
//                if (attackRange[i].m_selectFlag)
//                {
//                    if (targetGridPosition == gridPos)
//                    {
//                        gridEffectPattern  = (GridEffectPattern)((Int32)gridEffectPattern << 1);
//                    }
//                    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime);
//                }
//            }

//            for (int i = 0; i < moveRange.Count; ++i)
//            {
//                GridPosition gridPos = moveRange[i].m_grid;
//                Boolean stayFlag = moveRange[i].m_stayFlag;
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    gridData.m_pattern2 = gridData.m_moveRangePattern;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    gridData.m_pattern2 = gridData.m_moveRangePattern;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }

//            for (int i = 0; i < skillRange.Count; ++i)
//            {
//                GridPosition gridPos = skillRange[i].m_grid;
//                SkillIndicatorType skillIndicatorType = skillRange[i].m_indicatorType == SkillIndicatorType.Dir ? SkillIndicatorType.Harmful : skillRange[i].m_indicatorType;
//                GetSkillEffectPattern(skillIndicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_pattern2 = gridPattern;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_pattern2 = gridPattern;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }
//            ShowNormalGridRangeInternal(immediately);
//        }

//        public void ShowSkillRange(GridPosition originPosition, List<SkillGridPosition> attackRange)
//        {
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < attackRange.Count; ++i)
//            {
//                GridPosition gridPos = attackRange[i].m_grid;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                GetSkillEffectPattern(attackRange[i].m_indicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                gridData.m_pattern2 = GridPattern.Skill;
//                m_currentActiveGrids.Add(gridPos, gridData);
//                if (attackRange[i].m_selectFlag)
//                {
//                    Int32 rotation = 0;
//                    if (attackRange[i].m_indicatorType == SkillIndicatorType.Dir)
//                    {
//                        GridPosition dir = originPosition - gridPos;
//                        rotation = dir.x == 0 ? dir.y > 0 ? 0 : 180 : dir.x > 0 ? 90 : 270;
//                    }
//                    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime, rotation);
//                }
//            }
//            ShowNormalGridRangeInternal();
//        }

//        public void ShowMoveRange_SkillRange(GridPosition originPosition, List<MoveGridPosition> moveRange, List<SkillGridPosition> attackRange, Boolean immediately = false)
//        {
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < attackRange.Count; ++i)
//            {
//                GridPosition gridPos = attackRange[i].m_grid;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                GetSkillEffectPattern(attackRange[i].m_indicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                gridData.m_pattern2 = GridPattern.Skill;
//                m_currentActiveGrids.Add(gridPos, gridData);
//                if (attackRange[i].m_selectFlag)
//                {
//                    Int32 rotation = 0;
//                    if (attackRange[i].m_indicatorType == SkillIndicatorType.Dir)
//                    {
//                        GridPosition dir = originPosition - gridPos;
//                        rotation = dir.x == 0 ? dir.y > 0 ? 0 : 180 : dir.x > 0 ? 90 : 270;
//                    }
//                    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime, rotation);
//                }
//            }
//            for (int i = 0; i < moveRange.Count; ++i)
//            {
//                GridPosition gridPos = moveRange[i].m_grid;
//                Boolean stayFlag = moveRange[i].m_stayFlag;
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_pattern2 = GridPattern.None;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }
//            ShowNormalGridRangeInternal(immediately);
//        }

//        public void ShowSkillEffectRange(GridPosition originPosition, List<SkillGridPosition> skillEffectRange)
//        {
//            HideAllGridByType(GridType.DangerRange);
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < skillEffectRange.Count; ++i)
//            {
//                GridPosition gridPos = skillEffectRange[i].m_grid;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                SkillIndicatorType skillIndicatorType = skillEffectRange[i].m_indicatorType == SkillIndicatorType.Dir ? SkillIndicatorType.Harmful : skillEffectRange[i].m_indicatorType;
//                GetSkillEffectPattern(skillIndicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                gridData.m_pattern2 = gridPattern;
//                m_currentActiveGrids[gridPos] = gridData;
//                //if (skillRange[i].m_selectFlag)
//                //{
//                //    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime);
//                //}
//            }
//            ShowNormalGridRangeInternal();
//        }

//        public void ShowSkillRangeAndEffectRange(GridPosition originPosition, List<SkillGridPosition> skillRange, List<SkillGridPosition> skillEffectRange)
//        {
//            HideAllNormalGridInternal(true, false);
//            for (int i = 0; i < skillRange.Count; ++i)
//            {
//                GridPosition gridPos = skillRange[i].m_grid;
//                GridData gridData = new GridData();
//                gridData.m_gridPosition = gridPos;
//                gridData.DelayShowTime = 0; // originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                GetSkillEffectPattern(skillRange[i].m_indicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                gridData.m_pattern2 = GridPattern.Skill;
//                m_currentActiveGrids.Add(gridPos, gridData);
//                //if (skillRange[i].m_selectFlag)
//                //{
//                //    Int32 rotation = 0;
//                //    if (skillRange[i].m_indicatorType == SkillIndicatorType.Dir)
//                //    {
//                //        GridPosition dir = originPosition - gridPos;
//                //        rotation = dir.x == 0 ? dir.y > 0 ? 0 : 180 : dir.x > 0 ? 90 : 270;
//                //    }
//                //    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime, rotation);
//                //}
//            }
//            for (int i = 0; i < skillEffectRange.Count; ++i)
//            {
//                GridPosition gridPos = skillEffectRange[i].m_grid;
//                SkillIndicatorType skillIndicatorType = skillEffectRange[i].m_indicatorType == SkillIndicatorType.Dir ? SkillIndicatorType.Harmful : skillEffectRange[i].m_indicatorType;
//                GetSkillEffectPattern(skillIndicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_pattern2 = gridPattern;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = 0; // originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_pattern2 = gridPattern;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }
//            ShowNormalGridRangeInternal(true);

//        }

//        public void ShowSkillEffectMoveRange(GridPosition originPosition, List<MoveGridPosition> moveRange, List<SkillGridPosition> skillRange, Boolean immediately)
//        {
//            HideAllNormalGridInternal(true);
//            for (int i = 0; i < moveRange.Count; ++i)
//            {
//                GridPosition gridPos = moveRange[i].m_grid;
//                Boolean stayFlag = moveRange[i].m_stayFlag;
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_moveRangePattern = stayFlag ? GridPattern.Move : GridPattern.MoveJump;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }
//            }
//            for (int i = 0; i < skillRange.Count; ++i)
//            {
//                GridPosition gridPos = skillRange[i].m_grid;
//                SkillIndicatorType skillIndicatorType = skillRange[i].m_indicatorType == SkillIndicatorType.Dir ? SkillIndicatorType.Harmful : skillRange[i].m_indicatorType;
//                GetSkillEffectPattern(skillIndicatorType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern);
//                if (m_currentActiveGrids.TryGetValue(gridPos, out GridData gridData))
//                {
//                    gridData.m_pattern2 = gridPattern;
//                }
//                else
//                {
//                    gridData = new GridData();
//                    gridData.m_gridPosition = gridPos;
//                    gridData.DelayShowTime = immediately ? 0 : originPosition.DistanceTo(gridPos) * DELAYTIMEFACTOR;
//                    gridData.m_pattern2 = gridPattern;
//                    m_currentActiveGrids.Add(gridPos, gridData);
//                }

//                //if (skillRange[i].m_selectFlag)
//                //{
//                //    AddGirdEffect(gridPos, gridEffectPattern, gridData.DelayShowTime);
//                //}
//            }
//            ShowNormalGridRangeInternal(immediately);
//        }


//        public void ShowMultiGridEntityRange(List<EntityView> entityViews)
//        {
//            List<GridPosition> posList = new List<GridPosition>();
//            foreach (EntityView entityView in entityViews)
//            {
//                posList.Clear();
//                entityView.CollectOccupiedPos(posList);
//                ResetGridRangeMatrix();
//                foreach (GridPosition grid in posList)
//                {
//                    GridPosition gridPos = grid;
//                    SetGridRangeMatrixValueSafely(gridPos.x, gridPos.y, true);
//                }
//                foreach (GridPosition grid in posList)
//                {
//                    GridPosition gridPos = grid;
//                    SceneGridView gridView = GetGridViewByGridPosition(gridPos);
//                    if (gridView != null)
//                    {
//                        Boolean forward = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y + 1);
//                        Boolean back = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y - 1);
//                        Boolean left = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 0);
//                        Boolean right = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 0);
//                        Boolean forwardRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 1);
//                        Boolean forwardLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 1);
//                        Boolean backRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y - 1);
//                        Boolean backLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y - 1);
//                        SceneGridUtility.CalculateRangeGridPatternAndRotation(forward, back, left, right,
//                            forwardRight, forwardLeft, backRight, backLeft,
//                            out Single rotate, out RangeGridPattern pattern);
//                        Color vertexColor = SceneGridUtility.GetMoveRangeGridVertexColor(pattern);
//                        gridView.ShowOrHideMultiGridRangeGrid(true, m_dashedLineRangeGridMaterial, vertexColor, rotate);
//                    }
//                }
//            }
//        }

//        public void ShowDangerRange(List<GridPosition> dangerGrids)
//        {
//            foreach(GridPosition grid in dangerGrids)
//            {
//                if (m_currentActiveGrids.TryGetValue(grid, out GridData gridData))
//                {
//                    if (gridData.m_moveRangePattern == GridPattern.Move || gridData.m_moveRangePattern == GridPattern.MoveJump)
//                    {
//                        SceneGridView gridView = GetGridViewByGridPosition(grid);
//                        gridView.ShowOrHideDangerGrid(true, m_normalGridMaterial, SceneGridUtility.GetDangerRangeGridVertexColor(), 0);
//                    }
//                }
//            }
//        }

//        public void ShowMovePathGrid(List<GridPosition> movePathGrids)
//        {
//            HideAllGridByType(GridType.MovePath, true);
//            if(movePathGrids == null || movePathGrids.Count <= 1)
//            {
//                return;
//            }

//            ResetGridRangeMatrix();
//            foreach (GridPosition gridPos in movePathGrids)
//            {
//                SetGridRangeMatrixValueSafely(gridPos.x, gridPos.y, true);
//            }
//            for(Int32 i = 0; i < movePathGrids.Count; i++)
//            {
//                GridPosition grid = movePathGrids[i];
//                SceneGridView gridView = GetGridViewByGridPosition(grid);
//                if (gridView != null)
//                {
//                    Boolean forward = GetGridRangeMatrixValueSafely(grid.x + 0, grid.y + 1);
//                    Boolean back = GetGridRangeMatrixValueSafely(grid.x + 0, grid.y - 1);
//                    Boolean left = GetGridRangeMatrixValueSafely(grid.x - 1, grid.y + 0);
//                    Boolean right = GetGridRangeMatrixValueSafely(grid.x + 1, grid.y + 0);
//                    SceneGridUtility.CalculateMovePathGridPatternAndRotation(i, forward, back, left, right, out Single rotate, out MovePathGridPattern pattern);
//                    gridView.ShowOrHideMovePathGrid(true, m_movePathGridMaterial, SceneGridUtility.GetPathGridVertexColor(pattern), rotate);
//                }
//            }
//        }

//        private void ResetGridRangeMatrix()
//        {
//            for (Int32 i = 0; i < m_width; i++)
//            {
//                for (Int32 j = 0; j < m_height; j++)
//                {
//                    m_gridRangeMatrix[i, j] = false;
//                }
//            }
//        }

//        private void SetGridRangeMatrixValueSafely(Int32 x, Int32 y, Boolean value)
//        {
//            if (x >= 0 && x < m_width && y >= 0 && y < m_height)
//            {
//                m_gridRangeMatrix[x, y] = value;
//            }
//        }

//        private Boolean GetGridRangeMatrixValueSafely(Int32 x, Int32 y, Boolean defaultValue = false)
//        {
//            if (x < 0 || x >= m_width || y < 0 || y >= m_height)
//            {
//                return defaultValue;
//            }
//            return m_gridRangeMatrix[x, y];
//        }

//        private void ShowNormalGridRangeInternal(Boolean immediately = false)
//        {
//            List<GridData> moveRanges = new List<GridData>();
//            foreach(var item in m_currentActiveGrids)
//            {
//                GridPosition gridPos = item.Key;
//                GridData gridData = item.Value; 
//                if (gridData.m_moveRangePattern == GridPattern.Move || gridData.m_moveRangePattern == GridPattern.MoveJump)
//                {
//                    moveRanges.Add(gridData);
//                }
//                SceneGridView gridView = GetGridViewByGridPosition(gridPos);
//                if (gridView != null)
//                {
//                    gridView.ShowNormalGrid(m_normalGridMaterial, SceneGridUtility.GetNormalGridVertexColor(gridData.m_pattern2), gridData.DelayShowTime, immediately);
//                }
//            }

//            if (moveRanges.Count > 0)
//            {
//                ResetGridRangeMatrix();
//                foreach (GridData grid in moveRanges)
//                {
//                    GridPosition gridPos = grid.m_gridPosition;
//                    SetGridRangeMatrixValueSafely(gridPos.x, gridPos.y, true);
//                }
//                foreach (GridData grid in moveRanges)
//                {
//                    GridPosition gridPos = grid.m_gridPosition;
//                    SceneGridView gridView = GetGridViewByGridPosition(gridPos);
//                    if (gridView != null)
//                    {
//                        Boolean forward = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y + 1);
//                        Boolean back = GetGridRangeMatrixValueSafely(gridPos.x + 0, gridPos.y - 1);
//                        Boolean left = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 0);
//                        Boolean right = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 0);
//                        Boolean forwardRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y + 1);
//                        Boolean forwardLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y + 1);
//                        Boolean backRight = GetGridRangeMatrixValueSafely(gridPos.x + 1, gridPos.y - 1);
//                        Boolean backLeft = GetGridRangeMatrixValueSafely(gridPos.x - 1, gridPos.y - 1);
//                        SceneGridUtility.CalculateRangeGridPatternAndRotation(forward, back, left, right, 
//                            forwardRight, forwardLeft, backRight, backLeft, 
//                            out Single rotate, out RangeGridPattern pattern);
//                        Color vertexColor = SceneGridUtility.GetMoveRangeGridVertexColor(pattern);
//                        gridView.ShowOrHideMoveRangeGrid(true, m_moveRangeGridMaterial, vertexColor, rotate, grid.DelayShowTime, immediately);
//                    }
//                }
//            }
//        }

//        private void HideAllGridByTypeInternal(GridType gridType, Boolean immediately = false)
//        {
//            foreach (var item in m_gridContainer)
//            {
//                SceneGridView gridView = GetGridViewByGridPosition(item.Value.GridPosition);
//                switch (gridType)
//                {
//                    case GridType.DangerRange:
//                        gridView?.ShowOrHideDangerGrid(false, null, Color.black, 0);
//                        break;
//                    case GridType.MovePath:
//                        gridView?.ShowOrHideMovePathGrid(false, null, Color.black, 0);
//                        break;
//                    case GridType.MoveRange:
//                        gridView?.ShowOrHideMoveRangeGrid(false, null, Color.black, 0, 0, false);
//                        break;
//                    case GridType.MultiGridEntityRange:
//                        gridView?.ShowOrHideMultiGridRangeGrid(false, null, Color.black, 0);
//                        break;
//                }
//            }
//        }

//        private void HideAllNormalGridInternal(Boolean immediately = false, Boolean removeEffect = true)
//        {
//            if (removeEffect)
//            {
//                RemoveAllGridEffect(GridEffectPattern.SkillEffect);
//            }

//            foreach (var item in m_currentActiveGrids)
//            {
//                SceneGridView gridView = GetGridViewByGridPosition(item.Value.m_gridPosition);
//                if (gridView)
//                {
//                    gridView.HideNormalGrid(item.Value.DelayHideTime, immediately);
//                    gridView.ShowOrHideMoveRangeGrid(false, null, Color.black, 0, item.Value.DelayHideTime);
//                }
//            }
//            m_currentActiveGrids.Clear();
//            GridData.ResetMaxDelayShowTime();
//        }
//        #endregion




//        #region Private
//        private void GetSkillEffectPattern(SkillIndicatorType skillEffectType, out GridPattern gridPattern, out GridEffectPattern gridEffectPattern)
//        {
//            switch (skillEffectType)
//            {
//                case SkillIndicatorType.Helpful:
//                    gridPattern = GridPattern.SkillCureRange;
//                    gridEffectPattern = GridEffectPattern.SkillEffectGreen;
//                    break;
//                case SkillIndicatorType.Harmful:
//                    gridPattern = GridPattern.SkillHurtRange;
//                    gridEffectPattern = GridEffectPattern.SkillEffectRed;
//                    break;
//                case SkillIndicatorType.Dir:
//                    gridPattern = GridPattern.Skill;
//                    gridEffectPattern = GridEffectPattern.SkillEffectDir;
//                    break;
//                case SkillIndicatorType.Other:
//                    gridPattern = GridPattern.SkillMoveRange;
//                    gridEffectPattern = GridEffectPattern.SkillEffectBlue;
//                    break;
//                default:
//                    gridPattern = GridPattern.None;
//                    gridEffectPattern = GridEffectPattern.None;
//                    break;
//            }
//        }

//        private Int32 GetGridIndex(GridPosition gridPos)
//        {
//            return GetGridIndex(gridPos.x, gridPos.y);
//        }

//        private Int32 GetGridIndex(Int32 x, Int32 y)
//        {
//            return x * 100 + y;
//        }

//        #endregion


//        #region Debug
//        public void UpdateGridDebugText(bool isShow)
//        {
//#if UNITY_EDITOR

//            if (m_debugMeshList.Count == 0)
//            {
//                foreach (var grid in m_gridContainer)
//                {
//                    GameObject go = new GameObject($"DebugGrid{grid.Value.GridPosition}", typeof(TextMesh));
//                    TextMesh textMesh = go.GetComponent<TextMesh>();
//                    textMesh.text = string.Format("{0},{1}", grid.Value.GridPosition.x, grid.Value.GridPosition.y);
//                    textMesh.characterSize = 0.1f;
//                    textMesh.fontSize = 30;
//                    textMesh.color = Color.black;
//                    textMesh.anchor = TextAnchor.MiddleCenter;
//                    go.SetParent(grid.Value.gameObject);
//                    go.transform.localPosition = Vector3.up * 0.05f;
//                    go.transform.localEulerAngles = new Vector3(90, 0, 0);
//                    m_debugMeshList.Add(textMesh);
//                }
//            }
//            foreach (TextMesh textMesh in m_debugMeshList)
//            {
//                textMesh.SetActiveSafely(isShow);
//            }
//#endif
//        }

//        private void InitHitPointMesh()
//        {
//            foreach (var grid in m_gridContainer)
//            {
//                GameObject go = new GameObject($"HitBlock{grid.Value.GridPosition}", typeof(TextMesh));
//                TextMesh textMesh = go.GetComponent<TextMesh>();
//                textMesh.characterSize = 0.1f;
//                textMesh.fontSize = 120;
//                textMesh.color = Color.green / 2f;
//                textMesh.anchor = TextAnchor.MiddleCenter;
//                go.SetParent(grid.Value.gameObject);
//                go.transform.localPosition = Vector3.up * 0.05f;
//                go.transform.localEulerAngles = new Vector3(90, 0, 0);
//                m_hitBlockMeshList.Add(textMesh);
//            }
//        }

//        public void UpdateHitPointMesh()
//        {
//            int index = 0;
//            foreach (var grid in m_gridContainer)
//            {
//                TextMesh textMesh = m_hitBlockMeshList.GetValueSafely(index);
//                int blockCount = 0;
//                var list = BattleShortCut.sampleBattle.GetEntityListByFieldSummary(grid.Value.GridPosition);
//                foreach (var entity in list)
//                {
//                    if (entity.blockComponent == null)
//                    {
//                        continue;
//                    }
//                    blockCount = entity.blockComponent.GetBlockHitPoint(grid.Value.GridPosition);
//                    break;
//                }
//                if (blockCount > 0)
//                {
//                    textMesh.text = blockCount.ToString();
//                }
//                else
//                {
//                    textMesh.text = string.Empty;
//                }
//                index++;
//            }
//        }

//        #endregion
//    }
//}
