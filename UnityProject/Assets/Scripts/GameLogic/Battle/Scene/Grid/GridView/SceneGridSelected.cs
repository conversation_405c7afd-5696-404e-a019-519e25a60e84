using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class SceneGridSelected : SceneGridBase
    {

        public SceneGridSelected(GameObject go) : base(go)
        {
            m_gameObject.transform.localScale = Vector3.one;
            m_gameObject.transform.rotation = Quaternion.identity;
            m_decalMeshFilter = m_gameObject.GetOrAddComponent<MeshFilter>();
            m_decalMeshRenderer = m_gameObject.GetOrAddComponent<MeshRenderer>();
            m_decalMeshRenderer.receiveShadows = false;
            m_decalMeshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
        }

        public void UpdateGridMeshAndPosition(Mesh mesh, Material mat, Vector3 pos, Color vertexColor)
        {
            DecalMeshRenderer.sharedMaterial = mat;
            DecalMeshFilter.mesh = mesh;
            UpdateVertexColor(vertexColor);
            UpdateGridPosition(pos);
        }

        public override Vector3 Offset { get { return Vector3.up * 0.003f; } }
    }
}