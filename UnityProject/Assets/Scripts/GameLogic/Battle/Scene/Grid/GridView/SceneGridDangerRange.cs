using System;
using UnityEngine;

namespace Phoenix.GameLogic.Battle
{
    public class SceneGridDangerRange : SceneGridBase
    {
        private float m_rotate;
        public override Vector3 Offset { get { return Vector3.up * 0.0005f; } }

        public SceneGridDangerRange(GameObject go) : base(go)
        {
        }

        public void UpdateGirdVertexColor(Material mat, Color vertexColor, Single rotate)
        {
            DecalMeshRenderer.sharedMaterial = mat;
            m_rotate = rotate;
            DecalMeshRenderer.lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.Off;
            DecalMeshRenderer.reflectionProbeUsage = UnityEngine.Rendering.ReflectionProbeUsage.Off;
            UpdateVertexColor(vertexColor);
        }

        protected override void UpdateVertexColor(Color vertexColor)
        {
            vertexColor.b = m_rotate;
            base.UpdateVertexColor(vertexColor);
        }
    }
}