using UnityEngine;
using System;
using UnityEngine.UIElements.Experimental;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class SceneGridNormal : SceneGridBase
    {
        private bool m_isShow;
        private Single m_delayShowTime;
        private Single m_delayHideTime;
        private Single m_time;
        private Boolean m_enableSmooth;
        public virtual Single Duration { get { return 0.2f; } }
        public override Vector3 Offset { get { return Vector3.up * 0.001f; } }
        protected Vector3 m_sourceScale = new Vector3(0, 1, 0);
        protected Vector3 m_destScale = new Vector3(1, 1, 1);

        public SceneGridNormal(GameObject go) : base(go)
        {
        }

        public void UpdateGirdVertexColor(Material mat, Color vertexColor)
        {
            DecalMeshRenderer.sharedMaterial = mat;
            DecalMeshRenderer.lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.Off;
            DecalMeshRenderer.reflectionProbeUsage = UnityEngine.Rendering.ReflectionProbeUsage.Off;
            UpdateVertexColor(vertexColor);
        }

        public Mesh GetGridMesh()
        {
            return DecalMeshFilter.mesh;
        }

        public void Show(Single delayTime, bool immediately = false)
        {
            m_isShow = true;
            if (immediately)
            {
                m_gameObject.transform.localScale = Vector3.one;
                Show();
                return;
            }
            m_gameObject.transform.localScale = Vector3.zero;
            m_time = 0;
            m_delayShowTime = delayTime;
            Show();
            m_enableSmooth = true;
        }

        public void Hide(Single delayTime, bool immediately = false)
        {
            m_isShow = false;
            if (immediately)
            {
                Hide();
                return;
            }
            m_delayHideTime = delayTime;
            m_enableSmooth = true;
        }

        public void OnTick(TimeSlice ts)
        {
            if (!m_enableSmooth)
            {
                return;
            }

            if (m_isShow)
                SmoothShowGrid(ts.deltaTime);
            else
                SmoothHideGrid(ts.deltaTime);
        }

        #region Private Function
        private void SmoothShowGrid(float dt)
        {
            if (m_delayShowTime > 0)
            {
                m_delayShowTime -= dt;
                if (m_delayShowTime > 0)
                    return;
                m_delayShowTime = 0;
            }

            m_time += dt;
            float a = Duration > Single.Epsilon ? Mathf.Clamp01(m_time / Duration) : 1;
            Vector3 scale = Vector3.LerpUnclamped(m_sourceScale, m_destScale, Easing.OutBack(a));
            m_gameObject.transform.localScale = scale;
            if (m_time >= Duration)
            {
                m_enableSmooth = false;
            }
        }

        private void SmoothHideGrid(float dt)
        {
            if (m_delayHideTime > 0)
            {
                m_delayHideTime -= dt;
                if (m_delayHideTime > 0)
                    return;
                m_delayHideTime = 0;
            }

            m_time -= dt;
            float a = Duration > Single.Epsilon ? Mathf.Clamp01(m_time / Duration) : 1;
            Vector3 scale = Vector3.LerpUnclamped(m_sourceScale, m_destScale, Easing.OutBack(a));
            m_gameObject.transform.localScale = scale;
            if (m_time <= 0)
            {
                Hide();
                m_enableSmooth = false;
            }
        }
        #endregion

    }
}