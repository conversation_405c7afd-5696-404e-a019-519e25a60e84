
using Phoenix.Core;
using UnityEngine;
using Phoenix.Battle;
using System;

namespace Phoenix.GameLogic.Battle
{
    public class SceneGridView : MonoBehaviour
    {
        private BattleGridConfig m_config;
        private SceneGridNormal m_sceneGridNormal;
        private SceneGridMovePath m_sceneGridMovePath;
        private SceneGridDangerRange m_sceneGridDangerRange;
        private SceneGridMoveRange m_sceneGridMoveRange;
        private SceneGridDangerRange m_multiGridEntityRange;

        public GridPosition GridPosition { get { return m_config.gridPosition; } }
        public Vector3 Position { get { return m_sceneGridNormal.Position; } }

        public Mesh GridMesh { get { return m_sceneGridNormal.GetGridMesh(); } }

        public void Init()
        {
            m_config = GetComponent<BattleGridConfig>();
            m_sceneGridNormal = new SceneGridNormal(transform.Find("Range").gameObject);
            m_sceneGridMovePath = new SceneGridMovePath(transform.Find("Path").gameObject);
            m_sceneGridDangerRange = new SceneGridDangerRange(transform.Find("Danger").gameObject);
            m_sceneGridMoveRange = new SceneGridMoveRange(Instantiate(m_sceneGridDangerRange.GameObject, transform));
            m_sceneGridMoveRange.GameObject.name = "MoveRange";
            m_multiGridEntityRange = new SceneGridDangerRange(Instantiate(m_sceneGridMovePath.GameObject, transform));
            m_multiGridEntityRange.GameObject.name = "MultiGridEntityRange";
        }

        public void OnTick(TimeSlice ts)
        {
            m_sceneGridNormal?.OnTick(ts);
            m_sceneGridMoveRange?.OnTick(ts);
        }

        public void UnInit()
        {
            m_sceneGridNormal.Hide();
            m_sceneGridNormal = null;

            m_sceneGridMovePath.Hide();
            m_sceneGridMovePath = null;

            m_sceneGridDangerRange.Hide();
            m_sceneGridDangerRange = null;

            Destroy(m_sceneGridMoveRange.GameObject);
            m_sceneGridMoveRange = null;
            m_sceneGridMoveRange = null;

            Destroy(m_multiGridEntityRange.GameObject);
            m_multiGridEntityRange.Hide();
            m_multiGridEntityRange = null;
        }

        public void ShowNormalGrid(Material mat, Color vertexColor, float delayTime, bool immediately = false)
        {
            m_sceneGridNormal.UpdateGirdVertexColor(mat, vertexColor);
            m_sceneGridNormal.Show(delayTime, immediately);
        }

        public void HideNormalGrid(float delayTime, bool immediately = false)
        {
            m_sceneGridNormal.Hide(delayTime, immediately);
        }

        public void ShowOrHideDangerGrid(Boolean isShow, Material mat, Color vertexColor, Single rotate)
        {
            if (isShow)
            {
                m_sceneGridDangerRange.UpdateGirdVertexColor(mat, vertexColor, rotate);
                m_sceneGridDangerRange.Show();
            }
            else
            {
                m_sceneGridDangerRange.Hide();
            }
        }

        public void ShowOrHideMovePathGrid(Boolean isShow, Material mat, Color vertexColor, Single rotate)
        {
            if (isShow)
            {
                m_sceneGridMovePath.UpdateGirdVertexColor(mat, vertexColor, rotate);
                m_sceneGridMovePath.Show();
            }
            else
            {
                m_sceneGridMovePath.Hide();
            }
        }

        public void ShowOrHideMoveRangeGrid(Boolean isShow, Material mat, Color vertexColor, Single rotate, Single delay, bool immediately = false)
        {
            if (isShow)
            {
                m_sceneGridMoveRange.UpdateGirdVertexColor(mat, vertexColor, rotate);
                m_sceneGridMoveRange.Show(delay, immediately);
            }
            else
            {
                m_sceneGridMoveRange.Hide(delay, immediately);
            }
        }

        public void ShowOrHideMultiGridRangeGrid(Boolean isShow, Material mat, Color vertexColor, Single rotate)
        {
            if (isShow)
            {
                m_multiGridEntityRange.UpdateGirdVertexColor(mat, vertexColor, rotate);
                m_multiGridEntityRange.Show();
            }
            else
            {
                m_multiGridEntityRange.Hide();
            }
        }
    }
}