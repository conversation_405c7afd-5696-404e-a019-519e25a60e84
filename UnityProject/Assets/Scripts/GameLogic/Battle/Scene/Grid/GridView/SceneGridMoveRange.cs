using System;
using UnityEngine;

namespace Phoenix.GameLogic.Battle
{
    public class SceneGridMoveRange : SceneGridNormal
    {
        private float m_rotate;
        public override Vector3 Offset { get { return Vector3.up * 0.0007f; } }
        public override Single Duration { get { return 0f; } }

        public SceneGridMoveRange(GameObject go) : base(go)
        {
        }

        public void UpdateGirdVertexColor(Material mat, Color vertexColor, Single rotate)
        {
            DecalMeshRenderer.sharedMaterial = mat;
            DecalMeshRenderer.lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.Off;
            DecalMeshRenderer.reflectionProbeUsage = UnityEngine.Rendering.ReflectionProbeUsage.Off;
            m_rotate = rotate;
            UpdateVertexColor(vertexColor);
        }

        protected override void UpdateVertexColor(Color vertexColor)
        {
            vertexColor.b = m_rotate;
            base.UpdateVertexColor(vertexColor);
        }
    }
}