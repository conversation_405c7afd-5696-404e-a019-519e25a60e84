using Phoenix.Battle;
using Phoenix.Core;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.Playables;
using Phoenix.ConfigData;
using UnityEngine.SceneManagement;
using Phoenix.Hakoniwa;

namespace Phoenix.GameLogic.Battle
{
    public class BattleScene
    {
        private GameObject m_sceneEntityRoot;
        private GameObject m_mainCameraRoot;
        private GameObject m_sceneVirtualCameraObj;
        private GameObject m_sceneVirtualCameraRoot;

        private CombatSceneConfig m_combatSceneConfig;

        private Camera m_sceneCamera;
        private BattleSceneGridPlusHandler m_sceneGridHandler;
        private BattleSceneCameraHandler m_sceneCameraHandler;
        private BattleSkillPredictHandler m_skillPredictHandler;
        private TimelinePlayer m_battleTimelinePlayer;

        private Dictionary<int, GameObject> m_terrainEfxMap = new Dictionary<int, GameObject>();
        private List<ParticleRuntimeInfo> m_terrainEfxInfoList = new List<ParticleRuntimeInfo>();
        private Dictionary<int, GameObject> m_treasureBoxMap = new Dictionary<int, GameObject>();

        private Dictionary<SceneType, SceneGroup> m_sceneList = new Dictionary<SceneType, SceneGroup>();

        public string mainScenePath = string.Empty;
        public string combatScenePath = string.Empty;
        public GameObject sceneEntityRoot { get { return m_sceneEntityRoot; }}
        public BattleSceneGridPlusHandler sceneGridHandler { get { return m_sceneGridHandler; }  }
        public BattleSceneCameraHandler sceneCameraHandler { get { return m_sceneCameraHandler; } }
        public BattleSkillPredictHandler skillPredictHandler { get { return m_skillPredictHandler; } }
        public TimelinePlayer timelinePlayer { get { return m_battleTimelinePlayer; } }
        public CombatSceneConfig combatSceneConfig { get { return m_combatSceneConfig; } }

        public Camera MainCamera { get { return m_sceneCamera; } }

        public void InitBattleScene(Scene scene)
        {
            m_sceneEntityRoot = scene.FindGameObject("EntityRoot");
            m_sceneVirtualCameraRoot = scene.FindGameObject("VirtualCameraRoot");
            m_skillPredictHandler = new BattleSkillPredictHandler();
            m_battleTimelinePlayer = new TimelinePlayer();
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        public void UnInitBattleScene()
        {
            TickManager.instance.UnRegisterGlobalTick(OnTick);
            Reset();
        }

        protected void OnTick(TimeSlice timeSlice)
        {
            m_sceneCameraHandler?.OnTick(timeSlice);
            m_sceneGridHandler?.OnTick(timeSlice);
            m_battleTimelinePlayer?.Tick(timeSlice);
        }

        public void Refresh()
        {
            RefreshTreasureBox();
            m_skillPredictHandler?.Reset();
            foreach (var terrainEfxInfo in m_terrainEfxInfoList)
            {
                terrainEfxInfo.HideAndEnd();
            }
            m_terrainEfxInfoList.Clear();
            foreach (var kv in m_terrainEfxMap)
            {
                ParticleManager.instance.DeSpawn(kv.Value);
            }
            m_terrainEfxMap.Clear();
            foreach (var kv in BattleShortCut.sampleBattle.GetTerrainEffectMap())
            {
                AddTerrainEfx(kv.Value.uid, kv.Value.rid, kv.Value.pos, true);
            }
            foreach(var entity in BattleShortCut.sampleBattle.GetEntityList())
            {
                Skill skill;
                var posList = BattleUtility.GetPreAnnounceGridList(entity, out skill);
                if (posList != null)
                {
                    BattleShortCut.battleScene.skillPredictHandler.SkillPredictBegin(entity.uid, skill.uid, posList);
                }
            }
        }

        public void Reset()
        {
            foreach(var kv in m_terrainEfxMap)
            {
                ResourceHandleManager.instance.DespawnGameObject(kv.Value.gameObject);
            }
            foreach (var terrainEfxInfo in m_terrainEfxInfoList)
            {
                terrainEfxInfo.HideAndEnd();
            }
            m_terrainEfxInfoList.Clear();

            m_skillPredictHandler?.Reset();

            UnInitSceneGridHandler();
            UnInitializeSceneCamera();

            m_combatSceneConfig = null;
            for (int i = 0; i < (int)SceneType.Max; i++)
            {
                SceneType currentSceneType = (SceneType)i;
                if (m_sceneList.ContainsKey(currentSceneType))
                {
                    m_sceneList[currentSceneType].Clear();
                }
            }
            m_sceneList.Clear();
            m_battleTimelinePlayer.UnInit();
        }

        public void ToggleScene(SceneType sceneType)
        {
            for(int i = 0; i < (int)SceneType.Max; i++)
            {
                SceneType currentSceneType = (SceneType)i;
                m_sceneList[currentSceneType].ShowSceneGroupWithSceneType(sceneType);
            }

            if (sceneType == SceneType.Main)
            {
                foreach (var kv in m_terrainEfxMap)
                {
                    kv.Value.gameObject.SetActiveSafely(true);
                }
                foreach (var kv in m_treasureBoxMap)
                {
                    kv.Value.gameObject.SetActiveSafely(true);
                }
            }
            else if (sceneType == SceneType.Combat)
            {
                foreach (var terrainEfxInfo in m_terrainEfxInfoList)
                {
                    terrainEfxInfo.HideAndEnd();
                }
                m_terrainEfxInfoList.Clear();
                foreach (var kv in m_terrainEfxMap)
                {
                    kv.Value.gameObject.SetActiveSafely(false);
                }
                foreach (var kv in m_treasureBoxMap)
                {
                    kv.Value.gameObject.SetActiveSafely(false);
                }
            }
        }

        public Scene? GetUnityScene(SceneType sceneType)
        {
            SceneGroup sceneGroup = null;
            if (m_sceneList.TryGetValue(sceneType, out sceneGroup))
            {
                return sceneGroup.m_unityScene;
            }
            return null;
        }

        public GameObject GetSceneTimelinePlayableDirectorGameObject(SceneType sceneType)
        {
            return m_sceneList[sceneType].GetSceneTimelinePlayableDirectorGameObject();
        }


        public void UpdateScene(Scene unityScene, SceneType sceneType)
        {
            SceneGroup sceneGroup = new SceneGroup();
            sceneGroup.Init(unityScene, sceneType);
            m_sceneList[sceneType] = sceneGroup;
        }

        public void InitializeSceneCamera(int initCameraRotateOffset)
        {
            UnInitializeSceneCamera();

            if (StaticHakoniwa.IsHakoSceneBattle)
            {
                m_mainCameraRoot = MainCameraManager.instance.Root;
            }
            else
            {
                m_mainCameraRoot = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.physicsCameraRoot.path);
                MainCameraManager.RegisterMainCamera(m_mainCameraRoot);
            }
            m_sceneVirtualCameraObj = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.virtualCameraRoot.path);
            m_sceneVirtualCameraObj.SetParent(m_sceneVirtualCameraRoot);
            m_sceneCamera = MainCameraManager.instance.MainCamera;


            //SceneLayerPrefab sceneCameraLayer = m_sceneCameraObj.GetComponent<SceneLayerPrefab>();
            //sceneCameraLayer.InitLayerName("BattleScene");
            //SceneLayerManager.instance.PushLayer(sceneCameraLayer, true);

            m_sceneCameraHandler = new BattleSceneCameraHandler();
            m_sceneCameraHandler.Initialize(m_sceneVirtualCameraObj, m_sceneCamera, initCameraRotateOffset);
            EventManager.instance.Broadcast(EventID.BattleStart_OnUpdateCameraAspect, m_sceneCamera.aspect);

            // 初始化timeline播放器
            m_battleTimelinePlayer.Init(m_sceneCamera);
        }

        public void UnInitializeSceneCamera()
        {

            if (m_mainCameraRoot != null)
            {
                //SceneLayerPrefab sceneCameraLayer = m_sceneCameraObj.GetComponent<SceneLayerPrefab>();
                //SceneLayerManager.instance.PopLayer(sceneCameraLayer, false);

                if (StaticHakoniwa.IsHakoSceneBattle == false)
                {
                    MainCameraManager.UnRegisterMainCamera();
                    ResourceHandleManager.instance.DespawnGameObject(m_mainCameraRoot);
                }
                m_mainCameraRoot = null;
            }
            if (m_sceneVirtualCameraObj != null)
            {
                ResourceHandleManager.instance.DespawnGameObject(m_sceneVirtualCameraObj);
                m_sceneVirtualCameraObj = null;
            }
            if (m_sceneCameraHandler != null)
            {
                m_sceneCameraHandler.UnInitialize();
                m_sceneCameraHandler = null;
            }
        }

        public void InitSceneGridHandler(GameObject sceneConfigRoot, GameObject combatSceneConfigRoot)
        {
            UnInitSceneGridHandler();
            m_sceneGridHandler = new BattleSceneGridPlusHandler();
            m_sceneGridHandler.Init(sceneConfigRoot);

            BattleSceneConfig sceneConfig = sceneConfigRoot.GetComponent<BattleSceneConfig>();
            float startX = sceneConfig.offset.x;
            float startY = sceneConfig.offset.y;
            float width = sceneConfig.width * BattleParamSetting.instance.gridSize;
            float height = sceneConfig.height * BattleParamSetting.instance.gridSize;
            m_sceneCameraHandler.SetCameraMoveFieldRange(startX, startY, width, height);

            m_combatSceneConfig = combatSceneConfigRoot.GetComponent<CombatSceneConfig>();
        }

        public void UnInitSceneGridHandler()
        {
            if (m_sceneGridHandler != null)
            {
                m_sceneGridHandler.UnInit();
                m_sceneGridHandler = null;
            }

        }

        public Vector2 GetScreenPositionByWorldPosition(Vector3 worldPosition)
        {
            if (m_sceneCamera != null)
                return m_sceneCamera.WorldToScreenPoint(worldPosition);
            return Vector2.zero;
        }

        public GridPosition GetGridPositionByScreenPosition(Vector2 screenPos)
        {
            Ray ray = m_sceneCamera.ScreenPointToRay(screenPos);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, Mathf.Infinity, UnityLayerUtility.GetLayerMask(ELayerMaskType.TerrainTouch)))
            {
                return sceneGridHandler.GetGridPosition(hit.point);
            }
            return GridPosition.invalid;
        }

        public void AddTerrainEfx(int uid, int rid, GridPosition pos, bool isLoop)
        {
            var paticleConfig = ConfigDataManager.instance.GetParticle(rid);
            if (paticleConfig == null)
            {
                return;
            }
            if (isLoop)
            {
                if (!m_terrainEfxMap.ContainsKey(uid))
                {
                    var info = ParticleManager.instance.Spawn(paticleConfig.ResPath);
                    if (info != null)
                    {
                        info.go.transform.position = m_sceneGridHandler.GetGridWorldPosition(pos);
                        info.go.transform.localScale = Vector3.one;
                        m_terrainEfxMap.Add(uid, info.go);
                    }
                }
            }
            else
            {
                var info = ParticleManager.instance.Spawn(paticleConfig.ResPath);
                if (info != null)
                {
                    info.go.transform.position = m_sceneGridHandler.GetGridWorldPosition(pos);
                    info.go.transform.localScale = Vector3.one;
                    info.actionOnEnd = OnTerrainEffectPlayEnd;
                    m_terrainEfxInfoList.Add(info);
                }
            }
        }

        private void OnTerrainEffectPlayEnd(ParticleRuntimeInfo info)
        {
            m_terrainEfxInfoList.Remove(info);
        }

        public void HideTerrainEfx(int uid)
        {
            GameObject go;
            if (m_terrainEfxMap.TryGetValue(uid, out go))
            {
                ParticleManager.instance.DeSpawn(go);
                m_terrainEfxMap.Remove(uid);
            }
        }

        public void RefreshTreasureBox()
        {
            foreach (var kv in m_treasureBoxMap)
            {
                ResourceHandleManager.instance.DespawnGameObject(kv.Value);
            }
            m_treasureBoxMap.Clear();
            foreach (var treasureBoxInfo in BattleShortCut.sampleBattle.GetCurStageInfo().treasureBoxList)
            {
                if (BattleShortCut.sampleBattle.initData.gainedTreasureBoxIdList.Contains(treasureBoxInfo.treasureBoxId))
                {
                    continue;
                }
                if (BattleShortCut.sampleBattle.CheckGainTreasureBox(treasureBoxInfo.treasureBoxId))
                {
                    continue;
                }
                var skinConfig = ConfigDataManager.instance.GetEntitySkin(treasureBoxInfo.skinId);
                GameObject treasureBoxObj = ResourceHandleManager.instance.SpawnGameObject(skinConfig.PrefabPath);
                treasureBoxObj.transform.position = BattleShortCut.GetGridWorldPosition(treasureBoxInfo.pos);
                m_treasureBoxMap.Add(treasureBoxInfo.treasureBoxId, treasureBoxObj);
            }
        }

        public void AddTreasureBox(int id, GameObject obj)
        {
            m_treasureBoxMap.Add(id, obj);
        }

        public GameObject GetTreasureBox(int id)
        {
            return m_treasureBoxMap.GetClassValue(id);
        }

        public void RemoveTreasureBox(int id)
        {
            GameObject obj;
            if (m_treasureBoxMap.TryGetValue(id, out obj))
            {
                ResourceHandleManager.instance.DespawnGameObject(obj);
                m_treasureBoxMap.Remove(id);
            }
        }
    }

    public class SceneGroup
    {
        public Scene m_unityScene;
        public SceneType m_sceneType;
        public List<GameObject> m_gos = new List<GameObject>();


        public void Init(Scene unityScene, SceneType sceneType)
        {
            m_unityScene = unityScene;
            m_sceneType = sceneType;
            foreach (var obj in unityScene.GetRootGameObjects())
            {
                m_gos.Add(obj);
            }
        }

        public void Clear()
        {
            m_unityScene = default;
            m_sceneType = SceneType.Max;
            m_gos.Clear();
        }

        public void ShowSceneGroupWithSceneType(SceneType sceneType)
        {
            foreach (GameObject go in m_gos)
            {
                go.SetActiveSafely(sceneType == m_sceneType);
            }
            if (sceneType == m_sceneType)
            {
                SceneManager.SetActiveScene(m_unityScene);
            }
        }

        public GameObject GetSceneTimelinePlayableDirectorGameObject()
        {
            foreach (GameObject go in m_gos)
            {
                PlayableDirector pd = go.GetComponent<PlayableDirector>();
                if (pd != null)
                {
                    return go;
                }
            }
            return null;
        }
    }

    public enum SceneType
    {
        Main,
        Combat,
        Max
    }
}
