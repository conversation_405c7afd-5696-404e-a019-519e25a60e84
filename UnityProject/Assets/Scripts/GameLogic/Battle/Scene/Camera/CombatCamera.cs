using Cinemachine;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CombatCamera : VirtualCameraBase
    {
        #region Field
        private CinemachineVirtualCamera m_virtualCamera;
        private CinemachineTargetGroup m_battleTargetGroup;
        #endregion

        public CombatCamera(GameObject root) : base(root) { }

        public void Initialize()
        {
            m_virtualCamera = m_root.GetComponentInChildren<CinemachineVirtualCamera>();
            m_battleTargetGroup = m_root.GetComponentInChildren<CinemachineTargetGroup>();
            InitializeDefaultValue();
            m_virtualCamera.m_Lens.FieldOfView = CinemachineCameraSetting.instance.SettingData.m_combatCameraFieldOfView;
            Disable();
        }

        public void InitializeDefaultValue()
        {
        }


        public void UnInitialize() { }

        public void UpdateCombatCamera(float rotateX, float rotateY, float dutch, Transform leftEntity, Transform rightEntity, CinemachineCameraSetting.TargetGroupSetting targetGroup)
        {
            m_virtualCamera.transform.localEulerAngles = new Vector3(rotateX, rotateY, 0);
            m_virtualCamera.m_Lens.Dutch = dutch;

            if (leftEntity == null || rightEntity == null)
            {
                m_battleTargetGroup.m_Targets = null;
                return;
            }

            m_battleTargetGroup.m_Targets = new CinemachineTargetGroup.Target[2];

            m_battleTargetGroup.m_Targets[0].weight = targetGroup.m_originWeight;
            m_battleTargetGroup.m_Targets[0].target = leftEntity.transform;
            m_battleTargetGroup.m_Targets[0].radius = targetGroup.m_originRadius;

            m_battleTargetGroup.m_Targets[1].weight = targetGroup.m_targetWeight;
            m_battleTargetGroup.m_Targets[1].target = rightEntity.transform;
            m_battleTargetGroup.m_Targets[1].radius = targetGroup.m_targetRadius;

        }
    }
}