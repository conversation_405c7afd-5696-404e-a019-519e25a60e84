using Cinemachine;
using Google.Protobuf.WellKnownTypes;
using System;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class MainCamera : VirtualCameraBase
    {
        private Camera m_mainCamera;
        private CinemachineVirtualCamera m_virtualCamera;
        private CinemachineTrackedDolly m_cameraZoomDollyCart;
        private Vector3 m_position, m_localPosition;
        private Vector3 m_cameraForward, m_cameraRight;

        private FollowMode m_followMode = FollowMode.None;
        private GameObject m_followGameObject;
        private Vector2 m_minSoftZone;
        private Vector2 m_maxSoftZone;
        private float m_mainCameraAspect;

        #region Move相关
        private bool m_isTouchMoving;
        /// <summary> 用于 SmoothMove的帮助参数 </summary>
        private Vector3 m_smoothMoveStartPosition, m_smoothMoveTargetPosition, m_followModeTargetPosition;
        private float m_currentSmoothMoveTime, m_smoothMoveTotalTime;
        private Action m_cameraMoveEndAction;
        /// <summary> 手动 Touch Move的参数因子 </summary>
        private float m_manualMoveSpeedFactor;
        /// <summary> 手动 键盘 Move的参数因子 </summary>
        private float m_keyboardMoveSpeedFactor;
        /// <summary> 虚拟相机AutoMove的标准Speed </summary>
        private float m_autoMoveSpeed;
        /// <summary> 地图逻辑区域，做移动限制 </summary>
        private Rect m_logicSceneMapField;
        /// <summary> 拖动产生的阻尼速度 </summary>
        protected Vector3 m_touchMoveVelocity;
        private Vector3 m_touchMoveInertiaVelocity;
        protected Vector3 m_followMoveVelocity;

        private Vector3 m_prevDeltaMovePosition;
        private Vector3 m_defaultCameraLocalEulerAngles;
        #endregion

        #region Zoom相关
        private float m_currentSmoothZoomTime, m_smoothZoomTotalTime;
        private float m_smoothZoomStartValue, m_smoothZoomTargetValue;
        private float m_currentZoomValue, m_minZoomValue, m_maxZoomValue, m_defaultZoomValue;
        private float CurrentZoomValue { get { return m_currentZoomValue - m_minZoomValue; } }
        public float CurrentZoomPercentValue { get { return (m_currentZoomValue - m_minZoomValue) / (m_maxZoomValue - m_minZoomValue); } }
        #endregion

        private float m_fovSmoothTime, m_fovSmoothTotalTime;
        private float m_fovValue, m_smoothFovStartValue, m_smoothFovTargetValue;

        public MainCamera(GameObject root, Camera mainCamera, Int32 initCameraRotateOffset) : base(root)
        {
            m_mainCamera = mainCamera;
            m_mainCameraAspect = mainCamera.aspect;
            m_defaultCameraLocalEulerAngles = m_root.transform.localEulerAngles;
            Vector3 originEulerAngles = m_defaultCameraLocalEulerAngles;
            originEulerAngles.y += initCameraRotateOffset * 90;
            m_root.transform.localEulerAngles = originEulerAngles;
        }

        public override void OnTick(TimeSlice ts)
        {
            if (IsSmoothMoving())
            {
                m_touchMoveInertiaVelocity = Vector3.zero;
                m_touchMoveVelocity = Vector3.zero;
                SmoothUpdateCameraPosition(ts.deltaTime, ref m_position);
                ApplyCameraPositionValue(false, ts.deltaTime);
            }
            else if (m_isTouchMoving)
            {
                ApplyCameraPositionValue(true, ts.deltaTime);
            }
            else
            {
                //Touch移动后的惯性
                TickTouchMovedInertia(ref m_position, ts.deltaTime);
                // 处理跟随
                TickFollow(m_followMode, ref m_position, ts.deltaTime);
                ApplyCameraPositionValue(false, ts.deltaTime);
            }

            if (IsSmoothZooming())
            {
                SmoothUpdateCameraZoomValue(ts.deltaTime, ref m_currentZoomValue);
                ApplyCameraZoomValue();
            }

            if (IsSmoothUpdateFOV())
            {
                SmoothUpdateCameraFOVValue(ts.deltaTime, ref m_fovValue);
                ApplyCameraFOVValue();
            }
        }

        public void Initialize()
        {
            m_virtualCamera = m_root.GetComponentInChildren<CinemachineVirtualCamera>();
            m_fovValue = CinemachineCameraSetting.instance.SettingData.m_battleCameraFieldOfView;
            ApplyCameraFOVValue();
            m_cameraZoomDollyCart = m_virtualCamera.GetCinemachineComponent<CinemachineTrackedDolly>();
            InitDefaultValue();

            EventManager.instance.RegisterListener<GameObject, FollowMode>(EventID.EventOnCameraFollowBegin, EventOnCameraFollowBegin);
            EventManager.instance.RegisterListener(EventID.EventOnCameraFollowEnd, EventOnCameraFollowEnd);
            EventManager.instance.RegisterListener<InputData>(EventID.EventOnInputDown, EventOnTouchBegan);
            EventManager.instance.RegisterListener<InputData>(EventID.EventOnInputDrag, EventOnTouchMoved);
            EventManager.instance.RegisterListener<InputData>(EventID.EventOnInputUp, EventOnTouchEnded);
            EventManager.instance.RegisterListener<InputData>(EventID.EventOnInputZoomInOut, EventOnZoomChange);
            Disable();
        }

        private void InitDefaultValue()
        {
            m_isTouchMoving = false;
            UpdateCameraMoveDir();
            m_minZoomValue = 0;
            CinemachineSmoothPath smoothPath = m_cameraZoomDollyCart.m_Path as CinemachineSmoothPath;
            m_maxZoomValue = smoothPath ? smoothPath.PathLength : 2;
            Int32 defaultZoomPerecent = CinemachineCameraSetting.instance.SettingData.m_defaultZoomPercentValue;
            m_defaultZoomValue = m_currentZoomValue = defaultZoomPerecent * (m_maxZoomValue - m_minZoomValue) / 100f + m_minZoomValue;
            ApplyCameraZoomValue();
            m_manualMoveSpeedFactor = 0.35f;
            m_keyboardMoveSpeedFactor = 0.25f;
            m_autoMoveSpeed = 20f;
            m_logicSceneMapField = new Rect(0f, 0f, 30f, 30f);

            m_minSoftZone = new Vector2(0.25f / m_mainCameraAspect, 0.25f);
            m_maxSoftZone = new Vector2(1 - 0.25f / m_mainCameraAspect, 1 - 0.25f);
        }

        public void UnInitialize()
        {
            m_root.transform.localEulerAngles = m_defaultCameraLocalEulerAngles;

            EventManager.instance.UnRegisterListener<GameObject, FollowMode>(EventID.EventOnCameraFollowBegin, EventOnCameraFollowBegin);
            EventManager.instance.UnRegisterListener(EventID.EventOnCameraFollowEnd, EventOnCameraFollowEnd);
            EventManager.instance.UnRegisterListener<InputData>(EventID.EventOnInputDown, EventOnTouchBegan);
            EventManager.instance.UnRegisterListener<InputData>(EventID.EventOnInputDrag, EventOnTouchMoved);
            EventManager.instance.UnRegisterListener<InputData>(EventID.EventOnInputUp, EventOnTouchEnded);
            EventManager.instance.UnRegisterListener<InputData>(EventID.EventOnInputZoomInOut, EventOnZoomChange);
        }

        public Single SetCameraPositionWithSpeed(Vector3 targetPosition, float speed, bool immediately = false, Action end = null)
        {
            MoveTargetCorrection(ref targetPosition);
            if (Vector3.SqrMagnitude(targetPosition - m_position) < 1e-3)
            {
                if (end != null)
                    end.Invoke();
                return 0;
            }

            CameraMoveEnd();
            m_cameraMoveEndAction = end;
            m_smoothMoveStartPosition = m_position;
            m_smoothMoveTargetPosition = targetPosition;
            m_currentSmoothMoveTime = 0;
            m_smoothMoveTotalTime = 0;
            if (immediately)
            {
                m_position = m_smoothMoveTargetPosition;
                ApplyCameraPositionValue(false, 0);
                CameraMoveEnd();
            }
            else
            {
                m_smoothMoveTotalTime = Mathf.Min(1f, GetCameraMoveTime(speed > 1 ? speed : m_autoMoveSpeed));
            }
            return m_smoothMoveTotalTime;
        }

        public void SetCameraZoomPercentAndFov(float zoomPercentValue, float newFovValue, Boolean immediately = false)
        {
            if (immediately)
            {
                m_currentZoomValue = zoomPercentValue * (m_maxZoomValue - m_minZoomValue) / 100f + m_minZoomValue; ;
                ApplyCameraZoomValue();
                m_fovValue = newFovValue;
                ApplyCameraFOVValue();
            }
            else
            {
                m_smoothZoomStartValue = m_currentZoomValue;
                m_smoothZoomTargetValue = zoomPercentValue * (m_maxZoomValue - m_minZoomValue) / 100f + m_minZoomValue;
                float duration = Mathf.Max(0.5f, Mathf.Abs(m_smoothZoomTargetValue - m_smoothZoomStartValue) / 10f);

                m_currentSmoothZoomTime = 0;
                m_smoothZoomTotalTime = duration;

                m_smoothFovStartValue = m_fovValue;
                m_smoothFovTargetValue = newFovValue;
                m_fovSmoothTime = 0;
                m_fovSmoothTotalTime = duration;
            }
        }

        public void SetCameraMoveFieldRange(float x, float y, float width, float height)
        {
            m_logicSceneMapField = new Rect(x, y, width, height);
        }

        #region Private

        private void CameraMoveEnd()
        {
            if (m_cameraMoveEndAction != null)
            {
                Action onSmoothMoveEnd = m_cameraMoveEndAction;
                m_cameraMoveEndAction = null;
                onSmoothMoveEnd();
            }
        }

        private void EventOnCameraFollowBegin(GameObject go, FollowMode mode)
        {
            m_followMode = mode;
            m_followModeTargetPosition = m_position;
            m_followGameObject = m_followMode == FollowMode.None ? null : go;
        }
        private void EventOnCameraFollowEnd()
        {
            m_followMode = FollowMode.None;
            m_followGameObject = null;
        }
        private void EventOnTouchBegan(InputData eventData)
        {
            if (!m_isEnabled) return;
            m_isTouchMoving = true;
            //Debug.LogError("EventOnTouchBegan: " + m_isTouchMoving);
        }
        private void EventOnTouchMoved(InputData eventData)
        {
            if (!m_isEnabled) return;
            if (eventData.m_inputType == InputType.Touch)
            {
                {
                    //Vector2 deltaMoveValue = GetViewPos(eventData.m_deltaPosition) * 0.25f;
                    //Vector3 touchMoveVelocity = deltaMoveValue.x * m_cameraRight + deltaMoveValue.y * m_cameraForward;
                    //m_touchMoveInertiaVelocity = touchMoveVelocity;
                    //m_position += touchMoveVelocity;
                }
                Vector2 deltaMoveValue = GetViewPos(eventData.m_deltaPosition) * m_manualMoveSpeedFactor;
                Vector3 deltaMovePosition = deltaMoveValue.x * m_cameraRight + deltaMoveValue.y * m_cameraForward;
                m_prevDeltaMovePosition = Vector3.SmoothDamp(m_prevDeltaMovePosition, deltaMovePosition, ref m_touchMoveInertiaVelocity, 0.1f, 10000);
                m_position += deltaMovePosition;
            }
            else if (eventData.m_inputType == InputType.Keyboard)
            {
                Vector2 deltaMoveValue = eventData.m_deltaPosition * m_keyboardMoveSpeedFactor;
                Vector3 touchMoveVelocity = deltaMoveValue.x * m_cameraRight + deltaMoveValue.y * m_cameraForward;
                m_position += touchMoveVelocity;
            }
        }
        private void EventOnTouchEnded(InputData eventData)
        {
            if (!m_isEnabled) return;
            m_isTouchMoving = false;
            //Debug.LogError("EventOnTouchEnded: " + m_isTouchMoving);
        }
        private void EventOnZoomChange(InputData eventData)
        {
            if (!m_isEnabled) return;
            m_smoothZoomTotalTime = 0;
            //Debug.LogError("EventOnZoomChange: " + deltaZoomValue);
            Single zoomSpeedFactor = BattleParamSetting.instance.m_battleCameraSetting.m_cameraZoomSpeedFactor;
            Single tempZoomValue = m_currentZoomValue + (eventData.m_deltaValue - 1) * zoomSpeedFactor;
            m_currentZoomValue = Mathf.Clamp(tempZoomValue, m_minZoomValue, m_maxZoomValue);
            ApplyCameraZoomValue();
        }
        private void TickTouchMovedInertia(ref Vector3 position, Single deltaTime)
        {
            //处理手动移动
            //float speed;
            //if ((speed = m_touchMoveInertiaVelocity.magnitude) > 0)
            //{
            //    float speed2 = Mathf.Max(speed - speed * ts.deltaTime * 7.5f, 0);
            //    m_touchMoveInertiaVelocity = m_touchMoveInertiaVelocity / speed * speed2;
            //    m_position += m_touchMoveInertiaVelocity;
            //}
            m_position = GetLogicMapReachablePos(m_position);
            if (m_prevDeltaMovePosition.magnitude > Single.Epsilon)
            {
                m_prevDeltaMovePosition = Vector3.SmoothDamp(m_prevDeltaMovePosition, Vector3.zero, ref m_touchMoveInertiaVelocity, 0.1f, 10000, deltaTime);
                position += m_prevDeltaMovePosition;
            }
        }

        private void TickFollow(FollowMode followMode, ref Vector3 position, Single deltaTime)
        {
            if (followMode == FollowMode.SoftZone)
            {
                TickSoftZoneFollow(ref position, deltaTime);
            }
            else if (followMode == FollowMode.HardPoint)
            {
                TickHardPointFollow(ref position, deltaTime);
            }
        }
        private void TickSoftZoneFollow(ref Vector3 position, Single deltaTime)
        {
            if (m_followGameObject == null)
            {
                return;
            }
            if (m_mainCamera == null)
            {
                return;
            }

            var viewPort = m_mainCamera.WorldToViewportPoint(m_followGameObject.transform.position);
            if (viewPort.x < m_minSoftZone.x || viewPort.x > m_maxSoftZone.x || viewPort.y < m_minSoftZone.y || viewPort.y > m_maxSoftZone.y)
            {
                float speed = 0.5f;
                if (viewPort.x < m_minSoftZone.x)
                    m_followModeTargetPosition = position - m_cameraRight * speed;
                else if (viewPort.x > m_maxSoftZone.x)
                    m_followModeTargetPosition = position + m_cameraRight * speed;

                if (viewPort.y < m_minSoftZone.y)
                    m_followModeTargetPosition = position - m_cameraForward * speed;
                else if (viewPort.y > m_maxSoftZone.y)
                    m_followModeTargetPosition = position + m_cameraForward * speed;
            }
            if (Vector3.Distance(position, m_followModeTargetPosition) > float.Epsilon)
            {
                position = Vector3.SmoothDamp(position, m_followModeTargetPosition, ref m_followMoveVelocity, 0.1f, 1000, deltaTime);
            }
        }
        private void TickHardPointFollow(ref Vector3 position, Single deltaTime)
        {
            if (m_followGameObject == null)
            {
                return;
            }
            m_followModeTargetPosition = m_followGameObject.transform.position;
            MoveTargetCorrection(ref m_followModeTargetPosition);
            if (Vector3.Distance(position, m_followModeTargetPosition) > float.Epsilon)
            {
                position = Vector3.SmoothDamp(position, m_followModeTargetPosition, ref m_followMoveVelocity, 0.1f, 1000, deltaTime);
            }
        }

        /// <summary> 平滑插值相机到指定位置 </summary>
        protected void SmoothUpdateCameraPosition(Single dt, ref Vector3 position)
        {
            m_currentSmoothMoveTime += dt;
            float a = Mathf.Clamp01(m_currentSmoothMoveTime / m_smoothMoveTotalTime);
            //position = Vector3.LerpUnclamped(m_smoothMoveStartPosition, m_smoothMoveTargetPosition, Easing.Sinusoidal.InOut(a));
            position = Vector3.LerpUnclamped(m_smoothMoveStartPosition, m_smoothMoveTargetPosition, Animancer.Easing.Quadratic.Out(a));
            if (!IsSmoothMoving())
                CameraMoveEnd();
        }
        protected void SmoothUpdateCameraZoomValue(Single dt, ref Single zoomValue)
        {
            m_currentSmoothZoomTime += dt;
            float a = Mathf.Clamp01(m_currentSmoothZoomTime / m_smoothZoomTotalTime);
            zoomValue = Mathf.LerpUnclamped(m_smoothZoomStartValue, m_smoothZoomTargetValue, Animancer.Easing.Quadratic.Out(a));
        }
        protected void SmoothUpdateCameraFOVValue(Single dt, ref Single fovValue)
        {
            m_fovSmoothTime += dt;
            float a = Mathf.Clamp01(m_fovSmoothTime / m_fovSmoothTotalTime);
            fovValue = Mathf.LerpUnclamped(m_smoothFovStartValue, m_smoothFovTargetValue, a);
        }

        protected Vector2 GetViewPos(Vector2 p)
        {
            return new Vector2(p.x / Screen.width, p.y / Screen.height) * 2 * 10 * m_mainCameraAspect;
        }

        /// <summary> 是否正在平滑移动中 </summary>
        protected bool IsSmoothMoving()
        {
            return m_smoothMoveTotalTime > 0 && m_currentSmoothMoveTime < m_smoothMoveTotalTime;
        }
        protected bool IsSmoothZooming()
        {
            return m_smoothZoomTotalTime > 0 && m_currentSmoothZoomTime < m_smoothZoomTotalTime;
        }
        protected bool IsSmoothUpdateFOV()
        {
            return m_fovSmoothTotalTime > 0 && m_fovSmoothTime < m_fovSmoothTotalTime;
        }
        protected void UpdateCameraMoveDir()
        {
            if (m_cameraZoomDollyCart != null)
            {
                m_cameraForward = m_cameraZoomDollyCart.m_Path.transform.forward;
                m_cameraRight = m_cameraZoomDollyCart.m_Path.transform.right;
            }
        }

        private void MoveTargetCorrection(ref Vector3 pos)
        {
            if (pos.x < m_logicSceneMapField.x)
                pos.x = m_logicSceneMapField.x;
            else if (pos.x > m_logicSceneMapField.x + m_logicSceneMapField.width)
                pos.x = m_logicSceneMapField.x + m_logicSceneMapField.width;

            if (pos.z < m_logicSceneMapField.y)
                pos.z = m_logicSceneMapField.y;
            else if (pos.z > m_logicSceneMapField.y + m_logicSceneMapField.height)
                pos.z = m_logicSceneMapField.y + m_logicSceneMapField.height;
        }
        
        /// <summary> 根据速度获取相机此次移动所需要的时间 </summary>
        protected float GetCameraMoveTime(float speed)
        {
            float distance = Vector3.Distance(m_position, m_smoothMoveTargetPosition);
            float time = distance / speed;
            return time > 0 ? time : 0.01f;
        }

        protected Vector3 GetLogicMapReachablePos(Vector3 origin)
        {
            if (origin.x < m_logicSceneMapField.x)
            {
                origin.x = m_logicSceneMapField.x;
                ResetCameraAutoMoveParam();
            }
            else if (origin.x > m_logicSceneMapField.x + m_logicSceneMapField.width)
            {
                origin.x = m_logicSceneMapField.x + m_logicSceneMapField.width;
                ResetCameraAutoMoveParam();
            }

            if (origin.z < m_logicSceneMapField.y)
            {
                origin.z = m_logicSceneMapField.y;
                ResetCameraAutoMoveParam();
            }
            else if (origin.z > m_logicSceneMapField.y + m_logicSceneMapField.height)
            {
                origin.z = m_logicSceneMapField.y + m_logicSceneMapField.height;
                ResetCameraAutoMoveParam();
            }

            return origin;
        }
        protected void ResetCameraAutoMoveParam()
        {
            m_touchMoveInertiaVelocity = Vector2.zero;
            m_prevDeltaMovePosition = Vector3.zero;
        }

        protected void ApplyCameraPositionValue(Boolean smoothDamp, Single deltaTime)
        {
            m_position = GetLogicMapReachablePos(m_position);
            if (smoothDamp)
            {
                if (Vector3.Distance(m_localPosition, m_position) > float.Epsilon)
                {
                    m_localPosition = Vector3.SmoothDamp(m_localPosition, m_position, ref m_touchMoveVelocity, 0.02f, 100000, deltaTime);
                }
            }
            else
            {
                m_localPosition = m_position;
            }
            m_root.transform.localPosition = m_localPosition;
        }
        protected void ApplyCameraZoomValue()
        {
            m_cameraZoomDollyCart.m_PathPosition = CurrentZoomValue;
        }
        protected void ApplyCameraFOVValue()
        {
            m_virtualCamera.m_Lens.FieldOfView = m_fovValue;
        }
        #endregion
    }
}