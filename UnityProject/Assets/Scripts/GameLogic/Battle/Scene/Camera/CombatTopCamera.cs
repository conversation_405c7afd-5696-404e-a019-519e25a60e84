using Cinemachine;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CombatTopCamera : VirtualCameraBase
    {
        #region Field
        private CinemachineVirtualCamera m_virtualCamera;
        #endregion
        public CombatCameraId NextCameraId { get; set; }

        public CombatTopCamera(GameObject root) : base(root) { }

        public void Initialize()
        {
            m_virtualCamera = m_root.GetComponentInChildren<CinemachineVirtualCamera>();
            m_virtualCamera.m_Lens.FieldOfView = CinemachineCameraSetting.instance.SettingData.m_combatTopCameraFieldOfView;
            Disable();
        }



        public void UnInitialize() { }

        public void UpdateCamera(Vector3 attackerPos, Vector3 hitterPos, float degree)
        {
            Vector3 targetGroupPosition = (attackerPos + hitterPos) / 2f;
            float posX = Mathf.Cos(degree * Mathf.Deg2Rad) * 5f;
            float posY = 5;
            float posZ = Mathf.Sin(degree * Mathf.Deg2Rad) * 5f;
            Vector3 deltaPosition = new Vector3(posX, posY, posZ);

            // Vector3 direction = new Vector3(Mathf.Sin(degree), 0, Mathf.Cos(degree)); // TODO : 临时使用的初始攻击方向
            //Vector3 targetPosition = targetGroupPosition - direction * 5f;
            //targetPosition.y = targetPosition.y + 10f;

            m_virtualCamera.transform.position = targetGroupPosition + deltaPosition;
            m_virtualCamera.transform.forward = -deltaPosition;
        }
    }
}