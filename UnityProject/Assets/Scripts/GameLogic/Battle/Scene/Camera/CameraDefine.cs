
namespace Phoenix.GameLogic.Battle
{
    public enum VirtualCameraMode : byte
    {
        MainCamera,
        CombatTopCamera,
        LeftCombatCamera,
        RightCombatCamera,
        Max
    }

    /// <summary> 相机移动模式枚举 </summary>
    public enum FollowMode : byte
    {
        /// <summary> 默认 </summary>
        None = 0,
        /// <summary> 区域跟随 </summary>
        SoftZone,
        /// <summary> 点跟随 </summary>
        HardPoint
    }


    public enum CombatCameraId
    {
        None,
        Left,
        Right,
    }
}