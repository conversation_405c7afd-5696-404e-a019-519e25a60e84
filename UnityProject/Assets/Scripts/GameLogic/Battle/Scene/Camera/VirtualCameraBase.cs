using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public abstract class VirtualCameraBase : IVirtualCamera
    {
        protected GameObject m_root;
        protected bool m_isEnabled;

        public VirtualCameraBase(GameObject root)
        {
            m_root = root;
        }

        public virtual void OnTick(TimeSlice timeSlice) { }

        public virtual void Enable()
        {
            m_isEnabled = true;
            m_root.SetActive(true);
        }

        public virtual void Disable()
        {
            m_isEnabled = false;
            m_root.SetActive(false);
        }

    }
}

