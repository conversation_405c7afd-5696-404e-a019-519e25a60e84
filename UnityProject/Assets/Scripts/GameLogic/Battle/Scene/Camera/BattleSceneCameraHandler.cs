using System;
using UnityEngine;
using Phoenix.Core;
using Cinemachine;
using Phoenix.Battle;
using System.Collections.Generic;
using Phoenix.CameraEffect;
using Phoenix.ConfigData;
using Cysharp.Threading.Tasks;

namespace Phoenix.GameLogic.Battle
{
    public class BattleSceneCameraHandler
    {
        private Camera m_camera;
        private GameObject m_root;
        private GameObject m_mainCameraRoot;
        private GameObject m_combatTopCameraRoot;
        private GameObject m_leftCombatCameraRoot;
        private GameObject m_rightCombatCameraRoot;

        private IVirtualCamera m_currentVirtualCamera;
        private MainCamera m_mainCamera;
        private CombatTopCamera m_combatTopCamera;
        private CombatCamera m_leftCombatCamera;
        private CombatCamera m_rightCombatCamera;
        private CameraCrossFadeEffect m_cameraCrossFadeEffect;


        private CinemachineBrain m_cinemachineBrain;
        public float MainCameraZoomPercentValue { get { return m_mainCamera.CurrentZoomPercentValue; } }

        public bool Initialize(GameObject root, Camera mainCamera, Int32 initCameraRotateOffset)
        {
            m_root = root;
            m_mainCameraRoot = m_root.FindChild("MainCameraRoot");
            m_combatTopCameraRoot = m_root.FindChild("CombatCameraRoot(Top)");
            m_leftCombatCameraRoot = m_root.FindChild("CombatCameraRoot(Left)");
            m_rightCombatCameraRoot = m_root.FindChild("CombatCameraRoot(Right)");

            m_cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
            m_cinemachineBrain.m_UpdateMethod = CinemachineBrain.UpdateMethod.ManualUpdate;
            InitializeCinemachineBrain(m_cinemachineBrain);

            m_mainCamera = new MainCamera(m_mainCameraRoot, mainCamera, initCameraRotateOffset);
            m_mainCamera.Initialize();
            m_combatTopCamera = new CombatTopCamera(m_combatTopCameraRoot);
            m_combatTopCamera.Initialize();
            m_leftCombatCamera = new CombatCamera(m_leftCombatCameraRoot);
            m_leftCombatCamera.Initialize();
            m_rightCombatCamera = new CombatCamera(m_rightCombatCameraRoot);
            m_rightCombatCamera.Initialize();
            m_cameraCrossFadeEffect = mainCamera.GetOrAddComponent<CameraCrossFadeEffect>();
            SwitchVirtualCamera(VirtualCameraMode.MainCamera);
            return true;
        }

        public void OnTick(TimeSlice timeSlice)
        {
            m_mainCamera?.OnTick(timeSlice);
            m_cinemachineBrain?.ManualUpdate();

#if UNITY_EDITOR
            Test();
#endif
        }

        public void UnInitialize()
        {
            m_mainCamera?.UnInitialize();
            m_leftCombatCamera?.UnInitialize();
            m_rightCombatCamera?.UnInitialize();
            m_combatTopCamera?.UnInitialize();
            m_cameraCrossFadeEffect = null;
        }

        public void SetCameraMoveFieldRange(float x, float y, float width, float height)
        {
            m_mainCamera.SetCameraMoveFieldRange(x, y, width, height);
        }


        public void GenerateBattleCameraRandomIndex()
        {
            CinemachineCameraSetting.instance.SettingData.GenerateRandomIndex();
        }

        private GameObject GetLookAtObj(EntityView entityView)
        {
            GameObject resultObject = entityView.GetBindPointObj(EntityBindPointId.BattleCombatLookAt);
            if (resultObject == null)
            {
                resultObject = entityView.GetBindPointObj(EntityBindPointId.Chest);
            }
            if (resultObject == null)
            {
                resultObject = entityView.gameObject;
            }
            return resultObject;
        }

        public void InitCombatCamera(CombatCameraId id, EntityView sourceEntity, EntityView targetEntity)
        {
            GameObject sourceEntityLookAt = GetLookAtObj(sourceEntity);
            GameObject targetEntityLookAt = GetLookAtObj(targetEntity);
            float standardRotation = CalculateRotataDegree(sourceEntity.transform.position, targetEntity.transform.position);
            //m_leftCombatCameraRotateDegree = 0;
            //m_rightCombatCameraRotateDegree = 0;
            switch (id)
            {
                case CombatCameraId.Left:
                    float leftRotationX = CinemachineCameraSetting.instance.SettingData.BattleCamera1.m_battleCameraElevationAngel;
                    float leftRotationY = CinemachineCameraSetting.instance.SettingData.BattleCamera1.m_battleCameraRotationY;
                    float leftDutch = CinemachineCameraSetting.instance.SettingData.BattleCamera1.m_battleCameraDutch;

                    CinemachineCameraSetting.TargetGroupSetting targetGroup1 = CinemachineCameraSetting.instance.SettingData.BattleCamera1.m_targetGroupSetting;
                    float leftRotateY = 90 - standardRotation + 90 + leftRotationY + 180;
                    m_leftCombatCamera.UpdateCombatCamera(leftRotationX, leftRotateY, leftDutch, sourceEntityLookAt.transform, targetEntityLookAt.transform, targetGroup1);
                    m_leftCombatCamera.Disable();
                    break;
                case CombatCameraId.Right:
                    float rightRotationX = CinemachineCameraSetting.instance.SettingData.BattleCamera2.m_battleCameraElevationAngel;
                    float rightRotationY = CinemachineCameraSetting.instance.SettingData.BattleCamera2.m_battleCameraRotationY;
                    float rightDutch = CinemachineCameraSetting.instance.SettingData.BattleCamera2.m_battleCameraDutch;
                    CinemachineCameraSetting.TargetGroupSetting targetGroup2 = CinemachineCameraSetting.instance.SettingData.BattleCamera2.m_targetGroupSetting;
                    float rightRotateY = 90 - standardRotation - 90 + rightRotationY + 180;
                    m_rightCombatCamera.UpdateCombatCamera(rightRotationX, rightRotateY, rightDutch, sourceEntityLookAt.transform, targetEntityLookAt.transform, targetGroup2);
                    m_rightCombatCamera.Disable();
                    break;
            }
        }
        public void InitCombatTopCamera(CombatCameraId id, EntityView sourceEntity, EntityView targetEntity)
        {
            Vector3 sourceEntityPosition = sourceEntity.transform.position;
            Vector3 targetEntityPosition = targetEntity.transform.position;
            float standardRotation = CalculateRotataDegree(sourceEntity.transform.position, targetEntity.transform.position);

            //*
            //计算顶部相机偏移角度时，得到的是当前世界坐标系下的偏移角度
            //
            //*//
            switch (id)
            {
                case CombatCameraId.Left:
                    float leftRotationY = CinemachineCameraSetting.instance.SettingData.BattleCamera1.m_battleCameraRotationY;
                    m_combatTopCamera.UpdateCamera(sourceEntityPosition, targetEntityPosition, 180 + standardRotation + 90 - leftRotationY);
                    break;
                case CombatCameraId.Right:
                    float rightRotationY = CinemachineCameraSetting.instance.SettingData.BattleCamera2.m_battleCameraRotationY;
                    m_combatTopCamera.UpdateCamera(sourceEntityPosition, targetEntityPosition, 180 + standardRotation - (90 + rightRotationY));
                    break;
            }
            m_combatTopCamera.NextCameraId = id;
            m_combatTopCamera.Disable();
        }
        public void Switch2CombatTopCamera()
        {
            SwitchVirtualCamera(VirtualCameraMode.CombatTopCamera);
            TimerManager.instance.Start(0.1f, () => Switch2CombatCamera(m_combatTopCamera.NextCameraId));
        }
        public void Switch2CombatCamera(CombatCameraId id)
        {
            if (id == CombatCameraId.Left)
            {
                SwitchVirtualCamera(VirtualCameraMode.LeftCombatCamera);
            }
            else if (id == CombatCameraId.Right)
            {
                SwitchVirtualCamera(VirtualCameraMode.RightCombatCamera);
            }
        }

        public async UniTaskVoid Switch2MainCamera(Action onCaptureEnd = null, Action onCrossFadeEnd = null)
        {
            _ = m_cameraCrossFadeEffect.Capture();
            // 暂定延迟两帧
            await UniTask.DelayFrame(2);
            onCaptureEnd?.Invoke();
            m_cameraCrossFadeEffect.CrossFade(null, 0.3f);
            SwitchVirtualCamera(VirtualCameraMode.MainCamera);

            onCrossFadeEnd?.Invoke();
        }

        public Single SetCameraPositionWithSpeed(Vector3 targetPosition, float speed = 0, bool immediately = false, Action end = null)
        {
            return m_mainCamera.SetCameraPositionWithSpeed(targetPosition, speed, immediately, end);
        }
        public Single SetCameraGridPositionWithSpeed(GridPosition gridPosition, float speed = 0, bool immediately = false, Action end = null)
        {
            Vector3 targetPosition = BattleShortCut.GetGridWorldPosition(gridPosition);
            return m_mainCamera.SetCameraPositionWithSpeed(targetPosition, speed, immediately, end);
        }

        public void SetCameraZoomPercentAndFov(float zoomPercentValue, float newFovValue, Boolean immediately = false)
        {
            m_mainCamera.SetCameraZoomPercentAndFov(zoomPercentValue, newFovValue, immediately);
        }
        #region private

        private void SwitchVirtualCamera(VirtualCameraMode mode)
        {
            IVirtualCamera iCamera;
            switch (mode)
            {
                case VirtualCameraMode.MainCamera:
                    iCamera = m_mainCamera;
                    break;
                case VirtualCameraMode.CombatTopCamera:
                    iCamera = m_combatTopCamera;
                    break;
                case VirtualCameraMode.LeftCombatCamera:
                    iCamera = m_leftCombatCamera;
                    break;
                case VirtualCameraMode.RightCombatCamera:
                    iCamera = m_rightCombatCamera;
                    break;
                default:
                    iCamera = m_mainCamera;
                    break;
            }
            if (iCamera != m_currentVirtualCamera)
            {
                if (m_currentVirtualCamera != null)
                    m_currentVirtualCamera.Disable();
                m_currentVirtualCamera = iCamera;
                m_currentVirtualCamera.Enable();
            }
        }

        private void InitializeCinemachineBrain(CinemachineBrain cinemachineBrain)
        {
            var cameraBlendSetting = ScriptableObject.CreateInstance<CinemachineBlenderSettings>();
            cameraBlendSetting.hideFlags = HideFlags.HideAndDontSave;


            if (CinemachineCameraSetting.instance.SettingData != null)
            {
                cameraBlendSetting.m_CustomBlends = new CinemachineBlenderSettings.CustomBlend[CinemachineCameraSetting.instance.SettingData.m_mainCameraBlendSettings.Length];
                Int32 i = 0;
                foreach (var setting in CinemachineCameraSetting.instance.SettingData.m_mainCameraBlendSettings)
                {
                    CinemachineBlenderSettings.CustomBlend customBlend = new CinemachineBlenderSettings.CustomBlend
                    {
                        m_From = setting.m_from,
                        m_To = setting.m_to,
                        m_Blend = setting.m_style
                    };
                    cameraBlendSetting.m_CustomBlends[i++] = customBlend;
                }
            }
            else
            {
                List<CinemachineBlenderSettings.CustomBlend> customBlends = new List<CinemachineBlenderSettings.CustomBlend>
                {
                    //new CinemachineBlenderSettings.CustomBlend
                    //{
                    //    m_From = "MainCamera",
                    //    m_To = "CombatCamera",
                    //    m_Blend = new CinemachineBlendDefinition { m_Style = CinemachineBlendDefinition.Style.EaseIn, m_Time = 0.5f }
                    //},
                    new CinemachineBlenderSettings.CustomBlend
                    {
                        m_From = "CombatTopCamera",
                        m_To = "CombatCamera",
                        m_Blend = new CinemachineBlendDefinition { m_Style = CinemachineBlendDefinition.Style.EaseIn, m_Time = 0.3f }
                    },
                    new CinemachineBlenderSettings.CustomBlend
                    {
                        m_From = "CombatCamera",
                        m_To = "CombatCamera",
                        m_Blend = new CinemachineBlendDefinition { m_Style = CinemachineBlendDefinition.Style.EaseIn, m_Time = 0.2f }
                    }
                };
                cameraBlendSetting.m_CustomBlends = customBlends.ToArray();
            }


            cinemachineBrain.m_CustomBlends = cameraBlendSetting;
            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition { m_Style = CinemachineBlendDefinition.Style.Cut };
        }

        private float CalculateRotataDegree(Vector3 pos1, Vector3 pos2)
        {
            Vector2 dir = new Vector2(pos2.x - pos1.x, pos2.z - pos1.z);
            float degree = Mathf.Atan2(dir.y, dir.x) * Mathf.Rad2Deg;
            return degree;
        }

        #endregion



        #region Test

        private void Test()
        {
            //if (Input.GetKeyDown(KeyCode.L))
            //{
            //    SwitchVirtualCamera(VirtualCameraMode.LeftCombatCamera);
            //}
            //if (Input.GetKeyDown(KeyCode.R))
            //{
            //    SwitchVirtualCamera(VirtualCameraMode.RightCombatCamera);
            //}
            //if (Input.GetKeyDown(KeyCode.M))
            //{
            //    SwitchVirtualCamera(VirtualCameraMode.MainCamera);
            //}


            //if (Input.GetKeyDown(KeyCode.Space))
            //{
            //    CameraShakeSystem.Shake(ShakeParam.Small);
            //}

            if (Input.GetKeyDown(KeyCode.F5))
            {
                //GameSettingInitializer gameSetting = new GameSettingInitializer();
                //gameSetting.StartSync();
                InitializeCinemachineBrain(m_cinemachineBrain);
            }


            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                SetCameraZoomPercentAndFov(0, 30);
            }
            if (Input.GetKeyDown(KeyCode.Alpha5))
            {
                SetCameraZoomPercentAndFov(55, 20);
            }
            if (Input.GetKeyDown(KeyCode.Alpha9))
            {
                SetCameraZoomPercentAndFov(100, 20);
            }
        }
        #endregion
    }
}
