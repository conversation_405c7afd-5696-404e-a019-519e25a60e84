using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public static class SkillIndicatorUtility
    {
        public static SkillSelectIndicatorData GetSelectIndicatorData(Skill skill, List<short> paramList)
        {
            SkillSelectIndicatorData data = new SkillSelectIndicatorData();
            if (skill == null || skill.skillInfo == null || skill.skillInfo.selectStep == null)
            {
                return data;
            }
            IBattle battle = BattleShortCut.sampleBattle;
            var originEntity = skill.entity;
            var originSelectInfo = new TargetSelectInfo(originEntity);
            var stepInfo = skill.skillInfo.selectStep;
            var extraRange = skill.GetExtraSelectRange();
            var effectInfo = skill.skillInfo.effectList.GetValueSafely(skill.skillInfo.mainEffectIndex);
            if(effectInfo == null)
            {
                return data;
            }
            using var paramContainer = battle.FetchObj<TargetSelectParamContainer>();
            paramContainer.Init(paramList);
            var stepResult = TargetSelectUtility.HandleSelect(battle, stepInfo, originEntity, originEntity.GetLocation(), extraRange, paramContainer);
            if (stepResult.errorCode == BattleErrorCode.Ok)
            {
                data.done = true;
            }
            else if (stepResult.errorCode == BattleErrorCode.TargetSelectStepParamCanNotPop)
            {
                var posCollection = battle.CreateFieldSummaryForPosCollection();
                TargetSelectUtility.AppendInterruptRange(battle, stepInfo, stepResult, posCollection);
                foreach (var pos in posCollection.GetPosList())
                {
                    var posData = new SkillSelectIndicatorPosData();
                    posData.pos = pos;
                    posData.selectable = TargetSelectUtility.CheckSelectable(battle, stepInfo, originEntity, originEntity.GetLocation(), extraRange, paramContainer, false, pos);
                    data.posDataList.Add(posData);
                }
                posCollection.Release();
            }
            stepResult.Release();
            return data;
        }

        public static SkillEffectIndicatorData GetEffectIndicatorData(Skill skill, List<short> paramList)
        {
            SkillEffectIndicatorData data = new SkillEffectIndicatorData();
            if (skill == null || skill.skillInfo == null)
            {
                return data;
            }
            IBattle battle = BattleShortCut.sampleBattle;
            var skillInfo = skill.skillInfo;
            if (skill.ContainsTag(SkillTagType.AssistGuard))
            {
                CollectGridListOfAssistGuardSkill(skill.entity, skill, skill.entity.GetLocation(), data);
            }
            else
            {
                var paramContainer = battle.FetchObj<TargetSelectParamContainer>();
                paramContainer.Init(paramList);
                BattleVagueParamValueSet valueSet = SkillForcastUtiltiy.GetForcastValueSet(battle, skill, paramContainer);
                var effectInfo = skillInfo.effectList.GetValueSafely(skillInfo.mainEffectIndex);
                CollectGridList(effectInfo, valueSet, data);
                valueSet.Release();
                paramContainer.Release();
            }
            return data;
        }

        private static void CollectGridList(SkillEffectInfo effectInfo, BattleVagueParamValueSet valueSet, SkillEffectIndicatorData data)
        {
            if (effectInfo == null)
            {
                return;
            }
            using var effectHandler = BattleFactory.CreateSkillEffectHandler(valueSet.battle, effectInfo.effectType);
            using var posCollection = BattleShortCut.sampleBattle.CreateFieldSummaryForPosCollection();
            effectHandler.CollectGridPos(effectInfo, BattleActionEffectConditionAriseType.SkillEffect, valueSet, posCollection);
            foreach (var pos in posCollection.GetPosList())
            {
                SkillEffectIndicatorPosData positionData = new SkillEffectIndicatorPosData();
                positionData.pos = pos;
                data.posDataList.Add(positionData);
            }
            using var entityList = valueSet.battle.FetchList<IEntity>();
            effectHandler.CollectEntity(effectInfo, BattleActionEffectConditionAriseType.SkillEffect, valueSet, entityList);
            foreach (var entity in entityList)
            {
                data.effectEntityUidList.Add(entity.uid);
            }
        }

        private static void CollectGridListOfAssistGuardSkill(IEntity entity, Skill skill, GridPosition pos, SkillEffectIndicatorData data)
        {
            var battle = entity.GetBattle();
            if (skill == null)
            {
                return;
            }
            var posCollection = battle.CreateFieldSummaryForPosCollection();
            var filterEntityList = battle.FetchObj<List<IEntity>>();
            foreach (var effectInfo in skill.skillInfo.effectList)
            {
                if (effectInfo.effectType != SkillEffectFuncType.AttachBuff_Rid)
                {
                    continue;
                }
                var attachBuffInfo = effectInfo as SkillEffectInfo_AttachBuff_Rid;
                foreach (var buffItem in attachBuffInfo.itemList)
                {
                    var buffInfo = battle.infoGetter.GetBuffInfo(buffItem.buffRid);
                    if (buffInfo == null)
                    {
                        continue;
                    }
                    foreach (var buffEffectInfo in buffInfo.effectList)
                    {
                        if (buffEffectInfo.effectType != BuffEffectType.StateApply)
                        {
                            continue;
                        }
                        var stateApplyInfo = buffEffectInfo as BuffEffectInfo_StateApply;
                        if (stateApplyInfo.stateType != BuffEffectStateType.AssistGuard)
                        {
                            continue;
                        }
                        var guardInfo = stateApplyInfo as BuffEffectInfo_AssistGuard;
                        TargetSelectRangeInfo rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(battle, guardInfo.rangeId);
                        if (rangeInfo == null)
                        {
                            continue;
                        }
                        TargetSelectUtility.AppendRangePosList(battle, rangeInfo, new TargetSelectInfo(entity, pos), GridDirType.None, posCollection);
                        foreach (var gridPos in posCollection.GetPosList())
                        {
                            var positionData = data.posDataList.Find(p => p.pos == gridPos);
                            if (positionData == null)
                            {
                                positionData = new SkillEffectIndicatorPosData();
                                positionData.pos = gridPos;
                                data.posDataList.Add(positionData);
                            }
                            TargetSelectUtility.CheckFilterAndCollectEntity(battle, guardInfo.filterFuncType, new TargetSelectFilterContext(entity), gridPos, filterEntityList);
                        }
                        posCollection.Reset();
                    }
                }
            }
            foreach (var filterEntity in filterEntityList)
            {
                if (filterEntity == entity)
                {
                    continue;
                }
                data.effectEntityUidList.Add(filterEntity.uid);
            }
            battle.Release(filterEntityList);
            posCollection.Release();
        }
    }
}
