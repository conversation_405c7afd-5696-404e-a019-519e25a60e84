using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public static class BuffPerformanceUtility
    {
        public static void PerformanceBuffChange(IList<BuffChangeResult> resultList)
        {
            int resultCount = resultList.Count;
            for (int i = 0; i < resultCount; ++i)
            {
                PerformanceBuffChange(resultList[i]);
            }
        }

        public static void PerformanceBuffChange(BuffChangeResult result)
        {
            switch (result.changeType)
            {
                case BuffChangeType.Attach:
                    BuffAttachResult attachResult = result as BuffAttachResult;
                    PerformanceBuffAttach(attachResult);
                    break;
                case BuffChangeType.LifeTime:
                    BuffChangeLifeTimeResult changeLifeTimeResult = result as BuffChangeLifeTimeResult;
                    PerformanceChangeBuffLifeTime(changeLifeTimeResult);
                    break;
                case BuffChangeType.Detach:
                    BuffDetachResult detachResult = result as BuffDetachResult;
                    PerformanceBuffDetach(detachResult);
                    break;
            }
        }

        public static void PerformanceBuffAttach(BuffAttachResult result)
        {
            if (result == null)
            {
                return;
            }
            EntityView entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
            if (entityView == null)
            {
                return;
            }
            if (entityView.buffComponent != null)
            {
                entityView.buffComponent.Attach_Sync(result);
            }
            if (entityView.skillComponent != null)
            {
                var passiveSkill = entityView.GetPassiveSkillByUid(result.passiveUid);
                if (passiveSkill != null)
                {
                    passiveSkill.AddBuffUid(result.buffUid);
                }
            }
            var buffConfig = ConfigDataManager.instance.GetBuff(result.buffRid);
            if (buffConfig != null)
            {
                if (buffConfig.attachEffectId > 0)
                {
                    var particleConfig = ConfigDataManager.instance.GetParticle(buffConfig.attachEffectId);
                    var obj = entityView.GetBindPointObj(particleConfig.BindPointId);
                    ParticleManager.instance.Spawn(particleConfig.ResPath, obj, true, particleConfig.UseEntityModelScale, particleConfig.UseEntityParticleScale ? entityView.GetParticleScale() : 1f);
                }
                if (buffConfig.loopEffectId > 0)
                {
                    var particleConfig = ConfigDataManager.instance.GetParticle(buffConfig.loopEffectId);
                    var obj = entityView.GetBindPointObj(particleConfig.BindPointId);
                    var particleInfo = ParticleManager.instance.Spawn(particleConfig.ResPath, obj, true, particleConfig.UseEntityModelScale, particleConfig.UseEntityParticleScale ? entityView.GetParticleScale() : 1f);
                    if (particleInfo != null && !entityView.buffParticleMap.ContainsKey(result.buffUid))
                    {
                        entityView.buffParticleMap.Add(result.buffUid, particleInfo.go);
                    }
                }
                if (!string.IsNullOrEmpty(buffConfig.loopAnimName))
                {
                    var idleBehaviour = entityView.GetBehaviour<EntityPlayIdleBehaviour>();
                    if (idleBehaviour != null)
                    {
                        idleBehaviour.OverrideIdleName(buffConfig.loopAnimName);
                        idleBehaviour.PlaySceneIdle(false);
                    }
                }
            }
            EventManager.instance.Broadcast(EventID.Entity_Buff_Added, result.entityUid, result.buffUid);
        }

        public static void PerformanceBuffDetach(BuffDetachResult result)
        {
            if (result == null)
            {
                return;
            }
            EntityView entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
            if (entityView == null)
            {
                return;
            }
            entityView.buffComponent.DetachBuff_Sync(result);
            GameObject particleObj;
            if (entityView.buffParticleMap.TryGetValue(result.buffUid, out particleObj))
            {
                ParticleManager.instance.DeSpawn(particleObj);
            }
            var buffConfig = ConfigDataManager.instance.GetBuff(result.buffRid);
            if (buffConfig != null)
            {
                if (!string.IsNullOrEmpty(buffConfig.loopAnimName))
                {
                    var idleBehaviour = entityView.GetBehaviour<EntityPlayIdleBehaviour>();
                    if (idleBehaviour != null)
                    {
                        idleBehaviour.ResetIdleName();
                        idleBehaviour.PlaySceneIdle(false);
                    }
                }
            }
            EventManager.instance.Broadcast(EventID.Entity_Buff_Removed, result.entityUid, result.buffUid);
        }

        public static void PerformanceChangeBuffLifeTime(BuffChangeLifeTimeResult result)
        {
            if (result == null)
            {
                return;
            }
            EntityView entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
            if (entityView == null)
            {
                return;
            }
            entityView.ChangeBuffLeftLifeTime(result.buffUid, result.leftLiftTime);
            EventManager.instance.Broadcast(EventID.Entity_Buff_Updated, result.entityUid);
        }

        public static void PerformanceAttributeChange(int entityUid, List<EntityAttributeChangeResult> resultList)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if(entityView == null)
            {
                return;
            }
            for (int i = 0; i < resultList.Count; ++i)
            {
                EntityAttributeChangeResult result = resultList[i];
                if (result == null)
                {
                    continue;
                }
                EntityAttribute attribute = entityView.GetAttribute(result.attribute.attributeId, true);
                FixedValue preValue = attribute.GetValue(false);
                attribute.CopyForm(result.attribute);
                if (result.attribute.attributeId == AttributeId.HpMax)
                {
                    entityView.SetCurHp(result.curHp);
                }
                EventManager.instance.Broadcast(EventID.Entity_Attibute_Changed, entityUid, result.attribute.attributeId, preValue, attribute.GetValue(false));
            }
        }

        public static void PerformanceChangeActiveList(List<BuffChangeActiveResult> resultList)
        {
            foreach (var result in resultList)
            {
                PerformanceChangeActive(result);
            }
        }

        public static void PerformanceChangeActive(BuffChangeActiveResult result)
        {
            var entity = BattleShortCut.sampleBattle.GetEntityByUid(result.entityUid);
            var buff = entity.GetBuffByUid(result.buffUid);
            var effect = buff.GetBuffEffect(result.effectIndex);
            effect.SetActive(result.isActive);
        }
    }
}
