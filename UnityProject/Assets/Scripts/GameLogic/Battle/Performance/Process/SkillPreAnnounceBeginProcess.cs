using System;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class SkillPreAnnounceBeginProcess : ProcessBase
    {
        public EntityActionEndResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            GridPosition grid = result.preAnnounceStepPosList.GetValueSafely(0);
            Single duration = BattleShortCut.battleScene.sceneCameraHandler.SetCameraGridPositionWithSpeed(grid);
            TimerManager.instance.Start(duration + 0.2f, OnCameraMoveEnd);
            //EventManager.instance.Broadcast(EventID.BattlePerformance_SkillPreAnnounceBegin, result.entityUid, result.preAnnounceSkillUid, posList);
        }

        private void OnCameraMoveEnd()
        {
            var entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
            entityView.SetPreAnnounce(result.preAnnounceSkillUid, result.preAnnounceStepPosList);
            Skill skill;
            var posList = BattleUtility.GetPreAnnounceGridList(entityView, out skill);
            if (posList == null)
            {
                End();
                return;
            }
            TurnDir(entityView, result.preAnnounceStepPosList);
            ShowTip(entityView, skill);
            SkillPredictBegin(result.entityUid, result.preAnnounceSkillUid, posList);
            DelayEnd(1f);
        }

        private void TurnDir(EntityView entityView, List<GridPosition> grids)
        {
            if (grids.Count > 0)
            {
                GridDirType gridDir = TargetSelectUtility.GetDir(BattleShortCut.sampleBattle, new TargetSelectInfo(entityView), grids[0]);
                if (gridDir != GridDirType.None)
                {
                    entityView.SetDirection(gridDir);
                }
            }
        }

        private void ShowTip(EntityView entityView, Skill skill)
        {
            String entityName = entityView.dataGetter.GetName();
            String skillName = skill.skillInfo.name;
            String skillTip = ConstStringUtility.GetConstString(ConfigData.ConstStringId.BattleSkillPredictBeginTip, entityName, "1", skillName);
            TipUI.ShowTip(skillTip);
        }

        private void SkillPredictBegin(Int32 entityUid, Int32 skillUid, List<GridPosition> grids)
        {
            BattleShortCut.battleScene.skillPredictHandler.SkillPredictBegin(entityUid, skillUid, grids);
        }
    }
}
