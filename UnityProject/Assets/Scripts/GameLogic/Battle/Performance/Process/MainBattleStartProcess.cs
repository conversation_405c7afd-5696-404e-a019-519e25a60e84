using System;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class MainBattleStartProcess : ProcessBase
    {
        public MainBattleStartProcedureResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            BattleShortCut.sampleBattle.SetStageState_Sync(BattleStageStateId.MainBattleStart);
            foreach (var buffAttachResult in result.buffAttachResultList)
            {
                BuffPerformanceUtility.PerformanceBuffAttach(buffAttachResult);
            }
            foreach(var teamEnergyChangeResult in result.teamEnergyChangeResultList)
            {
                var team = BattleShortCut.sampleBattle.GetTeamByUid(teamEnergyChangeResult.teamUid);
                if (team == null)
                {
                    continue;
                }
                team.sharedEnergy = teamEnergyChangeResult.updateEnergy;
            }
            foreach (var teamMaxEnergyChangeResult in result.teamMaxEnergyChangeResultList)
            {
                var team = BattleShortCut.sampleBattle.GetTeamByUid(teamMaxEnergyChangeResult.teamUid);
                if (team == null)
                {
                    continue;
                }
                team.maxSharedEnergy = teamMaxEnergyChangeResult.updateMaxEnergy;
            }
            BuffPerformanceUtility.PerformanceChangeActiveList(result.buffChangeActiveResultList);
            BattleShortCut.RestBattleShortCut();
            BattleShortCut.ChangeBattleState(BattleStateId.Main);
            //BattleUIStateHandler.ActiveState(BattleUIState.BattleStageAnnounce);
            EventManager.instance.Broadcast<Action>(EventID.BattlePerformance_BattleStartWinConditionShow, End);
            EventManager.instance.Broadcast(EventID.Entity_BlockHitPoint_Changed);
        }

        protected override void OnEnd()
        {
            //BattleUIStateHandler.DeactiveState(BattleUIState.BattleStageAnnounce);
            BattleOpModeManager.instance.ChangeMode(BattleOpModeId.BattleOpMode);
            EventManager.instance.Broadcast(EventID.BattlePerformance_BattleStart);
        }
    }
}
