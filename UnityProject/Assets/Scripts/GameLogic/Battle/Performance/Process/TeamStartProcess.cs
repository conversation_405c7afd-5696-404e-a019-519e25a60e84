
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class TeamStartProcess : ProcessBase
    {
        public int teamIndex;

        public override void OnRelease()
        {
            teamIndex = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            BattleShortCut.sampleBattle.SetCurTeamIndex(teamIndex);
            EventManager.instance.Broadcast(EventID.BattlePerformance_TeamStart);
            End();
        }
    }
}
