using System;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using Phoenix.Battle;
using Phoenix.GameLogic.GameContext;
using Phoenix.ConfigData;
using System.Collections.Generic;
using Phoenix.Core.Entity;

namespace Phoenix.GameLogic.Battle
{
    public class StageExitProcess : ProcessBase
    {
        public bool hasNextStage;
        public bool isWin;
        private BattleType m_battleType;

        protected override void OnStart()
        {
            DebugUtility.Log($"[StageExitProcess] --> OnStart  {hasNextStage} ");

            //    isWin = true;
            BattleStatisticReport report = BattleShortCut.logicBattle.CreateReport();
            if (report != null && report.playerWinLoseMap.TryGetValue(BattleShortCut.hostPlayerId, out isWin))
            {

            }
            //m_battleType = BattleType.None;
            //if (EntityAdmin.instance.CurEntity != null)
            //{
            //    isWin = true;
            //    BattleStatisticReport report = BattleShortCut.logicBattle.CreateReport();
            //    if (report != null && report.playerWinLoseMap.TryGetValue(BattleShortCut.hostPlayerId, out isWin))
            //    {

            //    }
            //}
            //if (isWin)
            //{
            //    if (EntityAdmin.instance.CurEntity != null)
            //    {
            //        MsgPackLogic_Battle.instance.Send_BattleVerify(ExcuteProcessResult, false);
            //    }
            //    else
            //    {
            //        ExcuteProcessResult();
            //    }
            //}
            //else
            //{
            //    ExcuteProcessResult();
            //}

            ExcuteProcessResult();
        }


        private void ExcuteProcessResult(bool isCheckSuccessful = true)
        {
            BattleShortCut.sampleBattle.SetStageState_Sync(BattleStageStateId.StageExit);
            BattleShortCut.sampleBattle.ExitStage_Sync();
            BattleUIStateHandler.DeactiveState(BattleUIState.BattleStoryPerform);
            EventManager.instance.Broadcast(EventID.BattleMode_ResetBattleUIState);

            if (!hasNextStage)
            {
                ProcessBattleResult();
            }
            else
            {
                ProcessBattleStage();
                End();
            }
        }

        private void ProcessBattleStage()
        {
            BattleStageInfo stageInfo = BattleShortCut.sampleBattle.GetCurStageInfo();
            if (stageInfo.stageType == BattleStageType.Scinerio
                || stageInfo.stageType == BattleStageType.ScinerioToWin
                || stageInfo.stageType == BattleStageType.ScinerioTimeline)
            {
                BattleHelper.UpdateOtherEntityBloodMask(false, BloodMask.StoryPerformance);
            }
            BattleShortCut.battleExecuter.stateMachine.ExitCurState();
            BattleOpModeManager.instance.ChangeMode(BattleOpModeId.Default);
            BattleShortCut.battleScene.Reset();
            EntityViewManager.instance.DestroyAllEntityView();
        }


        private void ProcessBattleResult()
        {
            m_battleType = GamePlayerContext.instance.BattleModule.GetBattleType();
            Int32 battleId = BattleShortCut.sampleBattle.battleInfo.id;
            BattleConfigData battleConfig = ConfigDataManager.instance.GetBattle(battleId);
            if (battleConfig == null)
            {
                BattleLaunchUtility.ExitBattle(m_battleType);
                End();
                return;
            }
            BattleUIStateHandler.ActiveState(BattleUIState.BattleFinish);

            if (IsNeedShowBattleResult())
            {
                BattleResultOpenUIContext uiContext = new BattleResultOpenUIContext();
                uiContext.resultData = CollectBattleResultData(battleConfig);
                uiContext.resultData.onResultUICloseCallBack = OnBattleResultUIClose;
                UIManager.instance.Open(uiContext, true);
            }
            else
            {
                BattleLaunchUtility.ExitBattle(m_battleType);
            }
            SaveBattleCompletedData();
            GamePlayerContext.instance.BattleModule.ClearBattleParam();
            End();
        }

        private Boolean IsNeedShowBattleResult()
        {
            BattleType battleType = GamePlayerContext.instance.BattleModule.GetBattleType();
            Boolean result = true;
            if (battleType == BattleType.WorldMonster)
            {
                result = false;
            }
            else if (battleType == BattleType.OpenSceneBattle)
            {
                result = false;
            }
            return result;
        }

        private BattleResultData CollectBattleResultData(BattleConfigData battleInfo)
        {
            BattleResultData battleResultData = new BattleResultData();
            battleResultData.isWin = isWin;
            BattleType battleType = GamePlayerContext.instance.BattleModule.GetBattleType();
            if (battleType == BattleType.QuestBattle || battleType == BattleType.WorldMonster)
            {
                Int32 worldBattleId = GamePlayerContext.instance.ModuleProvider.BattleModule.GetBattleParam(2);
                if (GamePlayerContext.instance.ModuleProvider.WorldModule.IsWorldBattleCompleted(worldBattleId) == false)
                {
                    WorldBattleConfigData worldBattleConfig = ConfigDataManager.instance.GetWorldBattle(worldBattleId);
                    if (worldBattleConfig != null)
                    {
                        battleResultData.isShowReward = true;
                        battleResultData.normalRewardDropId = worldBattleConfig.RewardDropId;
                    }
                }
            }

            Boolean[] starStamps = BattleShortCut.logicBattle.GetStarStampArray();
            bool isShowStar = starStamps != null && starStamps.Length > 0;
            battleResultData.isShowAchievement = isShowStar;
            if (isShowStar)
            {
                battleResultData.isShowAchievement = true;
                battleResultData.starStamps.AddRange(starStamps);
            }


            List<BattleAchievement> battleAchievements = BattleShortCut.logicBattle.GetAchievementList();
            if (battleAchievements != null && battleAchievements.Count > 0)
            {
                battleResultData.isShowAchievement = true;
                foreach (BattleAchievement achievement in battleAchievements)
                {
                    if (achievement == null)
                    {
                        continue;
                    }
                    Boolean alreadyCompleted = GamePlayerContext.instance.BattleModule.IsBattleAchievementCompleted(achievement.id);
                    if (alreadyCompleted)
                    {
                        battleResultData.completedBattleAchievementInfos.Add(achievement.achievementInfo);
                    }
                    else if (achievement.isCompleted)
                    {
                        battleResultData.isShowReward = true;
                        battleResultData.newCompletedBattleAchievementInfos.Add(achievement.achievementInfo);
                    }
                    battleResultData.achievementInfos.Add(achievement.achievementInfo);
                }
            }

            return battleResultData;
        }

        private void OnBattleResultUIClose()
        {
            BattleLaunchUtility.ExitBattle(m_battleType);
            //End();
        }


        private void SaveBattleCompletedData()
        {
            Int32 battleId = BattleShortCut.sampleBattle.battleInfo.id;
            if (isWin)
            {
                GamePlayerContext.instance.BattleModule.SetBattleWin(battleId);

                if (m_battleType == BattleType.WorldMonster)
                {
                    Int32 worldBattleId = GamePlayerContext.instance.ModuleProvider.BattleModule.GetBattleParam(2);
                    if (GamePlayerContext.instance.ModuleProvider.WorldModule.IsWorldBattleCompleted(worldBattleId) == false)
                    {
                        WorldBattleConfigData worldBattleConfig = ConfigDataManager.instance.GetWorldBattle(worldBattleId);
                        if (worldBattleConfig != null)
                        {
                            EnterWorldPostActionShowReward postAction = new EnterWorldPostActionShowReward();
                            postAction.m_showRewardPattern = 0;
                            postAction.m_dropId = worldBattleConfig.RewardDropId;
                            GamePlayerContext.instance.ModuleProvider.WorldModule.AddEnterWorldPostAction(postAction);
                        }
                    }
                }

                List<BattleAchievement> battleAchievements = BattleShortCut.logicBattle.GetAchievementList();
                if (battleAchievements != null && battleAchievements.Count > 0)
                {
                    foreach (BattleAchievement achievement in battleAchievements)
                    {
                        if (achievement != null && achievement.isCompleted)
                        {
                            GamePlayerContext.instance.ModuleProvider.BattleModule.SetCompletedBattleAchievement(achievement.achievementInfo);
                        }
                    }
                }
            }
        }
    }
}
