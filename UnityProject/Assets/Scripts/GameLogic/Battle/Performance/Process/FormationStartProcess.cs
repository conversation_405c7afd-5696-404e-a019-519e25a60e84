using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
	public class FormationStartProcess : ProcessBase
    {
        protected override void OnStart()
        {
            base.OnStart();
            BattleShortCut.sampleBattle.SetStageState_Sync(BattleStageStateId.PrepareStart);
            BattleShortCut.ChangeBattleState(BattleStateId.Prepare);
            BattleOpModeManager.instance.ChangeMode(BattleOpModeId.PrepareOpMode);
            EventManager.instance.Broadcast(EventID.BattlePrepare_UpdateFormationEntityList);
            End();
        }
    }
}
