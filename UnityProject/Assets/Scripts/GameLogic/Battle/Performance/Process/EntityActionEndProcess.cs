using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityActionEndProcess : ProcessBase
    {
        public EntityActionEndResult result;

        public override void OnRelease()
        {
            result = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EntityView entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
            if (entityView != null)
            {
                HandleBuffChangeResultList(entityView);
                HandleSkillCoolTimeUpdateResultList(entityView);
                HandleBuffCoolTimeUpdateResultList();
                var actor = entityView.owner as Actor;
                if (actor != null && actor.transformActor != null)
                {
                    actor.transformLifetime = result.transformLifeTime;
                    if (actor.transformLifetime <= 0)
                    {
                        var transformActor = actor.transformActor;
                        actor.transformActor = null;
                        actor.SetLocation(transformActor.GetLocation());
                        actor.SetDir(transformActor.GetDir());
                        actor.transformActor.Release();
                        EntityViewManager.instance.DestroyEntityView(actor.uid);
                        EntityViewManager.instance.CreateAndAddEntityView(actor);
                        entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
                        EventManager.instance.Broadcast(EventID.Entity_BlockHitPoint_Changed);
                    }
                }
                if (result.needChangeTeam)
                {
                    var preTeam = entityView.GetTeam();
                    var curTeam = BattleShortCut.sampleBattle.GetTeamByUid(result.changeTeamUid);
                    curTeam.AddEntityFrom(entityView, preTeam, false);
                    EventManager.instance.Broadcast(EventID.Entity_Team_Changed, result.entityUid, curTeam.uid);
                }
                entityView.SetActionChance(result.hasActionChance);
                if (entityView.IsActionEnd())
                {
                    entityView.SetMaterial(EEntityMaterialId.ActionOver);
                    EventManager.instance.Broadcast(EventID.Entity_ActionChanceUpdate, result.entityUid, true);
                }
                BattleShortCut.battleSceneGridManager.RemoveGridEffect(entityView.GetLocatedPosition(), GridEffectPattern.Active);
            }
            End();
            //DelayEnd(0.5f);
        }

        protected override void OnEnd()
        {
            EventManager.instance.Broadcast(EventID.Entity_Action_End, result.entityUid);
            base.OnEnd();
        }

        private void HandleBuffChangeResultList(EntityView entityView)
        {
            if (entityView.buffComponent == null)
            {
                return;
            }
            foreach(var buffChangeResult in result.buffChangeResultList)
            {
                BuffPerformanceUtility.PerformanceBuffChange(buffChangeResult);
            }
        }

        private void HandleSkillCoolTimeUpdateResultList(EntityView entityView)
        {
            if (entityView.skillComponent == null)
            {
                return;
            }
            foreach (var skillCoolTimeUpdateResult in result.skillCoolTimeUpdateResultList)
            {
                var skill = entityView.GetSkillBySkillUid(skillCoolTimeUpdateResult.skillUid);
                if(skill != null)
                {
                    skill.coolTimeLeft = skillCoolTimeUpdateResult.cooltime;
                }
            }
        }

        private void HandleBuffCoolTimeUpdateResultList()
        {
            foreach (var buffCoolTimeUpdateResult in result.buffCoolTimeUpdateResultList)
            {
                var entity = BattleShortCut.sampleBattle.GetEntityByUid(buffCoolTimeUpdateResult.entityUid);
                if (entity == null)
                {
                    continue;
                }
                var buff = entity.GetBuffByUid(buffCoolTimeUpdateResult.buffUid);
                if (buff == null)
                {
                    continue;
                }
                var effect = buff.GetBuffEffect(buffCoolTimeUpdateResult.effectIndex) as BuffEffectOfSkillTrigger;
                if (effect == null)
                {
                    continue;
                }
                effect.SetCoolTime(buffCoolTimeUpdateResult.coolTime);
            }
        }
    }
}
