using System;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class EntityHudBloodProcess : ProcessBase
    {
        public Boolean isShow;

        public override void OnRelease()
        {
            isShow = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            BattleHelper.UpdateOtherEntityBloodMask(!isShow, BloodMask.StoryPerformance);
            End();
        }
    }
}
