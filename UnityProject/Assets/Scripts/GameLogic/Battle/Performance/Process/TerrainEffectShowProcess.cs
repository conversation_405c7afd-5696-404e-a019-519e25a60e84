using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TerrainEffectShowProcess : ProcessBase
    {
        public int rid;
        public int uid;
        public GridPosition pos;
        public bool isLoop;

        public override void OnRelease()
        {
            rid = default;
            uid = default;
            pos = default;
            isLoop = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            if (isLoop)
            {
                BattleShortCut.sampleBattle.AddTerrainEffect(uid, rid, pos);
            }
            BattleShortCut.battleScene.AddTerrainEfx(uid, rid, pos, isLoop);
            End();
        }
    }
}
