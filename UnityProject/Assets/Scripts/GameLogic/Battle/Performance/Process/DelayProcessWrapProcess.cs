using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using System.Diagnostics;
using UnityEditor;

namespace Phoenix.GameLogic.Battle
{
    public class DelayProcessWrapProcess : DelayActionProcess
    {
        private float m_delayTime;
        private ProcessBase m_process;
        private bool m_needWaitProcessEnd;

        protected override float delayTime
        {
            get { return m_delayTime; }
        }

        public override bool canStart
        {
            get { return base.canStart || m_process != null; }
        }

        public override void OnFetch()
        {
            base.OnFetch();
            m_needWaitProcessEnd = true;
        }

        public override void OnRelease()
        {
            m_delayTime = default;
            m_process.Release();
            m_process = null;
            m_needWaitProcessEnd = default;
            base.OnRelease();
        }

        public void SetProcess(float delayTime, ProcessBase process)
        {
            m_delayTime = delayTime;
            m_process = process;
        }

        protected override bool CheckEnd()
        {
            return base.CheckEnd() && (!m_needWaitProcessEnd || m_process == null || m_process.state == ProcessStateId.Ended);
        }

        protected override void OnMainStart()
        {
            base.OnMainStart();
            m_needWaitProcessEnd = false;
            if (m_process != null)
            {
                if (m_process.InitAndStartAndCheckRunning())
                {
                    m_process.actionOnEnd = OnMainProcessEnd;
                    m_needWaitProcessEnd = true;
                }
            }
            if (!m_needWaitProcessEnd)
            {
                TryEnd();
            }
        }

        protected override void OnMainTick(TimeSlice timeSlice)
        {
            m_process.Tick(timeSlice);
            base.OnMainTick(timeSlice);
        }

        private void OnMainProcessEnd(ProcessBase process)
        {
            TryEnd();
        }
    }
}
