using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class SummonEntityProcess : ProcessPlayerProcess
    {
        public int entityUid;
        public string dramaName;

        public override void OnRelease()
        {
            entityUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            DramaContext dramaContext = new DramaContext();
            var entityView = EntityViewManager.instance.GetEntityView(entityUid);
            dramaContext.dramaPath = entityView.GetDramaTimelinePath(dramaName);
            dramaContext.originEntityUid = entityUid;
            dramaContext.simplePerformance = true;
            dramaContext.scenePerformance = true;
            var dramaProcess = DramaTimelineProcess.Create(dramaContext);
            sequenceProcess.AddProcess(dramaProcess);
        }
    }
}
