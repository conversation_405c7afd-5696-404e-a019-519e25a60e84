using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.ConfigData;
using System.Linq;

namespace Phoenix.GameLogic.Battle
{
    public class StageActionGroupProcess : ProcessPlayerProcess
    {
        public List<BattleStageActionInfo> actionInfoList;
        public List<BattleActionResult> resultList;

        private int m_resultIndex;

        public override void OnRelease()
        {
            m_resultIndex = 0;
            actionInfoList = default;
            resultList = default;
            base.OnRelease();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            sequenceProcess.AddProcess(ClassPoolManager.instance.Fetch<StageActionGroupBeginProcess>());
            for (int i = 0; i < actionInfoList.Count; ++i)
            {
                AddProcssByActionInfoList(sequenceProcess, actionInfoList[i]);
            }
            sequenceProcess.AddProcess(ClassPoolManager.instance.Fetch<StageActionGroupEndProcess>());
        }

        private void AddProcssByActionInfoList(CollectionProcess<ProcessBase> collection, BattleStageActionInfo info)
        {
            if (info == null)
            {
                return;
            }
            switch (info.actionType)
            {
                case BattleStageActionType.Parallel:
                    collection.AddProcess(GetProcssByParallel(info as BattleStageActionInfo_Parallel));
                    break;
                case BattleStageActionType.Sequence:
                    collection.AddProcess(GetProcssBySequence(info as BattleStageActionInfo_Sequence));
                    break;
                case BattleStageActionType.Dialog:
                    {
                        var process = ClassPoolManager.instance.Fetch<DialogProcess>();
                        process.dialogInfo = info as BattleStageActionInfo_Dialog;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.DialogSelection:
                    {
                        var process = ClassPoolManager.instance.Fetch<DialogSelectionProcess>();
                        process.info = info as BattleStageActionInfo_DialogSelection;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.CameraShake:
                    {
                        var process = ClassPoolManager.instance.Fetch<CameraShakeProcess>();
                        //process.info = info as BattleStageActionInfo_CameraShake;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.PlayAnim_Actor_Uid:
                    {
                        var process = ClassPoolManager.instance.Fetch<EntityAnimationProcess>();
                        process.animationInfo = info as BattleStageActionInfo_PlayAnim_Actor_Uid;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.Move_Actor_Uid:
                    {
                        var result = PopResult<EntityMoveResult>();
                        var process = ClassPoolManager.instance.Fetch<EntityMoveProcess>();
                        process.entityUid = result.entityUid;
                        process.gridPos = result.movePos;
                        process.isMoveBack = (info as BattleStageActionInfo_Move_Actor_Uid).moveBack;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.AttachBuff_Actor_Uid:
                case BattleStageActionType.AttachBuff_Actor_TeamUid:
                case BattleStageActionType.AttachBuff_Actor_CampUid:
                    {
                        var result = PopResult<BuffAttachGroupResult>();
                        var process = ClassPoolManager.instance.Fetch<BuffAttachGroupProcess>();
                        process.result = result;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.Summon_Actor:
                    {
                        var result = PopResult<BattleStageActionEntitySummonResult>();
                        var process = ClassPoolManager.instance.Fetch<BattleStageActionEntitySummonProcess>();
                        process.result = result;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.Delay:
                    {
                        var process = ClassPoolManager.instance.Fetch<DelayEndProcess>();
                        process.delayTime = (info as BattleStageActionInfo_Delay).delayTime;
                        collection.AddProcess(process);
                    }

                    break;
                case BattleStageActionType.ChangeBehavior_Actor_Uid:
                    {
                        var process = ClassPoolManager.instance.Fetch<EntityBehaviorChangeProcess>();
                        process.entityUidList.AddRange((info as BattleStageActionInfo_ChangeBehavior_Actor_Uid).actorUidList);
                        process.aiType = (info as BattleStageActionInfo_ChangeBehavior_Actor_Uid).aiType;
                        collection.AddProcess(process);
                    }

                    break;
                case BattleStageActionType.Retreat_Actor_Uid:
                    {
                        var result = PopResult<EntityRetreatResult>();
                        EntityRetreatProcess process = ClassPoolManager.instance.Fetch<EntityRetreatProcess>();
                        process.entityUidList.AddRange(result.entityUidList);
                        collection.AddProcess(process);
                    }

                    break;
                case BattleStageActionType.ToggleHudVisibility:
                    {
                        var process = ClassPoolManager.instance.Fetch<EntityHudBloodProcess>();
                        process.isShow = (info as BattleStageActionInfo_ToggleHudVisibility).isShow;
                        collection.AddProcess(process);
                    }

                    break;
                case BattleStageActionType.ChangeTeam_Actor_Uid:
                    {
                        var result = PopResult<EntityTeamChangeResult>();
                        var process = ClassPoolManager.instance.Fetch<EntityTeamChangeProcess>();
                        process.entityUidList.AddRange(result.entityUidList);
                        process.teamUid = result.teamUid;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.SpawnTerrainEffect:
                    {
                        var process = ClassPoolManager.instance.Fetch<TerrainEffectShowProcess>();
                        process.uid = (info as BattleStageActionInfo_SpawnTerrainEffect).uid;
                        process.rid = (info as BattleStageActionInfo_SpawnTerrainEffect).rid;
                        process.pos = (info as BattleStageActionInfo_SpawnTerrainEffect).pos;
                        process.isLoop = (info as BattleStageActionInfo_SpawnTerrainEffect).isLoop;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.DespawnTerrainEffect:
                    {
                        var process = ClassPoolManager.instance.Fetch<TerrainEffectHideProcess>();
                        process.uid = (info as BattleStageActionInfo_DespawnTerrainEffect).uid;
                        collection.AddProcess(process);
                    }

                    break;
                case BattleStageActionType.CameraFocusPosition:
                    {
                        var process = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
                        process.m_gridPosition = (info as BattleStageActionInfo_CameraFocusPosition).pos;
                        process.m_immediately = (info as BattleStageActionInfo_CameraFocusPosition).immediately;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.CameraFocusEntity:
                    {
                        var process = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
                        process.entityUid = (info as BattleStageActionInfo_CameraFocusEntity).actorUid;
                        process.m_immediately = (info as BattleStageActionInfo_CameraFocusEntity).immediately;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.PlayTimeline:
                    {
                        var process = ClassPoolManager.instance.Fetch<PlayUnityTimelineProcess>();
                        process.info = info as BattleStageActionInfo_PlayTimeline;
                        collection.AddProcess(process);
                    }
                    break;
                case BattleStageActionType.TurnDir_Actor_Uid:
                    {
                        //var process = ClassPoolManager.instance.Fetch<EntityTurnDirProcess>();
                        //var actionInfo = info as BattleStageActionInfo_TurnDir_Actor_Uid;
                        //process.entityUidList = actionInfo.actorUidList;
                        //process.targetPos = actionInfo.dir;
                        //process.time = actionInfo.time;
                        //process.waitEnd = actionInfo.waitEnd;
                        //collection.AddProcess(process);
                    }
                    break;
                //case BattleStageActionType.TeamDecisionMark:
                //    {
                //    }
                //    break;
                //case BattleStageActionType.EntityDamaged:
                //case BattleStageActionType.EntityHeal:
                //    {
                //        var process = ClassPoolManager.instance.Fetch<BattleActionProcedurePerformanceProcess>();
                //        var result = PopResult<StageActionGroupInnerProcedureResult>();
                //        process.Init(result, false);
                //        collection.AddProcess(process);
                //    }
                //    break;
                //case BattleStageActionType.Music:
                //    {
                //        collection.AddProcess(CallbackProcess.Create(() =>
                //        {
                //            BattleShortCut.sampleBattle.stageManageComponent.bgmPath = (info as BattleStageActionMusicInfo).path;
                //            BattlePerformanceUtility.RefreshBgm();
                //        }));
                //    }
                //    break;
                //case BattleStageActionType.DialogBubble:
                //    {
                //        int bubbleId = (info as BattleStageActionDialogBubbleInfo).bubbleId;
                //        int entityUid = (info as BattleStageActionDialogBubbleInfo).entityUid;
                //        collection.AddProcess(CallbackProcess.Create(() =>
                //        {
                //            EntityPerformanceUtility.PerformanceDialogBubble(bubbleId, entityUid, null);
                //        }));
                //    }
                //    break;
                //case BattleStageActionType.SelectEntity:
                //    {
                //        int actorId = (info as BattleStageActionSelectEntityInfo).entityId;
                //        collection.AddProcess(CallbackProcess.Create(() =>
                //        {
                //            Debug.Log("StageActionGroupProcess: SelectEntity " + actorId);
                //            BattlePerformanceUtility.forceNextActionActorId = actorId;
                //        }));
                //    }
                //    break;
                default:
                    throw new ToDoException("AddProcssByActionInfoList:" + info.actionType.ToString());
            }
        }

        private T PopResult<T>() where T : BattleActionResult
        {
            return resultList[m_resultIndex++] as T;
        }

        private ProcessBase GetProcssByParallel(BattleStageActionInfo_Parallel parallelInfo)
        {
            ParallelProcess process = ClassPoolManager.instance.Fetch<ParallelProcess>();
            for (int i = 0; i < parallelInfo.actionList.Count; ++i)
            {
                AddProcssByActionInfoList(process, parallelInfo.actionList[i]);
            }
            return process;
        }

        private ProcessBase GetProcssBySequence(BattleStageActionInfo_Sequence sequenceInfo)
        {
            ParallelProcess process = ClassPoolManager.instance.Fetch<ParallelProcess>();
            for (int i = 0; i < sequenceInfo.actionList.Count; ++i)
            {
                AddProcssByActionInfoList(process, sequenceInfo.actionList[i]);
            }
            return process;
        }
    }
}
