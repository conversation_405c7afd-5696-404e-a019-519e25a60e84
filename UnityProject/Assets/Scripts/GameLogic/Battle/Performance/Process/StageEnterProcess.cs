
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
	public class StageEnterProcess : ProcessBase
    {
        public int stageIndex;
        private TimelinePlayer m_timelinePlayer;

        public override void OnRelease()
        {
            stageIndex = default;
            base.OnRelease();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            if (m_timelinePlayer != null)
            {
                m_timelinePlayer.Tick(timeSlice);
            }
        }

        protected override void OnStart()
        {
            BattleShortCut.sampleBattle.SetStageState_Sync(BattleStageStateId.StageEnter);
            BattleOpModeManager.instance.ChangeMode(BattleOpModeId.Default);
            Loading.Open(LoadingFlagId.BattleStageInitializer, LoadingViewType.FadeBlack, OnLoadingUIOpen);
        }

        private void OnLoadingUIOpen()
        {
            BattleShortCut.sampleBattle.EnterStage_Sync(stageIndex);
            BattlePerformanceUtility.RefreshBgm();
            BattleInfo battleInfo = BattleShortCut.sampleBattle.battleInfo;
            BattleStageInfo stageInfo = BattleShortCut.sampleBattle.GetCurStageInfo();

            if (stageInfo.stageType == BattleStageType.Scinerio
                || stageInfo.stageType == BattleStageType.ScinerioToWin
                || stageInfo.stageType == BattleStageType.ScinerioTimeline)
            {
                BattleHelper.UpdateOtherEntityBloodMask(true, BloodMask.StoryPerformance);
            }


            if (stageInfo.stageType == BattleStageType.ScinerioTimeline)
            {
                BattleUIStateHandler.ActiveState(BattleUIState.BattleStoryPerform);
                BattleScinerioTimelineStageInitializer scinerioTimelineInitializer = new BattleScinerioTimelineStageInitializer();
                scinerioTimelineInitializer.Init(battleInfo, stageIndex);
                scinerioTimelineInitializer.StartAsync(OnBattleScinerioTimelineStageInitEnd);
            }
            else
            {
                BattleStageInitializer battleStageInitializer = new BattleStageInitializer();
                battleStageInitializer.Init(battleInfo, stageIndex);
                battleStageInitializer.StartAsync(OnBattleStageInitEnd);
            }
        }

        private void OnBattleStageInitEnd(Initializer initializer)
        {
            Loading.Close(LoadingFlagId.BattleStageInitializer, End);
        }

        private void OnBattleScinerioTimelineStageInitEnd(Initializer initializer)
        {
            Loading.Close(LoadingFlagId.BattleStageInitializer, PlayScinerioTimeline);
        }

        private void PlayScinerioTimeline()
        {
            GameObject go = BattleShortCut.battleScene.GetSceneTimelinePlayableDirectorGameObject(SceneType.Main);
            if (go != null)
            {
                m_timelinePlayer = new TimelinePlayer();
                m_timelinePlayer.Init(null);
                m_timelinePlayer.PlayTimeline(go, OnScinerioTimelinePlayEnd);
            }
            else
            {
                OnScinerioTimelinePlayEnd();
            }
        }

        private void OnScinerioTimelinePlayEnd()
        {
            if (m_timelinePlayer != null)
            {
                m_timelinePlayer.UnInit();
                m_timelinePlayer = null;
            }
            End();
        }
    }
}
