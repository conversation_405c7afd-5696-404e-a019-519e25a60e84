using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityResetActionChanceAllProcess : ProcessBase
    {
        protected override void OnStart()
        {
            base.OnStart();
            foreach(var kv in EntityViewManager.instance.GetEntityViewMap())
            {
                EventManager.instance.Broadcast(EventID.Entity_ActionChanceUpdate, kv.Key, false);
                kv.Value.SetMaterial(EEntityMaterialId.Normal);
            }
            End();
        }
    }
}
