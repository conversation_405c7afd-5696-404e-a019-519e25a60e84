using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CameraFocusProcess : ProcessBase
    {
        public Int32 entityUid;
        public GridPosition m_gridPosition = GridPosition.invalid;
        public Boolean m_immediately;

        public override void OnRelease()
        {
            entityUid = default;
            m_gridPosition = GridPosition.invalid;
            m_immediately = false;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if (m_gridPosition == GridPosition.invalid)
            {
                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                //if (entityView == null)
                //{
                //    entityView = EntityViewManager.instance.GetEntityView(BattleShortCut.sampleBattle.GetCurEntityUid());
                //    if (entityView == null)
                //    {
                //        BattleTeam team = BattleShortCut.sampleBattle.GetTeamByIndex(BattleShortCut.sampleBattle.GetCurTeamIndex());
                //        if (team.controlledPlayer != null && team.controlledPlayer.playerId == BattleShortCut.hostPlayerId)
                //        {
                //            //todo: ������
                //            entityView = EntityViewManager.instance.GetEntityView(team.entityUidList.Find(e => BattleShortCut.sampleBattle.GetEntityByUid(e).HasActionChance()));
                //        }
                //    }
                //}
                if (entityView != null)
                {
                    m_gridPosition = entityView.GetLocatedPosition();
                }
            }
            if (m_gridPosition.isValid)
            {
                BattleShortCut.battleScene.sceneCameraHandler.SetCameraGridPositionWithSpeed(m_gridPosition, 50, m_immediately, End);
            }
            else
            {
                End();
            }
        }
    }
}
