using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CallbackProcess : ProcessBase
    {
        public Action action;

        public static CallbackProcess Create(Action action)
        {
            CallbackProcess process = ClassPoolManager.instance.Fetch<CallbackProcess>();
            process.action = action;
            return process;
        }

        public override void OnRelease()
        {
            action = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            if (action != null)
            {
                action();
            }
            End();
        }
    }
}
