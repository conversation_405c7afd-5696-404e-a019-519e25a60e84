using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Playables;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class DramaTimelineProcess : ProcessPlayerProcess
    {
        private DramaContext m_context;
        private DramaTimeline m_dramaTimeline;

        private float m_attackAnimMoveLength;

        public override void OnRelease()
        {
            base.OnRelease();
            m_attackAnimMoveLength = default;
            m_context = default;
            m_dramaTimeline = default;
        }

        public static DramaTimelineProcess Create(DramaContext context)
        {
            DramaTimelineProcess process = ClassPoolManager.instance.Fetch<DramaTimelineProcess>();
            process.m_context = context;
            process.InitDramaTimeline();
            return process;
        }

        private void InitDramaTimeline()
        {
            if (m_context.skipPerformance)
            {
                m_dramaTimeline = ClassPoolManager.instance.Fetch<DramaTimeline>();
            }
            else
            {
                m_dramaTimeline = DramaTimelineManager.instance.CreateDrama(m_context.dramaPath);
            }
            if(m_dramaTimeline == null)
            {
                m_dramaTimeline = ClassPoolManager.instance.Fetch<DramaTimeline>();
            }
            m_dramaTimeline.InitContext(m_context);
            List<DramaPlayAnimation> playAnimDramaList = m_dramaTimeline.FindDramaList<DramaPlayAnimation>();
            foreach (var drama in playAnimDramaList)
            {
                var originEntityView = EntityViewManager.instance.GetEntityView(m_context.originEntityUid);
                if(originEntityView != null)
                {
                     var animBehaviour = originEntityView.GetBehaviour<EntityAnimatorBehaviour>();
                    if(animBehaviour != null)
                    {
                        m_attackAnimMoveLength += animBehaviour.GetAnimationFinalRootOffset((drama.data as DramaPlayAnimationData).animationName).z;
                    }
                }
                var animData = drama.data as DramaPlayAnimationData;
                if (animData.skillEffectId < 0)
                {
                    continue;
                }
                var effectResult = m_context.GetEffectResult(animData.skillEffectId);
                drama.isMute = effectResult == null || !effectResult.success;
            }

            List<DramaPlayEffect> playEffectDramaList = m_dramaTimeline.FindDramaList<DramaPlayEffect>();
            foreach (var drama in playEffectDramaList)
            {
                var efxData = drama.data as DramaPlayEffectData;
                if (efxData.skillEffectId < 0)
                {
                    continue;
                }
                var effectResult = m_context.GetEffectResult(efxData.skillEffectId);
                drama.isMute = effectResult == null || !effectResult.success;
            }

            List<DramaPlaySound> playSoundDramaList = m_dramaTimeline.FindDramaList<DramaPlaySound>();
            foreach (var drama in playSoundDramaList)
            {
                var data = drama.data as DramaPlaySoundData;
                if (data.skillEffectId < 0)
                {
                    continue;
                }
                var effectResult = m_context.GetEffectResult(data.skillEffectId);
                drama.isMute = effectResult == null || !effectResult.success;
            }

            float lastOnHitFrame = 0f;
            List<DramaOnHit> onHitDramaList = m_dramaTimeline.FindDramaList<DramaOnHit>();
            List<HitInfo> hitInfoList = new List<HitInfo>();
            for (int i = 0; i < onHitDramaList.Count; ++i)
            {
                DramaOnHit onHitDrama = onHitDramaList[i];
                lastOnHitFrame = Math.Max(lastOnHitFrame, onHitDrama.startFrame);
                HitInfo hitInfo = hitInfoList.Find(info => info.effectId == onHitDrama.baseData.skillEffectId);
                if (hitInfo == null)
                {
                    hitInfo = new HitInfo();
                    hitInfo.effectId = onHitDrama.baseData.skillEffectId;
                    hitInfoList.Add(hitInfo);
                }
                hitInfo.dramaOnHitList.Add(onHitDrama);
            }
            
            foreach (var hitInfo in hitInfoList)
            {
                hitInfo.dramaOnHitList.Sort((x, y) => x.startFrame.CompareTo(y.startFrame));
            }
            if (m_context.effectResultList != null)
            {
                foreach (var effectResult in m_context.effectResultList)
                {
                    HitInfo hitInfo = hitInfoList.Find(info => info.effectId == effectResult.id);
                    if (hitInfo == null)
                    {
                        hitInfo = new HitInfo();
                        hitInfo.effectId = effectResult.id;
                        hitInfoList.Add(hitInfo);
                    }
                    if (hitInfo.dramaOnHitList.Count == 0)
                    {
                        DramaOnHit drama = DramaTimelineUtility.CreateDrama(DramaType.OnHit) as DramaOnHit;
                        DramaOnHitData dramaData = DramaTimelineUtility.CreateDramaData(DramaType.OnHit) as DramaOnHitData;
                        dramaData.startFrame = lastOnHitFrame;
                        drama.Init(dramaData);
                        hitInfo.dramaOnHitList.Add(drama);
                        m_dramaTimeline.AddDrama(drama);
                    }
                    foreach (var resultClip in effectResult.clipList)
                    {
                        HandleBattleActionEffect(hitInfo, resultClip);
                    }
                    var lastDramaOnHit = hitInfo.dramaOnHitList.GetLastValueSafely();
                    lastDramaOnHit.blockHitPointChangeResultList = effectResult.blockHitPointChangeResultList;
                    lastDramaOnHit.buffChangeActiveResultList = effectResult.buffChangeActiveResultList;
                }
            }
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            PlayDramaTimelineProcess playDramaTimelineProcess = ClassPoolManager.instance.Fetch<PlayDramaTimelineProcess>();
            playDramaTimelineProcess.dramaTimeline = m_dramaTimeline;
            sequenceProcess.AddProcess(playDramaTimelineProcess);
        }

        private void HandleBattleActionEffect(HitInfo hitInfo, BattleActionEffectResultClip resultClip)
        {
            if (resultClip.effectType == BattleActionEffectType.DamageEntity)
            {
                var damageResultClip = resultClip as BattleActionEffectDamageEntityResultClip;
                FixedValue totalDamage = damageResultClip.expectDamage;
                FixedValue totalDamageReflect = damageResultClip.expectDamageReflect;
                FixedValue totalHealthSteal = damageResultClip.expectHealthSteal;
                if (hitInfo == null)
                {
                    throw new Exception();
                }
                float damagePercentSum = 0;
                foreach (var drama in hitInfo.dramaOnHitList)
                {
                    damagePercentSum += drama.baseData.damageEntityData.damagePercent;
                }
                int cumulativeDamage = 0;
                int cumulativeDamageReflect = 0;
                int cumulativeHealthSteal = 0;
                for (int i = 0; i < hitInfo.dramaOnHitList.Count; ++i)
                {
                    DramaOnHit dramaOnHit = hitInfo.dramaOnHitList[i];
                    bool isLast = i == hitInfo.dramaOnHitList.Count - 1;
                    int damage = 0;
                    int damageReflect = 0;
                    int healthSteal = 0;
                    FixedValue curHp;
                    FixedValue curHpAttacker;
                    bool needDamageFloat = true;
                    float moveLength;
                    if (!isLast)
                    {
                        float percentage = dramaOnHit.baseData.damageEntityData.damagePercent;
                        moveLength = dramaOnHit.baseData.damageEntityData.moveLength;
                        m_attackAnimMoveLength -= moveLength;
                        if (percentage < 1e-4)
                        {
                            needDamageFloat = false;
                        }
                        if (damagePercentSum > 0)
                        {
                            damage = (int)((int)totalDamage * percentage) / (int)damagePercentSum;
                            damageReflect = (int)((int)totalDamageReflect * percentage) / (int)damagePercentSum;
                            healthSteal = (int)((int)totalHealthSteal * percentage) / (int)damagePercentSum;
                        }
                        cumulativeDamage += damage;
                        cumulativeDamageReflect += damageReflect;
                        cumulativeHealthSteal += healthSteal;
                        curHp = FixedValueMath.Max(damageResultClip.preHp - cumulativeDamage, 0);
                        curHpAttacker = FixedValueMath.Clamp(damageResultClip.preHpAttacker - cumulativeDamageReflect + cumulativeHealthSteal, 0, damageResultClip.maxHpAttacker);
                    }
                    else
                    {
                        moveLength = m_attackAnimMoveLength + m_context.deltaAdjustDistance;
                        if (moveLength < 0f)
                        {
                            moveLength = 0f;
                        }
                        damage = (int)totalDamage - cumulativeDamage;
                        damageReflect = (int)totalDamageReflect - cumulativeDamageReflect;
                        healthSteal = (int)totalHealthSteal - cumulativeHealthSteal;
                        curHp = damageResultClip.curHp;
                        curHpAttacker = damageResultClip.curHpAttacker;
                    }
                    OnHitDamageEntityHandler handler = DramaTimelineUtility.CreateOnHitHandler(resultClip.effectType) as OnHitDamageEntityHandler;
                    handler.Init(resultClip, dramaOnHit.baseData, m_context);
                    handler.damage = damage;
                    handler.damageReflect = damageReflect;
                    handler.healthSteal = healthSteal;
                    handler.needDamageFloat = needDamageFloat;
                    handler.curHp = curHp;
                    handler.curHpAttacker = curHpAttacker;
                    handler.moveLength = moveLength;
                    handler.isFirst = i == 0;
                    handler.isLast = isLast;
                    dramaOnHit.AddHandler(handler);
                }
            }
            else
            {
                DramaOnHit dramaOnHit = null;
                for (int i = 0; i < hitInfo.dramaOnHitList.Count; ++i)
                {
                    if (i == 0)
                    {
                        dramaOnHit = hitInfo.dramaOnHitList[i];
                    }
                    else
                    {
                        hitInfo.dramaOnHitList[i].isMute = true;
                    }
                }
                OnHitHandler handler = DramaTimelineUtility.CreateOnHitHandler(resultClip.effectType);
                handler.Init(resultClip, dramaOnHit.baseData, m_context);
                dramaOnHit.AddHandler(handler);
            }
        }

        private class HitInfo
        {
            public int effectId;
            public List<DramaOnHit> dramaOnHitList = new List<DramaOnHit>();
        }
    }
}
