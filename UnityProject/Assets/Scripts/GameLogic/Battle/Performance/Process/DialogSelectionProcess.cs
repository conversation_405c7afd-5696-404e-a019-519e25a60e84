using System;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class DialogSelectionProcess : ProcessBase
    {
        public BattleStageActionInfo_DialogSelection info;

        public override void OnRelease()
        {
            info = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if (info != null)
            {
                DialogueUI.ShowDialogue(info.dialogId, info.selectionGroupId, OnSelectionSelectCallBack);
            }
        }

        private void OnSelectionSelectCallBack(Int32 selectionId)
        {
            FrameCommandSendUtility.SendDialogSelect(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, selectionId);
            End();
        }
    }
}
