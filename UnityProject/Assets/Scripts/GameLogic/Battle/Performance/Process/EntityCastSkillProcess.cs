using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityCastSkillProcess : ProcessPlayerProcess
    {
        public int entityUid;
        public EntityCastSkillProcedureResult procedureResult;

        public override void OnRelease()
        {
            entityUid = default;
            procedureResult = default;
            base.OnRelease();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {

                CameraFocusProProcess process = ClassPoolManager.instance.Fetch<CameraFocusProProcess>();
                process.InitDatas(false, procedureResult.entityUid, procedureResult.targetPos);
                sequenceProcess.AddProcess(process);

                EntityCastSkillBeginProcess beginProcess = ClassPoolManager.instance.Fetch<EntityCastSkillBeginProcess>();
                beginProcess.procedureResult = procedureResult;
                sequenceProcess.AddProcess(beginProcess);

                SkillProcedurePerformanceProcess performanceProcess = ClassPoolManager.instance.Fetch<SkillProcedurePerformanceProcess>();
                performanceProcess.Init(procedureResult, BattleShortCut.ForceSimplePerformance);
                sequenceProcess.AddProcess(performanceProcess);

                EntityCastSkillEndProcess endProcess = ClassPoolManager.instance.Fetch<EntityCastSkillEndProcess>();
                endProcess.sourceEntityUid = procedureResult.entityUid;
                endProcess.skillUid = procedureResult.skillUid;
                endProcess.targetPosition = procedureResult.targetPos;
                endProcess.targetEntityUid = procedureResult.targetEntityUid;
                sequenceProcess.AddProcess(endProcess);

                sequenceProcess.AddProcess(DelayEndProcess.Create(0.5f));

                sequenceProcess.AddProcess(CallbackProcess.Create(() =>
                {
                    foreach (var kv in EntityViewManager.instance.GetEntityViewMap())
                    {
                        kv.Value.CheckSync();
                    }
                }));
            }
        }
    }
}
