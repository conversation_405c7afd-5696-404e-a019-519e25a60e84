
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TeamSharedEnergyUpdateProcess : ProcessBase
    {
        public int teamIndex;

        public override void OnRelease()
        {
            teamIndex = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            float time = BattleParamSetting.instance.m_battleUISetting.m_energyUpdateTime;
            float delayTime = BattleParamSetting.instance.m_battleUISetting.m_energyUpdateDelayShowTime;
            TimerManager.instance.Start(time, delegate
            {
                var team = BattleShortCut.sampleBattle.GetTeamByIndex(teamIndex);
                EventManager.instance.Broadcast(EventID.BattleTeam_SharedEnergy_Changed, team.uid, team.sharedEnergy);
            });
            DelayEnd(delayTime);
        }
    }
}
