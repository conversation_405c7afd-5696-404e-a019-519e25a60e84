using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class PlayDramaTimelineProcess : ProcessBase
    {
        public DramaTimeline dramaTimeline;

        public override void OnRelease()
        {
            dramaTimeline.Release();
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if(dramaTimeline != null)
            {
                dramaTimeline.Start(End);
            }
            else
            {
                End();
            }
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            if(dramaTimeline != null)
            {
                dramaTimeline.Tick(timeSlice);
            }
        }
    }
}
