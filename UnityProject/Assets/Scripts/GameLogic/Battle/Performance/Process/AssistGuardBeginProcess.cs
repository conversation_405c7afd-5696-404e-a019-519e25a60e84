using System;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class AssistGuardBeginProcess : ProcessBase
    {
        public int targetEntityUid;
        public int guarderEntityUid;
        public int srcEntityUid;

        private StaticLerpVec3 m_lerp = new StaticLerpVec3();

        public override void OnRelease()
        {
            targetEntityUid = default;
            guarderEntityUid = default;
            srcEntityUid = default;
            m_lerp.Reset();
            base.OnRelease();
        }

        protected override void OnStart()
        {
            EventManager.instance.Broadcast(EventID.BattlePerformance_AssistGuardBegin, targetEntityUid, guarderEntityUid);
            EntityView targetView = EntityViewManager.instance.GetEntityView(targetEntityUid);
            EntityView guarderView = EntityViewManager.instance.GetEntityView(guarderEntityUid);
            if (targetView == null || guarderView == null)
            {
                End();
                return;
            }
            guarderView.SetDirection(targetView.GetLocatedPosition(), 0);
            BattleHelper.UpdateEntityBloodMask(false, BloodMask.CastSkill, guarderEntityUid);
            targetView.SetActiveSafely(false);
            BattleHelper.UpdateEntityBloodMask(true, BloodMask.EntityHide, targetEntityUid);
            m_lerp.actionOnTick = (l) => guarderView.transform.position = l.curValue;
            m_lerp.actionOnFinish = OnLerpEnd;
            m_lerp.Start(guarderView.transform.position, targetView.transform.position, 0.2f, EBaseLerpFuncType.Linear);
        }

        private void OnLerpEnd(AbstractStaticLerp<Vector3> lerp)
        {
            EntityView guarderView = EntityViewManager.instance.GetEntityView(guarderEntityUid);
            EntityView targetView = EntityViewManager.instance.GetEntityView(targetEntityUid);
            EntityView srcView = EntityViewManager.instance.GetEntityView(srcEntityUid);
            bool waitEnd = false;
            if (guarderView != null && targetView != null)
            {
                guarderView.SetPosition(targetView.GetLocatedPosition());
                guarderView.SetDirection(srcView.GetLocatedPosition(), 0);
                var animBehaviour = guarderView.GetBehaviour<EntityAnimatorBehaviour>();
                var playIdleBehaviour = guarderView.GetBehaviour<EntityPlayIdleBehaviour>();
                if (playIdleBehaviour != null && animBehaviour != null)
                {
                    waitEnd = true;
                    animBehaviour.PlayAnimationOnce("Combat_Announce", 0f, interrupt =>
                    {
                        if (!interrupt)
                        {
                            playIdleBehaviour.PlayCombatIdle(true);
                        }
                        End();
                    });
                }
            }
            if (!waitEnd)
            {
                End();
            }
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            m_lerp.Tick(timeSlice.deltaTime);
        }
    }
}
