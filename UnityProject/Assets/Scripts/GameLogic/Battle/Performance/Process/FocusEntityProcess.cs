using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class FocusEntityProcess : ProcessBase
    {
        private EntityView m_entityView;

        public override void OnRelease()
        {
            m_entityView = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            m_entityView = BattlePerformanceUtility.GetFocusEntityView();
            if (m_entityView != null)
            {
                BattleShortCut.battleScene.sceneCameraHandler.SetCameraGridPositionWithSpeed(m_entityView.GetLocatedPosition(), 50, false, OnCameraFocusEnd);
            }
            else
            {
                End();
            }
        }

        private void OnCameraFocusEnd()
        {
            EventManager.instance.Broadcast(EventID.BattlePerformance_StepStart_SelectEntityEnd, m_entityView.uid);
            if (BattleShortCut.battleExecuter.IsWaitAllTeamUid != m_entityView.GetTeamUid())
            {
                BattleOpModeManager.instance.EventOnGridClick(m_entityView.GetLocatedPosition());
            }
            End();
        }
    }
}
