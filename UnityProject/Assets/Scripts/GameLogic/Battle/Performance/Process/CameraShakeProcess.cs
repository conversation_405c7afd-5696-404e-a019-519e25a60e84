
using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CameraShakeProcess : ProcessBase
    {
        //public BattleStageActionCameraShakeInfo info;
        public override void OnRelease()
        {
            //info = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            //if (info != null)
            //{
            //    Action onEnd = null;
            //    if (!info.immediately)
            //    {
            //        onEnd = End;
            //    }

            //    switch (info.pattern)
            //    {
            //        case CameraShakePattern.Custom:
            //            CameraShakeSystem.Shake(info.duration, info.amplitude, info.frequency, onEnd);
            //            break;
            //        case CameraShakePattern.Small:
            //            CameraShakeSystem.Shake(ShakeParam.Small, onEnd);
            //            break;
            //        case CameraShakePattern.Middle:
            //            CameraShakeSystem.Shake(ShakeParam.Middle, onEnd);
            //            break;
            //        case CameraShakePattern.Large:
            //            CameraShakeSystem.Shake(ShakeParam.Large, onEnd);
            //            break;
            //    }
            //    if (info.immediately)
            //    {
            //        End();
            //    }
            //}
            //else
            {
                End();
            }
        }
    }
}
