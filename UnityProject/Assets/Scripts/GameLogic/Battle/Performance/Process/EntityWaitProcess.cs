using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityWaitProcess : ProcessPlayerProcess
    {
        public int entityUid;
        public WaitProcedureResult procedureResult;

        public override void OnRelease()
        {
            entityUid = default;
            procedureResult = null;
            base.OnRelease();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                WaitProcedurePerformanceProcess performanceProcess = ClassPoolManager.instance.Fetch<WaitProcedurePerformanceProcess>();
                performanceProcess.Init(procedureResult, BattleShortCut.ForceSimplePerformance);
                sequenceProcess.AddProcess(performanceProcess);
                sequenceProcess.AddProcess(DelayEndProcess.Create(0.5f));
            }
        }
    }
}
