
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;


namespace Phoenix.GameLogic.Battle
{
    public class PlayUnityTimelineProcess : ProcessBase
    {
        public BattleStageActionInfo_PlayTimeline info;

        public override void OnRelease()
        {
            info = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            End();
            return;
            //m_timelinePlayer = new TimelinePlayer();
            //m_timelinePlayer.Init(BattleShortCut.battleScene.MainCamera);
            //Vector3 position = BattleShortCut.battleSceneGridManager.GetGridWorldPosition(info.gridPosition.x, info.gridPosition.y);
            //m_timelinePlayer.PlayTimeline(info.timelineId, position, Quaternion.identity, UnityEngine.Playables.DirectorWrapMode.None, OnTimelinePlayEnd);
            //if (info.playMode == Phoenix.Battle.PlayMode.Start)
            //{
            //    Vector3 position = GetPosition();
            //    if (info.immediately)
            //    {
            //        UnityEngine.Playables.DirectorWrapMode mode = UnityEngine.Playables.DirectorWrapMode.Hold;
            //        BattleShortCut.battleScene.timelinePlayer.PlayTimeline(info.timelineId, position, Quaternion.identity, mode);
            //        End();
            //    }
            //    else
            //    {
            //        UnityEngine.Playables.DirectorWrapMode mode = UnityEngine.Playables.DirectorWrapMode.None;
            //        BattleShortCut.battleScene.timelinePlayer.PlayTimeline(info.timelineId, position, Quaternion.identity, mode, End);
            //    }
            //}
            //else
            //{
            //    BattleShortCut.battleScene.timelinePlayer.StopTimeline();
            //    End();
            //}
        }

        private Vector3 GetPosition()
        {
            Vector3 position = Vector3.zero;
            //if (info.attachType == TimelineAttachType.Grid)
            //{
            //    position = BattleShortCut.battleSceneGridManager.GetGridWorldPosition(info.gridPosition.x, info.gridPosition.y);
            //}
            //else  if (info.attachType == TimelineAttachType.Entity)
            //{
            //    EntityView entityView = EntityViewManager.instance.GetEntityView(info.entityUid);
            //    if (entityView != null)
            //    {
            //        position = entityView.GetWorldPosition();
            //    }
            //    else
            //    {
            //        Debug.LogError("PlayUnityTimelineProcess entity is null id = " + info.entityUid);
            //    }
            //}
            return position;
        }

    }
}
