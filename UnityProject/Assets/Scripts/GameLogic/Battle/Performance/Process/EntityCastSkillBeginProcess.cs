using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityCastSkillBeginProcess : ProcessBase
    {
        public EntityCastSkillProcedureResult procedureResult;

        public override void OnRelease()
        {
            procedureResult = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            BattleUIStateHandler.ActiveState(BattleUIState.SkillPerform);
            BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.SkillPerformance);
            End();
        }
    }
}
