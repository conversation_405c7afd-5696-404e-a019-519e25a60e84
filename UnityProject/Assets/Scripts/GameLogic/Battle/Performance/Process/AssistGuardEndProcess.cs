using System;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class AssistGuardEndProcess : ProcessBase
    {
        public int targetEntityUid;
        public int guarderEntityUid;
        public GridPosition guarderOriginalPos;

        private StaticLerpVec3 m_lerp = new StaticLerpVec3();

        public override void OnRelease()
        {
            targetEntityUid = default;
            guarderEntityUid = default;
            m_lerp.Reset();
            base.OnRelease();
        }

        protected override void OnStart()
        {
            EventManager.instance.Broadcast(EventID.BattlePerformance_AssistGuardEnd, targetEntityUid, guarderEntityUid);
            EntityView targetView = EntityViewManager.instance.GetEntityView(targetEntityUid);
            EntityView guarderView = EntityViewManager.instance.GetEntityView(guarderEntityUid);
            BattleHelper.UpdateEntityBloodMask(false, BloodMask.EntityHide, targetEntityUid);
            if (targetView != null)
            {
                targetView.SetActiveSafely(true);
            }
            if (guarderView != null && guarderView.IsAlive())
            {
                m_lerp.actionOnTick = (l) => guarderView.transform.position = l.curValue;
                m_lerp.actionOnFinish = OnLerpEnd;
                var startPos = BattleShortCut.battleScene.sceneGridHandler.GetGridWorldPosition(guarderView.GetLocatedPosition());
                var endPos = BattleShortCut.battleScene.sceneGridHandler.GetGridWorldPosition(guarderOriginalPos);
                m_lerp.Start(startPos, endPos, 0.2f, EBaseLerpFuncType.Linear);
            }
            else
            {
                End();
            }
        }

        private void OnLerpEnd(AbstractStaticLerp<Vector3> lerp)
        {
            EntityView guarderView = EntityViewManager.instance.GetEntityView(guarderEntityUid);
            if (guarderView != null)
            {
                guarderView.SetPosition(guarderOriginalPos);
            }
            End();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            m_lerp.Tick(timeSlice.deltaTime);
        }
    }
}
