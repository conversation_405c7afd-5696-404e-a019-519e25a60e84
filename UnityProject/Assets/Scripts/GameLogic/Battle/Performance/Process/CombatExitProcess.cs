
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.GameLogic.UI;
using System;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class CombatExitProcess : ProcessBase
    {
        public int originEntityUid;
        public int targetEntityUid;
        public bool forceUseSimple;

        public override void OnRelease()
        {
            originEntityUid = default;
            targetEntityUid = default;
            forceUseSimple = false;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if (forceUseSimple)
            {
                TryCloseCombatUI(originEntityUid);
                DelayEnd(BattleParamSetting.instance.combatExitWaitTime2);
            }
            else
            {
                BattleShortCut.InBattleCombatStage = false;
                BattleShortCut.battleScene.sceneCameraHandler.Switch2MainCamera(OnSwitch2MainCameraEnd).Forget();
            }
        }

        protected override void OnEnd()
        {
            if (forceUseSimple)
            {
                EntityView originEntityView = EntityViewManager.instance.GetEntityView(originEntityUid);
                EntityView targetEntityView = EntityViewManager.instance.GetEntityView(targetEntityUid);
                UpdateEntityState(originEntityView, targetEntityView);
            }
            base.OnEnd();
        }

        private void OnSwitch2MainCameraEnd()
        {
            EntityView originEntityView = EntityViewManager.instance.GetEntityView(originEntityUid);
            EntityView targetEntityView = EntityViewManager.instance.GetEntityView(targetEntityUid);
            UpdateEntityState(originEntityView, targetEntityView);
            TryCloseCombatUI(originEntityUid);
            BattleShortCut.battleScene.ToggleScene(SceneType.Main);
            BattleHelper.UpdateEntityBloodMask(false, BloodMask.CombatPerformance, originEntityUid, targetEntityUid);
            DelayEnd(BattleParamSetting.instance.combatExitWaitTime);
        }


        private void UpdateEntityState(EntityView originEntityView, EntityView targetEntityView)
        {
            if (originEntityView != null)
            {
                originEntityView.SetPosition(originEntityView.GetLocatedPosition());
                if (targetEntityView != null)
                {
                    originEntityView.SetDirection(targetEntityView.GetLocatedPosition());
                }
                if (originEntityView.IsAlive())
                {
                    EntityPlayIdleBehaviour idleBehaviour = originEntityView.GetBehaviour<EntityPlayIdleBehaviour>();
                    if (idleBehaviour != null)
                    {
                        idleBehaviour.PlaySceneIdle(false);
                    }
                    //EntityAnimatorBehaviourBase animatorBehaviour = originEntityView.GetBehaviour<EntityAnimatorBehaviourBase>();
                    //if(animatorBehaviour != null)
                    //{
                    //    animatorBehaviour.PlayIdle();
                    //}
                }
            }
            if (targetEntityView != null)
            {
                targetEntityView.UpdateEntityMaterial();
                targetEntityView.SetPosition(targetEntityView.GetLocatedPosition());
                if (originEntityView != null)
                {
                    targetEntityView.SetDirection(originEntityView.GetLocatedPosition());
                }
                if (targetEntityView.IsAlive())
                {
                    EntityPlayIdleBehaviour idleBehaviour = targetEntityView.GetBehaviour<EntityPlayIdleBehaviour>();
                    if (idleBehaviour != null)
                    {
                        idleBehaviour.PlaySceneIdle(false);
                    }
                    //EntityAnimatorBehaviourBase animatorBehaviour = targetEntityView.GetBehaviour<EntityAnimatorBehaviourBase>();
                    //if (animatorBehaviour != null)
                    //{
                    //    animatorBehaviour.PlayIdle();
                    //}
                }
            }
            ShowOtherEntityView();
        }

        private void ShowOtherEntityView()
        {
            var entityViewMap = EntityViewManager.instance.GetEntityViewMap();
            foreach (var kv in entityViewMap)
            {
                if (kv.Key == originEntityUid || kv.Key == targetEntityUid)
                {
                    continue;
                }
                kv.Value.SetActiveSafely(true);
            }
        }


        protected void TryCloseCombatUI(Int32 sourceEntityUid)
        {
            EntityView sourceEntityView = EntityViewManager.instance.GetEntityView(sourceEntityUid);
            if (sourceEntityView != null)
            {
                //Skill skill = sourceEntityView.GetSkillBySkillUid(skillUid);
                //if (skill != null && skill.engageType == SkillEngageType.Combat)
                {
                    BattleCombatUI.CloseCombatUI();
                }
            }
        }
    }
}
