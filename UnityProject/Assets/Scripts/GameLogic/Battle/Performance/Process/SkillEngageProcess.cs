using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.Drama;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class SkillEngageProcess : ProcessPlayerProcess
    {
        private SkillEngageResult m_engageResult;
        private bool m_simplePerformance;

        public override void OnRelease()
        {
            m_engageResult = null;
            m_simplePerformance = false;
            base.OnRelease();
        }

        public void Init(SkillEngageResult engageResult, bool simplePerformance)
        {
            m_engageResult = engageResult;
            m_simplePerformance = simplePerformance;
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            SkillInfo skillInfo = BattleShortCut.infoGetter.GetSkillInfo(m_engageResult.skillRid);
            sequenceProcess.AddProcess(GetAssistGuardBeginProcess());
            var engageBeginProcess = ClassPoolManager.instance.Fetch<SkillEngageBeginProcess>();
            engageBeginProcess.result = m_engageResult;
            sequenceProcess.AddProcess(engageBeginProcess);

            //CameraFocusProProcess process = ClassPoolManager.instance.Fetch<CameraFocusProProcess>();
            //process.InitDatas(false, m_engageResult.originEntityUid, m_engageResult.targetEntityUid);
            //sequenceProcess.AddProcess(process);

            bool forceUseSimple = skillInfo.forceUseSimple;
            if (skillInfo.engageType == SkillEngageType.Combat)
            {
                CombatEnterProcess combatEnterProcess = ClassPoolManager.instance.Fetch<CombatEnterProcess>();
                combatEnterProcess.originEntityUid = m_engageResult.originEntityUid;
                combatEnterProcess.targetEntityUid = m_engageResult.targetEntityUid;
                combatEnterProcess.firstSkillUid = m_engageResult.skillCastResultList[0].skillUid;
                combatEnterProcess.forceUseSimple = forceUseSimple;
                sequenceProcess.AddProcess(combatEnterProcess);
            }
            for (int i = 0; i < m_engageResult.skillCastResultList.Count; ++i)
            {
                var skillBaseResult = m_engageResult.skillCastResultList[i];
                SkillCastResult nextResult = m_engageResult.skillCastResultList.GetValueSafely(i + 1);
                bool simplePerformance = forceUseSimple || m_simplePerformance;
                InitProcessBySkillCastResult(sequenceProcess, skillBaseResult, skillInfo.engageType, simplePerformance, nextResult);
            }
            if (skillInfo.engageType == SkillEngageType.Combat)
            {
                CombatExitProcess combatExitProcess = ClassPoolManager.instance.Fetch<CombatExitProcess>();
                combatExitProcess.originEntityUid = m_engageResult.originEntityUid;
                combatExitProcess.targetEntityUid = m_engageResult.targetEntityUid;
                combatExitProcess.forceUseSimple = forceUseSimple;
                sequenceProcess.AddProcess(combatExitProcess);
            }
            var engageEndProcess = ClassPoolManager.instance.Fetch<SkillEngageEndProcess>();
            sequenceProcess.AddProcess(engageEndProcess);
            sequenceProcess.AddProcess(GetAssistGuardEndProcess());
            //RemoveDeadEntityProcess removeDeadEntityProcess = ClassPoolManager.instance.Fetch<RemoveDeadEntityProcess>();
            //sequenceProcess.AddProcess(removeDeadEntityProcess);


        }

        private void InitProcessBySkillCastResult(SequenceProcess sequenceProcess, SkillCastResult skillCastResult, SkillEngageType engageType, bool simplePerformance, SkillCastResult nextResult)
        {
            int skillRid = skillCastResult.skillRid;
            bool scenePerformance = engageType != SkillEngageType.Combat || simplePerformance;
            SkillInfo skillInfo = BattleShortCut.infoGetter.GetSkillInfo(skillRid);
            if (skillInfo != null)
            {
                if (!scenePerformance && CheckResultSkillNeedChangeCamera(skillCastResult))
                {
                    CameraChangeProcess cameraChangeProcess = ClassPoolManager.instance.Fetch<CameraChangeProcess>();
                    cameraChangeProcess.originEntityUid = skillCastResult.originEntityUid;
                    sequenceProcess.AddProcess(cameraChangeProcess);
                }
                
                CombatSkillStageChangeProcess combatSkillStageChangeProcess = ClassPoolManager.instance.Fetch<CombatSkillStageChangeProcess>();
                combatSkillStageChangeProcess.originEntityUid = skillCastResult.originEntityUid;
                sequenceProcess.AddProcess(combatSkillStageChangeProcess);

                SkillAnnounceProcess announceProcess = ClassPoolManager.instance.Fetch<SkillAnnounceProcess>();
                announceProcess.entityUid = skillCastResult.originEntityUid;
                announceProcess.skillRid = skillCastResult.skillRid;
                announceProcess.skillUid = skillCastResult.skillUid;
                announceProcess.simplePerformance = simplePerformance;
                sequenceProcess.AddProcess(announceProcess);
                DramaContext dramaContext = new DramaContext();
                EntityView view = EntityViewManager.instance.GetEntityView(skillCastResult.originEntityUid);
                dramaContext.dramaPath = view.GetDramaTimelinePath(simplePerformance ? skillInfo.dramaNameForSimple : skillInfo.dramaName);
                if (string.IsNullOrEmpty(dramaContext.dramaPath))
                {
                    dramaContext.dramaPath = skillInfo.dramaName;
                }
                dramaContext.simplePerformance = simplePerformance;
                dramaContext.scenePerformance = scenePerformance;
                dramaContext.engageType = engageType;
                dramaContext.isNextCounterAttack = CheckResultSkillIsCouterAttack(nextResult);
                dramaContext.originEntityUid = skillCastResult.originEntityUid;
                dramaContext.targetPosition = skillCastResult.targetSelectInfoList.GetValueSafely(0).gridPos;
                dramaContext.combatTargetEntityUid = skillCastResult.combatTargetEntityUid;
                dramaContext.InitEffectResultList(skillCastResult.effectResultList);

                if (!scenePerformance)
                {
                    float adjustDistance;
                    if (TryGetNextAdjustDistance(nextResult, out adjustDistance))
                    {
                        dramaContext.deltaAdjustDistance = adjustDistance - DramaTimelineUtility.GetAdjustDistance(simplePerformance ? skillInfo.dramaNameForSimple : skillInfo.dramaName);
                    }
                    EntityAdjustDistanceProcess adjustProcess = ClassPoolManager.instance.Fetch<EntityAdjustDistanceProcess>();
                    adjustProcess.dramaContext = dramaContext;
                    sequenceProcess.AddProcess(adjustProcess);
                }
                var dramaProcess = DramaTimelineProcess.Create(dramaContext);
                sequenceProcess.AddProcess(dramaProcess);
                if (nextResult != null && scenePerformance)
                {
                    sequenceProcess.AddProcess(DelayEndProcess.Create(BattleParamSetting.instance.combatSkillIntervalWaitTime2));
                }
            }
        }

        private ProcessBase GetAssistGuardBeginProcess()
        {
            if (m_engageResult.originalEntityUid <= 0)
            {
                return null; ;
            }
            var assistGuardEntityUid = m_engageResult.targetEntityUid;

            var sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();
            DialogBubbleProcess dialogBubbleProcess = ClassPoolManager.instance.Fetch<DialogBubbleProcess>();
            dialogBubbleProcess.triggerType = DialogBubbleEntityTriggerType.AssistGuard;
            dialogBubbleProcess.entityUid = assistGuardEntityUid;
            sequenceProcess.AddProcess(dialogBubbleProcess);

            var parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();
            sequenceProcess.AddProcess(parallelProcess);

            var announceProcess = ClassPoolManager.instance.Fetch<AssistGuardBeginAnnounceProcess>();
            announceProcess.entityUid = assistGuardEntityUid;
            parallelProcess.AddProcess(announceProcess);

            AssistGuardBeginProcess assistGuardBeginProcess = ClassPoolManager.instance.Fetch<AssistGuardBeginProcess>();
            assistGuardBeginProcess.guarderEntityUid = assistGuardEntityUid;
            assistGuardBeginProcess.targetEntityUid = m_engageResult.originalEntityUid;
            assistGuardBeginProcess.srcEntityUid = m_engageResult.originEntityUid;
            parallelProcess.AddProcess(assistGuardBeginProcess);

            DramaContext dramaContext = new DramaContext();
            EntityView view = EntityViewManager.instance.GetEntityView(assistGuardEntityUid);
            dramaContext.dramaPath = view.GetDramaTimelinePath(BattleDefine.AssistGuardBeginDramaName);
            dramaContext.originEntityUid = assistGuardEntityUid;
            dramaContext.simplePerformance = true;
            dramaContext.skipPerformance = false;
            var dramaProcess = DramaTimelineProcess.Create(dramaContext);
            parallelProcess.AddProcess(dramaProcess);

            return sequenceProcess;
        }

        private ProcessBase GetAssistGuardEndProcess()
        {
            if (m_engageResult.originalEntityUid <= 0)
            {
                return null;
            }
            var parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();


            AssistGuardEndProcess assistGuardEndProcess = ClassPoolManager.instance.Fetch<AssistGuardEndProcess>();
            assistGuardEndProcess.guarderEntityUid = m_engageResult.targetEntityUid;
            assistGuardEndProcess.targetEntityUid = m_engageResult.originalEntityUid;
            assistGuardEndProcess.guarderOriginalPos = m_engageResult.assistGuardOriginalPos;
            parallelProcess.AddProcess(assistGuardEndProcess);

            return parallelProcess;
        }

        private bool TryGetNextAdjustDistance(SkillCastResult nextResult, out float adjustDistance)
        {
            if (nextResult != null)
            {
                var originEntity = EntityViewManager.instance.GetEntityView(nextResult.originEntityUid);
                if (originEntity != null)
                {
                    Skill skill = originEntity.GetSkillBySkillUid(nextResult.skillUid);
                    if (skill != null)
                    {
                        string dramaPath = originEntity.GetDramaTimelinePath(skill.skillInfo.dramaName);
                        adjustDistance = DramaTimelineUtility.GetAdjustDistance(dramaPath);
                        return true;
                    }
                }
            }
            adjustDistance = 0f;
            return false;
        }

        private bool CheckResultSkillNeedChangeCamera(SkillCastResult nextResult)
        {
            if (nextResult != null)
            {
                var originEntity = EntityViewManager.instance.GetEntityView(nextResult.originEntityUid);
                if (originEntity != null)
                {
                    Skill skill = originEntity.GetSkillBySkillUid(nextResult.skillUid);
                    if (skill != null)
                    {
                        return skill.slotId == SkillSlotId.CounterAttack || skill.slotId == SkillSlotId.AdditionalAttack;
                    }
                }
            }
            return false;
        }

        private bool CheckResultSkillIsCouterAttack(SkillCastResult nextResult)
        {
            if (nextResult != null)
            {
                var originEntity = EntityViewManager.instance.GetEntityView(nextResult.originEntityUid);
                if (originEntity != null)
                {
                    Skill skill = originEntity.GetSkillBySkillUid(nextResult.skillUid);
                    if (skill != null)
                    {
                        return skill.slotId == SkillSlotId.CounterAttack || skill.slotId == SkillSlotId.AdditionalAttack;
                    }
                }
            }
            return false;
        }
    }
}
