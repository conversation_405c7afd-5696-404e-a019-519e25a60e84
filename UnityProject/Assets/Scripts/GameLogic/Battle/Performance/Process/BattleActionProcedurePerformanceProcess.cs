using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class BattleActionProcedurePerformanceProcess : ProcessPlayerProcess
    {
        protected bool m_simplePerformance;
        protected BattleActionProcedureResult m_result;

        public void Init(BattleActionProcedureResult result, bool simplePerformance)
        {
            m_result = result;
            m_simplePerformance = simplePerformance;
        }

        public override void OnRelease()
        {
            m_result = null;
            m_simplePerformance = false;
            base.OnRelease();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            UI.DebugUI.AnalyzeBattleReport(m_result);
            sequenceProcess.AddProcess(BattleActionProcedurePreloadProcess.Create(m_result, m_simplePerformance));
            for (int i = 0; i < m_result.subResultList.Count; ++i)
            {
                BattleActionResult result = m_result.subResultList[i];
                if (result is BuffTriggerResult)
                {
                    InitProcessByBuffTriggerResult(sequenceProcess, result as BuffTriggerResult);
                }
                else if (result is SkillEngageResult)
                {
                    InitProcessBySkillEngageResult(sequenceProcess, result as SkillEngageResult);
                }
                else if (result is BuffTickResult)
                {
                    InitProcessByBuffTickResult(sequenceProcess, result as BuffTickResult);
                }
                else if (result is BuffAttachResult)
                {
                    InitProcessByBuffAttachResult(sequenceProcess, result as BuffAttachResult);
                }
                else if (result is BuffDetachResult)
                {
                    InitProcessByBuffDetachResult(sequenceProcess, result as BuffDetachResult);
                }
                else if (result is EntityActionEndResult)
                {
                    InitProcessByEntityActionEndResult(sequenceProcess, result as EntityActionEndResult);
                }
                else if (result is BuffChangeActiveGroupResult)
                {
                    InitProcessByBuffChangeActiveGroupResult(sequenceProcess, result as BuffChangeActiveGroupResult);
                }
                else if (result is TerrainEnterResult)
                {
                    InitProcessByTerrainEnterResult(sequenceProcess, result as TerrainEnterResult);
                }
                else if (result is TerrainTriggerResult)
                {
                    InitProcessByTerrainTriggerResult(sequenceProcess, result as TerrainTriggerResult);
                }
                else if (result is TerrainExitResult)
                {
                    InitProcessByTerrainExitResult(sequenceProcess, result as TerrainExitResult);
                }
                else if (result is EntityDeadLogicEventResult)
                {
                    InitProcessByEntityDeadLogicEventResult(sequenceProcess, result as EntityDeadLogicEventResult);
                }
                else if (result is EntityBlockBreakLogicEventResult)
                {
                    InitProcessByEntityBlockBreakLogicEventResult(sequenceProcess, result as EntityBlockBreakLogicEventResult);
                }
                else if (result is TreasureBoxGainResult)
                {
                    InitProcessByTreasureBoxGainResult(sequenceProcess, result as TreasureBoxGainResult);
                }
                else if (result is BattleActionEffectResult)
                {
                    InitProcessBySkillEffectResult(sequenceProcess, result as SkillEffectResult);
                }
                else if (result is BuffChangeLifeTimeResult)
                {
                    InitProcessByBuffChangeLifeTimeResult(sequenceProcess, result as BuffChangeLifeTimeResult);
                }
                else if (result is TeamEnergyChangeResult teamEnergyChangeResult)
                {
                    InitProcessByBuffChangeLifeTimeResult(sequenceProcess, teamEnergyChangeResult);
                }
                else
                {
                    throw new Exception();
                }
            }
            RemoveDeadEntityProcess removeDeadEntityProcess = ClassPoolManager.instance.Fetch<RemoveDeadEntityProcess>();
            sequenceProcess.AddProcess(removeDeadEntityProcess);
        }

        private void InitProcessBySkillEngageResult(SequenceProcess sequenceProcess, SkillEngageResult engageResult)
        {
            SkillEngageProcess engageProcess = ClassPoolManager.instance.Fetch<SkillEngageProcess>();
            engageProcess.Init(engageResult, m_simplePerformance);
            sequenceProcess.AddProcess(engageProcess);
        }

        private void InitProcessByBuffTriggerResult(SequenceProcess sequenceProcess, BuffTriggerResult buffTriggerResult)
        {
            BuffConfigData buffConfigData = ConfigDataManager.instance.GetBuff(buffTriggerResult.buffRid);
            bool skipPerformance = buffConfigData == null || !buffConfigData.needAnnounce;
            if (!skipPerformance)
            {
                CameraFocusProcess process = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
                process.entityUid = buffTriggerResult.originEntityUid;
                sequenceProcess.AddProcess(process);
            }
            var buffCoolTimeUpdateProcess = ClassPoolManager.instance.Fetch<BuffCoolTimeUpdateProcess>();
            buffCoolTimeUpdateProcess.buffTriggerResult = buffTriggerResult;
            sequenceProcess.AddProcess(buffCoolTimeUpdateProcess);

            if (!skipPerformance && buffConfigData.tagList.Contains(BuffTagId.Exciting))
            {
                DialogBubbleProcess dialogBubbleProcess = ClassPoolManager.instance.Fetch<DialogBubbleProcess>();
                dialogBubbleProcess.triggerType = DialogBubbleEntityTriggerType.CheerUp;
                dialogBubbleProcess.entityUid = buffTriggerResult.originEntityUid;
                sequenceProcess.AddProcess(dialogBubbleProcess);
            }

            ParallelProcess parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();
            sequenceProcess.AddProcess(parallelProcess);

            if (!skipPerformance)
            {
                BuffTriggerAnnounceProcess buffAnnounceProcess = ClassPoolManager.instance.Fetch<BuffTriggerAnnounceProcess>();
                buffAnnounceProcess.entityUid = buffTriggerResult.originEntityUid;
                buffAnnounceProcess.buffRid = buffTriggerResult.buffRid;
                parallelProcess.AddProcess(buffAnnounceProcess);
            }

            BuffInfo buffInfo = BattleShortCut.infoGetter.GetBuffInfo(buffTriggerResult.buffRid);
            if (buffInfo != null)
            {
                var buffEffectInfo = buffInfo.effectList.GetValueSafely(buffTriggerResult.effectIndex) as BuffEffectInfo_SkillTrigger;
                if (buffEffectInfo != null)
                {
                    DramaContext dramaContext = new DramaContext();
                    EntityView view = EntityViewManager.instance.GetEntityView(buffTriggerResult.originEntityUid);
                    dramaContext.dramaPath = view.GetDramaTimelinePath(buffEffectInfo.dramaName);
                    dramaContext.originEntityUid = buffTriggerResult.originEntityUid;
                    dramaContext.simplePerformance = m_simplePerformance;
                    dramaContext.scenePerformance = true;
                    dramaContext.skipPerformance = skipPerformance;
                    dramaContext.InitEffectResultList(buffTriggerResult.effectResultList);
                    var dramaProcess = DramaTimelineProcess.Create(dramaContext);
                    parallelProcess.AddProcess(dramaProcess);
                }
            }
            //RemoveDeadEntityProcess removeDeadEntityProcess = ClassPoolManager.instance.Fetch<RemoveDeadEntityProcess>();
            //sequenceProcess.AddProcess(removeDeadEntityProcess);
        }

        private void InitProcessByBuffTickResult(SequenceProcess sequenceProcess, BuffTickResult buffTickResult)
        {
            for (int i = 0; i < buffTickResult.buffChangeResultList.Count; ++i)
            {
                BuffChangeProcess process = ClassPoolManager.instance.Fetch<BuffChangeProcess>();
                process.result = buffTickResult.buffChangeResultList[i];
                sequenceProcess.AddProcess(process);
            }
        }

        private void InitProcessByBuffAttachResult(SequenceProcess sequenceProcess, BuffAttachResult attachResult)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                BuffPerformanceUtility.PerformanceBuffAttach(attachResult);
            }));
        }

        private void InitProcessByBuffDetachResult(SequenceProcess sequenceProcess, BuffDetachResult detachResult)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                BuffPerformanceUtility.PerformanceBuffDetach(detachResult);
            }));
        }

        private void InitProcessByEntityActionEndResult(SequenceProcess sequenceProcess, EntityActionEndResult result)
        {
            EntityActionEndProcess process = ClassPoolManager.instance.Fetch<EntityActionEndProcess>();
            process.result = result;
            sequenceProcess.AddProcess(process);
            if (result.triggerPreAnnounce)
            {
                var preAnnounceProcess = ClassPoolManager.instance.Fetch<SkillPreAnnounceBeginProcess>();
                preAnnounceProcess.result = result;
                sequenceProcess.AddProcess(preAnnounceProcess);
            }
            else
            {
                var preAnnounceProcess = ClassPoolManager.instance.Fetch<SkillPreAnnounceEndProcess>();
                preAnnounceProcess.result = result;
                sequenceProcess.AddProcess(preAnnounceProcess);
            }

            if (result.triggerExtraMove || result.triggerExtraAction)
            {
                CameraFocusProcess focusProcess = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
                focusProcess.entityUid = result.entityUid;
                sequenceProcess.AddProcess(focusProcess);
                sequenceProcess.AddProcess(DelayEndProcess.Create(0.2f));

                ParallelProcess parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();
                sequenceProcess.AddProcess(parallelProcess);

                string dramaName = string.Empty;
                if (result.triggerExtraMove)
                {
                    dramaName = BattleDefine.ExtraMoveAnnounceDramaName;
                    var extraProcess = ClassPoolManager.instance.Fetch<EntityExtraMoveAnnounceProcess>();
                    extraProcess.entityUid = result.entityUid;
                    parallelProcess.AddProcess(extraProcess);
                }
                else if (result.triggerExtraAction)
                {
                    dramaName = BattleDefine.ExtraActionAnnounceDramaName;
                    DialogBubbleProcess dialogBubbleProcess = ClassPoolManager.instance.Fetch<DialogBubbleProcess>();
                    dialogBubbleProcess.triggerType = DialogBubbleEntityTriggerType.ExtraAction;
                    dialogBubbleProcess.entityUid = result.entityUid;
                    parallelProcess.AddProcess(dialogBubbleProcess);

                    var extraProcess = ClassPoolManager.instance.Fetch<EntityExtraActionAnnounceProcess>();
                    extraProcess.entityUid = result.entityUid;
                    parallelProcess.AddProcess(extraProcess);
                }
                DramaContext dramaContext = new DramaContext();
                EntityView view = EntityViewManager.instance.GetEntityView(result.entityUid);
                dramaContext.dramaPath = view.GetDramaTimelinePath(dramaName);
                dramaContext.originEntityUid = result.entityUid;
                dramaContext.simplePerformance = m_simplePerformance;
                dramaContext.scenePerformance = true;
                dramaContext.skipPerformance = false;
                var dramaProcess = DramaTimelineProcess.Create(dramaContext);
                parallelProcess.AddProcess(dramaProcess);
            }
        }

        private void InitProcessByBuffChangeActiveGroupResult(SequenceProcess sequenceProcess, BuffChangeActiveGroupResult groupResult)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                foreach (var result in groupResult.resultList)
                {
                    BuffPerformanceUtility.PerformanceChangeActiveList(groupResult.resultList);
                }
            }));
        }

        private void InitProcessByTerrainEnterResult(SequenceProcess sequenceProcess, TerrainEnterResult result)
        {
            TerrainEnterProcess process = ClassPoolManager.instance.Fetch<TerrainEnterProcess>();
            process.result = result;
            sequenceProcess.AddProcess(process);
        }

        private void InitProcessByTerrainTriggerResult(SequenceProcess sequenceProcess, TerrainTriggerResult result)
        {
            TerrainTriggerProcess process = ClassPoolManager.instance.Fetch<TerrainTriggerProcess>();
            process.Init(result, m_simplePerformance);
            sequenceProcess.AddProcess(process);
        }

        private void InitProcessByTerrainExitResult(SequenceProcess sequenceProcess, TerrainExitResult result)
        {
            TerrainExitProcess process = ClassPoolManager.instance.Fetch<TerrainExitProcess>();
            process.result = result;
            sequenceProcess.AddProcess(process);
        }

        private void InitProcessByEntityDeadLogicEventResult(SequenceProcess sequenceProcess, EntityDeadLogicEventResult result)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                var entity = BattleShortCut.sampleBattle.GetEntityByUid(result.entityUid);
                var actor = entity as Actor;
                if (entity != null)
                {
                    if (entity.GetPreAnnounceSkillUid() > 0)
                    {
                        BattleShortCut.battleScene.skillPredictHandler.SkillPredictEnd(entity.uid, entity.GetPreAnnounceSkillUid());
                        entity.ResetPreAnnounce();
                    }
                    if (result.lifeCountChange)
                    {
                        entity.SetCurLifeCount(result.lifeCount);

                        IBattle battle = BattleShortCut.sampleBattle;
                        var targetActor = battle.GetEntityByUid(result.entityUid) as Actor;
                        var targetActorView = EntityViewManager.instance.GetEntityView(result.entityUid);
                        var animPlayCtrl = targetActorView.GetBehaviour<EntityAnimatorBehaviour>();
                        animPlayCtrl.InterruptAnimation();
                        var transformActor = result.newLifeEntity.Copy(battle) as Actor;
                        targetActor.transformActor = transformActor;
                        EntityViewManager.instance.DestroyEntityView(targetActor.uid);
                        EntityViewManager.instance.CreateAndAddEntityView(targetActor);

                        entity.Resurrect();
                        var entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
                        var playIdleBehaviour = entityView.GetBehaviour<EntityPlayIdleBehaviour>();
                        if (playIdleBehaviour != null)
                        {
                            playIdleBehaviour.PlaySceneIdle(false);
                        }
                        EventManager.instance.Broadcast(EventID.Entity_CurHp_Changed, result.entityUid);
                        EventManager.instance.Broadcast(EventID.Entity_CurLifeCount_Changed, result.entityUid);
                    }
                    else if (result.transformBack)
                    {
                        var transformActor = actor.transformActor;
                        actor.transformActor = null;
                        actor.SetLocation(transformActor.GetLocation());
                        actor.SetDir(transformActor.GetDir());
                        actor.SetActionChance(result.hasActionChance);
                        actor.transformActor.Release();
                        EntityViewManager.instance.DestroyEntityView(actor.uid);
                        EntityViewManager.instance.CreateAndAddEntityView(actor);
                    }
                    else
                    {
                        entity.SetDead();
                    }
                }
                BuffPerformanceUtility.PerformanceChangeActiveList(result.buffChangeActiveResultList);
            }));
        }

        private void InitProcessByEntityBlockBreakLogicEventResult(SequenceProcess sequenceProcess, EntityBlockBreakLogicEventResult result)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                var entity = BattleShortCut.sampleBattle.GetEntityByUid(result.entityUid);
                foreach (var buffAttachResult in result.attachResultList)
                {
                    BuffPerformanceUtility.PerformanceBuffAttach(buffAttachResult);
                }
                foreach (var buffDetachResult in result.detachResultList)
                {
                    BuffPerformanceUtility.PerformanceBuffDetach(buffDetachResult);
                }
            }));
        }

        private void InitProcessByTreasureBoxGainResult(SequenceProcess sequenceProcess, TreasureBoxGainResult result)
        {
            var process = ClassPoolManager.instance.Fetch<TreasureBoxGainProcess>();
            process.info = result.info;
            sequenceProcess.AddProcess(process);
        }

        private void InitProcessBySkillEffectResult(SequenceProcess sequenceProcess, SkillEffectResult result)
        {
            DramaContext dramaContext = new DramaContext();
            dramaContext.dramaPath = string.Empty;
            dramaContext.simplePerformance = m_simplePerformance;
            dramaContext.scenePerformance = true;
            dramaContext.InitSingleEffectResult(result);
            sequenceProcess.AddProcess(DramaTimelineProcess.Create(dramaContext));
        }

        private void InitProcessByBuffChangeLifeTimeResult(SequenceProcess sequenceProcess, BuffChangeLifeTimeResult result)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                BuffPerformanceUtility.PerformanceBuffChange(result);
            }));
        }

        private void InitProcessByBuffChangeLifeTimeResult(SequenceProcess sequenceProcess, TeamEnergyChangeResult result)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                var team = BattleShortCut.sampleBattle.GetTeamByUid(result.teamUid);
                team.sharedEnergy = result.updateEnergy;
                EventManager.instance.Broadcast(EventID.BattleTeam_SharedEnergy_Changed, team.uid, team.sharedEnergy);
            }));
        }

    }
}
