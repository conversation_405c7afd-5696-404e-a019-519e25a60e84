
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class StageActionGroupBeginProcess : ProcessBase
    {
        protected override void OnStart()
        {
            base.OnStart();
            BattleUIStateHandler.ActiveState(BattleUIState.BattleStoryPerform);
            BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.StoryPerformance);
            End();
        }
    }
}
