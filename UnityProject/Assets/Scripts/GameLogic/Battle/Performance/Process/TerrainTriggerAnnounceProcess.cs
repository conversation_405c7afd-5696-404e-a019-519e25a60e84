using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TerrainTriggerAnnounceProcess : ProcessBase
    {
        public bool isTerrainBuff;
        public int terrainRid;
        public int entityUid;

        public override void OnRelease()
        {
            isTerrainBuff = false;
            terrainRid = default;
            entityUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if (!NeedAnnounce())
            {
                End();
                return;
            }
            if (isTerrainBuff)
            {
                EventManager.instance.Broadcast(EventID.BattlePerformance_TerrainTriggerAnnounce, entityUid, terrainRid);
                DelayEnd(BattleParamSetting.instance.terrainBuffTriggerWaitTime);
            }
            else
            {
                End();
            }
        }

        private bool NeedAnnounce()
        {
            if (isTerrainBuff)
            {
                var terrainBuff = BattleShortCut.sampleBattle.GetEntityByUid(entityUid) as TerrainBuff;
                if (terrainBuff != null)
                {
                    var terrainBuffConfig = ConfigDataManager.instance.GetTerrainBuff(terrainBuff.rid);
                    return terrainBuffConfig != null && terrainBuffConfig.NeedAnnounce;
                }
            }
            return false;
        }
    }
}
