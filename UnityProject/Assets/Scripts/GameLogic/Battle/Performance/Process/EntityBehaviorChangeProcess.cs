using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityBehaviorChangeProcess : ProcessBase
    {
        public List<int> entityUidList = new List<int>();
        public EntityAIType aiType;

        public override void OnRelease()
        {
            entityUidList.Clear();
            aiType = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            foreach (var entityUid in entityUidList)
            {
                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                if (entityView != null)
                {
                    entityView.SetAIType(aiType);
                }
            }
            End();
        }
    }
}
