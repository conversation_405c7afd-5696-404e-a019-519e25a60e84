using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TerrainExitProcess : ProcessBase
    {
        public TerrainExitResult result;

        public override void OnRelease()
        {
            result.Release();
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if (result.buffDetachResultList.Count > 0)
            {
                foreach (var detachResult in result.buffDetachResultList)
                {
                    BuffPerformanceUtility.PerformanceBuffDetach(detachResult);
                }
            }
            End();
        }

    }
}
