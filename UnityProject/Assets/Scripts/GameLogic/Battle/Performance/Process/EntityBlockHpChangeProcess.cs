using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class EntityBlockHpChangeProcess : ProcessBase
    {
        public EntityBlockHitPointChangeResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            var entity = BattleShortCut.sampleBattle.GetEntityByUid(result.entityUid);
            if (entity != null)
            {
                foreach (var clip in result.clipList)
                {
                    entity.blockComponent.SetBlockHitPoint(clip.pos, clip.updateHitPoint);
                }
            }
            EventManager.instance.Broadcast(EventID.Entity_BlockHitPoint_Changed);
            End();
        }
    }
}
