using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class DialogProcess : ProcessBase
    {
        public BattleStageActionInfo_Dialog dialogInfo;
        private ActionWrap actionWrap;

        public override void OnRelease()
        {
            dialogInfo = default;
            base.OnRelease();
        }

        protected override void OnInit()
        {
            base.OnInit();
        }

        protected override void OnUnInit()
        {
            if(actionWrap != null)
            {
                actionWrap.Reset();
                actionWrap = default;
            }
            base.OnUnInit();
        }

        protected override void OnStart()
        {
            if(actionWrap == null)
            {
                actionWrap = new ActionWrap();
                actionWrap.Set(End);
            }
            DialogueUI.ShowDialogue(dialogInfo.dialogId, actionWrap.Invoke, null);
        }
    }
}
