using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class SkillEngageBeginProcess : ProcessBase
    {
        public SkillEngageResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            List<int> entityUidList = new List<int>() { result.originEntityUid };
            foreach (var skillCastResult in result.skillCastResultList)
            {
                foreach (var effectResult in skillCastResult.effectResultList)
                {
                    effectResult.CollectEntityUid(entityUidList);
                }
            }
            TimeScaleManager.instance.SetStaticTimeScale(TimeScaleId.EngageSpeed, BattleParamSetting.instance.engageSpeed);
            BattleHelper.UpdateOtherEntityBloodMask(true, BloodMask.CastSkill, entityUidList);
            End();
        }
    }
}
