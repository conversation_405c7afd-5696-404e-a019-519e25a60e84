
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class SkillProcedurePerformanceProcess : BattleActionProcedurePerformanceProcess
    {
        private EntityCastSkillProcedureResult specificResult
        {
            get { return m_result as EntityCastSkillProcedureResult; }
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            sequenceProcess.AddProcess(GetFaceToFaceClip());
            sequenceProcess.AddProcess(GetPrepareAnnounceClip());

            var result = m_result as EntityCastSkillProcedureResult;
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                var srcEntity = BattleShortCut.sampleBattle.GetEntityByUid(result.entityUid);
                srcEntity.SetCurHp(result.updateCurHp);
                srcEntity.GetTeam().sharedEnergy = result.updateEnergy;
                EventManager.instance.Broadcast(EventID.Entity_CurHp_Changed, result.entityUid);
                EventManager.instance.Broadcast(EventID.BattleTeam_SharedEnergy_Changed, BattleShortCut.sampleBattle.GetEntityByUid(result.entityUid).GetTeam().uid, result.updateEnergy);
            }));
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                //临时先放在这里
                BattleShortCut.sampleBattle.ResetEntityMustAct(result.entityUid);
                BattleUtility.SetOtherSummonRefMustAct(BattleShortCut.sampleBattle, result.entityUid, result.entityUid);
                if ((m_result as EntityCastSkillProcedureResult).resetExtraMove)
                {
                    var entityUid = (m_result as EntityCastSkillProcedureResult).entityUid;
                    BattleShortCut.sampleBattle.GetEntityByUid(entityUid).ResetExtraMoveChance();
                }
                if ((m_result as EntityCastSkillProcedureResult).resetExtraAction)
                {
                    var entityUid = (m_result as EntityCastSkillProcedureResult).entityUid;
                    BattleShortCut.sampleBattle.GetEntityByUid(entityUid).ResetExtraActionChance();
                }
            }));
            base.OnInitProcess(sequenceProcess);
        }

        protected ProcessBase GetFaceToFaceClip()
        {
            EntityView originEntityView = EntityViewManager.instance.GetEntityView(specificResult.entityUid);
            SkillInfo skillInfo = BattleShortCut.infoGetter.GetSkillInfo(specificResult.skillRid);
            if (skillInfo.engageType == SkillEngageType.Combat)
            {
                ParallelProcess parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();

                EntityLookAtProcess turnProcess = null;
                if (originEntityView != null)
                {
                    turnProcess = ClassPoolManager.instance.Fetch<EntityLookAtProcess>();
                    turnProcess.entityUidList.Add(specificResult.entityUid);
                    turnProcess.targetPos = specificResult.targetPos;
                    parallelProcess.AddProcess(turnProcess);
                }
                EntityView targetEntityView = EntityViewManager.instance.GetEntityView(specificResult.targetEntityUid);
                if (targetEntityView != null)
                {
                    turnProcess = ClassPoolManager.instance.Fetch<EntityLookAtProcess>();
                    turnProcess.entityUidList.Add(targetEntityView.uid);
                    turnProcess.targetPos = originEntityView.GetLocatedPosition();
                    parallelProcess.AddProcess(turnProcess);
                }
                return parallelProcess;
            }
            else if (skillInfo.engageType == SkillEngageType.Scene)
            {
                EntityLookAtProcess turnProcess = null;
                if (originEntityView != null)
                {
                    turnProcess = ClassPoolManager.instance.Fetch<EntityLookAtProcess>();
                    turnProcess.entityUidList.Add(specificResult.entityUid);
                    if (specificResult.targetDir != GridDirType.None)
                    {
                        turnProcess.targetPos = originEntityView.GetLocatedPosition() + BattleUtility.GetOffsetByDir(specificResult.targetDir);
                    }
                    else
                    {
                        turnProcess.targetPos = specificResult.targetPos;
                    }
                    turnProcess.waitEnd = true;
                    return turnProcess;
                }
            }
            return null;

        }

        protected ProcessBase GetPrepareAnnounceClip()
        {
            EntityCastSkillPrepareAnnounceProcess process = ClassPoolManager.instance.Fetch<EntityCastSkillPrepareAnnounceProcess>();
            process.procedureResult = specificResult;
            return process;
        }
    }
}
