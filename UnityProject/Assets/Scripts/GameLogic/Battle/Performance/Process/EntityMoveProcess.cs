using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityMoveProcess : ProcessBase
    {
        public int entityUid;
        public GridPosition gridPos;
        public bool isMoveBack;

        private List<GridPosition> m_posList = new List<GridPosition>();
        private MovePathFindResult m_movePathResult;

        public override void OnRelease()
        {
            entityUid = default;
            gridPos = default;
            isMoveBack = false;
            m_posList.Clear();
            if(m_movePathResult != null)
            {
                m_movePathResult.Release();
                m_movePathResult = null;
            }
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView == null || entityView.GetLocatedPosition() == gridPos)
            {
                End();
                return;
            }
            m_movePathResult = MovePathFindUtility.FindPathAnyway(entityView.GetLocatedPosition(), gridPos, entityView.GetMovePathRule());
            m_movePathResult.AppendToMovePos(m_posList);
            EventManager.instance.Broadcast(EventID.EventOnCameraFollowBegin, entityView.gameObject, FollowMode.HardPoint);
            entityView.StartMoveGridPosList(m_posList, isMoveBack, End);
        }

        protected override void OnEnd()
        {
            EventManager.instance.Broadcast(EventID.EventOnCameraFollowEnd);
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                entityView.SetLocation(gridPos);
                EventManager.instance.Broadcast(EventID.Entity_BlockHitPoint_Changed);
            }
            base.OnEnd();
        }
    }
}
