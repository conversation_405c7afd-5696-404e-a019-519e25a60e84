using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class DelayActionProcess : CollectionProcess<DelayActionProcess>
    {
        private List<DelayActionInfo> m_delayActionInfoList = new List<DelayActionInfo>();
        private float m_preTime;
        private float m_curTime;
        private float m_maxTime;
        private StateId m_state;

        public override bool canStart
        {
            get { return base.canStart || delayTime > 0f || m_delayActionInfoList.Count > 0; }
        }

        protected virtual float delayTime { get { return 0f; } }

        public override void OnRelease()
        {
            m_state = StateId.None;
            m_maxTime = 0f;
            m_curTime = 0f;
            m_preTime = float.MinValue;
            m_delayActionInfoList.Clear();
            base.OnRelease();
        }

        protected void AddDelayAction(float time, Action<float> action)
        {
            m_delayActionInfoList.Add(new DelayActionInfo()
            {
                time = time,
                action = action,
            });
        }

        protected void AddDelayProcess(float time, ProcessBase process)
        {
            DelayProcessWrapProcess processWrapProcess = ClassPoolManager.instance.Fetch<DelayProcessWrapProcess>();
            processWrapProcess.SetProcess(time, process);
            AddProcess(processWrapProcess);
        }

        protected sealed override void OnStart()
        {
            m_curTime = 0f;
            m_state = StateId.Delaying;
        }

        protected sealed override void OnTick(TimeSlice timeSlice)
        {
            if (m_state != StateId.Delaying)
            {
                for (int i = 0; i < m_subProcessList.Count; ++i)
                {
                    m_subProcessList[i].Tick(timeSlice);
                }
            }
            if (m_state == StateId.Delaying)
            {
                m_curTime += timeSlice.deltaTime;
                if (m_curTime > delayTime)
                {
                    m_state = StateId.Running;
                    for (int i = 0; i < m_subProcessList.Count; ++i)
                    {
                        DelayActionProcess process = m_subProcessList[i];
                        if (process == null)
                        {
                            continue;
                        }
                        if (!StartSubProcessAndCheckRunning(process))
                        {
                            OnSubProcessEnd(process);
                        }
                    }
                    OnMainStart();
                    float overtime = m_curTime - delayTime;
                    m_preTime = float.MinValue;
                    m_curTime = 0f;
                    m_maxTime = 0f;
                    for (int i = 0; i < m_delayActionInfoList.Count; ++i)
                    {
                        m_maxTime = Mathf.Max(m_delayActionInfoList[i].time);
                    }
                    timeSlice.ResizeByDeltaTime(overtime);
                    Tick(timeSlice);
                }
            }
            else if (m_state == StateId.Running)
            {
                float preTime = m_preTime;
                m_curTime += timeSlice.deltaTime;
                m_preTime = m_curTime;
                OnMainTick(timeSlice);
                for (int i = 0; i < m_delayActionInfoList.Count; ++i)
                {
                    DelayActionInfo delayActionInfo = m_delayActionInfoList[i];
                    if (delayActionInfo.time >= preTime && delayActionInfo.time < m_curTime)
                    {
                        float overtime = m_curTime - delayActionInfo.time;
                        if (overtime < 0f)
                        {
                            overtime = 0f;
                        }
                        delayActionInfo.action.InvokeSafely(overtime);
                    }
                }
                if (m_curTime > m_maxTime)
                {
                    EndMain();
                }
            }
        }

        protected override void OnSubProcessEnd(ProcessBase process)
        {
            TryEnd();
        }

        protected virtual void OnMainTick(TimeSlice timeSlice)
        {

        }

        protected virtual void OnMainStart()
        {

        }

        protected void TryEnd()
        {
            if (CheckEnd())
            {
                End();
            }
        }

        private void EndMain()
        {
            m_state = StateId.End;
            TryEnd();
        }

        protected virtual bool CheckEnd()
        {
            if (m_state != StateId.End)
            {
                return false;
            }
            for (int i = 0; i < m_subProcessList.Count; ++i)
            {
                if (!m_subProcessList[i].CheckEnd())
                {
                    return false;
                }
            }
            return true;
        }

        private struct DelayActionInfo
        {
            public float time;
            public Action<float> action;
        }

        private enum StateId
        {
            None,
            Delaying,
            Running,
            End,
        }
    }
}
