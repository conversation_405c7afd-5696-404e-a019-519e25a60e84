using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class EntityAdjustDistanceProcess : ProcessBase
    {
        public DramaContext dramaContext;

        public override void OnRelease()
        {
            dramaContext = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EntityView originEntity = EntityViewManager.instance.GetEntityView(dramaContext.originEntityUid);
            EntityView targetEntity = EntityViewManager.instance.GetEntityView(dramaContext.combatTargetEntityUid);
            if (originEntity == null || targetEntity == null)
            {
                End();
                return;
            }
            var animatorBahaviour = originEntity.GetBehaviour<EntityAnimatorBehaviour>();
            float distance = Vector3.Distance(originEntity.GetWorldPosition(), targetEntity.GetWorldPosition());
            float moveLength = 0f;
            float adjustDistance = DramaTimelineUtility.GetAdjustDistance(dramaContext.dramaPath);
            string animName = string.Empty;
            if (distance > adjustDistance + 0.1f)
            {
                moveLength = distance - adjustDistance;
                animName = "Combat_ReturnForwardRootMotion";
            }
            if (distance < adjustDistance - 0.1f)
            {
                End();
                return;
                moveLength = adjustDistance - distance;
                animName = "Combat_ReturnBackRootMotion";
            }
            if (moveLength < 1e-4)
            {
                End();
                return;
            }
            animatorBahaviour.rootMotionEnabled = true;
            animatorBahaviour.rootMotionRatioXZ = moveLength;
            animatorBahaviour.SetSpeed(EntityAnimatorSpeedPriority.AdjustPos, Mathf.Clamp(1 / moveLength, 0.5f, 1f));
            animatorBahaviour.PlayAnimationOnce(animName, 0f, interrupt =>
            {
                animatorBahaviour.rootMotionEnabled = false;
                animatorBahaviour.ResetSpeed(EntityAnimatorSpeedPriority.AdjustPos);
                End();
            });
        }
    }
}
