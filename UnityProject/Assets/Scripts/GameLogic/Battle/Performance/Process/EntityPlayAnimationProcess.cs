using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityPlayAnimationProcess : ProcessBase
    {
        public int entityUid;
        public string animationName;

        public override void OnRelease()
        {
            entityUid = default;
            animationName = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            EntityAnimatorBehaviour animatorBehaviour = entityView.GetBehaviour<EntityAnimatorBehaviour>();
            animatorBehaviour.PlayAnimationOnce(animationName, 0.2f, interrupt => End());
        }
    }
}
