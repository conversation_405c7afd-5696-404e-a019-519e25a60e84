using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class EntityResetActionChanceProcess : ProcessBase
    {
        public int entityUid;

        public override void OnRelease()
        {
            entityUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                entityView.SetMaterial(EEntityMaterialId.Normal);
            }
            EventManager.instance.Broadcast(EventID.Entity_ActionChanceUpdate, entityUid, false);
            End();
        }
    }
}
