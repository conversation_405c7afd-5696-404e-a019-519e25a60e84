using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class EntityExtraActionAnnounceProcess : ProcessBase
    {
        public int entityUid;

        public override void OnRelease()
        {
            entityUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            var entity = BattleShortCut.sampleBattle.GetEntityByUid(entityUid);
            if (entity == null)
            {
                End();
                return;
            }
            var source = entity.GetExtraActionSource();
            bool isFall = true;
            if (source.type == CastSkillSourceType.Buff)
            {
                //EventManager.instance.Broadcast(EventID.BattlePerformance_BuffTriggerAnnounce, entityUid, source.sourceRid);
                EventManager.instance.Broadcast(EventID.BattlePerformance_BuffTagAnnounce, entityUid, BuffAnnounceTag.ExtraAction);
                DelayEnd(BattleParamSetting.instance.buffTriggerWaitTime);
                isFall = false;
            }
            else if (source.type == CastSkillSourceType.Skill)
            {
                //EventManager.instance.Broadcast(EventID.BattlePerformance_SkillTriggerAnnounce, entityUid, source.sourceRid);
                EventManager.instance.Broadcast(EventID.BattlePerformance_BuffTagAnnounce, entityUid, BuffAnnounceTag.ExtraAction);
                DelayEnd(BattleParamSetting.instance.buffTriggerWaitTime);
                isFall = false;
            }
            else if (source.type == CastSkillSourceType.TerrainBuff)
            {
                //EventManager.instance.Broadcast(EventID.BattlePerformance_TerrainTriggerAnnounce, entityUid, source.sourceRid);
                EventManager.instance.Broadcast(EventID.BattlePerformance_BuffTagAnnounce, entityUid, BuffAnnounceTag.ExtraAction);
                DelayEnd(BattleParamSetting.instance.buffTriggerWaitTime);
                isFall = false;
            }
            if (isFall)
            {
                End();
            }

        }
    }
}
