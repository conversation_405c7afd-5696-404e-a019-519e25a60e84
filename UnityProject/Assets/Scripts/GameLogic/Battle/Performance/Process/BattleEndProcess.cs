using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class BattleEndProcess : ProcessBase
    {
        public bool isWin;

        public override void OnRelease()
        {
            isWin = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            //UIManager.instance.Open<BattleResultUI>(false, actionOnOpenEnd: (m) => End());
        }
    }
}
