using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityTeamChangeProcess : ProcessBase
    {
        public List<int> entityUidList = new List<int>();
        public int teamUid;

        public override void OnRelease()
        {
            entityUidList.Clear();
            teamUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            foreach (var entityUid in entityUidList)
            {
                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                if (entityView != null)
                {
                    var team = BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
                    team.AddEntityFrom(entityView, team, false);
                    entityView.SetActionChance(true);
                    BattleShortCut.sampleBattle.GetTeamByUid(teamUid);
                    EventManager.instance.Broadcast(EventID.Entity_Team_Changed, entityUid, teamUid);
                }
            }
            End();
        }
    }
}
