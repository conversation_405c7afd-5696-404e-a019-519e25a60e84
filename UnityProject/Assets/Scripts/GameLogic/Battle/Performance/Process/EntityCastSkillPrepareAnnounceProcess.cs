using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityCastSkillPrepareAnnounceProcess : ProcessBase
    {
        public EntityCastSkillProcedureResult procedureResult;

        private GridEffectPattern gridEffectPattern;

        public override void OnRelease()
        {
            gridEffectPattern = GridEffectPattern.None;
            procedureResult = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(procedureResult.entityUid);
            if (entityView == null)
            {
                End();
                return;
            }
            if (!entityView.CanControlledByHost() || entityView.GetTeam().controlledPlayer.isAuto)
            {
                TryPlayCombatIdleAnimation(procedureResult.entityUid, procedureResult.skillUid, procedureResult.targetEntityUid);
                Skill skill = entityView.GetSkillBySkillUid(procedureResult.skillUid);
                if (skill != null)
                {
                    switch (skill.skillInfo.indicatorType)
                    {
                        case SkillIndicatorType.Helpful:
                            gridEffectPattern = GridEffectPattern.SkillEffectGreen;
                            break;
                        case SkillIndicatorType.Harmful:
                        case SkillIndicatorType.Dir:
                            gridEffectPattern = GridEffectPattern.SkillEffectRed;
                            break;
                        case SkillIndicatorType.Other:
                            gridEffectPattern = GridEffectPattern.SkillEffectBlue;
                            break;
                    }
                    BattleShortCut.battleSceneGridManager.AddGirdEffect(procedureResult.targetPos, gridEffectPattern);
                }
                DelayEnd(BattleParamSetting.instance.castSkillBeginWaitTime);
            }
            else
            {
                End();
            }
        }

        protected override void OnEnd()
        {
            base.OnEnd();
            BattleShortCut.battleSceneGridManager.RemoveGridEffect(procedureResult.targetPos, gridEffectPattern);
        }

        private void TryPlayCombatIdleAnimation(Int32 sourceEntityUid, Int32 sourceSkillUid, Int32 targetEntityUid)
        {
            ActorView sourceEntityView = EntityViewManager.instance.GetEntityView(sourceEntityUid) as ActorView;
            ActorView targetEntityView = EntityViewManager.instance.GetEntityView(targetEntityUid) as ActorView;
            if (sourceEntityView != null && targetEntityView != null)
            {
                Skill skill = sourceEntityView.GetSkillBySkillUid(sourceSkillUid);
                if (skill != null && skill.skillInfo.engageType == SkillEngageType.Combat)
                {
                    EntityAnimatorBehaviour sourceEntityAnimBehaviour = sourceEntityView.GetBehaviour<EntityAnimatorBehaviour>();
                    sourceEntityAnimBehaviour.PlayAnimationOnce("Combat_Announce", 0f, interrupt =>
                    {
                        if (!interrupt)
                        {
                            sourceEntityAnimBehaviour.PlayAnimationLoop(sourceEntityView.animationConfig.combatIdle, 0);
                        }
                    });
                    if (!targetEntityView.BanAnimation())
                    {
                        EntityAnimatorBehaviour targetEntityAnimBehaviour = targetEntityView.GetBehaviour<EntityAnimatorBehaviour>();
                        targetEntityAnimBehaviour.PlayAnimationOnce("Combat_Announce", 0f, interrupt =>
                        {
                            if (!interrupt)
                            {
                                targetEntityAnimBehaviour.PlayAnimationLoop(targetEntityView.animationConfig.combatIdle, 0);
                            }
                        });
                    }
                }
            }
        }
    }
}
