using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CameraFocusProProcess : ProcessBase
    {
        public GridPosition[] m_grids;
        public Boolean m_immediately;

        public override void OnRelease()
        {
            m_grids = null;
            m_immediately = false;
            base.OnRelease();
        }

        public void InitDatas(Boolean immediately, params GridPosition[] grids)
        {
            m_immediately = immediately;
            m_grids = grids;
        }
        public void InitDatas(Boolean immediately, Int32 entityUid, GridPosition targetPosition)
        {
            m_immediately = immediately;
            m_grids = new GridPosition[] { GridPosition.invalid, targetPosition };
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                m_grids[0] = entityView.GetLocatedPosition();
            }
        }
        public void InitDatas(Boolean immediately, params Int32[] entityUids)
        {
            m_immediately = immediately;
            if (entityUids.Length > 0)
            {
                m_grids = new GridPosition[entityUids.Length];
                for(int i = 0; i < entityUids.Length; i++)
                {
                    EntityView entityView = EntityViewManager.instance.GetEntityView(entityUids[i]);
                    if (entityView != null)
                    {
                        m_grids[i] = entityView.GetLocatedPosition();
                    }
                    else
                    {
                        m_grids[i] = GridPosition.invalid;
                    }
                }
            }
        }

        protected override void OnStart()
        {
            BattleHelper.SetCameraPosition(m_immediately, End, m_grids);
        }
    }
}
