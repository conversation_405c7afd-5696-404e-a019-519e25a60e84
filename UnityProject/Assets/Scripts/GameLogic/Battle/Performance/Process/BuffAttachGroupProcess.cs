using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BuffAttachGroupProcess : ProcessBase
    {
        public BuffAttachGroupResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            foreach (var attachResult in result.attachResultList)
            {
                BuffPerformanceUtility.PerformanceBuffAttach(attachResult);
            }
            End();
        }
    }
}
