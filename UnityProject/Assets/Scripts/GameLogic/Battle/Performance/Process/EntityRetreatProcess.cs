using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class EntityRetreatProcess : ProcessBase
    {
        public List<int> entityUidList = new List<int>();

        public override void OnRelease()
        {
            entityUidList.Clear();
            base.OnRelease();
        }

        protected override void OnStart()
        {
            foreach (var entityUid in entityUidList)
            {
                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                if (entityView != null)
                {
                    EntityViewManager.instance.DestroyEntityView(entityUid);
                    BattleShortCut.sampleBattle.DestroyEntityById(entityUid);
                }
            }
            End();
        }
    }
}
