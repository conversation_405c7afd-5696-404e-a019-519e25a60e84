using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class DelayEndProcess : ProcessBase
	{
        public float delayTime;

        public override void OnRelease()
        {
            delayTime = default;
            base.OnRelease();
        }

        public static DelayEndProcess Create(float delayTime)
        {
            DelayEndProcess process = ClassPoolManager.instance.Fetch<DelayEndProcess>();
            process.delayTime = delayTime;
            return process;
        }

        protected override void OnStart()
        {
            DelayEnd(delayTime);
        }
    }
}
