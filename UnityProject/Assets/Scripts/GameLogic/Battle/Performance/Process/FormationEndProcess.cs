using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
	public class FormationEndProcess : ProcessBase
    {
        protected override void OnStart()
        {
            base.OnStart();
            BattleShortCut.sampleBattle.SetStageState_Sync(BattleStageStateId.PrepareEnd);
            EventManager.instance.Broadcast(EventID.BattlePrepare_Finish);
            End();
        }
    }
}
