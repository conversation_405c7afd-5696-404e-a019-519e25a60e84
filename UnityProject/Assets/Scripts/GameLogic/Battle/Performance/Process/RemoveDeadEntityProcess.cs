using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Drama;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class RemoveDeadEntityProcess : ProcessBase
    {
        private List<DramaTimeline> m_timelineList = new List<DramaTimeline>();
        private List<int> m_loadingIdList = new List<int>();

        public override void OnRelease()
        {
            base.OnRelease();
            m_timelineList.ReleaseAll();
            m_loadingIdList.Clear();
        }

        protected override void OnStart()
        {
            List<EntityView> deadEntityViewList = new List<EntityView>();
            foreach(var kv in EntityViewManager.instance.GetEntityViewMap())
            {
                EntityView entityView = kv.Value;
                if (entityView.IsAlive() || entityView.isDeadOver)
                {
                    continue;
                }
                if (entityView.CanResurrect())
                {
                    continue;
                }
                deadEntityViewList.Add(entityView);
            }
            foreach (var entityView in deadEntityViewList)
            {
                if (entityView.entityType == ConfigData.EntityType.Actor)
                {
                    string dramaPath = entityView.GetDramaTimelinePath("DeadRemove");
                    DramaContext context = new DramaContext();
                    int entityUid = entityView.uid;
                    context.originEntityUid = entityUid;
                    context.dramaPath = dramaPath;
                    context.scenePerformance = true;
                    ResourceHandleCollection collection = new ResourceHandleCollection();
                    DramaTimelineUtility.Collect(context, collection);
                    m_loadingIdList.Add(entityUid);
                    collection.StartLoad(() =>
                    {
                        m_loadingIdList.Remove(entityUid);
                        BattleShortCut.sampleBattle.DestroyEntityById(entityUid);
                        DramaTimeline dramaTimeline = DramaTimelineManager.instance.CreateDrama(dramaPath);
                        if (dramaTimeline != null)
                        {
                            dramaTimeline.InitContext(context);
                            dramaTimeline.Start(() =>
                            {
                                entityView.isDeadOver = true;
                                EntityViewManager.instance.DestroyEntityView(entityView.uid);
                            });
                            m_timelineList.Add(dramaTimeline);
                        }
                        else
                        {
                            entityView.isDeadOver = true;
                            EntityViewManager.instance.DestroyEntityView(entityView.uid);
                        }
                    });
                }
                else
                {
                    entityView.isDeadOver = true;
                    EntityViewManager.instance.DestroyEntityView(entityView.uid);
                }
            }
            CheckTickEnd(new TimeSlice());
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            CheckTickEnd(timeSlice);
        }

        private void CheckTickEnd(TimeSlice timeSlice)
        {
            bool isAllOver = true;
            if (m_loadingIdList.Count != 0)
            {
                return;
            }
            foreach (var timeline in m_timelineList)
            {
                timeline.Tick(timeSlice);
                if (timeline.isRunning)
                {
                    isAllOver = false;
                }
            }
            if (isAllOver)
            {
                End();
            }
        }
    }
}
