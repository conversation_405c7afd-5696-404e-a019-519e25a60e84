using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class WaitProcedurePerformanceProcess : BattleActionProcedurePerformanceProcess
    {
        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                var entityUid = (m_result as WaitProcedureResult).entityUid;
                //��ʱ�ȷ�������
                BattleShortCut.sampleBattle.ResetEntityMustAct(entityUid);
                BattleUtility.SetOtherSummonRefMustAct(BattleShortCut.sampleBattle, entityUid, entityUid);
                if ((m_result as WaitProcedureResult).resetExtraMove)
                {
                    BattleShortCut.sampleBattle.GetEntityByUid(entityUid).ResetExtraMoveChance();
                }
                if ((m_result as WaitProcedureResult).resetExtraAction)
                {
                    BattleShortCut.sampleBattle.GetEntityByUid(entityUid).ResetExtraActionChance();
                }
            }));
            base.OnInitProcess(sequenceProcess);
        }
    }
}
