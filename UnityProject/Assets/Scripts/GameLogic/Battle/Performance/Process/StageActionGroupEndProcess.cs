
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class StageActionGroupEndProcess : ProcessBase
    {
        protected override void OnStart()
        {
            base.OnStart();
            BattleUIStateHandler.DeactiveState(BattleUIState.BattleStoryPerform);
            BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.Default);
            End();
        }
    }
}
