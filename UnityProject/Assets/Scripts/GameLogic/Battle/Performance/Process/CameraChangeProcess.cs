using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class CameraChangeProcess : ProcessBase
    {
        public int originEntityUid;
        protected override void OnStart()
        {
            base.OnStart();
            CombatCameraId cameraId = CombatCameraId.None;

            var originEntity = EntityViewManager.instance.GetEntityView(originEntityUid);
            if (originEntity != null)
            {
                if (BattleShortCut.IsHostOrFriendTeam(originEntity.GetTeamUid()))
                {
                    cameraId = CombatCameraId.Left;
                }
                else
                {
                    cameraId = CombatCameraId.Right;
                }
            }

            BattleShortCut.battleScene.sceneCameraHandler.Switch2CombatCamera(cameraId);
            DelayEnd(0f);
        }
    }
}
