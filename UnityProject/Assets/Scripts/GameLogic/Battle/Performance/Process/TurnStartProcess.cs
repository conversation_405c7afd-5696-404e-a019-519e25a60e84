using System;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class TurnStartProcess : ProcessBase
    {
        public TurnStartProcedureResult result;

        public override void OnRelease()
        {
            result = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            BattleShortCut.sampleBattle.SetCurTurnIndex(result.turnIndex);
            BattleShortCut.sampleBattle.ClearCurStepIndex();
            foreach (var item in EntityViewManager.instance.GetEntityViewMap())
            {
                item.Value.SetActionChance(true);
            }
            foreach (var teamEnergyChangeResult in result.teamEnergyChangeResultList)
            {
                var team = BattleShortCut.sampleBattle.GetTeamByUid(teamEnergyChangeResult.teamUid);
                team.sharedEnergy = teamEnergyChangeResult.updateEnergy;
                //��ʱ�ŵ�TeamStartProcess
                //EventManager.instance.Broadcast(EventID.BattleTeam_SharedEnergy_Changed, team.uid, team.sharedEnergy);
            }
            EventManager.instance.Broadcast<Int32>(EventID.BattlePerformance_TurnStart, result.turnIndex);
            End();
        }
    }
}
