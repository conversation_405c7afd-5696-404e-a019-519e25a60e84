using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class EntityPreMoveProcess : ProcessBase
    {
        public int entityUid;

        public override void OnRelease()
        {
            entityUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (entityView != null)
            {
                BattleShortCut.battleSceneGridManager.RemoveGridEffect(entityView.GetLocatedPosition(), GridEffectPattern.Active);
            }
            End();
        }
    }
}
