using System;
using System.Collections;
using System.Collections.Generic;
using System.Xml.Linq;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Drama;


namespace Phoenix.GameLogic.Battle
{
    public class BattleActionProcedurePreloadProcess : ProcessBase
    {
        private BattleActionProcedureResult m_result;
        private BattleEventProcedureInitializer m_initializer = new BattleEventProcedureInitializer();
        private bool m_simplePerformance;

        public static BattleActionProcedurePreloadProcess Create(BattleActionProcedureResult result, bool simplePerformance)
        {
            var process = ClassPoolManager.instance.Fetch<BattleActionProcedurePreloadProcess>();
            process.m_result = result;
            process.m_simplePerformance = simplePerformance;
            return process;
        }

        public override void OnRelease()
        {
            base.OnRelease();
            m_simplePerformance = false;
            m_result = default;
            m_initializer.Reset();
        }

        protected override void OnStart()
        {
            m_initializer.Init(m_result, m_simplePerformance);
            m_initializer.StartAsync(OnLoadEnd);
            base.OnStart();
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            m_initializer.Tick(timeSlice);
        }

        private void OnLoadEnd(Initializer initializer)
        {
            End();
        }

        private class BattleEventProcedureInitializer : Initializer
        {
            private bool m_simplePerformance;
            private BattleActionProcedureResult m_result;
            private List<DramaContext> m_dramaContextList = new List<DramaContext>();
            private ResourceHandleCollection m_dramaTimelineCollection = new ResourceHandleCollection();
            private ResourceHandleCollection m_dramaResourceCollection = new ResourceHandleCollection();

            public void Init(BattleActionProcedureResult result, bool simplePerformance)
            {
                m_result = result;
                m_simplePerformance = simplePerformance;
            }

            protected override void OnReset()
            {
                m_simplePerformance = false;
                m_dramaContextList.Clear();
                m_dramaTimelineCollection.Clear();
                m_dramaResourceCollection.Clear();
            }

            protected override IEnumerator OnProcess()
            {
                CollectDrama();
                foreach(var dramaContext in m_dramaContextList)
                {
                    m_dramaTimelineCollection.ReadyLoad(dramaContext.dramaPath, dramaContext.originEntityUid, AssetType.Asset);
                }
                m_dramaTimelineCollection.StartLoad(null);
                while (!m_dramaTimelineCollection.isLoadEnd)
                {
                    yield return null;
                }
                foreach (var dramaContext in m_dramaContextList)
                {
                    DramaTimelineUtility.Collect(dramaContext, m_dramaResourceCollection);
                }
                m_dramaResourceCollection.StartLoad(null);
                while (!m_dramaResourceCollection.isLoadEnd)
                {
                    yield return null;
                }
            }

            private void CollectDrama()
            {
                for (int i = 0; i < m_result.subResultList.Count; ++i)
                {
                    BattleActionResult result = m_result.subResultList[i];
                    if (result is BuffTriggerResult)
                    {
                        CollectDramaFromBuffTriggerResult(result as BuffTriggerResult);
                    }
                    else if (result is SkillEngageResult)
                    {
                        CollectDramaFromSkilEngageResult(result as SkillEngageResult);
                    }
                }
            }

            private void CollectDramaFromBuffTriggerResult(BuffTriggerResult result)
            {
                BuffInfo buffInfo = BattleShortCut.infoGetter.GetBuffInfo(result.buffRid);
                if (buffInfo != null)
                {
                    var buffEffectInfo = buffInfo.effectList.GetValueSafely(result.effectIndex) as BuffEffectInfo_SkillTrigger;
                    if (buffEffectInfo != null)
                    {
                        DramaContext dramaContext = new DramaContext();
                        EntityView view = EntityViewManager.instance.GetEntityView(result.originEntityUid);
                        dramaContext.dramaPath = view.GetDramaTimelinePath(buffEffectInfo.dramaName);
                        dramaContext.originEntityUid = result.originEntityUid;
                        dramaContext.simplePerformance = m_simplePerformance;
                        dramaContext.scenePerformance = true;
                        dramaContext.InitEffectResultList(result.effectResultList);
                        m_dramaContextList.Add(dramaContext);
                    }
                }
            }

            private void CollectDramaFromSkilEngageResult(SkillEngageResult result)
            {
                var castSkillProcedureResult = m_result as EntityCastSkillProcedureResult;
                var skillInfo = BattleShortCut.sampleBattle.infoGetter.GetSkillInfo(castSkillProcedureResult.skillRid);
                bool simplePerformance = m_simplePerformance || skillInfo.forceUseSimple;
                for (int i = 0; i < result.skillCastResultList.Count; ++i)
                {
                    SkillBaseResult skillBaseResult = result.skillCastResultList[i];
                    CollectDramaFromSkillCastResult(skillBaseResult as SkillCastResult, skillInfo.engageType, simplePerformance);
                }
            }

            private void CollectDramaFromSkillCastResult(SkillCastResult result, SkillEngageType engageType, bool simplePerformance)
            {
                int skillRid = result.skillRid;
                SkillInfo skillInfo = BattleShortCut.infoGetter.GetSkillInfo(skillRid);
                if (skillInfo != null)
                {
                    DramaContext dramaContext = new DramaContext();
                    //todo
                    EntityView view = EntityViewManager.instance.GetEntityView(result.originEntityUid);
                    dramaContext.dramaPath = view.GetDramaTimelinePath(simplePerformance ? skillInfo.dramaNameForSimple : skillInfo.dramaName);
                    if (string.IsNullOrEmpty(dramaContext.dramaPath))
                    {
                        dramaContext.dramaPath = skillInfo.dramaName;
                    }
                    dramaContext.simplePerformance = simplePerformance;
                    dramaContext.scenePerformance = engageType != SkillEngageType.Combat || simplePerformance;
                    dramaContext.engageType = engageType;
                    dramaContext.originEntityUid = result.originEntityUid;
                    dramaContext.targetPosition = result.targetSelectInfoList.GetValueSafely(0).gridPos;
                    dramaContext.InitEffectResultList(result.effectResultList);
                    m_dramaContextList.Add(dramaContext);
                }
            }
        }
    }
}
