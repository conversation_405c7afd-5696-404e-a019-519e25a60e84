using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityCastSkillPreHandleProcess : ProcessBase
    {
        public EntityCastSkillProcedureResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            foreach(var subResult in result.subResultList)
            {
                if (subResult is SkillEngageResult)
                {
                    HandleSkillEngage(subResult as SkillEngageResult);
                }
            }
            End();
        }

        private void HandleSkillEngage(SkillEngageResult result)
        {
            foreach (var skillCastResult in result.skillCastResultList)
            {
                IEntity entity = BattleShortCut.sampleBattle.GetEntityByUid(skillCastResult.originEntityUid);
                if(entity != null)
                {
                    Skill skill = entity.GetSkillBySkillUid(skillCastResult.skillUid);
                    if (skill != null)
                    {
                        skill.coolTimeLeft = skillCastResult.coolTime;
                        Skill exSkill = skill.GetExSkill();
                        if (exSkill != null)
                        {
                            exSkill.coolTimeLeft = skill.coolTimeLeft;
                        }
                        Skill originSkill = skill.GetOriginSkill();
                        if (originSkill != null)
                        {
                            originSkill.coolTimeLeft = skill.coolTimeLeft;
                        }
                    }
                }  
            }
        }
    }
}
