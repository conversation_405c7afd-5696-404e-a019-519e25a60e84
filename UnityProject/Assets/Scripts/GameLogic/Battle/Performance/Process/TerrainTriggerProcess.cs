using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class TerrainTriggerProcess : ProcessPlayerProcess
    {
        private TerrainTriggerResult m_result;
        private bool m_simplePerformance;

        public override void OnRelease()
        {
            m_result = null;
            m_simplePerformance = false;
            base.OnRelease();
        }

        public void Init(TerrainTriggerResult result , bool simplePerformance)
        {
            m_result = result;
            m_simplePerformance = simplePerformance;
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            if (m_result.triggerResult != null)
            {
                TerrainTriggerAnnounceProcess announceProcess = ClassPoolManager.instance.Fetch<TerrainTriggerAnnounceProcess>();
                announceProcess.entityUid = m_result.entityUid;
                announceProcess.terrainRid = m_result.terrainRid;
                announceProcess.isTerrainBuff = m_result.isTerrainBuff;
                sequenceProcess.AddProcess(announceProcess);

                DramaContext dramaContext = new DramaContext();
                EntityView view = EntityViewManager.instance.GetEntityView(m_result.entityUid);
                dramaContext.dramaPath = view.GetDramaTimelinePath(m_result.logicInfo.dramaName);
                dramaContext.originEntityUid = m_result.entityUid;
                dramaContext.scenePerformance = true;
                dramaContext.simplePerformance = m_simplePerformance;
                dramaContext.InitEffectResultList(m_result.triggerResult.effectResultList);
                var dramaProcess = DramaTimelineProcess.Create(dramaContext);
                sequenceProcess.AddProcess(dramaProcess);

                if (m_result.destroy)
                {
                    BattleShortCut.sampleBattle.DestroyEntityById(m_result.terrainBuffUid);
                    sequenceProcess.AddProcess(CallbackProcess.Create(() => EntityViewManager.instance.DestroyEntityView(m_result.terrainBuffUid)));
                }
            }
        }
    }
}
