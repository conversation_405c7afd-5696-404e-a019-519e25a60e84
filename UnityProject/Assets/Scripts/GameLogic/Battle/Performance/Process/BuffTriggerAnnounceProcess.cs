using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BuffTriggerAnnounceProcess : ProcessBase
    {
        public int entityUid;
        public int buffRid;

        public override void OnRelease()
        {
            entityUid = default;
            buffRid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            BuffConfigData buffConfigData = ConfigDataManager.instance.GetBuff(buffRid);
            if (buffConfigData != null && buffConfigData.needAnnounce)
            {
                EventManager.instance.Broadcast(EventID.BattlePerformance_BuffTriggerAnnounce, entityUid, buffRid);
                DelayEnd(BattleParamSetting.instance.buffTriggerWaitTime);
            }
            else
            {
                End();
            }

        }
    }
}
