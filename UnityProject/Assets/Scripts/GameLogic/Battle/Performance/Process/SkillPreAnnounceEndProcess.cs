
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class SkillPreAnnounceEndProcess : ProcessBase
    {
        public EntityActionEndResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            var entityView = EntityViewManager.instance.GetEntityView(result.entityUid);
            entityView.ResetPreAnnounce();
            //EventManager.instance.Broadcast(EventID.BattlePerformance_SkillPreAnnounceEnd, result.entityUid, result.preAnnounceSkillUid);
            BattleShortCut.battleScene.skillPredictHandler.SkillPredictEnd(result.entityUid, result.preAnnounceSkillUid);
            End();
        }
    }
}
