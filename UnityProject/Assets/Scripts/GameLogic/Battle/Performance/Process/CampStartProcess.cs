using System;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CampStartProcess : ProcessBase
    {
        public int campId;

        public override void OnRelease()
        {
            campId = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            BattleUIStateHandler.ActiveState(BattleUIState.BattleStageAnnounce);
            EventManager.instance.Broadcast(EventID.BattlePerformance_CampStart, campId, (Action)End);
        }

        protected override void OnEnd()
        {
            BattleUIStateHandler.DeactiveState(BattleUIState.BattleStageAnnounce);
        }
    }
}
