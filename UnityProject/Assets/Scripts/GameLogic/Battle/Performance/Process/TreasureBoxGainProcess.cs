using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class TreasureBoxGainProcess : ProcessBase
    {
        public BattleTreasureBoxInfo info;

        public override void OnRelease()
        {
            info = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            CommonRewardUI.ShowUI(ItemUtility.GetRewards(info.rewardId), OnRewardUIEnd);
        }

        private void OnRewardUIEnd()
        {
            BattleShortCut.battleScene.RemoveTreasureBox(info.treasureBoxId);
            End();
        }
    }
}
