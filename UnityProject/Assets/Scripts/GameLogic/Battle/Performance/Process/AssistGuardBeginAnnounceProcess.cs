using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class AssistGuardBeginAnnounceProcess : ProcessBase
    {
        public int entityUid;

        public override void OnRelease()
        {
            entityUid = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            EventManager.instance.Broadcast(EventID.BattlePerformance_BuffTagAnnounce, entityUid, BuffAnnounceTag.Guard);
            DelayEnd(BattleParamSetting.instance.buffTriggerWaitTime);

        }
    }
}
