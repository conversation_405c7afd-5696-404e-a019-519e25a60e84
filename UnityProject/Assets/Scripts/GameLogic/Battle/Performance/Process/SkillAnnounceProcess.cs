using System;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class SkillAnnounceProcess : ProcessBase
    {
        public int entityUid;
        public int skillUid;
        public int skillRid;
        public bool simplePerformance;

        public override void OnRelease()
        {
            entityUid = default;
            skillUid = default;
            skillRid = default;
            simplePerformance = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            SkillConfigData skillConfigData = ConfigDataManager.instance.GetSkill(skillRid);
            bool needWaitEnd = false;
            if (skillConfigData != null)
            {
                if (skillConfigData.announceType != SkillAnnounceType.None)
                {
                    if (skillConfigData.engageType == SkillEngageType.Scene)
                    {
                        EventManager.instance.Broadcast<Int32, Int32, Action>(EventID.BattlePerformance_SceneSkillAnnounce, entityUid, skillRid, End);
                        needWaitEnd = true;
                    }
                    else if (skillConfigData.engageType == SkillEngageType.Combat)
                    {
                        EventManager.instance.Broadcast<Int32, Int32, Action>(EventID.BattlePerformance_CombatSkillAnnounce, entityUid, skillUid, End);
                        needWaitEnd = true;
                    }
                }
                var entity = BattleShortCut.sampleBattle.GetEntityByUid(entityUid);
                if (entity != null)
                {
                    var skill = entity.GetSkillBySkillUid(skillUid);
                    if (skill != null)
                    {
                        if (skill.slotId == SkillSlotId.ContinuousAttack)
                        {
                            EventManager.instance.Broadcast<Int32, Int32, bool>(EventID.BattlePerformance_ContinuousSkillAnnounce, entityUid, skillUid, simplePerformance);
                        }
                        else if (skill.slotId == SkillSlotId.AdditionalAttack)
                        {
                            EventManager.instance.Broadcast<Int32, Int32, bool>(EventID.BattlePerformance_AdditionalSkillAnnounce, entityUid, skillUid, simplePerformance);
                        }
                    }
                }
            }
            if (!needWaitEnd)
            {
                End();
            }
        }
    }
}
