using System;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class TextFloatProcess : ProcessBase
    {
        public int entityUid;
        public string text;

        private int m_timerId;

        public override void OnRelease()
        {
            TimerManager.instance.Stop(m_timerId);
            entityUid = default;
            m_timerId = default;
            text = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            TipUI.ShowTip(String.Format("TextFloatProcess {0}: {1} ", entityUid, text));
            End();
        }
    }
}
