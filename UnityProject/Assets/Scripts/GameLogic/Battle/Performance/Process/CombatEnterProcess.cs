
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

using Phoenix.GameLogic.UI;
using Phoenix.Drama;
using System;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class CombatEnterProcess : ProcessBase
    {
        public int originEntityUid;
        public int firstSkillUid;
        public int targetEntityUid;
        public bool forceUseSimple;

        public override void OnRelease()
        {
            originEntityUid = default;
            targetEntityUid = default;
            forceUseSimple = false;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            base.OnStart();
            if (forceUseSimple)
            {
                DelayEnd(BattleParamSetting.instance.combatEnterWaitTime2);
                TryShowCombatUI(originEntityUid, firstSkillUid, targetEntityUid);
            }
            else
            {
                BattleShortCut.InBattleCombatStage = true;
                BattleShortCut.battleScene.ToggleScene(SceneType.Combat);

                EntityView originEntityView = EntityViewManager.instance.GetEntityView(originEntityUid);
                EntityView targetEntityView = EntityViewManager.instance.GetEntityView(targetEntityUid);
                UpdateEntityState(originEntityView, targetEntityView);
                UpdateCombatCamera(originEntityView, targetEntityView);
                TryShowCombatUI(originEntityUid, firstSkillUid, targetEntityUid);

                BattleHelper.UpdateEntityBloodMask(true, BloodMask.CombatPerformance, originEntityUid, targetEntityUid);
                DelayEnd(BattleParamSetting.instance.combatEnterWaitTime);
                EventManager.instance.Broadcast(EventID.BattlePerformance_CombatBegin);
            }
        }

        private void UpdateEntityState(EntityView originEntityView, EntityView targetEntityView)
        {
            targetEntityView.SetMaterial(EEntityMaterialId.Normal);

            Skill skill = originEntityView.GetSkillBySkillUid(firstSkillUid);
            string dramaPath = originEntityView.GetDramaTimelinePath(skill.skillInfo.dramaName);
            float expectDistance = DramaTimelineUtility.GetAdjustDistance(dramaPath);
            //Vector3 toTargetVector = targetEntityView.transform.position - originEntityView.transform.position;
            //targetEntityView.SetPosition(originEntityView.GetPosition() + toTargetVector.normalized * expectDistance);

            Vector3 attackPosition = Vector3.zero;
            Vector3 attackDirection = Vector3.left;
            if (BattleShortCut.battleScene.combatSceneConfig != null)
            {
                if (BattleShortCut.IsHostOrFriendTeam(originEntityView.GetTeamUid()))
                {
                    attackPosition = BattleShortCut.battleScene.combatSceneConfig.m_attackPosition;
                    attackDirection = BattleShortCut.battleScene.combatSceneConfig.m_attackDirection;
                }
                else
                {
                    attackPosition = BattleShortCut.battleScene.combatSceneConfig.m_enemyAttackPosition;
                    attackDirection = BattleShortCut.battleScene.combatSceneConfig.m_enemyAttackDirection;
                }
            }
            originEntityView.SetWorldPosition(attackPosition);
            originEntityView.SetDirectionImmediately(attackDirection.normalized);
            targetEntityView.SetWorldPosition(attackPosition + attackDirection.normalized * expectDistance);
            targetEntityView.SetDirectionImmediately(-attackDirection.normalized);

            HideOtherEntityView();
        }

        private void HideOtherEntityView()
        {
            var entityViewMap = EntityViewManager.instance.GetEntityViewMap();
            foreach (var kv in entityViewMap)
            {
                if (kv.Key == originEntityUid || kv.Key == targetEntityUid)
                {
                    continue;
                }
                kv.Value.SetActiveSafely(false);
            }
        }

        private void UpdateCombatCamera(EntityView originEntity, EntityView targetEntity)
        {
            if (BattleShortCut.IsHostOrFriendTeam(originEntity.GetTeamUid()))
            {
                BattleShortCut.battleScene.sceneCameraHandler.GenerateBattleCameraRandomIndex();
                BattleShortCut.battleScene.sceneCameraHandler.InitCombatCamera(CombatCameraId.Left, originEntity, targetEntity);
                BattleShortCut.battleScene.sceneCameraHandler.InitCombatCamera(CombatCameraId.Right, targetEntity, originEntity);
                BattleShortCut.battleScene.sceneCameraHandler.InitCombatTopCamera(CombatCameraId.Left, originEntity, targetEntity);
            }
            else
            {
                BattleShortCut.battleScene.sceneCameraHandler.GenerateBattleCameraRandomIndex();
                BattleShortCut.battleScene.sceneCameraHandler.InitCombatCamera(CombatCameraId.Right, originEntity, targetEntity);
                BattleShortCut.battleScene.sceneCameraHandler.InitCombatCamera(CombatCameraId.Left, targetEntity, originEntity);
                BattleShortCut.battleScene.sceneCameraHandler.InitCombatTopCamera(CombatCameraId.Right, originEntity, targetEntity);
            }
            BattleShortCut.battleScene.sceneCameraHandler.Switch2CombatTopCamera();
        }

        protected void TryShowCombatUI(Int32 sourceEntityUid, Int32 skillUid, Int32 targetEntityUid)
        {
            EntityView sourceEntityView = EntityViewManager.instance.GetEntityView(sourceEntityUid);
            EntityView targetEntityView = EntityViewManager.instance.GetEntityView(targetEntityUid);
            if (sourceEntityView != null && targetEntityView != null)
            {
                Skill skill = sourceEntityView.GetSkillBySkillUid(skillUid);
                if (skill != null && skill.engageType == SkillEngageType.Combat)
                {
                    BattleCombatUI.ShowCombatUI(sourceEntityUid, skillUid, targetEntityView.uid, forceUseSimple);
                }
            }
        }
    }
}
