using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class EntityTurnDirProcess : ProcessBase
    {
        public List<int> entityUidList = new List<int>();
        public GridDirType dirType;
        public float time;
        public bool waitEnd;

        private EntityView m_entityView;
        //private StaticLerpQuaternion m_lerp = new StaticLerpQuaternion();

        private List<EntityView> m_waitEntityList = new List<EntityView>();

        public override void OnRelease()
        {
            entityUidList.Clear();
            m_waitEntityList.Clear();
            dirType = default;
            time = default;
            waitEnd = default;
            m_entityView = null;
            //m_lerp.Reset();
            base.OnRelease();
        }

        protected override void OnInit()
        {
            base.OnInit();
            //m_lerp.actionOnFinish = OnLerpFinish;
        }

        protected override void OnStart()
        {
            foreach (var entityUid in entityUidList)
            {
                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                if (entityView == null)
                {
                    continue;
                }
                if (dirType == GridDirType.None)
                {
                    continue;
                }
                if (waitEnd)
                {
                    m_waitEntityList.AddNotContains(entityView);
                }
                else
                {
                    entityView.SetDirection(dirType, time);
                }
            }
            if (m_waitEntityList.Count > 0)
            {
                foreach (var entityView in m_waitEntityList.ToArray())
                {
                    entityView.SetDirection(dirType, time, () => { OnTurnEnd(entityView); });
                }
            }
            else
            {
                End();
            }

            //var startRotation = Quaternion.LookRotation(m_entityView.transform.forward, Vector3.up);
            //var tarWorldPos = BattleShortCut.battleSceneGridManager.GetGridWorldPosition(targetPos);
            //var vecToTarget = tarWorldPos - m_entityView.GetWorldPosition();
            //vecToTarget.y = 0;
            //var forward = vecToTarget.normalized;
            //var targetRotation = Quaternion.LookRotation(forward, Vector3.up);
            //m_lerp.Start(startRotation, targetRotation, time, EBaseLerpFuncType.Linear);
            //if (!waitEnd)
            //{
            //    End();
            //}
        }

        private void OnTurnEnd(EntityView entityView)
        {
            if (m_waitEntityList.Remove(entityView) && m_waitEntityList.Count == 0)
            {
                End();
            }
        }

        //protected override void OnTick(TimeSlice timeSlice)
        //{
        //    base.OnTick(timeSlice);
        //    if (m_lerp.isStart)
        //    {
        //        m_lerp.Tick(timeSlice.deltaTime);
        //        if (m_entityView != null)
        //        {
        //            m_entityView.SetWorldRotation(m_lerp.curValue);
        //        }
        //    }
        //}

        //private void OnLerpFinish(AbstractStaticLerp<Quaternion> lerp)
        //{
        //    if (waitEnd)
        //    {
        //        End();
        //    }
        //}
    }
}
