using System;
using TMPro;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class EntityCastSkillEndProcess : ProcessBase
    {
        public int sourceEntityUid;
        public int skillUid;
        public GridPosition targetPosition;
        public int targetEntityUid;

        public override void OnRelease()
        {
            sourceEntityUid = default;
            skillUid = default;
            targetPosition = default;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            BattleUIStateHandler.DeactiveState(BattleUIState.SkillPerform);

            if (AutoBattleOpMode.Instance.IsProcessCommandSelect)
            {
                BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.SelectCommand, null);
            }
            else
            {
                BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.Default, null);
            }


            End();
        }
    }
}
