using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CrteateEntityViewProcess : ProcessBase
    {
        public Entity entity;

        public override void OnRelease()
        {
            entity = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            EntityViewManager.instance.CreateAndAddEntityView(entity);
            End();
        }
    }
}
