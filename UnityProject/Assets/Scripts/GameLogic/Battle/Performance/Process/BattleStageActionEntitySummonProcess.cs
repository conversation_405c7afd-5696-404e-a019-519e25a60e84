using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleStageActionEntitySummonProcess : ProcessPlayerProcess
    {
        public BattleStageActionEntitySummonResult result;

        public override void OnRelease()
        {
            result = null;
            base.OnRelease();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            var parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();
            foreach (var partResult in result.partResultList)
            {
                IBattle battle = BattleShortCut.sampleBattle;
                var entity = partResult.entity.Copy(battle);
                battle.AddEntity(entity);
                BattleTeam team = battle.GetTeamByUid(entity.GetTeamUid());
                if (team != null)
                {
                    team.AddEntity(entity, false);
                }

                var innerSequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();
                parallelProcess.AddProcess(innerSequenceProcess);
                var createViewProcess = ClassPoolManager.instance.Fetch<CrteateEntityViewProcess>();
                createViewProcess.entity = entity;
                innerSequenceProcess.AddProcess(createViewProcess);
                var summonProcess = ClassPoolManager.instance.Fetch<SummonEntityProcess>();
                summonProcess.entityUid = entity.uid;
                summonProcess.dramaName = partResult.info.dramaName;
                innerSequenceProcess.AddProcess(summonProcess);
                var buffAttachProcess = ClassPoolManager.instance.Fetch<BuffAttachPerformanceProcess>();
                buffAttachProcess.resultList = partResult.buffAttachResultList;
                innerSequenceProcess.AddProcess(buffAttachProcess);

            }
            sequenceProcess.AddProcess(parallelProcess);
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                BuffPerformanceUtility.PerformanceChangeActiveList(result.buffChangeActiveResultList);
            }));
        }
    }
}
