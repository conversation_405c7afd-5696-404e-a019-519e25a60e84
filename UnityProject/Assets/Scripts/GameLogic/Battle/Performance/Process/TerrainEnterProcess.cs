using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.Drama;

namespace Phoenix.GameLogic.Battle
{
    public class TerrainEnterProcess : ProcessPlayerProcess
    {
        public TerrainEnterResult result;

        public override void OnRelease()
        {
            result.Release();
            result = null;
            base.OnRelease();
        }

        protected override void OnInitProcess(SequenceProcess sequenceProcess)
        {
            if (result.buffAttachResultList.Count > 0)
            {
                foreach (var attachResult in result.buffAttachResultList)
                {
                    BuffPerformanceUtility.PerformanceBuffAttach(attachResult);
                }
            }
        }
    }
}
