using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public static class BattlePerformanceUtility
    {
        public static int forceNextActionActorId = 0;

        public static EntityView GetFocusEntityView()
        {
            EntityView entityView = null;
            if (forceNextActionActorId > 0)
            {
                entityView = EntityViewManager.instance.GetEntityView(forceNextActionActorId);
                forceNextActionActorId = 0;
            }
            if (entityView != null)
            {
                return entityView;
            }
            BattleTeam team = BattleShortCut.sampleBattle.GetTeamByIndex(BattleShortCut.sampleBattle.GetCurTeamIndex());
            if (team.controlledPlayer == null || team.controlledPlayer.playerId != BattleShortCut.hostPlayerId)
            {
                return null;
            }
            var entityList = ClassPoolManager.instance.Fetch<List<IEntity>>();
            IEntity tempEntity = null;
            while (true)
            {
                tempEntity = BattleUtility.GetNextEntityNeedAct(BattleShortCut.sampleBattle, tempEntity);
                if (tempEntity == null)
                {
                    break;
                }
                if (entityList.Contains(tempEntity))
                {
                    break;
                }
                entityList.Add(tempEntity);
            }
            IEntity fitEntity = null;
            float distance = 0;
            var centerPos = new Vector2(Screen.width, Screen.height) / 2f;
            foreach (var entity in entityList)
            {
                if (!entity.HasActionChance())
                {
                    continue;
                }
                if (entity.BanAction())
                {
                    continue;
                }
                var checkEntityView = EntityViewManager.instance.GetEntityView(entity.uid);
                if (checkEntityView == null)
                {
                    continue;
                }
                var screenPos = Camera.main.WorldToScreenPoint(checkEntityView.transform.position);
                var dist = Vector2.Distance(screenPos, centerPos);
                if (fitEntity == null)
                {
                    fitEntity = entity;
                    distance = dist;
                }
                else if (dist < distance)
                {
                    distance = dist;
                    fitEntity = entity;
                }
            }
            if (fitEntity != null)
            {
                entityView = EntityViewManager.instance.GetEntityView(fitEntity.uid);
            }
            ClassPoolManager.instance.Release(entityList);
            return entityView;
        }

        public static void RefreshBgm()
        {
            if (BattleShortCut.sampleBattle != null && BattleShortCut.sampleBattle.stageManageComponent != null)
            {
                var bgmPath = BattleShortCut.sampleBattle.stageManageComponent.bgmPath;
                if (string.IsNullOrEmpty(bgmPath))
                {
                    bgmPath = BattleParamSetting.instance.defaultBgmPath;
                }
                AudioManager.instance.PlayLoop(bgmPath, volume: 0.5f);
            }
        }
    }
}
