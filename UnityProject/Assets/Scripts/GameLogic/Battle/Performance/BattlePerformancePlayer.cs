using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    /// <summary>
    /// ս�����ִ�����
    /// </summary>
    public class BattlePerformancePlayer
    {
        private ProcessPlayer m_processPlayer = new ProcessPlayer();
        private Dictionary<int, PerformanceAnalyzer> m_analyzerMap = new Dictionary<int, PerformanceAnalyzer>();
        public Action actionOnPerformanceEnd;

        private BattlePerformance m_curPerformance;

        public bool isStart
        {
            get { return m_processPlayer.isStart; }
        }

        public bool isPerformanceEnd
        {
            get { return m_curPerformance == null; }
        }

        public void Init()
        {
            m_analyzerMap.Add((int)BattlePerformanceType.StageEnter, new StageEnterPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.StageExit, new StageExitPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.StageActionGroup, new StageActionGroupPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.FormationStart, new FormationStartPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.FormationEnd, new FormationEndPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.MainBattleStart, new MainBattleStartPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.MainBattleEnd, new MainBattleEndPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.EntityWait, new EntityWaitPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.EntityCastSkill, new EntityCastSkillPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.EntityResetActionChance, new EntityResetActionChancePerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.TurnStart, new TurnStartPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.TurnEnd, new TurnEndPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.TeamStart, new TeamStartPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.StepStart, new StepStartPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.BattleEnd, new BattleEndPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.Dialog, new DialogPerformanceAnalyzer());
            m_analyzerMap.Add((int)BattlePerformanceType.Simple, new SimplePerformanceAnalyzer());
        }

        public void StartPerformance(BattlePerformance performance)
        {
            m_curPerformance = performance;
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();
            sequenceProcess.AddProcess(AnalyzePerformance(m_curPerformance));
            sequenceProcess.AddProcess(CallbackProcess.Create(() => { OnProcessPlayedEnd(performance); }));
            m_processPlayer.Start(sequenceProcess);
        }

        //��ע��������������дPerformance�����Ļص���ֱ�����ϲ�tick��ʱ���ж���������process�������˾͵���performance������
        //public void StartProcess(ProcessBase process)
        //{
        //    m_processPlayer.Start(process);
        //}

        public void Pause()
        {
            m_processPlayer.Pause();
        }

        public void Resume()
        {
            m_processPlayer.Resume();
        }

        private void OnProcessPlayedEnd(BattlePerformance performance)
        {
            performance.Release();
            m_curPerformance = null;
            actionOnPerformanceEnd.Invoke();
        }

        public void Clear()
        {
            if(m_curPerformance != null)
            {
                m_curPerformance.Release();
            }
            m_curPerformance = null;
            m_processPlayer.Clear();
        }

        private ProcessBase AnalyzePerformance(BattlePerformance performance)
        {
            PerformanceAnalyzer analyzer = null;
            m_analyzerMap.TryGetValue((int)performance.performanceType, out analyzer);
            return analyzer.Analyze(performance);
        }

        public void Tick(TimeSlice timeSlice)
        {
            m_processPlayer.Tick(timeSlice);
        }
    }
}
