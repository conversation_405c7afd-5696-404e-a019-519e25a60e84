using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public abstract class PerformanceAnalyzer<T> : PerformanceAnalyzer where T : BattlePerformance
    {
        public abstract ProcessBase Analyze(T performance);

        public sealed override ProcessBase Analyze(BattlePerformance performance)
        {
            return Analyze(performance as T);
        }
    }

    public abstract class PerformanceAnalyzer
    {
        public abstract ProcessBase Analyze(BattlePerformance performance);
    }
}
