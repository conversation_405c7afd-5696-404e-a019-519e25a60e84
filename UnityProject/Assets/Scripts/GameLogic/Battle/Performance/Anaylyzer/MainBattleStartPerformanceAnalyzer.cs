using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class MainBattleStartPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceMainBattleStart>
    {
        public override ProcessBase Analyze(BattlePerformanceMainBattleStart performance)
        {
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();
            MainBattleStartProcess process = ClassPoolManager.instance.Fetch<MainBattleStartProcess>();
            process.result = performance.procedureResult;
            sequenceProcess.AddProcess(process);

            BattleActionProcedurePerformanceProcess procedureProcess = ClassPoolManager.instance.Fetch<BattleActionProcedurePerformanceProcess>();
            procedureProcess.Init(performance.procedureResult, BattleShortCut.ForceSimplePerformance);
            sequenceProcess.AddProcess(procedureProcess);

            return sequenceProcess;
        }
    }
}
