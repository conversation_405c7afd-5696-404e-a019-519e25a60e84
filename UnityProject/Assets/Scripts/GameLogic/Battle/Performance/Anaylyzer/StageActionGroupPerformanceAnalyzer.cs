using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class StageActionGroupPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceStageActionGroup>
    {
        public override ProcessBase Analyze(BattlePerformanceStageActionGroup performance)
        {
            StageActionGroupProcess process = ClassPoolManager.instance.Fetch<StageActionGroupProcess>();
            process.actionInfoList = performance.actionInfoList;
            process.resultList = performance.resultList;
            return process;
        }
    }
}
