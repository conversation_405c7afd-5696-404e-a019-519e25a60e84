using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class BattleEndPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceBattleEnd>
    {
        public override ProcessBase Analyze(BattlePerformanceBattleEnd performance)
        {
            BattleEndProcess process = ClassPoolManager.instance.Fetch<BattleEndProcess>();
            process.isWin = performance.isWin;
            return process;
        }
    }
}
