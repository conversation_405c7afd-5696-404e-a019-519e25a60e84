using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TurnStartPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceTurnStart>
    {
        public override ProcessBase Analyze(BattlePerformanceTurnStart performance)
        {
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();

            CollectEntityResetActionChanceAllProcess(sequenceProcess);
            CollectTurnStartProcess(sequenceProcess, performance.procedureResult);
            foreach (var blockHpChangeResult in performance.procedureResult.blockHpChangeResultList)
            {
                var cameraFocusProcess = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
                cameraFocusProcess.entityUid = blockHpChangeResult.entityUid;
                cameraFocusProcess.m_immediately = false;
                sequenceProcess.AddProcess(cameraFocusProcess);
                var blockHpChangeProcess = ClassPoolManager.instance.Fetch<EntityBlockHpChangeProcess>();
                blockHpChangeProcess.result = blockHpChangeResult;
                sequenceProcess.AddProcess(blockHpChangeProcess);
            }

            BattleActionProcedurePerformanceProcess procedureProcess = ClassPoolManager.instance.Fetch<BattleActionProcedurePerformanceProcess>();
            procedureProcess.Init(performance.procedureResult, BattleShortCut.ForceSimplePerformance);
            sequenceProcess.AddProcess(procedureProcess);
            return sequenceProcess;
        }

        private void CollectEntityResetActionChanceAllProcess(SequenceProcess sequenceProcess)
        {
            EntityResetActionChanceAllProcess process = ClassPoolManager.instance.Fetch<EntityResetActionChanceAllProcess>();
            sequenceProcess.AddProcess(process);
        }

        private void CollectTurnStartProcess(SequenceProcess sequenceProcess, TurnStartProcedureResult result)
        {
            TurnStartProcess process = ClassPoolManager.instance.Fetch<TurnStartProcess>();
            process.result = result;
            sequenceProcess.AddProcess(process);
        }
    }
}
