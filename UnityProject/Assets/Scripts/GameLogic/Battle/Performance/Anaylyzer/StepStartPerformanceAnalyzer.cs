using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class StepStartPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceStepStart>
	{
        public override ProcessBase Analyze(BattlePerformanceStepStart performance)
        {
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();
            sequenceProcess.AddProcess(CallbackProcess.Create(() =>
            {
                BattleShortCut.sampleBattle.SetCurStageIndex(performance.stepIndexUpdate);
            }));
            FocusEntityProcess focusEntity = ClassPoolManager.instance.Fetch<FocusEntityProcess>();
            //focusEntity.entityUid = performance.entityUid;
            sequenceProcess.AddProcess(focusEntity);
            return sequenceProcess;
        }
    }
}
