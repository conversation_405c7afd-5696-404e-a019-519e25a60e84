using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityCastSkillPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceEntityCastSkill>
    {
        public override ProcessBase Analyze(BattlePerformanceEntityCastSkill performance)
        {
            BattleRecordManager.instance.SaveRunningBattleRecord();
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();

            EntityCastSkillPreHandleProcess preHandleProcess = ClassPoolManager.instance.Fetch<EntityCastSkillPreHandleProcess>();
            preHandleProcess.result = performance.procedureResult;
            sequenceProcess.AddProcess(preHandleProcess);

            InitCameraFocusProcess(sequenceProcess, performance.entityUid);

            EntityPreMoveProcess preMoveProcess = ClassPoolManager.instance.Fetch<EntityPreMoveProcess>();
            preMoveProcess.entityUid = performance.entityUid;
            sequenceProcess.AddProcess(preMoveProcess);

            EntityMoveProcess moveProcess = ClassPoolManager.instance.Fetch<EntityMoveProcess>();
            moveProcess.entityUid = performance.entityUid;
            moveProcess.gridPos = performance.procedureResult.movePos;
            sequenceProcess.AddProcess(moveProcess);

            EntityCastSkillProcess castSkillProcess = ClassPoolManager.instance.Fetch<EntityCastSkillProcess>();
            castSkillProcess.entityUid = performance.entityUid;
            castSkillProcess.procedureResult = performance.procedureResult;
            sequenceProcess.AddProcess(castSkillProcess);
            return sequenceProcess;
        }

        private void InitCameraFocusProcess(SequenceProcess sequenceProcess, Int32 entityUid)
        {
            EntityView sourceEntityView = EntityViewManager.instance.GetEntityView(entityUid);
            if (sourceEntityView != null)
            {
                BattleTeam battleTeam = sourceEntityView.GetTeam();
                if (battleTeam != null)
                {
                    if (battleTeam.controlledPlayer.isAuto || battleTeam.uid != BattleShortCut.hostTeamUid)
                    {
                        CameraFocusProcess process = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
                        process.entityUid = entityUid;
                        sequenceProcess.AddProcess(process);
                    }
                }
            }
        }
    }
}
