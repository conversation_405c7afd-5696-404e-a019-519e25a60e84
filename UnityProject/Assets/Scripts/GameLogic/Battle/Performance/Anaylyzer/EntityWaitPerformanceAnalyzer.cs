using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class EntityWaitPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceEntityWait>
    {
        public override ProcessBase Analyze(BattlePerformanceEntityWait performance)
        {
            BattleRecordManager.instance.SaveRunningBattleRecord();
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();

            EntityWaitStartProcess waitStartProcess = ClassPoolManager.instance.Fetch<EntityWaitStartProcess>();
            waitStartProcess.entityUid = performance.entityUid;
            sequenceProcess.AddProcess(waitStartProcess);

            CameraFocusProcess process = ClassPoolManager.instance.Fetch<CameraFocusProcess>();
            process.entityUid = performance.entityUid;
            sequenceProcess.AddProcess(process);

            EntityPreMoveProcess preMoveProcess = ClassPoolManager.instance.Fetch<EntityPreMoveProcess>();
            preMoveProcess.entityUid = performance.entityUid;
            sequenceProcess.AddProcess(preMoveProcess);

            EntityMoveProcess moveProcess = ClassPoolManager.instance.Fetch<EntityMoveProcess>();
            moveProcess.entityUid = performance.entityUid;
            moveProcess.gridPos = performance.procedureResult.movePos;
            moveProcess.needCameraFollow = true;
            sequenceProcess.AddProcess(moveProcess);

            EntityWaitProcess waitProcess = ClassPoolManager.instance.Fetch<EntityWaitProcess>();
            waitProcess.entityUid = performance.entityUid;
            waitProcess.procedureResult = performance.procedureResult;
            sequenceProcess.AddProcess(waitProcess);
            return sequenceProcess;
        }
    }
}
