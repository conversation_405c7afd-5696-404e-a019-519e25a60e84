using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class SimplePerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceSimple>
    {
        public override ProcessBase Analyze(BattlePerformanceSimple performance)
        {
            ProcessBase process = null;
            switch (performance.simplePerformanceType)
            {
                case SimplePerformanceType.StageActionGroupStart:
                    StageActionGroupProcess process1 = ClassPoolManager.instance.Fetch<StageActionGroupProcess>();
                    //process1.actionInfoList = (performance as BattlePerformanceSimple<BattleStageActionGroupInfo, List<BattleActionResult>>).param0;
                    //process1.resultList = (performance as BattlePerformanceSimple<BattleStageActionGroupInfo, List<BattleActionResult>>).param1;
                    process = process1;
                    break;
                case SimplePerformanceType.Dialog:

                    break;
            }
            return process;
        }
    }
}
