using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class StageExitPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceStageExit>
    {
        public override ProcessBase Analyze(BattlePerformanceStageExit performance)
        {
            StageExitProcess process = ClassPoolManager.instance.Fetch<StageExitProcess>();
            process.hasNextStage = performance.hasNextStage;
            return process;
        }
    }
}
