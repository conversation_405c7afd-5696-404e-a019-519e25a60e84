using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TeamStartPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceTeamStart>
    {
        public override ProcessBase Analyze(BattlePerformanceTeamStart performance)
        {
            BattleTeam activeBattleTeam = BattleShortCut.sampleBattle.GetTeamByIndex(performance.teamIndex);
            if (activeBattleTeam == null)
            {
                return null;
            }

            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();
            CollectEntityResetActionChanceAllProcess(sequenceProcess);
            BattleTeam preActiveBattleTeam = null;
            if (performance.teamIndex > 0)
            {
                preActiveBattleTeam = BattleShortCut.sampleBattle.GetTeamByIndex(performance.teamIndex - 1);
            }
            if (preActiveBattleTeam == null || preActiveBattleTeam.campId != activeBattleTeam.campId)
            {
                CampStartProcess campStartProcess = ClassPoolManager.instance.Fetch<CampStartProcess>();
                campStartProcess.campId = activeBattleTeam.campId;
                sequenceProcess.AddProcess(campStartProcess);
            }
            TeamStartProcess teamStartProcess = ClassPoolManager.instance.Fetch<TeamStartProcess>();
            teamStartProcess.teamIndex = performance.teamIndex;
            sequenceProcess.AddProcess(teamStartProcess);

            TeamSharedEnergyUpdateProcess teamSharedEnergyUpdateProcess = ClassPoolManager.instance.Fetch<TeamSharedEnergyUpdateProcess>();
            teamSharedEnergyUpdateProcess.teamIndex = performance.teamIndex;
            sequenceProcess.AddProcess(teamSharedEnergyUpdateProcess);

            return sequenceProcess;
        }
        private void CollectEntityResetActionChanceAllProcess(SequenceProcess sequenceProcess)
        {
            if (BattleShortCut.logicBattle.GetCurStageInfo().roundRobinType == BattleRoundRobinType.TurnOver)
            {
                EntityResetActionChanceAllProcess process = ClassPoolManager.instance.Fetch<EntityResetActionChanceAllProcess>();
                sequenceProcess.AddProcess(process);
            }
        }

    }
}
