using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class DialogPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceDialog>
    {
        public override ProcessBase Analyze(BattlePerformanceDialog performance)
        {
            DialogProcess process = ClassPoolManager.instance.Fetch<DialogProcess>();
            process.dialogInfo = performance.dialogInfo;
            return process;
        }
    }
}
