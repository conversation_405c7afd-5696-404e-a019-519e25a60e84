using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class EntityResetActionChancePerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceEntityResetActionChance>
	{
        public override ProcessBase Analyze(BattlePerformanceEntityResetActionChance performance)
        {
            ParallelProcess parallelProcess = ClassPoolManager.instance.Fetch<ParallelProcess>();
            for (int i = 0; i < performance.entityUidList.Count; ++i)
            {
                int entityUid = performance.entityUidList[i];
                EntityResetActionChanceProcess process = ClassPoolManager.instance.Fetch<EntityResetActionChanceProcess>();
                process.entityUid = entityUid;
                parallelProcess.AddProcess(process);
            }
            return parallelProcess;
        }
    }
}
