using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class FormationEndPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceFormationEnd>
    {
        public override ProcessBase Analyze(BattlePerformanceFormationEnd performance)
        {
            FormationEndProcess process = ClassPoolManager.instance.Fetch<FormationEndProcess>();
            return process;
        }
    }
}
