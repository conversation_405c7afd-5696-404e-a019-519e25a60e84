using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class StageEnterPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceStageEnter>
	{
        public override ProcessBase Analyze(BattlePerformanceStageEnter performance)
        {
            SequenceProcess sequenceProcess = ClassPoolManager.instance.Fetch<SequenceProcess>();

            StageEnterProcess process = ClassPoolManager.instance.Fetch<StageEnterProcess>();
            process.stageIndex = performance.stageIndex;
            sequenceProcess.AddProcess(process);
            return sequenceProcess;
        }
    }
}
