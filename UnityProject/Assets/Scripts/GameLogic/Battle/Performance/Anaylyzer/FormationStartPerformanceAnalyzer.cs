using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
	public class FormationStartPerformanceAnalyzer : PerformanceAnalyzer<BattlePerformanceFormationStart>
	{
        public override ProcessBase Analyze(BattlePerformanceFormationStart performance)
        {
            FormationStartProcess process = ClassPoolManager.instance.Fetch<FormationStartProcess>();
            return process;
        }
    }
}
