using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public static class EntityPerformanceUtility
    {
        public static void PerformanceDialogBubble(int groupId, int entityUid, Action actionOnEnd)
        {
            var bubbleGroup = ConfigDataManager.instance.GetDialogBubbleGroup(groupId);
            if (bubbleGroup == null || bubbleGroup.RandomList.Count == 0)
            {
                actionOnEnd.InvokeSafely();
                return;
            }
            int sum = 0;
            foreach (var random in bubbleGroup.RandomList)
            {
                sum += random.Rate;
            }
            sum = Math.Max(sum, 100);
            var randomResult = UnityEngine.Random.Range(0, sum);
            DialogBubbleRandomConfigData fitRandom = null;
            foreach (var random in bubbleGroup.RandomList)
            {
                if (randomResult < random.Rate)
                {
                    fitRandom = random;
                    break;
                }
                randomResult -= random.Rate;
            }
            if (fitRandom == null)
            {
                actionOnEnd.InvokeSafely();
                return;
            }
            EventManager.instance.Broadcast(EventID.Entity_DialogBubbleUpdate, entityUid, fitRandom.Id);
            actionOnEnd.InvokeSafely();
        }
    }
}
