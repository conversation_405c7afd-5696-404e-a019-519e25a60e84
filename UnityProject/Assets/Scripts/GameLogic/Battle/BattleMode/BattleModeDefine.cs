using System;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{

    public enum BattleOpModeId : byte
    {
        None,
        Default,
        PrepareOpMode,
        BattleOpMode,
        AutoBattleOpMode,
        RetractOpMode,
        ReplayOpMode,
    }
    public enum BattleOpModeState : byte
    {
        Default,
        SelectEntity,
        SkillPerformance,
        StoryPerformance,
        SelectCommand,
    }

    public struct InputData
    {
        public static InputData Default = default;

        public InputType m_inputType;

        /// <summary> 触摸点Id </summary>
        public Int32 m_pointerId;

        /// <summary> 触摸点位置 </summary>
        public Vector3 m_position;

        /// <summary> 相对上一次Update，position变化量 </summary>
        public Vector3 m_deltaPosition;

        /// <summary> delta Value </summary>
        public Single m_deltaValue;

        /// <summary> 按键 </summary>
        public KeyCode m_keycode;

        /// <summary> 对象 </summary>
        public GameObject m_pointerClick;

        public BattleUIEventID m_battleUIEventId;
        public Int32 m_paramter1;
        public Int32 m_paramter2;
    }

    public enum InputType : byte
    {
        None,
        Touch,
        Keyboard,
        Joystick,
    }

    public enum InputLockType : byte
    {
        None,
        UserGuide,
    }

    public enum BattleUIEventID : byte
    {
        None,

        FormationOperation,
        FormationUIEntityClick,
        FormationStartButton,


        WaitButton,
        ConfirmButton,
        CancelButton,
        SelectSkill,
        NormalAttack,
        FocusNextButton,
        DangerRangeButton,
        TurnFinishBtnClick,
        RecommendSkillOnBtn,
        RecommendSkillOffBtn,
        RecommendCastSkillBtn,
        ShowEntityDetailInfoBtn,


        RetractSeriesOperation,
        Max
    }

    public struct MoveGridPosition
    {
        public GridPosition m_grid;
        public Boolean m_stayFlag;
        public MoveGridPosition(GridPosition target, Boolean stayFlag)
        {
            m_grid = target;
            m_stayFlag = stayFlag;
        }

        public override String ToString()
        {
            return string.Format("{0} {1}", m_grid, m_stayFlag);
        }
    }
    public struct SkillGridPosition
    {
        public GridPosition m_grid;
        public Boolean m_selectFlag;
        public SkillIndicatorType m_indicatorType;
        public SkillGridPosition(GridPosition target, Boolean selectFlag)
        {
            m_grid = target;
            m_selectFlag = selectFlag;
            m_indicatorType = SkillIndicatorType.Other;
        }
        public SkillGridPosition(GridPosition target, Boolean selectFlag, SkillIndicatorType skillIndicatorType)
        {
            m_grid = target;
            m_selectFlag = selectFlag;
            m_indicatorType = skillIndicatorType;
        }

        public void SetSelectFlag(Boolean flag)
        {
            this.m_selectFlag = flag;
        }
        public override String ToString()
        {
            return string.Format("{0} {1} {2}", m_grid, m_selectFlag, m_indicatorType);
        }
    }
}
