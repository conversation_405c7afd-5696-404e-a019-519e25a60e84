

using SRF;
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class GhostEntity
    {
        private Int32 m_entityUid;

        private Entity m_entity;
        private ActorView m_actorView;
        private GameObject m_go;

        private Dictionary<Renderer, List<Material>> matDict = new Dictionary<Renderer, List<Material>>();
        private int m_initLayer;
        private List<Material> tempMatList = new List<Material>();

        public GameObject Go { get { return m_go; } }
        public ActorView ActorView {  get { return m_actorView; } }
        public Int32 Uid => m_entityUid;
        public Boolean Alive { get; private set; }

        public void Init(Int32 entityUid, GameObject go)
        {
            m_entityUid = entityUid;
            m_go = go;
            InitGhostActorView();
            InitMaterial();
            Alive = true;
        }

        public void UnInit()
        {
            if (Alive == false)
            {
                return;
            }

            Alive = false;
            m_actorView.gameObject.SetLayerRecursive(m_initLayer);
            UnInitGhostActorView();
            UnInitMaterial();
            m_go = null;
        }


        public void SetPosition(GridPosition position)
        {
            if (Alive == false)
            {
                return;
            }

            m_actorView.SetPosition(position, false);
        }

        public void SetDirection(GridPosition targetPos)
        {
            if (Alive == false)
            {
                return;
            }

            m_actorView.SetDirection(targetPos);
        }

        private void InitGhostActorView()
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(m_entityUid);
            if (entityView != null)
            {
                m_entity = entityView.owner.Copy(BattleShortCut.sampleBattle);
            }
            m_actorView = m_go.AddComponent<ActorView>();
            // m_go.SetLayerRecursive(LayerMask.NameToLayer("Phantom"));
            m_actorView.Create();
            m_actorView.SetOwner(m_entity);
            m_actorView.Init();
            m_initLayer = m_actorView.gameObject.layer;
            m_actorView.gameObject.SetLayerRecursive((int)EUnityLayerType.Phantom);
            EntityPlayIdleBehaviour idleBehaviour = m_actorView.GetBehaviour<EntityPlayIdleBehaviour>();
            if (idleBehaviour != null)
            {
                idleBehaviour.PlaySceneIdle(false);
            }
        }

        private void UnInitGhostActorView()
        {
            // m_go.SetLayerRecursive(LayerMask.NameToLayer("Default"));
            if (m_actorView != null)
            {
                m_actorView.UnInit();
                UnityEngine.Object.DestroyImmediate(m_actorView);
                m_actorView = null;
            }
            if (m_entity != null)
            {
                m_entity.Release();
                m_entity = null;
            }
        }

        static int msBaseColorID = Shader.PropertyToID("_BaseColor");

        private void InitMaterial()
        {
            var shader = ResourceHandleManager.GetResourceEasy<Shader>(@"Assets/Res/Shader/Character_SDFOPT/URP_Toon_SDFOPT_Alpha.shader");
            //Material transparentMat = ResourceHandleManager.GetResourceEasy<Material>(CommonPrefabPathSetting.instance.transparentMaterial.path);
            matDict.Clear();
            Renderer[] meshRenders = m_go.GetComponentsInChildren<Renderer>();
            foreach (Renderer render in meshRenders)
            {
                Type renderType = render.GetType();
                if (renderType != typeof(MeshRenderer) && renderType != typeof(SkinnedMeshRenderer))
                {
                    continue;
                }

                if (!matDict.ContainsKey(render))
                {
                    matDict[render] = new List<Material>();
                }
                else
                {
                    matDict[render].Clear();
                }
                tempMatList.Clear();
                for (Int32 i = 0; i < render.materials.Length; i++)
                {
                    var preMat = render.materials[i];
                    var newMat = new Material(shader);
                    newMat.CopyMatchingPropertiesFromMaterial(preMat);
                    Color col = newMat.GetColor(msBaseColorID);
                    newMat.SetColor(msBaseColorID, new Color(col.r, col.g, col.b, 0.25f));
                    newMat.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
                    tempMatList.Add(newMat);
                    matDict[render].Add(preMat);
                }
                render.SetMaterials(new List<Material>());
                if (tempMatList.Count > 0)
                {
                    render.SetMaterials(tempMatList);
                }
                foreach (var material in tempMatList)
                {
                    material.SetFloat("_SurfaceType", 1);
                    material.SetFloat("_Cull", 2);
                    material.SetFloat("_AlphaClip", 0f);
                    material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                    material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                    // General Transparent Material Settings
                    material.SetOverrideTag("RenderType", "Transparent");
                    material.SetShaderPassEnabled("ShadowCaster", false);
                    material.SetFloat("_RenderQueue", (int)UnityEngine.Rendering.RenderQueue.Transparent);
                    material.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
                    material.DisableKeyword("_ALPHATEST_ON");
                    material.SetOverrideTag("RenderType", "Opaque");
                    material.doubleSidedGI = false;
                    //outline
                    material.SetColor("_OutlineColor", new Color(0.5f, 0.5f, 0.5f, 0.5f));
                    material.SetFloat("_OutlineWidth", 2.0f);
                    material.EnableKeyword("_USESMOOTHNORMAL");
                }
            }
        }

        private void UnInitMaterial()
        {
            foreach(var item in matDict)
            {
                item.Key.SetMaterials(item.Value);
            }
            matDict.Clear();
        }
    }
}
