

using System;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class SkillGhostingPreviewController
    {
        private Boolean m_useGhostEntity = true;

        private GameObject m_sourceGo;
        private GameObject m_targetGo;

        private GhostEntity m_GhostEntity;


        public Int32 EntityUid { get; private set; }
        public Boolean Alive { get; private set; }

        public void Init()
        {
            Alive = false;
        }

        public void UnInit()
        {
            Hide();
        }

        public void Show(Int32 entityUid, GridPosition standGrid, GridPosition targetPosition)
        {
            Hide();
            Alive = true;
            EntityUid = entityUid;
            m_sourceGo = SpawnGameObjectInternal(entityUid, standGrid, String.Empty);
            if (m_sourceGo != null)
            {
                SetPositionAndDirection(m_sourceGo, standGrid, targetPosition);
                EventManager.instance.Broadcast<Boolean, ActorView>(EventID.EntityGhostViewAddOrRemove, true, m_GhostEntity.ActorView);
            }
            //m_targetGo = SpawnGameObjectInternal(0, targetPosition, CommonPrefabPathSetting.instance.skillPreviewTargetGo.path);
            //SetPositionAndDirection(m_targetGo, targetPosition, GridPosition.invalid);
        }

        public void Hide()
        {
            if (Alive)
            {
                Alive = false;
                EventManager.instance.Broadcast<Boolean, ActorView>(EventID.EntityGhostViewAddOrRemove, false, null);
                DespawnGameObjectInternal(m_sourceGo);
                m_sourceGo = null;
                //DespawnGameObjectInternal(m_targetGo);
                //m_targetGo = null;
            }
        }

        private void SetPositionAndDirection(GameObject go, GridPosition standGrid, GridPosition targetPosition)
        {
            if (go == null)
            {
                return;
            }
            if (standGrid.isValid)
            {
                Vector3 standWorldPosition = BattleShortCut.GetGridWorldPosition(standGrid);
                go.transform.position = standWorldPosition;
                if (targetPosition.isValid)
                {
                    Vector3 tragetWorldPosition = BattleShortCut.GetGridWorldPosition(targetPosition);
                    Vector3 forward = tragetWorldPosition - standWorldPosition;
                    forward.y = 0f;
                    go.transform.forward = forward.normalized;
                }
            }
        }


        private GameObject SpawnGameObjectInternal(Int32 entityUid, GridPosition standGrid, String path)
        {
            GameObject go = null;
            if (m_useGhostEntity)
            {
                EntityView entityView = EntityViewManager.instance.GetEntityView(entityUid);
                if (entityView != null)
                {
                    if (standGrid == entityView.GetPinnedPosition())
                    {
                        return null;
                    }

                    ActorConfigData actorConfig = ConfigDataManager.instance.GetActor(entityView.rid);
                    if (actorConfig != null)
                    {
                        EntitySkinConfigData skinConfig = ConfigDataManager.instance.GetEntitySkin(actorConfig.SkinId);
                        if (skinConfig != null)
                        {
                            go = ResourceHandleManager.instance.SpawnGameObject(skinConfig.PrefabPath);
                            m_GhostEntity = new GhostEntity();
                            m_GhostEntity.Init(entityUid, go);
                        }
                    }
                }
            }
            if (go == null)
            {
                go = ResourceHandleManager.instance.SpawnGameObject(path);
            }
            return go;
        }

        private void DespawnGameObjectInternal(GameObject go)
        {
            if (go == null)
            {
                return;
            }
            if (m_useGhostEntity && m_GhostEntity != null)
            {
                m_GhostEntity.UnInit();
                m_GhostEntity = null;
            }
            ResourceHandleManager.instance.DespawnGameObject(go);
        }

    }
}
