
using System;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class ModeStateContext : ClassPoolObj
    {
        public Int32 originEntityUid;
        public Int32 skillUid;
        public Int32 targetEntityUid;
        public GridPosition targetGridPosition;
        public Boolean m_refreshView;
        public Boolean m_selectAnnounce;
        public TeamDecisionMarkId m_teamDecisionMarkId;

        public override void OnRelease()
        {
            originEntityUid = 0;
            targetEntityUid = 0;
            targetGridPosition = GridPosition.invalid;
            skillUid = 0;
            m_teamDecisionMarkId = TeamDecisionMarkId.None;
        }

        public static ModeStateContext Fetch(Boolean refreshView)
        {
            ModeStateContext context = ClassPoolManager.instance.Fetch<ModeStateContext>();
            context.m_refreshView = refreshView;
            return context;
        }
        public static ModeStateContext Fetch(Int32 targetEntityUid, GridPosition targetGridPosition)
        {
            ModeStateContext context = ClassPoolManager.instance.Fetch<ModeStateContext>();
            context.m_refreshView = true;
            context.targetEntityUid = targetEntityUid;
            context.targetGridPosition = targetGridPosition;
            return context;
        }


        public static ModeStateContext Fetch(Int32 originEntityUid, Boolean refreshView, Boolean selectAnnounce)
        {
            ModeStateContext context = ClassPoolManager.instance.Fetch<ModeStateContext>();
            context.originEntityUid = originEntityUid;
            context.m_refreshView = refreshView;
            context.m_selectAnnounce = selectAnnounce;
            return context;
        }

        public static ModeStateContext Fetch(Int32 originEntityUid, Int32 skillUid, Int32 targetEntityUid)
        {
            ModeStateContext context = ClassPoolManager.instance.Fetch<ModeStateContext>();
            context.originEntityUid = originEntityUid;
            context.skillUid = skillUid;
            context.targetEntityUid = targetEntityUid;
            context.m_refreshView = false;
            return context;
        }

        public static ModeStateContext Fetch(Int32 originEntityUid, Int32 skillUid, GridPosition gridPosition)
        {
            ModeStateContext context = ClassPoolManager.instance.Fetch<ModeStateContext>();
            context.originEntityUid = originEntityUid;
            context.skillUid = skillUid;
            context.targetGridPosition = gridPosition;
            context.m_refreshView = false;
            return context;
        }

        public static void Release(ModeStateContext context)
        {
            context?.Release();
        }
    }
}
