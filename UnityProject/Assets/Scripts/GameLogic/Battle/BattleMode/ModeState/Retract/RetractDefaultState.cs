
using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class RetractDefaultState : ModeStateBase
    {
        protected RetractOpMode m_retractMode;
        private EntityView m_selectEntity;

        List<MoveGridPosition> m_moveRange = new List<MoveGridPosition>();
        List<SkillGridPosition> m_attackRange = new List<SkillGridPosition>();

        public RetractDefaultState(OpModeBase owner) : base(owner) { }

        public override void OnInit()
        {
            m_retractMode = m_owner as RetractOpMode;
            base.OnInit();
            BattleUIStateHandler.ActiveState(BattleUIState.RetractMode);
        }

        public override void OnUnInit()
        {
            base.OnUnInit();
            m_retractMode = null;
            m_moveRange.Clear();
            m_attackRange.Clear();
            BattleUIStateHandler.DeactiveState(BattleUIState.RetractMode);
        }

        protected override void OnUpdateStatePerformance()
        {
            BattleModeHelper.UpdateEntityViewActiveState();
            BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
            EventManager.instance.Broadcast(EventID.BattleMode_EntityCancelSelected);
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.DangerRange);
        }

        protected override void OnEntitySelected(EntityView entity, GridPosition gridPosition)
        {
            if (entity != null && entity != m_selectEntity)
            {
                m_selectEntity = entity;
                m_retractMode.MoveRule4Preview.SetEntity(entity);
                entity.TryGetMoveRange(ref m_moveRange, m_retractMode.MoveRule4Preview);
                entity.TryGetNormalSkillRange(m_moveRange, ref m_attackRange);
                BattleShortCut.battleSceneGridManager.ShowNormalSkillMoveRange(entity.GetLocatedPosition(), m_moveRange, m_attackRange);
                BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(true);
                BattleModeHelper.ShowOccupyMultiGridEntityRange();
            }
        }

        protected override void OnGridSelected(GridPosition gridPosition)
        {
            m_selectEntity = null;
            m_moveRange.Clear();
            m_attackRange.Clear();
            BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
        }

        public override Boolean BattleUIClick(BattleUIEventID eventId, params int[] argvs)
        {
            Boolean result = false;
            if (eventId == BattleUIEventID.RetractSeriesOperation)
            {
                BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
                BattleModeHelper.UpdateEntityViewActiveState();
                result = true;
            }
            return result;
        }

    }
}
