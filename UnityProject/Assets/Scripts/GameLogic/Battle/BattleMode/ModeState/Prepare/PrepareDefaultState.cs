
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class PrepareDefaultState : PrepareModeStateBase
    {
        private EntityView m_dragSelectEntity;
        private EntityView m_clickSelectEntity;
        private GridPosition m_currentSelectGrid;

        List<MoveGridPosition> m_moveRange = new List<MoveGridPosition>();
        List<SkillGridPosition> m_attackRange = new List<SkillGridPosition>();

        public PrepareDefaultState(OpModeBase owner) : base(owner) { }

        
        protected override void OnEntitySelected(EntityView entity, GridPosition gridPosition)
        {
            if (entity != null)
            {
                m_prepareMode.MoveRule4Preview.SetEntity(entity);
                entity.TryGetMoveRange(ref m_moveRange, m_prepareMode.MoveRule4Preview);
                entity.TryGetNormalSkillRange(m_moveRange, ref m_attackRange);
                BattleShortCut.battleSceneGridManager.ShowNormalSkillMoveRange(entity.GetLocatedPosition(), m_moveRange, m_attackRange);
                EventManager.instance.Broadcast(EventID.PrepareMode_EntitySelected, entity.uid);
                EventManager.instance.Broadcast(EventID.PrepareMode_EntityGearSelected, gridPosition);
                BattleModeHelper.UpdateEntityAttackRangeSignRelationship(true, entity);
                BattleModeHelper.ShowOccupyMultiGridEntityRange();
            }
            if (m_clickSelectEntity != entity)
            {
                m_clickSelectEntity = entity;
                BattleModeHelper.ShowEntityOccupyGrid(entity, 1, 1);
            }
        }

        protected override void OnGridSelected(GridPosition gridPosition)
        {
            m_clickSelectEntity = null;
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
            EventManager.instance.Broadcast(EventID.PrepareMode_EntityGearSelected, gridPosition);
            BattleModeHelper.UpdateEntityAttackRangeSignRelationship(false);
        }

        public override void InputBegan(InputData eventData)
        {
            if (eventData.m_inputType == InputType.Touch && eventData.m_battleUIEventId == BattleUIEventID.FormationOperation)
            {
                EventManager.instance.Broadcast<Int32, Vector2>(EventID.BattlePrepare_DragBegin, eventData.m_paramter1, eventData.m_position);
            }
            else
            {
                GridPosition grid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
                EntityView selectEntity = EntityViewManager.instance.GetFirstControllableEntityViewOnGrid(grid);
                if (selectEntity != null && m_prepareMode.IsFormationGrid(selectEntity.GetTeamUid(), selectEntity.GetLocatedPosition()))
                {
                    m_dragSelectEntity = selectEntity;
                    EventManager.instance.Broadcast<Int32, Vector2>(EventID.BattlePrepare_DragBegin, m_dragSelectEntity.rid, eventData.m_position);
                }
                else
                {

                    base.InputBegan(eventData);
                }
            }
        }
        public override void InputMoved(InputData eventData)
        {
            GridPosition tempSelectGrid = GridPosition.invalid;
            if (eventData.m_inputType == InputType.Touch && eventData.m_battleUIEventId == BattleUIEventID.FormationOperation)
            {
                tempSelectGrid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
                EventManager.instance.Broadcast<Int32, Vector2>(EventID.BattlePrepare_Draging, eventData.m_paramter1, eventData.m_position);
            }
            else if (m_dragSelectEntity != null)
            {
                tempSelectGrid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
                EventManager.instance.Broadcast<Int32, Vector2>(EventID.BattlePrepare_Draging, m_dragSelectEntity.rid, eventData.m_position);
            }
            else
            {
                base.InputMoved(eventData);
            }
            if (m_currentSelectGrid != tempSelectGrid)
            {
                m_currentSelectGrid = tempSelectGrid;
                BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(true, m_currentSelectGrid);
            }
        }
        public override void InputEnded(InputData eventData)
        {
            if (eventData.m_inputType == InputType.Touch && eventData.m_battleUIEventId == BattleUIEventID.FormationOperation)
            {
                EventManager.instance.Broadcast<Int32>(EventID.BattlePrepare_DragEnd, eventData.m_paramter1);
                GridPosition grid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
                EntityView gridEntityView = EntityViewManager.instance.GetFirstControllableEntityViewOnGrid(grid);
                if (gridEntityView != null && gridEntityView.dataGetter.IsDestiny())
                {
                    TipUI.ShowTip(ConstStringUtility.GetConstString(ConfigData.ConstStringId.PrepareDestinyCharacterRetreatTip));
                }
                else if (m_prepareMode.TryGetFormationTeamId(grid, out Int32 teamId))
                {
                    SendFormationSetup(eventData.m_paramter1, grid, teamId);
                }
            }
            else if (m_dragSelectEntity != null)
            {
                EventManager.instance.Broadcast<Int32>(EventID.BattlePrepare_DragEnd, m_dragSelectEntity.rid);

                GridPosition grid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
                if (m_prepareMode.TryGetFormationTeamId(grid, out Int32 teamId))
                {
                    if (teamId == m_dragSelectEntity.GetTeamUid())
                    {
                        SendFormationExchange(m_dragSelectEntity, grid);
                    }
                }
                else
                {
                    SendFormationRetreat(m_dragSelectEntity);
                }
                m_dragSelectEntity = null;
                m_currentSelectGrid = GridPosition.invalid;
            }
            else
            {
                base.InputEnded(eventData);
            }
        }
        public override void ZoomInOut(InputData eventData)
        {
            base.ZoomInOut(eventData);
        }

        public override Boolean BattleUIClick(BattleUIEventID eventId, params int[] argvs)
        {
            Boolean result = false;
            switch (eventId)
            {
                case BattleUIEventID.FormationStartButton:
                    //先去除布阵界面加载战术ai 暂时没有使用功能不影响效果
                    // SendTeamDecisonData();
                    SendFormtionEnd();
                    result = true;
                    break;
                case BattleUIEventID.FormationUIEntityClick:
                    EventManager.instance.Broadcast(EventID.PrepareMode_UI_EntitySelected, argvs[0]);
                    result = true;
                    break;
            }
            return result;
        }


        

        #region Private


        private Int32 GetCurrentFormationedActorCount()
        {
            Int32 count = 0;
            List<BattleStageDispositionInfo> dispositions = BattleShortCut.sampleBattle.GetCurStageDispositionInfoList();
            if (dispositions != null && dispositions.Count > 0)
            {
                Int32 hostTeamUid = BattleShortCut.hostTeamUid;
                foreach (BattleStageDispositionInfo info in dispositions)
                {
                    if (info.teamId != hostTeamUid)
                    {
                        continue;
                    }
                    List<IEntity> entityList = BattleShortCut.sampleBattle.GetEntityListByFieldSummary(info.position);
                    foreach (var entity in entityList)
                    {
                        if (entity.entityType == EntityType.Actor)
                        {
                            count++;
                            break;
                        }
                    }
                }
            }
            return count;
        }



        private void SendFormationSetup(Int32 entityRid, GridPosition newPosition, Int32 teamId)
        {
            Int32 currentFormationActorCount = GetCurrentFormationedActorCount();
            Int32 maxFormationActorCount = BattleShortCut.sampleBattle.GetCurStageDispositionMaxCount();
            EntityView selectEntity = EntityViewManager.instance.GetFirstControllableEntityViewOnGrid(newPosition);
            if (selectEntity == null && currentFormationActorCount >= maxFormationActorCount)
            {
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConstStringId.PrepareEntityNumGreaterMaximum));
                return;
            }
            FrameCommandSendUtility.SendFormationSetup(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, teamId, entityRid, newPosition.x, newPosition.y);
        }

        private void SendFormationRetreat(EntityView entityView)
        {
            if (entityView.dataGetter.IsDestiny())
            {
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConstStringId.PrepareDestinyCharacterRetreatTip));
                return;
            }

            Int32 entityUid = entityView.uid;
            FrameCommandSendUtility.SendFormationRetreat(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, entityUid);
        }

        private void SendFormationExchange(EntityView entityView, GridPosition newPosition)
        {
            GridPosition originGrid = entityView.GetLocatedPosition();
            FrameCommandSendUtility.SendFormationExchange(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, originGrid.x, originGrid.y, newPosition.x, newPosition.y);
        }

        private void SendFormtionEnd()
        {
            Int32 currentActorCount = 0;
            Int32 maxActorCount = BattleShortCut.sampleBattle.GetCurStageDispositionMaxCount();
            List<BattleStageDispositionInfo> dispositions = BattleShortCut.sampleBattle.GetCurStageDispositionInfoList();
            if (dispositions != null && dispositions.Count > 0)
            {
                foreach (BattleStageDispositionInfo info in dispositions)
                {
                    Int32 teamId = info.teamId;
                    GridPosition position = info.position;
                    if (BattleShortCut.IsHostPlayerTeam(teamId))
                    {
                        List<IEntity> entityList = BattleShortCut.sampleBattle.GetEntityListByFieldSummary(position);
                        foreach (var entity in entityList)
                        {
                            if (entity.entityType == EntityType.Actor)
                            {
                                currentActorCount++;
                                break;
                            }
                        }
                    }
                }
            }
            if (currentActorCount == 0)
            {
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConstStringId.PrepareEntityNumEqualZero));
            }
            else
            {
                if (currentActorCount < maxActorCount)
                {
                    //TipUI.ShowTip(ConstStringUtility.GetConstString(ConfigData.ConstStringId.PrepareEntityNumEqualZero));
                }
                var player = BattleShortCut.logicBattle.GetPlayerByPlayerId(BattleShortCut.hostPlayerId);
                FrameCommandSendUtility.SendFormationEnd(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, (byte)player.slotId);
            }
        }

        private void SendTeamDecisonData()
        {
            var decisionDatas = GamePlayerContext.instance.DecisionModule.GetLoadDecisionDatas();

            var builder = new ByteBufferBuilder();
            builder.WriteByte((byte)decisionDatas.Count);
            foreach(var decisionData in decisionDatas)
            {   
                var teamDecisionData = new BattleTeamDecisionData();
                teamDecisionData.m_decisionId = decisionData.m_decisionId;
                teamDecisionData.m_decisionName = decisionData.m_decisionName;
                
                foreach(var orderData in decisionData.m_orders)
                {
                    BattleTeamDecisionItemData itemData = new BattleTeamDecisionItemData();
                    itemData.m_entityElementId = orderData.m_entityElementId;
                    itemData.m_entityCareerId = orderData.m_entityCareerId;
                    itemData.m_entityRespType = orderData.m_entityRespType;
                    itemData.m_actorRid = orderData.m_actorRid;
                    itemData.m_skillSelectType = orderData.m_skillSelectType;
                    itemData.m_skillRid = orderData.m_skillRid;
                    itemData.m_skillTags = orderData.m_skillTags;
                    itemData.m_condition1 = orderData.m_condition1;
                    itemData.m_condition2 = orderData.m_condition2;
                    itemData.m_locateSelect = orderData.m_locateSelect;
                    teamDecisionData.m_items.Add(itemData);
                }
                teamDecisionData.SerializableToBytes(builder);
            }

            FrameCommandSendUtility.SendSetupTeamDcisionDatas(BattleShortCut.logicBattle, 
                                                                            BattleShortCut.hostPlayerId, 
                                                                            BattleShortCut.hostTeamUid, builder.ToArray().ToList());
        }

        #endregion

    }
}
