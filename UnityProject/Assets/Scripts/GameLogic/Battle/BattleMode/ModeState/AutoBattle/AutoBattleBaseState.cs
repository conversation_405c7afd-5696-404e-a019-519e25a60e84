using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class AutoBattleBaseState : ModeStateBase
    {
        protected AutoBattleOpMode m_opMode;

        public AutoBattleBaseState(OpModeBase owner) : base(owner) { }

        public override void OnInit()
        {
            m_opMode = m_owner as AutoBattleOpMode;
            base.OnInit();
        }

        public override void OnUnInit()
        {
            base.OnUnInit();
            m_opMode = null;
        }

        protected void ChangeState2SelectDeactiveEntity(Int32 entityUid, Boolean refreshView)
        {
            Debug.Log($"EventOnBattleUIClick =====1-4===== {entityUid} ");
            //m_owner.ChangeStateInternal<AutoBattleSelectEntityState>(ModeStateContext.Fetch(entityUid, refreshView, false));

            ModeStateContext context = ModeStateContext.Fetch(entityUid, refreshView, false);
            BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.SelectEntity, context);
        }

    }
}
