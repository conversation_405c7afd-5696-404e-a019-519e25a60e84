using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.Battle
{
    public class SelectCommandState : AutoBattleBaseState
    {
        private TeamDecisionMarkId m_teamDecisionMarkId = TeamDecisionMarkId.None;
        public SelectCommandState(OpModeBase owner) : base(owner) { }

        bool m_isSelected = false;

        public override void OnUnInit()
        {
            base.OnUnInit();
            if (m_teamDecisionMarkId == TeamDecisionMarkId.Move)
            {
                bool isContaionCommandMove = AutoBattleOpMode.Instance.IsContainTeamDecisionMark(m_teamDecisionMarkId);
                if (!isContaionCommandMove)
                {
                    BattleShortCut.battleSceneGridManager.ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
                }
            }
        }

        protected override void OnUpdateStateContext(ModeStateContext context)
        {
            if (context != null && context.m_teamDecisionMarkId != TeamDecisionMarkId.None)
            {
                m_teamDecisionMarkId = context.m_teamDecisionMarkId;
            }
        }

        protected override void OnUpdateStatePerformance()
        {
            AutoBattleOpMode.Instance.IsProcessCommandMove = m_teamDecisionMarkId == TeamDecisionMarkId.Move;
            EventManager.instance.Broadcast(EventID.Entity_SelectCommand_InOrOut, true, m_teamDecisionMarkId);
            EventManager.instance.Broadcast(EventID.BattleMode_EntityCancelSelected);
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
            BattleModeHelper.UpdateEntityViewActiveState();

            if (m_teamDecisionMarkId == TeamDecisionMarkId.None)
            {
                return;
            }

            var entityMap = EntityViewManager.instance.GetEntityViewMap();
            foreach (var item in entityMap)
            {
                bool isAddEffect = false;
                EntityView entityView = item.Value;
                if (entityView.entityType == EntityType.Actor)
                {
                    if (m_teamDecisionMarkId == TeamDecisionMarkId.Focus || m_teamDecisionMarkId == TeamDecisionMarkId.Control)
                    {
                        isAddEffect = !BattleShortCut.IsHostOrFriendTeam(entityView.GetTeamUid());
                    }
                    else if (m_teamDecisionMarkId == TeamDecisionMarkId.Protect)
                    {
                        isAddEffect = BattleShortCut.IsHostOrFriendTeam(entityView.GetTeamUid());
                    }
                    if (isAddEffect)
                    {
                        BattleShortCut.battleSceneGridManager.AddGirdEffect(entityView.GetLocatedPosition(), GridEffectPattern.SkillEffectYellow);
                    }
                }
            }
        }

        protected override void OnGridSelected(GridPosition gridPosition)
        {
            if (!BattleShortCut.logicBattle.CheckPosValid(gridPosition))
            {
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConstStringId.AiselectTips4));
                return;
            }

            if (m_teamDecisionMarkId == TeamDecisionMarkId.Move)
            {
                BattleHelper.SetCameraPosition(false, gridPosition);
                BattleShortCut.battleSceneGridManager.ShowOrHideSelectCommandGrid(true, gridPosition);
                //BattleShortCut.battleSceneGridManager.AddGirdEffect(gridPosition, GridEffectPattern.SkillEffectBlue);
                EventManager.instance.Broadcast(EventID.Entity_SelectCommand_SelectGrid, gridPosition, m_teamDecisionMarkId);
                EventManager.instance.Broadcast(EventID.Entity_SelectCommand_UpdateConfirmView, m_teamDecisionMarkId);
                AutoBattleOpMode.Instance.IsInputMode = false;
                m_isSelected = true;
            }
        }

        protected override void OnEntitySelected(EntityView entity, GridPosition gridPosition)
        {
            if (m_teamDecisionMarkId == TeamDecisionMarkId.Move)
            {
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConstStringId.AiselectTips4));
            }

            m_isSelected = false;
            if (m_teamDecisionMarkId == TeamDecisionMarkId.Focus || m_teamDecisionMarkId == TeamDecisionMarkId.Control)
            {
                m_isSelected = !BattleShortCut.IsHostOrFriendTeam(entity.GetTeamUid());
            }
            else if (m_teamDecisionMarkId == TeamDecisionMarkId.Protect)
            {
                m_isSelected = BattleShortCut.IsHostOrFriendTeam(entity.GetTeamUid());
            }

            if (m_isSelected)
            {
                BattleHelper.SetCameraPosition(false, gridPosition);
                EventManager.instance.Broadcast(EventID.Entity_SelectCommand_SelectEntity, entity.uid, m_teamDecisionMarkId);
                EventManager.instance.Broadcast(EventID.Entity_SelectCommand_UpdateConfirmView, m_teamDecisionMarkId);
                AutoBattleOpMode.Instance.IsInputMode = false;
            }
        }

        public override Boolean BattleUIClick(BattleUIEventID eventId, params int[] argvs)
        {
            Debug.Log($"EventOnBattleUIClick =====1-3===== {eventId} ");
            Boolean result = false;
            switch (eventId)
            {
                case BattleUIEventID.ShowEntityDetailInfoBtn:
                    Int32 entityUid = argvs.GetValueSafely(0, 0);
                    ChangeState2SelectDeactiveEntity(entityUid, true);
                    result = true;
                    break;
            }
            return result;
        }

        //protected override void OnGearEntitysSelected(GridPosition gridPosition)
        //{
        //    if (AutoBattleOpMode.Instance.IsProcessCommandMove)
        //    {
        //        BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(false, gridPosition);
        //    }

        //}

        public override void InputBegan(InputData eventData)
        {
            if (!AutoBattleOpMode.Instance.IsInputMode)
            {
                return;
            }
            base.InputBegan(eventData);
        }

        public override void InputMoved(InputData eventData)
        {
            if (!AutoBattleOpMode.Instance.IsInputMode)
            {
                return;
            }
            base.InputMoved(eventData);
        }
        public override void InputEnded(InputData eventData)
        {
            if (!AutoBattleOpMode.Instance.IsInputMode)
            {
                return;
            }
            base.InputEnded(eventData);
        }
        public override void ZoomInOut(InputData eventData)
        {
            if (!AutoBattleOpMode.Instance.IsInputMode)
            {
                return;
            }
            base.ZoomInOut(eventData);
        }

    }
}
