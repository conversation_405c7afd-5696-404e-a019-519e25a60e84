using System;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class AutoBattleDefaultState : AutoBattleBaseState
    {

        public AutoBattleDefaultState(OpModeBase owner) : base(owner) { }

        protected override void OnUpdateStatePerformance()
        {
            m_opMode.IsInputMode = true;
            m_opMode.IsProcessCommandMove = false;
            //m_opMode.CurTeamDecisionMarkId = TeamDecisionMarkId.None;
            EventManager.instance.Broadcast(EventID.Entity_AutoStateSkillPerformance_InOrOut, false);
            //EventManager.instance.Broadcast(EventID.Entity_SelectCommand_InOrOut, false, TeamDecisionMarkId.None);
            EventManager.instance.Broadcast(EventID.BattleMode_EntityCancelSelected);
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, true);
            BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Active);
        }

        public override Boolean BattleUIClick(BattleUIEventID eventId, params int[] argvs)
        {
            Debug.Log($"EventOnBattleUIClick =====1-2===== {eventId} ");
            Boolean result = false;
            switch (eventId)
            {
                case BattleUIEventID.ShowEntityDetailInfoBtn:
                    Int32 entityUid = argvs.GetValueSafely(0, 0);
                    ChangeState2SelectDeactiveEntity(entityUid, true);
                    result = true;
                    break;
            }
            return result;
        }

        protected override void OnGridSelected(GridPosition gridPosition)
        {
            if (gridPosition.x == GridPosition.invalid.x || gridPosition.y == GridPosition.invalid.y)
            {
                return;
            }

            EventManager.instance.Broadcast(EventID.Entity_SelectCommand_SelectGrid, gridPosition, TeamDecisionMarkId.None);
        }

        protected override void OnEntitySelected(EntityView entity, GridPosition gridPosition)
        {
            if (gridPosition.x == GridPosition.invalid.x || gridPosition.y == GridPosition.invalid.y)
            {
                return;
            }

            EventManager.instance.Broadcast(EventID.Entity_SelectCommand_SelectGrid, gridPosition, TeamDecisionMarkId.None);
        }

        public override void InputBegan(InputData eventData)
        {
            base.InputBegan(eventData);
        }
        public override void InputMoved(InputData eventData)
        {
            base.InputMoved(eventData);
        }
        public override void InputEnded(InputData eventData)
        {
            base.InputEnded(eventData);
        }
        public override void ZoomInOut(InputData eventData)
        {
            base.ZoomInOut(eventData);
        }
    }
}
