using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class AutoBattleSelectEntityState : AutoBattleBaseState
    {
        public AutoBattleSelectEntityState(OpModeBase owner) : base(owner) { }

        protected AutoBattleOpMode m_battleMode;
        protected Boolean m_needUpdateView;

        public override void OnInit()
        {
            base.OnInit();
            m_battleMode = m_owner as AutoBattleOpMode;
        }

        protected override void OnUpdateStateContext(ModeStateContext context)
        {
            Debug.Log($"EventOnBattleUIClick =====1-5===== {context.originEntityUid} ");

            EntityView entityView = EntityViewManager.instance.GetEntityView(context.originEntityUid);
            m_battleMode.SelectEntityView(entityView, false);
            m_needUpdateView = context.m_refreshView;
        }

        protected override void OnUpdateStatePerformance()
        {
            EventManager.instance.Broadcast(EventID.AutoBattleMode_SelectEntityDetail, true);
            EventManager.instance.Broadcast(EventID.BattleMode_DeactiveEntitySelected, m_battleMode.SelectEntityUid);
            //EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, m_battleMode.CurrentSelectedGrid);
            if (m_needUpdateView)
            {
                BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(true);
                BattleShortCut.battleSceneGridManager.ShowNormalSkillMoveRange(m_battleMode.SelectEntityPosition, m_battleMode.MoveRange, m_battleMode.AttackRange);
                BattleModeHelper.UpdateEntityViewActiveState();
                m_battleMode.UpdateEntityAttackRangeSignRelationship(true);
            }
            BattleModeHelper.ShowEntityOccupyGrid(m_battleMode.SelectEntity, 1, 1);
        }

        protected override void OnGridSelected(GridPosition gridPosition)
        {
            m_battleMode.ResetSelectEntity();
            if (AutoBattleOpMode.Instance.CurTeamDecisionMarkId == ConfigData.TeamDecisionMarkId.None)
            {
                BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.Default);
            }
            else
            {
                AutoBattleOpMode.Instance.TryChangeToSelectCommand(true);
            }
            //EventManager.instance.Broadcast(EventID.BattleMode_EntityCancelSelected);
            EventManager.instance.Broadcast(EventID.AutoBattleMode_SelectEntityDetail, false);
        }

        protected override void OnEntitySelected(EntityView entityView, GridPosition gridPosition)
        {
            //if (entityView.CanActive())
            //{
            //    m_battleMode.ResetSelectEntity();
            //    ChangeState2SelectActiveEntity(entityView.uid, true, true);
            //}
            if (m_battleMode.SelectEntityUid != entityView.uid)
            {
                m_battleMode.ResetSelectEntity();
                ChangeState2SelectDeactiveEntity(entityView.uid, true);
            }
        }

        //public override Boolean BattleUIClick(BattleUIEventID eventId, params int[] argvs)
        //{
        //    Boolean result = base.BattleUIClick(eventId, argvs);
        //    if (result != false)
        //    {
        //        if (eventId == BattleUIEventID.TurnFinishBtnClick)
        //        {
        //            m_battleMode.SetTurnFinish();
        //        }
        //    }
        //    return result;
        //}

    }
}
