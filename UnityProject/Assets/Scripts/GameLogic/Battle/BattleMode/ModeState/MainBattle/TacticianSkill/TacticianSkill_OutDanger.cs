


using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class TacticianSkill_OutDanger : TacticianSkill_Base
    {
        protected List<SkillGridPosition> m_skillMaxRange = new List<SkillGridPosition>();
        protected List<SkillGridPosition> m_skillEffectRange = new List<SkillGridPosition>();
        protected GridPosition m_currentGrid = GridPosition.invalid;
        protected GridPosition m_invalidGrid = GridPosition.invalid;

        public TacticianSkill_OutDanger(OpModeBase owner) : base(owner) { }


        public override void OnInit()
        {
            base.OnInit();
            m_skillMaxRange.Clear();
            m_skillEffectRange.Clear();
            m_currentGrid = GridPosition.invalid;
        }


        protected override void OnUpdateStatePerformance()
        {
            m_tactician.TryGetSkillMaxRange(m_tacticianSkill.uid, ref m_skillMaxRange);
            ShowSkillRange();
        }

        protected override void OnGridSelected(GridPosition gridPosition)
        {
            Boolean canSelect = false;

            foreach (var item in m_skillMaxRange)
            {
                if (item.m_selectFlag && item.m_grid == gridPosition)
                {
                    canSelect = true;
                    break;
                }
            }

            if (canSelect)
            {
                m_stepGrids.Clear();
                m_stepGrids.Add(gridPosition);
                m_currentGrid = gridPosition;
                ShowSkillRange();
                EventManager.instance.Broadcast(EventID.BattleMode_ShowTacticianSkillConfirm);
            }
        }

        protected override void OnEntitySelected(EntityView entityView, GridPosition gridPosition)
        {
            OnGridSelected(gridPosition);
        }

        protected void ShowSkillRange()
        {
            BattleShortCut.battleSceneGridManager.ShowMax_EffectSkillRange(m_invalidGrid, m_currentGrid, m_skillMaxRange, m_skillEffectRange, true);
        }
    }
}
