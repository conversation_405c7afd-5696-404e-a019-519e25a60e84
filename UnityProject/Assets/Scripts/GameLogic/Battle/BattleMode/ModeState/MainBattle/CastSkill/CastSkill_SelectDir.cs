
using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class CastSkill_SelectDir : CastSkill_BaseState
    {
        public CastSkill_SelectDir(OpModeBase owner) : base(owner) { }

        protected override void OnUpdateStatePerformance()
        {
            BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Active);
            m_battleMode.UpdateEntityAttackRangeSignRelationship(true);
            EventManager.instance.Broadcast(EventID.BattleMode_EntitySkillSelected, m_battleMode.SelectEntityUid, m_battleMode.SelectSkillUid);
            BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);
            TryShowEntityMovePathGrid(m_battleMode.SelectEntityPosition, m_battleMode.SelectEntityRawPosition);
            m_battleMode.UpdateSkillMinRange();
            m_battleMode.UpdateDangerRangeGrid();
            ShowMove_SkillMinRangeGrid();
        }



        protected override void OnGridSelected(GridPosition gridPosition)
        {
            if (gridPosition.isValid == false)
            {
                return;
            }
            EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, gridPosition);
            if (m_battleMode.SkillTargetCount == 0)
            {
                if (m_battleMode.CanSelectMinSkillRangeDoAttack(gridPosition))
                {
                    // 选择【目标方向】
                    m_battleMode.PushSkillTarget(gridPosition);
                    m_battleMode.UpdateSkillEffectRange();
                    ShowMove_SkillMin_SkillEffectRangeGrid(true);
                    EventManager.instance.Broadcast(EventID.BattleMode_EntityCastSkillConfirm, m_battleMode.SelectEntityUid, 0, m_battleMode.SelectSkillUid);
                }
                else if (m_battleMode.CanSelectGridDoMove(gridPosition))
                {
                    // 执行移动
                    StartMoveImmediate(gridPosition);
                    m_battleMode.UpdateSkillMinRange();
                    ShowMove_SkillMinRangeGrid(true);
                    EventManager.instance.Broadcast(EventID.BattleMode_EntitySkillSelected, m_battleMode.SelectEntityUid, m_battleMode.SelectSkillUid);
                }
            }
            else if (m_battleMode.SkillTargetCount == 1)
            {
                if (m_battleMode.CanSelectMinSkillRangeDoAttack(gridPosition))
                {
                    // 切换【目标方向】
                    m_battleMode.PopSkillTarget();
                    m_battleMode.PushSkillTarget(gridPosition);
                    m_battleMode.UpdateSkillEffectRange();
                    ShowMove_SkillMin_SkillEffectRangeGrid(true);
                    EventManager.instance.Broadcast(EventID.BattleMode_EntityCastSkillConfirm, m_battleMode.SelectEntityUid, 0, m_battleMode.SelectSkillUid);
                }
                else if (m_battleMode.CanSelectGridDoMove(gridPosition))
                {
                    // 操作回退，继续执行移动
                    m_battleMode.PopSkillTarget();
                    StartMoveImmediate(gridPosition);
                    m_battleMode.UpdateSkillMinRange();
                    ShowMove_SkillMinRangeGrid(true);
                    EventManager.instance.Broadcast(EventID.BattleMode_EntitySkillSelected, m_battleMode.SelectEntityUid, m_battleMode.SelectSkillUid);
                }
            }
            else
            {
                EventManager.instance.Broadcast(EventID.BattleMode_SelectWrongSkillTarget);
            }
        }

        protected override void OnEntitySelected(EntityView entityView, GridPosition gridPosition)
        {
            if (gridPosition.isValid)
            {
                EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, gridPosition);
                if (m_battleMode.CanSelectMinSkillRangeDoAttack(gridPosition))
                {
                    OnGridSelected(gridPosition);
                }
                else
                {
                    EventManager.instance.Broadcast(EventID.BattleMode_SelectWrongSkillTarget);
                }
            }
        }



        internal void ShowMove_SkillMinRangeGrid(Boolean immediately = false)
        {
            GridPosition originGrid = m_battleMode.SelectEntityRawPosition;
            GridPosition targetGrid = m_battleMode.TargetGridPosition;
            List<MoveGridPosition> moveRange = m_battleMode.MoveRange;
            List<SkillGridPosition> minSkillRange = m_battleMode.MinSkillRange;
            BattleShortCut.battleSceneGridManager.ShowMove_MaxSkillRange(originGrid, targetGrid, moveRange, minSkillRange, immediately);
        }

        internal void ShowMove_SkillMin_SkillEffectRangeGrid(Boolean immediately = false)
        {
            GridPosition originGrid = m_battleMode.SelectEntityRawPosition;
            GridPosition targetGrid = m_battleMode.TargetGridPosition;
            List<MoveGridPosition> moveRange = m_battleMode.MoveRange;
            List<SkillGridPosition> minSkillRange = m_battleMode.MinSkillRange;
            List<SkillGridPosition> minEffectRange = m_battleMode.SkillEffectRange;
            BattleShortCut.battleSceneGridManager.ShowMove_Max_EffectSkillRange(originGrid, targetGrid, moveRange, minSkillRange, minEffectRange, immediately);
        }
    }
}
