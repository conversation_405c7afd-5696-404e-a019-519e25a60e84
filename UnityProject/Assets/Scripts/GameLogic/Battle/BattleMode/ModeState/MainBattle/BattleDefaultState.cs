
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleDefaultState : BattleModeStateBase
    {
        public BattleDefaultState(OpModeBase owner) : base(owner) { }

        protected override void OnUpdateStateContext(ModeStateContext context)
        {
            if (context != null && context.m_refreshView)
            {
                BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
                BattleModeHelper.UpdateEntityViewActiveState();
                m_battleMode.UpdateDangerRangeGrid(false);
            }
        }

        protected override void OnUpdateStatePerformance()
        {
            EntityView entityView = EntityViewManager.instance.GetEntityView(m_battleMode.PreActiveEntityUid);
            if (entityView != null && entityView.CanActive())
            {
                GridPosition gridPosition = entityView.GetPinnedPosition();
                EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, gridPosition);
                BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(true, gridPosition);
                ChangeState2SelectActiveEntity(m_battleMode.PreActiveEntityUid, true, true);
            }
            else
            {
                EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, m_battleMode.CurrentSelectedGrid);
                m_battleMode.UpdateEntityAttackRangeSignRelationship(false);
                BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);
            }
        }
        protected override void OnEntitySelected(EntityView entityView, GridPosition gridPosition)
        {
            if (entityView.CanActive())
            {
                ChangeState2SelectActiveEntity(entityView.uid, true, true);
            }
            else
            {
                ChangeState2SelectDeactiveEntity(entityView.uid, true);
            }
        }
    }
}
