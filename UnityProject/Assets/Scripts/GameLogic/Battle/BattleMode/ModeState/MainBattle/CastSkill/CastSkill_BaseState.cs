
using System;
using System.Collections.Generic;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public abstract class CastSkill_BaseState : BattleModeStateBase
    {
        protected Dictionary<Int32, Int32> m_entityHpPreviewDict = new Dictionary<Int32, Int32>();
        public CastSkill_BaseState(OpModeBase owner) : base(owner) { }

        protected override void OnUpdateStateContext(ModeStateContext context)
        {

        }

        protected override void OnUpdateStatePerformance()
        {

        }



        protected override void OnGridSelected(GridPosition gridPosition)
        {

        }

        protected override void OnEntitySelected(EntityView entityView, GridPosition gridPosition)
        {

        }


        public override void SkillCancel()
        {
            m_battleMode.PopSkillTarget(true);
            ChangeState2SelectActiveEntity(m_battleMode.SelectEntityUid, true, false);
        }

        public override void SkillConfirm()
        {
            PreviewBloodChangeInternal(false);
            Int32 entityUid = m_battleMode.SelectEntityUid;
            Int32 skillUid = m_battleMode.SelectSkillUid;
            GridPosition movePose = m_battleMode.SelectEntityRawPosition;
            List<GridPosition> stepGrids = m_battleMode.SkillTargets;
            CastSkill(entityUid, skillUid, movePose, stepGrids);
            m_battleMode.ResetSelectEntity();
        }



        #region Internal


        internal void PreviewBloodChangeInternal(Boolean isShow)
        {
            if (isShow)
            {
                EntityView sourceEntity = m_battleMode.SelectEntity;
                GridPosition standGridPosition = m_battleMode.SelectEntityRawPosition;
                Int32 skillUid = m_battleMode.SelectSkillUid;
                List<GridPosition> stepGrids = m_battleMode.SkillTargets;
                BattleModeHelper.PreviewBloodChange(true, sourceEntity, standGridPosition, skillUid, stepGrids, ref m_entityHpPreviewDict);
            }
            else
            {
                BattleModeHelper.PreviewBloodChange(false, null, GridPosition.invalid, 0, null, ref m_entityHpPreviewDict);
            }
        }

        #endregion
    }
}
