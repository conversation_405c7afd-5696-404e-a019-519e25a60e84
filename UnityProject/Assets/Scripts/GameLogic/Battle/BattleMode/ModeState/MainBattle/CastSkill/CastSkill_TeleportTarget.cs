
using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    /// <summary>
    /// 传送目标角色
    /// </summary>
    public class CastSkill_TeleportTarget : CastSkill_BaseState
    {
        public CastSkill_TeleportTarget(OpModeBase owner) : base(owner) { }

        protected override void OnUpdateStatePerformance()
        {
            BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Active);
            m_battleMode.UpdateEntityAttackRangeSignRelationship(true);
            EventManager.instance.Broadcast(EventID.BattleMode_EntitySkillSelected, m_battleMode.SelectEntityUid, m_battleMode.SelectSkillUid);
            BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);

            TryShowEntityMovePathGrid(m_battleMode.SelectEntityPosition, m_battleMode.SelectEntityRawPosition);
            m_battleMode.UpdateSkillMaxRange();
            m_battleMode.UpdateDangerRangeGrid();
            ShowTeleportSkillMoveMinMaxRangeGrid();
        }



        protected override void OnGridSelected(GridPosition gridPosition)
        {
            if (!BattleShortCut.logicBattle.CheckPosValid(gridPosition))
            {
                return;
            }

            EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, gridPosition);
            if (m_battleMode.SkillTargetCount  == 0)
            {
                if (m_battleMode.CanSelectGridDoMove(gridPosition))
                {
                    // 执行移动
                    StartMoveImmediate(gridPosition);
                    m_battleMode.UpdateSkillMaxRange();
                    ShowTeleportSkillMoveMinMaxRangeGrid(true);
                    BattleHelper.SetCameraPosition(m_battleMode.SelectEntityRawPosition);
                }
            }
            else if (m_battleMode.SkillTargetCount == 1)
            {
                // 选择【要传送的角色】的【传送位置】
                if (m_battleMode.CanSelectMinSkillRangeDoAttack(gridPosition))
                {
                    m_battleMode.PushSkillTarget(gridPosition);
                    ShowTeleportSkillEffectRange(false);
                    EventManager.instance.Broadcast(EventID.BattleMode_EntityCastSkillConfirm, m_battleMode.SelectEntityUid, 0, m_battleMode.SelectSkillUid);
                }
            }
            else if (m_battleMode.SkillTargetCount == 2)
            {
                // 切换【传送位置】
                if (gridPosition != m_battleMode.TargetGridPosition)
                {
                    if (m_battleMode.CanSelectMinSkillRangeDoAttack(gridPosition))
                    {
                        m_battleMode.PopSkillTarget();
                        m_battleMode.PushSkillTarget(gridPosition);
                        ShowTeleportSkillEffectRange(true);
                        EventManager.instance.Broadcast(EventID.BattleMode_EntityCastSkillConfirm, m_battleMode.SelectEntityUid, 0, m_battleMode.SelectSkillUid);
                    }
                }
            }

        }

        protected override void OnEntitySelected(EntityView entityView, GridPosition gridPosition)
        {
            EventManager.instance.Broadcast(EventID.BattleMode_GearSelected, gridPosition);
            if (m_battleMode.SkillTargetCount  == 0)
            {
                // 选择【要传送的角色】
                if (m_battleMode.CanSelectEntityDoAttack(entityView, ref gridPosition))
                {
                    if (gridPosition != m_battleMode.SelectEntityRawPosition)
                    {
                        // 传送其他人，先尝试移动到目标附近
                        GridPosition closestGridPosition = m_battleMode.GetClosestAttackGridPosition(gridPosition, m_battleMode.SelectSkillUid);
                        if(closestGridPosition != m_battleMode.SelectEntityRawPosition)
                        {
                            StartMoveImmediate(closestGridPosition);
                            m_battleMode.UpdateGhostEntityPositionStandard(closestGridPosition, gridPosition);
                        }
                    }
                    m_battleMode.PushSkillTarget(gridPosition);
                    m_battleMode.UpdateSkillMinRange();
                    ShowTeleportSkillEffectRange(false);
                    EventManager.instance.Broadcast(EventID.BattleMode_EntitySkillTargetSelect, m_battleMode.SelectEntityUid, 0, m_battleMode.SelectSkillUid);

                    BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.DangerRange, true);
                    BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.MoveRange, true);
                }
                else
                {
                    EventManager.instance.Broadcast(EventID.BattleMode_SelectWrongSkillTarget);
                    //UnityEngine.Debug.LogError($"[BattleOpMode] SelectActiveEntitySkillPlusState.OnEntitySelected ----> {entityView.name}");
                }
            }
            else
            {
                OnGridSelected(gridPosition);
            }

        }

        #region Internal

        internal void ShowTeleportSkillMoveMinMaxRangeGrid(Boolean immediately = false)
        {
            GridPosition originGrid = m_battleMode.SelectEntityRawPosition;
            List<MoveGridPosition> moveRange = m_battleMode.MoveRange;
            List<SkillGridPosition> minSkillRange = m_battleMode.MinSkillRange;
            List<SkillGridPosition> maxSkillRange = m_battleMode.MaxSkillRange;
            BattleShortCut.battleSceneGridManager.ShowMove_MinMaxSkillRange(originGrid, moveRange, minSkillRange, maxSkillRange, immediately);

        }

        internal void ShowTeleportSkillEffectRange(Boolean immediately = false)
        {
            m_battleMode.UpdateSkillEffectRange();
            GridPosition originGrid = m_battleMode.TargetGridPosition;
            GridPosition targetGrid = m_battleMode.TargetGridPosition;
            List<MoveGridPosition> moveRange = m_battleMode.MoveRange;
            List<SkillGridPosition> minSkillRange = m_battleMode.MinSkillRange;
            List<SkillGridPosition> skillEffectRange = m_battleMode.SkillEffectRange;
            BattleShortCut.battleSceneGridManager.ShowMax_EffectSkillRange(originGrid, targetGrid, minSkillRange, skillEffectRange, immediately);
        }
        #endregion
    }
}
