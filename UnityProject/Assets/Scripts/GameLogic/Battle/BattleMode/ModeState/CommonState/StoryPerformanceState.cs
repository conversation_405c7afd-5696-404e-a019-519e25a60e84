

using System;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    /// <summary>
    ///  通用技能表现
    /// </summary>
    public class StoryPerformanceState : ModeStateBase
    {
        public override Boolean Enable4Input { get { return false; } }

        public StoryPerformanceState(OpModeBase owner) : base(owner) { }


        protected override void OnUpdateStatePerformance()
        {
            EventManager.instance.Broadcast(EventID.BattleMode_EntityCancelSelected);
            BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);
        }

    }
}
