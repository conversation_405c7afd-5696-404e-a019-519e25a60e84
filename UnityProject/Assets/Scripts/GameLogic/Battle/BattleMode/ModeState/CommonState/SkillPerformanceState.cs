

using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    /// <summary>
    ///  通用技能表现
    /// </summary>
    public class SkillPerformanceState : ModeStateBase
    {
        public override Boolean Enable4Input { get { return false; } }

        public SkillPerformanceState(OpModeBase owner) : base(owner) { }

        public override void OnInit()
        {
            base.OnInit();
        }

        public override void OnUnInit()
        {
            base.OnUnInit();
        }

        protected override void OnUpdateStateContext(ModeStateContext context)
        {
            m_owner.Reset();
        }

        protected override void OnUpdateStatePerformance()
        {
            if (AutoBattleOpMode.Instance.IsProcessCommandSelect)
            {
                EventManager.instance.Broadcast(EventID.Entity_SelectCommand_CancelTarget);
            }
                
            EventManager.instance.Broadcast(EventID.Entity_AutoStateSkillPerformance_InOrOut, true);
            EventManager.instance.Broadcast(EventID.BattleMode_EntityCancelSelected);
            EventManager.instance.Broadcast(EventID.BattleMode_GearCancelSelected);
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.Normal, false);
            BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Active);
            BattleShortCut.battleScene.skillPredictHandler.ShowOrHideSkillPredictEffect(false);
        }

        //protected void TryShowCombatUI(Int32 sourceEntityUid, Int32 skillUid, Int32 targetEntityUid)
        //{
        //    EntityView sourceEntityView = EntityViewManager.instance.GetEntityView(sourceEntityUid);
        //    EntityView targetEntityView = EntityViewManager.instance.GetEntityView(targetEntityUid);
        //    if (sourceEntityView != null && targetEntityView != null)
        //    {
        //        Skill skill = sourceEntityView.GetSkillBySkillUid(skillUid);
        //        if (skill != null && skill.engageType == SkillEngageType.Combat)
        //        {
        //            BattleCombatUI.ShowCombatUI(sourceEntityUid, skillUid, targetEntityView.uid);
        //        }
        //    }
        //}

        //protected void TryCloseCombatUI(Int32 sourceEntityUid, Int32 skillUid)
        //{
        //    EntityView sourceEntityView = EntityViewManager.instance.GetEntityView(sourceEntityUid);
        //    if (sourceEntityView != null)
        //    {
        //        Skill skill = sourceEntityView.GetSkillBySkillUid(skillUid);
        //        if (skill != null && skill.engageType == SkillEngageType.Combat)
        //        {
        //            BattleCombatUI.CloseCombatUI();
        //        }
        //    }
        //}

    }
}
