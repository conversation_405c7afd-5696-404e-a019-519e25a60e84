
using System;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    /* ModeState 用来处理不同模式下的多个状态
     * 
     * 0、DefaultMode
     *    a. Default
     * 
     * 1、PrepareOpMode
     *    a. Default
     *    b. SelectOwnerEntity
     *    c. SelectOtherEntity
     *    
     * 2、BattleOpMode
     *    a. BattleDefaultState
     *    b. SelectDeactiveEntityState
     *    c. SelectActiveEntityState
     *    d. SelectSkillTargetState  (target1 target2 target3 target4 ...[targetStack])
     *    e. CastSkillConfirmState
     * 
     * 3、RetractOpMode
     *    a. Default
     *    b. SelectActiveEntity
     * 
     * 4、ReplayOpMode
     *    a. Default
     *    b. SelectActiveEntity
     * 
     * 5、StorylineOpMode
     *    a. Default
     * 
     */


    public abstract class ModeStateBase
    {
        protected OpModeBase m_owner;
        public virtual Boolean Enable4Input { get { return true; } }

        public ModeStateBase(OpModeBase owner) { m_owner = owner; }

        public void Init(ModeStateContext context)
        {
            OnInit();
            UpdateState(context);
        }

        public void Refresh()
        {
            OnRefresh();
        }
        public void UpdateState(ModeStateContext context)
        {
            OnUpdateStateContext(context);
            ModeStateContext.Release(context);
            OnUpdateStatePerformance();
        }

        public void UnInit()
        {
            OnUnInit();
        }

        public void Reset()
        {
            OnReset();
        }

        public void SelectGrid(GridPosition gridPosition)
        {
            if (BattleShortCut.logicBattle.CheckPosValid(gridPosition))
            {
                IEntity logicEntity = BattleShortCut.logicBattle.GetFirstEntityByPos(gridPosition, ConfigData.EntityType.Actor);
                EventManager.instance.Broadcast(EventID.BattleMode_EntitySelected, logicEntity == null ? 0 : logicEntity.uid);

                IEntity entity = BattleShortCut.sampleBattle.GetFirstEntityByPos(gridPosition, ConfigData.EntityType.Actor);
                if (entity != null)
                {
                    EntityView entityView = EntityViewManager.instance.GetEntityView(entity.uid);
                    if (entityView != null)
                    {
                        OnEntitySelected(entityView, gridPosition);
                    }
                }
                else
                {
                    OnGridSelected(gridPosition);
                }
            }
            else
            {
                OnGridSelected(gridPosition);
            }
        }

        #region Internal
        protected virtual void OnUpdateStateContext(ModeStateContext context) { }
        protected virtual void OnUpdateStatePerformance() { }
        protected virtual void OnEntitySelected(EntityView entity, GridPosition gridPosition) { }
        protected virtual void OnGridSelected(GridPosition gridPosition) { }
        #endregion

        #region Public
        public virtual void SelectSkill(Int32 skillUid) { }
        public virtual void EntityWait() { }
        public virtual void SkillCancel() { }
        public virtual void SkillConfirm() { }

        public virtual void InputBegan(InputData eventData)
        {
            if (!Enable4Input)
            {
                return;
            }
            EventManager.instance.Broadcast(EventID.EventOnInputDown, eventData);
        }
        public virtual void InputMoved(InputData eventData)
        {
            if (!Enable4Input)
            {
                return;
            }
            EventManager.instance.Broadcast(EventID.EventOnInputDrag, eventData);
        }
        public virtual void InputEnded(InputData eventData)
        {
            if (!Enable4Input)
            {
                return;
            }
            EventManager.instance.Broadcast(EventID.EventOnInputUp, eventData);
        }
        public virtual void ZoomInOut(InputData eventData)
        {
            if (!Enable4Input)
            {
                return;
            }
            EventManager.instance.Broadcast(EventID.EventOnInputZoomInOut, eventData);
        }
        public virtual void KeyboardEvent4Move(InputData eventData)
        {
            if (!Enable4Input)
            {
                return;
            }
            EventManager.instance.Broadcast(EventID.EventOnInputDrag, eventData);
        }
        public virtual Boolean BattleUIClick(BattleUIEventID eventId, params Int32[] argvs) { return false; }
        public virtual void KeyboardKeyDown(InputData eventData) { }
        #endregion


        public virtual void OnInit() { }
        public virtual void OnRefresh() { }
        public virtual void OnReset() { }
        public virtual void OnUnInit() { }

    }
}
