
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public class BattleOpModeManager : Singleton<BattleOpModeManager>
    {
        private OpModeBase m_currentMode;
        protected readonly Dictionary<BattleOpModeId, OpModeBase> m_states = new Dictionary<BattleOpModeId, OpModeBase>();

        protected readonly HashSet<InputLockType> m_lockSet = new HashSet<InputLockType>();

        #region Override

        protected override void OnInit()
        {
            base.OnInit();
            m_states[BattleOpModeId.Default] = new DefaultOpMode();
            m_states[BattleOpModeId.PrepareOpMode] = new PrepareOpMode();
            m_states[BattleOpModeId.BattleOpMode] = new BattleOpMode();
            m_states[BattleOpModeId.AutoBattleOpMode] = new AutoBattleOpMode();
            m_states[BattleOpModeId.RetractOpMode] = new RetractOpMode();
            m_states[BattleOpModeId.ReplayOpMode] = new ReplayOpMode();
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_currentMode?.UnInit();
            m_currentMode = null;
            foreach(var state in m_states)
            {
                state.Value.Dispose();
            }
            m_states.Clear();
            m_lockSet.Clear();
        }

        #endregion


        #region Public
        
        public void Tick(TimeSlice ts)
        {
            m_currentMode?.Tick(ts);
        }

        public BattleOpModeId GetCurrentModeId()
        {
            if (m_currentMode != null)
            {
                return m_currentMode.BattleOpModeId;
            }
            return BattleOpModeId.None;
        }

        public void ChangeMode(BattleOpModeId opMode)
        {
            if (m_states.TryGetValue(opMode, out OpModeBase state))
            {
                ChangeModeInternal(state);
            }
        }

        public void ChangeModeState(BattleOpModeId opModeId, BattleOpModeState state, ModeStateContext context)
        {
            if (m_states.TryGetValue(opModeId, out OpModeBase opMode))
            {
                if (m_currentMode != opMode)
                {
                    ChangeModeInternal(opMode);
                }
                if (state == BattleOpModeState.SkillPerformance)
                {
                    opMode.ChangeStateInternal<SkillPerformanceState>(context);
                }
                if (state == BattleOpModeState.Default)
                {
                    opMode.ChangeStateInternal<BattleDefaultState>(context);
                }
                if (state == BattleOpModeState.SelectCommand)
                {
                    opMode.ChangeStateInternal<SelectCommandState>(context);
                }
            }
        }

        public void ChangeModeState(BattleOpModeState state, ModeStateContext context = null)
        {
            if (m_currentMode != null)
            {
                m_currentMode.ChangeState(state, context);
            }
        }

        public void InputLockAdd(InputLockType lockType)
        {
            if (m_lockSet.Contains(lockType))
            {
                Debug.Log(String.Format("[Error] BattleOpModeManager LockSet State {0} Is Exists, Add Failed!", lockType));
                return;
            }
            m_lockSet.Add(lockType);
        }

        public void InputLockRemove(InputLockType lockType)
        {
            if (!m_lockSet.Contains(lockType))
            {
                Debug.Log(String.Format("[Error] BattleOpModeManager LockSet State {0} Is Not Exists, Remove Failed!", lockType));
                return;
            }
            m_lockSet.Remove(lockType);
        }

        #endregion

        #region Private

        private void ChangeModeInternal(OpModeBase opMode)
        {
            m_currentMode?.UnInit();
            m_currentMode = opMode;
            m_currentMode?.Init();
        }

        private bool IsInputDisable()
        {
            return m_lockSet.Count > 0 || m_currentMode != null && m_currentMode.IsInputDisable();
        }

        #endregion


        #region Input Event
        public void EventOnGridClick(GridPosition gridPosition) { m_currentMode?.EventOnSelectGrid(gridPosition); }
        public void EventOnInputClick(InputData eventData)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnInputClick(eventData);
        }
        public void EventOnInputBegan(InputData eventData)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnInputBegan(eventData);
        }
        public void EventOnInputMoved(InputData eventData)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnInputMoved(eventData);
        }
        public void EventOnInputEnded(InputData eventData)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnInputEnded(eventData);
        }
        public void EventOnZoomInOut(InputData eventData)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnZoomInOut(eventData);
        }

        public void EventOnKeyboardKeyDown(KeyCode keyCode)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnKeyboardKeyDown(keyCode);
        }
        public void EventOnKeyboardKeyHold(KeyCode keyCode)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnKeyboardKeyHold(keyCode);
        }
        public void EventOnKeyboardKeyUp(KeyCode keyCode)
        {
            if (IsInputDisable())
            {
                return;
            }
            m_currentMode?.EventOnKeyboardKeyUp(keyCode);
        }

        public void EventOnJoystickDown() { m_currentMode?.EventOnJoystickDown(); }
        public void EventOnJoystickHold(Vector2 deltaValue) { m_currentMode?.EventOnJoystickHold(deltaValue); }
        public void EventOnJoystickUp() { m_currentMode?.EventOnJoystickUp(); }

        public void EventOnBattleUIClick(BattleUIEventID eventId, params Int32[] argvs) { m_currentMode?.EventOnBattleUIClick(eventId, argvs); }

        #endregion


        #region Private

        #endregion


    }
}
