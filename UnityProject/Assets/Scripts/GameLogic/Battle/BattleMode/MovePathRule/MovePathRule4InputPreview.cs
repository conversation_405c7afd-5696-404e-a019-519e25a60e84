using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class MovePathRule4InputPreview : MovePathRule
    {
        private MovePathRule m_rule;
        private int m_leftMovePoint;

        public void Clear()
        {
            m_rule = null;
            m_leftMovePoint = default;
        }
        public void SetEntity(EntityView entityView)
        {
            SetRule(entityView.GetMovePathRule());
            if (entityView.HasExtraMoveChance())
            {
                SetLeftMovePoint(entityView.GetExtraMoveDist());
            }
            else
            {
                SetLeftMovePoint(entityView.GeMaxMovePoint());
            }
        }

        public void SetRule(MovePathRule rule)
        {
            m_rule = rule;
            InitBattleObj(rule.battle);
        }

        public void SetLeftMovePoint(int movePoint)
        {
            m_leftMovePoint = movePoint;
        }


        public override int CalcG(GridPosition fromGridPos, GridPosition toGridPos, bool isFirst)
        {
            return m_rule.CalcG(fromGridPos, toGridPos, isFirst);
        }

        public override int CalcH(GridPosition fromGridPos, GridPosition toGridPos)
        {
            return m_rule.CalcH(fromGridPos, toGridPos);
        }

        public override bool CanLocate(GridPosition gridPos)
        {
            return m_rule.CanLocate(gridPos);
        }

        public override bool CanPass(GridPosition gridPos)
        {
            return m_rule.CanPass(gridPos);
        }

        public override bool CheckG(int g)
        {
            return g <= m_leftMovePoint;
        }

        public override bool CheckArrive(MovePath path, GridPosition targetPos)
        {
            return m_rule.CheckArrive(path, targetPos);
        }

        public override MovePathRule Copy(IFetchable fetchable)
        {
            MovePathRule4InputPreview rule = fetchable.FetchObj<MovePathRule4InputPreview>();
            rule.m_rule = m_rule;
            return rule;
        }
    }
}
