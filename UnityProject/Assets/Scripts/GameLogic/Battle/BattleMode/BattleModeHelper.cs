
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public static class BattleModeHelper
    {
        public static Boolean IsEnemyEntity(EntityView targetEntity)
        {
            Boolean result = false;
            if (targetEntity != null && targetEntity.entityType == EntityType.Actor)
            {
                BattleTeam activeTeam = BattleShortCut.activeTeam;
                BattleTeam battleTeam = targetEntity.GetTeam();
                Int32 activeTeamId = activeTeam != null ? activeTeam.uid : 0;
                Int32 entityTeamId = battleTeam != null ? battleTeam.uid : 0;
                if (activeTeamId != entityTeamId)
                {
                    result = true;
                }
            }
            return result;
        }

        public static Boolean IsHostTeamEntity(EntityView targetEntity)
        {
            Boolean result = false;
            if (targetEntity != null && targetEntity.entityType == EntityType.Actor)
            {
                BattleTeam battleTeam = targetEntity.GetTeam();
                Int32 entityTeamId = battleTeam != null ? battleTeam.uid : 0;
                if (BattleShortCut.hostTeamUid == entityTeamId)
                {
                    result = true;
                }
            }
            return result;
        }
        public static Boolean CanActive(this EntityView entityView)
        {
            if (entityView != null &&
                entityView.entityType == EntityType.Actor &&
                entityView.HasActionChance() &&
                entityView.BanAction() == false &&
                entityView.CanControlledByHost() && 
                entityView.IsInActiveTeam())
            {
                return true;
            }
            return false;
        }

        public static Boolean CanAttack(this EntityView entityView, EntityView targetEntity)
        {
            BattleTeam ownerEntityTeam = entityView.GetTeam();
            BattleTeam targetEntityTeam = targetEntity.GetTeam();
            Int32 ownerEntityTeamId = ownerEntityTeam != null ? ownerEntityTeam.uid : 0;
            Int32 targetEntityTeamId = targetEntityTeam != null ? targetEntityTeam.uid : 0;
            if (ownerEntityTeamId != targetEntityTeamId)
            {
                return true;
            }
            return false;
        }

        public static Int32 GeMaxMovePoint(this EntityView entityView)
        {
            if (entityView.BanMove())
            {
                return 0;
            }
            return (Int32)entityView.GetAttributeValue(AttributeId.MovePoint);
        }


        /// <summary> 获取移动路径 </summary>
        public static void TryGetMovePath(GridPosition startPosition, GridPosition targetPosition, MovePathRule movePathRule, ref List<GridPosition> gridPosList)
        {
            gridPosList.Clear();
            MovePathFindResult result = MovePathFindUtility.FindPathAnyway(startPosition, targetPosition, movePathRule);
            result.AppendToMovePos(gridPosList);
            result.Release();
        }
        /// <summary> 获取移动范围 </summary>
        public static void TryGetMoveRange(this EntityView entityView, ref List<MoveGridPosition> gridPosList, MovePathRule movePathRule)
        {
            gridPosList.Clear();
            GridPosition entityPosition = entityView.GetPinnedPosition();
            MoveRangeFindResult rangeResult = MovePathFindUtility.FindRange(entityPosition, movePathRule); // 获取到的范围包括友方站立的位置
            foreach(MovePathGridResult gridResult in rangeResult.gridResultList)
            {
                gridPosList.Add(new MoveGridPosition(gridResult.pos, gridResult.canLocate));
            }
            rangeResult.Release();
        }

        public static void TryGetNormalSkillRange(this EntityView entityView, List<MoveGridPosition> moveGrids, ref List<SkillGridPosition> skillGrids)
        {
            Skill skill = entityView.GetNormalAttack();
            if (skill == null)
            {
                return;
            }
            entityView.TryGetSkillMaxRange(skill.uid, moveGrids, ref skillGrids);
        }


        public static void UpdateSkillRangeSelectableState(this EntityView entityView, Int32 skillUid, List<SkillGridPosition> minGrids, List<SkillGridPosition> maxGrids)
        {
            Func<List<SkillGridPosition>, GridPosition, Boolean> check = (grids, grid) =>
            {
                foreach(SkillGridPosition skillGrid in grids)
                {
                    if (skillGrid.m_grid == grid)
                    {
                        return true;
                    }
                }
                return false;
            };
            Skill skill = entityView.GetSkillBySkillUid(skillUid);
            if (skill == null)
            {
                return;
            }
            TargetSelectStepInfo selectStepInfo = skill.skillInfo.selectStep;
            GridPosition rawLocation = entityView.GetLocatedPosition();
            Int32 extraSelectRange = skill.GetExtraSelectRange();
            using TargetSelectParamContainer paramContainer = entityView.GetBattle().FetchObj<TargetSelectParamContainer>();
            paramContainer.Init();

            for(Int32 i = 0; i < maxGrids.Count; i++)
            {
                SkillGridPosition skillGrid = maxGrids[i];
                GridPosition grid = skillGrid.m_grid;
                Boolean checkInRange = check(minGrids, grid);
                Boolean selectFlag = TargetSelectUtility.CheckSelectable(entityView.GetBattle(), selectStepInfo, entityView, rawLocation, extraSelectRange, paramContainer, checkInRange, grid);
                skillGrid.SetSelectFlag(selectFlag);
                maxGrids[i] = skillGrid;
            }

            //foreach (SkillGridPosition skillGrid in maxGrids)
            //{
            //    GridPosition grid = skillGrid.m_grid;
            //    Boolean checkInRange = check(minGrids, grid);
            //    Boolean selectFlag = TargetSelectUtility.CheckSelectable(entityView.GetBattle(), selectStepInfo, entityView, rawLocation, extraSelectRange, paramContainer, checkInRange, grid);
            //    skillGrid.SetSelectFlag(selectFlag);
            //}
        }


        /// <summary> 获取技能攻击范围（SkillMaxRange） </summary>
        public static void TryGetSkillMaxRange(this EntityView entityView, Int32 skillUid, List<MoveGridPosition> moveGrids, ref List<SkillGridPosition> skillGrids)
        {
            skillGrids.Clear();
            if (entityView.BanActiveSkill())
            {
                return;
            }
            Skill skill = entityView.GetSkillBySkillUid(skillUid);
            if (skill == null)
            {
                return;
            }
            int extraSelectRange = skill.GetExtraSelectRange();
            TargetSelectStepInfo selectStepInfo = skill.skillInfo.selectStep;
            SkillIndicatorType indicatorType = skill.skillInfo.indicatorType;
            if (selectStepInfo != null)
            {
                BattleFieldSummaryForPosCollection fieldSummary = BattleShortCut.sampleBattle.CreateFieldSummaryForPosCollection();
                Int32 count = moveGrids.Count;
                for (int i = 0; i < count; ++i)
                {
                    if (moveGrids[i].m_stayFlag)
                    {
                        TargetSelectUtility.AppendFirstRange(entityView.GetBattle(), selectStepInfo, entityView, moveGrids[i].m_grid, extraSelectRange, fieldSummary);
                    }
                }
                foreach (GridPosition pos in fieldSummary.GetPosList())
                {
                    skillGrids.Add(new SkillGridPosition(pos, false, indicatorType));
                }
                fieldSummary.Release();
            }
#if !RELEASE
            UnityEngine.Debug.Log($"【CastSkill-MaxRange】entityUid:{entityView.uid}, skillUid:{skillUid}, movePose:{entityView.GetLocatedPosition()}, rangeCount: {skillGrids.Count}");
#endif
        }

        /// <summary> 获取技能攻击范围（SkillMinRange） </summary>
        public static void TryGetSkillMinRange(this EntityView entityView, Int32 skillUid, List<GridPosition> stepTargets, ref List<SkillGridPosition> skillGrids)
        {
            skillGrids.Clear();
            Skill skill = entityView.GetSkillBySkillUid(skillUid);
            if (skill != null)
            {
                SkillIndicatorType indicatorType = skill.skillInfo.indicatorType;
                List<Int16> skillParams = GetSkillTargetsParam(stepTargets);
                SkillSelectIndicatorData indicatorData = SkillIndicatorUtility.GetSelectIndicatorData(skill, skillParams);
                foreach (SkillSelectIndicatorPosData posData in indicatorData.posDataList)
                {
                    skillGrids.Add(new SkillGridPosition(posData.pos, posData.selectable, indicatorType));
                }
            }

#if !RELEASE
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < stepTargets.Count; ++i)
            {
                sb.Append(stepTargets[i]);
            }
            UnityEngine.Debug.Log($"【CastSkill-MinRange】entityUid:{entityView.uid}, skillUid:{skillUid}, movePose:{entityView.GetLocatedPosition()}, stepList:[{sb}], rangeCount: {skillGrids.Count}");
#endif
        }

        /// <summary> 获取技能效果范围（最终作用效果） </summary>
        public static void TryGetSkillEffectRange(this EntityView entityView, Int32 skillUid, List<GridPosition> stepTargets, ref List<SkillGridPosition> skillGrids)
        {

            skillGrids.Clear();
            Skill skill = entityView.GetSkillBySkillUid(skillUid);
            if (skill != null)
            {
                SkillIndicatorType indicatorType = skill.skillInfo.indicatorType;
                List<Int16> skillParams = GetSkillTargetsParam(stepTargets);
                var indicatorData = SkillIndicatorUtility.GetEffectIndicatorData(skill, skillParams);
                foreach (var posData in indicatorData.posDataList)
                {
                    bool selectFlag = indicatorData.effectEntityUidList.Exists(entityUid => BattleShortCut.sampleBattle.GetEntityByUid(entityUid).IsLocatedOn(posData.pos));
                    skillGrids.Add(new SkillGridPosition(posData.pos, selectFlag, indicatorType));
                }
            }
#if !RELEASE
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < stepTargets.Count; ++i)
            {
                sb.Append(stepTargets[i]);
            }
            UnityEngine.Debug.Log($"【CastSkill-EffectRange】entityUid:{entityView.uid}, skillUid:{skillUid}, movePose:{entityView.GetLocatedPosition()}, stepList:[{sb}], rangeCount: {skillGrids.Count}");
#endif
        }

        public static List<Int16> GetSkillTargetsParam(List<GridPosition> skillTargets)
        {
            List<Int16> skillParam = new List<Int16>();
            for (int i = 0; i < skillTargets.Count; ++i)
            {
                GridPosition gridPosition = skillTargets[i];
                skillParam.Add((Int16)gridPosition.x);
                skillParam.Add((Int16)gridPosition.y);
            }
            return skillParam;
        }

        public static Int32 GetGridEntityViewId(GridPosition grid)
        {
            Int32 entityUid = -1;
            EntityView entityView = GetGridEntityView(grid);
            if (entityView != null)
            {
                entityUid = entityView.uid;
            }
            return entityUid;
        }

        public static EntityView GetGridEntityView(GridPosition grid)
        {
            EntityView entityView = EntityViewManager.instance.GetFirstControllableEntityViewOnGrid(grid);
            return entityView;
        }

        public static void UpdateEntityViewActiveState(Boolean showActived = false)
        {
            BattleShortCut.battleSceneGridManager.RemoveAllGridEffect(GridEffectPattern.Active);
            var entityMap = EntityViewManager.instance.GetEntityViewMap();
            EntityView currentSelectEntity = BattleOpMode.Instance.SelectEntity;
            foreach (var item in entityMap)
            {
                EntityView entityView = item.Value;
                if (CanActive(entityView))
                {
                    if (showActived && currentSelectEntity == entityView)
                    {
                        if (entityView.GetLocatedPosition() == entityView.GetPinnedPosition())
                        {
                            BattleShortCut.battleSceneGridManager.AddGirdEffect(entityView.GetLocatedPosition(), GridEffectPattern.Actived);
                        }
                    }
                    else
                    {
                        BattleShortCut.battleSceneGridManager.AddGirdEffect(entityView.GetLocatedPosition(), GridEffectPattern.Activable);
                    }
                }
            }
        }

        public static List<GridPosition> GetDangerRangeGrid(MovePathRule4InputPreview movePathRule)
        {
            HashSet<GridPosition> dangerGrids = new HashSet<GridPosition>();
            List<MoveGridPosition> moveRange = new List<MoveGridPosition>();
            List<SkillGridPosition> attackRange = new List<SkillGridPosition>();

            var entityMap = EntityViewManager.instance.GetEntityViewMap();
            foreach (var item in entityMap)
            {
                EntityView entityView = item.Value;
                if (IsEnemyEntity(entityView))
                {
                    moveRange.Clear();
                    movePathRule.SetEntity(entityView);
                    entityView.TryGetMoveRange(ref moveRange, movePathRule);
                    attackRange.Clear();
                    entityView.TryGetNormalSkillRange(moveRange, ref attackRange);
                    foreach(SkillGridPosition skillGrid in attackRange)
                    {
                        dangerGrids.Add(skillGrid.m_grid);
                    }
                }
            }
            return dangerGrids.ToList();
        }


        public static void ShowEntityOccupyGrid(EntityView entityView, Int32 leastOccupyGridCount = 1, float duration = 1f)
        {
            if (entityView == null)
            {
                return;
            }

            int occupySize = entityView.GetOccupySize();
            if (occupySize > leastOccupyGridCount)
            {
                GridPosition location = entityView.GetLocation();
                List<GridPosition> occupyGrids = new List<GridPosition>();
                int occupyOffset = (occupySize - 1) / 2;
                for (int i = -occupyOffset; i <= occupyOffset; ++i)
                {
                    for (int j = -occupyOffset; j <= occupyOffset; ++j)
                    {
                        GridPosition pos = location + new GridPosition(i, j);
                        occupyGrids.Add(pos);
                    }
                }
                foreach (GridPosition grid in occupyGrids)
                {
                    BattleShortCut.battleSceneGridManager.AddGirdEffect(grid, GridEffectPattern.SkillEffectBlue);
                }
                TimerManager.instance.Start(duration, () =>
                {
                    foreach (GridPosition grid in occupyGrids)
                    {
                        BattleShortCut.battleSceneGridManager.RemoveGridEffect(grid, GridEffectPattern.SkillEffectBlue);
                    }
                });
            }
        }


        public static void UpdateEntityAttackRangeSignRelationship(Boolean isShow, EntityView sourceEntityView = null)
        {
            EventManager.instance.Broadcast(EventID.Entity_SignRelationshipHide);

            if (isShow)
            {
                Int32 skillUid = 0;
                var normalAttack = sourceEntityView.GetNormalAttack();
                if (normalAttack != null)
                {
                    skillUid = normalAttack.uid;
                }
                var entityMap = EntityViewManager.instance.GetEntityViewMap();
                foreach (var item in entityMap)
                {
                    EntityView targetEntityView = item.Value;
                    if (BattleModeHelper.IsEnemyEntity(targetEntityView)) { }
                    if (sourceEntityView.CanAttack(targetEntityView))
                    {
                        Int32 relation = BattleUIUtility.GetRelationship(sourceEntityView, skillUid, targetEntityView);
                        EventManager.instance.Broadcast(EventID.Entity_SignRelationshipUpdate, targetEntityView.uid, relation);
                    }
                }
            }
        }


        /// <summary>
        ///  技能预览-----血量信息
        /// </summary>
        /// <param name="isShow"></param>
        /// <param name="sourceEntityView"></param>
        /// <param name="standGrid"></param>
        /// <param name="skillUid"></param>
        /// <param name="stepGrids"></param>
        /// <param name="entityHpPreviewDict"></param>
        public static void PreviewBloodChange(
            Boolean isShow, 
            EntityView sourceEntityView, 
            GridPosition standGrid, 
            Int32 skillUid, 
            List<GridPosition> stepGrids, 
            ref Dictionary<Int32, Int32> entityHpPreviewDict)
        {
            if (isShow)
            {
                entityHpPreviewDict.Clear();
                TargetSelectParamContainer paramContainer = BattleShortCut.sampleBattle.FetchObj<TargetSelectParamContainer>();
                for (int i = 0; i < stepGrids.Count; ++i)
                {
                    paramContainer.PushGridPosition(stepGrids[i]);
                }
                SkillForcastResult forcastResult = BattleShortCut.ForcastSkill(sourceEntityView, standGrid, skillUid, paramContainer);
                foreach (SkillEffectForcastEntityResult result in forcastResult.effectEntityResultList)
                {
                    Int32 tergetEntityUid = result.entityUid;
                    EntityView targetEntityView = EntityViewManager.instance.GetEntityView(tergetEntityUid);
                    if (targetEntityView == null)
                    {
                        continue;
                    }
                    FixedValue oldHp = targetEntityView.GetCurHp();
                    FixedValue newHp = result.curHp;
                    Int32 hpChangeVlaue = (Int32)(newHp - oldHp);
                    entityHpPreviewDict[tergetEntityUid] = hpChangeVlaue;
                    EventManager.instance.Broadcast(EventID.Entity_ShowBloodChangePreview, tergetEntityUid, hpChangeVlaue);
                }
                forcastResult.Release();
            }
            else
            {
                entityHpPreviewDict.Clear();
                EventManager.instance.Broadcast(EventID.Entity_HideBloodChangePreview, 0);
            }
        }


        public static Boolean IsRecommendEnable()
        {
            Boolean flag = false;
            var curStageInfo = BattleShortCut.sampleBattle.GetCurStageInfo();
            if (curStageInfo != null && curStageInfo.functionEnable.recommand)
            {
                flag = GamePlayerContext.instance.ModuleProvider.SettingModule.GetSkillRecommendFlag();
            }
            return flag;
        }

        public static void ShowDebugInfo(BattleTeamDecisionResult result)
        {
            StringBuilder sb = new StringBuilder();
            if (result != null)
            {
                sb.Append($"EntityUid: {result.entityUid}, ");
                sb.Append($"SkillUid: {result.skillUid}, ");
                sb.Append($"MoveGrid: {result.movePos}, ");
                sb.Append($"StepPosListCount: {result.stepPosList.Count}, StepPosList[0]: {result.stepPosList.GetValueSafely(0)}.");
            }
            else
            {
                sb.Append("Result = null.");
            }
            UnityEngine.Debug.Log(sb.ToString());
        }


        public static void ShowOccupyMultiGridEntityRange()
        {
            List<EntityView> multiGridEntityList = new List<EntityView>();
            var entityMap = EntityViewManager.instance.GetEntityViewMap();
            foreach (var item in entityMap)
            {
                if (item.Value.entityType != EntityType.Actor)
                {
                    continue;
                }
                Int32 occupySize = item.Value.GetOccupySize();
                if (occupySize > 1)
                {
                    multiGridEntityList.Add(item.Value);
                }
            }
            BattleShortCut.battleSceneGridManager.ShowMultiGridEntityRange(multiGridEntityList);
        }


        public static void PlayIdleAnimation(this BattleModeStateBase modeState, ActorView sourceActorView, Int32 targetEntityUid, Boolean switch2combatIdle)
        {
            if (sourceActorView != null)
            {
                EntityAnimatorBehaviour animBehaviour = sourceActorView.GetBehaviour<EntityAnimatorBehaviour>();
                if (switch2combatIdle)
                {
                    animBehaviour.PlayAnimationOnce("Combat_Announce", 0f, interrupt =>
                    {
                        if (!interrupt)
                        {
                            animBehaviour.PlayAnimationLoop(sourceActorView.animationConfig.combatIdle, 0);
                        }
                    });
                }
                else
                {
                    animBehaviour.PlayAnimationLoop(sourceActorView.animationConfig.normalIdle, 0.2f);
                }
            }
            ActorView targetActorView = EntityViewManager.instance.GetEntityView(targetEntityUid) as ActorView;
            if (targetActorView != null && !targetActorView.BanAnimation())
            {
                EntityAnimatorBehaviour animBehaviour = targetActorView.GetBehaviour<EntityAnimatorBehaviour>();
                if (switch2combatIdle)
                {
                    animBehaviour.PlayAnimationOnce("Combat_Announce", 0f, interrupt =>
                    {
                        if (!interrupt)
                        {
                            animBehaviour.PlayAnimationLoop(targetActorView.animationConfig.combatIdle, 0);
                        }
                    });
                }
                else
                {
                    animBehaviour.PlayAnimationLoop(targetActorView.animationConfig.normalIdle, 0.2f);
                }
            }
        }


    }
}
