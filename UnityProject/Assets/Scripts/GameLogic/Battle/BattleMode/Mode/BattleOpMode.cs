
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;


namespace Phoenix.GameLogic.Battle
{
    public class BattleOpMode : CommonOpModeBase
    {
        public static BattleOpMode Instance { get; private set; }

        protected Int32 m_normalAttackSkillUid;
        protected Int32 m_selectSkillUid;
        protected List<MoveGridPosition> m_moveRange = new List<MoveGridPosition>();
        protected List<SkillGridPosition> m_skillMaxRange = new List<SkillGridPosition>(); // 遍历移动范围后计算的最大技能范围
        protected List<SkillGridPosition> m_skillMinRange = new List<SkillGridPosition>(); // 基于当前位置的最小技能范围
        protected List<SkillGridPosition> m_skillEffectRange = new List<SkillGridPosition>(); // 技能效果范围（存在护卫类型的特殊效果）

        protected List<GridPosition> m_skillStepTargets = new List<GridPosition>();
        protected EntityView m_selectEntity;
        protected GhostEntity m_GhostEntity = new GhostEntity();


        #region Property
        public GhostEntity GhostEntity => m_GhostEntity;
        public EntityView SelectEntity => m_selectEntity;
        public GridPosition SelectEntityPosition => SelectEntity.GetPinnedPosition();
        public GridPosition SelectEntityRawPosition => m_selectEntity.GetLocatedPosition();
        public Int32 SelectEntityUid => m_activeEntityUid;
        public GridPosition TargetGridPosition
        {
            get 
            {
                if (m_skillStepTargets.Count == 0)
                {
                    return GridPosition.invalid;
                }
                return m_skillStepTargets[SkillTargetCount - 1];
            }
        }
        public Int32 SelectEntityNormalAttackSkillUid => m_normalAttackSkillUid;
        public Int32 SelectSkillUid => m_selectSkillUid;
        public GridPosition CurrentSelectedGrid => m_currentSelectedGrid;


        public List<MoveGridPosition> MoveRange => m_moveRange;
        public List<SkillGridPosition> MinSkillRange => m_skillMinRange;
        public List<SkillGridPosition> MaxSkillRange => m_skillMaxRange;
        public List<SkillGridPosition> SkillEffectRange => m_skillEffectRange;
        public List<GridPosition> SkillTargets => m_skillStepTargets;
        public Int32 SkillTargetCount => m_skillStepTargets.Count;
        public Int32 PreActiveEntityUid { get; set; }

        #endregion


        #region Init

        public BattleOpMode() : base(BattleOpModeId.BattleOpMode)
        {
            Instance = this;

            m_states[typeof(SkillPerformanceState)] = new SkillPerformanceState(this);
            m_states[typeof(BattleDefaultState)] = new BattleDefaultState(this);
            m_states[typeof(SelectDeactiveEntityState)] = new SelectDeactiveEntityState(this);
            m_states[typeof(SelectActiveEntityState)] = new SelectActiveEntityState(this);

            m_states[typeof(CastSkill_SelectDir)] = new CastSkill_SelectDir(this);
            m_states[typeof(CastSkill_SelectGrid)] = new CastSkill_SelectGrid(this);
            m_states[typeof(CastSkill_SelectSelf)] = new CastSkill_SelectSelf(this);
            m_states[typeof(CastSkill_SelectTarget)] = new CastSkill_SelectTarget(this);
            m_states[typeof(CastSkill_Summon)] = new CastSkill_Summon(this);
            m_states[typeof(CastSkill_TeleportSelf)] = new CastSkill_TeleportSelf(this);
            m_states[typeof(CastSkill_TeleportTarget)] = new CastSkill_TeleportTarget(this);
        }

        public override void Init()
        {
            base.Init();
            ChangeStateInternal<BattleDefaultState>(ModeStateContext.Fetch(true));
        }

        public override void UnInit()
        {
            base.UnInit();
            PreActiveEntityUid = 0;
            ResetSelectEntity();
        }

        public override void Dispose()
        {
            base.Dispose();
            Instance = null;
        }

        public override void Reset()
        {
            m_activeEntityUid = -1;

            m_moveRange.Clear();
            m_skillMaxRange.Clear();
            m_selectSkillUid = 0;
            m_skillMinRange.Clear();
            m_skillStepTargets.Clear();
            m_selectEntity = null;
            m_normalAttackSkillUid = -1;
            HideGhostEntity();
        }

        public override void SelectEntityView(EntityView entityView, Boolean activable)
        {
            m_activeEntityUid = -1;
            m_selectEntity = entityView;
            m_moveRule4InputMove.SetEntity(m_selectEntity);
            m_moveRule4Preview.SetEntity(m_selectEntity);

            if (m_selectEntity != null)
            {
                Skill normalAttackSkill = entityView.GetNormalAttack();
                if (normalAttackSkill != null)
                {
                    m_normalAttackSkillUid = normalAttackSkill.uid;
                }
                m_activeEntityUid = m_selectEntity.uid;
                m_selectEntity.TryGetMoveRange(ref m_moveRange, m_moveRule4Preview);
            }
        }

        #endregion


        #region Public for State
        public void ResetSelectEntity()
        {
            m_selectEntity?.ResetLocatedPosition();
            Reset();
            m_preSelectedGrid = GridPosition.invalid;
            m_currentSelectedGrid = GridPosition.invalid;
        }
        public void SelectSkill(Int32 skillUid)
        {
            m_skillMaxRange.Clear();
            m_skillMinRange.Clear();
            m_skillEffectRange.Clear();
            m_selectSkillUid = skillUid;
        }
        public void PushSkillTarget(GridPosition grid)
        {
            m_skillStepTargets.Add(grid);
        }
        public void PopSkillTarget(Boolean popAll = false)
        {
            if (popAll)
            {
                m_skillStepTargets.Clear();
            }
            else if (m_skillStepTargets.Count > 0)
            {
                m_skillStepTargets.RemoveAt(m_skillStepTargets.Count - 1);
            }

        }


        public void UpdateSkillMinRange()
        {
            if (m_selectEntity == null)
            {
                return;
            }
            m_selectEntity.TryGetSkillMinRange(m_selectSkillUid, m_skillStepTargets, ref m_skillMinRange);
        }

        public void UpdateSkillMaxRange(Boolean caculateSelectable = true)
        {
            if (m_selectEntity == null)
            {
                return;
            }
            if (m_selectEntity.HasExtraMoveChance())
            {
                return;
            }
            if (caculateSelectable)
            {
                m_selectEntity.TryGetSkillMaxRange(m_selectSkillUid, m_moveRange, ref m_skillMaxRange);
                m_selectEntity.TryGetSkillMinRange(m_selectSkillUid, m_skillStepTargets, ref m_skillMinRange);
                m_selectEntity.UpdateSkillRangeSelectableState(SelectSkillUid, m_skillMinRange, m_skillMaxRange);
            }
            else
            {
                m_selectEntity.TryGetSkillMaxRange(m_selectSkillUid, m_moveRange, ref m_skillMaxRange);
            }
        }

        public void UpdateSkillEffectRange()
        {
            if (m_selectEntity == null)
            {
                return;
            }
            m_selectEntity.TryGetSkillEffectRange(m_selectSkillUid, m_skillStepTargets, ref m_skillEffectRange);
        }

        public Boolean CanSelectGridDoMove(GridPosition gridPosition)
        {
            foreach(MoveGridPosition skillGridPosition in m_moveRange)
            {
                if (skillGridPosition.m_stayFlag && skillGridPosition.m_grid == gridPosition)
                {
                    return true;
                }
            }
            return false;
        }
        public Boolean CanSelectMaxSkillRangeDoAttack(GridPosition gridPosition)
        {
            foreach (SkillGridPosition skillGridPosition in m_skillMaxRange)
            {
                if (skillGridPosition.m_selectFlag && skillGridPosition.m_grid == gridPosition)
                {
                    return true;
                }
            }
            return false;
        }
        public Boolean CanSelectMinSkillRangeDoAttack(GridPosition gridPosition)
        {
            foreach (SkillGridPosition skillGridPosition in m_skillMinRange)
            {
                if (skillGridPosition.m_selectFlag && skillGridPosition.m_grid == gridPosition)
                {
                    return true;
                }
            }
            return false;
        }
        public Boolean CanSelectEntityDoAttack(EntityView entityView, ref GridPosition gridPosition)
        {
            if (CanSelectMaxSkillRangeDoAttack(gridPosition))
            {
                return true;
            }
            Int32 occupySize = entityView.GetOccupySize();
            if (occupySize > 1)
            {
                List<GridPosition> posList = new List<GridPosition>();
                entityView.CollectOccupiedPos(posList);
                GridPosition entityGridPosition = SelectEntityRawPosition;
                posList.Sort((a, b) => a.DistanceTo(entityGridPosition).CompareTo(b.DistanceTo(entityGridPosition)));

                foreach (GridPosition tempGridPosition in posList)
                {
                    if (CanSelectMaxSkillRangeDoAttack(tempGridPosition))
                    {
                        gridPosition = tempGridPosition;
                        return true;
                    }
                }
                //判断大体型
                //GridPosition nearestGrid = entityView.GetNearestOccupiedPos(m_battleMode.SelectEntityRawPosition);
                //if (nearestGrid.isValid)
                //{
                //    gridPosition = nearestGrid;
                //    return m_battleMode.CanSelectTargetAttackGrid(nearestGrid);
                //}
            }
            return false;
        }


        public void UpdateDangerRangeGrid(Boolean switchState = false)
        {
            Boolean showDangerRangeFlag = GamePlayerContext.instance.ModuleProvider.SettingModule.GetShowDangerRangeFlag();
            if (switchState)
            {
                GamePlayerContext.instance.ModuleProvider.SettingModule.SetShowDangerRangeFlag(!showDangerRangeFlag);
            }
            BattleShortCut.battleSceneGridManager.HideAllGridByType(GridType.DangerRange);
            if (showDangerRangeFlag)
            {
                List<GridPosition> dangerGrids = BattleModeHelper.GetDangerRangeGrid(MoveRule4Preview);
                BattleShortCut.battleSceneGridManager.ShowDangerRange(dangerGrids);
            }
        }
        public void UpdateEntityAttackRangeSignRelationship(Boolean isShow)
        {
            BattleModeHelper.UpdateEntityAttackRangeSignRelationship(isShow, m_selectEntity);
        }
        public void SetTurnFinish()
        {
            var curTeam = BattleShortCut.sampleBattle.GetTeamByIndex(BattleShortCut.sampleBattle.GetCurTeamIndex());
            if (curTeam.controlledPlayer.playerId == BattleShortCut.hostPlayerId)
            {
                EventManager.instance.Broadcast(EventID.BattleControl_WaitAll, curTeam.uid);
            }
        }
        /// <summary>
        /// 获取技能目标的最近格子位置
        /// </summary>
        /// <param name="result">技能目标位置</param>
        /// <param name="skillUid">技能Uid</param>
        /// <returns></returns>
        public GridPosition GetClosestAttackGridPosition(GridPosition targetGridPos, Int32 skillUid)
        {
            List<GridPosition> attackGridPosList = new List<GridPosition>();
            Skill skill = SelectEntity.GetSkillBySkillUid(skillUid);
            SkillInfo skillInfo = skill.skillInfo;
            var stepInfo = skillInfo.selectStep;
            var fieldSummary = BattleShortCut.sampleBattle.CreateFieldSummaryForPosCollection();
            //TODO: RRR
            TargetSelectUtility.AppendFirstRange(SelectEntity.GetBattle(), stepInfo, SelectEntity, targetGridPos, skill.GetExtraSelectRange(), fieldSummary);
            attackGridPosList.AddRange(fieldSummary.GetPosList());
            fieldSummary.Release();

            GridPosition currentStandGrid = m_selectEntity.GetLocatedPosition();
            GridPosition result = GridPosition.invalid;
            while (attackGridPosList.Count > 0)
            {
                GridPosition gridPos = attackGridPosList[0];
                attackGridPosList.RemoveAt(0);
                if (gridPos == currentStandGrid)
                {
                    result = gridPos;
                    break;
                }
                if (!SelectEntity.CheckLocatable(gridPos) || !CanSelectGridDoMove(gridPos))
                {
                    continue;
                }
                if (currentStandGrid.DistanceTo(gridPos) < currentStandGrid.DistanceTo(result))
                {
                    result = gridPos;
                }
            }
            return result;
        }
        public Boolean TryGetImmediatelyActionEntity(out EntityView extraActionEntity)
        {
            Boolean result = false;
            extraActionEntity = null;
            var entityViewMap = EntityViewManager.instance.GetEntityViewMap();
            foreach(var item in entityViewMap)
            {
                if (BattleModeHelper.IsHostTeamEntity(item.Value) && (item.Value.HasExtraActionChance() || item.Value.HasExtraMoveChance()))
                {
                    result = true;
                    extraActionEntity = item.Value;
                    break;
                }
            }
            return result;
        }

        #endregion

        #region Override

        protected override bool CanThroughGrid(GridPosition grid)
        {
            foreach(MoveGridPosition movePosition in m_moveRange)
            {
                if (movePosition.m_grid == grid)
                {
                    return true;
                }
            }
            return base.CanThroughGrid(grid);
        }

        public override void ChangeState(BattleOpModeState stateType, ModeStateContext context = null)
        {
            switch (stateType)
            {
                case BattleOpModeState.Default:
                    ChangeStateInternal<BattleDefaultState>(context);
                    break;
                case BattleOpModeState.SkillPerformance:
                    ChangeStateInternal<SkillPerformanceState>(context);
                    break;
                case BattleOpModeState.StoryPerformance:
                    ChangeStateInternal<StoryPerformanceState>(context);
                    break;
            }
        }

        #endregion


        #region GhostEntity

        public void ShowGhostEntity()
        {
            if (m_GhostEntity.Alive && m_GhostEntity.Uid == m_selectEntity.uid)
            {
                return;
            }
            HideGhostEntity();
            ActorConfigData actorConfig = ConfigDataManager.instance.GetActor(m_selectEntity.rid);
            if (actorConfig != null)
            {
                EntitySkinConfigData skinConfig = ConfigDataManager.instance.GetEntitySkin(actorConfig.SkinId);
                if (skinConfig != null)
                {
                    GameObject go = ResourceHandleManager.instance.SpawnGameObject(skinConfig.PrefabPath);
                    m_GhostEntity.Init(m_selectEntity.uid, go);
                    m_GhostEntity.Go.transform.position = m_selectEntity.transform.position;
                    m_GhostEntity.Go.transform.forward = m_selectEntity.transform.forward;
                    EventManager.instance.Broadcast<Boolean, ActorView>(EventID.EntityGhostViewAddOrRemove, true, m_GhostEntity.ActorView);
                }
            }
        }
        public void HideGhostEntity()
        {
            if (m_GhostEntity.Alive)
            {
                ResourceHandleManager.instance.DespawnGameObject(m_GhostEntity.Go);
                m_GhostEntity.UnInit();
                EventManager.instance.Broadcast<Boolean, ActorView>(EventID.EntityGhostViewAddOrRemove, false, null);
            }
        }
        public void UpdateGhostEntityPosition(GridPosition preStandGrid, GridPosition standGrid)
        {
            if (m_GhostEntity.Alive == false)
            {
                return;
            }

            Vector3 tragetWorldPosition = BattleShortCut.GetGridWorldPosition(standGrid);
            m_GhostEntity.Go.transform.position = tragetWorldPosition;
            if (preStandGrid.isValid)
            {
                Vector3 preTargetWorldPosition = BattleShortCut.GetGridWorldPosition(preStandGrid);
                Vector3 forward = tragetWorldPosition - preTargetWorldPosition;
                forward.y = 0f;
                m_GhostEntity.Go.transform.forward = forward.normalized;
            }
            else
            {
                m_GhostEntity.Go.transform.forward = m_selectEntity.transform.forward;
            }
        }
        public void UpdateGhostEntityPositionStandard(GridPosition standGridPosition, GridPosition targetGridPosition)
        {
            if (m_GhostEntity.Alive == false)
            {
                return;
            }
            if (standGridPosition == targetGridPosition)
            {
                return;
            }
            Vector3 standWorldPosition = BattleShortCut.GetGridWorldPosition(standGridPosition);
            m_GhostEntity.Go.transform.position = standWorldPosition;
            if (targetGridPosition.isValid)
            {
                Vector3 targetWorldPosition = BattleShortCut.GetGridWorldPosition(targetGridPosition);
                Vector3 forward = targetWorldPosition - standWorldPosition;
                forward.y = 0f;
                m_GhostEntity.Go.transform.forward = forward.normalized;
            }
            else
            {
                m_GhostEntity.Go.transform.forward = m_selectEntity.transform.forward;
            }
        }

        #endregion


        #region Input Events
        public override void EventOnSelectGrid(GridPosition gridPosition)
        {
            OnSelectGridInternal(gridPosition);
        }
        public override void EventOnInputClick(InputData eventData)
        {
            GridPosition selectedGrid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
            OnSelectGridInternal(selectedGrid);
        }
        public override void EventOnBattleUIClick(BattleUIEventID eventId, params Int32[] argvs)
        {
            switch (eventId)
            {
                case BattleUIEventID.WaitButton:
                    m_currentState?.EntityWait();
                    break;
                case BattleUIEventID.SelectSkill:
                    Int32 skillUid = argvs.GetValueSafely(0, 0);
                    if (skillUid > 0)
                    {
                        m_currentState?.SelectSkill(skillUid);
                    }
                    else
                    {
                        Debug.LogError($"SkillUid Error value = {skillUid}");
                    }
                    break;
                case BattleUIEventID.CancelButton:
                    m_currentState?.SkillCancel();
                    break;
                case BattleUIEventID.ConfirmButton:
                    m_currentState?.SkillConfirm();
                    break;
                case BattleUIEventID.FocusNextButton:
                case BattleUIEventID.DangerRangeButton:
                case BattleUIEventID.TurnFinishBtnClick:
                case BattleUIEventID.RecommendCastSkillBtn:
                case BattleUIEventID.RecommendSkillOnBtn:
                case BattleUIEventID.RecommendSkillOffBtn:
                case BattleUIEventID.ShowEntityDetailInfoBtn:
                    m_currentState?.BattleUIClick(eventId, argvs);
                    break;
            }
        }
        public override void EventOnKeyboardKeyDown(KeyCode keyCode)
        {
            if (keyCode == KeyCode.Tab)
            {
                InputData inputData = new InputData();
                inputData.m_inputType = InputType.Keyboard;
                inputData.m_keycode = keyCode;
                m_currentState.KeyboardKeyDown(inputData);
            }
        }
        #endregion

        #region Private
        private void OnSelectGridInternal(GridPosition gridPosition)
        {
            m_preSelectedGrid = m_currentSelectedGrid;
            m_currentSelectedGrid = gridPosition;
            BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(true, gridPosition);
            m_currentState.SelectGrid(gridPosition);
        }

        #endregion

    }

}
