
using System;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class RetractOpMode : CommonOpModeBase
    {
        public RetractOpMode() : base(BattleOpModeId.RetractOpMode)
        {
            m_states[typeof(RetractDefaultState)] = new RetractDefaultState(this);
        }

        public override void Init()
        {
            base.Init();
            ChangeStateInternal<RetractDefaultState>();
        }

        public override void UnInit()
        {
            base.UnInit();
            Reset();
        }

        public override void Reset()
        {
            base.Reset();
        }


        #region Input Events
        public override void EventOnSelectGrid(GridPosition gridPosition)
        {
            OnSelectGridInternal(gridPosition);
        }

        public override void EventOnInputClick(InputData eventData)
        {
            GridPosition gridPosition = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
            OnSelectGridInternal(gridPosition);
        }

        public override void EventOnBattleUIClick(BattleUIEventID eventId, params Int32[] argvs) { m_currentState.BattleUIClick(eventId, argvs); }

        #endregion


        #region Private
        private void OnSelectGridInternal(GridPosition gridPosition)
        {
            m_currentSelectedGrid = gridPosition;
            BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(true, gridPosition);
            m_currentState.SelectGrid(gridPosition);
        }
        #endregion
    }
}
