
using System;
using System.Collections.Generic;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class PrepareOpMode : CommonOpModeBase
    {
        private Dictionary<Int32, List<GridPosition>> m_teamFormationGrids = new Dictionary<Int32, List<GridPosition>>();
        private List<Int32> m_ownerTeams = new List<Int32>();

        #region Init

        public PrepareOpMode() : base(BattleOpModeId.PrepareOpMode)
        {
            m_states[typeof(PrepareDefaultState)] = new PrepareDefaultState(this);
            m_states[typeof(SelectOwnerEntityState)] = new SelectOwnerEntityState(this);
            m_states[typeof(SelectOtherEntityState)] = new SelectOtherEntityState(this);
        }

        public override void Init()
        {
            base.Init();
            InitializeTeamFormationGrid();
            ChangeStateInternal<PrepareDefaultState>();
        }

        public override void UnInit()
        {
            base.UnInit();
            Reset();
            ResetFormationGrid();
        }

        public override void Reset()
        {
            base.Reset();
            m_activeEntityUid = -1;
        }

        public Boolean TryGetFormationTeamId(GridPosition gridPosition, out Int32 teamId)
        {
            foreach (var item in m_teamFormationGrids)
            {
                foreach (var grid in item.Value)
                {
                    if (grid == gridPosition)
                    {
                        teamId = item.Key;
                        return true;
                    }
                }
            }
            teamId = -1;
            return false;
        }
        public Boolean IsFormationGrid(Int32 teamId, GridPosition gridPosition)
        {
            if (m_teamFormationGrids.TryGetValue(teamId, out List<GridPosition> grids))
            {
                foreach (var grid in grids)
                {
                    if (grid == gridPosition)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        #endregion

        #region private

        private void InitializeTeamFormationGrid()
        {
            ResetFormationGrid();
            int hostPlayerSlotId = BattleShortCut.sampleBattle.GetPlayerSlotIdByPlayerId(BattleShortCut.hostPlayerId);
            foreach (var team in BattleShortCut.sampleBattle.GetTeamList())
            {
                if (team.controlledPlayerSlotId == hostPlayerSlotId)
                {
                    m_ownerTeams.Add(team.uid);
                }
            }

            List<BattleStageDispositionInfo> dispositions = BattleShortCut.sampleBattle.GetCurStageDispositionInfoList();
            if (dispositions != null && dispositions.Count > 0)
            {
                foreach (BattleStageDispositionInfo info in dispositions)
                {
                    if (!m_teamFormationGrids.TryGetValue(info.teamId, out List<GridPosition> grids))
                    {
                        grids = new List<GridPosition>();
                        m_teamFormationGrids.Add(info.teamId, grids);
                    }
                    grids.Add(info.position);
                }
            }
        }

        private void ResetFormationGrid()
        {
            foreach (var grids in m_teamFormationGrids)
            {
                grids.Value.Clear();
            }
            m_teamFormationGrids.Clear();
            m_ownerTeams.Clear();
        }

        #endregion


        #region Input Events
        public override void EventOnSelectGrid(GridPosition gridPosition)
        {
            OnSelectGridInternal(gridPosition);
        }


        public override void EventOnInputClick(InputData eventData)
        {
            if (eventData.m_battleUIEventId == BattleUIEventID.FormationOperation)
            {
                m_currentState?.BattleUIClick(eventData.m_battleUIEventId, eventData.m_paramter1);
            }
            else
            {
            }
            GridPosition gridPosition = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
            OnSelectGridInternal(gridPosition);
        }
        public override void EventOnInputBegan(InputData eventData)
        {
            m_currentState?.InputBegan(eventData);
        }
        public override void EventOnInputMoved(InputData eventData)
        {
            m_currentState?.InputMoved(eventData);
        }
        public override void EventOnInputEnded(InputData eventData)
        {
            m_currentState?.InputEnded(eventData);
        }
        public override void EventOnBattleUIClick(BattleUIEventID eventId, params Int32[] argvs) { m_currentState.BattleUIClick(eventId, argvs); }


        #endregion


        #region Private
        private void OnSelectGridInternal(GridPosition gridPosition)
        {
            m_currentSelectedGrid = gridPosition;
            BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(true, gridPosition);
            m_currentState.SelectGrid(gridPosition);
        }
        #endregion
    }
}
