using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public class AutoBattleOpMode : CommonOpModeBase
    {
        public static AutoBattleOpMode Instance { get; private set; }

        public Int32 MaxAICount = 2;

        public BattleOpModeState CurOpModeState { get; set; }

        public Int32 CurAIIndex { get; set; }

        public bool IsInputMode { get; set; }

        public bool IsProcessCommandSelect { get; set; }

        public bool IsProcessCommandMove { get; set; }

        public bool IsWatingForSkillPerformance { get; set; }

        public bool IsBlockPerfomance { get; set; }

        // Detail View ================= start =================
        public Int32 SelectEntityUid => m_activeEntityUid;

        protected EntityView m_selectEntity;
        public EntityView SelectEntity => m_selectEntity;

        protected Int32 m_normalAttackSkillUid;
        protected List<MoveGridPosition> m_moveRange = new List<MoveGridPosition>();
        protected List<SkillGridPosition> m_attackRange = new List<SkillGridPosition>();
        public List<MoveGridPosition> MoveRange => m_moveRange;
        public List<SkillGridPosition> AttackRange => m_attackRange;
        public GridPosition SelectEntityPosition => SelectEntity.GetPinnedPosition();
        // Detail View ================= end =================

        public TeamDecisionMarkId CurTeamDecisionMarkId { get; set; }

        private List<int> m_listBattleTeamDecisionIds = new List<int>();
        private List<BattleTeamDecisionData> m_listBattleTeamDecisionDatas = new List<BattleTeamDecisionData>();

        private List<TeamDecisionMarkConfigData> m_listTeamDecisionMarkDefault = new List<TeamDecisionMarkConfigData>();

        public Dictionary<TeamDecisionMarkId, BattleTeamDecisionMark> m_dictTeamDecisionSelect = new Dictionary<TeamDecisionMarkId, BattleTeamDecisionMark>();

        public AutoBattleOpMode() : base(BattleOpModeId.AutoBattleOpMode)
        {
            Instance = this;
            m_states[typeof(AutoBattleDefaultState)] = new AutoBattleDefaultState(this);
            m_states[typeof(SelectCommandState)] = new SelectCommandState(this);
            m_states[typeof(SkillPerformanceState)] = new SkillPerformanceState(this);
            m_states[typeof(StoryPerformanceState)] = new StoryPerformanceState(this);
            m_states[typeof(AutoBattleSelectEntityState)] = new AutoBattleSelectEntityState(this);
        }

        #region override life cycle
        public override void Init()
        {
            base.Init();

            BattleUIStateHandler.ActiveState(BattleUIState.AutoBattleMode);

            InitDefaultAIData();
            InitDefaultCommandData();

            ChangeStateInternal<AutoBattleDefaultState>();
            CurOpModeState = BattleOpModeState.Default;
            IsProcessCommandSelect = false;
            BattleShortCut.battleSceneGridManager.ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
        }

        public override void UnInit()
        {
            base.UnInit();

            BattleControl_BlockPerfomance(false);
            BattleUIStateHandler.DeactiveState(BattleUIState.AutoBattleMode);

            IsProcessCommandSelect = false;
            m_listTeamDecisionMarkDefault.Clear();
            //m_dictTeamDecisionSelect.Clear();
            m_listBattleTeamDecisionIds.Clear();
            m_listBattleTeamDecisionDatas.Clear();
            BattleShortCut.battleSceneGridManager.ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
        }

        public override void Dispose()
        {
            base.Dispose();
            Instance = null;
        }

        public override void Reset()
        {
            m_activeEntityUid = -1;
        }

        #endregion

        #region override state
        public override void ChangeState(BattleOpModeState stateType, ModeStateContext context = null)
        {
            if (stateType != BattleOpModeState.SelectEntity && CurOpModeState == stateType)
            { 
                return;
            }

            CurOpModeState = stateType;

            switch (stateType)
            {
                case BattleOpModeState.Default:
                    {
                        if (!IsProcessCommandSelect)
                        {
                            ChangeStateInternal<AutoBattleDefaultState>(context);
                        }
                    }
                    break;
                case BattleOpModeState.SelectCommand:
                    ChangeStateInternal<SelectCommandState>(context);
                    break;
                case BattleOpModeState.SkillPerformance:
                    ChangeStateInternal<SkillPerformanceState>(context);
                    break;
                case BattleOpModeState.StoryPerformance:
                    ChangeStateInternal<StoryPerformanceState>(context);
                    break;
                case BattleOpModeState.SelectEntity:
                    ChangeStateInternal<AutoBattleSelectEntityState>(context);
                    break;

            }
        }

        public override void SelectEntityView(EntityView entityView, Boolean activable)
        {
            m_activeEntityUid = -1;
            m_selectEntity = entityView;

            m_moveRule4InputMove.SetEntity(m_selectEntity);
            m_moveRule4Preview.SetEntity(m_selectEntity);

            if (m_selectEntity != null)
            {
                Skill normalAttackSkill = entityView.GetNormalAttack();
                if (normalAttackSkill != null)
                {
                    m_normalAttackSkillUid = normalAttackSkill.uid;
                }
                m_activeEntityUid = m_selectEntity.uid;
                m_selectEntity.TryGetMoveRange(ref m_moveRange, m_moveRule4Preview);
                if (!m_selectEntity.HasExtraMoveChance())
                {
                    m_selectEntity.TryGetNormalSkillRange(m_moveRange, ref m_attackRange);
                }
            }
        }

        public void UpdateEntityAttackRangeSignRelationship(Boolean isShow)
        {
            BattleModeHelper.UpdateEntityAttackRangeSignRelationship(isShow, m_selectEntity);
        }

        public void ResetSelectEntity()
        {
            m_selectEntity?.ResetLocatedPosition();
            Reset();
        }

        public void SetTurnFinish()
        {
            var curTeam = BattleShortCut.sampleBattle.GetTeamByIndex(BattleShortCut.sampleBattle.GetCurTeamIndex());
            if (curTeam.controlledPlayer.playerId == BattleShortCut.hostPlayerId)
            {
                EventManager.instance.Broadcast(EventID.BattleControl_WaitAll, curTeam.uid);
            }
        }
        #endregion

        #region custom func

        private void InitDefaultAIData()
        {
            m_listBattleTeamDecisionIds.Clear();
            m_listBattleTeamDecisionDatas.Clear();
            var team = BattleShortCut.GetHostBattleTeam();
            if(team != null)
            {
                m_listBattleTeamDecisionIds.AddRange(team.decisionIdList);
                //foreach (var item in team.decisionDatas)
                //{
                //    m_listBattleTeamDecisionDatas.Add(item);
                //}
            }
            //MaxAICount = m_listBattleTeamDecisionDatas.Count;
            MaxAICount = m_listBattleTeamDecisionIds.Count;
            CurAIIndex = team.decisionIndex;
            int tempIndex = CurAIIndex - 2;
            if (tempIndex >= 0 && tempIndex < MaxAICount)
            {
                CurAIIndex = tempIndex;
            }
            FrameCommandSendUtility.SendChangeTeamDecision(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, BattleShortCut.hostTeamUid, CurAIIndex);
        }

        private void InitDefaultCommandData()
        {
            m_listTeamDecisionMarkDefault.Clear();
            int defaultStart = (int)TeamDecisionMarkId.Focus;
            int defaultEnd = (int)TeamDecisionMarkId.Move;
            for (int i = defaultStart; i <= defaultEnd; i++)
            {
                TeamDecisionMarkConfigData teamDecisionMarkConfigData = ConfigDataManager.instance.GetTeamDecisionMark((TeamDecisionMarkId)i);
                m_listTeamDecisionMarkDefault.Add(teamDecisionMarkConfigData);
            }

            m_listTeamDecisionMarkDefault.Sort((a, b) => a.Order.CompareTo(b.Order));
        }

        public BattleTeamDecisionData GetAIData(Int32 index)
        {
            if (index < 0 || index >= m_listBattleTeamDecisionDatas.Count) { return null; }
            return m_listBattleTeamDecisionDatas[index];
        }

        public int GetAIID(Int32 index)
        {
            if (index < 0 || index >= m_listBattleTeamDecisionIds.Count) { return 0; }
            return m_listBattleTeamDecisionIds[index];
        }

        public Int32 GetCommandCount()
        {
            return m_listTeamDecisionMarkDefault.Count;
        }

        public TeamDecisionMarkConfigData GetCommandData(Int32 index)
        {
            if (index < 0 || index >= m_listTeamDecisionMarkDefault.Count) { return null; }
            return m_listTeamDecisionMarkDefault[index];
        }

        public Int32 GetSelectedCommandCount()
        {
            return m_dictTeamDecisionSelect.Count;
        }

        public void SetTeamDecisionMarkCommandSelect(TeamDecisionMarkId teamDecisionMarkId, GridPosition gridPos, EntityView entityView = null)
        {
            BattleTeamDecisionMark commandSelect = null;
            if (!m_dictTeamDecisionSelect.TryGetValue(teamDecisionMarkId, out commandSelect))
            {
                commandSelect = ClassPoolManager.instance.Fetch<BattleTeamDecisionMark>();
                m_dictTeamDecisionSelect.Add(teamDecisionMarkId, commandSelect);
            }

            commandSelect.markId = teamDecisionMarkId;
            commandSelect.pos = gridPos;
            commandSelect.entityUid = entityView != null ? entityView.uid : 0;

            if (entityView != null)
            {
                EventManager.instance.Broadcast(EventID.Entity_Command_Added, entityView.uid, teamDecisionMarkId);
            }
        }

        public void SendCancelTeamDecisionMark(TeamDecisionMarkId teamDecisionMarkId)
        {
            if (m_dictTeamDecisionSelect.ContainsKey(teamDecisionMarkId))
            {
                BattleTeamDecisionMark commandSelect = m_dictTeamDecisionSelect[teamDecisionMarkId];
                EntityView entityView = EntityViewManager.instance.GetEntityView(commandSelect.entityUid);
                FrameCommandSendUtility.SendCancelTeamDecisionMark(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, entityView.GetTeamUid(), (int)teamDecisionMarkId);
            }
        }

        public void RemoveTeamDecisionMarkCommandSelect(TeamDecisionMarkId teamDecisionMarkId)
        {
            if (m_dictTeamDecisionSelect.ContainsKey(teamDecisionMarkId))
            {
                BattleTeamDecisionMark commandSelect = m_dictTeamDecisionSelect[teamDecisionMarkId];
                //EntityView entityView = EntityViewManager.instance.GetView(commandSelect.entityUid);
                //FrameCommandSendUtility.SendCancelTeamDecisionMark(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, entityView.GetTeamUid(), (int)teamDecisionMarkId);

                EventManager.instance.Broadcast(EventID.Entity_Command_Removed, commandSelect.entityUid, teamDecisionMarkId);
                m_dictTeamDecisionSelect.Remove(teamDecisionMarkId);
                if (teamDecisionMarkId == TeamDecisionMarkId.Move)
                {
                    BattleShortCut.battleSceneGridManager.ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
                }
            }
        }

        public BattleTeamDecisionMark GetBattleTeamDecisionMark(TeamDecisionMarkId teamDecisionMarkId)
        {
            BattleTeamDecisionMark commandSelect = null;
            m_dictTeamDecisionSelect.TryGetValue(teamDecisionMarkId, out commandSelect);
            return commandSelect;
        }

        public bool IsContainTeamDecisionMark(TeamDecisionMarkId teamDecisionMarkId)
        {
            return m_dictTeamDecisionSelect.ContainsKey(teamDecisionMarkId);
        }

        public void ExcuteEntityDestroyDecisionMark(Int32 entityUid)
        {
            foreach (var item in m_dictTeamDecisionSelect)
            {
                var teamDecision = item.Value;
                if (teamDecision.entityUid == entityUid)
                {
                    SendCancelTeamDecisionMark(teamDecision.markId);
                    RemoveTeamDecisionMarkCommandSelect(teamDecision.markId);
                    break;
                }
            }
        }

        public void ClearDecisionMark()
        {
            for (int i = (int)TeamDecisionMarkId.None; i <= (int)TeamDecisionMarkId.Move; i++)
            {
                TeamDecisionMarkId teamDecisionMarkId = (TeamDecisionMarkId)i;
                SendCancelTeamDecisionMark(teamDecisionMarkId);
                RemoveTeamDecisionMarkCommandSelect(teamDecisionMarkId);
            }

            m_dictTeamDecisionSelect.Clear();
        }

        public void BattleControl_BlockPerfomance(bool isBlock)
        {
            IsBlockPerfomance = isBlock;
            EventManager.instance.Broadcast(EventID.BattleControl_BlockPerformance, isBlock);
        }

        public void BattleControl_BlockPerfomanceAndWait(Action callBack)
        {
            if (IsBlockPerfomance)
            {
                callBack?.Invoke();
                return;
            }

            TeamDecisionMarkConfigData teamDecisionMarkConfigData = ConfigDataManager.instance.GetTeamDecisionMark(CurTeamDecisionMarkId);
            if (teamDecisionMarkConfigData != null)
            {
                TipUI.ShowTip(ConstStringUtility.GetConstString(ConstStringId.AiselectTips3, teamDecisionMarkConfigData.Name));
            }
            EventManager.instance.Broadcast<Action>(EventID.BattleControl_BlockPerformanceAndWait, () => {
                callBack?.Invoke();
            });
        }

        public void TryChangeToSelectCommand(bool isFromEntityDetail = false)
        {
            if (isFromEntityDetail)
            {
                ChangeToSelectCommand();
            }
            else
            {
                if (IsWatingForSkillPerformance)
                {
                    IsWatingForSkillPerformance = false;
                    ChangeToSelectCommand();
                }
            }
            
        }

        public void ChangeToSelectCommand()
        {
            IsProcessCommandSelect = true;
            ModeStateContext context = ClassPoolManager.instance.Fetch<ModeStateContext>();
            context.m_teamDecisionMarkId = CurTeamDecisionMarkId;
            BattleOpModeManager.instance.ChangeModeState(BattleOpModeState.SelectCommand, context);
        }

        #endregion

        #region Input Events
        public override void EventOnSelectGrid(GridPosition gridPosition)
        {
            OnSelectGridInternal(gridPosition);
        }
        public override void EventOnInputClick(InputData eventData)
        {
            GridPosition selectedGrid = BattleShortCut.GetGridPositionByScreenPosition(eventData.m_position);
            OnSelectGridInternal(selectedGrid);
        }
        public override void EventOnBattleUIClick(BattleUIEventID eventId, params Int32[] argvs)
        {
            Debug.Log($"EventOnBattleUIClick =====1-1===== {eventId} ");
            switch (eventId)
            {
                case BattleUIEventID.DangerRangeButton:
                case BattleUIEventID.ShowEntityDetailInfoBtn:
                    m_currentState?.BattleUIClick(eventId, argvs);
                    break;
            }
        }
        #endregion

        #region Private
        private void OnSelectGridInternal(GridPosition gridPosition)
        {
            BattleShortCut.battleSceneGridManager.ShowOrHideSelectGrid(true, gridPosition);
            m_currentState.SelectGrid(gridPosition);
            m_preSelectedGrid = m_currentSelectedGrid;
            m_currentSelectedGrid = gridPosition;
        }

        #endregion

        #region TempFunc Review
        public void SetTeamDecisionMarkCommandSelect_Temp(TeamDecisionMarkId teamDecisionMarkId, GridPosition gridPos, EntityView entityView = null)
        {
            BattleTeamDecisionMark commandSelect = null;
            if (!m_dictTeamDecisionSelect.TryGetValue(teamDecisionMarkId, out commandSelect))
            {
                commandSelect = ClassPoolManager.instance.Fetch<BattleTeamDecisionMark>();
                m_dictTeamDecisionSelect.Add(teamDecisionMarkId, commandSelect);
            }
            else
            {
                if (entityView != null && commandSelect.entityUid != entityView.uid)
                {
                    EntityView entityViewPast = EntityViewManager.instance.GetEntityView(commandSelect.entityUid);
                    FrameCommandSendUtility.SendCancelTeamDecisionMark(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, entityViewPast.GetTeamUid(), (int)teamDecisionMarkId);
                    EventManager.instance.Broadcast(EventID.Entity_Command_Removed, commandSelect.entityUid, teamDecisionMarkId);
                }
            }
            commandSelect.markId = teamDecisionMarkId;
            commandSelect.pos = gridPos;
            commandSelect.entityUid = entityView != null ? entityView.uid : 0;

            if (entityView != null)
            {
                FrameCommandSendUtility.SendSetTeamDecisionMark(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, BattleShortCut.hostTeamUid, entityView.uid, (short)gridPos.x, (short)gridPos.y, (int)teamDecisionMarkId);
                EventManager.instance.Broadcast(EventID.Entity_Command_Added, entityView.uid, teamDecisionMarkId);
            }
            else
            {
                FrameCommandSendUtility.SendSetTeamDecisionMark(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, BattleShortCut.hostTeamUid, 0, (short)gridPos.x, (short)gridPos.y, (int)teamDecisionMarkId);
            }

        }

        public void RemoveTeamDecisionMarkCommandSelect_Temp(TeamDecisionMarkId teamDecisionMarkId)
        {
            if (m_dictTeamDecisionSelect.ContainsKey(teamDecisionMarkId))
            {
                BattleTeamDecisionMark commandSelect = m_dictTeamDecisionSelect[teamDecisionMarkId];
                EntityView entityView = EntityViewManager.instance.GetEntityView(commandSelect.entityUid);
                FrameCommandSendUtility.SendCancelTeamDecisionMark(BattleShortCut.logicBattle, BattleShortCut.hostPlayerId, entityView.GetTeamUid(), (int)teamDecisionMarkId);

                EventManager.instance.Broadcast(EventID.Entity_Command_Removed, commandSelect.entityUid, teamDecisionMarkId);
                m_dictTeamDecisionSelect.Remove(teamDecisionMarkId);
                if (teamDecisionMarkId == TeamDecisionMarkId.Move)
                {
                    BattleShortCut.battleSceneGridManager.ShowOrHideSelectCommandGrid(false, GridPosition.invalid);
                }
            }
        }
        #endregion TempFunc Review

    }

}
