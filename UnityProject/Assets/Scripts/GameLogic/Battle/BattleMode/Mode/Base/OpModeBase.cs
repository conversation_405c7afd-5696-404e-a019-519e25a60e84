
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public abstract class OpModeBase
    {
        public BattleOpModeId m_battleOpModeId;
        protected ModeStateBase m_currentState;
        protected readonly Dictionary<Type, ModeStateBase> m_states = new Dictionary<Type, ModeStateBase>();

        protected Int32 m_activeEntityUid;
        protected GridPosition m_preSelectedGrid;
        protected GridPosition m_currentSelectedGrid = GridPosition.invalid;

        public BattleOpModeId BattleOpModeId
        {
            get { return m_battleOpModeId; }
        }

        public OpModeBase(BattleOpModeId battleOpModeId)
        {
            m_battleOpModeId = battleOpModeId;
        }

        public virtual void Init() { }

        public virtual void Tick(TimeSlice ts) { }

        public virtual void Reset()
        {
            m_preSelectedGrid = GridPosition.invalid;
            m_currentSelectedGrid = GridPosition.invalid;
        }

        public virtual void UnInit()
        {
            if (m_currentState != null)
            {
                m_currentState.UnInit();
                m_currentState = null;
            }
            m_activeEntityUid = default;
            m_preSelectedGrid = GridPosition.invalid;
            m_currentSelectedGrid = GridPosition.invalid;
        }

        public virtual void Dispose()
        {
            m_states.Clear();
        }

        public virtual void SelectEntityView(EntityView entityView, Boolean activable) { }

        public virtual void ChangeState<T>(ModeStateContext context = null) where T : ModeStateBase
        {
            ChangeStateInternal(typeof(T), context);
        }

        public virtual void ChangeState(BattleOpModeState stateType, ModeStateContext context = null)
        {
        }

        #region

        public void ChangeStateInternal<T>(ModeStateContext context = null) where T : ModeStateBase
        {
            ChangeStateInternal(typeof(T), context);
        }

        protected void ChangeStateInternal(Type stateType, ModeStateContext context)
        {
            if (m_currentState != null && m_currentState.GetType() == stateType)
            {
                m_currentState.UpdateState(context);
            }
            else
            {
                if (m_currentState != null)
                {
                    m_currentState.UnInit();
                    m_currentState = null;
                }
                if (m_states.TryGetValue(stateType, out ModeStateBase state))
                {
                    m_currentState = state;
                    state.Init(context);
                }
            }
        }

        #endregion


        public Boolean IsInputDisable()
        {
            if (m_currentState == null || !m_currentState.Enable4Input)
            {
                return true;
            }
            return false;
        }


        #region Input Events

        public virtual void EventOnSelectGrid(GridPosition gridPosition) { }
        public virtual void EventOnInputClick(InputData eventData) { }
        public virtual void EventOnInputBegan(InputData eventData) { }
        public virtual void EventOnInputMoved(InputData eventData) { }
        public virtual void EventOnInputEnded(InputData eventData) { }
        public virtual void EventOnZoomInOut(InputData eventData) { }

        public virtual void EventOnKeyboardKeyDown(KeyCode keyCode) { }
        public virtual void EventOnKeyboardKeyHold(KeyCode keyCode) { }
        public virtual void EventOnKeyboardKeyUp(KeyCode keyCode) { }

        public virtual void EventOnJoystickDown() { }
        public virtual void EventOnJoystickHold(Vector2 deltaValue) { }
        public virtual void EventOnJoystickUp() { }

        public virtual void EventOnBattleUIClick(BattleUIEventID eventId, params Int32[] argvs) { }
        #endregion


    }
}
