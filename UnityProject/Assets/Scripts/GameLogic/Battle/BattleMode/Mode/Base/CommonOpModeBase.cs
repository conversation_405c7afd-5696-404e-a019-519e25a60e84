
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;

namespace Phoenix.GameLogic.Battle
{
    public class CommonOpModeBase : OpModeBase
    {
        private Dictionary<KeyCode, Vector2> m_keyboard4MoveCameraDict = new Dictionary<KeyCode, Vector2>()
        {
            {KeyCode.A, Vector2.left},
            {KeyCode.S, Vector2.down},
            {KeyCode.D, Vector2.right},
            {KeyCode.W, Vector2.up},
        };


        protected MovePathRule4InputMove m_moveRule4InputMove;
        protected MovePathRule4InputPreview m_moveRule4Preview;

        public CommonOpModeBase(BattleOpModeId battleOpModeId) : base(battleOpModeId)
        {
        }

        public MovePathRule4InputMove MoveRule4InputMove => m_moveRule4InputMove;
        public MovePathRule4InputPreview MoveRule4Preview => m_moveRule4Preview;


        public override void Init()
        {
            base.Init();
            m_moveRule4InputMove = new MovePathRule4InputMove();
            m_moveRule4InputMove.SetCanThroughCheckFunc(CanThroughGrid);
            m_moveRule4Preview = new MovePathRule4InputPreview();
        }

        public override void UnInit()
        {
            base.UnInit();
            m_moveRule4InputMove.Clear();
            m_moveRule4InputMove = null;
            m_moveRule4Preview.Clear();
            m_moveRule4Preview = null;
        }

        protected virtual Boolean CanThroughGrid(GridPosition grid)
        {
            return false;
        }


        #region Input Events
        public override void EventOnInputBegan(InputData eventData)
        {
            m_currentState?.InputBegan(eventData);
        }
        public override void EventOnInputMoved(InputData eventData)
        {
            m_currentState?.InputMoved(eventData);
        }
        public override void EventOnInputEnded(InputData eventData)
        {
            m_currentState?.InputEnded(eventData);
        }
        public override void EventOnZoomInOut(InputData eventData)
        {
            m_currentState?.ZoomInOut(eventData);
        }
        public override void EventOnKeyboardKeyDown(KeyCode keyCode) { }
        public override void EventOnKeyboardKeyHold(KeyCode keyCode)
        {
            if (m_keyboard4MoveCameraDict.TryGetValue(keyCode, out Vector2 value))
            {
                InputData inputData = new InputData();
                inputData.m_deltaPosition = value;
                inputData.m_inputType = InputType.Keyboard;
                m_currentState?.KeyboardEvent4Move(inputData);
            }
        }
        public override void EventOnKeyboardKeyUp(KeyCode keyCode) { }

        public override void EventOnJoystickDown() { }
        public override void EventOnJoystickHold(Vector2 deltaValue) { }
        public override void EventOnJoystickUp() { }
        #endregion


    }
}
