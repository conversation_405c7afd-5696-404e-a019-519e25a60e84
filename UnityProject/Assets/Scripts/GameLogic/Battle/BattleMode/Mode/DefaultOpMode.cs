
namespace Phoenix.GameLogic.Battle
{
    public class DefaultOpMode : OpModeBase
    {
        public DefaultOpMode() : base(BattleOpModeId.Default)
        {
            m_states[typeof(StoryPerformanceState)] = new StoryPerformanceState(this);
        }

        public override void ChangeState(BattleOpModeState stateType, ModeStateContext context = null)
        {
            ChangeStateInternal<StoryPerformanceState>(context);
        }
    }
}
