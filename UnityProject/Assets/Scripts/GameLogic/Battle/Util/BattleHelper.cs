
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.Battle
{
    public static class BattleHelper
    {
        public static void UpdateEntityBloodMask(Boolean addOrRemove, BloodMask mask, params Int32[] entityUids)
        {
            List<Int32> resultEntityUids = new List<Int32>();
            if (entityUids != null && entityUids.Length > 0)
            {
                foreach (Int32 entityUid in entityUids)
                {
                    resultEntityUids.Add(entityUid);
                }
            }
            UpdateEntityBloodMask(addOrRemove, mask, resultEntityUids);
        }
        public static void UpdateEntityBloodMask(Boolean addOrRemove, BloodMask mask, List<Int32> entityUids)
        {
            if (entityUids != null && entityUids.Count > 0)
            {
                EventManager.instance.Broadcast(EventID.Entity_BloodMaskChanged, addOrRemove, mask, entityUids);
            }
        }

        public static void UpdateOtherEntityBloodMask(Boolean addOrRemove, BloodMask mask, params Int32[] entityUids)
        {
            List<Int32> resultEntityUids = new List<Int32>();
            if (EntityViewManager.instance != null)
            {
                foreach(var item in EntityViewManager.instance.GetEntityViewMap())
                {
                    resultEntityUids.Add(item.Key);
                }
            }
            if(entityUids != null && entityUids.Length > 0)
            {
                foreach (Int32 entityUid in entityUids)
                {
                    resultEntityUids.Remove(entityUid);
                }
            }
            EventManager.instance.Broadcast(EventID.Entity_BloodMaskChanged, addOrRemove, mask, resultEntityUids);
        }

        public static void UpdateOtherEntityBloodMask(Boolean addOrRemove, BloodMask mask, List<Int32> entityUids)
        {
            List<Int32> resultEntityUids = new List<Int32>();
            if (EntityViewManager.instance != null)
            {
                foreach (var item in EntityViewManager.instance.GetEntityViewMap())
                {
                    resultEntityUids.Add(item.Key);
                }
            }
            if (entityUids != null && entityUids.Count > 0)
            {
                foreach (Int32 entityUid in entityUids)
                {
                    resultEntityUids.Remove(entityUid);
                }
            }
            EventManager.instance.Broadcast(EventID.Entity_BloodMaskChanged, addOrRemove, mask, resultEntityUids);
        }
        public static string GetRefereeDesc(BattleStageInfo battleStage, BattleCampRefereeSituationInfo situationInfo)
        {
            string refereeStr = string.Empty;
            switch (situationInfo.refereeType)
            {
                case BattleCampRefereeSituationType.TurnEnd:
                    if (situationInfo is BattleCampRefereeSituationInfo_TurnEnd refereeTurnEnd)
                    {
                        Int32 turnIndex = refereeTurnEnd.turnIndex + 1;
                        refereeStr = $"第{turnIndex}回合结束";
                    }
                    break;
                case BattleCampRefereeSituationType.EnemyAllDead:
                    refereeStr = "敌方全灭";
                    break;
                case BattleCampRefereeSituationType.SelfAllDead:
                    refereeStr = "我方全灭";
                    break;
                case BattleCampRefereeSituationType.EntityListAllDead:
                    refereeStr = "我方全灭";
                    break;
                case BattleCampRefereeSituationType.AnyActorOccupyAnyGrid:
                    refereeStr = "抵达目标格子[1]";
                    break;
                case BattleCampRefereeSituationType.AnyActorOccupyAnyGrid2:
                    refereeStr = "抵达目标格子[2]";
                    break;
                default:
                    refereeStr = $"未定义 {situationInfo.refereeType}";
                    break;
            }
            return refereeStr;
        }

        public static string GetRefereeDesc1(BattleStageInfo battleStage, BattleCampRefereeSituationInfo situationInfo)
        {
            return situationInfo.desc;
            //string refereeStr = string.Empty;

            //if (String.IsNullOrEmpty(refereeInfo.desc))
            //{
            //    switch(refereeInfo.refereeType)
            //    {
            //        case BattleCampRefereeType.ActorDead:
            //            {
            //                if (refereeInfo is BattleCampRefereeActorDeadInfo refereeActorDead)
            //                {
            //                    switch(refereeActorDead.type)
            //                    {
            //                        case BattleCampRefereeActorDeadType.SelfCampAll:
            //                        case BattleCampRefereeActorDeadType.OtherCampAll:
            //                            if (refereeInfo.winOrLose)
            //                            {
            //                                refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeWin01);
            //                            }
            //                            else
            //                            {
            //                                refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeLose01);
            //                            }
            //                            break;
            //                        case BattleCampRefereeActorDeadType.SpecificActorAll:
            //                            ActorConfigData actorConfig = GetActorConfig(battleStage, refereeActorDead.actorIdList.GetLastValueSafely(0));
            //                            if (actorConfig != null)
            //                            {
            //                                if (refereeInfo.winOrLose)
            //                                {
            //                                    refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeWin03, actorConfig.Name);
            //                                }
            //                                else
            //                                {
            //                                    refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeLose03, actorConfig.Name);
            //                                }
            //                            }
            //                            break;
            //                        case BattleCampRefereeActorDeadType.SelfCampAny:
            //                            break;
            //                        case BattleCampRefereeActorDeadType.OtherCampAny:
            //                            break;
            //                        case BattleCampRefereeActorDeadType.SpecificCampAny:
            //                            break;
            //                        case BattleCampRefereeActorDeadType.SpecificActorAny:
            //                            break;
            //                    }
            //                }
            //            }
            //            break;
            //        case BattleCampRefereeType.TurnEnd:
            //            {
            //                if (refereeInfo is BattleCampRefereeTurnEndInfo refereeTurnEnd)
            //                {
            //                    Int32 turnIndex = refereeTurnEnd.turnIndex + 1;
            //                    if (refereeInfo.winOrLose)
            //                    {
            //                        refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeWin06, turnIndex.ToString());
            //                    }
            //                    else
            //                    {
            //                        refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeLose06, turnIndex.ToString());
            //                    }
            //                }
            //            }
            //            break;
            //        case BattleCampRefereeType.OccupyPos:
            //            {
            //                if (refereeInfo is BattleCampRefereeActorOccupyPosInfo refereeActorOccupyPos)
            //                {
            //                    ActorConfigData actorConfig = GetActorConfig(battleStage, refereeActorOccupyPos.actorUid);
            //                    if (actorConfig != null)
            //                    {
            //                        if (refereeInfo.winOrLose)
            //                        {
            //                            refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeWin05, actorConfig.Name);
            //                        }
            //                        else
            //                        {
            //                            refereeStr = ConstStringUtility.GetConstString(ConstStringId.BattleRefereeLose05, actorConfig.Name);
            //                        }
            //                    }
            //                }
            //            }
            //            break;

            //    }
            //}
            //else
            //{
            //    refereeStr = refereeInfo.desc;
            //}

            //return refereeStr;
        }

        private static ActorConfigData GetActorConfig(BattleStageInfo battleStage, Int32 actorUid)
        {
            if (actorUid > 0)
            {
                Int32 actorRid = 0;
                foreach (BattleStageDisposedActorInfo actorInfo in battleStage.disposedActorList)
                {
                    if (actorInfo.uid == actorUid)
                    {
                        actorRid = actorInfo.rid;
                        break;
                    }
                }
                if (actorRid != 0)
                {
                    ActorConfigData actorConfig = ConfigDataManager.instance.GetActor(actorRid);
                    return actorConfig;
                }
            }
            return null;
        }

        public static Int32 GetEnergyRatio()
        {
            Int32 energyRatio = ConfigDataManager.instance.GetConstValue(BattleConstValueId.Energy2PointRatio);
            energyRatio = 1;
            return energyRatio;
        }

        private static Single GetTimeScaleOfBattleSpeed()
        {
            Int32 battleSpeedMode = GamePlayerContext.instance.ModuleProvider.SettingModule.GetBattleSpeedMode();
            Single timeScale = 1;
            if (battleSpeedMode == 0)
            {
                timeScale = BattleParamSetting.instance.m_battleUISetting.m_timeScaleBattleSpeedX1;
            }
            else if (battleSpeedMode == 1)
            {
                timeScale = BattleParamSetting.instance.m_battleUISetting.m_timeScaleBattleSpeedX2;
            }
            return timeScale;
        }

        public static void UpdateBattleSpeedTimeScale(Boolean reset = false)
        {
            if (reset)
            {
                TimeScaleManager.instance.ResetStaticTimeScale(TimeScaleId.BattleSpeed);
            }
            else
            {
                Single timeScale = GetTimeScaleOfBattleSpeed();
                TimeScaleManager.instance.SetStaticTimeScale(TimeScaleId.BattleSpeed, timeScale);
            }
        }


        public static Single SetCameraPosition(params GridPosition[] grids)
        {
            return SetCameraPositionInternal(false, null, grids);
        }

        public static Single SetCameraPosition(Boolean immediately, params GridPosition[] grids)
        {
            return SetCameraPositionInternal(immediately, null, grids);
        }

        public static Single SetCameraPosition(Boolean immediately, Action onEnd, params GridPosition[] grids)
        {
            return SetCameraPositionInternal(immediately, onEnd, grids);
        }

        private static Single SetCameraPositionInternal(Boolean immediately, Action onEnd, params GridPosition[] grids)
        {
            if (grids.Length == 0)
            {
                onEnd?.Invoke();
                return 0;
            }
            Int32 actualCount = 0;
            Vector3 targetPosition = Vector3.zero;
            foreach(GridPosition grid in grids)
            {
                if (grid.isValid)
                {
                    actualCount++;
                    targetPosition += BattleShortCut.GetGridWorldPosition(grid);
                }
            }
            targetPosition /= actualCount;
            return BattleShortCut.battleScene.sceneCameraHandler.SetCameraPositionWithSpeed(targetPosition, 10, immediately, onEnd);
        }
    }
}
