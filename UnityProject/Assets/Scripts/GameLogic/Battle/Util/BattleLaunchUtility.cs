using System;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;
using Phoenix.GameLogic.World;
using Phoenix.Guide;
using Phoenix.Hakoniwa;

namespace Phoenix.GameLogic.Battle
{
    public static class BattleLaunchUtility
    {
        public static void StartHakoniwaBattle(GameStateContextHakoniwaBattle context)
        {
            GameManager.instance.stateMachine.ChangeState((int)EGameState.Battle, context);
        }


        public static void StartBattle(int battleConfigId, BattleDataContext dataContext = null)
        {
            StartBattle(battleConfigId, dataContext, null);
        }

        public static void StartBattle(BattleSessionInitData initData)
        {
            DebugUtility.Log($"[StartBattle] pvp --> SessionId:{initData.SessionId}, BattleRid:{initData.BattleRid}");
            GameStateContextBattle ctx = ClassPoolManager.instance.Fetch<GameStateContextBattle>();
            ctx.battleSessionInitData = initData;
            Loading.Open(LoadingFlagId.EnterBattle, LoadingViewType.Normal, () =>
            {
                GameManager.instance.stateMachine.ChangeState((int)EGameState.Battle, ctx);
                Loading.Close(LoadingFlagId.EnterBattle);
            });
        }

        public static void StartBattle(int battleConfigId, BattleDataContext dataContext, Action onOpenStart)
        {
            DebugUtility.Log($"[StartBattle] BattleId --> {battleConfigId}, Context:{dataContext}");
            GameStateContextBattle ctx = ClassPoolManager.instance.Fetch<GameStateContextBattle>();
            ctx.battleConfigId = battleConfigId;
            if (StaticHakoniwa.IsHakoSceneBattle)
            {
                onOpenStart?.Invoke();
                GameManager.instance.stateMachine.ChangeState((int)EGameState.Battle, ctx);
            }
            else
            {
                Loading.Open(LoadingFlagId.EnterBattle, LoadingViewType.Normal, () =>
                {
                    onOpenStart?.Invoke();
                    GameManager.instance.stateMachine.ChangeState((int)EGameState.Battle, ctx);
                    Loading.Close(LoadingFlagId.EnterBattle);
                });
            }
        }

        public static void StartBattle(BattleRecordMsg battleRecord)
        {
            DebugUtility.Log($"[StartBattle] BattleRecord --> {battleRecord.battleInitData.battleRid}");
            GameStateContextBattle ctx = ClassPoolManager.instance.Fetch<GameStateContextBattle>();
            ctx.battleRecord = battleRecord;
            Loading.Open(LoadingFlagId.EnterBattle, LoadingViewType.Normal, () =>
            {
                GameManager.instance.stateMachine.ChangeState((int)EGameState.Battle, ctx);
                Loading.Close(LoadingFlagId.EnterBattle);
            });
        }

        public static void ExitBattle(BattleType battleType = BattleType.None, Boolean isWin = false)
        {
            if (battleType == BattleType.None)
            {
                battleType = GamePlayerContext.instance.BattleModule.GetBattleType();
            }
            switch (battleType)
            {
                case BattleType.OpenSceneBattle:
                    WorldHelper.EnterWorld(10001);
                    break;
                case BattleType.QuestBattle:
                case BattleType.BossChallenge:
                case BattleType.WorldMonster:
                    Int32 worldId = GamePlayerContext.instance.BasicModule.GetWorldId();
                    WorldHelper.EnterWorld(worldId);
                    break;
                case BattleType.GuideBattle:
                    Loading.Open(LoadingFlagId.Login, LoadingViewType.Normal, () =>
                    {
                        GameStateContextLogin contextLogin = new GameStateContextLogin();
                        contextLogin.showGuideBattleLevel = true;
                        GameManager.instance.stateMachine.ChangeState((int)EGameState.Login, contextLogin);
                        Loading.Close(LoadingFlagId.Login);
                    });
                    break;
                case BattleType.Hakoniwa:
                    StaticHakoniwaInterface.ClearHakoniwaTempMonsterIds(isWin);
                    Int32 hakoniwaId = GamePlayerContext.instance.HakoniwaModule.HakoniwaDS.HakoniwaData.currentHakoniwa.hakoniwaId;
                    Int32 waypointId = GamePlayerContext.instance.HakoniwaModule.HakoniwaDS.HakoniwaData.currentHakoniwa.waypointId;
                    HakoLocation location = GamePlayerContext.instance.HakoniwaModule.HakoniwaDS.HakoniwaData.location;
                    StaticHakoniwa.EnterHakoniwa(hakoniwaId, waypointId, isWin ? location : HakoLocation.invalid);
                    break;
                default:
                    Loading.Open(LoadingFlagId.Lobby, LoadingViewType.Normal, () =>
                    {
                        GameManager.instance.stateMachine.ChangeState((int)EGameState.Lobby);
                        Loading.Close(LoadingFlagId.Lobby);
                    });
                    break;

            }
        }

        public static void EnterBattleByRecordOrInvoke(Action actionOnFallback)
        {
            var record = BattleRecordManager.instance.LoadRunningBattleRecord();
            if (record != null)
            {
                var str = ConstStringUtility.GetConstString(ConstStringId.BattleRecordAskStartMessageBoxStr);
                MessageBox.Show(str, string.Empty, () =>
                {
                    if (GuideManager.instance.NeedRestartBattleSceneGuide(record.battleInitData.battleRid))
                    {
                        StartBattle(record.battleInitData.battleRid);
                    }
                    else
                    {
                        StartBattle(record);
                    }
                },
                () =>
                {
                    BattleRecordManager.instance.SaveBattleRecordToHistory(record);
                    actionOnFallback.InvokeSafely();
                });
            }
            else
            {
                actionOnFallback.InvokeSafely();
            }
        }
    }
}
