using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic.Battle
{
    public static partial class GameLogicBattleExtension
    {
        public static EntityView GetView(this Entity entity)
        {
            if(entity != null)
            {
                return EntityViewManager.instance.GetEntityView(entity.uid);
            }
            return null;
        }
    }
}
