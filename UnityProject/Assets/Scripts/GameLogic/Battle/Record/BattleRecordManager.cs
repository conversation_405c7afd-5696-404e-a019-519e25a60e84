using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Phoenix.Battle;
using UnityEngine;
using Phoenix.Core;
using Newtonsoft.Json;
using MessagePack;
using Mos.MsgPackLogic.Protocol;

namespace Phoenix.GameLogic.Battle
{
    public class BattleRecordManager : Singleton<BattleRecordManager>
    {
        private const string Extension = ".txt";
        private const string FileName = "BattleRecord";
        private const int maxHistoryCount = 20;

        private string m_folderPath;

        protected override void OnInit()
        {
            base.OnInit();
            m_folderPath = FilePathUtility.GetPath(Application.persistentDataPath, FileOutputSetting.instance.battleRecordFolderPath);
            if (!Directory.Exists(m_folderPath))
            {
                Directory.CreateDirectory(m_folderPath);
            }
        }

        public List<FileSystemInfo> GetBattleLocalFiles()
        {
            var directoryInfo = new DirectoryInfo(m_folderPath);
            var fileSystemInfos = new List<FileSystemInfo>();
            foreach (var fileSystemInfo in directoryInfo.GetFileSystemInfos())
            {
                fileSystemInfos.Add(fileSystemInfo);
            }
            fileSystemInfos.Sort((x, y) => y.LastWriteTime.CompareTo(x.LastWriteTime));
            return fileSystemInfos;
        }

        public void SaveRunningBattleRecord()
        {
            if (BattleShortCut.battleExecuter == null)
            {
                return;
            }
            BattleRecordMsg record = BattleShortCut.battleExecuter.CreateRecord();
            string path = GetFilePathOfRunning();
            BattleRecordUtility.Save(record, path, new BattleLogger());
        }

        public void SaveRunningBattleRecordToHistory()
        {
            if (BattleShortCut.battleExecuter == null)
            {
                return;
            }
            BattleRecordMsg record = BattleShortCut.battleExecuter.CreateRecord();
            SaveBattleRecordToHistory(record);
        }

        public void SaveBattleRecordToHistory(BattleRecordMsg record)
        {
            string path = GetFilePathOfHistory(record.battleInitData.battleRid, record.battleInitData.startTime);
            BattleRecordUtility.Save(record, path, new BattleLogger());
            DeleteRunningRecord();
            DeleteHistoryRecords(maxHistoryCount);
        }

        public BattleRecordMsg LoadRunningBattleRecord()
        {
            var path = GetFilePathOfRunning();
            return BattleRecordUtility.Load(path, new BattleLogger());
        }

        public BattleRecordMsg LoadBattleRecordByFileName(string fileName)
        {
            var path = GetFilePathByFileName(fileName);
            return BattleRecordUtility.Load(path, new BattleLogger());
        }

        private void DeleteRunningRecord()
        {
            string path = GetFilePathOfRunning();
            if (File.Exists(path))
            {
                File.Delete(path);
            }
        }

        private void DeleteHistoryRecords(int leftCount)
        {
            var fileSystemInfos = GetBattleLocalFiles();
            if (fileSystemInfos.Count > leftCount)
            {
                for (int i = leftCount; i < fileSystemInfos.Count; i++)
                {
                    fileSystemInfos[i].Delete();
                }
            }
        }

        private string GetFilePathOfRunning()
        {
            string fileName = string.Format("{0}{1}", FileName, Extension);
            return FilePathUtility.GetPath(m_folderPath, fileName);
        }

        private string GetFilePathOfHistory(int battleRid, string dateTimeStr)
        {
            string fileName = string.Format("{0}_{2}_{3}{1}", FileName, Extension, dateTimeStr, battleRid);
            return FilePathUtility.GetPath(m_folderPath, fileName);
        }

        private string GetFilePathByFileName(string fileName)
        {
            if (fileName.EndsWith(Extension))
            {
                return FilePathUtility.GetPath(m_folderPath, fileName);
            }
            return FilePathUtility.GetPath(m_folderPath, fileName + Extension);
        }
    }
}
