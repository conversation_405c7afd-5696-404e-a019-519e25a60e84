using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Guide
{
    public static class GuideUtility
    {
        private static Color m_startEventIdColor = new Color(204f / 255f, 102f / 255f, 102f / 255f);
        private static Color m_endEventIdColor = new Color(153f / 255f, 51f / 255f, 0f / 255f);
        private static Color m_triggerColor = Color.yellow * 0.75f;
        private static Color m_conditionColor = Color.green * 0.75f;
        private static Color m_actionColor = new Color(1f, 0.5f, 0f) * 0.75f;
        private static object[][] m_formatParamArrays = new object[10][];

        public static int GetStepUniqueId(int groupId, int stepId)
        {
            return groupId * 100 + stepId;
        }

        public static string GetStepDesc(GuideStepInfo stepInfo, bool needRichColor = true)
        {
            StringBuilder sb = new StringBuilder();
            AppendTriggerInfoList(sb, stepInfo.triggerInfoList, needRichColor);
            GuideActionInfo actionInfo = stepInfo.actionInfo;
            if (actionInfo != null && actionInfo.type != EGuideActionType.Empty)
            {
                if (sb.Length > 0)
                {
                    sb.Append("，");
                }
                AppendActionInfo(sb, actionInfo, needRichColor);
            }
            return sb.ToString();
        }

        public static string GetTriggerDesc(GuideTriggerInfo triggerInfo)
        {
            StringBuilder sb = new StringBuilder();
            AppendTriggerInfo(sb, triggerInfo);
            return sb.ToString();
        }

        public static string GetConditionDesc(GuideConditionInfo conditionInfo)
        {
            StringBuilder sb = new StringBuilder();
            AppendConditionInfo(sb, conditionInfo);
            return sb.ToString();
        }

        public static string GetTriggerDesc(EGuideTriggerType type, List<string> paramList, Color? color = null)
        {
            switch (type)
            {
                case EGuideTriggerType.ObjectActiveChanged:
                    return GetDesc("物体显示状态改变", "物体{0}显示状态改变为{1}", paramList, 2, color);
                case EGuideTriggerType.GuideStepFinished:
                    return GetDesc("完成引导步骤", "完成引导步骤{0}", paramList, 1, color);
                case EGuideTriggerType.GuideGroupFinished:
                    return GetDesc("完成引导组", "完成引导组{0}", paramList, 1, color);
                case EGuideTriggerType.UIStateChanged:
                    return GetDesc("UI节点动画结束", "UI节点{0}动画{1}结束", paramList, 2, color);
                case EGuideTriggerType.TimelinePlayEnded:
                    return GetDesc("Timeline动画结束", "Timeline动画{0}结束", paramList, 1, color);
                case EGuideTriggerType.BattleActorSelected:
                    if (AnalyzeParam<bool>(paramList, 1))
                    {
                        return GetDesc("选中角色", "强制选中角色{0}", paramList, 1, color);
                    }
                    return GetDesc("选中角色", "选中角色{0}", paramList, 1, color);
                case EGuideTriggerType.BattleActorMoveEnded:
                    return GetDesc("角色移动完", "角色{0}移动结束", paramList, 1, color);
                case EGuideTriggerType.UITaskPreStarted:
                    return GetDesc("UITask开始前", "UITask（{0}）开始前", paramList, 1, color);
                case EGuideTriggerType.UITaskStarted:
                    return GetDesc("UITask开始", "UITask（{0}）开始", paramList, 1, color);
                case EGuideTriggerType.UITaskStopped:
                    return GetDesc("UITask结束", "UITask（{0}）结束", paramList, 1, color);
                case EGuideTriggerType.BattleStarted:
                    return GetDescByParamList("进入战斗", "进入战斗{0}", paramList, 0, color);
                case EGuideTriggerType.BattleEnded:
                    return GetDesc("结束战斗", "结束战斗", paramList, 0, color);
                case EGuideTriggerType.TurnChanged:
                    return GetDesc("回合改变", "回合改变为{0}阵营为{1}", paramList, 2, color);
                case EGuideTriggerType.EnterWorldMap:
                    return GetDesc("进入世界地图", "进入世界地图", paramList, 0, color);
                case EGuideTriggerType.EnterCamp:
                    return GetDesc("进入营地", AnalyzeParam<bool>(paramList, 0) ? "从战斗或登录进入营地" : "进入营地", paramList, 0, color);
                case EGuideTriggerType.EnterCampOrWorldMap:
                    return GetDesc("进入营地或世界地图", "进入营地或世界地图", paramList, 0, color);
                case EGuideTriggerType.MainBattleStarted:
                    return GetDesc("主战斗开始", "主战斗开始", paramList, 0, color);
                case EGuideTriggerType.WinConditionNotified:
                    return GetDesc("胜利条件播放前", "胜利条件播放前", paramList, 0, color);
                case EGuideTriggerType.CampEventFinished:
                    return GetDesc("营地事件交互结束", "营地事件交互{0}结束", paramList, 1, color);
                case EGuideTriggerType.WayPointEventFinished:
                    return GetDesc("大地图事件完成", "大地图事件{0}完成", paramList, 1, color);
                case EGuideTriggerType.BattleActorActionEnded:
                    return GetDesc("角色行动完", "角色{0}行动完", paramList, 1, color);
                case EGuideTriggerType.CharacterListPanelOpen:
                    return GetDesc("打开角色列表", "打开角色列表", paramList, 0, color);
                case EGuideTriggerType.ManualTrigger:
                    return GetDesc("手动调用", "手动调用", paramList, 0, color);
            }
            return string.Empty;
        }

        public static string GetActionDesc(EGuideActionType type, List<string> paramList, Color? color = null)
        {
            switch (type)
            {
                case EGuideActionType.Empty:
                    return "空";
                case EGuideActionType.ClickButton:
                    return GetDesc("点击UI控件", "点击UI控件{0}", paramList, 1, color);
                case EGuideActionType.ClickButtonUp:
                    return GetDesc("弹起UI控件", "弹起UI控件{0}", paramList, 1, color);
                case EGuideActionType.ClickChildButton:
                    return GetDesc("点击UI子控件", "点击UI控件{0}的第{1}个子控件", paramList, 2, color);
                case EGuideActionType.ClickChildPathButton:
                    return GetDesc("点击UI子控件（路径）", "点击UI控件{0}的{2}路径下的第{1}个子控件", paramList, 3, color);
                case EGuideActionType.ClickRollScrollItem:
                    return GetDesc("点击RollScrollView的子控件", "点击RollScrollView {0}的第{1}个子控件", paramList, 2, color);
                case EGuideActionType.ClickRollScrollItemPathButton:
                    return GetDesc("点击RollScrollView的子控件（路径）", "点击UI控件{0}的{2}路径下的第{1}个子控件", paramList, 3, color);
                case EGuideActionType.ClickBattleGrid:
                    return GetDesc("点击战场上的格子", "点击战场上的格子({0},{1})", paramList, 2, color);
                case EGuideActionType.ClickWayPoint:
                    return GetDesc("点击大地图路点", "点击大地图路点{0}", paramList, 1, color);
                case EGuideActionType.ClickHomeBuilding:
                    return GetDesc("点击骑士领建筑", "点击骑士领建筑{0}", paramList, 1, color);
                case EGuideActionType.ClickCharacterInCharacterList:
                    return GetDesc("点击英雄详情列表英雄", "点击英雄详情列表英雄{0}", paramList, 1, color);
                case EGuideActionType.DragActorToGrid:
                    return GetDesc("拖动上阵角色到格子", "拖动角色{0}到格子({1},{2})上", paramList, 3, color);
                case EGuideActionType.BattleCameraLookPos:
                    return GetDesc("将摄像机移到指定格子上", "将摄像机移到指定格子({0},{1})上", paramList, 2, color);
                case EGuideActionType.SetObjectActive:
                    return GetDesc("设置物体显示状态", "设置物体{0}显示状态为{1}", paramList, 2, color);
                case EGuideActionType.StartDialog:
                    return GetDesc("开始对话", "开始对话{0}", paramList, 1, color);
                case EGuideActionType.ShowTip:
                    return GetDesc("显示文本提示", "显示提示{0},{1}", paramList, 2, color);
                case EGuideActionType.HideTip:
                    return GetDesc("隐藏文本提示", "隐藏提示", paramList, 0, color);
                case EGuideActionType.WaitSeconds:
                    return GetDesc("延迟", "延迟{0}秒", paramList, 1, color);
                case EGuideActionType.EnableInput:
                    return GetDesc("是否允许玩家点击界面", AnalyzeParam<bool>(paramList, 0) ? "允许玩家点击界面" : "不允许玩家点击界面", paramList, 0, color);
                case EGuideActionType.EnableClick:
                    return GetDesc("是否允许玩家点击场景", AnalyzeParam<bool>(paramList, 0) ? "允许玩家点击场景" : "不允许玩家点击场景", paramList, 0, color);
                case EGuideActionType.EnableDrag:
                    return GetDesc("是否允许玩家拖动场景", AnalyzeParam<bool>(paramList, 0) ? "允许玩家拖动场景" : "不允许玩家拖动场景", paramList, 0, color);
                case EGuideActionType.EnableAll:
                    return GetDesc("是否允许玩家操作", AnalyzeParam<bool>(paramList, 0) ? "允许玩家操作" : "不允许玩家操作", paramList, 0, color);
                case EGuideActionType.ShowHint:
                    return GetDesc("显示界面提示", "显示界面提示{0}", paramList, 1, color);
                case EGuideActionType.HideHint:
                    return GetDesc("隐藏界面提示", "隐藏界面提示{0}", paramList, 1, color);
                case EGuideActionType.HideAllHint:
                    return GetDesc("隐藏所有界面提示", "隐藏所有界面提示", paramList, 0, color);
                case EGuideActionType.ShowGraphic:
                    return GetDescByParamListCountIndex("开始图片引导", "开始图片引导{0}", paramList, 0, color);
                case EGuideActionType.ShowMonsterGraphic:
                    return GetDescByParamListCountIndex("开始怪物引导", "开始怪物引导{0}", paramList, 0, color);
                case EGuideActionType.SendEnd:
                    return GetDesc("发送引导结束", "发送引导结束", paramList, 0, color);
                case EGuideActionType.Trigger:
                    return GetDesc("触发引导组", "触发引导组{0}", paramList, 1, color);
                case EGuideActionType.BlockTeamActiveProcess:
                    return GetDesc("暂停队伍激活流程", "暂停队伍激活流程", paramList, 0, color);
                case EGuideActionType.ContinueTeamActiveProcess:
                    return GetDesc("继续队伍激活流程", "继续队伍激活流程", paramList, 0, color);
                case EGuideActionType.BlockBattleProcess:
                    return GetDesc("暂停战斗流程", "暂停战斗流程", paramList, 0, color);
                case EGuideActionType.ContinueBattleProcess:
                    return GetDesc("继续战斗流程", "继续战斗流程", paramList, 0, color);
                case EGuideActionType.NewFuncOpen:
                    return GetDesc("新功能开启提示", "新功能{0}开启提示", paramList, 1, color);
                case EGuideActionType.ForceNextActionActorId:
                    return GetDesc("指定下一个选择的角色", "指定下一个选择的角色{0}", paramList, 1, color);
                case EGuideActionType.ShowCampEventMarkActive:
                    return GetDesc("设置营地任务显示状态", "设置营地任务{0}显示状态{1}", paramList, 2, color);
                case EGuideActionType.ClickCampActor:
                    return GetDesc("点击营地角色", "点击营地角色{0}", paramList, 1, color);
                case EGuideActionType.SignIn:
                    return GetDesc("签到", "签到", paramList, 0, color);
                case EGuideActionType.ChapterShow:
                    return GetDesc("章节开始", "章节{0}开始", paramList, 1, color);
                case EGuideActionType.ChangeUIState:
                    return GetDesc("播放UI动画", "播放UI({0})动画{1}", paramList, 2, color);
                case EGuideActionType.SelectBattleActor:
                    return GetDesc("选择角色", "选择角色{0}", paramList, 1, color);
                case EGuideActionType.ScrollHorizontalNormalize:
                    return GetDesc("改变水平滑动区域位置", "改变水平滑动区域{0}位置为{1}", paramList, 2, color);
                case EGuideActionType.ScrollVerticalNormalize:
                    return GetDesc("改变竖直滑动区域位置", "改变竖直滑动区域{0}位置为{1}", paramList, 2, color);
                case EGuideActionType.StartAppraise:
                    return GetDesc("开始平台评价", "开始平评价", paramList, 0, color);
                case EGuideActionType.LookAtWayPoint:
                    return GetDesc("镜头看向目标路点", "镜头看向目标路点{0}", paramList, 1, color);
                case EGuideActionType.ShowOrHideWorldMapUI:
                    return GetDesc("是否显示世界地图界面", AnalyzeParam<bool>(paramList, 0) ? "显示世界地图界面" : "隐藏世界地图界面", paramList, 0, color);
                case EGuideActionType.SetDangerRangeState:
                    return GetDesc("是否开启危险范围", AnalyzeParam<bool>(paramList, 0) ? "开启危险范围" : "关闭危险范围", paramList, 0, color);
            }
            return string.Empty;
        }

        public static string GetConditionDesc(EGuideConditionType type, List<string> paramList, Color? color = null)
        {
            switch (type)
            {
                case EGuideConditionType.ScenarioFinished:
                    return GetDesc("剧情是否完成", AnalyzeParam<bool>(paramList, 1) ? "剧情{0}已完成" : "剧情{0}未完成", paramList, 1, color);
                case EGuideConditionType.GuideGroupFinished:
                    return GetDesc("引导组是否完成", AnalyzeParam<bool>(paramList, 1) ? "引导组{0}已完成" : "引导组{0}未完成", paramList, 1, color);
                case EGuideConditionType.RiftLevelFinished:
                    return GetDesc("时之隙是否完成", AnalyzeParam<bool>(paramList, 1) ? "时之隙{0}已完成" : "时之隙{0}未完成", paramList, 1, color);
                case EGuideConditionType.PlayerLevel:
                    return GetDesc("玩家是否达到等级", AnalyzeParam<bool>(paramList, 1) ? "玩家等级已达到{0}" : "玩家等级未达到{0}", paramList, 1, color);
                case EGuideConditionType.WayPointArrived:
                    return GetDesc("是否抵达路点", AnalyzeParam<bool>(paramList, 1) ? "已抵达路点{0}" : "未抵达路点{0}", paramList, 1, color);
                case EGuideConditionType.BattleSceneStarted:
                    return GetDesc("正在进行战斗", "正在进行战斗{0}", paramList, 1, color);
                case EGuideConditionType.WayPointEventFinished:
                    return GetDesc("路点事件是否完成", AnalyzeParam<bool>(paramList, 1) ? "已完成路点事件{0}" : "未完成路点事件{0}", paramList, 1, color);
                case EGuideConditionType.CampEventFinished:
                    return GetDesc("营地事件交互是否完成", AnalyzeParam<bool>(paramList, 1) ? "已完成营地事件交互{0}" : "未完成营地事件交互{0}", paramList, 1, color);
                case EGuideConditionType.CanSignIn:
                    return GetDesc("是否满足签到条件", AnalyzeParam<bool>(paramList, 0) ? "已满足签到条件" : "不满足签到条件", paramList, 0, color);
                case EGuideConditionType.HaveActor:
                    return GetDesc("是否拥有角色", AnalyzeParam<bool>(paramList, 1) ? "已拥有角色{0}" : "未拥有角色{0}", paramList, 1, color);
                case EGuideConditionType.NoviceOpen:
                    return GetDesc("宿命之旅是否开启", AnalyzeParam<bool>(paramList, 0) ? "宿命之旅已开启" : "宿命之旅未开启", paramList, 0, color);
                case EGuideConditionType.ArenaFirstRobotCompleted:
                    return GetDesc("心魔幻境是否打过", AnalyzeParam<bool>(paramList, 0) ? "已打过心魔幻境" : "未打过心魔幻境", paramList, 0, color);
                case EGuideConditionType.HasEntrustMission:
                    return GetDesc("是否有门派委托任务", AnalyzeParam<bool>(paramList, 0) ? "有门派委托任务" : "没有门派委托任务", paramList, 0, color);
                case EGuideConditionType.CanStartArena:
                    return GetDesc("是否可以开始竞技场", AnalyzeParam<bool>(paramList, 0) ? "可以开始竞技场" : "不可以开始竞技场", paramList, 0, color);
                case EGuideConditionType.DuringUIState:
                    return GetDesc("在UIState状态下", "{0}的UIState在{1}状态下", paramList, 2, color);
                case EGuideConditionType.BattleActionGreaterThan:
                    return GetDesc("在某回合行动数之后", "在第{0}回合的第{1}次行动之后", paramList, 2, color);
            }
            return string.Empty;
        }

        public static string GetEventDesc(GuideStepInfo stepInfo)
        {
            StringBuilder sb = new StringBuilder();
            if (stepInfo.startEventIdList.Count > 0 || stepInfo.endEventIdList.Count > 0)
            {
                sb.Append("<");
                bool hasOtherBefore = false;
                if (stepInfo.startEventIdList.Count > 0)
                {
                    hasOtherBefore = true;
                    for (int i = 0; i < stepInfo.startEventIdList.Count; ++i)
                    {
                        if (i != 0)
                        {
                            sb.Append(",");
                        }
                        sb.Append(string.Format("<color=#{1}>{0}</color>", stepInfo.startEventIdList[i], ColorUtility.ToHtmlStringRGB(m_startEventIdColor)));
                    }
                }
                if (stepInfo.endEventIdList.Count > 0)
                {
                    if (hasOtherBefore)
                    {
                        sb.Append(";");
                    }
                    for (int i = 0; i < stepInfo.endEventIdList.Count; ++i)
                    {
                        if (i != 0)
                        {
                            sb.Append(",");
                        }
                        sb.Append(string.Format("<color=#{1}>{0}</color>", stepInfo.endEventIdList[i], ColorUtility.ToHtmlStringRGB(m_endEventIdColor)));
                    }
                }
                sb.Append(">");
            }
            return sb.ToString();
        }

        public static string GetGuidePath(GameObject go)
        {
            if (go != null)
            {
                Transform trans = go.transform;
                string str = string.Empty;
                while (trans != null)
                {
                    str = "/" + trans.gameObject.name + str;
                    trans = trans.parent;
                }
                return str.Substring(1);
            }
            return string.Empty;
        }

        private static void AppendTriggerInfoList(StringBuilder sb, List<GuideTriggerInfo> triggerInfoList, bool needRichColor = true)
        {
            for (int i = 0; i < triggerInfoList.Count; ++i)
            {
                if (i != 0)
                {
                    sb.Append("，或");
                }
                sb.Append("当");
                AppendTriggerInfo(sb, triggerInfoList[i], needRichColor);
            }
        }

        private static void AppendTriggerInfo(StringBuilder sb, GuideTriggerInfo triggerInfo, bool needRichColor = true)
        {
            string triggerDesc = GetTriggerDesc(triggerInfo.type, triggerInfo.paramList, needRichColor ? m_triggerColor : (Color?)null);
            sb.Append(triggerDesc);
            if (triggerInfo.conditionInfoList.Count > 0)
            {
                sb.Append("(");
                AppendConditionInfoList(sb, triggerInfo.conditionInfoList, needRichColor);
                sb.Append(")");
            }
        }

        private static void AppendConditionInfoList(StringBuilder sb, List<GuideConditionInfo> conditionInfoList, bool needRichColor = true)
        {
            for (int i = 0; i < conditionInfoList.Count; ++i)
            {
                if (i != 0)
                {
                    sb.Append("，");
                }
                AppendConditionInfo(sb, conditionInfoList[i], needRichColor);
            }
        }

        private static void AppendConditionInfo(StringBuilder sb, GuideConditionInfo conditionInfo, bool needRichColor = true)
        {
            string conditionDesc = GetConditionDesc(conditionInfo.type, conditionInfo.paramList, needRichColor ? m_conditionColor : (Color?)null);
            sb.Append(conditionDesc);
        }

        private static void AppendActionInfo(StringBuilder sb, GuideActionInfo actionInfo, bool needRichColor = true)
        {
            string actionDesc = GetActionDesc(actionInfo.type, actionInfo.paramList, needRichColor ? m_actionColor : (Color?)null);
            sb.Append(actionDesc);
        }

        private static string GetDescByParamList(string desc, string descFormat, List<string> paramList, int countIndex, Color? color)
        {
            string prm = GetListString(paramList);
            return GetDesc(desc, descFormat, new List<string> { prm }, 1, color);
        }

        private static string GetDescByParamListCountIndex(string desc, string descFormat, List<string> paramList, int countIndex, Color? color)
        {
            string prm = GetParamListString(paramList, countIndex);
            return GetDesc(desc, descFormat, new List<string> {prm}, 1, color);
        }

        private static string GetDesc(string desc, string descFormat, List<string> paramList, int paramCount, Color? color)
        {
            if (paramList == null)
            {
                if (color != null)
                {
                    desc = ChangeColor(desc, color.Value);
                }
                return desc;
            }
            object[] paramArray = m_formatParamArrays.GetValueSafely(paramCount);
            if (paramArray == null)
            {
                paramArray = new object[paramCount];
                m_formatParamArrays[paramCount] = paramArray;
            }
            for (int i = 0; i < paramCount; ++i)
            {
                string prm = paramList.GetValueSafely(i);
                if (prm == null)
                {
                    prm = string.Empty;
                }
                paramArray[i] = prm;
            }
            if (color != null)
            {
                descFormat = ChangeColor(descFormat, color.Value);
            }
            return string.Format(descFormat, paramArray);
        }

        private static string ChangeColor(string str, Color color)
        {
            string openStr = string.Format("<color=#{0}>", ColorUtility.ToHtmlStringRGB(color));
            string closeStr = "</color>";
            if (string.IsNullOrEmpty(str))
            {
                return string.Empty;
            }
            StringBuilder sb = new StringBuilder();
            sb.Append(openStr);
            foreach (char ch in str)
            {
                if (ch == '{')
                {
                    sb.Append(closeStr);
                }
                sb.Append(ch);
                if (ch == '}')
                {
                    sb.Append(openStr);
                }
            }
            sb.Append(closeStr);
            return sb.ToString();
        }

        public static string GetParamListString(List<string> paramList, int countIndex, string seperator = ",")
        {
            int count = AnalyzeParam<int>(paramList, countIndex);
            return GetListString(paramList, seperator, countIndex + 1, count);
        }

        public static string GetListString(IList list, string seperator = ",", int startIndex = 0, int count = -1)
        {
            if (list == null)
            {
                return string.Empty;
            }
            if (count < 0)
            {
                count = list.Count - startIndex;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < count; ++i)
            {
                if (i != 0)
                {
                    sb.Append(seperator);
                }
                sb.Append(list[startIndex + i]);
            }
            return sb.ToString();
        }

        private static T AnalyzeParam<T>(List<string> paramList, int index)
        {
            if (paramList != null)
            {
                string prm = paramList.GetValueSafely(index);
                T value;
                if (TypeMethodUtility.TryParse(prm, out value))
                {
                    return value;
                }
            }
            return default(T);
        }

        private static bool TryAnalyzeParam<T>(List<string> paramList, int index, out T result)
        {
            if (paramList != null)
            {
                return TypeMethodUtility.TryParse(paramList.GetValueSafely(index), out result);
            }
            result = default(T);
            return false;
        }
    }
}
