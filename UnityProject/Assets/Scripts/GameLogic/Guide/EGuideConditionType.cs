using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Guide
{
    public enum EGuideConditionType
    {
        ScenarioFinished,
        GuideGroupFinished,
        RiftLevelFinished,
        PlayerLevel,
        WayPointArrived,
        BattleSceneStarted,
        WayPointEventFinished,
        CampEventFinished,
        CanSignIn,
        HaveActor,
        NoviceOpen,
        ArenaFirstRobotCompleted,
        HasEntrustMission,
        CanStartArena,
        DuringUIState,
        BattleActionGreaterThan,
    }
}
