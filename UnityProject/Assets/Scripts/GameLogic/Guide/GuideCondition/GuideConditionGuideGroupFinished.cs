using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Guide
{
    [GuideConditionAttibute(EGuideConditionType.GuideGroupFinished)]
    public class GuideConditionGuideGroupFinished : GuideCondition
    {
        protected override bool OnCheck()
        {
            return GuideManager.instance.CheckStepGroupComplete(GetParam<int>(0), false) == GetParam<bool>(1);
        }
    }
}
