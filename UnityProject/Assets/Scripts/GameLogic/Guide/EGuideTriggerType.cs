using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Guide
{
    public enum EGuideTriggerType
    {
        ObjectActiveChanged,
        GuideStepFinished,
        GuideGroupFinished,
        UIStateChanged,
        BattleActorSelected,
        BattleActorMoveEnded,
        UITaskPreStarted,
        UITaskStarted,
        UITaskStopped,
        BattleStarted,
        BattleEnded,
        TurnChanged,
        EnterWorldMap,
        EnterCamp,
        EnterCampOrWorldMap,
        MainBattleStarted,
        CampEventFinished,
        WayPointEventFinished,
        BattleActorActionEnded,
        CharacterListPanelOpen,
        WinConditionNotified,
        TimelinePlayEnded,
        ManualTrigger,
    }
}
