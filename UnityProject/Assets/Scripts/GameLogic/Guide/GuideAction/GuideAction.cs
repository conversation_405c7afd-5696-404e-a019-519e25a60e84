using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Common;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Guide
{
    public abstract class GuideAction : ClassPoolObj
    {
        protected List<string> m_paramList = new List<string>();
        protected int m_id;
        protected int m_groupId;

        private List<int> m_enableInputList;
        private Action<int> m_actionOnEnd;
        private bool m_isHandling;
        private Timer m_timer = new Timer();

        protected int paramCount { get { return m_paramList.Count; } }
        public bool isHandling { get { return m_isHandling; } }

        public override void OnRelease()
        {
            base.OnRelease();
            m_timer.Reset();
        }

        public void Handle(int groupId, int id, List<string> prmList, Action<int> actionOnEnd)
        {
            m_groupId = groupId;
            m_id = id;
            m_actionOnEnd = actionOnEnd;
            m_enableInputList = CreateEnableInputList();
            m_paramList.Clear();
            m_paramList.AddRange(prmList);
            SetInputState(true);
            m_isHandling = true;
            OnHandle();
        }

        protected void StartTimer(float time, Action actionOnTimeUp)
        {
            m_timer.Start(time, actionOnTimeUp);
        }

        protected T GetParam<T>(int index)
        {
            if (index >= 0 && index < m_paramList.Count)
            {
                string prm = m_paramList[index];
                T value;
                if (TypeMethodUtility.TryParse(prm, out value))
                {
                    return value;
                }
            }
            return default(T);
        }

        protected void End()
        {
            if (!m_isHandling)
            {
                return;
            }
            m_isHandling = false;
            if (this.isReleased)
            {
                return;
            }
            SetInputState(false);
            OnEnd();
            if (m_actionOnEnd != null)
            {
                try
                {
                    m_actionOnEnd(m_id);
                }
                catch (Exception e)
                {
                    UnityEngine.Debug.LogError(e.Message + e.StackTrace);
                }
            }
        }

        public void Tick(float deltaTime)
        {
            if (isHandling)
            {
                OnTick(deltaTime);
            }
            if (m_timer.isActive)
            {
                var timeSlice = new TimeSlice()
                {
                    deltaTime = deltaTime,
                    unscaledDeltaTime = deltaTime,
                };
                m_timer.Tick(timeSlice);
            }
        }

        public void Abort()
        {
            if (!m_isHandling)
            {
                return;
            }
            m_isHandling = false;
            OnAbort();
            OnEnd();
        }

        private void SetInputState(bool isEnable)
        {
            if (m_enableInputList == null)
            {
                return;
            }
            for (int i = 0; i < m_enableInputList.Count; ++i)
            {
                GuideManager.instance.SetEnableInput((EGuideEnableInputType)i, isEnable);
            }
        }

        protected virtual List<int> CreateEnableInputList()
        {
            return null;
        }

        protected virtual void OnTick(float deltaTime)
        {
            
        }

        protected virtual void OnAbort() { }

        protected abstract void OnHandle();

        protected virtual void OnEnd()
        {
            
        }
    }
}
