using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Common;
using UnityEngine;

namespace Phoenix.Guide
{
    [GuideActionAttibute(EGuideActionType.Trigger)]
    public class GuideActionTrigger : GuideAction
    {
        protected override void OnHandle()
        {
            int groupId = GetParam<int>(0);
            GuideGroupInfo groupInfo = GuideManager.instance.GetGroupInfo(groupId);
            if (groupInfo == null)
            {
                Debug.LogError("触发的引导组Id填写错误：" + groupId);
                End();
                return;
            }
            Debug.Log(string.Format("StartGuide: Trigger {0}", groupId));
            List<string> paramList = new List<string>();
            for (int i = 1; i < m_paramList.Count; ++i)
            {
                paramList.Add(m_paramList[i]);
            }
            End();
            GuideManager.instance.ManualTriggerGuide(groupId);
        }
    }
}
