using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Guide
{
    public abstract class GuideTrigger : ClassPoolObj
    {
        private int m_id;
        private EGuideTriggerType m_type;
        private List<GuideConditionInfo> m_conditionInfoList = new List<GuideConditionInfo>();
        private Action<int> m_actionOnTriggered;
        private bool m_triggerWaitOneTick;
        protected bool m_isStarted;

        protected List<string> m_prmList;

        public int id { get { return m_id; } }

        public EGuideTriggerType type
        {
            get { return m_type; }
        }

        public override void OnFetch()
        {
            RegisterListener();
        }

        public override void OnRelease()
        {
            m_isStarted = false;
            m_prmList = null;
            m_actionOnTriggered = null;
            m_conditionInfoList.Clear();
        }

        public void UnInit()
        {
            m_isStarted = false;
            UnregisterListener();
        }

        protected virtual void RegisterListener()
        {

        }

        protected virtual void UnregisterListener()
        {

        }

        public void SetTrigger(int id, GuideTriggerInfo triggerInfo, Action<int> actionOnTriggered)
        {
            SetTrigger(id, triggerInfo.type, triggerInfo.paramList, triggerInfo.conditionInfoList, actionOnTriggered);
        }

        public void SetTrigger(int id, EGuideTriggerType type, List<string> prmList, List<GuideConditionInfo> conditionInfoList, Action<int> actionOnTriggered)
        {
            m_id = id;
            m_type = type;
            m_prmList = prmList;
            AppendConditionInfoList(conditionInfoList);
            m_actionOnTriggered = actionOnTriggered;
            m_isStarted = true;
            OnSetTrigger();
        }

        public void AppendConditionInfoList(List<GuideConditionInfo> conditionInfoList)
        {
            m_conditionInfoList.AddRange(conditionInfoList);
        }

        public bool TryTrigger(List<string> paramList)
        {
            if (this.isReleased)
            {
                return false;
            }
            if (CheckSameParamList(paramList) && CheckCondition() && CheckCondition())
            {
                Trigger();
                return true;
            }
            return false;
        }

        public bool TryTriggerContain(string param)
        {
            if (this.isReleased)
            {
                return false;
            }
            if (ContainsParam(param) && CheckCondition() && CheckCondition())
            {
                Trigger();
                return true;
            }
            return false;
        }

        protected void TryTrigger()
        {
            if (this.isReleased)
            {
                return;
            }
            if (CheckTrigger() && CheckCondition())
            {
                Trigger();
            }
            else
            {
                OnTriggerFailed();
            }
        }

        protected void TryTriggerWaitOneTick()
        {
            m_triggerWaitOneTick = true;
        }

        private bool CheckCondition()
        {
            for (int i = 0; i < m_conditionInfoList.Count; ++i)
            {
                GuideConditionInfo conditionInfo = m_conditionInfoList[i];
                if (!GuideConditionUtility.CheckCondition(conditionInfo))
                {
                    return false;
                }
            }
            return true;
        }

        protected void LogCondition()
        {
            for (int i = 0; i < m_conditionInfoList.Count; ++i)
            {
                GuideConditionInfo conditionInfo = m_conditionInfoList[i];
                Debug.Log(string.Format("[Guide] Guide Condition Result is {0}, {1}, {2}, {3}", GuideConditionUtility.CheckCondition(conditionInfo), conditionInfo.type, conditionInfo.paramList.GetValueSafely(0), conditionInfo.paramList.GetValueSafely(1)));
            }
        }

        private void Trigger()
        {
            if (m_actionOnTriggered != null)
            {
                m_actionOnTriggered(m_id);
            }
        }

        private bool CheckSameParamList(List<string> paramList)
        {
            if (paramList == null)
            {
                return m_prmList.Count == 0;
            }
            if (m_prmList.Count != paramList.Count)
            {
                return false;
            }
            for (int i = 0; i < m_prmList.Count; ++i)
            {
                if (m_prmList[i] != paramList[i])
                {
                    return false;
                }
            }
            return true;
        }

        protected bool ContainsParam(string str, int startIndex = 0)
        {
            for (int i = startIndex; i < m_prmList.Count; ++i)
            {
                if (str == m_prmList[i])
                {
                    return true;
                }
            }
            return false;
        }

        protected T GetParam<T>(int index)
        {
            if (m_prmList != null)
            {
                if (index >= 0 && index < m_prmList.Count)
                {
                    string prm = m_prmList[index];
                    T value;
                    if (TypeMethodUtility.TryParse(prm, out value))
                    {
                        return value;
                    }
                }
            }
            return default(T);
        }

        public void Tick(float deltaTime)
        {
            if (!m_isStarted)
            {
                return;
            }
            OnTick(deltaTime);
            if (m_triggerWaitOneTick)
            {
                m_triggerWaitOneTick = false;
                TryTrigger();
            }
        }

        protected virtual void OnTick(float deltaTime) { }
        protected abstract bool CheckTrigger();
        protected virtual void OnSetTrigger() { }
        protected virtual void OnTriggerFailed() { }
    }
}
