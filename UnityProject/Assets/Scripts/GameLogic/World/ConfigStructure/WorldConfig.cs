
using UnityEngine;
using Phoenix.Core;


namespace Phoenix.GameLogic
{
    [CreateAssetMenu(fileName = "WorldConfig", menuName = "ScriptableObjects/WorldConfig", order = 2)]
    public class WorldConfig : ScriptableObject
    {
        public AssetWeakRef m_artScene;
        public AssetWeakRef m_logicScene; 
        public AssetWeakRef m_virtualCamera;
        public AssetWeakRef m_miniMap;
    }
}
