



using System;
using UnityEngine;
using UnityEngine.AI;
using Phoenix.Core;

namespace Phoenix.GameLogic.World
{
    public partial class WorldEntityView
    {
        private NavMeshAgent m_agent;
        private Vector3 m_moveTargetPosition;
        private Vector3 m_movePosition;
        private Action m_moveEndAction;
        private Boolean m_isMoving;

        protected void InitMoveController()
        {
            m_isMoving = false;
        }

        protected void UnInitMoveController()
        {
            if (m_agent != null)
            {
                UnityEngine.Object.Destroy(m_agent);
            }
            m_isMoving = false;
        }

        protected void TickNavagentMove(TimeSlice ts)
        {
            if (m_agent == null)
            {
                return;
            }

            if (m_agent.hasPath)
            {
                if (!m_isMoving)
                {
                    PlayAnimation("Move");
                    m_isMoving = true;
                }
                m_movePosition = m_agent.transform.position;
                Vector3 steeringTarget = new Vector3(m_agent.steeringTarget.x, m_movePosition.y, m_agent.steeringTarget.z);
                Vector3 direction = steeringTarget - m_movePosition;
                Quaternion rotation = Quaternion.LookRotation(direction.normalized);
                SetEntityRotation(rotation);
            }
            else
            {
                if (m_isMoving)
                {
                    m_agent.enabled = false;
                    PlayDefaultAnimation();
                    SetEntityPositionImmediately(m_moveTargetPosition);
                    ExecuteOnMoveEndAction();
                    m_isMoving = false;
                }
            }
        }


        public void MoveToTarget(Vector3 targetPosition, Action onMoveEnd = null)
        {
            if (m_agent == null)
            {
                m_agent = m_entityRoot.AddComponent<NavMeshAgent>();
                InitNavMeshAgent(m_agent);
                m_agent.enabled = false;
            }
            targetPosition = WorldSceneUtility.GetWorldTerrainPhysicsPosition(targetPosition);
            if (NavMesh.SamplePosition(targetPosition, out NavMeshHit hit, 0.2f, NavMesh.AllAreas))
            {
                m_agent.enabled = true;
                m_movePosition = m_position;
                m_moveTargetPosition = hit.position;
                m_agent.SetDestination(m_moveTargetPosition);
                ExecuteOnMoveEndAction();
                m_moveEndAction = onMoveEnd;
            }
            else
            {
                onMoveEnd?.Invoke();
            }
        }

        private void InitNavMeshAgent(NavMeshAgent agent)
        {
            agent.speed = WorldConfigSetting.instance.m_npcNavagentConfig.m_speed;
            agent.angularSpeed = WorldConfigSetting.instance.m_npcNavagentConfig.m_angularSpeed;
            agent.acceleration = WorldConfigSetting.instance.m_npcNavagentConfig.m_acceleration;
            agent.stoppingDistance = WorldConfigSetting.instance.m_npcNavagentConfig.m_stoppingDistance;

            agent.updateRotation = WorldConfigSetting.instance.m_npcNavagentConfig.m_updateRotation;
            agent.autoBraking = WorldConfigSetting.instance.m_npcNavagentConfig.m_autoBraking;


            agent.radius = WorldConfigSetting.instance.m_npcNavagentConfig.m_radius;
            agent.height = WorldConfigSetting.instance.m_npcNavagentConfig.m_height;

            agent.autoTraverseOffMeshLink = WorldConfigSetting.instance.m_npcNavagentConfig.m_autoTraverseOffMeshLink;
            agent.autoRepath = WorldConfigSetting.instance.m_npcNavagentConfig.m_autoRepath;
        }

        private void ExecuteOnMoveEndAction()
        {
            if (m_moveEndAction != null)
            {
                Action tempAction = m_moveEndAction;
                m_moveEndAction = null;
                tempAction.Invoke();
            }
        }
    }
}
