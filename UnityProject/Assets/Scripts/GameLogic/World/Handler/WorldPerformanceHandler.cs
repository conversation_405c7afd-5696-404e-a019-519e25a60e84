
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;


namespace Phoenix.GameLogic.World
{
    public class WorldPerformanceHandler : WorldHandler
    {
        public WorldPerformancePlayer m_performancePlayer = new WorldPerformancePlayer();
        public Boolean IsBlockInput { get { return m_performancePlayer.IsBlockInput; } }
        public WorldPerformanceHandler(WorldManager worldManager) : base(worldManager)
        {
        }
        protected override void OnTick(TimeSlice timeSlice)
        {
            m_performancePlayer.Tick(timeSlice);
        }
        protected override void OnRegisterEvent()
        {
            GamePlayerContext.instance.WorldModule.EventOnExecuteWorldAction += EventOnExecuteWorldAction;
            GamePlayerContext.instance.QuestModule.EventOnQuestAccept += EventOnQuestAccept;
            GamePlayerContext.instance.QuestModule.EventOnQuestCompleted += EventOnQuestCompleted;

            m_owner.WorldBase.EventOnWorldEntityAdd += EventOnWorldEntityAdd;
            m_owner.WorldBase.EventOnWorldEntityRemove += EventOnWorldEntityRemove;
            m_owner.WorldBase.EventOnWorldEntityInteractionAdd += EventOnWorldEntityInteractionAdd;
            m_owner.WorldBase.EventOnWorldEntityInteractionRemove += EventOnWorldEntityInteractionRemove;
            m_owner.WorldBase.EventOnWorldStateChange += EventOnWorldStateChange;

            m_owner.WorldBase.EventOnWorldTransformationToOther += EventOnWorldTransformationToOther;
            m_owner.WorldBase.EventOnWorldTransformationRevert += EventOnWorldTransformationRevert;
        }

        protected override void OnUnRegisterEvent()
        {
            GamePlayerContext.instance.WorldModule.EventOnExecuteWorldAction -= EventOnExecuteWorldAction;
            GamePlayerContext.instance.QuestModule.EventOnQuestAccept -= EventOnQuestAccept;
            GamePlayerContext.instance.QuestModule.EventOnQuestCompleted -= EventOnQuestCompleted;

            m_owner.WorldBase.EventOnWorldEntityAdd -= EventOnWorldEntityAdd;
            m_owner.WorldBase.EventOnWorldEntityRemove -= EventOnWorldEntityRemove;
            m_owner.WorldBase.EventOnWorldEntityInteractionAdd -= EventOnWorldEntityInteractionAdd;
            m_owner.WorldBase.EventOnWorldEntityInteractionRemove -= EventOnWorldEntityInteractionRemove;
            m_owner.WorldBase.EventOnWorldStateChange -= EventOnWorldStateChange;

            m_owner.WorldBase.EventOnWorldTransformationToOther -= EventOnWorldTransformationToOther;
            m_owner.WorldBase.EventOnWorldTransformationRevert -= EventOnWorldTransformationRevert;
        }


        #region Public Method

        public void PushPerformance(WorldPerformanceBase performanceAdapter)
        {
            m_performancePlayer.StartPerformance(performanceAdapter);
        }

        public void PushPerformance(List<EnterWorldActionBase> actions)
        {
            foreach(EnterWorldActionBase worldAction in actions)
            {
                if (worldAction is EnterWorldPostActionShowReward showRewardAction)
                {
                    PushShowToastRewardPerformance(showRewardAction.m_dropId);
                }
                else if (worldAction is EnterWorldPostActionWorldAction showWorldAction)
                {
                    PushPerformance(showWorldAction.m_worldActionGroupId);
                }
                else if (worldAction is EnterWorldPostActionPathFinding pathFinding)
                {
                    PushPlayerPathFindingPerformance(pathFinding.m_entityRid);
                }
            }
        }

        public void PushPerformance(Int32 worldActionGroupId)
        {
            PushPerformanceWithActionGroup(worldActionGroupId);
        }


        #endregion



        #region Internal Method

        private void PushPerformanceWithActionGroup(Int32 worldActionGroupId)
        {
            WorldPerformanceExecuteActionGroup performance = ClassPoolManager.instance.Fetch<WorldPerformanceExecuteActionGroup>();
            performance.worldActionGroupId = worldActionGroupId;
            performance.mainCamera = m_owner.MainCamera;
            m_performancePlayer.StartPerformance(performance);
        }

        private void PushShowToastRewardPerformance(Int32 dropId)
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessShowReward process = ClassPoolManager.instance.Fetch<WorldProcessShowReward>();
            process.rewardDropId = dropId;
            process.showType = RewardShowType.Toast;
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance);
        }

        private void PushPlayerPathFindingPerformance(Int32 entityId)
        {
            WorldEntityView worldEntityView = WorldManager.instance.GetWorldEntityView(entityId);
            if (worldEntityView != null && worldEntityView.WorldEntityConfigData.WorldId == m_owner.WorldId)
            {
                WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
                WorldProcessPlayerPathFinding process = ClassPoolManager.instance.Fetch<WorldProcessPlayerPathFinding>();
                process.m_position = worldEntityView.GetEntityPosition();
                performance.AddProcess(process);
                m_performancePlayer.StartPerformance(performance);
            }
        }

        #endregion




        #region Event CallBack
        private void EventOnExecuteWorldAction(Int32 worldActionGroupId)
        {
            PushPerformanceWithActionGroup(worldActionGroupId);
        }

        private void EventOnQuestAccept(QuestProcessor questProcessor)
        {
            WorldPerformanceQuestAccept performance = ClassPoolManager.instance.Fetch<WorldPerformanceQuestAccept>();
            performance.questProcessor = questProcessor;
            m_performancePlayer.StartPerformance(performance);
        }

        private void EventOnQuestCompleted(QuestProcessor questProcessor)
        {
            WorldPerformanceQuestCompleted performance = ClassPoolManager.instance.Fetch<WorldPerformanceQuestCompleted>();
            performance.questId = questProcessor.Id;
            performance.questGroupId = questProcessor.QuestGroupId;
            if (questProcessor.IsQuestGroupProcessor)
            {
                performance.questGroupLastQuestId = questProcessor.QuestGroupConfig.LastQuestId;
            }
            performance.rewardDropId = questProcessor.QuestConfig.DropRewardId;
            m_performancePlayer.StartPerformance(performance);
        }

        private void EventOnWorldEntityAdd(WorldEntity worldEntity, Boolean immediately)
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessAddEntity process = ClassPoolManager.instance.Fetch<WorldProcessAddEntity>();
            process.m_worldEntity = worldEntity;
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance, immediately);
        }

        private void EventOnWorldEntityRemove(Int32 entityId, Boolean immediately)
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessRemoveEntity process = ClassPoolManager.instance.Fetch<WorldProcessRemoveEntity>();
            process.m_entityId = entityId;
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance, immediately);
        }

        private void EventOnWorldEntityInteractionAdd(Int32 entityId, WorldEntityInteraction interaction)
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessAddEntityInteraction process = ClassPoolManager.instance.Fetch<WorldProcessAddEntityInteraction>();
            process.m_entityId = entityId;
            process.m_interactionId = interaction.Id;
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance);
        }

        private void EventOnWorldEntityInteractionRemove(Int32 entityId, Int32 interactionId)
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessRemoveEntityInteraction process = ClassPoolManager.instance.Fetch<WorldProcessRemoveEntityInteraction>();
            process.m_entityId = entityId;
            process.m_interactionId = interactionId;
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance);
        }

        private void EventOnWorldStateChange(Int32 newStateId)
        {
            GameObject timelineObj = m_owner.m_sceneHandler.GetEnterStateTimeline(newStateId);
            if (timelineObj != null)
            {
                WorldPerformanceSceneStateUpdate performance = new WorldPerformanceSceneStateUpdate();
                performance.m_newStateId = newStateId;
                performance.timelineObject = timelineObj;
                performance.mainCamera = m_owner.m_cameraHandler.MainCamera;
                m_performancePlayer.StartPerformance(performance, true);
            }
        }

        private void EventOnWorldTransformationToOther(Int32 transformationSkinId)
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessTransformationToOther process = ClassPoolManager.instance.Fetch<WorldProcessTransformationToOther>();
            process.m_transformationSkinId = transformationSkinId;
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance);
        }

        private void EventOnWorldTransformationRevert()
        {
            WorldPerformanceSequence performance = ClassPoolManager.instance.Fetch<WorldPerformanceSequence>();
            WorldProcessTransformationRevert process = ClassPoolManager.instance.Fetch<WorldProcessTransformationRevert>();
            performance.AddProcess(process);
            m_performancePlayer.StartPerformance(performance);
        }

        #endregion
    }
}
