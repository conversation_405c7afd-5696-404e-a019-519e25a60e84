
using Cinemachine;
using System;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic.TouchEvent;

namespace Phoenix.GameLogic.World
{
    public enum CameraMoveModeId
    {
        Follow,
        Custom
    }

    public class WorldCameraHandler : WorldHandler
    {
        private ScreenTouchHandler m_touchHandler;
        private CameraMoveModeId m_cameraModeMode;
        private Action m_cameraMoveEndCallback;

        private Vector3 m_followPosition, m_followVelocity;
        protected Vector3 m_autoMoveStartPosition, m_autoMoveTargetPosition;
        private float m_smoothMoveTime, m_smoothMoveDuration;
        private Vector3 m_position;


        private GameObject m_mainCameraGo;
        private GameObject m_virtualCameraGo;


        private CinemachineBrain m_cinemachineBrainComp;
        private CinemachineVirtualCamera m_virtualCameraComp;
        private CinemachineTrackedDolly m_virtualCameraDollyCartComp;

        private float m_currentZoomValue, m_downValue = 1, m_upValue = 2;
        private Transform m_worldMapVirtualCameraRotateTransform;
        private Boolean m_enableCameraRotate = false;

        public WorldCameraHandler(WorldManager worldManager) : base(worldManager)
        {
        }

        public Camera MainCamera { get; private set; }


        #region Public

        public void SetCameraRotateState(Boolean isOn)
        {
            m_enableCameraRotate = isOn;
        }

        public void SetCameraMoveMode(CameraMoveModeId mode)
        {
            ChangeCameraMoveModeInternal(mode);
        }

        public void SetCameraFollowPosition(Vector3 position, bool immediately = false)
        {
            //ChangeCameraMoveModeInternal(CameraMoveModeId.Follow);
            m_followPosition = position;
            if (immediately)
            {
                m_position = m_followPosition;
                ApplyPosition2Camera(m_position);
            }
        }

        public void SetCameraPositionWithTime(Vector3 target, float duration, Action onEnd = null)
        {
            //ChangeCameraMoveModeInternal(CameraMoveModeId.Custom);
            OnCameraSmoothMoveEnd();
            m_cameraMoveEndCallback = onEnd;
            m_autoMoveStartPosition = m_position;
            m_autoMoveTargetPosition = target;

            m_smoothMoveTime = 0;
            m_smoothMoveDuration = duration;

            if (m_smoothMoveDuration < 1e-3)
            {
                m_position = m_autoMoveTargetPosition;
                OnCameraSmoothMoveEnd();
            }
        }

        public void SetCameraPositionWithSpeed(Vector3 target, float speed, Action onEnd = null)
        {
            //ChangeCameraMoveModeInternal(CameraMoveModeId.Custom);
            OnCameraSmoothMoveEnd();
            m_cameraMoveEndCallback = onEnd;
            m_autoMoveStartPosition = m_position;
            m_autoMoveTargetPosition = target;

            m_smoothMoveTime = 0;
            m_smoothMoveDuration = Vector3.Distance(m_position, target) / speed;

            if (m_smoothMoveDuration < 1e-3)
            {
                m_position = m_autoMoveTargetPosition;
                OnCameraSmoothMoveEnd();
            }
        }

        #endregion


        protected override void OnInit()
        {
            GameObject parent = m_owner.m_sceneHandler.SceneVirtualCameraRoot;
            m_mainCameraGo = ResourceHandleManager.instance.SpawnGameObject(CommonPrefabPathSetting.instance.physicsCameraRoot.path, parent);
            m_virtualCameraGo = ResourceHandleManager.instance.SpawnGameObject(m_owner.WorldBase.WorldConfigData.VirtualCameraFullPath, parent);

            MainCamera = m_mainCameraGo.GetComponentInChildren<Camera>();
            m_virtualCameraComp = m_virtualCameraGo.GetComponentInChildren<CinemachineVirtualCamera>();
            m_virtualCameraDollyCartComp = m_virtualCameraComp.GetCinemachineComponent<CinemachineTrackedDolly>();
            m_cinemachineBrainComp = MainCamera.GetComponent<CinemachineBrain>();
            m_cinemachineBrainComp.m_UpdateMethod = CinemachineBrain.UpdateMethod.ManualUpdate;

            m_worldMapVirtualCameraRotateTransform = m_virtualCameraGo.transform;
            //m_cameraRotationY = PlayerPrefs.GetInt(String.Format("{0}_RotateY", m_owner.WorldIdKey));
            //m_worldMapVirtualCameraRotateTransform.eulerAngles = new Vector3(0, m_cameraRotationY, 0);

            m_touchHandler = new ScreenTouchHandler();
            m_touchHandler.EventOnPinchSpread += EventOnTouchZoom;
            m_touchHandler.EventOnTouchMoved += EventOnTouchMoved;
            m_touchHandler.EventOnTouchTap += EventOnTouchTap;

            //LocationConfigData location = m_owner.WorldBase.m_worldConfigData.BornLocation;
            SetCameraFollowPosition(WorldHelper.GetPlayerPosition(), true);

            EventManager.instance.RegisterListener(EventID.WorldNavagentMoveBegin, EventOnWorldNavagentMoveBegin);
            EventManager.instance.RegisterListener<Vector3>(EventID.WorldNavagentMoving, EventOnWorldNavagentMoving);
            EventManager.instance.RegisterListener(EventID.WorldNavagentMoveEnd, EventOnWorldNavagentMoveEnd);
        }

        protected override void OnUnInit()
        {
            ResourceHandleManager.instance.DespawnGameObject(m_mainCameraGo);
            ResourceHandleManager.instance.DespawnGameObject(m_virtualCameraGo);

            m_touchHandler.EventOnPinchSpread -= EventOnTouchZoom;
            m_touchHandler.EventOnTouchMoved -= EventOnTouchMoved;
            m_touchHandler.EventOnTouchTap += EventOnTouchTap;
            m_touchHandler = null;
            m_cinemachineBrainComp = null;


            EventManager.instance.UnRegisterListener(EventID.WorldNavagentMoveBegin, EventOnWorldNavagentMoveBegin);
            EventManager.instance.UnRegisterListener<Vector3>(EventID.WorldNavagentMoving, EventOnWorldNavagentMoving);
            EventManager.instance.UnRegisterListener(EventID.WorldNavagentMoveEnd, EventOnWorldNavagentMoveEnd);
        }

        protected override void OnTick(TimeSlice ts)
        {
            m_touchHandler?.Tick(ts.deltaTime);
            m_cinemachineBrainComp?.ManualUpdate();

            switch (m_cameraModeMode)
            {
                case CameraMoveModeId.Follow:
                    TickSmoothFollow(ts.deltaTime, ref m_position);
                    break;
                case CameraMoveModeId.Custom:
                    if (IsSmoothMoving())
                    {
                        TickSmoothAutoMove(ts.deltaTime, ref m_position);
                    }
                    break;
            }
            ApplyPosition2Camera(m_position);
        }


        private void TickSmoothFollow(Single dt, ref Vector3 position)
        {
            if (Vector3.SqrMagnitude(position - m_followPosition) > 0)
            {
                Single followFactor = WorldConfigSetting.instance.m_worldCameraFollowFactor;
                position = Vector3.SmoothDamp(position, m_followPosition, ref m_followVelocity, 0.1f, 1000, dt * followFactor);
               //  m_position = Vector3.LerpUnclamped(m_position, m_smoothMoveTargetPosition, dt * followFactor);
            }
        }

        private void TickSmoothAutoMove(Single dt, ref Vector3 position)
        {
            m_smoothMoveTime += dt;
            float a = Mathf.Clamp01(m_smoothMoveTime / m_smoothMoveDuration);
            position = Vector3.LerpUnclamped(m_autoMoveStartPosition, m_autoMoveTargetPosition, a);
            if (!IsSmoothMoving())
                OnCameraSmoothMoveEnd();
        }

        protected bool IsSmoothMoving()
        {
            return m_smoothMoveDuration > 0 && m_smoothMoveTime < m_smoothMoveDuration;
        }


        private void OnCameraSmoothMoveEnd()
        {
            if(m_cameraMoveEndCallback != null)
            {
                Action action = m_cameraMoveEndCallback;
                m_cameraMoveEndCallback = null;
                action();
            }
        }

        private void ChangeCameraMoveModeInternal(CameraMoveModeId mode)
        {
            if (mode != m_cameraModeMode)
            {
                m_cameraModeMode = mode;
            }
        }

        private void ApplyPosition2Camera(Vector3 position)
        {
            m_virtualCameraGo.transform.position = position;
        }



        private void EventOnTouchMoved(Vector2 position, Vector2 deltaMoveValue)
        {
            //if (m_enableCameraRotate)
            //{
            //    if (Mathf.Abs(deltaMoveValue.x) > 0)
            //    {
            //        m_worldMapVirtualCameraRotateTransform.Rotate(new Vector3(0, -deltaMoveValue.x * 0.1f, 0));
            //        m_cameraRotationY = Mathf.RoundToInt(m_worldMapVirtualCameraRotateTransform.eulerAngles.y);
            //    }
            //}
        }

        private void EventOnTouchZoom(float deltaMoveValue)
        {
            if (Mathf.Abs(deltaMoveValue) > 0)
            {
                m_currentZoomValue = Mathf.Clamp(m_currentZoomValue * deltaMoveValue, m_downValue, m_upValue);
                m_virtualCameraDollyCartComp.m_PathPosition = m_currentZoomValue - m_downValue;
            }
        }

        private void EventOnTouchTap(Vector2 position)
        {
            Ray ray = MainCamera.ScreenPointToRay(position);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit))
            {
                if (hit.collider.gameObject.layer != LayerMask.NameToLayer("Terrain"))
                {
                    return;
                }
                Vector3 point = hit.point;
                EventManager.instance.Broadcast(EventID.SetPlayerDestination, point);
            }
        }


        private void EventOnWorldNavagentMoveBegin()
        {

        }

        private void EventOnWorldNavagentMoving(Vector3 position)
        {
            SetCameraFollowPosition(position);
        }

        private void EventOnWorldNavagentMoveEnd()
        {

        }
    }
}
