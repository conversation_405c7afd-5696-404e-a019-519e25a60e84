using System;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.SceneManagement;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.World
{
    public class WorldNavagentHandler : WorldHandler
    {
        private GameObject m_root;
        private NavMeshAgent m_agent;
        private NavMeshPath m_path;
        private Vector3 m_position;
        private Boolean m_isTickMoving;

        private Boolean m_isManualMoving;

        public WorldNavagentHandler(WorldManager worldManager) : base(worldManager)
        {
        }

        public Vector3 Position { get { return m_position; } }

        protected override void OnInit()
        {
            m_root = new GameObject("NavagentRoot");
            SceneManager.MoveGameObjectToScene(m_root, m_owner.m_sceneHandler.WorldMainScene);
            m_root.transform.tag = "Player";
            m_root.AddComponent<WorldNavagentTrigger>();
            m_root.AddComponent<WorldNavagentGizmos>();
            //LocationConfigData location = m_owner.WorldBase.m_worldConfigData.BornLocation;
            m_position = WorldHelper.GetPlayerPosition();
            m_root.transform.position = m_position;
            m_agent = m_root.AddComponent<NavMeshAgent>();
            m_path = new NavMeshPath();
            InitNavMeshAgent(m_agent);
            InitCollider();
        }

        private void InitNavMeshAgent(NavMeshAgent agent)
        {
            agent.speed = WorldConfigSetting.instance.m_navagentConfig.m_speed;
            agent.angularSpeed = WorldConfigSetting.instance.m_navagentConfig.m_angularSpeed;
            agent.acceleration = WorldConfigSetting.instance.m_navagentConfig.m_acceleration;
            agent.stoppingDistance = WorldConfigSetting.instance.m_navagentConfig.m_stoppingDistance;

            agent.updateRotation = WorldConfigSetting.instance.m_navagentConfig.m_updateRotation;
            agent.autoBraking = WorldConfigSetting.instance.m_navagentConfig.m_autoBraking;


            agent.radius = WorldConfigSetting.instance.m_navagentConfig.m_radius;
            agent.height = WorldConfigSetting.instance.m_navagentConfig.m_height;

            agent.autoTraverseOffMeshLink = WorldConfigSetting.instance.m_navagentConfig.m_autoTraverseOffMeshLink;
            agent.autoRepath = WorldConfigSetting.instance.m_navagentConfig.m_autoRepath;
            //agent.isStopped = false;
            //agent.areaMask = 1 << 1 | 1 << 2;
        }

        private void InitCollider()
        {
            Rigidbody rigidbody = m_root.AddComponent<Rigidbody>();
            rigidbody.useGravity = false;
            rigidbody.isKinematic = true;
            rigidbody.mass = 0;
            CapsuleCollider collider = m_root.AddComponent<CapsuleCollider>();
            collider.center = new Vector3(0, 1, 0);
            collider.radius = 0.5f;
            collider.height = 2f;
        }


        protected override void OnUnInit()
        {
            m_path = null;
            WorldHelper.SetPlayerPosition(m_position);
            UnityEngine.Object.Destroy(m_root);
        }

        protected override void OnTick(TimeSlice ts)
        {
            if (m_agent.hasPath)
            {
                Vector3 steeringTarget = new Vector3(m_agent.steeringTarget.x, m_position.y, m_agent.steeringTarget.z);
                Vector3 direction = steeringTarget - m_position;
                direction.y = 0;
                Quaternion rotation = Quaternion.LookRotation(direction.normalized);
                m_position = m_root.transform.position;
                EventManager.instance.Broadcast(EventID.WorldNavagentSteering, rotation);

                if (!m_isTickMoving)
                {
                    EventManager.instance.Broadcast(EventID.WorldNavagentMoveBegin);
                    m_isTickMoving = true;
                }
                EventManager.instance.Broadcast(EventID.WorldNavagentMoving, m_position);
            }
            else
            {
                if (m_isTickMoving)
                {
                    EventManager.instance.Broadcast(EventID.WorldPlayerQuestTrackStateUpdate, false);
                    EventManager.instance.Broadcast(EventID.WorldNavagentMoveEnd);
                    m_isTickMoving = false;
                }
            }
        }


        #region Public

        public void CalculatePath(Vector3 targetPosition, NavMeshPath path)
        {
            m_agent.CalculatePath(targetPosition, path);
        }

        public void ManualMoveBegin()
        {
            EventManager.instance.Broadcast(EventID.WorldPlayerQuestTrackStateUpdate, false);
            m_isManualMoving = true;
            m_agent.isStopped = false;
        }
        public void ManualMoving(Vector3 deltaPosition)
        {
            if (m_isManualMoving)
            {
                Vector3 targetPosition = m_position + deltaPosition;
                m_agent.CalculatePath(targetPosition, m_path);
                if (m_path.status != NavMeshPathStatus.PathInvalid)
                {
                    m_agent.SetDestination(targetPosition);
                }
                else
                {
                    Vector3 direction = targetPosition - m_position;
                    EventManager.instance.Broadcast(EventID.WorldNavagentSteering, Quaternion.LookRotation(direction.normalized));
                }
            }
        }
        public void ManualMoveEnd()
        {
            m_isManualMoving = false;
            if (m_agent != null)
            {
                m_agent.ResetPath();
                m_agent.isStopped = true;
                m_agent.velocity = Vector3.zero;
            }
        }

        public void SetDestination(Vector3 position)
        {
            m_agent.isStopped = false;
            m_agent.velocity = Vector3.zero;
            m_agent.SetDestination(position);
        }

        public void StopMoving()
        {
            ManualMoveEnd();
        }
        #endregion

    }
}
