
using System;
using System.Collections;
using Phoenix.Core;


namespace Phoenix.GameLogic.World
{
    public class WorldEntityAnimationInitializer : Initializer
    {
        public Int32 m_entityId;
        public String m_animationName;
        public String m_animationFullName;

        public void Init(Int32 entityId, String animationName, String animationFullName)
        {
            m_entityId = entityId;
            m_animationName = animationName;
            m_animationFullName = animationFullName;
        }

        protected override IEnumerator OnProcess()
        {
            LoaderContext loaderContext = ClassPoolManager.instance.Fetch<LoaderContext>();
            loaderContext.Initialize(m_entityId);
            loaderContext.AddResourcePath(m_animationFullName, AssetType.Animation);
            LoaderProvider provider = ResourceHandleManager.instance.StartLoad(loaderContext, true, null);
            while (!provider.IsDone)
            {
                yield return null;
            }
            yield return null;
        }
    }
}
