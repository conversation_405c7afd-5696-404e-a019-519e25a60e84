


using System;
using Phoenix.Core;

namespace Phoenix.GameLogic.World
{
    public class WorldProcessAddEntityInteraction : WorldProcessBase
    {
        public Int32 m_entityId;
        public Int32 m_interactionId;

        public override void OnRelease()
        {
            m_entityId = 0;
            m_interactionId = 0;
            base.OnRelease();
        }
        protected override void OnStart()
        {
            EventManager.instance.Broadcast(EventID.WorldEntityInteractionAdd, m_entityId, m_interactionId);
            End();
        }
    }
}
