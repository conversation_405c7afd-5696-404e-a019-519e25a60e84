


using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.World
{
    public class WorldProcessAddEntity : WorldProcessBase
    {
        public WorldEntity m_worldEntity;

        public override void OnRelease()
        {
            m_worldEntity = null;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            if (m_worldEntity != null)
            {
                EventManager.instance.Broadcast(EventID.WorldEntityViewAdd, m_worldEntity);
            }
            End();
        }

    }

}
