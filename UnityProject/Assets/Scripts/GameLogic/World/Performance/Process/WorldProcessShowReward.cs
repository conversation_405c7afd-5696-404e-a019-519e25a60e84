


using System;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic.World
{
    public enum RewardShowType
    {
        None,
        Toast,
        RewardBoxUI,
    }

    public class WorldProcessShowReward : WorldProcessBase
    {
        public Int32 rewardDropId;
        public RewardShowType showType;

        public override void OnRelease()
        {
            rewardDropId = 0;
            showType = RewardShowType.None;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            if (showType == RewardShowType.Toast)
            {
                EventManager.instance.Broadcast(EventID.WorldUI_ShowToastReward, rewardDropId);
            }
            else if (showType == RewardShowType.RewardBoxUI)
            {
                DropGroupConfigData dropGroupConfigData = ConfigDataManager.instance.GetDropGroup(rewardDropId);
                if (dropGroupConfigData != null)
                {
                    CommonRewardUI.ShowUI(ItemUtility.GetRewardItems(dropGroupConfigData.Rewards));
                }
            }
            End();
        }
    }
}
