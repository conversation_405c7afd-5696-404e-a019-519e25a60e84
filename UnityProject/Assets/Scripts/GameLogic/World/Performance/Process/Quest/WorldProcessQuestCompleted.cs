


using System;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.World
{
    public class WorldProcessQuestCompleted : WorldProcessBase
    {
        public Int32 questId;
        public Int32 questGroupId;
        public Int32 questGroupLastQuestId;

        public override void OnRelease()
        {
            questId = 0;
            questGroupId = 0;
            questGroupLastQuestId = 0;
            base.OnRelease();
        }

        protected override void OnStart()
        {
            Boolean delayEnd = false;
            if (questGroupLastQuestId == questId)
            {
                QuestGroupConfigData questGroupConfig = ConfigDataManager.instance.GetQuestGroup(questGroupId);
                if (questGroupConfig != null && questGroupConfig.CompletedTip)
                {
                    delayEnd = true;
                    EventManager.instance.Broadcast(EventID.WorldQuestGroupCompleted, questId, questGroupId);
                }
            }
            EventManager.instance.Broadcast(EventID.WorldQuestCompleted, questId, questGroupId);
            QuestConfigData questConfig = ConfigDataManager.instance.GetQuest(questId);
            if (questConfig.CompletedTip)
            {
                // 目前完成任务没有反馈
            }
            if (delayEnd)
            {
                DelayEnd(WorldConfigSetting.instance.m_questUpdateDuration);
            }
            else
            {
                End();
            }
        }
    }
}
