
using System;
using UnityEngine;

namespace Phoenix.GameLogic.World
{
    public class WorldActionProcessCameraFocus : WorldActionProcessBase
    {
        public Int32 m_speed;
        public Single m_posX;
        public Single m_posY;
        public Single m_posZ;
        public override void OnRelease()
        {
            m_speed = 0;
            m_posX = 0;
            m_posY = 0;
            m_posZ = 0;
            base.OnRelease();
        }

        public override void OnParseParamter()
        {
            m_speed = GetParam<Int32>(0);
            m_posX = GetParam<Int32>(1) / 10f;
            m_posY = GetParam<Int32>(2) / 10f;
            m_posZ = GetParam<Int32>(3) / 10f;
        }

        protected override void OnStart()
        {
            Vector3 targetPosition = new Vector3(m_posX, m_posY, m_posZ);
            WorldManager.instance.m_cameraHandler.SetCameraMoveMode(CameraMoveModeId.Custom);
            WorldManager.instance.m_cameraHandler.SetCameraPositionWithSpeed(targetPosition, m_speed, End);
        }
    }


    public class WorldActionProcessCameraFocusEntity : WorldActionProcessBase
    {
        public Int32 m_speed;
        public Int32 m_entityId;
        public override void OnRelease()
        {
            m_speed = 0;
            m_entityId = 0;
            base.OnRelease();
        }

        public override void OnParseParamter()
        {
            m_speed = GetParam<Int32>(0);
            m_entityId = GetParam<Int32>(1);
        }

        protected override void OnStart()
        {
            WorldEntityView worldEntityView = WorldManager.instance.m_worldEntityHandler.GetWorldEntityView(m_entityId);
            if (worldEntityView != null)
            {
                WorldManager.instance.m_cameraHandler.SetCameraMoveMode(CameraMoveModeId.Custom);
                WorldManager.instance.m_cameraHandler.SetCameraPositionWithSpeed(worldEntityView.GetEntityPosition(), m_speed, End);
            }
            else
            {
                End();
            }
        }
    }
}
