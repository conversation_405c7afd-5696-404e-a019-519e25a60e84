


using System;
using UnityEngine;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.World
{
    public class WorldActionProcessAddEntity : WorldActionProcessBase
    {
        public Int32 m_entityId;


        public override void OnRelease()
        {
            m_entityId = 0;
            base.OnRelease();
        }

        public override void OnParseParamter()
        {
            m_entityId = GetParam<Int32>(0);
        }

        protected override void OnStart()
        {
            WorldManager.instance.WorldBase.AddOrRemoveEntity(true, m_entityId, false);
            End();
        }

    }

    public class WorldActionProcessRemoveEntity : WorldActionProcessBase
    {
        public Int32 m_entityId;

        public override void OnRelease()
        {
            m_entityId = 0;
            base.OnRelease();
        }

        public override void OnParseParamter()
        {
            m_entityId = Int32.Parse(m_paramter);
        }

        protected override void OnStart()
        {
            WorldManager.instance.WorldBase.AddOrRemoveEntity(false, m_entityId, false);
            End();
        }

    }
}
