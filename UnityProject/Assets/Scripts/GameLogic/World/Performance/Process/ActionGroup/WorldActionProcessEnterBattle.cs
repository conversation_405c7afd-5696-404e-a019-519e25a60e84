
using System;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.World
{
    public class WorldActionProcessEnterBattle : WorldActionProcessBase
    {
        public Int32 m_worldBattleId;

        public override void OnRelease()
        {
            m_worldBattleId = 0;
            base.OnRelease();
        }

        public override void OnParseParamter()
        {
            m_worldBattleId = GetParam<Int32>(0);
        }

        protected override void OnStart()
        {
            Int32 worldBattleId = m_worldBattleId;
            WorldHelper.StartWorldBattle(worldBattleId, BattleType.QuestBattle, End);
        }
    }
}
