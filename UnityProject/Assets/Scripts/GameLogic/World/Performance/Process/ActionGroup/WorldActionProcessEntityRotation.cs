


using System;
using UnityEngine;

namespace Phoenix.GameLogic.World
{
    public class WorldActionProcessEntityRotation : WorldActionProcessBase
    {
        public Int32 m_entityId;
        public Int32 m_rotation;
        public override void OnRelease()
        {
            m_entityId = 0;
            m_rotation = 0;
            base.OnRelease();
        }

        public override void OnParseParamter()
        {
            m_entityId = GetParam<Int32>(0);
            m_rotation = GetParam<Int32>(1);
        }

        protected override void OnStart()
        {
            WorldEntityView worldEntityView = WorldManager.instance.m_worldEntityHandler.GetWorldEntityView(m_entityId);
            if (worldEntityView != null)
            {
                Quaternion quaternion = Quaternion.Euler(Vector3.up * m_rotation);
                worldEntityView.SetEntityRotation(quaternion);
            }
            End();
        }
    }
}
