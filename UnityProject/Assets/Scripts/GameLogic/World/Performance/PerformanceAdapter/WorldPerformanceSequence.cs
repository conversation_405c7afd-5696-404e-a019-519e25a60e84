

using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.GameLogic.World
{
    public class WorldPerformanceSequence : WorldPerformanceBase
    {
        public List<WorldProcessBase> worldProcesses = new List<WorldProcessBase>();

        public override void OnRelease()
        {
            worldProcesses.Clear();
        }

        public void AddProcess(WorldProcessBase process)
        {
            worldProcesses.Add(process);
        }

        protected override void OnCollectProcess(SequenceProcess sequenceProcess)
        {
            foreach(var process in worldProcesses)
            {
                sequenceProcess.AddProcess(process);
            }
        }
    }
}
