


using System;
using UnityEngine;
using Phoenix.ConfigData;
using Phoenix.Core;
using YooAsset;

namespace Phoenix.GameLogic.World
{
    public class WorldPerformanceExecuteActionGroup : WorldPerformanceBase
    {
        public Int32 worldActionGroupId;
        public Camera mainCamera;

        public override bool DisableWorldUI => true;


        public override void OnRelease()
        {
            worldActionGroupId = 0;
            mainCamera = null;
            base.OnRelease();
        }

        protected override void OnCollectProcess(SequenceProcess sequenceProcess)
        {
            WorldActionGroupConfig actionGroup = null;
            WorldActionGroupConfigData actionGroupConfig = ConfigDataManager.instance.GetWorldActionGroup(worldActionGroupId);
            if (actionGroupConfig != null)
            {
                AssetHandle assetHandle = ResourceManager.instance.LoadSync<WorldActionGroupConfig>(actionGroupConfig.ActionAssetPath);
                if (assetHandle != null)
                {
                    actionGroup = assetHandle.AssetObject as WorldActionGroupConfig;
                    assetHandle.Release();
                }
            }
            if (actionGroup  != null)
            {
                foreach (WorldActionConfig actionConfig in actionGroup.m_actions)
                {
                    ProcessBase process = CreateProcess(actionConfig);
                    if (process != null)
                    {
                        sequenceProcess.AddProcess(process);
                    }
                }
            }
        }


        protected override void OnPerformanceEnd()
        {
            WorldManager.instance.m_cameraHandler.SetCameraMoveMode(CameraMoveModeId.Follow);
        }


        public ProcessBase CreateProcess(WorldActionConfig actionConfig)
        {
            WorldActionProcessBase result = null;
            switch (actionConfig.m_actionType.value)
            {
                case WorldActionType.Delay:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessDelay>();
                    break;
                case WorldActionType.Talk:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessTalk>();
                    break;
                case WorldActionType.AddEntity:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessAddEntity>();
                    break;
                case WorldActionType.RemoveEntity:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessRemoveEntity>();
                    break;
                case WorldActionType.EntityMove:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessEntityMove>();
                    break;
                case WorldActionType.PlayEntityAnimation:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessPlayEntityAnimation>();
                    break;
                case WorldActionType.EntityRotation:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessEntityRotation>();
                    break;
                case WorldActionType.EntityForward:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessEntityForward>();
                    break;
                case WorldActionType.CameraFocus:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessCameraFocus>();
                    break;
                case WorldActionType.CameraFocusEntity:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessCameraFocusEntity>();
                    break;
                case WorldActionType.EnterBattle:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessEnterBattle>();
                    break;
                case WorldActionType.ScreenFade:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessScreenFade>();
                    break;
                case WorldActionType.PlayTimeline:
                    WorldActionProcessPlayTimeline process = ClassPoolManager.instance.Fetch<WorldActionProcessPlayTimeline>();
                    process.m_mainCamera = mainCamera;
                    result = process;
                    break;
                case WorldActionType.EntityEmojiHud:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessEntityEmojiHud>();
                    break;
                case WorldActionType.TriggerGuide:
                    result = ClassPoolManager.instance.Fetch<WorldActionProcessTriggerGuide>();
                    break;
            }
            if (result != null)
            {
                result.m_paramter = actionConfig.m_paramter;
            }
            return result;
        }
    }
}
