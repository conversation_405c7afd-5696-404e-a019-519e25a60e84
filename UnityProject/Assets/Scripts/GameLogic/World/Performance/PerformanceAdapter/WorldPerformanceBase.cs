


using System;
using Phoenix.Core;

namespace Phoenix.GameLogic.World
{
    public class WorldPerformanceBase : ClassPoolObj
    {
        public virtual Boolean DisableWorldUI { get { return false; } }

        public void PerformanceBegin()
        {
            OnPerformanceBegin();
        }
        public void PerformanceEnd()
        {
            OnPerformanceEnd();
        }

        public void CollectProcess(SequenceProcess sequenceProcess)
        {
            OnCollectProcess(sequenceProcess);
        }

        protected virtual void OnCollectProcess(SequenceProcess sequenceProcess) { }


        protected virtual void OnPerformanceBegin() { }
        protected virtual void OnPerformanceEnd() { }
    }
}
