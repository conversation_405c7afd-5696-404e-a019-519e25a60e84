
using System;
using System.IO;
using Unity.AI.Navigation;
using UnityEditor;
using UnityEngine;
using UnityEngine.AI;
using Phoenix.GameLogic.World;

namespace Phoenix.GameLogic.World
{
    [RequireComponent(typeof(NavMeshSurface))]
    public class BakeNavmeshData : MonoBehaviour
    {
        NavMeshSurface navMeshSurface;

        public void BuildNavMesh()
        {
            navMeshSurface = GetComponent<NavMeshSurface>();
            navMeshSurface.BuildNavMesh();
        }

        public void BuildNewNavmesh()
        {
            navMeshSurface = GetComponent<NavMeshSurface>();
            // 烘焙NavMesh
            navMeshSurface.BuildNavMesh();

            // 获取生成的NavMesh数据
            NavMeshData navMeshData = navMeshSurface.navMeshData;

            // 保存NavMesh数据为资产文件
#if UNITY_EDITOR
            var activeScenePath = navMeshSurface.gameObject.scene.path;
            if (!string.IsNullOrEmpty(activeScenePath))
            {
                String targetPath = Path.Combine(Path.GetDirectoryName(activeScenePath), Path.GetFileNameWithoutExtension(activeScenePath));
                var combinedAssetPath = Path.Combine(targetPath, "NavMesh-" + navMeshSurface.name + ".asset");
                combinedAssetPath = AssetDatabase.GenerateUniqueAssetPath(combinedAssetPath);
                AssetDatabase.CreateAsset(navMeshData, combinedAssetPath);
                AssetDatabase.SaveAssets();
            }
#endif
        }
    }
}


#if UNITY_EDITOR
[CustomEditor(typeof(BakeNavmeshData), false)]
[CanEditMultipleObjects]
public class BackNavmeshDataEditor : Editor
{
    private BakeNavmeshData m_bakeNavmesh;
    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        EditorGUILayout.BeginVertical("HelpBox");
        {
            EditorGUILayout.BeginHorizontal();
            {
                if (m_bakeNavmesh == null)
                {
                    m_bakeNavmesh = target as BakeNavmeshData;
                    if (m_bakeNavmesh == null) return;
                }
                if (GUILayout.Button("ReBuildNavmesh", GUILayout.Width(120)))
                {
                    m_bakeNavmesh.BuildNavMesh();
                }
                if (GUILayout.Button("BuildNewNavmesh", GUILayout.Width(120)))
                {
                    m_bakeNavmesh.BuildNewNavmesh();
                }
            }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndVertical();
    }
}

#endif