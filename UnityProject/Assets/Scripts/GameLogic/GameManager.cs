using AK.Wwise;
using Phoenix.Core;
using UnityEngine;
using Phoenix.GameLogic.UI;
using Phoenix.Core.Entity;
using MyFramework;
using Phoenix.Common.Network;
using YooAsset;
using State = Phoenix.Core.State;

namespace Phoenix.GameLogic
{
    public enum EGameState
    {
        Init,
        Login,
        Lobby,
        Battle,
        World,
        Hakoniwa,
    }

    public class GameManager : MonoSingleton<GameManager>
    {
        private StateMachine m_stateMachine = new StateMachine();

        public StateMachine stateMachine
        {
            get { return m_stateMachine; }
        }

        protected override void OnInit()
        {
            State[] states = new State[]
            {
                new GameStateInit(),
                new GameStateLogin(),
                new GameStateLobby(),
                new GameStateBattle(),
                new GameStateWorld(),
                new GameStateHakoniwa(),
            };
            m_stateMachine.Init(states);
            RegisterListener();
            InitDebugUtility();
            InitGlobalDefine();
        }

        private void InitDebugUtility()
        {
            DebugUtility.actionOnLog = Debug.Log;
            DebugUtility.actionOnLogWarning = Debug.LogWarning;
            DebugUtility.actionOnLogError = Debug.LogError;
        }

        protected override void OnUnInit()
        {
            m_stateMachine.UnInit();
            RemoveListener();
            GameContext.GamePlayerContext.DestroyInstance();
            var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            bool? isConnectServer = gameIni.GetBool("ConnectServer");
            if (isConnectServer.HasValue && isConnectServer.Value)
            {
                NetworkClient.DestroyInstance();
            }
            EntityAdmin.DestroyInstance();
            WwiseAudioManager.Instance.UnInit();
            WwiseHelper.IsGameRunning = false;

            AsyncActionManager.DestroyInstance();

            base.OnUnInit();
        }

        private void RegisterListener()
        {
        }

        private void RemoveListener()
        {
        }

        private void InitGlobalDefine()
        {
            ClassPoolManager.CreateInstance();
#if UNITY_EDITOR
            var classPoolDebugSetting = UnityEditor.AssetDatabase.LoadAssetAtPath<Phoenix.Core.ClassPoolDebugSetting>("Assets/Res/Setting/ClassPoolDebugSetting.asset");
            ClassPoolManager.instance.SetDebugHelper(classPoolDebugSetting);
#endif
            ResourceManager.CreateInstance();
            ResourceManager.instance.InitLoader(false);
            GameObjectPoolManager.CreateInstanceByResources("Assets/Res/Common/Prefabs/PoolManager.prefab", true);
            SceneLayerManager.CreateInstanceByResources("Assets/Res/Common/Prefabs/SceneLayerManager.prefab", true);
            //BPTaskManager.CreateInstanceByResources("Assets/Res/Common/Prefabs/BPTaskManager.prefab", true);
            InitWwise();
            AudioManager.CreateInstanceByResources("Assets/Res/Common/Prefabs/AudioManager.prefab", true);
            TouchEffectManager.CreateInstanceByResources("Assets/Res/TouchEffect/Prefab/TouchEffectManager.prefab", true);
            EventManager.CreateInstance();
            TimerManager.CreateInstance();
            var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            bool? isConnectServer = gameIni.GetBool("ConnectServer");
            if (isConnectServer.HasValue && isConnectServer.Value)
            {
                NetworkClient.CreateInstance();
            }
            EntityAdmin.CreateInstance();
            AsyncActionManager.CreateInstance(true);
        }

        private void Update()
        {
            TimeSlice timeSlice = new TimeSlice();
            timeSlice.deltaTime = Time.deltaTime;
            timeSlice.unscaledDeltaTime = Time.unscaledDeltaTime;
            m_stateMachine.Tick(timeSlice);
            Phoenix.Core.TickManager.instance.Tick(timeSlice);
            WwiseAudioManager.Instance.Tick();
        }

        public void StartGame()
        {
            m_stateMachine.ChangeState((int)EGameState.Init);
        }

        private bool InitWwise()
        {
            AssetHandle wwiseBanksManifestHandle = ResourceManager.instance.LoadSync<WwiseBanksManifest>("Assets/Res/Wwise/ScriptableObjects/WwiseBanksManifest.asset");
            if (wwiseBanksManifestHandle == null)
            {
                WwiseLogManager.PrintLogError($"[{nameof(GameManager)}] 初始化Wwise失败. 加载\"{nameof(WwiseBanksManifest)}\"失败.\n路径\"Assets/Res/Wwise/ScriptableObjects/WwiseBanksManifest.asset\".");
                return false;
            }
            var wwiseBanksManifest = wwiseBanksManifestHandle.AssetObject as WwiseBanksManifest;
            AssetHandle akWwiseInitializationSettingsHandle = ResourceManager.instance.LoadSync<AkWwiseInitializationSettings>("Assets/Res/Wwise/ScriptableObjects/AkWwiseInitializationSettings.asset");
            if (akWwiseInitializationSettingsHandle == null)
            {
                WwiseLogManager.PrintLogError($"[{nameof(GameManager)}] 初始化Wwise失败. 加载\"{nameof(AkWwiseInitializationSettings)}\"失败.\n路径\"Assets/Res/Wwise/ScriptableObjects/AkWwiseInitializationSettings.asset\".");
                return false;
            }

            var akWwiseInitializationSettings = akWwiseInitializationSettingsHandle.AssetObject as AkWwiseInitializationSettings;

            {
                //适配热更新模式下的加载路径问题
                SetWwiseBasePath(akWwiseInitializationSettings);
            }
            
            bool result = WwiseAudioManager.Instance.Init(akWwiseInitializationSettings, wwiseBanksManifest);
            if (result)
            {
                WwiseHelper.IsGameRunning = true;
            }
            else
            {
                WwiseLogManager.PrintLogError($"[{nameof(GameManager)}] 初始化Wwise失败.");
            }
            wwiseBanksManifestHandle.Dispose();
            akWwiseInitializationSettingsHandle.Dispose();
            return result;
        }

        private void SetWwiseBasePath(AkWwiseInitializationSettings settings)
        {
            string platform = GetPlatformName();
            // 处理Host模式下wwise的加载路径问题
            var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            int ? playMode = gameIni.GetInt("PlayMode");
            EPlayMode ePlayMode = (EPlayMode)playMode;
            if (ePlayMode == EPlayMode.HostPlayMode)
            {
                ResourcePackage defaultPackage = YooAssets.GetPackage("DefaultPackage");
                // string localRawFolder = $"{Application.persistentDataPath}/Bundles/RawFiles";
                string sandboxRootDir = defaultPackage.GetPackageSandboxRootDirectory();
                string localRawFolder =  $"{sandboxRootDir}/DefaultPackage/RawFiles";

                string basePath = "Assets/Res/Wwise/Banks";

                // settings.UserSettings.m_BasePath = $"{localRawFolder}/{basePath}/{platform}";

                // string perPath = settings.AdvancedSettings.m_SoundBankPersistentDataPath;
                
                settings.AdvancedSettings.m_SoundBankPersistentDataPath = $"{localRawFolder}/{basePath}/{platform}";
            }
        }


        private string GetPlatformName()
        {
            #if UNITY_EDITOR
			    return UnityEditor.EditorUserBuildSettings.activeBuildTarget.ToString();
            #else
                if (Application.platform == RuntimePlatform.Android)
                    return "Android";
                else if (Application.platform == RuntimePlatform.IPhonePlayer)
                    return "iOS";
                else if (Application.platform == RuntimePlatform.OSXPlayer)
                    return "Mac";
                else
                    return "Windows";
            #endif
        }
    }
}

