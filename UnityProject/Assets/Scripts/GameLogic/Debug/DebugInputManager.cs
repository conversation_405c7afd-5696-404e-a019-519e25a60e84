using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public class DebugInputManager : Singleton<DebugInputManager>
    {
        private DebugInputRecord m_record;
        private float m_startTime;

        public void StartNewRecord(DebugInputRecord.RecordType recordType)
        {
            StopRecord();
            m_record = new DebugInputRecord();
            m_record.type = recordType;
            m_startTime = Time.time;
        }

        public void StopRecord()
        {
            return;
            if (m_record != null)
            {
                var jsonStr = JsonUtility.ToJson(m_record, true);
                string fileName = string.Format("DebugInput_{0}_{1}.txt", m_record.type.ToString(), DateTime.Now.ToString("yyyyMMdd_HHmmss"));
                string filePath = FilePathUtility.GetPath(Application.persistentDataPath, fileName);
                FileUtility.WriteText(filePath, jsonStr);
            }
        }

        public void RecordUIClick(PointerEventData eventData)
        {
            float curTime = Time.time - m_startTime;
            var item = new DebugInputRecord.Item();
            item.time = curTime;
            item.touchName = eventData.pointerClick?.name;
            item.posX = (int)eventData.position.x;
            item.posY = (int)eventData.position.y;
            item.isUI = true;
            m_record.itemList.Add(item);
        }
    }
}
