
namespace Phoenix.GameLogic
{
    public static partial class UnityLayerUtility
    {
        public static int GetLayerMask(ELayerMaskType type)
        {
            return m_layerMaskList[(int)type];
        }

        public static bool GetLayerInMask(int mask, EUnityLayerType layerType)
        {
            return (mask & (1 << (int)layerType)) > 0;
        }

        public static int SetLayerInMask(int mask, EUnityLayerType layerType, bool flag)
        {
            if (flag)
            {
                return mask | (1 << (int)layerType);
            }
            else
            {
                return mask & ~(1 << (int)layerType);
            }
        }

        public static bool GetLayerInMask(ELayerMaskType maskType, EUnityLayerType layerType)
        {
            return GetLayerInMask(GetLayerMask(maskType), layerType);
        }

        public static int SetLayerInMask(ELayerMaskType maskType, EUnityLayerType layerType, bool flag)
        {
            return SetLayerInMask(GetLayerMask(maskType), layerType, flag);
        }
    }
}
