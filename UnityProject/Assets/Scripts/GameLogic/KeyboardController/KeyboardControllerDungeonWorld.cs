//using System;
//using System.Collections;
//using System.Collections.Generic;
//using UnityEngine;
//using Phoenix.Core;

//namespace Phoenix.GameLogic
//{
//    public class KeyboardControllerDungeonWorld : MonoBehaviour
//    {
//        private void Update()
//        {
//            Vector2 vector = Vector2.zero;
//            if (Input.GetKey(KeyCode.W))
//            {
//                vector.y = 1f;
//            }
//            else if (Input.GetKey(KeyCode.S))
//            {
//                vector.y = -1f;
//            }
//            if (Input.GetKey(KeyCode.A))
//            {
//                vector.x = -1;
//            }
//            else if (Input.GetKey(KeyCode.D))
//            {
//                vector.x = 1;
//            }
//            if(vector.sqrMagnitude > 1e-4)
//            {
//                Move(vector, Input.GetKey(KeyCode.LeftShift));
//            }
//            else if((Input.GetKeyUp(KeyCode.W) || Input.GetKeyUp(KeyCode.S) || Input.GetKeyUp(KeyCode.A) || Input.GetKeyUp(KeyCode.D)))
//            {
//                StopMove();
//            }
//        }

//        private void Move(Vector2 vector, bool isRun)
//        {
//            isRun = true;
//            float moveRate = isRun ? 1f : 0.3f;
//            //InputEventData eventData = new InputEventData()
//            //{
//            //    direction = vector.normalized,
//            //    moveRate = moveRate,
//            //    deltaPosition = vector
//            //};
//            //InputManager.instance.ExecuteEvent(InputEventId.ThirdPersonActorStartMoveDir, eventData);
//        }

//        private void StopMove()
//        {
//            //InputManager.instance.ExecuteEvent(InputEventId.ThirdPersonActorStopMove);
//        }
//    }
//}
