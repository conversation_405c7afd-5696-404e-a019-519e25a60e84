using System.Collections;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic
{
    public enum EWindowId
    {
        None,
        Lobby,
        Loading,
        BattleMain,
        BattleTop,
        BattlePrepare,
        BattlePrepareCover,
        BattleResult,
    }

    public static class UIWindowDefine
    {
        public static void DefineAfterGameInit()
        {
        }
    }
}
