using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using UnityEngine;

namespace Phoenix.GameLogic
{
    public class UIInitializer : Initializer
    {
        protected override IEnumerator OnProcess()
        {
            var assetHandle = ResourceManager.instance.LoadAsync<UIConfigInventory>(UIConfigInventory.path);
            while (!assetHandle.IsDone)
            {
                yield return null;
            }
            UIConfigInventory inventory = assetHandle.AssetObject as UIConfigInventory;
            if (inventory != null)
            {
                foreach (UIConfig config in inventory.configList)
                {
                    if (config == null)
                    {
                        continue;
                    }
                    UIManager.instance.RegisterConfig(config);
                }
            }
            //assetHandle.Release();
            Debug.Log("[Initializer] UIInitializer");
        }
    }
}
