using System.Collections;
using Phoenix.Core;
using Phoenix.ConfigData;
using UnityEngine;
using System.Collections.Generic;
using YooAsset;

namespace Phoenix.GameLogic
{
    public class ConfigDataInitializer : Initializer
    {
        private Dictionary<string, AssetHandle> m_assetHandleMap = new Dictionary<string, AssetHandle>();
        private bool m_isDone;

#if UNITY_EDITOR
        public static void InitImmediately_Editor()
        {
            ConfigDataManager.instance.LoadAllSyncByFile(ConfigDataSetting.instance.resPath);
        }
#endif

        protected override IEnumerator OnProcess()
        {
            string formatPath = ConfigDataManager.instance.GetFilePathFormatByFolder(ConfigDataSetting.instance.resPath, false);
            foreach (var key in ConfigDataManager.instance.GetLoaderKeys())
            {
                string path = string.Format(formatPath, key);
                AssetHandle assetHandle = ResourceManager.instance.LoadAsync<TextAsset>(path, OnLoadEnd);
                m_assetHandleMap.Add(key, assetHandle);
            }
            while (!m_isDone)
            {
                yield return null;
            }
            foreach(var kv in m_assetHandleMap)
            {
                kv.Value.Release();
                ResourceManager.instance.TryUnloadUnusedAsset(kv.Value.GetAssetInfo().AssetPath);
            }
            m_assetHandleMap.Clear();
            ConfigDataManager.instance.AnalyzeAfterLoadAll();
            Debug.Log("[Initializer] ConfigDataInitializer");
        }

        private void OnLoadEnd(AssetHandle assetHandle)
        {
            TextAsset textAsset = assetHandle.AssetObject as TextAsset;
            if (textAsset != null)
            {
                string key = string.Empty;
                foreach(var kv in m_assetHandleMap)
                {
                    if(kv.Value == assetHandle)
                    {
                        key = kv.Key;
                        break;
                    }
                }
                ConfigDataManager.instance.Load(key, textAsset.bytes);
            }
            if (CheckIsAllDone())
            {
                m_isDone = true;
            }
        }

        private bool CheckIsAllDone()
        {
            foreach (var kv in m_assetHandleMap)
            {
                if (!kv.Value.IsDone)
                {
                    return false;
                }
            }
            return true;
        }
    }
}
