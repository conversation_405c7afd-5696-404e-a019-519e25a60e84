using System.Collections;
using Phoenix.Core;
using UnityEngine;
using MyFramework;

namespace Phoenix.GameLogic
{
    public class PatchDataInitializer : Initializer
    {
        protected override IEnumerator OnProcess()
        {
            GameINI gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            GameStateFSM fsm = CommonArchitecture.Interface.GetUtility<GameStateFSM>();
            fsm.StartFSM((YooAsset.EPlayMode)(gameIni.GetInt("PlayMode").Value));
            yield return null;

            PatchManagerFSM patch = CommonArchitecture.Interface.GetUtility<PatchManagerFSM>();            
            while (!patch.IsEnd)
            {
                yield return null;
            }
            Debug.Log("[Initializer] PatchDataInitializer");
        }
    }
}