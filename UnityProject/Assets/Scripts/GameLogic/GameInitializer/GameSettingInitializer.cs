using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using YooAsset;

namespace Phoenix.GameLogic
{
    public class GameSettingInitializer : Initializer
    {
        private List<LoadHandler> m_handlerList = new List<LoadHandler>();

        protected override IEnumerator OnProcess()
        {
            m_handlerList.Add(new LoadHandler<ConfigDataSetting>());
            m_handlerList.Add(new LoadHandler<BattleParamSetting>());
            m_handlerList.Add(new LoadHandler<FileOutputSetting>());
            m_handlerList.Add(new LoadHandler<CommonPrefabPathSetting>());
            m_handlerList.Add(new LoadHandler<WorldConfigSetting>());
            m_handlerList.Add(new LoadHandler<CinemachineCameraSetting>());
            foreach (var handler in m_handlerList)
            {
                handler.StartLoad();
            }
            foreach (var handler in m_handlerList)
            {
                while (!handler.isLoadEnd)
                {
                    yield return null;
                }
            }
            //foreach (var handler in m_handlerList)
            //{
            //    handler.ReleaseHandle();
            //}
            //m_handlerList.Clear();
            Debug.Log("[Initializer] GameSettingInitializer");
        }

        private class LoadHandler<T> : LoadHandler where T : SettingBase<T>
        {
            public override void StartLoad()
            {
                m_assetHandle = ResourceManager.instance.LoadAsync<T>(SettingBase<T>.path, OnLoadEnd);
            }

            private void OnLoadEnd(AssetHandle handle)
            {
                m_isDone = true;
                SettingBase<T>.instance = handle.AssetObject as T;
            }
        }

        private abstract class LoadHandler
        {
            protected AssetHandle m_assetHandle;
            protected bool m_isDone;

            public bool isLoadEnd
            {
                get { return m_isDone; }
            }

            public abstract void StartLoad();

            public void ReleaseHandle()
            {
                m_assetHandle.Release();
                m_assetHandle = null;
            }
        }
    }
}
