using System.Collections;
using Phoenix.Core;
using UnityEngine;
using Phoenix.Guide;

namespace Phoenix.GameLogic
{
    public class GameInitializer : Initializer
    {


        public GameInitializer()
        {
            // AddSubInitializer(new PatchDataInitializer());
            AddSubInitializer(new PipelineSettingInitializer());
            AddSubInitializer(new GameSettingInitializer());
            AddSubInitializer(new ConfigDataInitializer());
            AddSubInitializer(new UIInitializer());
            AddSubInitializer(new GuideInitializer());
        }

        protected override IEnumerator OnProcess()
        {
            //Application.targetFrameRate = 60;
            yield return null;
            Debug.Log("[Initializer] GameInitializer");
        }
    }
}
