using UnityEngine;
using UnityEngine.Playables;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public class TimelineAudioHandler : MonoBehaviour
    {
        public PlayableDirector timelineDirector;
        public TimelineAudioManager audioManager;

        TimelineAudioManager GetAudioManager()
        {
            if(audioManager == null)
            {
                audioManager = GetComponent<TimelineAudioManager>();
            }
            return audioManager; 
        }

        void Start()
        {
            if(timelineDirector == null)
            {
                timelineDirector = GetComponent<PlayableDirector>();
            }
            // Subscribe to the director's events
            if (timelineDirector != null)
            {
                OnTimelinePlayed(timelineDirector);
                timelineDirector.stopped += OnTimelineStopped;
            }
        }

        void OnTimelinePlayed(PlayableDirector director)
        {
            if (GetAudioManager() != null)
            {
                GetAudioManager().StopAllAudioSources();
            }
        }

        void OnTimelineStopped(PlayableDirector director)
        {
            if (GetAudioManager() != null)
            {
                GetAudioManager().ResumeAllAudioSources();
            }
        }

        void OnDestroy()
        {
            // Unsubscribe from the director's events
            if (timelineDirector != null)
            {
                timelineDirector.played -= OnTimelinePlayed;
                timelineDirector.stopped -= OnTimelineStopped;
            }
        }
    }
}
