
using System;
using System.Collections;
using Phoenix.ConfigData;
using Phoenix.Core;


namespace Phoenix.GameLogic
{
    public class TimelineInitializer : Initializer
    {
        public TimelineInfoConfigData m_timelineInfo;
        public Boolean m_isAsync;

        protected override IEnumerator OnProcess()
        {
            yield return LoadWorldConfigAsset(m_timelineInfo);
            yield return null;
        }

        private IEnumerator LoadWorldConfigAsset(TimelineInfoConfigData timelineInfo)
        {
            if (timelineInfo != null)
            {
                LoaderContext loaderContext = ClassPoolManager.instance.Fetch<LoaderContext>();
                loaderContext.Initialize(0);
                loaderContext.AddResourcePath(timelineInfo.AssetPath, AssetType.Prefab);
                LoaderProvider provider = ResourceHandleManager.instance.StartLoad(loaderContext, m_isAsync, null);
                while (!provider.IsDone)
                {
                    yield return null;
                }
            }
        }
    }
}
