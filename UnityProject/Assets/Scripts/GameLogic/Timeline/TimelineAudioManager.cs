using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.GameLogic
{
    public class TimelineAudioManager : MonoBehaviour
    {
        private List<AudioSource> allAudioSources = new List<AudioSource>(8);
        const string msAudioTag = "AudioSource";

        void Awake()
        {
            // Find all AudioSource components in the scene
            var objs = GameObject.FindGameObjectsWithTag(msAudioTag);
            allAudioSources.Clear();
            foreach (GameObject obj in objs)
            {
                var allAudioSource = obj.GetComponentsInChildren<AudioSource>();
                allAudioSources.AddRange(allAudioSource);
            }
            
        }

        public void StopAllAudioSources()
        {
            foreach (AudioSource audioSource in allAudioSources)
            {
                audioSource.Pause(); // Alternatively, you can use audioSource.Stop() if you want to stop it completely
            }
        }

        public void ResumeAllAudioSources()
        {
            foreach (AudioSource audioSource in allAudioSources)
            {
                audioSource.UnPause(); // Use audioSource.Play() if you used Stop() to resume playing from the beginning
            }
        }
    }
}
