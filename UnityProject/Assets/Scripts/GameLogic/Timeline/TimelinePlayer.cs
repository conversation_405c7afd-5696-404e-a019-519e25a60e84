
using Cinemachine;
using System;
using UnityEngine;
using UnityEngine.Playables;
using Phoenix.ConfigData;
using Phoenix.Core;


namespace Phoenix.GameLogic
{
    public enum TimelineStatus
    {
        None,
        Loading,
        Playing,
        Stoped = None,
    }


    public class TimelinePlayer
    {
        private Boolean m_isInited = false;

        private const Single TOLERANCE = 0.03f;
        private TimelineStatus m_status;
        private Int32 m_timelineId;
        private Vector3 m_position;
        private Quaternion m_rotation;
        private DirectorWrapMode m_playMode;

        private Boolean m_needDespawnRoot;


        private GameObject m_timelineRoot;
        private PlayableDirector m_playableDirector;
        private Single m_curTime;
        private double m_durationTime;
        private Action m_eventOnTimelinePlayEnd;
        private Camera m_mainCamera;
        private CinemachineBrain m_brain;
        private SceneLayerCanvas m_sceneCanvasLayer;



        public void Init(Camera mainCamera)
        {
            if (!m_isInited)
            {
                EventManager.instance.RegisterListener(EventID.Timeline_Skip, StopTimeline);
                m_isInited = true;
            }
            m_mainCamera = mainCamera;
            m_brain = m_mainCamera?.GetComponent<CinemachineBrain>();
        }

        public void UnInit()
        {
            if (m_isInited)
            {
                EventManager.instance.UnRegisterListener(EventID.Timeline_Skip, StopTimeline);
                m_isInited = false;
            }
            m_mainCamera = null;
            m_brain = null;
        }


        public void Tick(TimeSlice ts)
        {
            Tick(ts.deltaTime);
        }

        public void Tick(Single deltaTime)
        {
            if (m_status == TimelineStatus.Playing)
            {
                if (m_playableDirector == null)
                {
                    StopTimeline();
                    return;
                }
                //if (m_playableDirector.state == PlayState.Playing)
                //{
                //}
                m_curTime += deltaTime;
                if (m_curTime + TOLERANCE > m_durationTime && m_playMode == DirectorWrapMode.None)
                {
                    StopTimeline();
                    return;
                }

                if (!m_playableDirector.gameObject.activeSelf)
                {
                    StopTimeline();
                    return;
                }
            }
        }

        public void PlayTimeline(Int32 timelineId, Vector3 position, Quaternion rotation, DirectorWrapMode mode, Action onPlayEnd = null)
        {
            StopTimeline();
            m_timelineId = timelineId;
            m_position = position;
            m_rotation = rotation;
            m_playMode = mode;
            m_eventOnTimelinePlayEnd = onPlayEnd;
            TimelineInfoConfigData timelineInfo = ConfigDataManager.instance.GetTimelineInfo(timelineId);
            if (timelineInfo != null)
            {
                StartLoad(false, timelineInfo);
            }
            else
            {
                EventOnTimelinePlayEnd(timelineId);
            }
        }


        public void PlayTimeline(GameObject go, Vector3 position, Quaternion rotation, Action onPlayEnd = null)
        {
            StopTimeline();
            m_timelineRoot = go;
            m_position = position;
            m_rotation = rotation;
            m_playMode = DirectorWrapMode.None;
            m_eventOnTimelinePlayEnd = onPlayEnd;
            PlayInternal();
        }

        public void PlayTimeline(GameObject go, Action onPlayEnd = null)
        {
            StopTimeline();
            m_timelineRoot = go;
            m_position = go.transform.position;
            m_rotation = go.transform.rotation;
            m_playMode = DirectorWrapMode.None;
            m_eventOnTimelinePlayEnd = onPlayEnd;
            PlayInternal();
        }


        public void StopTimeline()
        {
            if (m_status == TimelineStatus.Stoped)
            {
                return;
            }
            if (m_playableDirector != null)
            {
                m_playableDirector.Stop();
                m_playableDirector.extrapolationMode = DirectorWrapMode.None;
                m_playableDirector = null;
            }
            if (m_sceneCanvasLayer != null)
            {
                SceneLayerManager.instance.PopLayer(m_sceneCanvasLayer, true);
                m_sceneCanvasLayer = null;
            }
            if (m_timelineRoot != null)
            {
                if (m_needDespawnRoot)
                {
                    ResourceHandleManager.instance.DespawnGameObject(m_timelineRoot);
                }
                m_timelineRoot.SetActiveSafely(false);
                m_timelineRoot = null;
            }
            EventManager.instance.Broadcast(EventID.Timeline_Stop, m_timelineId);
            Int32 tempTimelineId = m_timelineId;
            m_timelineId = -1;
            m_status = TimelineStatus.Stoped;
            m_needDespawnRoot = false;
            EventOnTimelinePlayEnd(tempTimelineId);
            m_playMode = DirectorWrapMode.None;
        }

        private void StartLoad(Boolean async, TimelineInfoConfigData timelineInfo)
        {
            m_status = TimelineStatus.Loading;
            if (async)
            {
                TimelineInitializer initializer = new TimelineInitializer();
                initializer.m_timelineInfo = timelineInfo;
                initializer.m_isAsync = async;
                initializer.StartAsync(LoadAsyncEnd);
            }
            else
            {
                LoaderContext loaderContext = ClassPoolManager.instance.Fetch<LoaderContext>();
                loaderContext.Initialize(0);
                loaderContext.AddResourcePath(timelineInfo.AssetPath, AssetType.Prefab);
                LoaderProvider provider = ResourceHandleManager.instance.StartLoad(loaderContext, false, null);
                TimelineLoadCompletedInternal(timelineInfo.Id);
            }
        }

        private void LoadAsyncEnd(Initializer initializer)
        {
            TimelineInitializer timelineInitializer = initializer as TimelineInitializer;
            if (timelineInitializer != null)
            {
                if (timelineInitializer.m_timelineInfo.Id == m_timelineId)
                {
                    TimelineLoadCompletedInternal(m_timelineId);
                }
                else
                {
                    StopTimeline();
                }
            }
        }
        private void TimelineLoadCompletedInternal(Int32 timelineId)
        {
            if (m_status == TimelineStatus.Loading)
            {
                TimelineInfoConfigData timelineInfo = ConfigDataManager.instance.GetTimelineInfo(timelineId);
                m_timelineRoot = ResourceHandleManager.instance.SpawnGameObject(timelineInfo.AssetPath);
                m_needDespawnRoot = true;
                if (m_timelineRoot != null)
                {
                    PlayInternal();
                }
                else
                {
                    StopTimeline();
                }
            }
            else
            {
                StopTimeline();
            }
        }

        private void PlayInternal()
        {
            EventManager.instance.Broadcast(EventID.Timeline_Start, m_timelineId);
            m_timelineRoot.transform.position = m_position;
            m_timelineRoot.transform.rotation = m_rotation;
            m_timelineRoot.SetActiveSafely(true);
            m_sceneCanvasLayer = m_timelineRoot.GetComponentInChildren<SceneLayerCanvas>();
            if (m_sceneCanvasLayer != null)
            {
                SceneLayerManager.instance.PushLayer(m_sceneCanvasLayer, true);
            }

            PlayableDirector pd = m_timelineRoot.GetComponent<PlayableDirector>();
            if (!PlayableDirectorPlayInternal(pd))
            {
                StopTimeline();
            }
        }

        private Boolean PlayableDirectorPlayInternal(PlayableDirector playable)
        {
            if (playable == null)
            {
                return false;
            }
            m_status = TimelineStatus.Playing;
            m_playableDirector = playable;
            m_playableDirector.extrapolationMode = m_playMode;
            BindCinemachineBrain(m_playableDirector);
            m_curTime = 0;
            m_durationTime = m_playableDirector.duration;
            m_playableDirector.Play();
            return true;
        }

        private void BindCinemachineBrain(PlayableDirector playableDirector)
        {
            if (m_mainCamera == null || m_brain == null)
            {
                return;
            }
            foreach (var item in playableDirector.playableAsset.outputs)
            {
                if (item.outputTargetType == typeof(CinemachineBrain))
                    playableDirector.SetGenericBinding(item.sourceObject, m_brain);
            }
        }

        private void EventOnTimelinePlayEnd(Int32 timelineId)
        {
            if (m_eventOnTimelinePlayEnd != null)
            {
                Action tempAction = m_eventOnTimelinePlayEnd;
                m_eventOnTimelinePlayEnd = null;
                tempAction.Invoke();
            }
        }
    }
}
