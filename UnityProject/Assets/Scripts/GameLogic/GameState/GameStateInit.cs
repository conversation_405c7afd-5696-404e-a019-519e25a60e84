
using MyFramework;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic
{
    public class GameStateInit : State
    {
        private GameInitializer m_initializer;

        public override int stateIndex
        {
            get { return (int)EGameState.Init; }
        }

        protected override void OnEnter(StateContext context)
        {
            m_initializer = new GameInitializer();
            m_initializer.StartAsync(OnGameInitEnd);
        }

        protected override void OnExit()
        {
            m_initializer = null;
        }

        private void OnGameInitEnd(Initializer initializer)
        {
            FinishGameInit();
        }

        private void FinishGameInit()
        {
#if !RELEASE
            UIManager.instance.Open<DebugUI>(false, true);
#endif
            UIManager.instance.Open<GameCommonUI>(false, true);
            var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            if (gameIni.GetBool("TestMode").Value)
            {
                BattleLaunchUtility.EnterBattleByRecordOrInvoke(() =>
                {
                    m_ownerMachine.ChangeState((int)EGameState.Lobby);
                });
            }
            else
            {
                m_ownerMachine.ChangeState((int)EGameState.Login);
            }
        }
    }
}
