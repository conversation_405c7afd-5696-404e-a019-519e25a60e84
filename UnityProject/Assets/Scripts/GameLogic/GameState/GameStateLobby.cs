using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.GameLogic.UI;
using State = Phoenix.Core.State;
using StateContext = Phoenix.Core.StateContext;
using System.Collections;
using System;
using Phoenix.GameLogic.World;
using Phoenix.Guide;
using Phoenix.GameLogic.Battle;
using Phoenix.Common.Network;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameLogic
{
    public class GameStateLobby : State
    {
        public override int stateIndex
        {
            get { return (int)EGameState.Lobby; }
        }

        protected override void OnEnter(StateContext context)
        {
            GuideManager.CreateInstance();
            EventManager.instance.RegisterListener<int>(EventID.BattleStart_StartRequest, OnBattleStartRequestByBtnClick);
            EventManager.instance.RegisterListener<int>(EventID.WorldEnter_StartRequest, OnWorldStartRequestByBtnClick);
            NetworkClient.instance.RegisterCallback<MsgPack_Duel_Ntf>(OnReciveBattleInviteNtf);
            NetworkClient.instance.RegisterCallback<MsgPack_DuelBattleSessionInit_Ntf>(OnBattleSessionInitNtf);
            Loading.Open(LoadingFlagId.LobbyInitializer, LoadingViewType.FadeBlack, () =>
            {
                LobbyInitializer initializer = new LobbyInitializer();
                initializer.StartAsync(OnLoadEnd);
            });
        }

        protected override void OnExit()
        {
            UIManager.instance.CloseUIWithTag(UITagId.Lobby);
            EventManager.instance.UnRegisterListener<int>(EventID.BattleStart_StartRequest, OnBattleStartRequestByBtnClick);
            EventManager.instance.UnRegisterListener<int>(EventID.WorldEnter_StartRequest, OnWorldStartRequestByBtnClick);
            NetworkClient.instance.UnRegisterCallback<MsgPack_Duel_Ntf>();
            NetworkClient.instance.UnRegisterCallback<MsgPack_DuelBattleSessionInit_Ntf>();
        }

        private void OnLoadEnd(Initializer initializer)
        {
            Loading.Close(LoadingFlagId.LobbyInitializer);
        }

        private void OnBattleStartRequestByBtnClick(int id)
        {
            BattleLaunchUtility.StartBattle(id);
        }

        private void OnWorldStartRequestByBtnClick(Int32 worldMapId)
        {
            WorldHelper.EnterWorld(worldMapId);
        }


        private void OnReciveBattleInviteNtf(MsgPackStructBase repsonse)
        {
            MsgPack_Duel_Ntf msgPack = repsonse as MsgPack_Duel_Ntf;
            if (msgPack != null)
            {
                MessageBox.Show($"收到{msgPack.SrcUid}的切磋邀请?", "通知", () =>
                {
                    Send_BattleInviteReplyReq(msgPack.SrcUid, true);
                }, () =>
                {
                    Send_BattleInviteReplyReq(msgPack.SrcUid, false);
                }, MessageBoxType.System);
            }
        }

        private async void Send_BattleInviteReplyReq(Int64 srcUid, Boolean accept)
        {
            MsgPack_DuelReply_Req msgPack = new MsgPack_DuelReply_Req();
            msgPack.Init();
            msgPack.SrcUid = srcUid;
            msgPack.IsAccept = accept;

            NetworkTaskBase inviteReqTask = new NetworkTaskBase(msgPack);
            await inviteReqTask.SendAndReceiveAsync();
        }

        private void OnBattleSessionInitNtf(MsgPackStructBase repsonse)
        {
            MsgPack_DuelBattleSessionInit_Ntf ntf = repsonse as MsgPack_DuelBattleSessionInit_Ntf;
            if (ntf != null)
            {

                BattleSessionInitData sessionInitData = new BattleSessionInitData();
                sessionInitData.SessionId = ntf.SessionId;
                sessionInitData.RandomSeed = ntf.RandomSeed;
                sessionInitData.BattleRid = ntf.BattleRid;
                sessionInitData.PlayerIds = new System.Collections.Generic.List<ulong>(ntf.PlayerIds);
                BattleLaunchUtility.StartBattle(sessionInitData);
            }
        }



        private class LobbyInitializer : Initializer
        {
            protected override IEnumerator OnProcess()
            {
                var identify = new RuntimeSceneIdentify(@"Assets/Res/Scene/Basic/LobbyScene.unity");
                var operation = RuntimeSceneManager.instance.ChangeScene(identify, true, null);
                if (!operation.isDone)
                {
                    yield return null;
                }
                UIManager.instance.Open<LobbyUI>(false);
                yield return null;
            }
        }
    }
}

