using Phoenix.Battle;

namespace Phoenix.GameLogic
{
    public class GameStateContextBattle : Core.StateContext
    {
        public int battleConfigId;
        public BattleRecordMsg battleRecord;
        public BattleSessionInitData battleSessionInitData;

        public override void OnRelease()
        {
            battleConfigId = default;
            battleRecord = null;
            battleSessionInitData = null;
            base.OnRelease();
        }
    }
}
