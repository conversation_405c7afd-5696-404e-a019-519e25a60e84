using System;
using System.Collections.Generic;
using Phoenix.Battle;

namespace Phoenix.GameLogic
{
    public class GameStateContextHakoniwaBattle : Core.StateContext
    {
        public Int32 battleId;
        public GridPosition playerInitPosition;
        public List<Int32> actors = new List<Int32>();
        public List<InitActorInfo> enemys = new List<InitActorInfo>();


        public override void OnRelease()
        {
            battleId = 0;
            playerInitPosition = GridPosition.invalid;
            actors.Clear();
            enemys.Clear();
            base.OnRelease();
        }
    }

    public class InitActorInfo
    {
        public Int32 m_actorUid;
        public GridPosition m_grid;
        public Int32 m_hpRate;

        public InitActorInfo(Int32 actorUid)
        {
            m_actorUid = actorUid;
            m_grid = GridPosition.invalid;
            m_hpRate = 10000;
        }

        public InitActorInfo(Int32 actorUid, GridPosition grid)
        {
            m_actorUid = actorUid;
            m_grid = grid;
            m_hpRate = 10000;
        }
        public InitActorInfo(Int32 actorUid, Int32 hpRate)
        {
            m_actorUid = actorUid;
            m_grid = GridPosition.invalid;
            m_hpRate = hpRate;
        }
    }
}
