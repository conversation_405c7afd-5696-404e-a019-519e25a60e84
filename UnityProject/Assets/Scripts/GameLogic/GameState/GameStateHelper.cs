

using System;
using Phoenix.GameLogic.UI;

namespace Phoenix.GameLogic
{
    public static class GameStateHelper
    {
        public static void ChangeGameState(EGameState state, LoadingFlagId loadingFlag, Action onLoadingOpenEnd = null)
        {
            Loading.Open(loadingFlag, LoadingViewType.FadeBlack, () =>
            {
                onLoadingOpenEnd?.Invoke();
                GameManager.instance.stateMachine.ChangeState((int)state);
                Loading.Close(loadingFlag);
            });

        }
    }
}
