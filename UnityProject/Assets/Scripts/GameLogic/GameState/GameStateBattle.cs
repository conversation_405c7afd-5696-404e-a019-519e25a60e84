using System;
using System.Collections.Generic;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.Core;
using Phoenix.GameLogic.Battle;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.TouchEvent;
using Phoenix.GameLogic.UI;
using Phoenix.Guide;
using Phoenix.Hakoniwa;
using Sirenix.Utilities;

namespace Phoenix.GameLogic
{
    public class GameStateBattle : State
    {
        private BattleExecuterClient m_battleExecuter;
        private BattleExecuterServer m_battleExecuterServer;
        private BattleScene m_battleScene;
        private EntityViewLoaderHandler m_entityViewLoaderHandler;
        private BattleTouchEventHandler m_screenTouchHandler;
        private BattleKeyboardEventHandler m_keyboardEventHandler;
        private BattleUIStateHandler m_battleUIStateHandler;

        private BattleInitData m_battleInitData;
        private BattleRecordMsg m_battleRecord;

        private bool m_isLoadEnd;
        private bool m_isNet;

        private const bool m_dummyServer = false;
        private const float m_dummyLag = 0.2f;

        //假的后面用正式的
        private const ulong m_tempHostPlayerId = 12345;

        public override int stateIndex
        {
            get { return (int)EGameState.Battle; }
        }

        public BattleExecuterClient battleExecuter
        {
            get { return m_battleExecuter; }
        }

        public BattleScene battleScene
        {
            get { return m_battleScene; }
        }

        protected override void OnEnter(StateContext context)
        {
            m_isLoadEnd = false;
            m_isNet = false;
            DebugInputManager.instance.StartNewRecord(DebugInputRecord.RecordType.Battle);

            if (context is GameStateContextBattle battleContext) InitBattle(battleContext);
            if (context is GameStateContextHakoniwaBattle hakoniwaBattleContext) InitBattle(hakoniwaBattleContext);

            m_screenTouchHandler = new BattleTouchEventHandler();
            m_screenTouchHandler.Init();
            m_keyboardEventHandler = new BattleKeyboardEventHandler();
            m_keyboardEventHandler.Init();
            m_entityViewLoaderHandler = new EntityViewLoaderHandler();
            m_entityViewLoaderHandler.Init();
            m_battleUIStateHandler = new BattleUIStateHandler();
            m_battleUIStateHandler.Init();

            BattleHelper.UpdateBattleSpeedTimeScale();
            StartLoad();
        }

        private void InitBattle(GameStateContextBattle battleContext)
        {
            if (battleContext.battleRecord != null)
            {
                m_battleRecord = battleContext.battleRecord;
                m_battleInitData = BattleRecordUtility.ConvertToBattleInitData(m_battleRecord.battleInitData);
            }
            else if (battleContext.battleSessionInitData != null)
            {
                var initData = battleContext.battleSessionInitData;
                m_battleInitData = CreateBattleInitDataByNet(initData.BattleRid, initData.RandomSeed, initData.PlayerIds);
                m_isNet = true;
            }
            else
            {
                m_battleInitData = CreateBattleInitData(battleContext.battleConfigId, 0);
            }
        }

        private void InitBattle(GameStateContextHakoniwaBattle hakoniwaBattleContext)
        {
            m_battleInitData = CreateBattleInitData(hakoniwaBattleContext.battleId, 0);

            //m_battleInitData.groundStartX = 6;
            //m_battleInitData.groundStartY = 9;
            //m_battleInitData.groundEndX = 13;
            //m_battleInitData.groundEndY = 15;

            PlayerInitData playerInitData = m_battleInitData.playerInitDataList[0];
            playerInitData.dirType = GridDirType.Up;
            playerInitData.pos = hakoniwaBattleContext.playerInitPosition;
            playerInitData.disposedActorRidList.Clear();
            playerInitData.disposedActorRidList.AddRange(hakoniwaBattleContext.actors);
            m_battleInitData.needExpand = true;

            foreach (var enemy in hakoniwaBattleContext.enemys)
            {
                m_battleInitData.disposedActorInitDataList.Add(new BattleDisposedActorInitData()
                {
                    actorUid = enemy.m_actorUid,
                    hpRate = enemy.m_hpRate,
                });

                if (enemy.m_grid.isValid)
                {
                    m_battleInitData.disposedActorPosInitDataList.Add(new BattleDisposedActorPosInitData()
                    {
                        actorUid = enemy.m_actorUid,
                        pos = enemy.m_grid,
                    });
                }
            }
        }



        protected override void OnExit()
        {
            // AudioManager.instance.StopAllLoop();
            DebugInputManager.instance.StopRecord();
            BattleOpModeManager.DestroyInstance();
            if (m_battleScene != null)
            {
                //这个要在BattleOpmode之后处理
                m_battleScene.UnInitBattleScene();
                m_battleScene = null;
            }
            GuideManager.instance.RestartAllTrigger();
            GameObjectPoolManager.instance.Clear();
            UIManager.instance.CloseUIWithTag(UITagId.Battle);
            BattleRecordManager.instance.SaveRunningBattleRecordToHistory();
            BattleRetractManager.DestroyInstance();
            EntityViewManager.DestroyInstance();
            m_battleExecuter.UnInit();
            if (m_dummyServer)
            {
                m_battleExecuterServer.UnInit();
            }
            m_screenTouchHandler?.Uninit();
            m_screenTouchHandler = null;
            m_keyboardEventHandler?.UnInit();
            m_keyboardEventHandler = null;
            m_entityViewLoaderHandler?.UnInit();
            m_entityViewLoaderHandler = null;
            m_battleUIStateHandler?.UnInit();
            m_battleUIStateHandler = null;
            m_battleRecord = null;
            ResourceHandleManager.instance.ReleaseAllEntity();
            if (StaticHakoniwa.IsHakoSceneBattle == false)
            {
                ResourceHandleManager.instance.UnloadUnusedAssets();
            }

            BattleHelper.UpdateBattleSpeedTimeScale(true);
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            base.OnTick(timeSlice);
            if (m_battleExecuter != null && m_isLoadEnd)
            {
                m_battleExecuter.Tick(timeSlice);
            }
            if (m_battleExecuterServer != null && m_isLoadEnd)
            {
                m_battleExecuterServer.Tick(timeSlice);
            }
            if (m_screenTouchHandler != null)
            {
                m_screenTouchHandler.Tick(timeSlice);
            }
            if (m_keyboardEventHandler != null)
            {
                m_keyboardEventHandler.Tick(timeSlice);
            }
            if (m_battleUIStateHandler != null)
            {
                m_battleUIStateHandler.Tick(timeSlice);
            }
        }

        private void StartLoad()
        {
            LoadingUIContext loadingContext = new LoadingUIContext();
            loadingContext.viewType = LoadingViewType.Normal;
            loadingContext.waitFlagIdList.Add(LoadingFlagId.BattleStageInitializer);

            if (StaticHakoniwa.IsHakoSceneBattle)
            {
                BattleInitializer battleInitializer = new BattleInitializer();
                battleInitializer.battleRid = m_battleInitData.battleRid;
                battleInitializer.StartAsync(OnBattleInitializerEnd);
            }
            else
            {
                Loading.Open(LoadingFlagId.BattleInitializer, loadingContext, () =>
                {
                    BattleInitializer battleInitializer = new BattleInitializer();
                    battleInitializer.battleRid = m_battleInitData.battleRid;
                    battleInitializer.StartAsync(OnBattleInitializerEnd);
                });
            }
        }

        private void OnBattleInitializerEnd(Initializer initializer)
        {
            BattleInitializer battleInitializer = initializer as BattleInitializer;
            var unityScene = RuntimeSceneManager.instance.GetScene(BattleInitializer.scenePath).unityScene;
            m_battleScene = new BattleScene();
            m_battleScene.InitBattleScene(unityScene);
            if (m_dummyServer)
            {
                var executer = new BattleExecuterClientNet();
                m_battleExecuterServer = new BattleExecuterServer();
                executer.actionOnSendFrameCmdWrap = OnClientSendCmdWrap;
                m_battleExecuterServer.RegisterAutoSend(OnServerSendCut);
                m_battleExecuter = executer;
            }
            else if (m_isNet)
            {
                var executer = new BattleExecuterClientNet();
                executer.SetHostPlayerId((ulong)Core.Entity.EntityAdmin.instance.CurEntity.UserId);
                m_battleExecuter = executer;
            }
            else
            {
                m_battleExecuter = new BattleExecuterClientLocal();
            }
            var logger = new BattleLogger();
            var infoGetter = new BattleInfoGetter();
            infoGetter.Init(logger);
            m_battleExecuter.Init(m_battleInitData, infoGetter, logger);
            m_battleExecuter.RegisterBattleEnd(OnBattleEnd);
            if (m_dummyServer)
            {
                m_battleExecuterServer.Init(m_battleInitData, infoGetter, logger);
            }
            if (m_battleRecord != null)
            {
                m_battleExecuter.StartBattleByRecord(m_battleRecord, true);
                if (m_dummyServer)
                {
                    //m_battleExecuterServer.StartBattleByRecord(m_battleRecord, true);
                }
            }
            else
            {
                m_battleExecuter.StartBattle();
                if (m_dummyServer)
                {
                    m_battleExecuterServer.StartBattle();
                }
            }
            EventManager.instance.Broadcast(EventID.BattleStart_BattleInitEnd);
            if (StaticHakoniwa.IsHakoSceneBattle == false)
            {
                Loading.Close(LoadingFlagId.BattleInitializer);
            }
            m_isLoadEnd = true;
        }

        private void OnBattleEnd()
        {
        }

        private void OnServerSendCut(FrameCut cut)
        {
            if (m_dummyLag > 0f)
            {
                TimerManager.instance.Start(m_dummyLag, () =>
                {
                    (m_battleExecuter as BattleExecuterClientNet).Receive(cut);
                });
            }
            else
            {
                (m_battleExecuter as BattleExecuterClientNet).Receive(cut);
            }
        }

        private void OnClientSendCmdWrap(FrameCommandWrap wrap)
        {
            if (m_dummyLag > 0f)
            {
                TimerManager.instance.Start(m_dummyLag, () =>
                {
                    var errorCode = m_battleExecuterServer.ReceiveAndExecute(wrap, out FrameCut outCut);
                    if (errorCode == BattleErrorCode.Ok)
                    {
                        (m_battleExecuter as BattleExecuterClientNet).Receive(outCut);
                    }
                });
            }
            else
            {
                var errorCode = m_battleExecuterServer.ReceiveAndExecute(wrap, out FrameCut outCut);
                if (errorCode == BattleErrorCode.Ok)
                {
                    (m_battleExecuter as BattleExecuterClientNet).Receive(outCut);
                }
            }
        }

        private BattleInitData CreateBattleInitDataByNet(int battleRid, int randomSeed, List<ulong> playerIdList)
        {
            BattleInitData battleInitData = new BattleInitData();
            battleInitData.battleRid = battleRid;
            battleInitData.randomSeed = randomSeed;
            battleInitData.startTime = DateTime.Now.ToString(Phoenix.Core.CommonDefine.DateTimeFormat);

            foreach (var playerId in playerIdList)
            {
                PlayerInitData playerData = new PlayerInitData();
                playerData.playerId = playerId;
                battleInitData.playerInitDataList.Add(playerData);
            }
            return battleInitData;
        }

        private BattleInitData CreateBattleInitData(int battleRid, int randomSeed)
        {
            var battleInfo = ConfigDataManager.instance.GetBattle(battleRid);
            BattleInitData battleInitData = new BattleInitData();
            battleInitData.battleRid = battleRid;
            battleInitData.randomSeed = randomSeed;
            battleInitData.startTime = DateTime.Now.ToString(Phoenix.Core.CommonDefine.DateTimeFormat);
            PlayerInitData hostPlayerData = new PlayerInitData();
            hostPlayerData.playerId = m_tempHostPlayerId;
            hostPlayerData.tacticianRid = 101;
            hostPlayerData.tacticianSkillRid = 1002;
            //LoadDecisionDatas(hostPlayerData);

            var configData = ConfigDataManager.instance.GetBattle(battleRid);
            bool findDestiny = false;
            foreach (var kv in ConfigDataManager.instance.collectableActorMap)
            {
                if (kv.Value.GroupId != battleInfo.dispositionGroupId)
                {
                    continue;
                }
                if (kv.Value.IsDestiny && configData.disposedActorList.Contains(kv.Value.EntityConfigId))
                {
                    hostPlayerData.disposedActorRidList.AddNotContains(kv.Value.EntityConfigId);
                    findDestiny = true;
                    break;
                }
            }
            if (!findDestiny)
            {
                foreach (var kv in ConfigDataManager.instance.collectableActorMap)
                {
                    if (kv.Value.GroupId != battleInfo.dispositionGroupId)
                    {
                        continue;
                    }
                    if (kv.Value.IsDestiny)
                    {
                        hostPlayerData.disposedActorRidList.AddNotContains(kv.Value.EntityConfigId);
                        break;
                    }
                }
            }
            foreach (var actorRid in configData.disposedActorList)
            {
                var actor = ConfigDataManager.instance.GetActor(actorRid);
                if (actor != null && !actor.IsDestiny)
                {
                    hostPlayerData.disposedActorRidList.AddNotContains(actorRid);
                }
            }
            foreach (var kv in ConfigDataManager.instance.collectableActorMap)
            {
                if (kv.Value.GroupId != battleInfo.dispositionGroupId)
                {
                    continue;
                }
                if (!kv.Value.IsDestiny)
                {
                    hostPlayerData.disposedActorRidList.AddNotContains(kv.Value.EntityConfigId);
                }
            }

            battleInitData.playerInitDataList.Add(hostPlayerData);
            return battleInitData;
        }

        private void LoadDecisionDatas(PlayerInitData hostPlayerData)
        {
            var decisionDatas = GamePlayerContext.instance.DecisionModule.GetLoadDecisionDatas();
            foreach (var decisionData in decisionDatas)
            {
                var teamDecisionData = new BattleTeamDecisionData();
                teamDecisionData.m_decisionId = decisionData.m_decisionId;
                teamDecisionData.m_decisionName = decisionData.m_decisionName;

                foreach (var orderData in decisionData.m_orders)
                {
                    BattleTeamDecisionItemData itemData = new BattleTeamDecisionItemData();
                    itemData.m_entityElementId = orderData.m_entityElementId;
                    itemData.m_entityCareerId = orderData.m_entityCareerId;
                    itemData.m_entityRespType = orderData.m_entityRespType;
                    itemData.m_actorRid = orderData.m_actorRid;
                    itemData.m_skillSelectType = orderData.m_skillSelectType;
                    itemData.m_skillRid = orderData.m_skillRid;
                    itemData.m_skillTags = orderData.m_skillTags;
                    itemData.m_condition1 = orderData.m_condition1;
                    itemData.m_condition2 = orderData.m_condition2;
                    itemData.m_locateSelect = orderData.m_locateSelect;

                    teamDecisionData.m_items.Add(itemData);
                }

                //hostPlayerData.teamDecisionDatas.Add(teamDecisionData);
            }

        }

    }
}
