
using System;
using System.Collections;
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.UI;
using Phoenix.Guide;

namespace Phoenix.GameLogic
{
    public class GameStateLogin : State
    {
        private Boolean showGuideBattleLevelUI = false;
        public override int stateIndex
        {
            get { return (int)EGameState.Login; }
        }

        protected override void OnEnter(StateContext context)
        {
            GuideManager.DestroyInstance();
            UIManager.instance.CloseUIWithTag(UITagId.Login);
            showGuideBattleLevelUI = false;
            if (context is GameStateContextLogin contextLogin)
            {
                showGuideBattleLevelUI = contextLogin.showGuideBattleLevel;
            }
            GamePlayerContext.CreateInstance();
            Loading.Open(LoadingFlagId.LoginInitializer, LoadingViewType.FadeBlack, () =>
            {
                LoginInitializer initializer = new LoginInitializer();
                initializer.StartAsync(OnSceneLoadEnd);
            });
        }

        private void OnSceneLoadEnd(Initializer initializer)
        {
            Loading.Close(LoadingFlagId.LoginInitializer);
            if (showGuideBattleLevelUI)
            {
                GuideBattleUIOpenContext uiContext = new GuideBattleUIOpenContext();
                uiContext.showGuideBattleLevel = true;
                UIManager.instance.Open(uiContext, true);
            }
            else
            {
                UIManager.instance.Open<LoginUI>(false, true);
            }
        }
        protected override void OnExit()
        {
            GuideManager.CreateInstance();
            UIManager.instance.CloseUIWithTag(UITagId.Login);
        }
    }


    public class LoginInitializer : Initializer
    {
        protected override IEnumerator OnProcess()
        {
            yield return LoadScene();
            yield return null;
        }

        private IEnumerator LoadScene()
        {
            string scenePath = "Assets/Res/Scene/LoginScene/Scene/Login.unity";
            RuntimeSceneLoadOperation worldLogicSceneOperation = RuntimeSceneManager.instance.ChangeScene(scenePath, true, null);
            while (!worldLogicSceneOperation.isDone)
            {
                yield return null;
            }
        }
    }
}
