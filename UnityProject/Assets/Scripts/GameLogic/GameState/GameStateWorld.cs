
using Phoenix.Core;
using Phoenix.GameLogic.GameContext;
using Phoenix.GameLogic.World;

namespace Phoenix.GameLogic
{

    public class GameStateWorld : State
    {
        public override int stateIndex => (int)EGameState.World;


        protected override void OnEnter(StateContext context)
        {
            WorldManager.CreateInstance();
            if (context is GameStateContextWorld worldMapState)
            {
                WorldBase worldBase = GamePlayerContext.instance.WorldModule.EnterWorld(worldMapState.worldId);
                if (worldBase != null)
                {
                    WorldManager.instance.EnterWorld(worldBase);
                }
            }
        }

        protected override void OnExit()
        {
            UIManager.instance.CloseUIWithTag(UITagId.World);
            WorldManager.instance.ExitWorld();
            WorldBase worldBase = GamePlayerContext.instance.WorldModule.GetCurrentWorld();
            GamePlayerContext.instance.WorldModule.ExitWorld(worldBase.WorldId);
            WorldManager.DestroyInstance();
        }

    }
}
