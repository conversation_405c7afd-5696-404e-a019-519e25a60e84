//#define SHOW_TRAIL_TEXTURE
//#define SHOW_HIZ_MAP
//#define SHOW_LOD_LEVEL

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using Phoenix.Core;

namespace Phoenix.Common.Rendering
{
    [Serializable]
    public class WindParameter
    {
        [Tooltip("风场纹理，uv = frac(positionWS.xz * _WindPositionScale + _Time.y * _WindTimeScale) * _WindTextureScale")]
        [SerializeField]
        public Texture2D NoiseTexture = null;

        [Tooltip("世界坐标缩放系数")]
        [SerializeField]
        public float PositionScale = 0.1f;

        [Tooltip("风场纹理缩放系数")]
        [SerializeField]
        public float TextureScale = 1.0f;

        [Tooltip("风场纹理移动速度")]
        [SerializeField]
        public float TimeScale = 0.05f;

        [Tooltip("风场强度，即草被吹倒的最大弧度。")]
        [Range(0.0f, Mathf.PI / 4.0f)]
        [SerializeField]
        public float Amplitude = Mathf.PI / 4.0f;
    }

    public class GrasslandManager : MonoBehaviour
    {
        #region Public Variables
        public int NumOfTrampler => m_NumOfTrampler;
        public Vector3 CameraPosition => m_CameraTransform.position;
        public Matrix4x4 CameraViewProj => m_CameraViewProj;

#if UNITY_EDITOR
        public int InstanceCount;
#endif
        #endregion

        #region Private Variables

        [SerializeField] private WindParameter m_WindParameter;
        //每帧Update更新Trampler性能太差，通过发消息EventID.GrassManager_Trampler_Changed更新，此时修改它为false
        [SerializeField] bool mbUpdateTramplerEveryFrame = true;
        /// <summary>
        /// 踩踏者数据
        /// </summary>
        private Vector4[] m_TramplerData;
        private int m_NumOfTrampler;
        private ComputeBuffer m_TramplerDataBuffer;

        private Camera m_Camera;
        private Transform m_CameraTransform;
        private Matrix4x4 m_CameraViewProj;

        private readonly List<Grassland> m_Grasslands = new List<Grassland>();
        private bool bInit = false;

        // 调试踩踏数据
        //[SerializeField] Material m_TrailMaterial;
        //[SerializeField] private Vector2 m_TrailTextureSize;
        //[SerializeField] private Vector2 m_GrasslandSize;
        //[SerializeField] private Vector3 m_GrasslandOrigin = new Vector3(-5.0f, 0.0f, 5.0f);
        //private RenderTexture m_TrailTexture;

        #endregion

        #region Const Variables
        private const int k_MaxNumOfTrampler = 10;  // 踩踏者数量上限
        private const int k_TramplerDataStride = sizeof(float) * (3 + 1);
        #endregion

        #region MonoBehaviour Functions

        private void Awake()
        {
            Init();
        }

        public void Init()
        {
            if(!bInit)
            {
                m_TramplerData = new Vector4[k_MaxNumOfTrampler];
                for (int i = 0; i < k_MaxNumOfTrampler; i++)
                {
                    m_TramplerData[i] = Vector4.zero;
                }

                m_TramplerDataBuffer = new ComputeBuffer(k_MaxNumOfTrampler, k_TramplerDataStride);
                m_TramplerDataBuffer.SetData(m_TramplerData);

                InitCamera();
                EventManager.instance.RegisterListener(EventID.GrassManager_Trampler_Changed, CollectTramplerData);
                bInit = true;
            }
        }

        private void OnDestroy()
        {
            m_TramplerDataBuffer.Release();
            if (EventManager.hasInstance)
                EventManager.instance.UnRegisterListener(EventID.GrassManager_Trampler_Changed, CollectTramplerData);
        }

        private void Update()
        {
            if (m_Grasslands.Count == 0) // 当前场景没有草地则不做处理
            {
                return;
            }

#if UNITY_EDITOR
            foreach (var grassland in m_Grasslands)
            {
                grassland.SetWindParameter(m_WindParameter);
            }
#endif
            if(mbUpdateTramplerEveryFrame)
                CollectTramplerData();
            if (m_NumOfTrampler != 0)
            {
                m_TramplerDataBuffer.SetData(m_TramplerData);
            }

            if (m_Camera == null) // 切场景后更新摄像机
            {
                InitCamera();
            }
            m_CameraViewProj = GL.GetGPUProjectionMatrix(m_Camera.projectionMatrix, false) * m_Camera.worldToCameraMatrix;
        }

#if UNITY_EDITOR
        void OnGUI()
        {
            GUIStyle labelFont = new GUIStyle { fontSize = 50 };
            labelFont.normal.textColor = Color.white;

            GUILayout.Label($"InstanceCount : {InstanceCount}", labelFont);
        }
#endif

        #endregion

        #region Public Functions
        /// <summary>
        /// 通知当前草地渲染草
        /// </summary>
        /// <param name="commandBuffer"></param>
        /// <param name="hizTexture"></param>
        public void GenerateGrass(CommandBuffer commandBuffer)
        {
            {
#if UNITY_EDITOR
                InstanceCount = 0;
#endif
                foreach (var grassland in m_Grasslands)
                {
                    grassland.GenerateGrass(commandBuffer);
                }
            }
        }

        public void AddGrassland(Grassland grassland)
        {
            if (!enabled) return;

            if (!m_Grasslands.Contains(grassland))
            {
                m_Grasslands.Add(grassland);

                if (m_WindParameter != null)
                {
                    grassland.SetWindParameter(m_WindParameter);
                }
                grassland.SetTramplerDataBuffer(m_TramplerDataBuffer);

                //m_HierarchicalZFeature.SetActive(true); // 添加草地后开启处理流程
            }
        }

        public void RemoveGrassland(Grassland grassland)
        {
            if (!enabled) return;

            if (m_Grasslands.Contains(grassland))
            {
                m_Grasslands.Remove(grassland);
            }
        }

        public int GetGrasslandCount()
        {
            return m_Grasslands.Count;
        }
        #endregion

        #region Private Functions

        private void InitCamera()
        {
            m_Camera = Camera.main;
            m_CameraTransform = m_Camera.transform;
        }

        /// <summary>
        /// 收集踩踏者信息，用于踩踏草地。
        /// </summary>
        private void CollectTramplerData()
        {
            m_NumOfTrampler = 0;

            var tramplers = GameObject.FindGameObjectsWithTag("Trampler");
            if (tramplers.Length != 0)
            {
                Vector4 tramplerData;
                foreach (var unit in tramplers)
                {
                    tramplerData = unit.transform.position; // 践踏者位置

                    var extents = unit.GetComponent<Collider>().bounds.extents;
                    tramplerData.w = Mathf.Max(extents.x, extents.z); // 践踏者半径

                    m_TramplerData[m_NumOfTrampler] = tramplerData;

                    if (++m_NumOfTrampler == k_MaxNumOfTrampler) // 控制数量
                    {
                        break;
                    }
                }
            }
        }
        #endregion
    }
}

