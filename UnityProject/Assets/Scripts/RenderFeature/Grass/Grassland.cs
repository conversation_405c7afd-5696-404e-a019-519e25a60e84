using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;
using UnityEngine.Rendering;

namespace Phoenix.Common.Rendering
{
    /// <summary>
    /// 在每个三角形的重心种草。
    /// 密度大于1则在（递归）细分三角形的重心种草。
    /// </summary>
    public class Grassland : MonoBehaviour
    {
        public ComputeShader ComputeShader
        {
            get { return m_ComputeShader; }
        }

        #region Private Variables
        [SerializeField][Tooltip("草的预设")] private GameObject m_GrassPrefab;
        [SerializeField][Tooltip("生成草的ComputeShader")] private ComputeShader m_ComputeShaderAsset;

        [SerializeField][Range(0, 7)][Tooltip("每个三角形内产生草的数量 = pow(4, density)")] private int m_GrassDensity = 0;
        [SerializeField][Range(0.0f, 5.0f)][Tooltip("草被踩倒后恢复直立的速度")] private float m_Springiness = 0.05f;

        [SerializeField][Tooltip("草的剪裁距离（x：0级，y：1级，z：2级，w：剔除）")] private Vector4 m_CullDistance = Vector4.zero;
        [SerializeField][Tooltip("草的剪裁百分比（有效值[0, 1]）")] private Vector4 m_CullRate = Vector4.zero;
        [SerializeField][Tooltip("草的缩放(X,Y,Z)")] private Vector3 mGrassScale = Vector4.one;
        [SerializeField] private Vector4[] m_SeedPositionArray; // 草籽位置
        [SerializeField] private Vector3 m_LocalMinPosition = Vector3.zero; // 草地左下角（XZ平面局部坐标）
        [SerializeField] private Vector3 m_LocalMaxPosition = Vector3.zero; // 草地右上角（XZ平面局部坐标）

        [SerializeField] private Transform m_CachedTransform;
        [SerializeField] private GrasslandManager m_GrasslandManager;
        /// <summary>
        /// 实例草的数据
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        private struct GrassData
        {
            /// <summary>
            /// 世界矩阵
            /// </summary>
            public Matrix4x4 worldMatrix;
            /// <summary>
            /// 踩踏力度
            /// </summary>
            public float trampleIntensity;
        };

        private ComputeBuffer m_SeedBuffer; // 草的世界坐标
        private ComputeBuffer m_TrampleIntensityBuffer; // 草的践踏力度

        private ComputeBuffer m_GrassDataBuffer; // 实例草
        private ComputeBuffer m_IndirectArgsBuffer; // 实例参数

        private readonly int[] m_ArgsBufferReset = new int[] { 0, 0, 0, 0, 0 }; // index count per instance, instance count, start index location, base vertex location, start instance location

        private ComputeShader m_ComputeShader;
        private int m_Kernel;
        private int m_DispatchSize;

        private Mesh m_GrassMesh;
        private Material m_GrassMaterial;

        private int m_LightmapIndex;
        private Vector4 m_LightmapScaleOffset;

        private Vector3 m_WorldMinPosition; // 草地左下角（XZ平面世界坐标）
        private Vector3 m_WorldMaxPosition; // 草地右上角（XZ平面世界坐标）

        private bool m_ResetData;

        #endregion

        #region Const Variables
        private const int k_SeedPositionStride = sizeof(float) * 4;
        private const int k_TrampleIntensityStride = sizeof(float);
        private const int k_GrassDataStride = sizeof(float) * (16 + 1);
        #endregion

        #region MonoBehaviour Functions
        private void Awake()
        {
            if(m_GrasslandManager == null)
                m_GrasslandManager = FindObjectOfType<GrasslandManager>();
            if (!m_GrasslandManager.enabled)
                return;

            if (m_SeedPositionArray == null || m_SeedPositionArray.Length == 0)
            {
                CalculateSeedPosition();
            }

            m_GrassMesh = m_GrassPrefab.GetComponentInChildren<MeshFilter>().sharedMesh;
            m_ArgsBufferReset[0] = (int)m_GrassMesh.GetIndexCount(0);

            int numOfSeed = m_SeedPositionArray.Length;

            m_SeedBuffer = new ComputeBuffer(numOfSeed, k_SeedPositionStride, ComputeBufferType.Structured, ComputeBufferMode.Immutable);
            m_SeedBuffer.SetData(m_SeedPositionArray);

            m_TrampleIntensityBuffer = new ComputeBuffer(numOfSeed, k_TrampleIntensityStride);

            m_GrassDataBuffer = new ComputeBuffer(numOfSeed, k_GrassDataStride, ComputeBufferType.Append);
            m_GrassDataBuffer.SetCounterValue(0);

            m_IndirectArgsBuffer = new ComputeBuffer(5, sizeof(int), ComputeBufferType.IndirectArguments);

            m_ComputeShader = Instantiate(m_ComputeShaderAsset);
            m_Kernel = m_ComputeShader.FindKernel("Main");
            m_ComputeShader.GetKernelThreadGroupSizes(m_Kernel, out uint threadGroupSize, out _, out _);
            m_DispatchSize = Mathf.CeilToInt((float)numOfSeed / threadGroupSize);

            m_ComputeShader.SetInt("_NumOfSeed", numOfSeed);
            m_ComputeShader.SetFloat("_Springiness", m_Springiness);

            m_ComputeShader.SetVector("_CullDistance", m_CullDistance);
            m_ComputeShader.SetVector("_CullRate", m_CullRate);

            var boundMin = m_GrassMesh.bounds.min;
            var boundMax = m_GrassMesh.bounds.max;
            m_ComputeShader.SetVector("_BoundMin", new Vector4(boundMin.x, boundMin.y, boundMin.z, 1.0f));
            m_ComputeShader.SetVector("_BoundMax", new Vector4(boundMax.x, boundMax.y, boundMax.z, 1.0f));

            m_ComputeShader.SetBuffer(m_Kernel, "_SeedBuffer", m_SeedBuffer);
            m_ComputeShader.SetBuffer(m_Kernel, "_TrampleIntensityBuffer", m_TrampleIntensityBuffer);
            m_ComputeShader.SetBuffer(m_Kernel, "_GrassDataBuffer", m_GrassDataBuffer);
            m_ComputeShader.SetBuffer(m_Kernel, "_IndirectArgsBuffer", m_IndirectArgsBuffer);

            m_GrassMaterial = Instantiate(m_GrassPrefab.GetComponentInChildren<Renderer>().sharedMaterial);
            m_GrassMaterial.SetBuffer("_GrassDataBuffer", m_GrassDataBuffer);

            m_GrassMaterial.EnableKeyword("DIRLIGHTMAP_COMBINED");
            m_GrassMaterial.EnableKeyword("LIGHTMAP_ON");

            var meshRenderer = GetComponent<MeshRenderer>();
            m_LightmapIndex = meshRenderer.lightmapIndex;
            m_LightmapScaleOffset = meshRenderer.lightmapScaleOffset;
        }

        private void OnEnable()
        {
            m_ResetData = true;
            if (m_GrasslandManager != null)
            {
                m_GrasslandManager.Init();
                m_GrasslandManager.AddGrassland(this);
            }
        }

        private void OnDisable()
        {
            if (m_GrasslandManager != null)
            {
                m_GrasslandManager.RemoveGrassland(this);
            }
        }

        private void OnDestroy()
        {
            m_SeedBuffer?.Release();
            m_TrampleIntensityBuffer?.Release();
            m_GrassDataBuffer?.Release();
            m_IndirectArgsBuffer?.Release();
        }
        #endregion

        #region Public Functions
#if UNITY_EDITOR
        /// <summary>
        /// 导入时将草籽数据序列化到预设上
        /// </summary>
        public void SerializePrefabData()
        {
            CalculateSeedPosition();
        }
#endif

        public void GenerateGrass(CommandBuffer commandBuffer)
        {
            if (m_ResetData)
            {
                m_ResetData = false;

                m_WorldMinPosition = m_CachedTransform.TransformPoint(m_LocalMinPosition);
                m_WorldMaxPosition = m_CachedTransform.TransformPoint(m_LocalMaxPosition);
                m_GrassMaterial.SetVector("_FieldSize", new Vector4(m_WorldMinPosition.x, m_WorldMinPosition.z, m_WorldMaxPosition.x - m_WorldMinPosition.x, m_WorldMaxPosition.z - m_WorldMinPosition.z));
                m_ComputeShader.SetMatrix("_FieldWorldMatrix", m_CachedTransform.localToWorldMatrix);
            }

            var cameraPosition = m_GrasslandManager.CameraPosition;
            if (m_WorldMinPosition.z > (cameraPosition.z + m_CullDistance.w)) // 草地位于剔除距离之外，不处理。
            {
                return;
            }

#if UNITY_EDITOR
            commandBuffer.SetComputeVectorParam(m_ComputeShader, "_CullDistance", m_CullDistance);
            commandBuffer.SetComputeVectorParam(m_ComputeShader, "_CullRate", m_CullRate);
#endif

            commandBuffer.SetComputeVectorParam(m_ComputeShader, "_CameraPosition", cameraPosition);
            commandBuffer.SetComputeVectorParam(m_ComputeShader, "_GrassScale", mGrassScale);
            commandBuffer.SetComputeMatrixParam(m_ComputeShader, "_CameraViewProj", m_GrasslandManager.CameraViewProj);
            commandBuffer.SetComputeIntParam(m_ComputeShader, "_NumOfTrampler", m_GrasslandManager.NumOfTrampler);
            commandBuffer.SetBufferCounterValue(m_GrassDataBuffer, 0);
            commandBuffer.SetBufferData(m_IndirectArgsBuffer, m_ArgsBufferReset);
            commandBuffer.DispatchCompute(m_ComputeShader, m_Kernel, m_DispatchSize, 1, 1);

            //// TODO：OnEnable的时候设置一次
            //m_GrassMaterial.SetTexture("_MyLightmap", LightmapSettings.lightmaps[m_LightmapIndex].lightmapColor); // 设置光照图
            //m_GrassMaterial.SetTexture("_MyLightmapInd", LightmapSettings.lightmaps[m_LightmapIndex].lightmapDir);
            //m_GrassMaterial.SetVector("_LightmapScaleOffset", m_LightmapScaleOffset);

            commandBuffer.DrawMeshInstancedIndirect(m_GrassMesh, 0, m_GrassMaterial, 0, m_IndirectArgsBuffer);

#if UNITY_EDITOR
            int[] buffer = new int[5];
            m_IndirectArgsBuffer.GetData(buffer);
            m_GrasslandManager.InstanceCount += buffer[1];
#endif
        }

        public void SetWindParameter(WindParameter windParameter)
        {
            m_GrassMaterial.SetTexture("_WindTexture", windParameter.NoiseTexture);
            m_GrassMaterial.SetFloat("_WindPositionScale", windParameter.PositionScale);
            m_GrassMaterial.SetFloat("_WindTextureScale", windParameter.TextureScale);
            m_GrassMaterial.SetFloat("_WindTimeScale", windParameter.TimeScale);
            m_GrassMaterial.SetFloat("_WindAmplitude", windParameter.Amplitude);
        }

        public void SetTramplerDataBuffer(ComputeBuffer tramplerDataBuffer)
        {
            m_ComputeShader.SetBuffer(m_Kernel, "_TramplerDataBuffer", tramplerDataBuffer);
        }
        #endregion

        #region Private Functions
        // This applies the game object's transform to the local bounds
        // Code by benblo from https://answers.unity.com/questions/361275/cant-convert-bounds-from-world-coordinates-to-loca.html
        private Bounds TransformBounds(Transform transform, Bounds boundsOS)
        {
            var center = transform.TransformPoint(boundsOS.center);

            // transform the local extents' axes
            var extents = boundsOS.extents;
            var axisX = transform.TransformVector(extents.x, 0, 0);
            var axisY = transform.TransformVector(0, extents.y, 0);
            var axisZ = transform.TransformVector(0, 0, extents.z);

            // sum their absolute value to get the world extents
            extents.x = Mathf.Abs(axisX.x) + Mathf.Abs(axisY.x) + Mathf.Abs(axisZ.x);
            extents.y = Mathf.Abs(axisX.y) + Mathf.Abs(axisY.y) + Mathf.Abs(axisZ.y);
            extents.z = Mathf.Abs(axisX.z) + Mathf.Abs(axisY.z) + Mathf.Abs(axisZ.z);

            return new Bounds { center = center, extents = extents };
        }

        private void CreateSubdivisionMesh(Mesh mesh, int level, out Vector3[] outputVertices, out int[] outputTriangles)
        {
            List<Vector3> meshVertices = new List<Vector3>();
            List<int> meshIndices = new List<int>();

            var vertices = mesh.vertices;
            var triangles = mesh.triangles;

            for (int i = 0; i < triangles.Length; i += 3)
            {
                CreateSubdivisionTriangle(level, vertices[triangles[i]], vertices[triangles[i + 1]], vertices[triangles[i + 2]], meshVertices, meshIndices);
            }

            outputVertices = meshVertices.ToArray();
            outputTriangles = meshIndices.ToArray();
        }

        /// <summary>
        /// 沿边线细分一个三角形（不考虑三角形之间共用顶点）
        /// 细分后的三角形数量 = pow(4, level)
        ///       
        ///                 (v1)                               (v1)                               (v1)
        ///                  /\                                 /\                                 /\
        ///                 /  \                               /  \                               /  \
        ///                /    \                             /    \                             /____\ 
        ///               /      \                           /      \                           /\    /\
        ///              /        \                         /        \                         /  \  /  \
        ///             /          \                       /__________\                       /____\/____\ 
        ///            /            \                     /\          /\                     /\    /\    /\
        ///           /              \                   /  \        /  \                   /  \  /  \  /  \
        ///          /                \                 /    \      /    \                 /____\/____\/____\  
        ///         /                  \               /      \    /      \               /\    /\    /\    /\
        ///        /                    \             /        \  /        \             /  \  /  \  /  \  /  \
        ///   (v0)/______________________\(v2)   (v0)/__________\/__________\(v2)   (v0)/____\/____\/____\/____\(v2)
        ///   
        /// 
        ///               (v1)10
        ///                  /\
        ///            j=0  /  \
        ///              ↘6____\11
        ///               /\    /\
        ///         j=0  /  \  /  \
        ///           ↘3____\7____\12
        ///            /\    /\    /\
        ///      j=0  /  \  /  \  /  \
        ///        ↘1____\4____\8____\13
        ///         /\    /\    /\    /\
        ///        /  \  /  \  /  \  /  \
        ///   (v0)0____\2____\5____\9____14(v2)
        ///       ↑     ↑     ↑     ↑     ↑
        ///       i=0   i=1   i=2   i=3   i=4
        ///       
        /// </summary>
        /// <param name="level">细分等级（沿边线切段数量）</param>
        /// <param name="v0">三角形第一个顶点</param>
        /// <param name="v1">三角形第二个顶点</param>
        /// <param name="v2">三角形第三个顶点</param>
        /// <param name="meshVertices">细分模型的顶点列表</param>
        /// <param name="meshIndices">细分模型的索引列表</param>
        private void CreateSubdivisionTriangle(int level, Vector3 v0, Vector3 v1, Vector3 v2, List<Vector3> meshVertices, List<int> meshIndices)
        {
            var baseIndex = meshVertices.Count;

            meshVertices.Add(v0);

            var segment = Mathf.Pow(2, level); // 边线分段数量

            for (int i = 1; i <= segment; i++) // 遍历边线的分段连线
            {
                var v01 = Vector3.Lerp(v0, v1, i / segment);
                var v02 = Vector3.Lerp(v0, v2, i / segment);

                { // 添加分段上的顶点
                    for (int j = 0; j <= i; j++)
                    {
                        meshVertices.Add(Vector3.Lerp(v01, v02, (float)j / i));
                    }
                }

                { // 添加分段上的三角形
                    var index = baseIndex + Accumulate(i);
                    for (int j = 0; j < i - 1; j++)
                    {
                        meshIndices.Add(index);
                        meshIndices.Add(index + 1);
                        meshIndices.Add(index - i);

                        index++;

                        meshIndices.Add(index);
                        meshIndices.Add(index - i);
                        meshIndices.Add(index - i - 1);
                    }

                    meshIndices.Add(index);
                    meshIndices.Add(index + 1);
                    meshIndices.Add(index - i);
                }
            }
        }

        private int Accumulate(int x)
        {
            int result = 0;
            for (int i = 1; i <= x; i++)
            {
                result += i;
            }
            return result;
        }

        private void CalculateSeedPosition()
        {
            m_CachedTransform = transform;
            m_CullRate = Clamp(m_CullRate, 0.0f, 1.0f);

            List<Vector4> seedList = new List<Vector4>();

            var meshFilters = GetComponentsInChildren<MeshFilter>();
            foreach (var meshFilter in meshFilters)
            {
                GetSeedFromMesh(meshFilter, seedList);
            }

            m_SeedPositionArray = seedList.ToArray();
        }

        private void GetSeedFromMesh(MeshFilter meshFilter, List<Vector4> seedList)
        {
            CreateSubdivisionMesh(meshFilter.sharedMesh, m_GrassDensity, out var vertices, out var triangles);

            for (int i = 0; i < triangles.Length; i += 3)
            {
                var position = (vertices[triangles[i]] + vertices[triangles[i + 1]] + vertices[triangles[i + 2]]) / 3; // 三角形重心
                seedList.Add(new Vector4(position.x, position.y, position.z, 1.0f));
            }
        }

        private Vector4 Clamp(Vector4 vector, float minValue, float maxVale)
        {
            return new Vector4(Mathf.Clamp(vector.x, minValue, maxVale), Mathf.Clamp(vector.y, minValue, maxVale), Mathf.Clamp(vector.z, minValue, maxVale), Mathf.Clamp(vector.w, minValue, maxVale));
        }
        #endregion
    }
}

