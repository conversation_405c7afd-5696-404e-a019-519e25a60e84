using System;
using System.Threading.Tasks;
using Phoenix.Core;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Common.Network;
using UnityEngine;

namespace Phoenix.GameLogic.GameContext
{
    public class GamePlayerLogicGlobalSync : GamePlayerLogicBase
    {
        private readonly Int32 _timeHearbeatIntervalMS = 30000;

        private bool _isInitServerTime = false;
        private Int64 _deltaStamp = 0;
        private Int64 _lastSendHeartBeatTime = 0;

        public bool IsTestStopSendHeartBeat { get; set; }

        public override void OnInit()
        {
            base.OnInit();

            NetworkClient.instance.RegisterCallback<MsgPack_ServerTime_Ack>(OnResponseServerTime);
            NetworkClient.instance.RegisterCallback<ServerDisconnectNtf>(OnNotifyServerDisconnect);
        }

        public override void OnUnInit()
        {
            NetworkClient.instance.UnRegisterCallback<MsgPack_ServerTime_Ack>();
            NetworkClient.instance.UnRegisterCallback<ServerDisconnectNtf>();

            base.OnUnInit();
        }

        public override void OnTick(TimeSlice ts)
        {
            SendHeartBeat();
        }

        public async void SendHeartBeat()
        {
            if (IsTestStopSendHeartBeat || GetCurrentTimeMilliSecond() - _lastSendHeartBeatTime < _timeHearbeatIntervalMS)
            {
                return;
            }
            Debug.Log($"SendHeartBeat CurrentTimeMilliSecond = {GetCurrentTimeMilliSecond()}, _lastSendHeartBeatTime = {_lastSendHeartBeatTime}");
            _lastSendHeartBeatTime = GetCurrentTimeMilliSecond();
            MsgPack_HeartBeat_Req msgPack = new MsgPack_HeartBeat_Req();
            NetworkTaskBase requestTask = new NetworkTaskBase(msgPack);
            bool retResult = await requestTask.SendAndReceiveAsync();

            if (retResult)
            {
                var msgData = requestTask.ResponseMsg as MsgPack_HeartBeat_Ack;
                if (msgData == null)
                {
                    Debug.LogError("MsgPack_HeartBeat_Ack response is null");
                    return;
                }

                AlignServerTime(msgData.CurrentServerTime);
            }
        }

        public void RequestServerTime()
        {
            MsgPack_ServerTime_Req msgPack = new MsgPack_ServerTime_Req();
            NetworkTaskBase requestTask = new NetworkTaskBase(msgPack);
            requestTask.SendAsync(OnResponseServerTime);
        }

        private void OnResponseServerTime(MsgPackStructBase response)
        {
            var msgData = response as MsgPack_ServerTime_Ack;
            
            AlignServerTime(msgData.CurrentServerTime);
        }

        private void OnNotifyServerDisconnect(MsgPackStructBase notify)
        {
            var msgData = notify as ServerDisconnectNtf;
            Debug.Log($"OnNotifyServerDisconnect ReasonCode = {msgData.ReasonCode}");
        }

        private void AlignServerTime(Int64 currentServerTime)
        {
            long timestampMilliseconds = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            _deltaStamp = currentServerTime - timestampMilliseconds;
            _isInitServerTime = true;
            Debug.Log($"AlignServerTime _deltaStamp = {_deltaStamp}, ServerRemoteMS = {currentServerTime}, ClientLocalMS = {timestampMilliseconds}");
        }

        private Int64 GetCurrentTimeMilliSecond()
        {
            long timestampMilliseconds = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            return _isInitServerTime ? timestampMilliseconds + _deltaStamp : timestampMilliseconds;
        }

    }
}