using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.GameModel.Base;
using UnityEngine;

namespace Phoenix.GameLogic.GameContext
{
    public interface IGamePlayerLogic
    {
        void OnInit();

        void OnUnInit();

        void SetOwner(GamePlayer gamePlayer);

        void SetComp(IGamePlayerComp iGamePlayerComp);

        T GetComp<T>() where T : class, IGamePlayerComp;

        T AddUI<T>() where T : UIBase;

        void RemoveUI<T>() where T : UIBase;

        T GetUI<T>() where T : UIBase;

        void OnTick(TimeSlice ts);

    }
}

