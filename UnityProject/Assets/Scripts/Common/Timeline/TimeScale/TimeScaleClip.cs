using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

namespace Phoenix.TimelineExtension
{
    [Serializable]
    public class TimeScaleClip : PlayableAsset, ITimelineClipAsset
    {
        public AnimationCurve mCurve = new AnimationCurve();

        public ClipCaps clipCaps
        {
            get { return ClipCaps.None; }
        }

        public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
        {
            TimeScaleBehaviour tls = new TimeScaleBehaviour();
            tls.mCurve = mCurve;
            return ScriptPlayable<TimeScaleBehaviour>.Create(graph, tls);
        }
    }
}
