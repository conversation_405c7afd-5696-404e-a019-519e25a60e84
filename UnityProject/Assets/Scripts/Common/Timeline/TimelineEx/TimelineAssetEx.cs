

using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

namespace Phoenix.TimelineExtension
{
    public class TimelineAssetEx<T> : TimelineAssetEx where T : TimelineBehaviourEx, new()
    {
        public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
        {
            T behaviour = new T();
            behaviour.Init(this);
            return ScriptPlayable<T>.Create(graph, behaviour);
        }
    }


    public abstract class TimelineAssetEx : PlayableAsset, ITimelineClipAsset
    {
        protected TimelineTrackEx m_track;
        protected bool m_isInitialized;

        public TimelineTrackEx track
        {
            get { return m_track; }
        }

        public virtual ClipCaps clipCaps
        {
            get { return ClipCaps.None; }
        }

        public void Init(TimelineTrackEx track)
        {
            m_track = track;
            OnInit();
            m_isInitialized = true;
        }

        protected virtual void OnInit() { }
    }
}
