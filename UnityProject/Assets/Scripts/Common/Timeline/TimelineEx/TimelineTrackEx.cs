

using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

namespace Phoenix.TimelineExtension
{
    [HideInMenu]
    
    public class TimelineTrackEx : PlayableTrack
    {
        protected PlayableDirector m_director;
        private bool m_inited;

        protected virtual bool needInit
        {
            get { return !m_inited; }
        }

        protected virtual void OnInit()
        {
        }

        public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
        {
            base.GatherProperties(director, driver);
            m_director = director;
            Init();
        }

        protected override Playable CreatePlayable(PlayableGraph graph, GameObject go, TimelineClip clip)
        {
            Init();
            TimelineAssetEx exClip = clip.asset as TimelineAssetEx;
            if (exClip != null)
            {
                exClip.Init(this);
            }
            return base.CreatePlayable(graph, go, clip);
        }

        private void Init()
        {
            if (needInit)
            {
                m_inited = true;
                OnInit();
            }
        }
    }
}
