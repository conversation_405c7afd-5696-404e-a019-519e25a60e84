#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core
{
    [CreateAssetMenu(fileName = "AssetSaveSetting", menuName = "Custom Asset/AssetSaveSetting")]
    public abstract class AssetSaveSetting : ScriptableObject
    {
        public abstract void Apply(string path, AssetSaveProcessorSetting.RuleSetting setting);
    }
}
#endif