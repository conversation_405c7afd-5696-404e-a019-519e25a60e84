using UnityEditor;
using UnityEngine.SceneManagement;
using UnityEditor.SceneManagement;
using UnityEngine;

public static class SceneTool
{
    [MenuItem("Assets/删除Scene Missing Prefab")]
    public static void DeleteMissingPrefab()
    {
        Object[] gos = Selection.GetFiltered(typeof(SceneAsset), SelectionMode.DeepAssets);
        foreach (var selectObj in gos)
        {
            SceneAsset sceneAsset = selectObj as SceneAsset;
            var path = AssetDatabase.GetAssetOrScenePath(sceneAsset);
            EditorSceneManager.OpenScene(path);
            Scene currentScene = EditorSceneManager.GetActiveScene();
            GameObject[] rootObjects = currentScene.GetRootGameObjects();

            foreach (GameObject rootObj in rootObjects)
            {
                DeleteMissingPrefabsRecursive(rootObj);
            }

            EditorSceneManager.SaveScene(currentScene);
            EditorSceneManager.CloseScene(currentScene, false);
        }

        EditorSceneManager.OpenScene("Assets/Scenes/StartScene.unity");
    }
    
    public static void DeleteMissingPrefabsRecursive(GameObject obj)
    {
        if (obj == null) return;

        if (PrefabUtility.GetPrefabAssetType(obj) != PrefabAssetType.NotAPrefab &&
            PrefabUtility.GetPrefabInstanceStatus(obj) == PrefabInstanceStatus.MissingAsset)
        {
            // 如果是丢失的预设体，则删除它
            Debug.LogError($"Delete Missing Prefab:{obj.gameObject.name}");
            GameObject.DestroyImmediate(obj);
        }
        else
        {
            int childCount = obj.transform.childCount;
            for (int i = childCount - 1; i >= 0; i--)
            {
                DeleteMissingPrefabsRecursive(obj.transform.GetChild(i).gameObject);
            }
        }
    }

}
