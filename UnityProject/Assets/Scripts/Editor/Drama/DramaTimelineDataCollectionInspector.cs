using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Drama
{
    [CustomEditor(typeof(DramaTimelineDataCollection))]
    public class DramaTimelineDataCollectionInspector : Editor
    {
        private List<string> appendPathList = new List<string>()
        {
            "Assets/Res/Entity/Common/Actor/DramaTimelineCollection_CommonActor.asset",
        };

        public override void OnInspectorGUI()
        {
            if (GUILayout.Button("�ռ�"))
            {
                DramaTimelineDataCollection collection = (DramaTimelineDataCollection)target;
                string assetPath = AssetDatabase.GetAssetPath(collection);
                string folderPath = FilePathUtility.GetFileFolderPath(assetPath);
                if (folderPath != null)
                {
                    List<DramaTimelineData> dataList = new List<DramaTimelineData>();
                    string dramaFolderPath = FilePathUtility.GetPath(folderPath, "Drama");
                    DirectoryInfo directoryInfo = new DirectoryInfo(dramaFolderPath);
                    if (!directoryInfo.Exists)
                    {
                        return;
                    }
                    foreach (var fileInfo in directoryInfo.GetFiles())
                    {
                        if (fileInfo.Name.EndsWith(".meta"))
                        {
                            continue;
                        }
                        string path = AssetPathUtility.GetAssetPath(fileInfo);
                        DramaTimelineData timelineData = AssetDatabase.LoadAssetAtPath<DramaTimelineData>(path);
                        if (timelineData != null)
                        {
                            dataList.Add(timelineData);
                        }
                    }
                    dataList.Sort((x, y) => x.name.CompareTo(y.name));
                    collection.itemList.Clear();
                    foreach (var data in dataList)
                    {
                        var item = new DramaTimelineDataCollection.Item();
                        item.name = data.name;
                        item.drama = AssetWeakRefEditorUtility.GetAssetWeakRef(data);
                        collection.itemList.Add(item);
                    }
                    collection.appendCollectionList.Clear();
                    foreach(var appendPath in appendPathList)
                    {
                        if (appendPath == assetPath)
                        {
                            continue;
                        }
                        var c = AssetDatabase.LoadAssetAtPath<DramaTimelineDataCollection>(appendPath);
                        collection.appendCollectionList.AddNotContainsNotNull(c);
                    }
                    EditorUtility.SetDirty(collection);
                    AssetDatabase.SaveAssets();
                }
            }
            base.OnInspectorGUI();
        }
    }
}
