using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using UnityEditor;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.Core;
using Phoenix.Drama;
using Phoenix.GameLogic.Battle;

public static class PrefabPackTool
{
    private const string PackFolderPath = "Assets/PrefabPack";
    private const string PathRefFilePath = "Assets/DependenceRef.txt";
    private readonly static Dictionary<string, string> ValidExtensionList = new Dictionary<string, string>()
    {
        { "prefab", "Prefab" },
        { "fbx", "Mesh" },
        { "png", "Texture" },
        { "tga", "Texture" },
        { "mat", "Material" },
    };

    [MenuItem("Tools/Refresh ScriptableObject Cache")]
    public static void RefreshCache()
    {
        string[] guids = AssetDatabase.FindAssets("t:DramaTimelineData");

        foreach (string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            var dramaTimelineData = AssetDatabase.LoadAssetAtPath<DramaTimelineData>(path);
            foreach(var drama in dramaTimelineData.dramaList)
            {
                if(drama.dramaType != DramaType.OnHit)
                {
                    continue;
                }
                var dramaOnHit = drama as DramaOnHitData;
                if (dramaOnHit == null)
                {
                    continue;
                }
                var preName = dramaOnHit.effectType.GetName();
                string newName = string.Empty;
                switch (preName)
                {
                    case "DamageEntity":
                        newName = "Damage";
                        break;
                    case "HealEntity":
                        newName = "Heal";
                        break;
                    case "TeleportEntity":
                        newName = "Teleport";
                        break;
                    case "SummonEntity":
                        newName = "Summon";
                        break;
                    case "AttachBuff":
                        newName = "AttachBuff";
                        break;
                    case "DetachBuff":
                        newName = "DetachBuff";
                        break;
                    case "TransformEntity":
                        newName = "Transform";
                        break;
                    case "MoveEntity":
                        newName = "Move";
                        break;
                    case "ChangeAnger":
                        newName = "ChangeTeamEnergy";
                        break;
                    case "EntityExtraMove":
                        newName = "ExtraMove";
                        break;
                    case "EntityExtraAction":
                        newName = "ExtraAction";
                        break;
                    case "ResurrectEntity":
                    case "ActivateEntity":
                    case "None":
                    case "0":
                        newName = "";
                        break;
                    default:
                        Debug.LogError(preName);
                        break;
                        //throw new ToDoException(nameof(PrefabPackTool), nameof(RefreshCache), preName);

                }
                dramaOnHit.effectType.ReplaceName(newName);
            }
            EditorUtility.SetDirty(dramaTimelineData);
        }
        AssetDatabase.SaveAssets();
    }

    [MenuItem("Assets/批处理U角色贴图")]
    public static void HandleTextureFolder()
    {
        var originMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Res/Entity/Actor/gujingling/Static/Model/Material/Hero1200_Skin04_Body_Mat.mat");
        foreach (var selectObj in Selection.objects)
        {
            string assetPath = AssetDatabase.GetAssetPath(selectObj);
            var textureFolder = new DirectoryInfo(assetPath + "/Static/Model/Texture");
            var materialFolder = new DirectoryInfo(assetPath + "/Static/Model/Material");
            foreach (var textureFileInfo in textureFolder.GetFiles())
            {
                if (textureFileInfo.Name.EndsWith("meta"))
                {
                    continue;
                }
                string texturePath = assetPath + "/Static/Model/Texture/" + textureFileInfo.Name;
                string materialPath = assetPath + "/Static/Model/Material/" + FilePathUtility.GetFileNameWithoutExtension(textureFileInfo.Name) + ".mat";
                var mat = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                if(mat == null)
                {
                    mat = new Material(originMat);
                    AssetDatabase.CreateAsset(mat, materialPath);
                }
                mat.CopyPropertiesFromMaterial(originMat);
                mat.SetTexture("_BaseMap", AssetDatabase.LoadAssetAtPath<Texture>(texturePath));
                EditorUtility.SetDirty(mat);
            }
        }
        AssetDatabase.SaveAssets();
    }

    [MenuItem("Assets/批处理Fx文件夹")]
    public static void HandleFxFolder()
    {
        var selectedFolderAsset = Selection.objects[0] as DefaultAsset;
        if (selectedFolderAsset == null)
        {
            return;
        }
        string selectedFolderPath = AssetDatabase.GetAssetPath(selectedFolderAsset);
        string rootFolderPath = FilePathUtility.GetFileFolderPath(selectedFolderPath);
        string fxFolderPath = FilePathUtility.GetPath(rootFolderPath, "Fx");
        if (!Directory.Exists(fxFolderPath))
        {
            Directory.CreateDirectory(fxFolderPath);
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }
        string fxStaticFolderPath = FilePathUtility.GetPath(rootFolderPath, "Static", "Fx");
        if (!Directory.Exists(fxStaticFolderPath))
        {
            Directory.CreateDirectory(fxStaticFolderPath);
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }
        foreach (var directorInfo in new DirectoryInfo(selectedFolderPath).GetDirectories())
        {
            if (directorInfo.Name == "Prefab")
            {
                continue;
            }
            string destName = FilePathUtility.GetPath(fxStaticFolderPath, directorInfo.Name);
            if (Directory.Exists(destName))
            {
                foreach (var fileInfo in directorInfo.GetFiles())
                {
                    string destPath = FilePathUtility.GetPath(destName, fileInfo.Name);
                    if (File.Exists(destPath))
                    {
                        continue;
                    }
                    File.Move(fileInfo.FullName, destPath);
                }
            }
            else
            {
                Directory.Move(directorInfo.FullName, destName);
            }
        }
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        foreach (var fileInfo in new DirectoryInfo(selectedFolderPath).GetFiles())
        {
            if (fileInfo.Name == "Prefab.meta")
            {
                continue;
            }
            string destName = FilePathUtility.GetPath(fxStaticFolderPath, fileInfo.Name);
            if (File.Exists(destName))
            {
                continue;
            }
            File.Move(fileInfo.FullName, destName);
        }
        foreach(var fileInfo in new DirectoryInfo(FilePathUtility.GetPath(selectedFolderPath, "Prefab")).GetFiles())
        {
            File.Move(fileInfo.FullName, FilePathUtility.GetPath(fxFolderPath, fileInfo.Name));
        }
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
    }


    [MenuItem("Assets/批处理创建角色资源文件夹")]
    public static void HandleActorFolder()
    {
        foreach (var selectObj in Selection.objects)
        {
            string assetPath = AssetDatabase.GetAssetPath(selectObj);
            Directory.CreateDirectory(assetPath + "/Static");
            Directory.CreateDirectory(assetPath + "/Static/Model");
            Directory.CreateDirectory(assetPath + "/Static/Model/Mesh");
            Directory.CreateDirectory(assetPath + "/Animation");
            Directory.CreateDirectory(assetPath + "/Animation/Battle");
            Directory.CreateDirectory(assetPath + "/Drama");
            Directory.CreateDirectory(assetPath + "/Skill");
            AssetDatabase.CreateAsset(new AnimationCollection(), assetPath + "/AnimationCollection.asset");
            AssetDatabase.CreateAsset(new ActorAnimationConfig(), assetPath + "/AnimationConfig.asset");
            AssetDatabase.CreateAsset(new DramaTimelineDataCollection(), assetPath + "/DramaCollection.asset");
        }
        AssetDatabase.Refresh();
    }

    [MenuItem("Assets/批处理创建角色资源文件夹中的MaterialImportMode")]
    public static void HandleActorFolder2()
    {
        foreach (var selectObj in Selection.objects)
        {
            string assetPath = AssetDatabase.GetAssetPath(selectObj);
            DirectoryInfo di = new DirectoryInfo(assetPath);
            foreach (var fi in di.GetFiles("*.fbx", SearchOption.AllDirectories))
            {
                string fbxPath = AssetPathUtility.GetAssetPath(fi);
                ModelImporter importer = AssetImporter.GetAtPath(fbxPath) as ModelImporter;
                if (importer != null)
                {
                    importer.materialImportMode = ModelImporterMaterialImportMode.None;
                    importer.SaveAndReimport();
                }
            }
        }
        AssetDatabase.SaveAssets();
    }

    [MenuItem("Assets/批处理U角色资源动画")]
    public static void HandleUActorFolder2()
    {
        foreach (var selectObj in Selection.objects)
        {
            string assetPath = AssetDatabase.GetAssetPath(selectObj);
            Directory.CreateDirectory(assetPath + "/Animation");
            Directory.CreateDirectory(assetPath + "/Animation/Battle");
            MoveDirContent(assetPath + "/Animator/Animation/Static", assetPath + "/Animation/Battle", true);
            MoveDirContent(assetPath + "/Animator/Animation/Dynamic", assetPath + "/Animation/Battle", true);
            DeleteDir(assetPath + "/Animator", true);
        }
        AssetDatabase.Refresh();
    }

    public static List<GameObject> GetPrefabs(string dir)
    {
        List<GameObject> prefabList = new List<GameObject>();
        DirectoryInfo di = new DirectoryInfo(dir);
        if (!di.Exists)
        {
            return prefabList;
        }
        foreach(var file in di.GetFiles("*.prefab"))
        {
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(dir + "/" + file.Name);
            if(prefab != null)
            {
                prefabList.Add(prefab);
            }
        }
        return prefabList;
    }

    [MenuItem("Assets/批处理角色资源Prefab")]
    public static void HandleUActorPrefab2()
    {
        foreach(var obj in Selection.objects)
        {
            GameObject go =obj as GameObject;
            if (go == null)
            {
                return;
            }
            HandleUActorPrefab2(go);
        }
        AssetDatabase.SaveAssets();
    }

    //[MenuItem("Assets/批处理BattleEditorData")]
    //public static void HandleBattleEditorData()
    //{
    //    var battleDataList = AssetFindUtility.FindAllScriptableObjects<BattleEditorData>();
    //    List<int> actorIdList = new List<int>();
    //    foreach (var battleData in battleDataList)
    //    {
    //        foreach (var stage in battleData.stageList)
    //        {
    //            //if (stage.stageType.value == BattleStageType.Scinerio)
    //            //{
    //            //    stage.stageType.value = BattleStageType.Battle;
    //            //}
    //            //Dictionary<int, List<int>> teamMap = new Dictionary<int, List<int>>();
    //            //foreach (var t in stage.playerControlDataList)
    //            //{
    //            //    int slotId = t.playerSlotId;
    //            //    if (slotId != stage.playerControlDataList.GetValueSafely(0)?.playerSlotId)
    //            //    {
    //            //        slotId = 0;
    //            //    }
    //            //    var list = teamMap.GetClassValue(slotId);
    //            //    if(list == null)
    //            //    {
    //            //        list = new List<int>();
    //            //        teamMap.Add(slotId, list);
    //            //    }
    //            //    list.AddRange(t.teamIdList);
    //            //}
    //            foreach (var entity in stage.disposedActorDataList)
    //            {
    //                actorIdList.AddNotContains(entity.actorRid);
    //                //Debug.LogError(entity.actorRid);
    //                //if (removeTeamList.Exists(t => t.id == entity.teamId))
    //                //{
    //                //    entity.teamId = 0;
    //                //}
    //            }
    //            //foreach (var entity in stage.disposedTerrainBuffDataList)
    //            //{
    //            //    if (removeTeamList.Exists(t => t.id == entity.teamId))
    //            //    {
    //            //        entity.teamId = 0;
    //            //    }
    //            //}

    //        }
    //        //EditorUtility.SetDirty(battleData);
    //    }
    //    StringBuilder sb = new StringBuilder();
    //    foreach(var rid in actorIdList)
    //    {
    //        sb.Append(rid).Append(",");
    //    }
    //    Debug.LogError(sb.ToString());
    //    //AssetDatabase.SaveAssets();
    //}

    //private static int GetCampId(int id)
    //{

    //    if (id == 1)
    //    {
    //        return 1001;
    //    }
    //    else if (id == 2)
    //    {
    //        return 1002;
    //    }
    //    if(id == 1001 || id == 1002)
    //    {
    //        return id;
    //    }
    //    return 0;
    //}

    //[MenuItem("Assets/批处理BattleEditorData2")]
    //public static void HandleBattleEditorData2()
    //{
    //    var skillLogicDataList = AssetFindUtility.FindAllScriptableObjects<SkillLogicEditorData>();
    //    foreach (var skillLogicData in skillLogicDataList)
    //    {
    //        foreach (var ss in skillLogicData.buffAttachBeforeList)
    //        {
    //        }
    //        //foreach (var group in skillLogicData.effectGroupDataList)
    //        //{
    //        //    foreach (var effect in group.effectDataList)
    //        //    {
    //        //        if (effect.effectType.value == Phoenix.ConfigData.SkillEffectType.AttachBuff)
    //        //        {
    //        //        }
    //        //        if (effect.effectType.value == Phoenix.ConfigData.SkillEffectType.ExpandBuff)
    //        //        {
    //        //        }
    //        //    }
    //        //}
    //        EditorUtility.SetDirty(skillLogicData);
    //    }
    //    var buffDataList = AssetFindUtility.FindAllScriptableObjects<BuffEditorData>();
    //    foreach (var buffData in buffDataList)
    //    {
    //        foreach (var effect in buffData.effectDataList)
    //        {
    //            if (effect.effectType.value == Phoenix.ConfigData.BuffEffectType.AuraApply)
    //            {
    //            }
    //        }
    //        EditorUtility.SetDirty(buffData);
    //    }
    //    AssetDatabase.SaveAssets();
    //}

    public static void HandleUActorPrefab2(GameObject prefab)
    {
        var animator = prefab.GetOrAddComponent<Animator>();
        if (animator != null)
        {
            animator.runtimeAnimatorController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>("Assets/Res/Entity/Common/Actor/AnimatorController_CommonActor.controller");
        }
        SetEntityViewField(prefab, "m_bindPointHud", "HudRoot");
        SetEntityViewField(prefab, "m_bindPointCameraLook", "LookAt");
        SetEntityViewField(prefab, "m_bindPointHead", "soket_Head");
        SetEntityViewField(prefab, "m_bindPointChest", "soket_Chest");
        SetEntityViewField(prefab, "m_bindPointFoot", "soket_Foot");
        SetEntityViewField(prefab, "m_bindPointLeftHand", "soket_LeftHand");
        SetEntityViewField(prefab, "m_bindPointRightHand", "soket_RightHand");
        SetEntityViewField(prefab, "m_bindPointSpecial01", "soket_Special01");
        SetEntityViewField(prefab, "m_bindPointSpecial02", "soket_Special02");
        SetEntityViewField(prefab, "m_bindPointSpecial03", "soket_Special03");
        EditorUtility.SetDirty(prefab);
    }

    private static void SetEntityViewField(GameObject go, string fieldName, string goName)
    {
        var viewConfig = go.GetOrAddComponent<EntityViewConfig>();
        FieldInfo fi = typeof(EntityViewConfig).GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
        if (fi != null)
        {
            var transList = go.GetComponentsInChildren<Transform>().ToList();
            Transform trans = transList.Find(a => a.name.ToLower() == goName.ToLower());
            if (trans != null)
            {
                fi.SetValue(viewConfig, trans.gameObject);
            }
        }
    }

    private static void MoveDirContent(string dir1, string dir2, bool delete)
    {
        DirectoryInfo di1 = new DirectoryInfo(dir1);
        if (!di1.Exists)
        {
            return;
        }
        if (!Directory.Exists(dir2))
        {
            Directory.CreateDirectory(dir2);
        }
        foreach(var fi in di1.GetFiles())
        {
            fi.MoveTo(dir2 + "/" + fi.Name);
        }
        foreach(var di in di1.GetDirectories())
        {
            di.MoveTo(dir2 + "/" + di.Name);
        }
        if (delete)
        {
            DeleteDir(dir1, true);
        }
    }

    private static void DeleteDir(string dir, bool deleteMeta)
    {
        if (Directory.Exists(dir))
        {
            Directory.Delete(dir, true);
        }
        if (deleteMeta)
        {
            if (File.Exists(dir + ".meta"))
            {
                File.Delete(dir + ".meta");
            }
        }
    }

    [MenuItem("Assets/删除Missing脚本")]
    public static void DeleteMissingComponent()
    {
        foreach (var selectObj in Selection.objects)
        {
            GameObject go = selectObj as GameObject;
            List<Transform> transList = new List<Transform>() { go.transform };
            while(transList.Count > 0)
            {
                Transform trans = transList.First();
                transList.RemoveAt(0);

                GameObjectUtility.RemoveMonoBehavioursWithMissingScript(trans.gameObject);

                for (int i = 0; i < trans.childCount; ++i)
                {
                    transList.Add(trans.GetChild(i));
                }
            }
            EditorUtility.SetDirty(go);
        }
        AssetDatabase.SaveAssets();
    }

    [MenuItem("Assets/删除文件")]
    public static void Delete()
    {
        foreach (var selectObj in Selection.objects)
        {
            string assetPath = AssetDatabase.GetAssetPath(selectObj);
            if (File.Exists(assetPath))
            {
                File.Delete(assetPath);
                File.Delete(assetPath + ".meta");
            }
            else if (Directory.Exists(assetPath))
            {
                DeleteDir(assetPath, true);
            }
        }
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
    }

    private static Dictionary<string, string> ReadPathRefMap()
    {
        Dictionary<string, string> pathRefMap = new Dictionary<string, string>();
        if (File.Exists(PathRefFilePath))
        {
            using (StreamReader file = new StreamReader(PathRefFilePath))
            {
                string str = file.ReadToEnd();
                string[] lineSlipts = str.Split('\n');
                foreach (var lineStr in lineSlipts)
                {
                    if (string.IsNullOrEmpty(lineStr.Trim()))
                    {
                        continue;
                    }
                    string[] pathSlipts = lineStr.Trim().Split(',');
                    pathRefMap[pathSlipts[0].Trim()] = pathSlipts[1].Trim();
                }
            }
        }
        return pathRefMap;
    }

    private static void WritePathRefMap(Dictionary<string, string> pathRefMap)
    {
        using (StreamWriter file = new StreamWriter(PathRefFilePath, false, Encoding.UTF8))
        {
            StringBuilder sb = new StringBuilder();
            foreach(var kv in pathRefMap)
            {
                sb.Append(string.Format("{0},{1}\n", kv.Key, kv.Value));
            }
            file.Write(sb.ToString());
        }
    }
}
