//using System;
//using System.Collections;
//using System.Collections.Generic;
//using System.Reflection;
//using System.Runtime.InteropServices;
//using Phoenix.ConfigData;
//using Phoenix.Core;
//using Phoenix.Core.ClassCollect;
//using Sirenix.OdinInspector;
//using UnityEditor;
//using UnityEngine;
//using System.Text;
//using Sirenix.Utilities;
//using Phoenix.Drama;
//using System.IO;
//using Phoenix.GameLogic;
//using Phoenix.GameLogic.Battle;
//using Newtonsoft.Json;

//namespace Phoenix.Battle
//{
//	public static class BattleStaticDataGenerateTool
//    {
//        [MenuItem("Tools/Battle/Generate BattleProtoCode By Editor", false, 2)]
//        public static void GenerateBattleEditorDataProto()
//        {
//            string ilOutFolderPath = "Assets/Scripts/Editor/BattleStaticDataGenerator";
//            string csOutPath = "Assets/Assemblies/BattleExecuter/Scripts/BattleStaticData";
//            string ilOutPath = ilOutFolderPath + "/BattleStaticData.proto";
//            string utilityCodePath_StaticData = "Assets/Scripts/GameLogic/Battle/StaticData/AutoGen/BattleStaticDataUtility_AutoGen.cs";
//            string utilityCodePath_Info = "Assets/Assemblies/BattleExecuter/Scripts/BattleDataConvert/AutoGen/BattleDataConvertUtility_AutoGen.cs";
//            string infoCodeFolderPath = "Assets/Assemblies/Battle/Scripts/Info/AutoGen";

//            List<ClassExportInfo> classInfoList = CollectEditorDataClassExportInfoList();

//            GenerateProtoFile(ilOutPath, classInfoList);
//            ProtoUtility.RunProtoc(ilOutPath, ilOutFolderPath, csOutPath);
//            GenerateUtilityCode(utilityCodePath_StaticData, "BattleStaticDataUtility", classInfoList);

//            GenerateInfo(infoCodeFolderPath, classInfoList);
//            GenerateUtilityCodeOfInfo(utilityCodePath_Info, "BattleDataConvertUtility", classInfoList);

//            //GenerateClassJson(classInfoList);
//        }

//        private static void GenerateClassJson(List<ClassExportInfo> classInfoList)
//        {
//            List<Type> enumTypeList = new List<Type>();
//            var list = new List<ClassDesInfo>();
//            foreach (var classInfo in classInfoList)
//            {
//                if (IsEnum(classInfo.type))
//                {
//                    enumTypeList.AddNotContains(classInfo.type);
//                }
//                if (!classInfo.needExportStaticData)
//                {
//                    continue;
//                }
//                ClassDesInfo classDesInfo = new ClassDesInfo();
//                classDesInfo.name = classInfo.className_EditorData.Replace("EditorData", "");
//                if (classInfo.baseClassInfo != null)
//                {
//                    classDesInfo.baseClassName = classInfo.baseClassInfo.className_EditorData.Replace("EditorData", "");
//                }
//                if (classInfo.type.BaseType.Name != ("Object") && classInfo.type.BaseType.Name != ("ScriptableObject"))
//                {
//                    classDesInfo.baseClassName = classInfo.type.BaseType.Name.Replace("EditorData", "");
//                }
//                if (classInfo.superClassEnumValue != null)
//                {
//                    classDesInfo.inheritEnumType = classInfo.superClassEnumValue.GetType().Name;
//                    classDesInfo.inheritEnumValue = classInfo.superClassEnumValue.ToString();
//                    if (classInfo.superEnumMember != null)
//                    {
//                        classDesInfo.inheritEnumMember = classInfo.superEnumMember.memberName_EditorData;
//                    }
//                }
//                foreach (var memberInfo in classInfo.memberList)
//                {
//                    if(memberInfo.type != null && memberInfo.type.IsEnum)
//                    {
//                        enumTypeList.AddNotContains(memberInfo.type);
//                    }
//                    if (memberInfo.usingSuperClassEnum || memberInfo.isSuperClassEnum)
//                    {
//                        continue;
//                    }
//                    var attrs = memberInfo.fieldInfo.GetAttributes();
//                    var memberDesInfo = new ClassMemberDesInfo();
//                    memberDesInfo.tag = "";
//                    memberDesInfo.name = memberInfo.memberName_EditorData.Replace("EditorData", "").Replace("Data", "");
//                    memberDesInfo.type = memberInfo.memberTypeName_EditorData.Replace("EditorData", "");
//                    memberDesInfo.isList = IsList(memberInfo.type);
//                    memberDesInfo.isEnum = IsEnum(memberInfo.type) || IsEnumString(memberInfo.type);
//                    foreach (var attr in attrs)
//                    {
//                        if (attr is EditorDataFixedValueAttribute)
//                        {
//                            memberDesInfo.isRate = true;
//                        }
//                        else if(attr is TitleGroupAttribute)
//                        {
//                            memberDesInfo.tag = (attr as TitleGroupAttribute).GroupName;
//                        }
//                        else if (attr is LabelTextAttribute)
//                        {
//                            memberDesInfo.tag = (attr as LabelTextAttribute).Text;
//                        }
//                    }
//                    classDesInfo.memberList.Add(memberDesInfo);
//                }
//                list.Add(classDesInfo);
//            }
//            FileWriter classWriter = new FileWriter();
//            FileWriter memberWriter = new FileWriter();
//            foreach (var classInfo in list)
//            {
//                classWriter.AppendLine("{0},{1},{2},{3},{4}", classInfo.name, classInfo.baseClassName, classInfo.inheritEnumType, classInfo.inheritEnumValue, classInfo.inheritEnumMember);
//                foreach (var memberInfo in classInfo.memberList)
//                {
//                    memberWriter.AppendLine("{0},{1},{2},{3},{4},{5},{6}", classInfo.name, memberInfo.name, memberInfo.type, memberInfo.tag, memberInfo.isList, memberInfo.isEnum, memberInfo.isRate);
//                }
//            }
//            classWriter.WriteFile(@"E:\Class.csv");
//            memberWriter.WriteFile(@"E:\ClassMember.csv");

//            foreach (var enumType in enumTypeList)
//            {
//                FileWriter enumWriter = new FileWriter();
//                var enumTypeDescAttr = enumType.GetCustomAttribute<EnumForDescAttribute>();
//                if (enumTypeDescAttr == null)
//                {
//                    foreach (var fieldInfo in enumType.GetFields())
//                    {
//                        enumWriter.AppendLine(fieldInfo.Name);
//                    }
//                }
//                else
//                {
//                    foreach (var fieldInfo in enumType.GetFields())
//                    {
//                        var customAttributes = fieldInfo.GetCustomAttributes(false);
//                        if (customAttributes == null || customAttributes.Length == 0)
//                        {
//                            continue;
//                        }
//                        foreach (var customAttribute in customAttributes)
//                        {
//                            var descAttribute = customAttribute as EnumValueDescAttribute;
//                            if (descAttribute != null)
//                            {
//                                if (string.IsNullOrEmpty(descAttribute.name))
//                                {
//                                    enumWriter.AppendLine(fieldInfo.Name);
//                                }
//                                else
//                                {
//                                    enumWriter.AppendLine("{0},{1}", fieldInfo.Name, descAttribute.name.Replace(',', '，'));
//                                }
//                                break;
//                            }
//                        }
//                    }
//                }
//                enumWriter.WriteFile(string.Format(@"E:\EnumDef\{0}.csv", enumType.Name));
//            }
//            //string jsonStr = JsonConvert.SerializeObject(list, Formatting.Indented);
//            //using (StreamWriter sw = new StreamWriter(@"E:\123.txt", false, Encoding.UTF8))
//            //{
//            //    sw.Write(jsonStr);
//            //}
//        }

//        private static List<ClassExportInfo> CollectEditorDataClassExportInfoList()
//        {
//            List<Type> collectClassExceptTypeList = new List<Type>()
//            {
//                typeof(ScriptableObject),
//            };
//            List<ClassInfo> classInfoList = new List<ClassInfo>();
//            ClassCollectUtility.CollectClass(typeof(BattleEditorData), classInfoList, collectClassExceptTypeList, false);
//            ClassCollectUtility.CollectClass(typeof(SkillLogicEditorData), classInfoList, collectClassExceptTypeList, false);
//            ClassCollectUtility.CollectClass(typeof(BuffEditorData), classInfoList, collectClassExceptTypeList, false);
//            ClassCollectUtility.CollectClass(typeof(TerrainLogicEditorData), classInfoList, collectClassExceptTypeList, false);
//            ClassCollectUtility.CollectClass(typeof(BattleStageActionGroupEditorData), classInfoList, collectClassExceptTypeList, false);

//            List<ClassExportInfo> classExportInfoList = GetClassExportInfoList(classInfoList);
//            AnalyzeClassExportInfoList(classExportInfoList);
//            return classExportInfoList;
//        }

//        [MenuItem("Tools/Battle/Generate BattleProtoData By Editor", false, 2)]
//        private static void GenerateProtoData()
//        {
//            ConfigDataInitializer.InitImmediately_Editor();
//            string folderName = "Assets/Res/Battle/Proto/";
//            if (!Directory.Exists(folderName))
//            {
//                Directory.CreateDirectory(folderName);
//            }
//            GenerateBattleGroundData(folderName, "BattleGround");
//            GenerateBattleProtoData<BattleConfigData, BattleEditorData, BattleStaticData>(ConfigDataManager.instance.battleMap, d => d.EditorDataPath, BattleStaticDataUtility.GetBattleStaticData, folderName, "Battle");
//            GenerateBattleProtoData<BuffConfigData, BuffEditorData, BuffStaticData>(ConfigDataManager.instance.buffMap, d => d.EditorDataPath, BattleStaticDataUtility.GetBuffStaticData, folderName, "Buff");
//            GenerateBattleProtoData<SkillConfigData, SkillLogicEditorData, SkillLogicStaticData>(ConfigDataManager.instance.skillMap, d => d.LogicPath, BattleStaticDataUtility.GetSkillLogicStaticData, folderName, "Skill");
//            GenerateBattleProtoData<TerrainBuffConfigData, TerrainLogicEditorData, TerrainLogicStaticData>(ConfigDataManager.instance.terrainBuffMap, d => d.TerrainEffectPath, BattleStaticDataUtility.GetTerrainLogicStaticData, folderName, "TerrainBuff");
//            GenerateBattleProtoData<BattleTerrainConfigData, TerrainLogicEditorData, TerrainLogicStaticData>(ConfigDataManager.instance.battleTerrainMap, d => d.LogicPath, BattleStaticDataUtility.GetTerrainLogicStaticData, folderName, "Terrain");

//            //BattleInfoGetterProto getter = new BattleInfoGetterProto();
//            //getter.ReadAllFormFolder(folderName, OnReadBuffer);
//        }

//        //private static byte[] OnReadBuffer(string path)
//        //{
//        //    using (FileStream fs = File.Open(path, FileMode.Open))
//        //    {
//        //        byte[] buffer = new byte[fs.Length];
//        //        fs.Read(buffer, 0, buffer.Length);
//        //        return buffer;
//        //    }
//        //}

//        private static void GenerateBattleGroundData(string folderPath, string name)
//        {
//            Dictionary<string, BattleGroundStaticData> serMap = new Dictionary<string, BattleGroundStaticData>();
//            foreach (var kv in ConfigDataManager.instance.battleMap)
//            {
//                var value = kv.Value;
//                var editorDataPath = value.EditorDataPath;
//                var editorData = AssetDatabase.LoadAssetAtPath<BattleEditorData>(editorDataPath);
//                if (editorData == null)
//                {
//                    continue;
//                }
//                foreach (var stageData in editorData.stageList)
//                {
//                    var groundDataPath = stageData.sceneData.path;
//                    if (serMap.ContainsKey(groundDataPath))
//                    {
//                        continue;
//                    }
//                    var stageText = AssetDatabase.LoadAssetAtPath<TextAsset>(groundDataPath);
//                    if (stageText == null)
//                    {
//                        continue;
//                    }
//                    var groundStaticData = BattleGroundStaticData.Parser.ParseFrom(stageText.bytes);
//                    if (groundStaticData == null)
//                    {
//                        continue;
//                    }
//                    serMap.Add(groundDataPath, groundStaticData);
//                }
//            }
//            string outputPath = folderPath + name;
//            WriteMap(serMap, OnWriteStringKey, outputPath);
//        }

//        private static void OnWriteStringKey(string key, Google.Protobuf.CodedOutputStream stream)
//        {
//            stream.WriteString(key);
//        }

//        private static void OnWriteIntKey(int key, Google.Protobuf.CodedOutputStream stream)
//        {
//            stream.WriteInt32(key);
//        }

//        private static void WriteMap<TKey, TValue>(Dictionary<TKey, TValue> map, Action<TKey, Google.Protobuf.CodedOutputStream> actionOnWriteKey, string outputPath)
//            where TValue : Google.Protobuf.IMessage
//        {
//            using (FileStream fileStream = new FileStream(outputPath, FileMode.Create))
//            {
//                using (MemoryStream memoryStream = new MemoryStream())
//                {
//                    using (var outputStream = new Google.Protobuf.CodedOutputStream(memoryStream))
//                    {
//                        outputStream.WriteInt32(map.Count);
//                        foreach (var kv in map)
//                        {
//                            var key = kv.Key;
//                            var value = kv.Value;

//                            actionOnWriteKey(key, outputStream);
//                            using (MemoryStream memoryStream2 = new MemoryStream())
//                            {
//                                using (var outputStream2 = new Google.Protobuf.CodedOutputStream(memoryStream2))
//                                {
//                                    value.WriteTo(outputStream2);
//                                    outputStream2.Flush();
//                                    var bs = Google.Protobuf.ByteString.CopyFrom(memoryStream2.ToArray());
//                                    outputStream.WriteBytes(bs);
//                                }
//                            }
//                        }
//                        outputStream.Flush();
//                        fileStream.Write(memoryStream.ToArray(), 0, (int)memoryStream.Position);
//                    }
//                }
//            }
//        }

//        private static void GenerateBattleProtoData<TConfigData, TEditorData, TProtoData>(Dictionary<int, TConfigData> map, Func<TConfigData, string> funcOnGetEditorDataPath, Func<TEditorData, TProtoData> funcOnParseProtoData, string folderPath, string name)
//            where TConfigData : Google.Protobuf.IMessage
//            where TEditorData : ScriptableObject
//            where TProtoData : Google.Protobuf.IMessage
//        {
//            var serMap = new Dictionary<int, TProtoData>(map.Count);
//            foreach (var kv in map)
//            {
//                var key = kv.Key;
//                var value = kv.Value;
//                var editorDataPath = funcOnGetEditorDataPath(value);
//                var editorData = AssetDatabase.LoadAssetAtPath<TEditorData>(editorDataPath);
//                if (editorData == null)
//                {
//                    continue;
//                }
//                var protoData = funcOnParseProtoData(editorData);
//                if (protoData == null)
//                {
//                    continue;
//                }
//                serMap.Add(key, protoData);
//            }
//            string outputPath = folderPath + name;
//            WriteMap(serMap, OnWriteIntKey, outputPath);
//        }

//        private static List<ClassExportInfo> CollectDramaClassExportInfoList()
//        {
//            List<Type> collectClassExceptTypeList = new List<Type>()
//            {
//                typeof(ScriptableObject),
//            };
//            List<ClassInfo> classInfoList = new List<ClassInfo>();
//            ClassCollectUtility.CollectClass(typeof(DramaTimelineData), classInfoList, collectClassExceptTypeList, false);

//            List<ClassExportInfo> classExportInfoList = GetClassExportInfoList(classInfoList);
//            AnalyzeClassExportInfoList(classExportInfoList);
//            return classExportInfoList;
//        }

//        private static List<ClassExportInfo> GetClassExportInfoList(List<ClassInfo> classInfoList)
//        {
//            List<ClassExportInfo> classExportInfoList = new List<ClassExportInfo>();
//            foreach (var classInfo in classInfoList)
//            {
//                if (classInfo.type.IsPrimitive)
//                {
//                    continue;
//                }
//                ClassExportInfo classExportInfo = new ClassExportInfo();
//                classExportInfo.type = classInfo.type;

//                classExportInfo.className_EditorData = GetClassName_EditorData(classInfo.type);
//                classExportInfo.className_StaticData = GetClassName_StaticData(classInfo.type);
//                classExportInfo.className_Info = GetClassName_Info(classInfo.type);

//                classExportInfo.instanceName_EditorData = GetClassInstanceName_EditorData(classInfo.type);
//                classExportInfo.instanceName_StaticData = GetClassInstanceName_StaticData(classInfo.type);
//                classExportInfo.instanceName_Info = GetClassInstanceName_Info(classInfo.type);

//                classExportInfo.needExportStaticData = CheckClassTypeNeedExportStaticData(classInfo.type);
//                classExportInfo.needExportInfo = CheckClassTypeNeedExportInfo(classInfo.type);

//                classExportInfoList.Add(classExportInfo);

//                foreach (var memberInfo in classInfo.memberList)
//                {
//                    ClassMemberExportInfo memberExportInfo = new ClassMemberExportInfo();
//                    memberExportInfo.type = memberInfo.type;
//                    memberExportInfo.fieldInfo = memberInfo.fieldInfo;
//                    memberExportInfo.ownerClassInfo = classExportInfo;

//                    memberExportInfo.memberName_EditorData = GetMemberName_EditorData(memberInfo);
//                    memberExportInfo.memberName_StaticData = GetMemberName_StaticData(memberInfo);
//                    memberExportInfo.memberName_Info = GetMemberName_Info(memberInfo);

//                    memberExportInfo.memberTypeName_EditorData = GetMemberTypeName_EditorData(memberInfo.type);
//                    memberExportInfo.memberTypeName_StaticData = GetMemberTypeName_StaticData(memberInfo.type);
//                    memberExportInfo.memberTypeName_Info = GetMemberTypeName_Info(memberInfo.type);

//                    classExportInfo.memberList.Add(memberExportInfo);
//                }
//            }
//            return classExportInfoList;
//        }

//        private static void AnalyzeClassExportInfoList(List<ClassExportInfo> classInfoList)
//        {
//            foreach (var classInfo in classInfoList)
//            {
//                var partialAttr = classInfo.type.GetCustomAttribute<EditorDataPartialAttribute>();
//                if(partialAttr != null)
//                {
//                    classInfo.isPartial = true;
//                }
//                var snapshotAttr = classInfo.type.GetCustomAttribute<EditorDataBattleSnapshotAttribute>();
//                if (snapshotAttr != null)
//                {
//                    classInfo.isSnapshot = true;
//                }
//                foreach (var memberInfo in classInfo.memberList)
//                {
//                    if (IsList(memberInfo.type))
//                    {
//                        Type innerType = memberInfo.type.GetGenericArguments()[0];
//                        memberInfo.typeClassInfo = classInfoList.Find(c => c.type == innerType);
//                    }
//                    else
//                    {
//                        memberInfo.typeClassInfo = classInfoList.Find(c => c.type == memberInfo.type);
//                    }
//                    var dynamicIdAttr = memberInfo.fieldInfo.GetCustomAttribute<EditorDataDynamicIdAttribute>();
//                    if (dynamicIdAttr != null)
//                    {
//                        memberInfo.isDynamicId = true;
//                    }
//                    var inheritAttr = memberInfo.fieldInfo.GetCustomAttribute<EditorDataInheritAttribute>();
//                    if (inheritAttr != null)
//                    {
//                        classInfo.isBaseClass = true;
//                        ClassMemberExportInfo superEnumMember = classInfo.memberList.Find(m => m.isSuperClassEnum);
//                        classInfo.superEnumMember = superEnumMember;
//                        memberInfo.usingSuperClassEnum = true;
//                        ClassExportInfo memberTypeClassInfo = memberInfo.typeClassInfo;
//                        if (memberTypeClassInfo != null)
//                        {
//                            memberTypeClassInfo.baseClassInfo = classInfo;
//                            memberTypeClassInfo.superClassEnumValue = inheritAttr.enumValue;
//                        }
//                    }
//                    var inheritEnumAttr = memberInfo.fieldInfo.GetCustomAttribute<EditorDataInheritEnumAttribute>();
//                    if (inheritEnumAttr != null)
//                    {
//                        classInfo.isBaseClass = true;
//                        memberInfo.isSuperClassEnum = true;
//                    }
//                    var fixedValueAttr = memberInfo.fieldInfo.GetCustomAttribute<EditorDataFixedValueAttribute>();
//                    if (fixedValueAttr != null)
//                    {
//                        memberInfo.isFix = true;
//                        memberInfo.fixPrecision = fixedValueAttr.precision;
//                    }
//                }
//            }
//        }

//        private static void GenerateProtoFile(string path, List<ClassExportInfo> classList)
//        {
//            FileWriter writer = new FileWriter();
//            writer.AppendLine("syntax = \"proto3\";");
//            writer.AppendLine("package Phoenix.Battle;");
//            writer.AppendLine();
//            foreach (var classInfo in classList)
//            {
//                if (!classInfo.needExportStaticData)
//                {
//                    continue;
//                }
//                writer.AppendLine("message {0}", classInfo.className_StaticData);
//                writer.StartBlock();
//                {
//                    for (int i = 0; i < classInfo.memberList.Count; ++i)
//                    {
//                        ClassMemberExportInfo memberInfo = classInfo.memberList[i];
//                        writer.AppendLine("{0} {1} = {2};", memberInfo.memberTypeName_StaticData, memberInfo.memberName_StaticData, i + 1);
//                    }
//                }
//                writer.EndBlock();
//                writer.AppendLine();
//            }
//            writer.WriteFile(path);
//        }

//        private static void GenerateUtilityCode(string path, string utilityClassName, List<ClassExportInfo> classList)
//        {
//            FileWriter writer = new FileWriter();
//            writer.AppendLine("using System;");
//            writer.AppendLine("using System.Collections;");
//            writer.AppendLine("using System.Collections.Generic;");
//            writer.AppendLine("using Phoenix.Battle;");
//            writer.AppendLine("using Phoenix.ConfigData;");
//            writer.AppendLine();
//            writer.AppendLine("namespace Phoenix.GameLogic.Battle");
//            writer.StartBlock();
//            {
//                writer.AppendLine("public static partial class {0}", utilityClassName);
//                writer.StartBlock();
//                {
//                    foreach (var classInfo in classList)
//                    {
//                        if (!classInfo.needExportStaticData)
//                        {
//                            continue;
//                        }
//                        writer.AppendLine("public static {0} Get{0}({1} {2})", classInfo.className_StaticData, classInfo.className_EditorData, classInfo.instanceName_EditorData);
//                        writer.StartBlock();
//                        {
//                            if (classInfo.type.IsClass)
//                            {
//                                writer.AppendLine("if ({0} == null)", classInfo.instanceName_EditorData);
//                                writer.StartBlock();
//                                {
//                                    writer.AppendLine("return null;");
//                                }
//                                writer.EndBlock();
//                            }
//                            writer.AppendLine("{0} {1} = new {0}();", classInfo.className_StaticData, classInfo.instanceName_StaticData);
//                            if (classInfo.isBaseClass)
//                            {
//                                writer.AppendLine("{0}.{1} = (int){2}.{3}.value;", classInfo.instanceName_StaticData, classInfo.superEnumMember.memberName_StaticData, classInfo.instanceName_EditorData, classInfo.superEnumMember.memberName_EditorData);
//                                writer.AppendLine("switch (({0}){1}.{2})", classInfo.superEnumMember.memberTypeName_Info, classInfo.instanceName_EditorData, classInfo.superEnumMember.memberName_EditorData);
//                                writer.StartBlock();
//                                {
//                                    foreach (var memberInfo in classInfo.memberList)
//                                    {
//                                        if (!memberInfo.usingSuperClassEnum)
//                                        {
//                                            continue;
//                                        }
//                                        writer.AppendLine("case {0}.{1}:", memberInfo.typeClassInfo.superClassEnumValue.GetType().Name, memberInfo.typeClassInfo.superClassEnumValue.ToString());
//                                        writer.StartTab();
//                                        {
//                                            WriteClassMemberSet_StaticData(writer, classInfo, memberInfo, classList);
//                                            writer.AppendLine("break;");
//                                        }
//                                        writer.EndTab();
//                                    }
//                                }
//                                writer.EndBlock();
//                                bool hasOtherMembers = false;
//                                foreach (var memberInfo in classInfo.memberList)
//                                {
//                                    if (memberInfo.usingSuperClassEnum)
//                                    {
//                                        continue;
//                                    }
//                                    if (memberInfo.isSuperClassEnum)
//                                    {
//                                        continue;
//                                    }
//                                    hasOtherMembers = true;
//                                    break;
//                                }
//                                if (hasOtherMembers)
//                                {
//                                    writer.StartBlock();
//                                    {
//                                        foreach (var memberInfo in classInfo.memberList)
//                                        {
//                                            if (memberInfo.usingSuperClassEnum)
//                                            {
//                                                continue;
//                                            }
//                                            if (memberInfo.isSuperClassEnum)
//                                            {
//                                                continue;
//                                            }
//                                            WriteClassMemberSet_StaticData(writer, classInfo, memberInfo, classList);
//                                        }
//                                    }
//                                    writer.EndBlock();
//                                }
//                            }
//                            else
//                            {
//                                foreach (var memberInfo in classInfo.memberList)
//                                {
//                                    WriteClassMemberSet_StaticData(writer, classInfo, memberInfo, classList);
//                                }
//                            }
//                            writer.AppendLine("return {0};", classInfo.instanceName_StaticData);
//                        }
//                        writer.EndBlock();
//                        writer.AppendLine();
//                    }
//                }
//                writer.EndBlock();
//            }
//            writer.EndBlock();
//            writer.WriteFile(path);
//        }


//        private static void WriteClassMemberSet_StaticData(FileWriter writer, ClassExportInfo classInfo, ClassMemberExportInfo memberInfo, List<ClassExportInfo> classList)
//        {
//            Type memberType = memberInfo.type;
//            ClassExportInfo memberTypeClassInfo = memberInfo.typeClassInfo;
//            if (IsList(memberType))
//            {
//                Type innerType = memberType.GetGenericArguments()[0];
//                string innerItemName = GetNameExceptList(memberInfo.memberName_EditorData);
//                writer.AppendLine("foreach(var {0} in {1}.{2})", innerItemName, classInfo.instanceName_EditorData, memberInfo.memberName_EditorData);
//                writer.StartBlock();
//                {
//                    ClassExportInfo innerTypeClassInfo = classList.Find(c => c.type == innerType && c.needExportStaticData);
//                    if (innerTypeClassInfo != null)
//                    {
//                        writer.AppendLine("{0}.{1}.Add(Get{2}({3}));", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, innerTypeClassInfo.className_StaticData, innerItemName);
//                    }
//                    else if (IsEnumString(innerType))
//                    {
//                        writer.AppendLine("{0}.{1}.Add((int){2}.value);", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, innerItemName);
//                    }
//                    else
//                    {
//                        writer.AppendLine("{0}.{1}.Add({2});", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, innerItemName);
//                    }
//                }
//                writer.EndBlock();
//            }
//            else if (memberTypeClassInfo != null && memberTypeClassInfo.needExportStaticData)
//            {
//                writer.AppendLine("{0}.{1} = Get{4}({2}.{3});", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, classInfo.instanceName_EditorData, memberInfo.memberName_EditorData, memberTypeClassInfo.className_StaticData);
//            }
//            else if (IsEnumString(memberType))
//            {
//                writer.AppendLine("{0}.{1} = (int){2}.{3}.value;", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, classInfo.instanceName_EditorData, memberInfo.memberName_EditorData);
//            }
//            else if (IsAssetWeakRef(memberType))
//            {
//                writer.AppendLine("{0}.{1} = {2}.{3}.path;", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, classInfo.instanceName_EditorData, memberInfo.memberName_EditorData);
//            }
//            else if (IsEnum(memberType))
//            {
//                writer.AppendLine("{0}.{1} = (int){2}.{3};", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, classInfo.instanceName_EditorData, memberInfo.memberName_EditorData);
//            }
//            else
//            {
//                writer.AppendLine("{0}.{1} = {2}.{3};", classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, classInfo.instanceName_EditorData, memberInfo.memberName_EditorData);
//            }
//        }

//        private static void GenerateInfo(string path, List<ClassExportInfo> classList)
//        {
//            ClearAutoGenFolder(path);
//            foreach (var classInfo in classList)
//            {
//                if (!classInfo.needExportInfo)
//                {
//                    continue;
//                }
//                FileWriter writer = new FileWriter();
//                writer.AppendLine("using System;");
//                writer.AppendLine("using System.Collections;");
//                writer.AppendLine("using System.Collections.Generic;");
//                writer.AppendLine("using Phoenix.ConfigData;");
//                writer.AppendLine("using Phoenix.Core;");
//                writer.AppendLine();
//                writer.AppendLine("namespace Phoenix.Battle");
//                writer.StartBlock();
//                {
//                    StringBuilder baseClassDefSb = new StringBuilder();
//                    if (classInfo.baseClassInfo != null)
//                    {
//                        baseClassDefSb.Append(" : ");
//                        baseClassDefSb.Append(classInfo.baseClassInfo.className_Info);
//                    }
//                    bool isClassSnapshot = CheckClassIsSnapshot(classInfo);
//                    if (isClassSnapshot && classInfo.isSnapshot)
//                    {
//                        if (baseClassDefSb.Length == 0)
//                        {
//                            baseClassDefSb.Append(" : ");
//                        }
//                        else
//                        {
//                            baseClassDefSb.Append(", ");
//                        }
//                        baseClassDefSb.Append("IBattleSnapshot");
//                    }
//                    writer.AppendLine("public {0}{1}class {2}{3}",
//                        classInfo.isBaseClass ? "abstract " : "",
//                        classInfo.isPartial ? "partial " : "",
//                        classInfo.className_Info,
//                        baseClassDefSb.ToString()
//                        );
//                    writer.StartBlock();
//                    {
//                        bool hasWritten = false;
//                        foreach (var memberInfo in classInfo.memberList)
//                        {
//                            if (memberInfo.usingSuperClassEnum || memberInfo.isSuperClassEnum)
//                            {
//                                continue;
//                            }
//                            if (memberInfo.isFix)
//                            {
//                                writer.AppendLine("public FixedValue {0};", memberInfo.memberName_Info);
//                            }
//                            else if (memberInfo.type.IsGenericType && memberInfo.type.GetGenericTypeDefinition() == typeof(List<>))
//                            {
//                                writer.AppendLine("public {0} {1} = new {0}();", memberInfo.memberTypeName_Info, memberInfo.memberName_Info);
//                            }
//                            else
//                            {
//                                writer.AppendLine("public {0} {1};", memberInfo.memberTypeName_Info, memberInfo.memberName_Info);
//                            }
//                            hasWritten = true;
//                        }
//                        foreach (var memberInfo in classInfo.memberList)
//                        {
//                            if (memberInfo.isSuperClassEnum)
//                            {
//                                if (hasWritten)
//                                {
//                                    writer.AppendLine();
//                                }
//                                writer.AppendLine("public abstract {0} {1} {{ get; }}", memberInfo.memberTypeName_Info, memberInfo.memberName_Info);
//                            }
//                        }
//                        if (classInfo.baseClassInfo != null)
//                        {
//                            if (hasWritten)
//                            {
//                                writer.AppendLine();
//                            }
//                            writer.AppendLine("public override {0} {1}", classInfo.superClassEnumValue.GetType().Name, classInfo.baseClassInfo.superEnumMember.memberName_Info);
//                            writer.StartBlock();
//                            {
//                                writer.AppendLine("get {{ return {0}.{1}; }}", classInfo.superClassEnumValue.GetType().Name, classInfo.superClassEnumValue.ToString());
//                            }
//                            writer.EndBlock();
//                            hasWritten = true;
//                        }
//                        if (CheckClassIsSnapshot(classInfo))
//                        {
//                            if (hasWritten)
//                            {
//                                writer.AppendLine();
//                            }
//                            List<ClassExportInfo> snapshotMemberTypeClassInfoList = new List<ClassExportInfo>();
//                            writer.AppendLine("public {0} void BuildSnapshot(ByteBufferBuilder builder)", classInfo.isSnapshot ? "virtual" : "override");
//                            writer.StartBlock();
//                            {
//                                if (!classInfo.isSnapshot)
//                                {
//                                    writer.AppendLine("base.BuildSnapshot(builder);");
//                                }
//                                foreach (var memberInfo in classInfo.memberList)
//                                {
//                                    if (memberInfo.usingSuperClassEnum || memberInfo.isSuperClassEnum)
//                                    {
//                                        continue;
//                                    }
//                                    WriteBuildSnapshot(writer, memberInfo, memberInfo.type, memberInfo.memberName_Info, snapshotMemberTypeClassInfoList);
//                                }
//                            }
//                            writer.EndBlock();
//                            writer.AppendLine();
//                            writer.AppendLine("public {0} void LoadSnapshot(ByteBufferLoader loader)", classInfo.isSnapshot ? "virtual" : "override");
//                            writer.StartBlock();
//                            {
//                                if (!classInfo.isSnapshot)
//                                {
//                                    writer.AppendLine("base.LoadSnapshot(loader);");
//                                }
//                                foreach (var memberInfo in classInfo.memberList)
//                                {
//                                    if (memberInfo.usingSuperClassEnum || memberInfo.isSuperClassEnum)
//                                    {
//                                        continue;
//                                    }
//                                    WriteLoadSnapshot(writer, memberInfo, memberInfo.type, memberInfo.memberName_Info);
//                                }
//                            }
//                            writer.EndBlock();

//                            if (snapshotMemberTypeClassInfoList.Count > 0)
//                            {
//                                writer.AppendLine();
//                                foreach (var info in snapshotMemberTypeClassInfoList)
//                                {
//                                    writer.AppendLine("private partial void BuildSnapshot_{0}(ByteBufferBuilder builder, {0} {1});", info.className_Info, CodeNameUtility.GetFirstLowerName(info.type.Name));
//                                    writer.AppendLine("private partial {0} LoadSnapshot_{0}(ByteBufferLoader loader);", info.className_Info);
//                                }
//                            }
//                        }
//                    }
//                    writer.EndBlock();
//                }
//                writer.EndBlock();
//                writer.WriteFile(FilePathUtility.GetPath(path, string.Format("{0}.cs", classInfo.className_Info)));
//            }
//        }

//        private static void ClearAutoGenFolder(string folderPath)
//        {
//            DirectoryInfo dirInfo = new DirectoryInfo(folderPath);
//            if (!dirInfo.Exists)
//            {
//                return;
//            }
//            List<DirectoryInfo> dirList = new List<DirectoryInfo>() { dirInfo };
//            while (dirList.Count > 0)
//            {
//                dirInfo = dirList[0];
//                dirList.RemoveAt(0);
//                List<string> folderMetaFileList = new List<string>();
//                foreach (DirectoryInfo subDirInfo in dirInfo.GetDirectories())
//                {
//                    string metaName = subDirInfo.FullName + ".meta";
//                    folderMetaFileList.Add(metaName);
//                    dirList.Add(subDirInfo);
//                }
//                List<string> deleteFileList = new List<string>();
//                foreach (FileInfo fileInfo in dirInfo.GetFiles())
//                {
//                    if (folderMetaFileList.Find(s => s == fileInfo.FullName) == null)
//                    {
//                        deleteFileList.Add(fileInfo.FullName);
//                    }
//                }
//                foreach (string filePath in deleteFileList)
//                {
//                    try
//                    {
//                        File.Delete(filePath);
//                    }
//                    catch (Exception e)
//                    {
//                        UnityEngine.Debug.LogError(e.Message);
//                    }
//                }
//            }
//        }

//        private static void GenerateUtilityCodeOfInfo(string path, string utilityClassName, List<ClassExportInfo> classList)
//        {
//            FileWriter writer = new FileWriter();
//            writer.AppendLine("using System;");
//            writer.AppendLine("using System.Collections;");
//            writer.AppendLine("using System.Collections.Generic;");
//            writer.AppendLine("using Phoenix.Battle;");
//            writer.AppendLine("using Phoenix.ConfigData;");
//            writer.AppendLine();
//            writer.AppendLine("namespace Phoenix.Battle");
//            writer.StartBlock();
//            {
//                writer.AppendLine("public static partial class {0}", utilityClassName);
//                writer.StartBlock();
//                {
//                    foreach (var classInfo in classList)
//                    {
//                        if (!classInfo.needExportInfo)
//                        {
//                            continue;
//                        }
//                        writer.AppendLine("public static {0} Get{0}({1} {2})", classInfo.className_Info, classInfo.className_StaticData, classInfo.instanceName_StaticData);
//                        writer.StartBlock();
//                        {
//                            writer.AppendLine("if ({0} == null)", classInfo.instanceName_StaticData);
//                            writer.StartBlock();
//                            {
//                                writer.AppendLine("return null;");
//                            }
//                            writer.EndBlock();
//                            if (classInfo.isBaseClass)
//                            {
//                                writer.AppendLine("{0} {1} = null;", classInfo.className_Info, classInfo.instanceName_Info);
//                                writer.AppendLine("switch (({0}){1}.{2})", classInfo.superEnumMember.memberTypeName_Info, classInfo.instanceName_StaticData, classInfo.superEnumMember.memberName_StaticData);
//                                writer.StartBlock();
//                                {
//                                    foreach (var memberInfo in classInfo.memberList)
//                                    {
//                                        if (!memberInfo.usingSuperClassEnum)
//                                        {
//                                            continue;
//                                        }
//                                        writer.AppendLine("case {0}.{1}:", memberInfo.typeClassInfo.superClassEnumValue.GetType().Name, memberInfo.typeClassInfo.superClassEnumValue.ToString());
//                                        writer.StartTab();
//                                        {
//                                            writer.AppendLine("{3} = Get{0}({1}.{2});", memberInfo.typeClassInfo.className_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, classInfo.instanceName_Info);
//                                            writer.AppendLine("break;");
//                                        }
//                                        writer.EndTab();
//                                    }
//                                }
//                                writer.EndBlock();
//                                bool hasOtherMembers = false;
//                                foreach (var memberInfo in classInfo.memberList)
//                                {
//                                    if (memberInfo.usingSuperClassEnum)
//                                    {
//                                        continue;
//                                    }
//                                    if (memberInfo.isSuperClassEnum)
//                                    {
//                                        continue;
//                                    }
//                                    hasOtherMembers = true;
//                                    break;
//                                }
//                                if (hasOtherMembers)
//                                {
//                                    writer.AppendLine("if ({0} != null)", classInfo.instanceName_Info);
//                                    writer.StartBlock();
//                                    {
//                                        foreach (var memberInfo in classInfo.memberList)
//                                        {
//                                            if (memberInfo.usingSuperClassEnum)
//                                            {
//                                                continue;
//                                            }
//                                            if (memberInfo.isSuperClassEnum)
//                                            {
//                                                continue;
//                                            }
//                                            WriteClassMemberSet_Info(writer, classInfo, memberInfo, classList);
//                                        }
//                                    }
//                                    writer.EndBlock();
//                                }
//                                writer.AppendLine("return {0};", classInfo.instanceName_Info);
//                            }
//                            else
//                            {
//                                writer.AppendLine("{0} {1} = new {0}();", classInfo.className_Info, classInfo.instanceName_Info);

//                                foreach (var memberInfo in classInfo.memberList)
//                                {
//                                    WriteClassMemberSet_Info(writer, classInfo, memberInfo, classList);
//                                }
//                                writer.AppendLine("return {0};", classInfo.instanceName_Info);
//                            }
//                        }
//                        writer.EndBlock();
//                        writer.AppendLine();
//                    }
//                }
//                writer.EndBlock();
//            }
//            writer.EndBlock();
//            writer.WriteFile(path);
//        }

//        private static void WriteClassMemberSet_Info(FileWriter writer, ClassExportInfo classInfo, ClassMemberExportInfo memberInfo, List<ClassExportInfo> classList)
//        {
//            Type memberType = memberInfo.type;
//            ClassExportInfo memberTypeClassInfo = memberInfo.typeClassInfo;
//            if (IsList(memberType))
//            {
//                Type innerType = memberType.GetGenericArguments()[0];
//                string innerItemName = CodeNameUtility.GetFirstLowerName(GetNameExceptList(memberInfo.memberName_StaticData));
//                writer.AppendLine("foreach(var {0} in {1}.{2})", innerItemName, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData);
//                writer.StartBlock();
//                {
//                    ClassExportInfo innerTypeClassInfo = classList.Find(c => c.type == innerType && c.needExportStaticData);
//                    if (innerTypeClassInfo != null)
//                    {
//                        writer.AppendLine("{0}.{1}.Add(Get{2}({3}));", classInfo.instanceName_Info, memberInfo.memberName_Info, innerTypeClassInfo.className_Info, innerItemName);
//                    }
//                    else if (IsEnumString(innerType))
//                    {
//                        writer.AppendLine("{0}.{1}.Add(({3}){2});", classInfo.instanceName_Info, memberInfo.memberName_Info, innerItemName, innerType.GetGenericArguments()[0].Name);
//                    }
//                    else if (IsEnum(innerType))
//                    {
//                        writer.AppendLine("{0}.{1}.Add(({3}){2});", classInfo.instanceName_Info, memberInfo.memberName_Info, innerItemName, innerType.Name);
//                    }
//                    else if (memberInfo.isFix)
//                    {
//                        writer.AppendLine("{0}.{1}.Add(new FixedValue({2}, {3}));", classInfo.instanceName_Info, memberInfo.memberName_Info, innerItemName, memberInfo.fixPrecision);
//                    }
//                    else
//                    {
//                        writer.AppendLine("{0}.{1}.Add({2});", classInfo.instanceName_Info, memberInfo.memberName_Info, innerItemName);
//                    }
//                }
//                writer.EndBlock();
//            }
//            else if (IsEnumString(memberType))
//            {
//                writer.AppendLine("{0}.{1} = ({4}){2}.{3};", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, memberInfo.memberTypeName_Info);
//            }
//            else if (IsEnum(memberType))
//            {
//                writer.AppendLine("{0}.{1} = ({4}){2}.{3};", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, memberInfo.memberTypeName_Info);
//            }
//            else if (IsAssetWeakRef(memberType))
//            {
//                writer.AppendLine("{0}.{1} = {2}.{3};", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData);
//            }
//            else if (IsGridPosition(memberType))
//            {
//                writer.AppendLine("{0}.{1}.x = {2}.{3}.X;", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData);
//                writer.AppendLine("{0}.{1}.y = {2}.{3}.Y;", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData);
//            }
//            else if (memberInfo.isFix)
//            {
//                writer.AppendLine("{0}.{1} = new FixedValue({2}.{3}, {4});", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, memberInfo.fixPrecision);
//            }
//            else if (memberTypeClassInfo != null && memberTypeClassInfo.needExportInfo)
//            {
//                writer.AppendLine("{0}.{1} = Get{4}({2}.{3});", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData, memberTypeClassInfo.className_Info);
//            }
//            else
//            {
//                writer.AppendLine("{0}.{1} = {2}.{3};", classInfo.instanceName_Info, memberInfo.memberName_Info, classInfo.instanceName_StaticData, memberInfo.memberName_StaticData);
//            }
//        }

//        private static void WriteBuildSnapshot(FileWriter writer, ClassMemberExportInfo memberInfo, Type memberType, string memberName, List<ClassExportInfo> snapshotClassList)
//        {
//            if (memberType == typeof(int))
//            {
//                writer.AppendLine("builder.WriteInt({0});", memberName);
//            }
//            else if (memberType == typeof(uint))
//            {
//                writer.AppendLine("builder.WriteUint({0});", memberName);
//            }
//            else if (memberType == typeof(short))
//            {
//                writer.AppendLine("builder.WriteShort({0});", memberName);
//            }
//            else if (memberType == typeof(ushort))
//            {
//                writer.AppendLine("builder.WriteUshort({0});", memberName);
//            }
//            else if (memberType == typeof(byte))
//            {
//                writer.AppendLine("builder.WriteByte({0});", memberName);
//            }
//            else if (IsEnumString(memberType))
//            {
//                writer.AppendLine("builder.WriteShort((short){0});", memberName);
//            }
//            else if (IsEnum(memberType))
//            {
//                writer.AppendLine("builder.WriteShort((short){0});", memberName);
//            }
//            else if (IsGridPosition(memberType))
//            {
//                writer.AppendLine("builder.WriteByte((byte){0}.x);", memberName);
//                writer.AppendLine("builder.WriteByte((byte){0}.y);", memberName);
//            }
//            else if (memberInfo.isFix)
//            {
//                writer.AppendLine("builder.WriteInt(FixedValue.Export({0}));", memberName);
//            }
//            else if (IsList(memberType))
//            {
//                Type innerType = memberType.GetGenericArguments()[0];
//                string innerItemName = CodeNameUtility.GetFirstLowerName(GetNameExceptList(memberName));
//                writer.AppendLine("builder.WriteUshort((ushort){0}.Count);", memberName);
//                writer.AppendLine("foreach(var {0} in {1})", innerItemName, memberName);
//                writer.StartBlock();
//                {
//                    WriteBuildSnapshot(writer, memberInfo, innerType, innerItemName, snapshotClassList);
//                }
//                writer.EndBlock();
//            }
//            else if (CheckClassIsSnapshot(memberInfo.typeClassInfo))
//            {
//                writer.AppendLine("BuildSnapshot_{0}(builder, {1});", memberInfo.typeClassInfo.className_Info, memberName);
//                snapshotClassList.Add(memberInfo.typeClassInfo);
//            }
//            else
//            {
//                throw new Exception("无法生成BuildSnapshot: " + memberType.Name);
//            }
//        }

//        private static void WriteLoadSnapshot(FileWriter writer, ClassMemberExportInfo memberInfo, Type memberType, string memberName)
//        {
//            if (memberType == typeof(int))
//            {
//                writer.AppendLine("{0} = loader.ReadInt();", memberName);
//            }
//            else if (memberType == typeof(uint))
//            {
//                writer.AppendLine("{0} = loader.ReadUint();", memberName);
//            }
//            else if (memberType == typeof(short))
//            {
//                writer.AppendLine("{0} = loader.ReadShort();", memberName);
//            }
//            else if (memberType == typeof(ushort))
//            {
//                writer.AppendLine("{0} = loader.ReadUshort();", memberName);
//            }
//            else if (memberType == typeof(byte))
//            {
//                writer.AppendLine("{0} = loader.ReadByte();", memberName);
//            }
//            else if (IsEnumString(memberType))
//            {
//                writer.AppendLine("{0} = ({1})loader.ReadShort();", memberName, GetMemberTypeName_Info(memberType.GetGenericArguments()[0]));
//            }
//            else if (IsEnum(memberType))
//            {
//                writer.AppendLine("{0} = ({1})loader.ReadShort();", memberName, memberType.Name);
//            }
//            else if (IsGridPosition(memberType))
//            {
//                writer.AppendLine("{0}.x = loader.ReadByte();", memberName);
//                writer.AppendLine("{0}.y = loader.ReadByte();", memberName);
//            }
//            else if (memberInfo.isFix)
//            {
//                writer.AppendLine("{0} = FixedValue.Import(loader.ReadInt());", memberName);
//            }
//            else if (IsList(memberType))
//            {
//                Type innerType = memberType.GetGenericArguments()[0];
//                writer.AppendLine("int {0}Count = (int)loader.ReadUshort();", memberName);
//                writer.AppendLine("for (int i = 0; i < {0}Count; ++i)", memberName);
//                writer.StartBlock();
//                {

//                    if (innerType == typeof(int))
//                    {
//                        writer.AppendLine("{0}.Add(loader.ReadInt());", memberName);
//                    }
//                    else if (innerType == typeof(uint))
//                    {
//                        writer.AppendLine("{0}.Add(loader.ReadUint());", memberName);
//                    }
//                    else if (innerType == typeof(short))
//                    {
//                        writer.AppendLine("{0}.Add(loader.ReadShort());", memberName);
//                    }
//                    else if (innerType == typeof(ushort))
//                    {
//                        writer.AppendLine("{0}.Add(loader.ReadUshort());", memberName);
//                    }
//                    else if (innerType == typeof(byte))
//                    {
//                        writer.AppendLine("{0}.Add(loader.ReadByte());", memberName);
//                    }
//                    else if (IsEnumString(innerType))
//                    {
//                        writer.AppendLine("{0}.Add(({1})loader.ReadShort());", memberName, GetMemberTypeName_Info(innerType.GetGenericArguments()[0]));
//                    }
//                    else if (IsEnum(innerType))
//                    {
//                        writer.AppendLine("{0}.Add(({1})loader.ReadShort());", memberName, innerType.Name);
//                    }
//                    else if (IsGridPosition(innerType))
//                    {
//                        writer.AppendLine("{0}.Add(GridPosition((int)loader.ReadByte(), (int)loader.ReadByte());", memberName);
//                    }
//                    else if (memberInfo.isFix)
//                    {
//                        writer.AppendLine("{0}.Add(FixedValue.Import(loader.ReadInt()));", memberName);
//                    }
//                    else if (CheckClassIsSnapshot(memberInfo.typeClassInfo))
//                    {
//                        writer.AppendLine("{1}.Add(LoadSnapshot_{0}(loader));", memberInfo.typeClassInfo.className_Info, memberName);
//                    }
//                    else
//                    {
//                        throw new Exception("无法生成BuildSnapshot: " + memberType.Name);
//                    }
//                }
//                writer.EndBlock();
//            }
//            else if (CheckClassIsSnapshot(memberInfo.typeClassInfo))
//            {
//                writer.AppendLine("{1} = LoadSnapshot_{0}(loader);", memberInfo.typeClassInfo.className_Info, memberName);
//            }
//            else
//            {
//                throw new Exception("无法生成BuildSnapshot: " + memberType.Name);
//            }
//        }

//        private static bool CheckClassTypeNeedExportStaticData(Type type)
//        {
//            if (type.IsPrimitive)
//            {
//                return false;
//            }
//            if (type == typeof(string))
//            {
//                return false;
//            }
//            if (type == typeof(AssetWeakRef))
//            {
//                return false;
//            }
//            if (IsEnum(type))
//            {
//                return false;
//            }
//            if (IsEnumString(type))
//            {
//                return false;
//            }
//            if (IsList(type))
//            {
//                return false;
//            }
//            return true;
//        }

//        private static bool CheckClassTypeNeedExportInfo(Type type)
//        {
//            if (type.IsPrimitive)
//            {
//                return false;
//            }
//            if (type == typeof(string))
//            {
//                return false;
//            }
//            if (type == typeof(AssetWeakRef))
//            {
//                return false;
//            }
//            if (IsEnum(type))
//            {
//                return false;
//            }
//            if (IsEnumString(type))
//            {
//                return false;
//            }
//            if (IsList(type))
//            {
//                return false;
//            }
//            if (IsGridPosition(type))
//            {
//                return false;
//            }
//            return true;
//        }

//        private static string GetClassName_EditorData(Type type)
//        {
//            return type.Name;
//        }

//        private static string GetClassName_StaticData(Type type)
//        {
//            return CodeNameUtility.GetFirstUpperName(type.Name.Replace("EditorData", "StaticData"));
//        }

//        private static string GetClassName_Info(Type type)
//        {
//            return CodeNameUtility.GetFirstUpperName(type.Name.Replace("EditorData", "Info"));
//        }

//        private static string GetClassInstanceName_EditorData(Type type)
//        {
//            return CodeNameUtility.GetFirstLowerName(GetClassName_EditorData(type));
//        }

//        private static string GetClassInstanceName_StaticData(Type type)
//        {
//            return CodeNameUtility.GetFirstLowerName(GetClassName_StaticData(type));
//        }

//        private static string GetClassInstanceName_Info(Type type)
//        {
//            return CodeNameUtility.GetFirstLowerName(GetClassName_Info(type));
//        }

//        private static string GetMemberName_EditorData(ClassMemberInfo memberInfo)
//        {
//            return memberInfo.name;
//        }

//        private static string GetMemberName_StaticData(ClassMemberInfo memberInfo)
//        {
//            return CodeNameUtility.GetFirstUpperName(memberInfo.name);
//        }

//        private static string GetMemberName_Info(ClassMemberInfo memberInfo)
//        {
//            string name = CodeNameUtility.GetFirstLowerName(memberInfo.name.Replace("Data", "Info"));
//            if (IsAssetWeakRef(memberInfo.type))
//            {
//                name += "Path";
//            }
//            return name;
//        }

//        private static string GetMemberTypeName_EditorData(Type type)
//        {
//            if (type == typeof(int))
//            {
//                return "Int32";
//            }
//            if (type == typeof(float))
//            {
//                return "Float";
//            }
//            if (type == typeof(bool))
//            {
//                return "Bool";
//            }
//            if (type == typeof(string))
//            {
//                return "String";
//            }
//            if (type == typeof(AssetWeakRef))
//            {
//                return "String";
//            }
//            if (IsGridPosition(type))
//            {
//                return typeof(GridPosition).Name;
//            }
//            if (IsEnumString(type))
//            {
//                return GetMemberTypeName_EditorData(type.GetGenericArguments()[0]);
//            }
//            if (IsList(type))
//            {
//                return GetMemberTypeName_EditorData(type.GetGenericArguments()[0]);
//            }
//            return GetClassName_EditorData(type);
//        }

//        private static string GetMemberTypeName_StaticData(Type type)
//        {
//            if (type == typeof(int))
//            {
//                return "int32";
//            }
//            if (type == typeof(float))
//            {
//                return "float";
//            }
//            if (type == typeof(bool))
//            {
//                return "bool";
//            }
//            if (type == typeof(string))
//            {
//                return "string";
//            }
//            if (type == typeof(AssetWeakRef))
//            {
//                return "string";
//            }
//            if (IsEnumString(type))
//            {
//                return GetMemberTypeName_StaticData(typeof(int));
//            }
//            if (IsList(type))
//            {
//                return string.Format("repeated {0}", GetMemberTypeName_StaticData(type.GetGenericArguments()[0]));
//            }
//            return GetClassName_StaticData(type);
//        }

//        private static string GetMemberTypeName_Info(Type type)
//        {
//            if (type == typeof(int))
//            {
//                return "int";
//            }
//            if (type == typeof(float))
//            {
//                return "float";
//            }
//            if (type == typeof(bool))
//            {
//                return "bool";
//            }
//            if (type == typeof(string))
//            {
//                return "string";
//            }
//            if (type == typeof(AssetWeakRef))
//            {
//                return "string";
//            }
//            if (IsGridPosition(type))
//            {
//                return typeof(GridPosition).Name;
//            }
//            if (IsEnumString(type))
//            {
//                return GetMemberTypeName_Info(type.GetGenericArguments()[0]);
//            }
//            if (IsList(type))
//            {
//                return string.Format("List<{0}>", GetMemberTypeName_Info(type.GetGenericArguments()[0]));
//            }
//            return GetClassName_Info(type);
//        }

//        private static bool IsGridPosition(Type type)
//        {
//            return type == typeof(GridPositionEditorData);
//        }

//        private static bool IsEnum(Type type)
//        {
//            return type.IsEnum;
//        }

//        private static bool IsEnumString(Type type)
//        {
//            return typeof(EnumString).IsAssignableFrom(type);
//        }

//        private static bool IsList(Type type)
//        {
//            return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>);
//        }

//        private static bool IsAssetWeakRef(Type type)
//        {
//            return typeof(AssetWeakRef).IsAssignableFrom(type);
//        }

//        private static string GetNameExceptList(string name)
//        {
//            return GetNameExceptEndStr(name, "List");
//        }

//        private static string GetNameExceptEndStr(string name, string endStr)
//        {
//            if (name.EndsWith(endStr))
//            {
//                return name.Substring(0, name.Length - endStr.Length);
//            }
//            return null;
//        }

//        private static bool CheckClassIsSnapshot(ClassExportInfo classInfo)
//        {
//            if(classInfo == null)
//            {
//                return false;
//            }
//            if (classInfo.isSnapshot)
//            {
//                return true;
//            }
//            ClassExportInfo curInfo = classInfo.baseClassInfo;
//            while (curInfo != null)
//            {
//                if (curInfo.isSnapshot)
//                {
//                    return true;
//                }
//                curInfo = curInfo.baseClassInfo;
//            }
//            return false;
//        }

//        [Serializable]
//        private class ClassDesInfo
//        {
//            public string name;
//            public string baseClassName;
//            public string inheritEnumType;
//            public string inheritEnumValue;
//            public string inheritEnumMember;

//            public List<ClassMemberDesInfo> memberList = new List<ClassMemberDesInfo>();
//        }

//        [Serializable]
//        private class ClassMemberDesInfo
//        {
//            public string tag;
//            public string name;
//            public string type;
//            public bool isList;
//            public bool isEnum;
//            public bool isRate;
//        }

//        private class ClassExportInfo
//        {
//            public Type type;

//            public string className_EditorData;
//            public string className_StaticData;
//            public string className_Info;

//            public string instanceName_EditorData;
//            public string instanceName_StaticData;
//            public string instanceName_Info;

//            public bool needExportStaticData;
//            public bool needExportInfo;
//            public List<ClassMemberExportInfo> memberList = new List<ClassMemberExportInfo>();

//            public object superClassEnumValue;
//            public ClassExportInfo baseClassInfo;
//            public ClassExportInfo baseClassInfoReal;
//            public bool isSnapshot;
//            public bool isPartial;
//            public bool isBaseClass;
//            public ClassMemberExportInfo superEnumMember;
//        }

//        [Serializable]
//        private class ClassMemberExportInfo
//        {
//            public Type type;
//            public FieldInfo fieldInfo;
//            public ClassExportInfo typeClassInfo;
//            public ClassExportInfo ownerClassInfo;

//            public string memberName_EditorData;
//            public string memberName_StaticData;
//            public string memberName_Info;

//            public string memberTypeName_EditorData;
//            public string memberTypeName_StaticData;
//            public string memberTypeName_Info;

//            public bool usingSuperClassEnum;
//            public bool isSuperClassEnum;
//            public bool isDynamicId;
//            public bool isFix;
//            public int fixPrecision;
//        }
//    }
//}
