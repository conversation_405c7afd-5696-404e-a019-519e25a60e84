using Google.Protobuf.Reflection;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Phoenix.Battle;
using Phoenix.ConfigData;
using Phoenix.GameLogic.Battle;

namespace Phoenix.Core
{
    [CustomPropertyDrawer(typeof(EnumString), true)]
    public class EnumStringInspector : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);
            SerializedProperty enumValueProperty = GetEnumValue(property);
            if (enumValueProperty.type == "Enum")
            {
                int enumValueIndex = EditorGUI.Popup(position, enumValueProperty.enumValueIndex, enumValueProperty.enumDisplayNames);
                ApplyValueIndex(property, enumValueIndex);
            }

            EditorGUI.EndProperty();
        }

        public static SerializedProperty GetEnumValue(SerializedProperty sp)
        {
            return sp.FindPropertyRelative("value");
        }


        public static SerializedProperty GetEnumName(SerializedProperty sp)
        {
            return sp.FindPropertyRelative("m_enumName");
        }

        public static void ApplyValueIndex(SerializedProperty sp, int enumValueIndex)
        {
            SerializedProperty enumValueProperty = GetEnumValue(sp);
            SerializedProperty enumNameProperty = GetEnumName(sp);
            if(enumValueProperty.type != "Enum")
            {
                return;
            }
            if (enumValueIndex != enumValueProperty.enumValueIndex)
            {
                enumValueProperty.enumValueIndex = enumValueIndex;
                GUI.changed = true;
            }
            string enumStringValue = enumValueProperty.enumNames.GetValueSafely(enumValueProperty.enumValueIndex);
            if (enumStringValue != enumNameProperty.stringValue)
            {
                enumNameProperty.stringValue = enumStringValue;
                GUI.changed = true;
            }
        }
    }

    public class EnumStringInspector<T> : EnumStringInspector
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);
            SerializedProperty enumValueProperty = GetEnumValue(property);
            Dictionary<string, string> nameMap = new Dictionary<string, string>();
            if (typeof(T) == typeof(TargetSelectRangeId))
            {
                CollectDisplayNameMap_TargetSelectRangeId(nameMap);
            }
            if (typeof(T) == typeof(AttributePartId))
            {
                CollectDisplayNameMap_AttributePartId(nameMap);
            }
            if (typeof(T) == typeof(AttributeId))
            {
                CollectDisplayNameMap_AttributeId(nameMap);
            }
            if (typeof(T) == typeof(BuffEffectType))
            {
                CollectDisplayNameMap_BuffEffectType(nameMap);
            }
            if (typeof(T) == typeof(BattleCampRefType))
            {
                CollectDisplayNameMap_BattleCampRefType(nameMap);
            }
            if (typeof(T) == typeof(EntityHpType))
            {
                CollectDisplayNameMap_EntityHpType(nameMap);
            }
            else
            {
                CollectDisplayNameMap_Default(nameMap);
            }
            List<string> displayNames = new List<string>();
            foreach (var ss in enumValueProperty.enumNames)
            {
                string displayName;
                if (!nameMap.TryGetValue(ss, out displayName))
                {
                    displayName = ss;
                }
                displayNames.Add(displayName);
            }
            int enumValueIndex = EditorGUI.Popup(position, enumValueProperty.enumValueIndex, displayNames.ToArray());
            ApplyValueIndex(property, enumValueIndex);

            EditorGUI.EndProperty();
        }

        private void CollectDisplayNameMap_Default(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as EnumValueDescAttribute;
                    if (descAttribute != null)
                    {
                        nameMap.Add(fieldInfo.Name, descAttribute.name);
                        break;
                    }
                }
            }
        }

        private void CollectDisplayNameMap_TargetSelectRangeId(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as OriginalNameAttribute;
                    if (descAttribute != null)
                    {
                        var splits = descAttribute.Name.Split('_');
                        string nameStr = splits[1];
                        switch (splits[1])
                        {
                            case "None":
                                nameStr = "空";
                                break;
                            case "All":
                                nameStr = "全场";
                                break;
                            case "One":
                                nameStr = "单格(不被buff影响)";
                                break;
                            case "Rhomb":
                                nameStr = "菱形";
                                break;
                            case "RhombIgnoreSize":
                                nameStr = "菱形(无视体型)";
                                break;
                            case "Square":
                                nameStr = "方形";
                                break;
                            case "Cross":
                                nameStr = "十字";
                                break;
                            case "LineForward":
                                nameStr = "线形";
                                break;
                            case "LineForward3l":
                                nameStr = "线形3宽度";
                                break;
                        }
                        if(splits.Length > 2)
                        {
                            if (splits[3] != "0")
                            {
                                nameStr += string.Format("/{0}格中空{1}格", splits[2], splits[3]);
                            }
                            else
                            {

                                nameStr += string.Format("/{0}格", splits[2]);
                            }
                        }
                        nameMap.Add(fieldInfo.Name, nameStr);
                        break;
                    }
                }
            }
        }


        private void CollectDisplayNameMap_AttributePartId(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as OriginalNameAttribute;
                    if (descAttribute != null)
                    {
                        var splits = descAttribute.Name.Split('_');
                        string nameStr = splits[1];
                        switch (splits[1])
                        {
                            case "None":
                                nameStr = "空";
                                break;
                            case "HpMax":
                                nameStr = "气血";
                                break;
                            case "ShieldHpMax":
                                nameStr = "护盾值上限";
                                break;
                            case "AngerMax":
                                nameStr = "愤怒值";
                                break;
                            case "PhysicalAttack":
                                nameStr = "物攻";
                                break;
                            case "MagicalAttack":
                                nameStr = "法攻";
                                break;
                            case "PhysicalDefence":
                                nameStr = "物防";
                                break;
                            case "MagicalDefence":
                                nameStr = "法防";
                                break;
                            case "Tech":
                                nameStr = "技巧";
                                break;
                            case "Speed":
                                nameStr = "速度";
                                break;
                            case "MovePoint":
                                nameStr = "行动力";
                                break;
                            case "CriticalRate":
                                nameStr = "暴击率";
                                break;
                            case "TechAnti":
                                nameStr = "技巧抗性";
                                break;
                            case "CriticalRateAnti":
                                nameStr = "暴击率抵抗";
                                break;
                            case "CriticalDamageRate":
                                nameStr = "暴击伤害加成";
                                break;
                            case "CriticalDamageRateAnti":
                                nameStr = "暴击伤害减免";
                                break;
                            case "PhysicalDamageRate":
                                nameStr = "物理伤害加成";
                                break;
                            case "PhysicalDamageRateAnti":
                                nameStr = "物理伤害减免";
                                break;
                            case "MagicalDamageRate":
                                nameStr = "法术伤害加成";
                                break;
                            case "MagicalDamageRateAnti":
                                nameStr = "法术伤害减免";
                                break;
                            case "AllDamageRate":
                                nameStr = "全伤害加成";
                                break;
                            case "AllDamageRateAnti":
                                nameStr = "全伤害减免";
                                break;
                            case "HpRateDamageRateAnti":
                                nameStr = "生命百分比伤害减免";
                                break;
                            case "RealDamageRateAnti":
                                nameStr = "固伤减免";
                                break;
                            case "HealRate":
                                nameStr = "治疗效果加成";
                                break;
                            case "HealRateAnti":
                                nameStr = "治疗效果降低";
                                break;
                            case "FightbackAttackDamageRate":
                                nameStr = "反击伤害%";
                                break;
                            case "ChaseHitAttackDamageRate":
                                nameStr = "追击伤害%";
                                break;
                            case "DoubleHitAttackDamageRate":
                                nameStr = "连击伤害%";
                                break;
                            case "DodgeRate":
                                nameStr = "闪避率加成";
                                break;
                            case "PhysicalDefenceBreakRate":
                                nameStr = "物理穿透";
                                break;
                            case "MagicalDefenceBreakRate":
                                nameStr = "法术穿透";
                                break;
                            case "ExtraAttackSelectRange":
                                nameStr = "普攻范围增加";
                                break;
                            case "ExtraSkillSelectRange":
                                nameStr = "技能选择范围增加";
                                break;
                            case "ExtraSkillEffectRange":
                                nameStr = "技能效果范围增加";
                                break;
                            case "HealthStealRate":
                                nameStr = "吸血";
                                break;
                        }
                        if (splits[1] != "None")
                        {
                            nameStr += "/";
                        }
                        if (splits.Length > 2)
                        {
                            if (splits.GetValueSafely(2) == "Base")
                            {
                                nameStr += "基础";
                            }
                            else if (splits.GetValueSafely(2) == "BaseGrowth")
                            {
                                nameStr += "基础成长";
                            }
                            else if (splits.GetValueSafely(2) == "BaseMult")
                            {
                                nameStr += "基础%";
                            }
                            else if (splits.GetValueSafely(2) == "Add")
                            {
                                if (splits.GetValueSafely(3) == "Extra")
                                {
                                    nameStr += "动态";
                                }
                                else if (splits.GetValueSafely(3) == null)
                                {
                                    nameStr += "静态";
                                }
                                else
                                {
                                    nameStr += splits.GetValueSafely(3);
                                }
                            }
                            else if (splits.GetValueSafely(2) == "Mult")
                            {
                                if (splits.GetValueSafely(3) == "Level")
                                {
                                    nameStr += "动态%等级修正";
                                }
                                else if (splits.GetValueSafely(3) == "Extra")
                                {
                                    nameStr += "动态%";
                                }
                                else if (splits.GetValueSafely(3) == null)
                                {
                                    nameStr += "静态%";
                                }
                                else
                                {
                                    nameStr += splits.GetValueSafely(3);
                                }
                            }
                        }
                        nameMap.Add(fieldInfo.Name, nameStr);
                        break;
                    }
                }
            }
        }

        private void CollectDisplayNameMap_AttributeId(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as OriginalNameAttribute;
                    if (descAttribute != null)
                    {
                        var splits = descAttribute.Name.Split('_');
                        string nameStr = splits[1];
                        switch (splits[1])
                        {
                            case "None":
                                nameStr = "空";
                                break;
                            case "HpMax":
                                nameStr = "气血";
                                break;
                            case "ShieldHpMax":
                                nameStr = "护盾值上限";
                                break;
                            case "AngerMax":
                                nameStr = "愤怒值";
                                break;
                            case "PhysicalAttack":
                                nameStr = "物攻";
                                break;
                            case "MagicalAttack":
                                nameStr = "法攻";
                                break;
                            case "PhysicalDefence":
                                nameStr = "物防";
                                break;
                            case "MagicalDefence":
                                nameStr = "法防";
                                break;
                            case "Tech":
                                nameStr = "技巧";
                                break;
                            case "Speed":
                                nameStr = "速度";
                                break;
                            case "MovePoint":
                                nameStr = "行动力";
                                break;
                            case "CriticalRate":
                                nameStr = "暴击率";
                                break;
                            case "TechAnti":
                                nameStr = "技巧抗性";
                                break;
                            case "CriticalRateAnti":
                                nameStr = "暴击率抵抗";
                                break;
                            case "CriticalDamageRate":
                                nameStr = "暴击伤害加成";
                                break;
                            case "CriticalDamageRateAnti":
                                nameStr = "暴击伤害减免";
                                break;
                            case "PhysicalDamageRate":
                                nameStr = "物理伤害加成";
                                break;
                            case "PhysicalDamageRateAnti":
                                nameStr = "物理伤害减免";
                                break;
                            case "MagicalDamageRate":
                                nameStr = "法术伤害加成";
                                break;
                            case "MagicalDamageRateAnti":
                                nameStr = "法术伤害减免";
                                break;
                            case "AllDamageRate":
                                nameStr = "全伤害加成";
                                break;
                            case "AllDamageRateAnti":
                                nameStr = "全伤害减免";
                                break;
                            case "HpRateDamageRateAnti":
                                nameStr = "生命百分比伤害减免";
                                break;
                            case "RealDamageRateAnti":
                                nameStr = "固伤减免";
                                break;
                            case "HealRate":
                                nameStr = "治疗效果加成";
                                break;
                            case "HealRateAnti":
                                nameStr = "治疗效果降低";
                                break;
                            case "FightbackAttackDamageRate":
                                nameStr = "反击伤害%";
                                break;
                            case "ChaseHitAttackDamageRate":
                                nameStr = "追击伤害%";
                                break;
                            case "DoubleHitAttackDamageRate":
                                nameStr = "连击伤害%";
                                break;
                            case "DodgeRate":
                                nameStr = "闪避率加成";
                                break;
                            case "PhysicalDefenceBreakRate":
                                nameStr = "物理穿透";
                                break;
                            case "MagicalDefenceBreakRate":
                                nameStr = "法术穿透";
                                break;
                            case "ExtraAttackSelectRange":
                                nameStr = "普攻范围增加";
                                break;
                            case "ExtraSkillSelectRange":
                                nameStr = "技能选择范围增加";
                                break;
                            case "ExtraSkillEffectRange":
                                nameStr = "技能效果范围增加";
                                break;
                            case "HealthStealRate":
                                nameStr = "吸血";
                                break;
                        }
                        nameMap.Add(fieldInfo.Name, nameStr);
                        break;
                    }
                }
            }
        }

        private void CollectDisplayNameMap_BattleCampRefType(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as OriginalNameAttribute;
                    if (descAttribute != null)
                    {
                        var splits = descAttribute.Name.Split('_');
                        string nameStr = splits[1];
                        switch (splits[1])
                        {
                            case "None":
                                nameStr = "空";
                                break;
                            case "Friendly":
                                nameStr = "友方";
                                break;
                            case "Rival":
                                nameStr = "敌方";
                                break;
                        }
                        nameMap.Add(fieldInfo.Name, nameStr);
                        break;
                    }
                }
            }
        }

        private void CollectDisplayNameMap_EntityHpType(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as OriginalNameAttribute;
                    if (descAttribute != null)
                    {
                        var splits = descAttribute.Name.Split('_');
                        string nameStr = splits[1];
                        switch (splits[1])
                        {
                            case "None":
                                nameStr = "空";
                                break;
                            case "Value":
                                nameStr = "血量";
                                break;
                            case "HitCount":
                                nameStr = "命中次数";
                                break;
                        }
                        nameMap.Add(fieldInfo.Name, nameStr);
                        break;
                    }
                }
            }
        }

        private void CollectDisplayNameMap_BuffEffectType(Dictionary<string, string> nameMap)
        {
            foreach (var fieldInfo in typeof(T).GetFields())
            {
                var customAttributes = fieldInfo.GetCustomAttributes(false);
                if (customAttributes == null || customAttributes.Length == 0)
                {
                    continue;
                }
                foreach (var customAttribute in customAttributes)
                {
                    var descAttribute = customAttribute as OriginalNameAttribute;
                    if (descAttribute != null)
                    {
                        var splits = descAttribute.Name.Split('_');
                        string nameStr = splits[1];
                        switch (splits[1])
                        {
                            case "None":
                                nameStr = "空";
                                break;
                            case "AttributeChange":
                                nameStr = "属性改变";
                                break;
                            case "AttributeAccumulate":
                                nameStr = "属性改变（乘累加参数）";
                                break;
                            case "AttributeDepend":
                                nameStr = "属性关联";
                                break;
                            case "StateApply":
                                nameStr = "施加状态";
                                break;
                            case "AuraApply":
                                nameStr = "光环";
                                break;
                            case "SkillTrigger":
                                nameStr = "触发";
                                break;
                        }
                        nameMap.Add(fieldInfo.Name, nameStr);
                        break;
                    }
                }
            }
        }
    }

    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectAttachBuffFuncType>), false)]
    //public class EnumStringInspector_BattleActionEffectAttachBuffFuncType : EnumStringInspector<BattleActionEffectAttachBuffFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectDecreaseBuffFuncType>), false)]
    //public class EnumStringInspector_BattleActionEffectDecreaseBuffFuncType : EnumStringInspector<BattleActionEffectDecreaseBuffFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectType>), false)]
    //public class EnumStringInspector_BattleActionEffectType : EnumStringInspector<BattleActionEffectType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFactorType>), false)]
    //public class EnumStringInspector_BattleActionEffectFactorType : EnumStringInspector<BattleActionEffectFactorType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectDetachBuffFuncType>), false)]
    //public class EnumStringInspector_BattleActionEffectDetachBuffFuncType : EnumStringInspector<BattleActionEffectDetachBuffFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectSummonFuncType>), false)]
    //public class EnumStringInspector_BattleActionEffectSummonFuncType : EnumStringInspector<BattleActionEffectSummonFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<TargetSelectCenterType>), false)]
    //public class EnumStringInspector_SkillTargetType : EnumStringInspector<TargetSelectCenterType> { }
    //[CustomPropertyDrawer(typeof(EnumString<TargetSelectFuncType>), false)]
    //public class EnumStringInspector_TargetSelectFuncType : EnumStringInspector<TargetSelectFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<TargetSelectFilterFuncType>), false)]
    //public class EnumStringInspector_TargetSelectFilterFuncType : EnumStringInspector<TargetSelectFilterFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFuncOfConditionType>), false)]
    //public class EnumStringInspector_BattleActionEffectFuncOfConditionType : EnumStringInspector<BattleActionEffectFuncOfConditionType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFuncOfEntityListType>), false)]
    //public class EnumStringInspector_BattleActionEffectFuncOfEntityListType : EnumStringInspector<BattleActionEffectFuncOfEntityListType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectSummonLevelType>), false)]
    //public class EnumStringInspector_BattleActionEffectSummonLevelType : EnumStringInspector<BattleActionEffectSummonLevelType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFuncOfConditionKillTargetFuncType>), false)]
    //public class EnumStringInspector_BattleActionEffectFuncOfConditionKillTargetFuncType : EnumStringInspector<BattleActionEffectFuncOfConditionKillTargetFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BuffTriggerType>), false)]
    //public class EnumStringInspector_BuffTriggerType : EnumStringInspector<BuffTriggerType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleTriggerType>), false)]
    //public class EnumStringInspector_BattleTriggerType : EnumStringInspector<BattleTriggerType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleStageType>), false)]
    //public class EnumStringInspector_BattleStageType : EnumStringInspector<BattleStageType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleRoundRobinType>), false)]
    //public class EnumStringInspector_BattleRoundRobinType : EnumStringInspector<BattleRoundRobinType> { }
    //[CustomPropertyDrawer(typeof(EnumString<EntityAnimatorIdleType>), false)]
    //public class EnumStringInspector_EntityAnimatorReturnType : EnumStringInspector<EntityAnimatorIdleType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleStageActionAttachBuffFuncType>), false)]
    //public class EnumStringInspector_BattleStageActionBuffAttachFuncType : EnumStringInspector<BattleStageActionAttachBuffFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<Battle.SummonCountType>), false)]
    //public class EnumStringInspector_BattleStageActionEntitySummonCountType : EnumStringInspector<Battle.SummonCountType> { }
    //[CustomPropertyDrawer(typeof(EnumString<Battle.SummonLevelType>), false)]
    //public class EnumStringInspector_BattleStageActionEntitySummonLevelType : EnumStringInspector<Battle.SummonLevelType> { }
    //[CustomPropertyDrawer(typeof(EnumString<SummonResolveType>), false)]
    //public class EnumStringInspector_BattleStageActionEntitySummonResolveType : EnumStringInspector<SummonResolveType> { }
    //[CustomPropertyDrawer(typeof(EnumString<SummonPerformanceType>), false)]
    //public class EnumStringInspector_BattleStageActionEntitySummonPerformanceType : EnumStringInspector<SummonPerformanceType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleStageActionEntityFindFuncType>), false)]
    //public class EnumStringInspector_BattleStageActionEntityFindFuncType : EnumStringInspector<BattleStageActionEntityFindFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFuncOfValueType>), false)]
    //public class EnumStringInspector_BattleActionEffectFuncOfValueType : EnumStringInspector<BattleActionEffectFuncOfValueType> { }
    //[CustomPropertyDrawer(typeof(EnumString<CompareType>), false)]
    //public class EnumStringInspector_CompareType : EnumStringInspector<CompareType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFuncOfConditionSubjectType>), false)]
    //public class EnumStringInspector_BattleActionEffectFuncOfConditionSubjectType : EnumStringInspector<BattleActionEffectFuncOfConditionSubjectType> { }
    //[CustomPropertyDrawer(typeof(EnumString<TargetSelectRangeId>), false)]
    //public class EnumStringInspector_TargetSelectRangeId : EnumStringInspector<TargetSelectRangeId> { }
    //[CustomPropertyDrawer(typeof(EnumString<AttributePartId>), false)]
    //public class EnumStringInspector_AttributePartId : EnumStringInspector<AttributePartId> { }
    //[CustomPropertyDrawer(typeof(EnumString<AttributeId>), false)]
    //public class EnumStringInspector_AttributeId : EnumStringInspector<AttributeId> { }
    //[CustomPropertyDrawer(typeof(EnumString<BuffEffectType>), false)]
    //public class EnumStringInspector_BuffEffectType : EnumStringInspector<BuffEffectType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectFuncOfGridType>), false)]
    //public class EnumStringInspector_BattleActionEffectFuncOfGridType : EnumStringInspector<BattleActionEffectFuncOfGridType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectGridSelectType>), false)]
    //public class EnumStringInspector_BattleActionEffectGridSelectType : EnumStringInspector<BattleActionEffectGridSelectType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleActionEffectCheckEntityFuncType>), false)]
    //public class EnumStringInspector_BattleActionEffectCheckEntityFuncType : EnumStringInspector<BattleActionEffectCheckEntityFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BuffEffectSkillTriggerCdType>), false)]
    //public class EnumStringInspector_BuffEffectSkillTriggerCdType : EnumStringInspector<BuffEffectSkillTriggerCdType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BattleCampRefType>), false)]
    //public class EnumStringInspector_BattleCampRefType : EnumStringInspector<BattleCampRefType> { }
    //[CustomPropertyDrawer(typeof(EnumString<Battle.BuffEffectAttributeAccumulateType>), false)]
    //public class EnumStringInspector_SkillEffectAccumulateType : EnumStringInspector<Battle.BuffEffectAttributeAccumulateType> { }
    //[CustomPropertyDrawer(typeof(EnumString<Battle.BuffEffectStateType>), false)]
    //public class EnumStringInspector_EntityStateType : EnumStringInspector<Battle.BuffEffectStateType> { }
    //[CustomPropertyDrawer(typeof(EnumString<BuffEffectImmuneFuncType>), false)]
    //public class EnumStringInspector_BuffEffectImmuneFuncType : EnumStringInspector<BuffEffectImmuneFuncType> { }
    //[CustomPropertyDrawer(typeof(EnumString<EntityHpType>), false)]
    //public class EnumStringInspector_EntityHpType : EnumStringInspector<EntityHpType> { }


}
