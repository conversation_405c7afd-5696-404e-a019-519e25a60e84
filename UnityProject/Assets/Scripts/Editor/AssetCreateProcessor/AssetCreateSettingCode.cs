#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;

namespace Phoenix.Core
{
    public class AssetCreateSettingCode : AssetCreateSetting
    {
        public string nameSpace;
        public string superClassName;
        public List<string> usingNameSpaceList = new List<string>();
        public List<string> attributeList = new List<string>();

        public override void Apply(string path, AssetCreateProcessorSetting.RuleSetting setting)
        {
            path = path.Replace(".meta", "");
            string codeStr = File.ReadAllText(path);
            if (!codeStr.Contains("// Update is called once per frame"))
            {
                return;
            }
            Create(path, setting);
        }

        public override void Create(string path, AssetCreateProcessorSetting.RuleSetting setting)
        {
            string typeName = FilePathUtility.GetFileNameWithoutExtension(path);
            FileWriter writer = new FileWriter();
            foreach (string usingNameSpace in usingNameSpaceList)
            {
                writer.AppendLine("using {0};", usingNameSpace);
            }
            writer.AppendLine();
            if (!string.IsNullOrEmpty(nameSpace))
            {
                writer.AppendLine("namespace {0}", nameSpace);
                writer.StartBlock();
            }
            foreach(string attribute in attributeList)
            {
                writer.AppendLine("[{0}]", attribute);
            }
            if (string.IsNullOrEmpty(superClassName))
            {
                writer.AppendLine("public class {0}", typeName);
            }
            else
            {
                writer.AppendLine("public class {0} : {1}", typeName, superClassName);
            }
            writer.StartBlock();
            {
            }
            writer.EndBlock();
            if (!string.IsNullOrEmpty(nameSpace))
            {
                writer.EndBlock();
            }
            writer.WriteFile(path);
        }
    }
}
#endif