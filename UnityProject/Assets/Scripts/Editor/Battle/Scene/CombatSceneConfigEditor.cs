//using System;
//using System.Collections.Generic;
//using UnityEditor;
//using UnityEngine;
//using UnityEditorInternal;
//using System.Linq.Expressions;
//using System.Text;

//namespace Phoenix.GameLogic.Battle
//{
//    public class BaseEditor<T> : Editor where T : class
//    {
//        /// <summary>
//        /// The target object, cast as the same class as the object being edited
//        /// </summary>
//        protected T Target { get { return target as T; } }
//    }


//    [CustomEditor(typeof(CombatSceneConfig))]
//    public class CombatSceneConfigEditor : BaseEditor<CombatSceneConfig>
//    {
//        public Vector3 m_attackPosition;
//        public Vector3 m_attackDirection;

//        public override void OnInspectorGUI()
//        {
//            serializedObject.Update();
//            // Waypoints
//            EditorGUI.BeginChangeCheck();
//            if (EditorGUI.EndChangeCheck())
//                serializedObject.ApplyModifiedProperties();
//        }

//        void OnSceneGUI()
//        {
//            if (Tools.current == Tool.Move)
//            {
//                Color colorOld = Handles.color;
//                Matrix4x4 localToWorld = Target.transform.localToWorldMatrix;
//                DrawSelectionHandle(i, localToWorld);
//                if (mWaypointList.index == i)
//                    DrawPositionControl(i, localToWorld, Target.transform.rotation); // Waypoint is selected


//                for (int i = 0; i < Target.m_Waypoints.Length; ++i)
//                {
//                }
//                Handles.color = colorOld;
//            }
//        }


//        void DrawSelectionHandle(Vector3 position, Matrix4x4 localToWorld)
//        {
//            if (Event.current.button != 1)
//            {
//                Vector3 pos = localToWorld.MultiplyPoint(position);
//                float size = HandleUtility.GetHandleSize(pos) * 0.2f;
//                Handles.color = Color.white;
//                if (Handles.Button(pos, Quaternion.identity, size, size, Handles.SphereHandleCap))
//                {
//                    RepaintGameView();
//                }
//                Handles.BeginGUI();
//                Vector2 labelSize = new Vector2(EditorGUIUtility.singleLineHeight * 2, EditorGUIUtility.singleLineHeight);
//                Vector2 labelPos = HandleUtility.WorldToGUIPoint(pos);
//                labelPos.y -= labelSize.y / 2;
//                labelPos.x -= labelSize.x / 2;
//                GUILayout.BeginArea(new Rect(labelPos, labelSize));
//                GUIStyle style = new GUIStyle();
//                style.normal.textColor = Color.black;
//                style.alignment = TextAnchor.MiddleCenter;
//                GUILayout.Label(new GUIContent("Waypoint"), style);
//                GUILayout.EndArea();
//                Handles.EndGUI();
//            }
//        }

//        void DrawPositionControl(int i, Matrix4x4 localToWorld, Quaternion localRotation)
//        {
//            Vector3 position = Target.m_Waypoints[i];
//            Vector3 pos = localToWorld.MultiplyPoint(position);
//            EditorGUI.BeginChangeCheck();
//            Handles.color = Color.green;
//            Quaternion rotation = (Tools.pivotRotation == PivotRotation.Local)
//                ? localRotation : Quaternion.identity;
//            float size = HandleUtility.GetHandleSize(pos) * 0.1f;
//            Handles.SphereHandleCap(0, pos, rotation, size, EventType.Repaint);
//            pos = Handles.PositionHandle(pos, rotation);
//            if (EditorGUI.EndChangeCheck())
//            {
//                Undo.RecordObject(target, "Move Waypoint");
//                position = Matrix4x4.Inverse(localToWorld).MultiplyPoint(pos);
//                Target.m_Waypoints[i] = position;
//                RepaintGameView();
//            }
//        }

//        void DrawWaypointEditor(Rect rect, int index)
//        {
//            SerializedProperty element = mWaypointList.serializedProperty.GetArrayElementAtIndex(index);
//            float hSpace = 3;
//            rect.width -= hSpace; rect.y += 1;
//            Vector2 numberDimension = GUI.skin.label.CalcSize(new GUIContent("999"));
//            Rect r = new Rect(rect.position, numberDimension);
//            if (GUI.Button(r, new GUIContent(index.ToString(), "Go to the waypoint in the scene view")))
//            {
//                if (SceneView.lastActiveSceneView != null)
//                {
//                    mWaypointList.index = index;
//                    SceneView.lastActiveSceneView.pivot = Target.m_Waypoints[index];
//                    SceneView.lastActiveSceneView.size = 4;
//                    SceneView.lastActiveSceneView.Repaint();
//                }
//            }
//            r.x += r.width + hSpace; r.width = rect.width - (r.width + hSpace) - (r.height + hSpace);
//            EditorGUI.PropertyField(r, element, GUIContent.none);
//        }




//        private static int m_lastRepaintFrame;
//        public static void RepaintGameView()
//        {
//            if (m_lastRepaintFrame == Time.frameCount)
//                return;
//            m_lastRepaintFrame = Time.frameCount;

//            EditorApplication.QueuePlayerLoopUpdate();
//            InternalEditorUtility.RepaintAllViews();
//        }
//    }
//}
