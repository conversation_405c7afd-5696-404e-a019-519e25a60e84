


using NodeCanvas.Framework;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.StoryGraphModule
{
    public partial class StoryGraph
    {
        private bool enterStartNodeFlag;
        public List<StoryNode> Nodes = new List<StoryNode>(32);
        public List<StoryNode> executedNodes = new List<StoryNode>(8);

        protected override void OnGraphStarted()
        {
            Debug.LogError($"OnGraphStarted");
            enterStartNodeFlag = true;
        }

        protected override void OnGraphUpdate()
        {
            if (enterStartNodeFlag)
            {
                enterStartNodeFlag = false;
                EnterNode((StoryNode)primeNode);
            }
            executedNodes.Clear();
            for (int i = 0; i < Nodes.Count; i++)
            {
                StoryNode node = Nodes[i];
                if (node is ITickable tickableNode)
                {
                    tickableNode.Tick(Time.deltaTime);
                }
                if (node.status == Status.Success)
                {
                    executedNodes.Add(node);
                }
            }
            foreach (StoryNode node in executedNodes)
            {
                Nodes.Remove(node);
            }

            if (Nodes.Count == 0)
            {
                Stop(true);
            }
        }

        protected override void OnGraphStoped()
        {
            Debug.LogError($"OnGraphStoped");
            Nodes.Clear();
        }

        public void EnterNode(StoryNode node)
        {
            if (node == null)
            {
                return;
            }
            if (node.CanActive)
            {
                Debug.LogError($"EnterNode_____{node.ID}");
            }
            else
            {
                Debug.LogError($"EnterNode___Failed_____{node.ID}");
                return;
            }


            Nodes.Add(node);
            node.Reset(false);
            node.Execute(agent, blackboard);
            if (node.status == Status.Error)
            {
                Stop(false);
            }
        }

    }
}
