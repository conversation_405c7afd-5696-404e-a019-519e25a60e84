
using System;
using NodeCanvas.Framework;
using ParadoxNotion;
using UnityEngine;

namespace Phoenix.StoryGraphModule
{
    [CreateAssetMenu(fileName = "StoryGraphData", menuName = "ParadoxNotion/CustomGraph/StoryGraphAsset")]
    public partial class StoryGraph : Graph
    {
        public override Type baseNodeType => typeof(ActionNode);
        public override bool requiresAgent => false;
        public override bool requiresPrimeNode => true;
        public override bool allowBlackboardOverrides => false;
        public override bool canAcceptVariableDrops => false;
        public override bool isTree => true;
        public override PlanarDirection flowDirection => PlanarDirection.Vertical;



        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR
        protected override UnityEditor.GenericMenu OnCanvasContextMenu(UnityEditor.GenericMenu menu, Vector2 mousePos)
        {
            return menu;
        }


        [UnityEditor.MenuItem("Tools/CustomGraph/Create/StoryGraphAsset", false, 1)]
        public static void CreateGraph()
        {
            StoryGraph sg = ParadoxNotion.Design.EditorUtils.CreateAsset<StoryGraph>();
            UnityEditor.Selection.activeObject = sg;
        }
#endif
        ///----------------------------------------------------------------------------------------------
    }
}