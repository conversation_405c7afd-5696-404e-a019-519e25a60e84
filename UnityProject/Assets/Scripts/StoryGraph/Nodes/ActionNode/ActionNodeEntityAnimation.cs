
using System;
using ParadoxNotion.Design;

namespace Phoenix.StoryGraphModule
{
    [Category("Entity")]
    [Name("角色动画")]
    public class ActionNodeEntityAnimation : ActionNode
    {
        public Int32 m_entityUid;
        public String m_animationName;


        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR

        protected override string NodeSummary => $"{m_entityUid}播放{m_animationName}";

        protected override void OnNodeInspectorGUI()
        {
            base.OnNodeInspectorGUI();
        }

#endif
    }
}