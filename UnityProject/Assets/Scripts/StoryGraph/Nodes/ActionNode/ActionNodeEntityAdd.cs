
using NodeCanvas.Framework;
using ParadoxNotion.Design;
using Phoenix.Core;
using Phoenix.Hakoniwa.Logic;
using UnityEngine;

namespace Phoenix.StoryGraphModule
{
    [Category("Entity")]
    [Name("角色添加")]
    public class ActionNodeEntityAdd : ActionNode
    {
        public EntityInfo m_entityInfo;

        protected override Status OnExecute(Component agent, IBlackboard blackboard)
        {
            HakoniwaEntity hakoEntity = new HakoniwaEntity()
            {
                Uid = m_entityInfo.uid,
                SkinId = m_entityInfo.rid,
                EntityType = m_entityInfo.entityType,
                Position = m_entityInfo.position,
                DefaultAnimationName = m_entityInfo.defaultAnimation
            };

            EventManager.instance.Broadcast(EventID.HakoEntityViewAdd, hakoEntity);
            ContinueToNext();
            return Status.Success;
        }


        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR

        protected override string NodeSummary => $"{m_entityInfo}";
        protected override void OnNodeInspectorGUI()
        {
            base.OnNodeInspectorGUI();
        }

#endif
    }
}