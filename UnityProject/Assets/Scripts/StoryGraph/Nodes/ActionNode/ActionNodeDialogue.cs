
using System;
using NodeCanvas.Framework;
using ParadoxNotion.Design;
using Phoenix.GameLogic.UI;
using UnityEngine;

namespace Phoenix.StoryGraphModule
{
    [Name("播放对话")]
    [Color("990099")]
    public class ActionNodeDialogue : ActionNode
    {
        public Int32 m_dialogueId;


        protected override Status OnExecute(Component agent, IBlackboard blackboard)
        {
            // TODO Open DialogueUI
            DialogueUI.ShowDialogue(m_dialogueId, OnExecuteEnd, null);
            //OnExecuteEnd();
            return Status.Running;
        }


        protected virtual void OnExecuteEnd()
        {
            ContinueToNext();
            SetStatus(Status.Success);
        }


        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR

        protected override string NodeSummary => $"{m_dialogueId}";

        protected override void OnNodeInspectorGUI()
        {
            base.OnNodeInspectorGUI();
        }

#endif
    }
}