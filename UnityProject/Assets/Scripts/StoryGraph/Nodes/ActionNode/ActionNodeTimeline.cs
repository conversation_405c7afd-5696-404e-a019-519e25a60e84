
using System;
using NodeCanvas.Framework;
using ParadoxNotion.Design;
using Phoenix.Core;
using UnityEngine;

namespace Phoenix.StoryGraphModule
{
    [Name("播放Timeline")]
    public class ActionNodeTimeline: ActionNode
    {
        public String m_timeline;

        protected override Status OnExecute(Component agent, IBlackboard blackboard)
        {
            EventManager.instance.Broadcast<String, Action<String>>(EventID.HakoSceneTimelinePlay, m_timeline, OnTimelinePlayEnd);
            return Status.Running;
        }

        protected void OnTimelinePlayEnd(String timelinePath)
        {
            if (timelinePath.Equals(m_timeline))
            {
                ContinueToNext();
                SetStatus(Status.Success);
            }
        }


        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR

        protected override string NodeSummary
        {
            get
            {
                GameObject go = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(m_timeline);
                if (go == null)
                {
                    return "empty";
                }
                return m_timeline.Substring(m_timeline.LastIndexOf('/') + 1);
            }
        }

        protected override void OnNodeGUI()
        {
            GUILayout.BeginHorizontal(Styles.roundedBox);
            {
                GUILayout.Label($"<b>{NodeSummary}</b>", Styles.leftLabel);
                var lastRect = GUILayoutUtility.GetLastRect();
                if (GUI.Button(lastRect, String.Empty, Styles.centerLabel))
                {
                    GameObject go = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(m_timeline);
                    if (go != null)
                    {
                        UnityEditor.EditorGUIUtility.PingObject(go);
                    }
                }
            }
            GUILayout.EndHorizontal();

        }

        protected override void OnNodeInspectorGUI()
        {
            GameObject go = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(m_timeline);
            UnityEditor.EditorGUI.BeginChangeCheck();
            UnityEngine.Object newGo = UnityEditor.EditorGUILayout.ObjectField("Timeline", go, typeof(GameObject), false);
            if (UnityEditor.EditorGUI.EndChangeCheck())
            {
                m_timeline = newGo == null ? String.Empty : UnityEditor.AssetDatabase.GetAssetPath(newGo);
            }
        }

#endif
    }
}