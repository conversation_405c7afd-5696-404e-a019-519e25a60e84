
using System;
using ParadoxNotion.Design;

namespace Phoenix.StoryGraphModule
{
    [Category("Entity")]
    [Name("角色表情Emoji")]
    public class ActionNodeEntityEmoji : ActionNode
    {
        public Int32 m_entityUid;
        public String m_emojiName;

        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR

        protected override string NodeSummary => $"{m_entityUid}播放{m_emojiName}";

        protected override void OnNodeInspectorGUI()
        {
            base.OnNodeInspectorGUI();
        }

#endif
    }
}