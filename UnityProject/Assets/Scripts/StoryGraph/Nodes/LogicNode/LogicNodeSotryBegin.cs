

using System;
using ParadoxNotion.Design;

namespace Phoenix.StoryGraphModule
{
    [Name("剧情开始")]
    [Description("剧情节点图开始标记")]
    [Color("00CC66")]
    public class LogicNodeSotryBegin : LogicNode
    {
        public override bool allowAsPrime => true;
        public override int maxInConnections => 0;
        public override int maxOutConnections => 1;



        ///----------------------------------------------------------------------------------------------
        ///---------------------------------------UNITY EDITOR-------------------------------------------
#if UNITY_EDITOR

        protected override string NodeSummary => String.Empty;

#endif
    }
}