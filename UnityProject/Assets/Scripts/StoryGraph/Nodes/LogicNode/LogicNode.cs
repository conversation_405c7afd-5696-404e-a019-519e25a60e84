

using ParadoxNotion;
using ParadoxNotion.Design;

namespace Phoenix.StoryGraphModule
{
    [Color("0033FF")]
    public abstract class LogicNode : ActionNode
    {
        public override bool allowAsPrime => false;
        public override bool canSelfConnect => false;
        public override Alignment2x2 commentsAlignment { get { return Alignment2x2.Bottom; } }
        public override Alignment2x2 iconAlignment { get { return Alignment2x2.Default; } }
        public override int maxInConnections => -1;
        public override int maxOutConnections => -1;
    }
}