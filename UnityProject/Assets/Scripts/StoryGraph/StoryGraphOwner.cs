

using NodeCanvas.Framework;

namespace Phoenix.StoryGraphModule
{
    public class StoryGraphOwner : GraphOwner<StoryGraph>
    {
        public void CallFunction(string name)
        {
            //behaviour.CallFunction(name);
        }

        public object CallFunction(string name, params object[] args)
        {
            return null;
            //return behaviour.CallFunction(name, args);
        }

        public void CallFunctionAsync(string name, System.Action<object> callback, params object[] args)
        {
            //behaviour.CallFunctionAsync(name, callback, args);
        }
    }

}

