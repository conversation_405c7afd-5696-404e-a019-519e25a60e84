// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_PropOpInfo_S2C : MsgPackStructBase
    {
        public override void Init()
		{
			// ProtoCode = EProtoCode.PROPOPINFO_PROPOPINFO_S2C;
			OpType = EMsgPackOpType.OP_DEFAULT;
		}

        [Key(2)]
        public String ComponentName { get; set; }

        [Key(3)]
        public Int32 KeyIdxClient { get; set; }

        [Key(4)]
        public object Value { get; set; }

    }

}