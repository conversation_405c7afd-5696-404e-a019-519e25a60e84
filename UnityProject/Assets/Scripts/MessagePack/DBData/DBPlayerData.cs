using MessagePack;
using System;
using System.Collections.Generic;
using Phoenix.Core.Entity;

namespace Mos.DBData
{
    [MessagePackObject]
    public class DBPlayerData 
    {
        Dictionary<Int32, PropOpInfo> m_dictPropOpInfo = new Dictionary<Int32, PropOpInfo>();


        [Key(0)]
        public Int64 BirthZoneId
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0, 778);
                return (Int64)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0);
                if (propOpInfo.Value == null || (Int64)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(1)]
        public string Name
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1, "");
                return (string)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1);
                if (propOpInfo.Value == null || (string)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(2)]
        public Int64 Uid
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 2, 0);
                return (Int64)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 2);
                if (propOpInfo.Value == null || (Int64)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(3)]
        public Int64 NewbornFlag
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 3, 0);
                return (Int64)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 3);
                if (propOpInfo.Value == null || (Int64)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(4)]
        public Mos.DBData.DBPlayerDataBasic DBPlayerBasic = new Mos.DBData.DBPlayerDataBasic();

        [Key(5)]
        public Mos.DBData.DBPlayerBag DBPlayerBag = new Mos.DBData.DBPlayerBag();

        [Key(6)]
        public Mos.DBData.DBPlayerTask DBPlayerTask = new Mos.DBData.DBPlayerTask();

        public void OnPropOpLog(Int32 index, object Value)
        {
            PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, index);
            propOpInfo.Value = Value;
        }
    }
}