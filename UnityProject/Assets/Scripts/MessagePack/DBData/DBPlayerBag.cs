using MessagePack;
using System;
using System.Collections.Generic;
using Phoenix.Core.Entity;

namespace Mos.DBData
{
    [MessagePackObject]
    public class DBPlayerBag 
    {
        Dictionary<Int32, PropOpInfo> m_dictPropOpInfo = new Dictionary<Int32, PropOpInfo>();


        [Key(0)]
        public List<Mos.DBData.DBItem> ListItems
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0, new List<Mos.DBData.DBItem>());
                return (List<Mos.DBData.DBItem>)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0);
                if (propOpInfo.Value == null || (List<Mos.DBData.DBItem>)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(1)]
        public Dictionary<String, Mos.DBData.DBItem> DictItems
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1, new Dictionary<String, Mos.DBData.DBItem>());
                return (Dictionary<String, Mos.DBData.DBItem>)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1);
                if (propOpInfo.Value == null || (Dictionary<String, Mos.DBData.DBItem>)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        public void OnPropOpLog(Int32 index, object Value)
        {
            PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, index);
            propOpInfo.Value = Value;
        }
    }
}