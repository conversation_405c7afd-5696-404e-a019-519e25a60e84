using MessagePack;
using System;
using System.Collections.Generic;
using Phoenix.Core.Entity;

namespace Mos.DBData
{
    [MessagePackObject]
    public class DBItem 
    {
        Dictionary<Int32, PropOpInfo> m_dictPropOpInfo = new Dictionary<Int32, PropOpInfo>();


        [Key(0)]
        public Int32 Id
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0, 0);
                return (Int32)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0);
                if (propOpInfo.Value == null || (Int32)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(1)]
        public Int64 Count
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1, 0);
                return (Int64)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1);
                if (propOpInfo.Value == null || (Int64)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        public void OnPropOpLog(Int32 index, object Value)
        {
            PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, index);
            propOpInfo.Value = Value;
        }
    }
}