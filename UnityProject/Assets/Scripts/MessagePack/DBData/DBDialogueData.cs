using MessagePack;
using System;
using System.Collections.Generic;
using Phoenix.Core.Entity;

namespace Mos.DBData
{
    [MessagePackObject]
    public class DBDialogueData 
    {
        Dictionary<Int32, PropOpInfo> m_dictPropOpInfo = new Dictionary<Int32, PropOpInfo>();


        [Key(0)]
        public UInt32 DialogueID
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0, 0);
                return (UInt32)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0);
                if (propOpInfo.Value == null || (UInt32)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(1)]
        public UInt32 DefenceValue
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1, 0);
                return (UInt32)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1);
                if (propOpInfo.Value == null || (UInt32)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(2)]
        public UInt64 OutIndexState
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 2, 0);
                return (UInt64)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 2);
                if (propOpInfo.Value == null || (UInt64)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        public void OnPropOpLog(Int32 index, object Value)
        {
            PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, index);
            propOpInfo.Value = Value;
        }
    }
}