using MessagePack;
using System;
using System.Collections.Generic;
using Phoenix.Core.Entity;

namespace Mos.DBData
{
    [MessagePackObject]
    public class DBStoryData 
    {
        Dictionary<Int32, PropOpInfo> m_dictPropOpInfo = new Dictionary<Int32, PropOpInfo>();


        [Key(0)]
        public UInt32 StoryID
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0, 0);
                return (UInt32)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 0);
                if (propOpInfo.Value == null || (UInt32)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(1)]
        public UInt32 FinishTimes
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1, 0);
                return (UInt32)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 1);
                if (propOpInfo.Value == null || (UInt32)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(2)]
        public UInt32 LastUpdateTime
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 2, 0);
                return (UInt32)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 2);
                if (propOpInfo.Value == null || (UInt32)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(3)]
        public Dictionary<Int32, List<UInt32>> DictCurNodeID
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 3, new Dictionary<Int32, List<UInt32>>());
                return (Dictionary<Int32, List<UInt32>>)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 3);
                if (propOpInfo.Value == null || (Dictionary<Int32, List<UInt32>>)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(4)]
        public Dictionary<UInt32, UInt64> DictTaskTriggerState
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 4, new Dictionary<UInt32, UInt64>());
                return (Dictionary<UInt32, UInt64>)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 4);
                if (propOpInfo.Value == null || (Dictionary<UInt32, UInt64>)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(5)]
        public Dictionary<UInt32, UInt64> DictTaskFinishState
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 5, new Dictionary<UInt32, UInt64>());
                return (Dictionary<UInt32, UInt64>)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 5);
                if (propOpInfo.Value == null || (Dictionary<UInt32, UInt64>)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        [Key(6)]
        public Dictionary<String, UInt64> DictVariablePool
        {
            get 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 6, new Dictionary<String, UInt64>());
                return (Dictionary<String, UInt64>)propOpInfo.Value;
            }
            set 
            {
                PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, 6);
                if (propOpInfo.Value == null || (Dictionary<String, UInt64>)propOpInfo.Value != value)
                {
                    propOpInfo.Value = value;
                }
            }
        }

        public void OnPropOpLog(Int32 index, object Value)
        {
            PropOpInfo propOpInfo = PropOpInfo.GetPropOpInfo(ref m_dictPropOpInfo, index);
            propOpInfo.Value = Value;
        }
    }
}