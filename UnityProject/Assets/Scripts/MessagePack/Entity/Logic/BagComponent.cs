using Mos.DBData;
using Phoenix.MsgPackLogic.Protocol;
using UnityEngine;

namespace Phoenix.Core.Entity
{
    public class BagComponent : EntityComponent
    {
        public GameBasePlayer OwnerEntity { get; private set; }

        public DBPlayerBag LinkData { get; set; }

        public BagComponent(GameBasePlayer ownerEntity) : base(ownerEntity)
        {
            OwnerEntity = ownerEntity;
        }

        public void Init(DBPlayerBag linkData)
        {
            LinkData = linkData;
        }

        public override void OnPropOpLog(MsgPack_PropOpInfo_S2C msgPack_S2C)
        {
            Debug.Log($"{GetComponentName()} OnPropOpLog =====1-2===== {msgPack_S2C.ComponentName}, {msgPack_S2C.KeyIdxClient}");
            LinkData.OnPropOpLog(msgPack_S2C.KeyIdxClient, msgPack_S2C.Value);
        }

        public override string GetComponentName()
        {
            return "Component_Basic";
        }
    }
}

