using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Mos.DBData;

namespace Phoenix.Core.Entity
{
    public class GameBasePlayer : Entity
    {
        private DBPlayerData PlayerData { get; set; }

        public BasicComponent BasicComp { get; private set; }

        public PlayerST OnlineState { get; private set; } = PlayerST.OFFLINE;

        public GameBasePlayer(Int64 entityId = 0) : base(entityId)
        {
            AddTypeTag(EObjectTypeTag.ETAG_BASE_PLAYER);
        }

        public override void CreateComponents()
        {
            BasicComp = new BasicComponent(this);
            AddComponent(BasicComp);
        }

        public override void Init(object data)
        {
            PlayerData = data as DBPlayerData;
            UserId = PlayerData.Uid;

            // todo
            //PlayerData = PlayerData.InitByServerData(PlayerData);

            BasicComp.Init(PlayerData.DBPlayerBasic);
        }

        public void PostInit()
        {
            // EFlagPlayer.EFLag_World
            // EFlagPlayer.EFLag_Space
            Debug.Log($"set player newborn_flag={PlayerData.NewbornFlag.ToString()} uid={PlayerData.Uid}");
        }

        public override void Destroy()
        {
            base.Destroy();
        }

        public override byte[] SerializeData()
        {
            return MessagePack.MessagePackSerializer.Serialize(PlayerData);
        }

        public EntityComponent? GetComponent(string strCompName)
        {
            return m_dictComponent[strCompName];
        }
    }
}

