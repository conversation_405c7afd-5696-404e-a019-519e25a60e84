using Mos.DBData;
using Phoenix.MsgPackLogic.Protocol;
using UnityEngine;

namespace Phoenix.Core.Entity
{
    public class TaskComponent : EntityComponent
    {
        public GameBasePlayer OwnerEntity { get; private set; }

        public DBPlayerTask LinkData { get; set; }

        public TaskComponent(GameBasePlayer ownerEntity) : base(ownerEntity)
        {
            OwnerEntity = ownerEntity;
        }

        public void Init(DBPlayerTask linkData)
        {
            LinkData = linkData;
        }

        public override void OnPropOpLog(MsgPack_PropOpInfo_S2C msgPack_S2C)
        {
            Debug.Log($"{GetComponentName()} OnPropOpLog =====1-2===== {msgPack_S2C.ComponentName}, {msgPack_S2C.KeyIdxClient}");
            LinkData.OnPropOpLog(msgPack_S2C.KeyIdxClient, msgPack_S2C.Value);
            Debug.Log($"{GetComponentName()} OnPropOpLog =====1-3===== {msgPack_S2C.ComponentName}, {msgPack_S2C.KeyIdxClient}");
        }

        public override string GetComponentName()
        {
            return "Component_Task";
        }
    }
}
