using MessagePack;
using UnityEngine;
using Mos.DBData;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Core.Entity
{
    public class BasicComponent : EntityComponent
    {
        public GameBasePlayer OwnerEntity { get; private set; }

        public DBPlayerDataBasic LinkData { get; set; }

        public BasicComponent(GameBasePlayer ownerEntity) : base(ownerEntity)
        {
            OwnerEntity = ownerEntity;
        }

        public void Init(DBPlayerDataBasic linkData)
        {
            LinkData = linkData;
        }

        public override void OnPropOpLog(MsgPack_PropOpInfo_S2C msgPack_S2C)
        {
            Debug.Log($"{GetComponentName()} OnPropOpLog =====1-2===== {msgPack_S2C.ComponentName}, {msgPack_S2C.KeyIdxClient}, {LinkData.Exp}");
            LinkData.OnPropOpLog(msgPack_S2C.KeyIdxClient, msgPack_S2C.Value);
            Debug.Log($"{GetComponentName()} OnPropOpLog =====1-3===== {msgPack_S2C.ComponentName}, {msgPack_S2C.KeyIdxClient}, {LinkData.Exp}");
        }

        public override string GetComponentName()
        {
            return "Component_Basic";
        }

        public void TestRpcMethodFromServer()
        {
            Debug.Log("TestRpcMethodFromServer =====1-1=====");
        }

        public void TestRpcMethodFromServerWithParams(byte[]? packedArgs)
        {
            string strArgs = string.Empty;
            if (packedArgs != null)
            {
                strArgs = MessagePackSerializer.Deserialize<string>(packedArgs);
            }
            Debug.Log($"TestRpcMethodFromServerWithParams =====1-2===== {strArgs}");
        }
    }
}

