using System;
using System.Collections.Generic;

namespace Phoenix.Core.Entity
{
    public class PropOpInfo
    {
        public Int32 KeyIdx { get; set; }

        public object Value { get; set; }

        public Boolean IsChanged { get; set; }

        public static PropOpInfo GetPropOpInfo(ref Dictionary<Int32, PropOpInfo> dictPropOpInfo, Int32 KeyIndex, object defaultValue = null)
        {
            PropOpInfo propOpInfo = null;
            if (!dictPropOpInfo.TryGetValue(KeyIndex, out propOpInfo))
            {
                propOpInfo = new PropOpInfo();
                propOpInfo.KeyIdx = KeyIndex;
                propOpInfo.Value = defaultValue;
                dictPropOpInfo.Add(KeyIndex, propOpInfo);
            }
            return propOpInfo;
        }
    }
}
