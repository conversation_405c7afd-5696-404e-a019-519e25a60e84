using Phoenix.MsgPackLogic.Protocol;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core.Entity
{
    public abstract class EntityComponent
    {
        public EntityComponent(Entity entity)
        {
            Entity = entity;
        }

        public abstract string GetComponentName();

        public Entity? Entity { get; set; }

        public virtual void OnPropOpLog(MsgPack_PropOpInfo_S2C msgPack_S2C)
        {
            Debug.Log($"MosComponent OnPropOpLog =====1-1===== {msgPack_S2C.ComponentName}, {msgPack_S2C.KeyIdxClient}");
        }

        private static void RegisterComponentName<T>(string name) where T : EntityComponent
        {
            ComponentNameToType[name] = typeof(T);
            TypeToComponentName[typeof(T)] = name;
        }

        public static string? GetComponentName(Type type)
        {
            return TypeToComponentName.TryGetValue(type, out var name) ? name : null;
        }

        public virtual void PreDestroy()
        {

        }

        public virtual void OnDestroy()
        {

        }

        private static Dictionary<string, Type> ComponentNameToType = new Dictionary<string, Type>();
        private static Dictionary<Type, string> TypeToComponentName = new Dictionary<Type, string>();
    }
}

