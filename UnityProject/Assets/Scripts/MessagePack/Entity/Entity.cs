using System;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core.Entity
{
    public class Entity
    {
        public Int64 EntityId { get; private set; }

        public Int64 UserId { get; set; }

        public long TypeTag { get; set; }

        public EntityState ENTState { get; set; }

        private List<EntityComponent> m_listComponents = new();
        protected Dictionary<string, EntityComponent> m_dictComponent = new();

        public Entity(Int64 entityId = 0)
        {
            EntityId = entityId;

            CreateComponents();
        }

        public virtual void Init(object data) { }

        public virtual byte[] SerializeData() { return null; }

        public virtual void CreateComponents() { }

        protected void AddComponent(EntityComponent comp)
        {
            m_listComponents.Add(comp);
            m_dictComponent.Add(comp.GetComponentName(), comp);
        }

        public void AddTypeTag(EObjectTypeTag typeTag)
        {
            TypeTag |= (Int64)typeTag;
        }

        public bool IsBasePlayer()
        {
            return (TypeTag & (Int64)EObjectTypeTag.ETAG_BASE_PLAYER) != 0;
        }

        public virtual void Destroy()
        {
            if (IsDestroy())
            {
                Debug.LogError($"Entity already destroyed eid = {EntityId}");
                return;
            }

            /*SyncComponent.flush_dirty_prop();
            enable_prop_persist(false);
            enable_prop_sync(false);
            ent_state = EntityState.ENT_ST_DESTROYING;

            EventPreDestroy.Invoke();
            pre_destroy();

            // unregister mailbox
            if (string.IsNullOrEmpty(mailbox_name))
            {
                LogicSystem.RpcMgr.unregister_mailbox(mailbox_name);
            }

            // clear all pending timer in current entity instance
            EntityAdmin.Instance.entityTimer.clear_object_timer(EntityId);
            if(!is_migrating && !is_ghost)
            {
                persist
            }*/
        }

        public bool IsDestroy()
        {
            return ENTState == EntityState.ENT_ST_DESTROYING || ENTState == EntityState.ENT_ST_DESTROYED;
        }
        
    }
}

