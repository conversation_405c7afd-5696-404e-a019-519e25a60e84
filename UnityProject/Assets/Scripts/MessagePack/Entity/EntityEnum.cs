using System;

public enum EObjectTypeTag : Int64
{
    // fast type casting ( 1 ~ 16bit)
    ETAG_ENTITY = 1L,
    ETAG_BASE_PLAYER = 1L << 2,
    ETAG_ACTOR = 1L << 3,
    ETAG_SPACE_PLAYER = 1L << 4,
    ETAG_COMPONENT = 1L << 5,

    // Object Ability tag (from 16bit)
    ETAG_MOVABLE = 1L << 15,
};

public enum EntityState
{
    ENT_ST_PRE_INIT = 0,   // python object instance created, but not init
    ENT_ST_DATA_INITED = 1, // data async load is done
    ENT_ST_INITED = 2,       // component, dependent objects init done
    ENT_ST_DESTROYING = 3,  // destroying, waiting async persist done and other cleanup work
    ENT_ST_DESTROYED = 4,   // destoyred, not allow any gameplay logic work and property modification.
    ENG_ST_RECOVER = 5,    // failover recover status
};

public enum  EFlagPlayer
{
    EFLag_World = 0,
    EFLag_Space = 1,
}

public enum PlayerST
{
    // Player is offline
    OFFLINE = 1,

    // Player is loading data from the database in progress
    LOADING = 2,

    // Deprecated state in world_router, a loaded state but not online is not needed anymore,
    // it will be regarded as ONLINE. Set from loading -> online in latest login process
    LOADED = 3,

    // Player is loaded and regarded as activated in server
    ONLINE = 4,

    // Client requests login from another gate while player is online in server
    REBINDING = 5,

    // Player is being destroyed, maybe doing persisting and cleanup work
    BEING_DESTROYED = 6,

    // Player object is migrating between space nodes in progress
    MIGRATING = 7
}
