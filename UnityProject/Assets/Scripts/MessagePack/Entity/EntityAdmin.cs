using Mos.MsgPackLogic;
using Mos.MsgPackLogic.Protocol;
using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;

namespace Phoenix.Core.Entity
{
    public class EntityAdmin : Singleton<EntityAdmin>
    {
        protected override void OnInit()
        {
            base.OnInit();
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
        }

        public Dictionary<Int64, Entity> Entities = new();
        public Dictionary<Int64, Entity> BasePlayerEntities = new();

        public void AddEntity(Entity entity, bool isSelf = false)
        {
            if (Entities.TryGetValue(entity.EntityId, out var oldEntity))
            {
                if (oldEntity == entity)
                {
                    return;
                }
                else
                {
                    DelEntity(oldEntity);
                    Debug.LogError($"Entity already exists remove old eid={entity.EntityId}");
                }
            }

            Entities[entity.EntityId] = entity;
            if (entity.UserId > 0)
            {
                BasePlayerEntities[entity.UserId] = entity;
            }

            if (isSelf)
            {
                CurEntity = entity;
            }

            Debug.Log($"AddEntity eid={entity.EntityId} uid={entity.UserId}");
        }

        public void DelEntity(Entity entity)
        {
            if (entity == null)
            {
                return;
            }

            Entities.Remove(entity.EntityId);
            if (entity.UserId > 0)
            {
                BasePlayerEntities.Remove(entity.UserId);
            }
            if (CurEntity != null && entity.UserId == CurEntity.UserId)
            {
                CurEntity = null;
            }
        }

        public Entity? GetEntity(long entityId)
        {
            return Entities.GetValueOrDefault(entityId);
        }

        public GameBasePlayer? GetBasePlayer(long uid)
        {
            BasePlayerEntities.TryGetValue(uid, out var entity);
            return entity as GameBasePlayer;
        }

        public GameBasePlayer? GetCurBasePlayer()
        {
            if (CurEntity == null)
            {
                return null;
            }
            return CurEntity as GameBasePlayer;
        }

        public Entity CurEntity { get; private set; } = null;
    }

}
