using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Phoenix.LOS2D
{
    [CustomEditor(typeof(LOSMesh))]
    public class LOSMeshEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            LOSMesh m = target as LOSMesh;

            if (!Application.isPlaying)
            {
                EditorGUILayout.PropertyField(serializedObject.FindProperty("meshComp"));
            }

            GUIStyle labelBold = new GUIStyle("label") { fontStyle = FontStyle.Bold };
            EditorGUILayout.LabelField("Mesh Parameters", labelBold);

            EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.resolution)));

            {
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.defaultFOV)));
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.defaultMaxDist)));
            }

            EditorGUILayout.LabelField("Mesh Display", labelBold);

            SerializedProperty drawSimpleSector = serializedObject.FindProperty(nameof(m.drawSimpleSector));

            EditorGUILayout.PropertyField(drawSimpleSector);

            if (!drawSimpleSector.boolValue)
            {
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.drawHidden)));
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.drawSight)));
            }

            EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.fillUV)));

            EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(m.autoRegenerateMesh)));

            serializedObject.ApplyModifiedProperties();

            if (GUILayout.Button("RegenerateMesh"))
            {
                (target as LOSMesh).RegenerateMesh();
            }
        }
    }
}
