using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Phoenix.LOS2D
{
    public class LOSSource : MonoBehaviour
    {
        [Range(0, 180)]
        public float fov = 60f;

        [Range(0, 100)]
        public float maxDist = 10f;

        public LayerMask maskForEvent = 1;
        //public LayerMask maskForRender = 1;

        public float eyeHeight = 0.5f;

        [Header("LOS Mesh Settings")]
        public GameObject losMeshPrefab;

        private GameObject losMeshInstance;
        private LOSMesh losMeshComponent;

        private void OnEnable()
        {
            LOSManager.Register(this);

            // Runtime时自动生成LOS Mesh
            if (Application.isPlaying)
            {
                EnsureLOSMeshExists();
            }
        }

        private void OnDisable()
        {
            LOSManager.Unregister(this);
        }

        private void OnDestroy()
        {
            if (losMeshInstance != null)
            {
                GameObject.Destroy(losMeshInstance);
                losMeshInstance = null;
            }
        }

        private void Start()
        {
            // 确保在Start时也检查LOS Mesh
            if (Application.isPlaying)
            {
                EnsureLOSMeshExists();
            }
        }

        /// <summary>
        /// 确保LOS Mesh存在，如果不存在则自动生成
        /// </summary>
        private void EnsureLOSMeshExists()
        {
            if (losMeshInstance == null)
            {
                // 先尝试在子节点中查找现有的LOS Mesh
                losMeshInstance = FindLOSMeshInChildren();

                if (losMeshInstance == null && losMeshPrefab != null)
                {
                    // 如果没找到且有预制体，则生成新的
                    GenerateLOSMesh();
                }
            }

            // 更新LOS Mesh的参数
            UpdateLOSMeshParameters();
        }

        /// <summary>
        /// 在子节点中查找LOS Mesh
        /// </summary>
        private GameObject FindLOSMeshInChildren()
        {
            for (int i = 0; i < transform.childCount; i++)
            {
                Transform child = transform.GetChild(i);
                if (child.TryGetComponent<LOSMesh>(out var meshComponent))
                {
                    losMeshComponent = meshComponent;
                    return child.gameObject;
                }
            }
            return null;
        }

        /// <summary>
        /// 生成LOS Mesh
        /// </summary>
        public void GenerateLOSMesh()
        {
            if (losMeshPrefab == null)
            {
                Debug.LogWarning($"LOSSource '{name}': LOS Mesh Prefab is not assigned!");
                return;
            }


            // 生成新的LOS Mesh实例
            if (losMeshInstance == null)
            {
#if UNITY_EDITOR
                losMeshInstance = PrefabUtility.InstantiatePrefab(losMeshPrefab, transform) as GameObject;
#else
                losMeshInstance = Instantiate(losMeshPrefab, transform);
#endif                
            }

            if (losMeshInstance != null)
            {
                losMeshInstance.name = "LOS Mesh";
                losMeshComponent = losMeshInstance.GetComponent<LOSMesh>();
                //Y如果一样会导致mesh叠在一起闪烁
                losMeshInstance.transform.localPosition = new Vector3(0.0f, Random.Range(0.05f, 0.15f), 0.0f);

                if (losMeshComponent == null)
                {
                    Debug.LogWarning($"LOSSource '{name}': The LOS Mesh Prefab doesn't have LOSMesh component!");
                    return;
                }

                // 设置参数
                UpdateLOSMeshParameters();

                Debug.Log($"LOSSource '{name}': LOS Mesh generated successfully.");
            }
        }

        /// <summary>
        /// 更新LOS Mesh的参数
        /// </summary>
        private void UpdateLOSMeshParameters()
        {
            if (losMeshComponent != null)
            {
                losMeshComponent.defaultFOV = fov;
                losMeshComponent.defaultMaxDist = maxDist;

                // 触发网格重新生成
                if (!Application.isPlaying && losMeshComponent.autoRegenerateMesh)
                {
                    losMeshComponent.RegenerateMesh();
                }
            }
        }

        /// <summary>
        /// 当参数改变时更新LOS Mesh
        /// </summary>
        private void OnValidate()
        {
            if (!Application.isPlaying && losMeshComponent != null)
            {
                UpdateLOSMeshParameters();
            }
        }

#if UNITY_EDITOR
        /// <summary>
        /// 编辑器中的生成LOS Mesh按钮
        /// </summary>
        [ContextMenu("Generate LOS Mesh")]
        public void GenerateLOSMeshEditor()
        {
            GenerateLOSMesh();
            EditorUtility.SetDirty(this);
        }
#endif
    }
}
