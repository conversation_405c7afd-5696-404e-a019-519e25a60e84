using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using UnityEngine;

namespace Phoenix.LOS2D
{
    /// <summary>
    /// LOS (Line of Sight) 视线检测管理器
    /// 负责管理所有LOSSource和LOSTarget之间的视线检测
    /// 支持性能优化和调试功能
    /// </summary>
    public class LOSManager : MonoBehaviour
    {
        #region 内部数据结构

        /// <summary>
        /// Source-Target配对结构，用于缓存视线状态
        /// </summary>
        private readonly struct SourceTargetPair : IEquatable<SourceTargetPair>
        {
            public readonly LOSSource source;
            public readonly LOSTarget target;

            public SourceTargetPair(LOSSource source, LOSTarget target)
            {
                this.source = source;
                this.target = target;
            }

            public bool Equals(SourceTargetPair other)
            {
                return source == other.source && target == other.target;
            }

            public override bool Equals(object obj)
            {
                return obj is SourceTargetPair other && Equals(other);
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(source, target);
            }
        }

        /// <summary>
        /// 性能配置参数
        /// </summary>
        [System.Serializable]
        public class PerformanceSettings
        {
            [Header("更新频率设置")]
            [Range(1f, 60f)]
            [Tooltip("每秒更新次数")]
            public float updateFrequency = 10f;

            [Range(1, 10)]
            [Tooltip("每帧处理的Source数量")]
            public int sourcesPerFrame = 2;
        }

        /// <summary>
        /// 调试配置参数
        /// </summary>
        [System.Serializable]
        public class DebugSettings
        {
            [Header("调试日志")]
            [Tooltip("启用调试日志")]
            public bool enableDebugLog = false;

            [Tooltip("记录扇形检查(含距离)")]
            public bool logSectorCheck = true;

            [Tooltip("记录射线检查")]
            public bool logRaycastCheck = true;

            [Tooltip("记录分帧处理")]
            public bool logFrameProcessing = false;
        }

        #endregion

        #region 单例模式

        private static LOSManager instance;

        /// <summary>
        /// 获取LOSManager实例
        /// </summary>
        internal static LOSManager Instance
        {
            get
            {
                EnsureInstance();
                return instance;
            }
        }

        /// <summary>
        /// 确保实例存在
        /// </summary>
        private static void EnsureInstance()
        {
            if (instance == null)
            {
                var go = new GameObject($"[{nameof(LOSManager)}]");
                instance = go.AddComponent<LOSManager>();
                DontDestroyOnLoad(go);
            }
        }

        private void Awake()
        {
            if (instance != null && instance != this)
            {
                Destroy(this);
            }
            else
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
        }

        #endregion

        #region 静态数据和事件

        /// <summary>
        /// 默认渲染遮罩
        /// </summary>
        public static LayerMask defaultMaskForRender = 1;

        /// <summary>
        /// 缓存的视线状态配对
        /// </summary>
        private static readonly HashSet<SourceTargetPair> pairsInSight = new();

        /// <summary>
        /// 注册的所有LOSSource
        /// </summary>
        private static readonly HashSet<LOSSource> sources = new();

        /// <summary>
        /// 注册的所有LOSTarget
        /// </summary>
        private static readonly HashSet<LOSTarget> targets = new();

        /// <summary>
        /// 当Source看到Target时触发
        /// </summary>
        public static event Action<LOSSource, LOSTarget> OnEnter;

        /// <summary>
        /// 当Source失去Target视线时触发
        /// </summary>
        public static event Action<LOSSource, LOSTarget> OnExit;

        #endregion

        #region 公共API

        /// <summary>
        /// 检查Source是否能看到Target
        /// </summary>
        public static bool IsInSight(LOSSource source, LOSTarget target)
        {
            if (source == null || target == null)
                return false;

            EnsureInstance();
            return pairsInSight.Contains(new SourceTargetPair(source, target));
        }

        /// <summary>
        /// 获取Source能看到的所有Target
        /// </summary>
        public static LOSTarget[] GetTargetsInSight(LOSSource source)
        {
            if (source == null)
                return Array.Empty<LOSTarget>();

            EnsureInstance();
            return pairsInSight.Where(pair => pair.source == source).Select(p => p.target).ToArray();
        }

        /// <summary>
        /// 获取能看到Target的所有Source
        /// </summary>
        public static LOSSource[] GetSourcesHaveSightOf(LOSTarget target)
        {
            if (target == null)
                return Array.Empty<LOSSource>();

            EnsureInstance();
            return pairsInSight.Where(pair => pair.target == target).Select(p => p.source).ToArray();
        }

        #endregion

        #region 注册管理

        /// <summary>
        /// 注册LOSSource
        /// </summary>
        internal static bool Register(LOSSource source)
        {
            EnsureInstance();
            return sources.Add(source);
        }

        /// <summary>
        /// 注销LOSSource
        /// </summary>
        internal static bool Unregister(LOSSource source)
        {
            if (sources.Remove(source))
            {
                pairsInSight.RemoveWhere(p => p.source == source);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 注册LOSTarget
        /// </summary>
        internal static bool Register(LOSTarget target)
        {
            EnsureInstance();
            return targets.Add(target);
        }

        /// <summary>
        /// 注销LOSTarget
        /// </summary>
        internal static bool Unregister(LOSTarget target)
        {
            if (targets.Remove(target))
            {
                pairsInSight.RemoveWhere(p => p.target == target);
                return true;
            }
            return false;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 检查并触发视线状态变化事件
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static void CheckAndFireEvent(LOSSource source, LOSTarget target, bool inSight)
        {
            var pair = new SourceTargetPair(source, target);
            bool wasInSight = pairsInSight.Contains(pair);

            if (wasInSight == inSight)
                return;

            if (inSight)
            {
                pairsInSight.Add(pair);
                OnEnter?.Invoke(source, target);
            }
            else
            {
                pairsInSight.Remove(pair);
                OnExit?.Invoke(source, target);
            }
        }

        #endregion

        #region 实例配置和状态

        [Header("性能优化设置")]
        [SerializeField] private PerformanceSettings performanceSettings = new();

        [Header("调试设置")]
        [SerializeField] private DebugSettings debugSettings = new();

        /// <summary>
        /// 处理缓冲区
        /// </summary>
        private readonly List<LOSSource> sourceBuffer = new();
        private readonly List<LOSTarget> targetBuffer = new();

        /// <summary>
        /// 分帧处理状态
        /// </summary>
        private float updateTimer = 0f;
        private int currentSourceIndex = 0;
        private bool isProcessing = false;

        /// <summary>
        /// 调试统计数据
        /// </summary>
        private readonly DebugStats debugStats = new();

        /// <summary>
        /// 调试统计结构
        /// </summary>
        private class DebugStats
        {
            public int frameProcessedSources = 0;
            public int totalSectorChecks = 0;
            public int passedSectorChecks = 0;
            public int totalRaycastChecks = 0;
            public int passedRaycastChecks = 0;

            public void Reset()
            {
                frameProcessedSources = 0;
                totalSectorChecks = 0;
                passedSectorChecks = 0;
                totalRaycastChecks = 0;
                passedRaycastChecks = 0;
            }
        }

        #endregion

        #region 主要更新逻辑

        /// <summary>
        /// 主更新循环
        /// </summary>
        private void Update()
        {
            // 按频率更新
            updateTimer += Time.deltaTime;
            if (updateTimer < 1f / performanceSettings.updateFrequency)
                return;

            updateTimer = 0f;

            // 开始新的检测周期
            if (!isProcessing)
            {
                StartNewDetectionCycle();
            }

            // 分帧处理source
            ProcessSourcesThisFrame();
        }

        /// <summary>
        /// 开始新的检测周期
        /// </summary>
        private void StartNewDetectionCycle()
        {
            sourceBuffer.Clear();
            targetBuffer.Clear();
            sourceBuffer.AddRange(sources);
            targetBuffer.AddRange(targets);
            currentSourceIndex = 0;
            isProcessing = true;

            // 重置调试统计
            if (debugSettings.enableDebugLog)
            {
                debugStats.Reset();

                if (debugSettings.logFrameProcessing)
                {
                    Debug.Log($"[LOSManager] 开始新的检测周期 - Sources: {sourceBuffer.Count}, Targets: {targetBuffer.Count}, 频率: {performanceSettings.updateFrequency}Hz");
                }
            }
        }

        /// <summary>
        /// 分帧处理Sources
        /// </summary>
        private void ProcessSourcesThisFrame()
        {
            if (sourceBuffer.Count == 0 || targetBuffer.Count == 0)
            {
                isProcessing = false;
                return;
            }

            int processedCount = 0;

            while (processedCount < performanceSettings.sourcesPerFrame && currentSourceIndex < sourceBuffer.Count)
            {
                var source = sourceBuffer[currentSourceIndex];
                if (source != null)
                {
                    if (debugSettings.enableDebugLog && debugSettings.logFrameProcessing)
                    {
                        Debug.Log($"[LOSManager] 处理Source [{currentSourceIndex}]: {source.name}");
                    }

                    ProcessSourceTargets(source);
                    debugStats.frameProcessedSources++;
                }

                currentSourceIndex++;
                processedCount++;
            }

            // 检查是否完成了所有source的处理
            if (currentSourceIndex >= sourceBuffer.Count)
            {
                CompleteDetectionCycle();
            }
        }

        /// <summary>
        /// 完成检测周期
        /// </summary>
        private void CompleteDetectionCycle()
        {
            isProcessing = false;

            // 输出本轮统计信息
            if (debugSettings.enableDebugLog && debugSettings.logFrameProcessing)
            {
                Debug.Log($"[LOSManager] 检测周期完成 - 处理Sources: {debugStats.frameProcessedSources}, " +
                         $"扇形检查: {debugStats.passedSectorChecks}/{debugStats.totalSectorChecks}, " +
                         $"射线检查: {debugStats.passedRaycastChecks}/{debugStats.totalRaycastChecks}");
            }

            sourceBuffer.Clear();
            targetBuffer.Clear();
        }

        /// <summary>
        /// 处理单个Source对所有Target的检测
        /// </summary>
        private void ProcessSourceTargets(LOSSource source)
        {
            Vector3 sourcePos = source.transform.position;

            foreach (var target in targetBuffer)
            {
                if (target == null) continue;

                bool inSight = PerformLineOfSightCheck(source, target, sourcePos, out string debugInfo);

                // 输出调试信息
                if (debugSettings.enableDebugLog && !string.IsNullOrEmpty(debugInfo))
                {
                    string result = inSight ? "可见" : "不可见";
                    Debug.Log($"[LOSManager] {source.name} -> {target.name}: {debugInfo} = {result}");
                }

                CheckAndFireEvent(source, target, inSight);
            }
        }

        /// <summary>
        /// 执行视线检测
        /// </summary>
        private bool PerformLineOfSightCheck(LOSSource source, LOSTarget target, Vector3 sourcePos, out string debugInfo)
        {
            debugInfo = "";
            Vector3 targetPos = target.transform.position;
            debugStats.totalSectorChecks++;

            // 扇形检查（包含距离检查）
            if (InSector(target, source))
            {
                debugStats.passedSectorChecks++;

                if (debugSettings.enableDebugLog && debugSettings.logSectorCheck)
                {
                    float distance = Vector3.Distance(sourcePos, targetPos);
                    debugInfo += $"扇形检查(含距离): {distance:F2}m <= {source.maxDist:F2}m, FOV: {source.fov}° [通过]";
                }

                // 射线检测
                return PerformRaycastCheck(source, target, sourcePos, targetPos, ref debugInfo);
            }
            else
            {
                if (debugSettings.enableDebugLog && debugSettings.logSectorCheck)
                {
                    float distance = Vector3.Distance(sourcePos, targetPos);
                    debugInfo += $"扇形检查(含距离): {distance:F2}m > {source.maxDist:F2}m 或不在FOV: {source.fov}° [失败]";
                }
                return false;
            }
        }

        /// <summary>
        /// 执行射线检测
        /// </summary>
        private bool PerformRaycastCheck(LOSSource source, LOSTarget target, Vector3 sourcePos, Vector3 targetPos, ref string debugInfo)
        {
            Vector3 sPos = sourcePos;
            Vector3 dir = targetPos - sPos;
            sPos.y += source.eyeHeight;
            var ray = new Ray(sPos, dir);

            debugStats.totalRaycastChecks++;

            if (Physics.Raycast(ray, out RaycastHit hit, source.maxDist))
            {
                if (hit.transform.IsChildOf(target.transform))
                {
                    debugStats.passedRaycastChecks++;

                    if (debugSettings.enableDebugLog && debugSettings.logRaycastCheck)
                    {
                        debugInfo += $" -> 射线检查: 命中{hit.transform.name} [通过]";
                    }
                    return true;
                }
                else
                {
                    if (debugSettings.enableDebugLog && debugSettings.logRaycastCheck)
                    {
                        debugInfo += $" -> 射线检查: 命中{hit.transform.name} [阻挡]";
                    }
                    return false;
                }
            }
            else
            {
                if (debugSettings.enableDebugLog && debugSettings.logRaycastCheck)
                {
                    debugInfo += " -> 射线检查: 无命中 [失败]";
                }
                return false;
            }
        }

        #endregion

        #region 调试API

        /// <summary>
        /// 设置调试日志开关
        /// </summary>
        /// <param name="enable">是否启用调试日志</param>
        /// <param name="logSector">记录扇形检查(含距离)</param>
        /// <param name="logRaycast">记录射线检查</param>
        /// <param name="logFrame">记录分帧处理</param>
        public static void SetDebugLog(bool enable, bool logSector = true, bool logRaycast = true, bool logFrame = true)
        {
            if (instance != null)
            {
                instance.debugSettings.enableDebugLog = enable;
                instance.debugSettings.logSectorCheck = logSector;
                instance.debugSettings.logRaycastCheck = logRaycast;
                instance.debugSettings.logFrameProcessing = logFrame;

                Debug.Log($"[LOSManager] 调试日志已{(enable ? "启用" : "禁用")} - 扇形:{logSector}, 射线:{logRaycast}, 分帧:{logFrame}");
            }
        }

        /// <summary>
        /// 获取当前性能统计信息
        /// </summary>
        public static string GetPerformanceStats()
        {
            if (instance == null) return "LOSManager未初始化";

            return $"LOSManager性能统计:\n" +
                   $"- 更新频率: {instance.performanceSettings.updateFrequency}Hz\n" +
                   $"- 每帧处理Sources: {instance.performanceSettings.sourcesPerFrame}\n" +
                   $"- 总Sources: {sources.Count}\n" +
                   $"- 总Targets: {targets.Count}\n" +
                   $"- 扇形检查(含距离): {instance.debugStats.passedSectorChecks}/{instance.debugStats.totalSectorChecks}\n" +
                   $"- 射线检查: {instance.debugStats.passedRaycastChecks}/{instance.debugStats.totalRaycastChecks}";
        }

        #endregion

        #region 几何检测算法

        /// <summary>
        /// 检查目标是否在源的扇形视野内（包含距离和角度检查）
        /// </summary>
        /// <param name="target">目标</param>
        /// <param name="source">源</param>
        /// <param name="heightTolerance">高度容差</param>
        /// <returns>是否在扇形内</returns>
        private static bool InSector(LOSTarget target, LOSSource source, float heightTolerance = 1f)
        {
            Vector3 targetPos = target.transform.position;
            Vector3 sourcePos = source.transform.position;

            // 高度检查
            if (Mathf.Abs(targetPos.y - sourcePos.y) > heightTolerance)
                return false;

            // 距离检查（使用平方距离避免开方运算）
            var targetPos2D = new Vector2(targetPos.x, targetPos.z);
            var sourcePos2D = new Vector2(sourcePos.x, sourcePos.z);
            float maxDistSqr = source.maxDist * source.maxDist;

            if (Vector2.SqrMagnitude(targetPos2D - sourcePos2D) > maxDistSqr)
                return false;

            // 角度检查
            Vector2 dirToTarget = targetPos2D - sourcePos2D;
            Vector3 sourceForward = source.transform.forward;
            var sourceForward2D = new Vector2(sourceForward.x, sourceForward.z);
            float halfFov = source.fov * 0.5f;

            return Vector2.Angle(dirToTarget, sourceForward2D) <= halfFov;
        }

        #endregion
    }
}
