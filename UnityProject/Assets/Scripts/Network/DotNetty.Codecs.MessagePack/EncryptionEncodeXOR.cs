// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Phoenix.Common.Network;

namespace Codecs.MessagePack
{
    public class EncryptionEncodeXOR : MessageToMessageEncoder<IByteBuffer>
    {
        protected override void Encode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
        {
            try
            {
                Int32 protoCode = message.ReadInt();
                Int32 iOffset = message.ArrayOffset + message.ReaderIndex;
                byte xorCode = NetworkClient.instance.XORCode;
                // UnityEngine.Debug.Log($"Encode protoCode = {protoCode}, xorCode = {xorCode}");
                for (int i = 0; i < message.ReadableBytes; i++)
                {
                    message.Array[iOffset + i] ^= xorCode;
                }
                message.SetReaderIndex(0);
                output!.Add(message.Retain());
            }
            catch (Exception e)
            {
                throw new CodecException(e);
            }
        }
    }
}


