// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Phoenix.Common.Network;

namespace Codecs.MessagePack
{
    public class EncryptionDecodeXOR : MessageToMessageDecoder<IByteBuffer>
    {
        protected override void Decode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
        {
            try
            {
                Int32 iOffset = message.ArrayOffset + message.ReaderIndex;
                byte xorCode = NetworkClient.instance.XORCode;
                // UnityEngine.Debug.Log($"Decode xorCode = {xorCode}");
                for (int i = 0; i < message.ReadableBytes; i++)
                {
                    message.Array[iOffset + i] ^= xorCode;
                }
                output!.Add(message.Retain());
            }
            catch (Exception e)
            {
                throw new CodecException(e);
            }
        }
    }
}


