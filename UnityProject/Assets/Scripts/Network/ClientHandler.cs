using Cysharp.Threading.Tasks;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using MessagePack;
using Phoenix.MsgPackLogic.Protocol;
using System;
using System.Net;
using UnityEngine;

namespace Phoenix.Common.Network
{
    public class ClientHandler : ChannelHandlerAdapter
    {
        private readonly NetworkMessageQueue<MsgPackStructBase> _messageQueue;
        // 定义断线事件
        public event Action OnDisconnected;
        public event Action OnConnected;

        public ClientHandler(NetworkMessageQueue<MsgPackStructBase> messageQueue)
        {
            _messageQueue = messageQueue;
        }

        public override void ChannelRead(IChannelHandlerContext context, object message)
        {
            if (message is MsgPackStructBase mes)
            {
                _messageQueue.Push(mes);
            }                
        }

        public override void ChannelInactive(IChannelHandlerContext context)
        {
            Debug.Log("连接已断开，尝试重连...");
            OnDisconnected?.Invoke(); // 触发断线事件
        }

        public override void ChannelActive(IChannelHandlerContext context)
        {
            Debug.Log("连接已完成");
            OnConnected?.Invoke(); // 触发断线事件
        }

        public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
        {
            Debug.Log($"Exception: {exception}");
            context.CloseAsync();
        }
    }
}