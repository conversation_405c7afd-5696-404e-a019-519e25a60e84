using Cysharp.Threading.Tasks;
using Phoenix.MsgPackLogic.Protocol;
using System;
using UnityEngine;

namespace Phoenix.Common.Network
{
    public partial class NetworkTaskBase
    {
        public enum NetworkTaskState
        {
            Idle,           // 任务未开始
            Running,        // 任务正在运行
            Completed,      // 任务成功完成
            Faulted,        // 任务出错
            Reconnecting    // 正在尝试重连
        }

        private Action<MsgPackStructBase> _callback;
        private readonly MsgPackStructBase _sendMsg;
        private readonly NetworkClient _client;
        private readonly int _maxRetryCount;

        private NetworkTaskState _state;
        private MsgPackStructBase _responseMsg;
        private UniTaskCompletionSource<bool> _sendCompletionSource;

        public NetworkTaskState State
        {
            get => _state;
            private set
            {
                _state = value;
            }
        }

        public MsgPackStructBase ResponseMsg => _responseMsg;

        public NetworkTaskBase(
            MsgPackStructBase msgPackStructBase,
            int maxRetryCount = 3)
        {
            _sendMsg = msgPackStructBase ?? throw new ArgumentNullException(nameof(msgPackStructBase));
            _maxRetryCount = maxRetryCount > 0 ? maxRetryCount : throw new ArgumentOutOfRangeException(nameof(maxRetryCount));
            _client = NetworkClient.instance;
            State = NetworkTaskState.Idle; // 初始状态为 Idle
        }

        /// <summary>
        /// 阻塞调用，await后通过ResponeMsg获取服务器返回的数据
        /// </summary>
        /// <returns></returns>
        public async UniTask<bool> SendAndReceiveAsync(double timeout=15.0)
        {
            if (State == NetworkTaskState.Running)
            {
                Debug.LogError("[NetworkTaskBase] Task is already running.");
                return false;
            }

            try
            {
                _sendCompletionSource = new UniTaskCompletionSource<bool>();
                State = NetworkTaskState.Running;

                bool sendSuccess = await SendInternalAsync();
                if (!sendSuccess)
                {
                    State = NetworkTaskState.Faulted;
                    Debug.LogError("[NetworkTaskBase] SendAsync failed.");
                    return false;
                }

                bool respSuccess = await _sendCompletionSource.Task.Timeout(TimeSpan.FromSeconds(timeout)); ;
                if (respSuccess)
                {
                    State = NetworkTaskState.Completed;
                    Debug.Log("[NetworkTaskBase] SendAsync completed successfully.");
                    return true;
                }
                else
                {
                    State = NetworkTaskState.Faulted;
                    Debug.LogError("[NetworkTaskBase] SendAsync Timeout Task failed.");
                    return false;
                }
            }
            catch (TimeoutException)
            {
                // 超时处理逻辑
                State = NetworkTaskState.Faulted;
                Debug.LogError($"[NetworkTaskBase] Send operation timed out after {timeout} seconds.");
                return false;
            }
            catch (Exception ex)
            {
                // 其他异常处理逻辑
                State = NetworkTaskState.Faulted;
                Debug.LogError($"[NetworkTaskBase] Send operation failed: {ex.Message}");
                return false;
            }
        }

        // 新增的 SendAsync 函数,callback 为发送成功后回调函数
        public void SendAsync(Action<MsgPackStructBase> callback = null)
        {
            if (State == NetworkTaskState.Running)
            {
                Debug.LogError("[NetworkTaskBase] Task is already running.");
                return;
            }

            State = NetworkTaskState.Running;
            _callback = callback;
            // 启动一个异步任务，但不等待它完成
            SendInternalAsync().Forget();
        }

        // 提取的公共逻辑
        private async UniTask<bool> SendInternalAsync()
        {
            int retryCount = 0;
            while (retryCount < _maxRetryCount)
            {
                try
                {
                    // 检查连接状态
                    if (!_client.IsConnected)
                    {
                        State = NetworkTaskState.Reconnecting;
                        await ReconnectAsync();

                        if (!_client.IsConnected)
                        {
                            throw new Exception("Failed to reconnect.");
                        }
                    }

                    // 发送消息并等待回复
                    bool isSendSuccess = await _client.SendAsync(_sendMsg, OnMessageReceived);
                    return isSendSuccess;
                }
                catch (Exception ex)
                {
                    retryCount++;
                    Debug.LogError($"[NetworkTaskBase] Attempt {retryCount} failed: {ex.Message}");

                    if (retryCount >= _maxRetryCount)
                    {
                        State = NetworkTaskState.Faulted;
                        Debug.LogError("[NetworkTaskBase] Max retry attempts reached.");
                        return false;
                    }
                }
            }

            return false;
        }

        private async UniTask<E_ConnectState> ReconnectAsync()
        {
            Debug.Log("[NetworkTaskBase] Attempting to reconnect...");
            return await _client.ReconnectAsync();
        }

        private void OnMessageReceived(MsgPackStructBase response)
        {
            Debug.Log("[NetworkTaskBase] OnMessageReceived");

            _sendCompletionSource?.TrySetResult(true);

            _client.HandleServerMessage(response);
            _responseMsg = response;
            // 调用回调
            _callback?.Invoke(_responseMsg);
            
            State = NetworkTaskState.Completed;
        }
    }
}