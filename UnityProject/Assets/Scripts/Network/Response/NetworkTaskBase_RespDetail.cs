using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Common.Network
{
    public partial class NetworkTaskBase
    {
        private void OnServerMessage(MsgPack_Example_Ack responseMsg)
        {

        }

        private void OnServerMessageDefault(MsgPackStructBase responseMsg)
        {

        }

        
    }
}

