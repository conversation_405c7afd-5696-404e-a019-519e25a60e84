using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Common.Network
{
    public partial class NetworkTaskBase
    {
        private void HandleServerMessage(MsgPackStructBase responseMsg)
        {
            switch (responseMsg.ProtoCode)
            {
                case EProtoCode.EXAMPLE_EXAMPLE_ACK:
                    {
                        OnServerMessage((responseMsg as MsgPack_Example_Ack)!);
                        break;
                    }
                default:
                    {
                        OnServerMessageDefault(responseMsg);
                        break;
                    }
            }
        }
    }
}

