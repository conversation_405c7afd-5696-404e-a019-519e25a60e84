using Phoenix.MsgPackLogic.Protocol;
using System;
using System.Collections.Generic;

namespace Phoenix.Common.Network
{
    public class MessageDispatcher
    {
        private Dictionary<uint, Action<MsgPackStructBase>> _tokenCallbacks = new Dictionary<uint, Action<MsgPackStructBase>>();
        private Dictionary<Type, Action<MsgPackStructBase>> _typeCallbacks = new Dictionary<Type, Action<MsgPackStructBase>>();
        private uint _messageIdCounter = 0;

        private NetworkClient _networkClient;

        public MessageDispatcher(NetworkClient networkClient)
        {
            _networkClient = networkClient;
        }

        public uint RegisterCallback(Action<MsgPackStructBase> callback)
        {
            uint messageId = ++_messageIdCounter;
            _tokenCallbacks[messageId] = callback;
            return messageId;
        }

        public void RegisterCallback<T>(Action<MsgPackStructBase> callback) where T : MsgPackStructBase
        {
            _typeCallbacks[typeof(T)] = callback;
        }

        public void UnRegisterCallback<T>() where T : MsgPackStructBase
        {
            _typeCallbacks.Remove(typeof(T));
        }

        public void ClearTypeCallbacks()
        {
            _typeCallbacks.Clear();
        }

        public void Dispatch(uint messageId, MsgPackStructBase msgPackData)
        {
            if (_tokenCallbacks.Remove(messageId, out var callback))
            {
                callback(msgPackData);
            }
        }

        public void Dispatch(Type tt, MsgPackStructBase msgPackData)
        {
            if (_typeCallbacks.TryGetValue(tt, out var callback))
            {
                _networkClient?.HandleServerMessage(msgPackData);
                callback(msgPackData);
            }
        }

    }
}