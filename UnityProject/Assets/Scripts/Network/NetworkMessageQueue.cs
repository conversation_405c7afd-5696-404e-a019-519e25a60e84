using Cysharp.Threading.Tasks;
using Phoenix.MsgPackLogic.Protocol;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;


namespace Phoenix.Common.Network
{
    public class NetworkMessageQueue<T> where T : MsgPackStructBase
    {
        readonly Queue<T> mQueue = new(32);
        MessageDispatcher _dispatcher;
        public bool Block { get; set; }

        const int mcMaxParsePerFrame = 10;

        public NetworkMessageQueue(MessageDispatcher dispatcher)
        {
            _dispatcher = dispatcher;
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        public void Push(T mes)
        {
            mQueue.Enqueue(mes);
        }

        private void OnTick(TimeSlice t)
        {
            if (!Block)
            {
                int minCount = System.Math.Min(mQueue.Count, mcMaxParsePerFrame);
                for (int i = 0; i < minCount; i++)
                {
                    T ctx = mQueue.Dequeue();
                    Dispatcher(ctx);
                }
            }
        }

        private void Dispatcher(T mes)
        {
            if (mes.GetToken().HasValue)
            {
                Debug.LogFormat("ChannelRead. Token {0}", mes.GetToken().Value);
                _dispatcher.Dispatch(mes.GetToken().Value, mes);
            }
            else
            {
                _dispatcher.Dispatch(mes.GetType(), mes);
            }
        }
    }
}