using System;
using System.Net;
using System.Collections.Generic;
using Codecs.MessagePack;
using Cysharp.Threading.Tasks;

using DotNetty.Codecs.Protobuf;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Channels.Sockets;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.CommonDefine;
using UnityEngine;
using Phoenix.Core;
using Phoenix.GameLogic;
using Phoenix.ConfigData;
using Phoenix.GameLogic.UI;

namespace Phoenix.Common.Network
{
    public partial class NetworkClient : Singleton<NetworkClient>
    {
        private NetworkMessageQueue<MsgPackStructBase> _messageQueue;
        private IChannel _channel;
        private Bootstrap _bootstrap;
        private MessageDispatcher _dispatcher;
        private SingleThreadEventLoop _group;
        private string _host;
        private int _port;

        public E_ConnectState EConnectState { get; private set; } = E_ConnectState.None;

        public bool IsConnected => EConnectState == E_ConnectState.Connected;

        public ServerDisconnectReasonCode DisConnectReason { get; private set; } = ServerDisconnectReasonCode.None;

        private Byte _xorCode = 0;
        public Byte XORCode { get => _xorCode; set => _xorCode = value; }

        private List<Int32> listXORExcept = new()
        {
            (Int32)EProtoCode.LoginByAuthTokenReq,
            (Int32)EProtoCode.LoginByAuthTokenAck,
            (Int32)EProtoCode.LoginBySessionTokenReq,
            (Int32)EProtoCode.LoginBySessionTokenAck
        };


        public NetworkClient()
        {

        }

        public void SetHostAndPort(string host, int port)
        {
            _host = host;
            _port = port;
        }

        public void SetDispatcher(MessageDispatcher dispatcher)
        {
            _dispatcher = dispatcher;
        }

        /// <summary>
        /// 连接服务器
        /// </summary>
        /// <param name="host"></param>
        /// <param name="port"></param>
        /// <returns></returns>
        public async UniTask<E_ConnectState> ConnectAsync(string host = "", int port = 0)
        {
            _host = string.IsNullOrEmpty(host) ? _host : host;
            _port = port == 0 ? _port : port;

            return await InternalConnectAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async UniTask<E_ConnectState> ConnectAsync()
        {
            return await InternalConnectAsync();
        }

        /// <summary>
        /// 重连服务器
        /// </summary>
        /// <returns></returns>
        public async UniTask<E_ConnectState> ReconnectAsync(double delaySecond = 0)
        {
            await CloseAsync();
            await UniTask.Delay(TimeSpan.FromSeconds(delaySecond));
            Debug.Log("Reconnecting...");
            return await InternalConnectAsync();
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="message"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public async UniTask<bool> SendAsync<T>(T message, Action<MsgPackStructBase> callback) where T : MsgPackStructBase
        {
            if (!CheckInternetState())
            {
                return false;
            }

            if (_channel == null || !_channel.Active || !IsConnected)
            {
                Debug.LogWarning("Channel is not active. Attempting to reconnect...");
                await ReconnectAsync();
                if (!IsConnected)
                {
                    Debug.LogError($"Reconnect failed: {EConnectState.ToString()}");
                    return false;
                }
            }

            if (message.HasToken())
            {
                uint token = _dispatcher.RegisterCallback(callback);
                Debug.LogFormat("SendAsync. Token {0}", token);
                message.SetToken(token);
            }

            await _channel.WriteAndFlushAsync(message);
            return true;
        }

        /// <summary>
        /// 注册服务器推送消息的回调函数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="callback"></param>
        public void RegisterCallback<T>(Action<MsgPackStructBase> callback) where T : MsgPackStructBase
        {
            _dispatcher.RegisterCallback<T>(callback);
        }

        public void UnRegisterCallback<T>() where T : MsgPackStructBase
        {
            _dispatcher.UnRegisterCallback<T>();
        }

        /// <summary>
        /// 关闭连接
        /// </summary>
        /// <returns></returns>
        public async UniTask CloseAsync()
        {
            if (_channel != null)
            {
                await _channel.CloseAsync();
                _channel = null;
                EConnectState = E_ConnectState.Disconnected;
            }
        }


        protected override void OnInit()
        {
            base.OnInit();
            // MsgPackProtoHelper.Instance.Init();
            SetDispatcher(new MessageDispatcher(this));
            _messageQueue = new NetworkMessageQueue<MsgPackStructBase>(_dispatcher);

            RegisterNeworkCallback();
        }

        protected override void OnUnInit()
        {
            UnRegisterNeworkCallback();
            _dispatcher.ClearTypeCallbacks();

            base.OnUnInit();
        }

        private ClientHandler CreateChannelHandler()
        {
            var clientHandler = new ClientHandler(_messageQueue);
            clientHandler.OnDisconnected += OnDisConnected;
            clientHandler.OnConnected += OnConnected;
            return clientHandler;
        }

        private void OnDisConnected()
        {
            EConnectState = E_ConnectState.Disconnected;
            Debug.Log("Channel Handler Callback OnDisConnected...");
            //ReconnectAsync().Forget();

            AsyncActionManager.QueueOnMainThread((param) =>
            {
                AsyncActionManager.instance.ShowDisconnectMessageBox();
            }, null, 1.0f);
        }

        private void OnConnected()
        {
            EConnectState = E_ConnectState.Connected;
            DisConnectReason = ServerDisconnectReasonCode.None;
            Debug.Log("Channel Handler Callback OnConnected...");
        }

        private async UniTask<E_ConnectState> InternalConnectAsync()
        {
            if (!CheckInternetState())
            {
                return EConnectState;
            }

            if (IsConnected) return EConnectState;

            if (string.IsNullOrEmpty(_host) || _port == 0)
            {
                EConnectState = E_ConnectState.None;
                Debug.Log($"Host or Port is empty host = {_host}, port = {_port}");
                return EConnectState;
            }

            if (_bootstrap != null)
            {
                try
                {
                    _channel = await _bootstrap.ConnectAsync(new IPEndPoint(IPAddress.Parse(_host), _port));
                    Debug.Log("Reconnected to server.");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Failed to Reconnect: {ex.Message}");
                    //Next time, we will try to reconnect with a new bootstrap.
                    _bootstrap = null;
                    EConnectState = E_ConnectState.Failed;
                    throw; // 抛出异常，方便外部处理
                }
            }
            else
            {
                try
                {
                    _group = new SingleThreadEventLoop();

                    _bootstrap = new Bootstrap()
                        .Group(_group)
                        .Channel<TcpSocketChannel>()
                        .Handler(new ActionChannelInitializer<ISocketChannel>(channel =>
                        {
                            channel.Pipeline.AddLast(
                                new ProtobufVarint32FrameDecoder(),
                                new EncryptionDecodeXOR(),
                                new MessagePackDecoder<MsgPackStructBase>(),

                                new ProtobufVarint32LengthFieldPrepender(),
                                new EncryptionEncodeXOR(),
                                new MessagePackEncoder<MsgPackStructBase>(),

                                CreateChannelHandler());
                        }));

                    _channel = await _bootstrap.ConnectAsync(new IPEndPoint(IPAddress.Parse(_host), _port));
                    Debug.Log("Connected to server.");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Failed to connect: {ex.Message}");
                    EConnectState = E_ConnectState.Failed;
                    throw; // 抛出异常，方便外部处理
                }
            }
            return EConnectState;
        }

        public bool IsExceptProtoCode(int iProtoCode)
        {
            return listXORExcept.Contains(iProtoCode);
        }

        private bool CheckInternetState()
        {
            if (Application.internetReachability == NetworkReachability.NotReachable)
            {
                string strContent = ConstStringUtility.GetConstString(ConstStringId.CheckNetworkState);
                TipUI.ShowTip(strContent);
                return false;
            }

            return true;
        }
    }
}