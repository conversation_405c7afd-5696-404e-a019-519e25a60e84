using System;
using System.Text;
using UnityEngine;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.GameLogic.GameContext;
using Cysharp.Threading.Tasks;
using Phoenix.CommonDefine;
using Phoenix.GameModel.Client;

namespace Phoenix.Common.Network
{
    public partial class NetworkClient
    {
        public async UniTask<bool> SendHakoniwaEnterReq(int hakoniwaId, int enterWaypointId)
        {
            HakoniwaEnterReq req = new();
            req.HakoniwaId = hakoniwaId;
            req.EnterWayPointId = enterWaypointId;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaDialogCompleteReq(int hakoniwaId, int dialogId)
        {
            HakoniwaDialogCompleteReq req = new();
            req.DialogId = dialogId;
            req.HakoniwaId = hakoniwaId;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaBattleStartReq(int hakoniwaId, int levelId)
        {
            HakoniwaBattleStartReq req = new();
            req.HakoniwaId = hakoniwaId;
            req.LevelId = levelId;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaBattleFinishReq(int hakoniwaId, int levelId)
        {
            HakoniwaBattleFinishReq req = new();
            req.HakoniwaId = hakoniwaId;
            req.LevelId = levelId;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaReachPointReq(int hakoniwaId, int questId, int questCondIndex)
        {
            HakoniwaReachPointReq req = new();
            req.HakoniwaId = hakoniwaId;
            req.QuestId = questId;
            req.QuestCondIndex = questCondIndex;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaTreasureBoxOpenReq(int hakoniwaId, int treasureBoxId)
        {
            HakoniwaTreasureBoxOpenReq req = new();
            req.HakoniwaId = hakoniwaId;
            req.TreasureBoxId = treasureBoxId;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaGiveUpReq(int hakoniwaId)
        {
            HakoniwaGiveUpReq req = new();
            req.HakoniwaId = hakoniwaId;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        public async UniTask<bool> SendHakoniwaPosSyncReq(int hakoniwaId, int sceneId, PosInfo posInfo)
        {
            HakoniwaPosSyncReq req = new();
            req.HakoniwaId = hakoniwaId;
            req.SceneId = sceneId;
            req.PosInfo = posInfo;
            NetworkTaskBase networkTask = new NetworkTaskBase(req);
            return await networkTask.SendAndReceiveAsync();
        }
        
        private void OnServerMessage(PlayerHakoniwaCompDataNtf responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("PlayerHakoniwaCompDataNtf response is null");
                return;
            }
            CompHakoniwa.InitFromServerData(responseMsg);
        }
        
        private void OnServerMessage(HakoniwaEnterAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaEnterAck response is null");
                return;
            }

            if (responseMsg.ErrCode == (int)ErrCode.ErrCodeOk)
            {
                CompHakoniwa.OnHakoniwaEnterAck(responseMsg);
            }
        }

        private void OnServerMessage(HakoniwaDialogCompleteAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaDialogCompleteAck response is null");
                return;
            }
        }
        
        private void OnServerMessage(HakoniwaFinishNtf responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaFinishNtf response is null");
                return;
            }
            CompHakoniwa.OnHakoniwaFinishNtf(responseMsg);
        }
        private void OnServerMessage(HakoniwaQuestCondChangeNtf responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaQuestCondChangeNtf response is null");
                return;
            }
            CompHakoniwa.OnHakoniwaQuestCondChangeNtf(responseMsg);
        }
        private void OnServerMessage(HakoniwaQuestStateChangeNtf responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaQuestStateChangeNtf response is null");
                return;
            }
            CompHakoniwa.OnHakoniwaQuestStateChangeNtf(responseMsg);
        }
        
        private void OnServerMessage(HakoniwaBattleStartAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaBattleStartAck response is null");
                return;
            }

            if (responseMsg.ErrCode == (int)ErrCode.ErrCodeOk)
            {
                CompHakoniwa.OnStartHakoniwaBattle(responseMsg.HakoniwaId, responseMsg.BattleProcessInfo as HakoniwaBattleProcessInfo);
            }
        }
        
        private void OnServerMessage(HakoniwaBattleFinishAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaBattleFinishAck response is null");
                return;
            }
            if (responseMsg.ErrCode == (int)ErrCode.ErrCodeOk)
            {
                CompHakoniwa.OnHakoniwaBattleFinishAck(responseMsg);
            }
        }
        
        private void OnServerMessage(HakoniwaReachPointAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaReachPointAck response is null");
                return;
            }
        }
        
        private void OnServerMessage(HakoniwaTreasureBoxOpenAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaTreasureBoxOpenAck response is null");
                return;
            }

            if (responseMsg.ErrCode == (int)ErrCode.ErrCodeOk)
            {
                CompHakoniwa.OnOpenHakoniwaTreasureBox(responseMsg.HakoniwaId, responseMsg.TreasureBoxId);
            }
        }
        
        private void OnServerMessage(HakoniwaGiveUpAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaGiveUpAck response is null");
                return;
            }

            if (responseMsg.ErrCode == (int)ErrCode.ErrCodeOk)
            {
                CompHakoniwa.OnGiveUpHakoniwa(responseMsg.HakoniwaId);
            }
        }
        
        private void OnServerMessage(HakoniwaPosSyncAck responseMsg)
        {
            if (responseMsg == null)
            {
                Debug.LogError("HakoniwaPosSyncAck response is null");
                return;
            }
            if (responseMsg.ErrCode == (int)ErrCode.ErrCodeOk)
            {
                CompHakoniwa.OnHakoniwaPosSync(responseMsg.HakoniwaId, responseMsg.SceneId, responseMsg.PosInfo);
            }
        }

        private GamePlayerCompHakoniwa CompHakoniwa => GamePlayerContext.instance.GamePlayer?.CompHakoniwa;
    }
}