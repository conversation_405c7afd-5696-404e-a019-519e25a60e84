
using System;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;
using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    public interface IGamePlayerContext
    {
        GamePlayerModuleProvider ModuleProvider { get; }
        T GetDataSection<T>() where T : DataSection;
        T GetPlayerModule<T>() where T : GamePlayerModule;
    }

    public partial class GamePlayerContext : Singleton<GamePlayerContext>, IGamePlayerContext
    {
        private Boolean m_isInitCompleted;
        private GamePlayerModuleProvider m_moduleProvider;

        private PlayerData m_playerData;
        private readonly List<DataSection> m_dataSections = new List<DataSection>();
        private readonly List<GamePlayerModule> m_modules = new List<GamePlayerModule>();

        public GamePlayerModuleProvider ModuleProvider => m_moduleProvider;

        //private readonly PriorityList<GamePlayerModule> m_modules = new PriorityList<GamePlayerModule>();

        public T GetDataSection<T>() where T : DataSection
        {
            foreach(DataSection dataSection in m_dataSections)
            {
                if (dataSection.GetType() == typeof(T))
                {
                    return (T)dataSection;
                }
            }
            return default(T);
        }

        public T GetPlayerModule<T>() where T : GamePlayerModule
        {
            foreach (GamePlayerModule module in m_modules)
            {
                if (module.GetType() == typeof(T))
                {
                    return (T)module;
                }
            }
            return default(T);
        }


        protected override void OnInit()
        {
            InitInternal();
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        protected override void OnUnInit()
        {
            UnInitInternal();
            TickManager.instance.UnRegisterGlobalTick(OnTick);
        }


        private void InitInternal()
        {
            m_moduleProvider = new GamePlayerModuleProvider();
            InitPlayerData();
            InitDataSection();
            foreach (DataSection dataSection in m_dataSections)
            {
                dataSection.Init(m_playerData);
            }
            InitGamePlayerModule();
            foreach (GamePlayerModule module in m_modules)
            {
                module.Init();
            }
            PostInit();
            m_isInitCompleted = true;
        }

        private void UnInitInternal()
        {
            TickDataSection();
            m_isInitCompleted = false;
            foreach (GamePlayerModule module in m_modules)
            {
                module.UnInit();
            }
            m_modules.Clear();
            foreach (DataSection dataSection in m_dataSections)
            {
                dataSection.UnInit();
            }
            m_dataSections.Clear();
            m_moduleProvider = null;

            UnInitGamePlayer();
        }

        private void PostInit()
        {
            foreach (GamePlayerModule module in m_modules)
            {
                module.PostInit();
            }
        }


        private void InitPlayerData()
        {
            m_playerData = PlayerDataSerialization.LoadPlayerDataByJson();
        }

        private void InitDataSection()
        {
            Assembly assembly = Assembly.GetAssembly(typeof(DataSectionAttribute));
            Type[] allTypes = assembly.GetExportedTypes();
            IEnumerable<Type> dataSectionTypes = allTypes.Where(type => type.GetCustomAttributes(typeof(DataSectionAttribute), true).Any());
            foreach (Type type in dataSectionTypes)
            {
                DataSection dataSection = Activator.CreateInstance(type) as DataSection;
                m_dataSections.Add(dataSection);
            }
        }

        private void InitGamePlayerModule()
        {
            Assembly assembly = Assembly.GetAssembly(typeof(GamePlayerModuleAttribute));
            Type[] allTypes = assembly.GetExportedTypes();
            IEnumerable<Type> handlerTypes = allTypes.Where(type => type.GetCustomAttributes(typeof(GamePlayerModuleAttribute), true).Any());
            foreach(Type type in handlerTypes)
            {
                GamePlayerModule module = Activator.CreateInstance(type, this) as GamePlayerModule;
                m_moduleProvider.InitModule(module);
                Int32 index = 0;
                while(index < m_modules.Count)
                {
                    GamePlayerModule tempModule = m_modules.ElementAt(index);
                    if (module.m_moduleId < tempModule.m_moduleId)
                    {
                        break;
                    }
                    index++;
                }
                m_modules.Insert(index, module);
            }
        }

        private void OnTick(TimeSlice ts)
        {
            if (!m_isInitCompleted)
            {
                return;
            }
            TickModule(ts);
            TickDataSection();
            
            GamePlayer?.OnTick(ts);
        }

        private void TickModule(TimeSlice ts)
        {
            foreach (GamePlayerModule module in m_modules)
            {
                module.Tick(ts);
            }
        }
        private void TickDataSection()
        {
            Boolean dirty = false;
            foreach (DataSection dataSection in m_dataSections)
            {
                if (dataSection.Dirty)
                {
                    dirty = true;
                    dataSection.ClearDirty();
                }
            }
            if (dirty)
            {
                PlayerDataSerialization.SavePlayerDataByJson(m_playerData);
            }
        }

        public void ResetPlayerContext()
        {
            PlayerDataSerialization.DeletePlayerDataFile();
            m_playerData = PlayerDataSerialization.LoadPlayerDataByJson();
            UnInitInternal();
            InitInternal();
        }

        // ======================== new logic start ========================

        public E_LoginState ELoginState { get; set; } = E_LoginState.None;

        private GamePlayer m_gamePlayer = null;
        public GamePlayer GamePlayer { get => m_gamePlayer; set => m_gamePlayer = value; }

        public void UnInitGamePlayer()
        {
            if (GamePlayer != null)
            {
                GamePlayer.UnInitInternal();
                GamePlayer = null;
            }
        }


        // ======================== new logic end ========================


    }
}
