

using System;

namespace Phoenix.GameLogic.GameContext
{
    public partial class GamePlayerContext
    {
        public GamePlayerServerProxyModule ServerProxy
        {
            get
            {
                return GetPlayerModule<GamePlayerServerProxyModule>();
            }
        }
        public GamePlayerBasicModule BasicModule
        {
            get
            {
                return GetPlayerModule<GamePlayerBasicModule>();
            }
        }
        public GamePlayerQuestModule QuestModule
        {
            get
            {
                return GetPlayerModule<GamePlayerQuestModule>();
            }
        }
        public GamePlayerWorldModule WorldModule
        {
            get
            {
                return GetPlayerModule<GamePlayerWorldModule>();
            }
        }
        public GamePlayerBattleModule BattleModule
        {
            get
            {
                return GetPlayerModule<GamePlayerBattleModule>();
            }
        }
        public GamePlayerBagModule BagModule
        {
            get
            {
                return GetPlayerModule<GamePlayerBagModule>();
            }
        }
        public GamePlayerSettingModule SettingModule
        {
            get
            {
                return GetPlayerModule<GamePlayerSettingModule>();
            }
        }

        public GamePlayerRewardModule RewardModule
        {
            get
            {
                return GetPlayerModule<GamePlayerRewardModule>();
            }
        }

        public GamePlayerFavorModule FavorModule
        {
            get
            {
                return GetPlayerModule<GamePlayerFavorModule>();
            }
        }

        public GamePlayerTransformationModule TransformationModule
        {
            get
            {
                return GetPlayerModule<GamePlayerTransformationModule>();
            }
        }

        public GamePlayerDecisionModule DecisionModule
        {
            get
            {
                return GetPlayerModule<GamePlayerDecisionModule>();
            }
        }

        public GamePlayerExploreModule ExploreModule
        {
            get
            {
                return GetPlayerModule<GamePlayerExploreModule>();
            }
        }

        public GamePlayerExchangeModule ExchangeModule
        {
            get
            {
                return GetPlayerModule<GamePlayerExchangeModule>();
            }
        }
        public GamePlayerHakoniwaModule HakoniwaModule
        {
            get
            {
                return GetPlayerModule<GamePlayerHakoniwaModule>();
            }
        }
    }
}
