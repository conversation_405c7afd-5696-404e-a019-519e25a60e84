

using System;

namespace Phoenix.GameLogic.GameContext
{
    [Serializable]
    public class PlayerData
    {
        public PlayerBasicData m_playerBasicData = new PlayerBasicData();
        public PlayerBattleData m_playerBattleData = new PlayerBattleData();
        public PlayerQuestData m_playerQuestData = new PlayerQuestData();
        public PlayerWorldData m_playerWorldData = new PlayerWorldData();
        public PlayerBagData m_playerBagData = new PlayerBagData();
        public PlayerSettingData m_playerSettingData = new PlayerSettingData();
        public PlayerRewardData m_playerRewardData = new PlayerRewardData();
        public PlayerFavorData m_playerFavorData = new PlayerFavorData();
        public PlayerTransformationData m_playerTransformationData = new PlayerTransformationData();
        public PlayerDecisionData m_playerDecisionData = new PlayerDecisionData();
        public PlayerExploreData m_playerExploreData = new PlayerExploreData();
        public PlayerHakoniwaData m_hakoniwaData = new PlayerHakoniwaData();
    }
}
