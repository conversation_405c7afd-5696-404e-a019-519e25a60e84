using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using Phoenix.ConfigData;
using pbc = global::Google.Protobuf.Collections;

namespace Phoenix.GameLogic.GameContext
{
    [DataSection(GameModuleId.Favor)]
    public class FavorDataSection : DataSection
    {
        private PlayerFavorData m_favorInfo;
        private StringBuilder m_strKey = new StringBuilder();
        private Int32 m_maxFvorCount = 3;

        protected override void OnInit(PlayerData playerData)
        {
            m_favorInfo = playerData.m_playerFavorData;
            m_favorInfo.InitData();
        }


        public Dictionary<string, FavorData> GetFavorDatas()
        {
            return m_favorInfo.m_dictFavorData;
        }

        private string GetDataKey(Int32 idFavor, Int32 idEntity)
        {
            return idFavor.ToString();
            //m_strKey.Clear();
            //m_strKey.Append(idFavor);
            //m_strKey.Append("_");
            //m_strKey.Append(idEntity);
            //return m_strKey.ToString(); 
        }

        public FavorData GetFavorData(Int32 idFavor, Int32 idEntity = -1)
        {
            if (idEntity == -1)
            {
                foreach (KeyValuePair<string, FavorData> kv in m_favorInfo.m_dictFavorData)
                {
                    FavorData favorData = kv.Value;
                    if (favorData.m_favorId == idFavor)
                    {
                        return favorData;
                    }
                }
            }

            FavorData data = null;
            if (!m_favorInfo.m_dictFavorData.TryGetValue(GetDataKey(idFavor, idEntity), out data))
            {
                Debug.LogError($"FavorDataSection: GetFavorData is null idFavor = {idFavor}, idEntity = {idEntity}");
            }
            return data;
        }

        public int GetFavorCount(Int32 idFavor, Int32 idEntity)
        {
            int count = 0;
            FavorData data = null;
            if (m_favorInfo.m_dictFavorData.TryGetValue(GetDataKey(idFavor, idEntity), out data))
            {
                count = data.m_count;
            }
            return count;
        }

        public int GetMaxFavorCount()
        {
            return m_maxFvorCount;
        }

        public FavorData AddFavorData(Int32 idFavor, Int32 idEntity)
        {
            FavorData data = null;
            string strKey = GetDataKey(idFavor, idEntity);
            if (!m_favorInfo.m_dictFavorData.TryGetValue(strKey, out data))
            {
                data = new FavorData();
                m_favorInfo.m_dictFavorData.Add(strKey, data);
            }

            data.m_favorId = idFavor;
            data.m_entityId = idEntity;
            data.m_count = data.m_count > 0 ? data.m_count : 0;

            // 检测GM指定好感度数量
            Int32 tempFavorCount = GamePlayerContext.instance.FavorModule.GetTempFavorCount();
            if(tempFavorCount > -1)
            {
                data.m_count = tempFavorCount;
                if (data.m_count > m_maxFvorCount)
                {
                    data.m_count = m_maxFvorCount;
                }
            }

            SetDirtyLocal();

            return data;
        }

        public void AddFavorCount(Int32 idFavor, Int32 idEntity, Int32 count)
        {
            FavorData data = GetFavorData(idFavor, idEntity);
            if (data != null)
            {
                data.m_count += count;
                if (data.m_count > m_maxFvorCount)
                {
                    data.m_count = m_maxFvorCount;
                }
                SetDirtyLocal();
            }
        }

        public void UpdateFavorExploreStatus(Int32 idFavor, Int32 idEntity, Int32 idExplore, Int32 status)
        {
            FavorData data = GetFavorData(idFavor, idEntity);
            if (data != null)
            {
                data.m_dictExploreStatus[idExplore] = status;
                m_favorInfo.m_dictExploreStatus[idExplore] = status;
                SetDirtyLocal();
            }
        }

        public bool IsFavorExploreStatusNone(Int32 idExplore)
        {
            Int32 status = 0;
            if (m_favorInfo.m_dictExploreStatus.TryGetValue(idExplore, out status))
            {
                return (FavorProcessStatus)status == FavorProcessStatus.None;
            }
            return true;
        }

        public bool IsFavorExploreStatusShowTip(Int32 idExplore)
        {
            Int32 status = 0;
            if (m_favorInfo.m_dictExploreStatus.TryGetValue(idExplore, out status))
            {
                return (FavorProcessStatus)status == FavorProcessStatus.ShowTip;
            }
            return false;
        }

        public bool IsFavorExploreStatusFinish(Int32 idExplore)
        {
            Int32 status = 0;
            if (m_favorInfo.m_dictExploreStatus.TryGetValue(idExplore, out status))
            {
                return (FavorProcessStatus)status == FavorProcessStatus.Finish;
            }
            return false;
        }

        public void UpdateFavorExchangeStatus(Int32 idFavor, Int32 idEntity, Int32 idExchange, Int32 status)
        {
            FavorData data = GetFavorData(idFavor, idEntity);
            if (data != null)
            {
                data.m_dictExchangeStatus[idExchange] = status;
                m_favorInfo.m_dictExchangeStatus[idExchange] = status;
                SetDirtyLocal();
            }
        }

        public bool IsFavorExchangeStatusNone(Int32 idExchange)
        {
            Int32 status = 0;
            if (m_favorInfo.m_dictExchangeStatus.TryGetValue(idExchange, out status))
            {
                return (FavorProcessStatus)status == FavorProcessStatus.None;
            }
            return true;
        }

        public bool IsFavorExchangeStatusShowTip(Int32 idExchange)
        {
            Int32 status = 0;
            if (m_favorInfo.m_dictExchangeStatus.TryGetValue(idExchange, out status))
            {
                return (FavorProcessStatus)status == FavorProcessStatus.ShowTip;
            }
            return false;
        }

        public bool IsFavorExchangeStatusFinish(Int32 idExchange)
        {
            Int32 status = 0;
            if (m_favorInfo.m_dictExchangeStatus.TryGetValue(idExchange, out status))
            {
                return (FavorProcessStatus)status == FavorProcessStatus.Finish;
            }
            return false;
        }

        public bool IsAllFavorExchangeStatusFinish(pbc::RepeatedField<ExchangeItemCountConfigData> listExchangeItemInfo, Int32 idFavor, Int32 idEntity = -1)
        {
            FavorData data = GetFavorData(idFavor, idEntity);
            if (data != null)
            {
                return false;
            }

            for (int i = 0; i < listExchangeItemInfo.Count; i++)
            {
                ExchangeItemCountConfigData exchangeItemCountConfigData = listExchangeItemInfo[i];
                Int32 status = 0;
                if (!data.m_dictExchangeStatus.TryGetValue(exchangeItemCountConfigData.ExchangeId, out status))
                {
                    return false;
                }
                if ((FavorProcessStatus)status != FavorProcessStatus.Finish)
                {
                    return false;
                }
            }

            return true;
        }

        public void UpdateFavorExchangeCount(Int32 idFavor, Int32 idEntity, Int32 idExchange, Int32 count)
        {
            FavorData data = GetFavorData(idFavor, idEntity);
            if (data != null)
            {
                Int32 countData = 0;
                data.m_dictExchangeCount.TryGetValue(idExchange, out countData);
                countData += count;
                data.m_dictExchangeCount[idExchange] = countData;
                SetDirtyLocal();
            }
        }

        public void RemoveFavorData(Int32 idFavor, Int32 idEntity)
        {
            string strKey = GetDataKey(idFavor, idEntity);
            if (m_favorInfo.m_dictFavorData.ContainsKey(strKey))
            {
                m_favorInfo.m_dictFavorData.Remove(strKey);
                SetDirtyLocal();
            }
        }

        public void ClearFavorData()
        {
            m_favorInfo.m_dictFavorData.Clear();
            SetDirtyLocal();
        }

        private void SetDirtyLocal()
        {
            SetDirty();
            m_favorInfo.SaveData();
        }
    }
}

