using System;
using System.Collections.Generic;

namespace Phoenix.GameLogic.GameContext
{
    [DataSection(GameModuleId.Reward)]
    public class RewardDataSection : DataSection
    {
        private PlayerRewardData m_rewardInfo;
        protected override void OnInit(PlayerData playerData)
        {
            m_rewardInfo = playerData.m_playerRewardData;
        }

        public List<RewardItem> GetItems()
        {
            return m_rewardInfo.listRewardItem;
        }

        public RewardItem GetItem(Int32 id)
        {
            foreach (RewardItem item in m_rewardInfo.listRewardItem)
            {
                if (item.m_itemId == id)
                {
                    return item;
                }
            }
            return null;
        }

        private void AddItemNew(Int32 itemId, Int32 count)
        {
            RewardItem item = new RewardItem();
            item.m_index = m_rewardInfo.listRewardItem.Count;
            item.m_itemId = itemId;
            item.m_count = count;
            m_rewardInfo.listRewardItem.Add(item);
        }

        public void AddItem(Int32 itemId, Int32 count, bool isMerge = true)
        {
            if (!isMerge)
            {
                AddItemNew(itemId, count);
                SetDirty();
                return;
            }

            RewardItem item = GetItem(itemId);
            if (item != null)
            {
                item.m_count += count;
            }
            else
            {
                AddItemNew(itemId, count);
            }

            SetDirty();
        }

        public void RemoveItem(Int32 itemId, Int32 count)
        {
            RewardItem item = GetItem(itemId);
            if (item != null)
            {
                if (item.m_count > count)
                {
                    item.m_count -= count;
                }
                else
                {
                    m_rewardInfo.listRewardItem.Remove(item);
                }
                SetDirty();
            }
        }

        public void ClearItem()
        {
            m_rewardInfo.listRewardItem.Clear();
            SetDirty();
        }
    }
}

