
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    [DataSection(GameModuleId.Battle)]
    public class BattleDataSection : DataSection
    {
        private PlayerBattleData m_playerBattleData;
        protected override void OnInit(PlayerData playerData)
        {
            m_playerBattleData = playerData.m_playerBattleData;
        }


        public void SetBattleParam(Int32 battleId, BattleType battleType, params Int32[] paramters)
        {
            m_playerBattleData.m_battleParams.Clear();
            m_playerBattleData.m_battleParams.Add(battleId);
            m_playerBattleData.m_battleParams.Add((Int32)battleType);
            m_playerBattleData.m_battleParams.AddRange(paramters);
            SetDirty();
        }

        public void ResetBattleParam()
        {
            m_playerBattleData.m_battleParams.Clear();
            SetDirty();
        }

        public Int32 GetBattleParam(Int32 index)
        {
            if (m_playerBattleData.m_battleParams.Count > index)
            {
                return m_playerBattleData.m_battleParams[index];
            }
            return 0;
        }

        public Boolean IsInitBattleCompleted()
        {
            return m_playerBattleData.m_initBattleCompleted;
        }
        public void SetInitBattleCompleted()
        {
            m_playerBattleData.m_initBattleCompleted = true;
            SetDirty();
        }
        public void AddCompletedGuideBattle(Int32 guideBattleId)
        {
            m_playerBattleData.m_completedGuideBattleIds.Add(guideBattleId);
            SetDirty();
        }
        public Boolean IsGuideBattleCompleted(Int32 guideBattleId)
        {
            return m_playerBattleData.m_completedGuideBattleIds.Contains(guideBattleId);
        }


        public Boolean IsBattleAchievementCompleted(Int32 id)
        {
            return m_playerBattleData.m_completedBattleAchievementIds.Contains(id);
        }
        public void SetBattleAchievementCompleted(Int32 id)
        {
            if (!IsBattleAchievementCompleted(id))
            {
                m_playerBattleData.m_completedBattleAchievementIds.Add(id);
                SetDirty();
            }
        }
    }
}
