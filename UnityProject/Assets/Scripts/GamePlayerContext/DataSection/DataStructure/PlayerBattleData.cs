


using System;
using System.Collections.Generic;

namespace Phoenix.GameLogic.GameContext
{
    [Serializable]
    public class PlayerBattleData
    {
        // Index[BattleId][BattleType][Paramter1][...][Paramter100]
        public List<Int32> m_battleParams = new List<Int32>();
        public List<Int32> m_completedGuideBattleIds = new List<Int32>();
        public Boolean m_initBattleCompleted;


        public List<Int32> m_completedBattleAchievementIds = new List<Int32>();
        public List<Int32> m_completedBattleTreasureBoxIds = new List<Int32>();
    }
}
