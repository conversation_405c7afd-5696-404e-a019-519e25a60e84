using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using Phoenix.GameLogic.GameContext;

namespace Phoenix.GameLogic.GameContext
{
    [Serializable]
    public enum ExploreProcessStatus
    {
        None, // 默认状态
        Finish, // 某个行为结束:已解锁，已兑换
    }

    [Serializable]
    public class PlayerExploreData
    {
        public Dictionary<string, ExploreData> m_dictExploreData = new Dictionary<string, ExploreData>();
        public List<ExploreData> m_listExploreData = new List<ExploreData>();
        public Dictionary<Int32, Int32> m_dictSystemStatus = new Dictionary<Int32, Int32>();
        public Dictionary<Int32, Int32> m_dictClueStatus = new Dictionary<Int32, Int32>();
        public Dictionary<Int32, Int32> m_dictExchangeStatus = new Dictionary<Int32, Int32>();

        public void InitData()
        {
            m_dictExploreData.Clear();
            for (int i = 0; i < m_listExploreData.Count; i++)
            {
                ExploreData explore = m_listExploreData[i];
                string strKey = explore.m_exploreId.ToString(); // explore.GetKey(explore.m_exploreId, explore.m_entityId);
                if (!m_dictExploreData.ContainsKey(strKey))
                {
                    explore.InitData();
                    m_dictExploreData.Add(strKey, explore);
                }

                foreach (KeyValuePair<Int32, Int32> kv in explore.m_dictSystemStatus)
                {
                    m_dictSystemStatus[kv.Key] = kv.Value;
                }

                foreach (KeyValuePair<Int32, Int32> kv in explore.m_dictClueStatus)
                {
                    m_dictClueStatus[kv.Key] = kv.Value;
                }

                foreach (KeyValuePair<Int32, Int32> kv in explore.m_dictExchangeStatus)
                {
                    m_dictExchangeStatus[kv.Key] = kv.Value;
                }
            }
        }

        public void SaveData()
        {
            m_listExploreData.Clear();
            foreach (KeyValuePair<string, ExploreData> kv in m_dictExploreData)
            {
                kv.Value.SaveData();
            }
            m_listExploreData.AddRange(m_dictExploreData.Values);
        }

    }

    [Serializable]
    public class ExploreData
    {
        public Int32 m_exploreId;
        public Int32 m_entityId;
        public Dictionary<Int32, Int32> m_dictSystemStatus = new Dictionary<Int32, Int32>();
        public List<string> m_listSystemStatus = new List<string>();
        public Dictionary<Int32, Int32> m_dictClueStatus = new Dictionary<Int32, Int32>();
        public List<string> m_listClueStatus = new List<string>();
        public Dictionary<Int32, Int32> m_dictExchangeStatus = new Dictionary<Int32, Int32>();
        public List<string> m_listExchangeStatus = new List<string>();
        public Dictionary<Int32, Int32> m_dictExchangeCount = new Dictionary<Int32, Int32>();
        public List<string> m_listExchangeCount = new List<string>();

        public void InitData()
        {
            AddToDictionary(m_listSystemStatus, m_dictSystemStatus);
            AddToDictionary(m_listClueStatus, m_dictClueStatus);
            AddToDictionary(m_listExchangeStatus, m_dictExchangeStatus);
            AddToDictionary(m_listExchangeCount, m_dictExchangeCount);
        }

        private void AddToDictionary(List<string> m_list, Dictionary<Int32, Int32> m_dict)
        {
            m_dict.Clear();
            for (int i = 0; i < m_list.Count; i++)
            {
                string strKeyValue = m_list[i];
                string[] strKeyValueArr = strKeyValue.Split('_');
                if (strKeyValueArr.Length != 2)
                {
                    continue;
                }
                Int32 key = Int32.Parse(strKeyValueArr[0]);
                Int32 value = Int32.Parse(strKeyValueArr[1]);
                m_dict[key] = value;
            }
        }

        public void SaveData()
        {
            AddToList(m_dictSystemStatus, m_listSystemStatus);
            AddToList(m_dictClueStatus, m_listClueStatus);
            AddToList(m_dictExchangeStatus, m_listExchangeStatus);
            AddToList(m_dictExchangeCount, m_listExchangeCount);
        }

        public void AddToList(Dictionary<Int32, Int32> m_dict, List<string> m_list)
        {
            m_list.Clear();
            foreach (KeyValuePair<Int32, Int32> kv in m_dict)
            {
                string strKeyValue = GetKey(kv.Key, kv.Value);
                m_list.Add(strKeyValue);
            }
        }

        private StringBuilder m_strKey = new StringBuilder();
        public string GetKey(Int32 key, Int32 value)
        {
            m_strKey.Clear();
            m_strKey.Append(key);
            m_strKey.Append("_");
            m_strKey.Append(value);
            return m_strKey.ToString();
        }

    }
}
