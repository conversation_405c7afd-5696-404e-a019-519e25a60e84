using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    [Serializable]
    public class PlayerRewardData
    {
        public List<RewardItem> listRewardItem = new List<RewardItem>();
    }

    [Serializable]
    public class RewardItem
    {
        public Int32 m_index;
        public Int32 m_itemId;
        public Int32 m_count;
        public StoryExhibitType m_storyExhibitType = StoryExhibitType.None;
    }
}

