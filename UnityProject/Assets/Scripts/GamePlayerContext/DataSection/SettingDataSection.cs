
using System;
using static Cinemachine.AxisState;

namespace Phoenix.GameLogic.GameContext
{
    [DataSection(GameModuleId.Setting)]
    public class SettingDataSection : DataSection
    {
        public PlayerSettingData m_playerSettingData;
        protected override void OnInit(PlayerData playerData)
        {
            m_playerSettingData = playerData.m_playerSettingData;
        }



        public Boolean GetSimpleSkillDescFlag()
        {
            return m_playerSettingData.m_simpleSkillDescFlag;
        }
        public void SetSimpleSkillDescFlag(Boolean flag)
        {
            if (m_playerSettingData.m_simpleSkillDescFlag != flag)
            {
                m_playerSettingData.m_simpleSkillDescFlag = flag;
                SetDirty();
            }
        }
        public Int32 GetBattleSpeedMode()
        {
            return m_playerSettingData.m_battleSpeedMode;
        }
        public void SetBattleSpeedMode(Int32 speedMode)
        {
            if (m_playerSettingData.m_battleSpeedMode != speedMode)
            {
                m_playerSettingData.m_battleSpeedMode = speedMode;
                SetDirty();
            }
        }
        public void SetExpertFlag(Int32 expertFlag)
        {
            if (m_playerSettingData.m_expertFlag != expertFlag)
            {
                m_playerSettingData.m_expertFlag = expertFlag;
                SetDirty();
            }
        }
        public void SetSkipGuideBattleFlag(Int32 skipGuideBattle)
        {
            if (m_playerSettingData.m_skipGuideBattleFlag != skipGuideBattle)
            {
                m_playerSettingData.m_skipGuideBattleFlag = skipGuideBattle;
                SetDirty();
            }
        }

        public Boolean IsUserGuideStepComplete(Int32 guideGroupId)
        {
            return m_playerSettingData.m_completedGuideGroupIds.Contains(guideGroupId);
        }

        public void SetGuideGroupComplete(Int32 guideGroupId)
        {
            if (!IsUserGuideStepComplete(guideGroupId))
            {
                m_playerSettingData.m_completedGuideGroupIds.Add(guideGroupId);
                SetDirty();
            }
        }
        public Boolean GetSkillRecommendFlag()
        {
            return m_playerSettingData.m_skillRecommendFlag;
        }
        public void SetSkillRecommendFlag(Boolean skillRecommend)
        {
            if (m_playerSettingData.m_skillRecommendFlag != skillRecommend)
            {
                m_playerSettingData.m_skillRecommendFlag = skillRecommend;
                SetDirty();
            }
        }
        public Boolean GetShowDangerRangeFlag()
        {
            return m_playerSettingData.m_showDangerRangeFlag;
        }
        public void SetShowDangerRangeFlag(Boolean showDangerRange)
        {
            if (m_playerSettingData.m_showDangerRangeFlag != showDangerRange)
            {
                m_playerSettingData.m_showDangerRangeFlag = showDangerRange;
                SetDirty();
            }
        }
    }
}
