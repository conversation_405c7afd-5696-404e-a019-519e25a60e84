


using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    public static class ItemUtility
    {
        public static List<RewardItem> GetRewardItems(IList<RewardConfigData> rewards)
        {
            List<RewardItem> rewardItems = new List<RewardItem>();
            Int32 index = 0;
            foreach(RewardConfigData reward in  rewards)
            {
                RewardItem item = new RewardItem();
                item.m_index = index;
                item.m_itemId = reward.ItemId;
                item.m_count = reward.Count;
                rewardItems.Add(item);
                index++;
            }
            return rewardItems;
        }
        public static List<RewardItem> GetRewards(Int32 dropGroupId)
        {
            List<RewardItem> rewardItems = new List<RewardItem>();
            DropGroupConfigData dropGroupConfigData = ConfigDataManager.instance.GetDropGroup(dropGroupId);
            if (dropGroupConfigData != null)
            {
                Int32 index = 0;
                foreach (RewardConfigData reward in dropGroupConfigData.Rewards)
                {
                    RewardItem item = new RewardItem();
                    item.m_index = index;
                    item.m_itemId = reward.ItemId;
                    item.m_count = reward.Count;
                    rewardItems.Add(item);
                    index++;
                }

            }

            return rewardItems;
        }
    }
}
