

//using System;
//using Phoenix.ConfigData;
//using Phoenix.Core;

//namespace Phoenix.GameLogic.GameContext
//{
//    public class QuestGroupProcessor
//    {
//        private GamePlayerQuestModule m_owner;
//        private QuestGroupConfigData m_questGroupConfig;
//        private Int32 m_runningQuestIndex;
//        private QuestProcessor m_questProcessor;


//        public QuestGroupConfigData QuestGroupConfig
//        {
//            get { return m_questGroupConfig;}
//        }

//        public QuestProcessor RunningQuestProcessor
//        {
//            get { return m_questProcessor; }
//        }


//        public void Init(GamePlayerQuestModule questModule, QuestGroupConfigData questGroupConfig, Int32 runningQuestIndex)
//        {
//            m_owner = questModule;
//            m_questGroupConfig = questGroupConfig;
//            m_runningQuestIndex = runningQuestIndex;
//        }



//        public void Start()
//        {
//            StartQuestInternal(m_runningQuestIndex);
//        }
        
//        public void Tick(TimeSlice ts) { }

//        public void UnInit()
//        {
//            m_owner = null;
//            m_questGroupConfig = null;
//        }


//        #region Internal


//        protected void StartQuestInternal(Int32 questIndex)
//        {
//            if (m_questGroupConfig.m_questInfos.Count <= questIndex)
//            {
//                // End.
//                return;
//            }
//            else
//            {
//                QuestConfigData questConfig = m_questGroupConfig.m_questInfos[questIndex];
//                m_questProcessor = new QuestProcessor();
//                m_questProcessor.Init(m_owner, this);
//                m_questProcessor.Start(questConfig);


//                // 
//                QuestProgressData progressData = m_owner.DataSection.CreateQuestProgressData(questConfig);
//                m_owner.DataSection.AddQuestProgressData(progressData);
//                m_owner.OnQuestGroupAcceptQuest(m_questProcessor);
//            }
//        }

//        #endregion




//        //public event Action<QuestProcessor> EventOnQuestAccept;
//        //public event Action<QuestProcessor, Int32, Int32> EventOnQuestProgressUpdate;
//        //public event Action<QuestProcessor, Boolean> EventOnQuestStatusChange;
//        //public event Action<QuestProcessor> EventOnQuestCompleted;
//    }
//}
