

//using System;
//using Phoenix.ConfigData;

//namespace Phoenix.GameLogic.GameContext
//{
//    public static class QuestGroupProcessorCreater
//    {
//        /// <summary>
//        /// 初始化任务组, 分析已保存的数据来创建
//        /// </summary>
//        public static QuestGroupProcessor InitFromDB(GamePlayerQuestModule questModule, QuestGroupConfigData questGroupConfig)
//        {
//            QuestGroupProcessor processor = null;
//            Boolean started = false;
//            Int32 processQuestIndex = 0;
//            foreach (QuestConfigData questConfig in questGroupConfig.m_questInfos)
//            {
//                if (questModule.IsQuestCompleted(questConfig.Id))
//                {
//                    processQuestIndex++;
//                    started = true;
//                    continue;
//                }
//                else if (questModule.IsQuestRunning(questConfig.Id))
//                {
//                    started = true;
//                    break;
//                }
//                else
//                {
//                    started = false;
//                    break;
//                }
//            }
//            if(started)
//            {
//                // 任务组已开启，判断是否结束
//                if (processQuestIndex < questGroupConfig.QuestInfoCount)
//                {
//                    processor = new QuestGroupProcessor();
//                }
//            }
//            else
//            {
//                // 任务组未开启，判断是否自动开启
//                if (questGroupConfig.AutoBegin)
//                {
//                    processor = new QuestGroupProcessor();
//                }
//            }
//            if (processor != null)
//            {
//                processor.Init(questModule, questGroupConfig, processQuestIndex);
//            }
//            return processor;
//        }



//        /// <summary>
//        /// 接受新的任务组
//        /// </summary>
//        public static QuestGroupProcessor InitNewQuestGroup(GamePlayerQuestModule questModule, QuestGroupConfigData questGroupConfig)
//        {
//            QuestGroupProcessor processor = null;
//            if (questGroupConfig.m_questInfos.Count > 0)
//            {
//                Int32 firstQuestId = questGroupConfig.m_questInfos[0].Id;
//                if (questModule.IsQuestRunning(firstQuestId) || questModule.IsQuestRunning(firstQuestId))
//                {
//                    DebugUtility.LogError("任务重复接取");
//                    // 任务重复接取
//                }
//                else
//                {
//                    processor = new QuestGroupProcessor();
//                    processor.Init(questModule, questGroupConfig, 0);
//                }
//            }
//            return processor;
//        }
//    }
//}
