

using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.Quest)]
    public class GamePlayerQuestModule : GamePlayerModule, IGameEventPipeListener
    {
        private QuestDataSection m_dataSection;

        private GamePlayerWorldModule m_worldModule;
        private GamePlayerBagModule m_bagModule;
        private GamePlayerEventDispatchModule m_eventDispatchModule;


        private List<Int32> m_dynamicWorldEntitys = new List<Int32>();
        private Dictionary<Int32, List<Int32>> m_dynamicWorldEntityInteractions = new Dictionary<Int32, List<Int32>>();


        private List<QuestProcessor> m_runningQuestList = new List<QuestProcessor>();
        private List<QuestProcessor> m_tempQuestList = new List<QuestProcessor>();



        public event Action<QuestProcessor, Int32, Int32> EventOnQuestProgressUpdate;
        public event Action<QuestProcessor, QuestStatus> EventOnQuestStatusChange;
        public event Action<QuestProcessor> EventOnQuestAccept;
        public event Action<QuestProcessor> EventOnQuestCompleted;

        public event Action<Int32> EventOnTracingQuestGroupUpdate;



        public GamePlayerQuestModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.Quest;
        }


        #region Internal Override Base

        protected override void OnInit()
        {
            m_dataSection = m_owner.GetDataSection<QuestDataSection>();
            m_eventDispatchModule = m_owner.GetPlayerModule<GamePlayerEventDispatchModule>();
            m_worldModule = m_owner.GetPlayerModule<GamePlayerWorldModule>();
            m_bagModule = m_owner.GetPlayerModule<GamePlayerBagModule>();
        }

        protected override void OnPostInit()
        {
            InitializeAllQuest();
        }

        protected override void OnRegisterListener()
        {
            m_eventDispatchModule.AddListener(this);
        }

        protected override void OnUnRegisterListener()
        {
            m_eventDispatchModule.RemoveListener(this);
        }

        protected override void OnUnInit()
        {
            foreach (var item in m_runningQuestList)
            {
                item.UnInit();
            }
            m_runningQuestList.Clear();
            m_dynamicWorldEntitys.Clear();
            foreach(var kv in m_dynamicWorldEntityInteractions)
            {
                kv.Value.Clear();
            }
            m_dynamicWorldEntityInteractions.Clear();
            m_worldModule = null;
            m_eventDispatchModule = null;
            m_bagModule = null;
        }

        protected void InitializeAllQuest()
        {
            List<QuestProgressData> progressDatas = m_dataSection.GetAllRunningQuestProgressData();
            foreach (QuestProgressData progressData in progressDatas)
            {
                AcceptQuestInternal(progressData.m_id, false);
            }

            // 检查自动接受的任务
            foreach (var kv in ConfigDataManager.instance.questGroupMap)
            {
                if (kv.Value.AutoBegin)
                {
                    Int32 beiginQuestId = kv.Value.FirstQuestId;
                    if (IsQuestRunningOrCompleted(beiginQuestId))
                    {
                        continue;
                    }
                    AcceptQuestInternal(beiginQuestId);
                }
            }
            UpdateTracingQuestGroup();
        }

        #endregion






        #region GM

        public void ResetQuestData()
        {
            m_runningQuestList.Clear();
            m_dynamicWorldEntitys.Clear();
            foreach (var kv in m_dynamicWorldEntityInteractions)
            {
                kv.Value.Clear();
            }
            m_dynamicWorldEntityInteractions.Clear();
            m_dataSection.ResetSectionData();
            InitializeAllQuest();
        }

        #endregion


        #region  Public

        public void OnEvent(GameEvent e)
        {
            m_tempQuestList.Clear();
            m_tempQuestList.AddRange(m_runningQuestList);
            foreach (var questProcessor in m_tempQuestList)
            {
                questProcessor.ProcessEvent(e);
            }

            if (e.m_eventId == GameEventId.QuestAccept 
                || e.m_eventId == GameEventId.QuestCompleted
                || e.m_eventId == GameEventId.WorldEntityExploreUpdate)
            {
                CheckAndAcceptQuestInternal(e);
            }
        }


        /// <summary>
        ///  手动完成任务，（一般为领取奖励，或者提交道具）
        /// </summary>
        /// <param name="questId"></param>
        public void SetQuestCompleted(int questId)
        {
            QuestProcessor questProcessor = GetQuestProcessorInternal(questId);
            if (questProcessor != null)
            {
                questProcessor.SetQuestCompleted();
            }
        }


        public Boolean IsQuestCompleted(Int32 questId)
        {
            return m_dataSection.GetQuestCompletedStatus(questId);
        }

        public Boolean IsQuestRunning(Int32 questId)
        {
            return m_dataSection.GetRunningQuestProgressData(questId) != null;
        }

        public Boolean IsQuestRunningOrCompleted(Int32 questId)
        {
            return m_dataSection.GetQuestCompletedStatus(questId) || m_dataSection.GetRunningQuestProgressData(questId) != null;
        }

        public QuestStatus GetQuestStatus(Int32 questId)
        {
            foreach (QuestProcessor questProcessor in m_runningQuestList)
            {
                if (questProcessor.QuestConfig.Id == questId)
                {
                    return questProcessor.Status;
                }
            }
            return QuestStatus.None;
        }

        public void OnQuestProgressUpdate(QuestProcessor questHandler, Int32 conditionIndex, Int32 newProgress)
        {
            m_dataSection.SetQuestConditionProgress(questHandler.Id, conditionIndex, newProgress);
            // 抛出进度更新的消息
            FireQuestProgressUpdateEventInternal(questHandler, conditionIndex, newProgress);
        }

        public void OnQuestStatusChange(QuestProcessor questProcessor, QuestStatus oldStatus)
        {
            FireQuestStatusChangeEventInternal(questProcessor, oldStatus);

            if (questProcessor.Status == QuestStatus.Completed)
            {
                CompleteQuestInternal(questProcessor);
            }
        }


        public void AddWorldEntity(Int32 worldEntityId)
        {
            m_dynamicWorldEntitys.Add(worldEntityId);
            m_worldModule.AddWorldEntity(worldEntityId);
        }

        public void RemoveWorldEntity(Int32 worldEntityId)
        {
            m_dynamicWorldEntitys.Remove(worldEntityId);
            m_worldModule.RemoveWorldEntity(worldEntityId);
        }

        public void AddWorldEntityInteraction(Int32 worldEntityId, Int32 interactionId)
        {
            if (!m_dynamicWorldEntityInteractions.TryGetValue(worldEntityId, out List<Int32> interactions))
            {
                interactions = new List<Int32>();
                m_dynamicWorldEntityInteractions.Add(worldEntityId, interactions);
            }
            interactions.Add(interactionId);
            m_worldModule.AddWorldEntityInteraction(worldEntityId, interactionId);
        }

        public void RemoveWorldEntityInteraction(Int32 worldEntityId, Int32 interactionId)
        {
            if (m_dynamicWorldEntityInteractions.TryGetValue(worldEntityId, out List<Int32> interactions))
            {
                interactions.Remove(interactionId);
            }
            m_worldModule.RemoveWorldEntityInteraction(worldEntityId, interactionId);
        }


        public List<QuestProcessor> GetAllRunningQuestGroup()
        {
            List<QuestProcessor> questGroupList = new List<QuestProcessor> ();

            if (m_runningQuestList.Count > 0)
            {
                foreach(QuestProcessor questProcessor in m_runningQuestList)
                {
                    if(questProcessor.QuestConfig.m_questGroupConfigData != null)
                    {
                        questGroupList.Add(questProcessor);
                    }
                }
            }
            return questGroupList;
        }

        public QuestProcessor GetRunningQuestProcessor(Int32 questId)
        {
            foreach (QuestProcessor questProcessor in m_runningQuestList)
            {
                if (questProcessor.Id == questId)
                {
                    return questProcessor;
                }
            }
            return null;
        }

        public QuestProcessor GetRunningQuestGroupProcessor(Int32 questGroupId)
        {
            foreach (QuestProcessor questProcessor in m_runningQuestList)
            {
                if (questProcessor.QuestGroupId == questGroupId)
                {
                    return questProcessor;
                }
            }
            return null;
        }



        public Int32 GetTracingQuestGroupId()
        {
            return m_dataSection.GetTracingQuestGroupId();
        }

        public void SetTracingQuestGroupId(Int32 tracingQuestGroupId)
        {
            m_dataSection.SetTracingQuestGroupId(tracingQuestGroupId);
            EventOnTracingQuestGroupUpdate?.Invoke(tracingQuestGroupId);
        }


        public List<Int32> GetRunningQuestLinkWorldEntity()
        {
            List<Int32> result = new List<Int32>(m_dynamicWorldEntitys);
            return result;
        }

        public Dictionary<Int32, List<Int32>> GetRunningQuestLinkWorldEntityInteraction()
        {
            Dictionary<Int32, List<Int32>> result = new Dictionary<Int32, List<Int32>>(m_dynamicWorldEntityInteractions);
            return result;
        }

        public Int32 GetConditionProgress(Int32 questId, Int32 conditionIndex)
        {
            return m_dataSection.GetQuestConditionProgress(questId, conditionIndex);
        }

        #endregion



        #region Internal Private

        protected void UpdateTracingQuestGroup()
        {
            Int32 tracingQuestGroupId = m_dataSection.GetTracingQuestGroupId();
            Boolean existQuestGroup = false;

            foreach (QuestProcessor quest in m_runningQuestList)
            {
                if (quest.IsQuestGroupProcessor && quest.QuestGroupId == tracingQuestGroupId)
                {
                    existQuestGroup = true;
                }
            }

            if (existQuestGroup)
            {
                return;
            }

            List<QuestGroupConfigData> questGroups = new List<QuestGroupConfigData>();
            foreach (QuestProcessor quest in m_runningQuestList)
            {
                if (quest.IsQuestGroupProcessor)
                {
                    questGroups.Add(quest.QuestGroupConfig);
                }
            }
            questGroups.Sort(SortQuestGroup);

            if(questGroups.Count > 0)
            {
                SetTracingQuestGroupId(questGroups[0].Id);
            }
            else
            {
                SetTracingQuestGroupId(0);
            }
        }

        private Int32 SortQuestGroup(QuestGroupConfigData quest1, QuestGroupConfigData quest2)
        {
            if (quest1.GroupType == quest2.GroupType)
            {
                return quest2.SortPriority.CompareTo(quest1.SortPriority);
            }
            else
            {
                if (quest1.GroupType == QuestGroupType.Main)
                {
                    return -1;
                }
                else if (quest2.GroupType == QuestGroupType.Main)
                {
                    return 1;
                }
            }
            return 0;
        }


        protected QuestProcessor GetQuestProcessorInternal(Int32 questConfigId)
        {
            foreach (QuestProcessor questProcessor in m_runningQuestList)
            {
                if (questProcessor != null && questProcessor.Id == questConfigId)
                {
                    return questProcessor;
                }
            }
            return null;
        }


        protected void AcceptQuestInternal(Int32 questId, Boolean pushAction = true)
        {
            QuestConfigData questConfig = ConfigDataManager.instance.GetQuest(questId);
            if (questConfig == null)
            {
                return;
            }
            QuestProcessor questProcessor = new QuestProcessor();
            questProcessor.Init(this);
            questProcessor.Start(questConfig);
            m_runningQuestList.Add(questProcessor);
            questProcessor.ProcessQuestActions(questConfig.AcceptActions, pushAction);

            if (pushAction)
            {
                QuestProgressData progressData = new QuestProgressData();
                progressData = new QuestProgressData();
                progressData.m_id = questConfig.Id;
                progressData.m_progress = new int[questConfig.CompletedCondition.Count];
                m_dataSection.AddQuestProgressData(progressData);
                FireQuestAcceptEventInternal(questProcessor);
                m_eventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.QuestAccept, questId));

                if (questProcessor.IsQuestGroupProcessor && 
                    questProcessor.QuestGroupConfig.GroupType == QuestGroupType.Main &&
                    questProcessor.QuestGroupConfig.FirstQuestId == questId)
                {
                    Int32 tracingQuestGroupId = m_dataSection.GetTracingQuestGroupId();
                    QuestGroupConfigData questGroupConfig = ConfigDataManager.instance.GetQuestGroup(tracingQuestGroupId);
                    if (questGroupConfig == null || questGroupConfig.GroupType == QuestGroupType.Branch)
                    {
                        m_dataSection.SetTracingQuestGroupId(0);
                        UpdateTracingQuestGroup();
                    }
                }
                else
                {
                    Int32 tracingQuestGroupId = m_dataSection.GetTracingQuestGroupId();
                    QuestGroupConfigData questGroupConfig = ConfigDataManager.instance.GetQuestGroup(tracingQuestGroupId);
                    if (questGroupConfig == null)
                    {
                        UpdateTracingQuestGroup();
                    }
                }
            }
        }

        protected void CompleteQuestInternal(QuestProcessor questProcessor)
        {
            m_dataSection.SetQuestCompleted(questProcessor.Id);
            DropGroupConfigData dropGroupConfigData = ConfigDataManager.instance.GetDropGroup(questProcessor.QuestConfig.DropRewardId);
            if (dropGroupConfigData != null && dropGroupConfigData.Rewards.Count > 0)
            {
                m_bagModule.AddItems(dropGroupConfigData.Rewards);
            }
            questProcessor.ProcessQuestActions(questProcessor.QuestConfig.CompletedActions, true);
            FireQuestCompletedEventInternal(questProcessor);
            m_eventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.QuestCompleted, questProcessor.Id));
            OnCompleteQuestInternal(questProcessor);
        }



        protected void OnCompleteQuestInternal(QuestProcessor questProcessor)
        {
            m_runningQuestList.Remove(questProcessor);
            QuestGroupConfigData questGroup = questProcessor.QuestConfig.m_questGroupConfigData;
            if (questGroup != null)
            {
                Int32 nextQuestId = questGroup.GetNextQuestId(questProcessor.Id);
                if (nextQuestId != 0 && !IsQuestRunningOrCompleted(nextQuestId))
                {
                    AcceptQuestInternal(nextQuestId);
                }
                else if (GetTracingQuestGroupId() == questGroup.Id)
                {
                    UpdateTracingQuestGroup();
                }
            }
            questProcessor.UnInit();
        }

        private void CheckAndAcceptQuestInternal(GameEvent e)
        {
            var questMap = ConfigDataManager.instance.questMap;
            foreach(var kv in questMap)
            {
                if (kv.Value == null)
                {
                    continue;
                }
                if (IsQuestRunningOrCompleted(kv.Key))
                {
                    continue;
                }
                if (kv.Value.m_questGroupType != QuestGroupType.None)
                {
                    continue;
                }
                if (ConditionUtility.CheckConditons(kv.Value.AcceptCondition))
                {
                    AcceptQuestInternal(kv.Key);
                }
            }

            var questGroupMap = ConfigDataManager.instance.questGroupMap;
            foreach(var kv in questGroupMap)
            {
                if (kv.Value.AutoBegin)
                {
                    continue;
                }
                if (IsQuestRunningOrCompleted(kv.Value.FirstQuestId))
                {
                    continue;
                }
                if (ConditionUtility.CheckConditons(kv.Value.AcceptCondition))
                {
                    AcceptQuestInternal(kv.Value.FirstQuestId);
                }
            }
        }
        #endregion



        #region FireEvent


        private void FireQuestAcceptEventInternal(QuestProcessor questProcessor)
        {
            EventOnQuestAccept?.Invoke(questProcessor);
        }
        private void FireQuestProgressUpdateEventInternal(QuestProcessor questProcessor, Int32 conditionIndex, Int32 newProgressValue)
        {
            EventOnQuestProgressUpdate?.Invoke(questProcessor, conditionIndex, newProgressValue);
        }
        private void FireQuestStatusChangeEventInternal(QuestProcessor questProcessor, QuestStatus oldStatus)
        {
            EventOnQuestStatusChange?.Invoke(questProcessor, oldStatus);
        }
        private void FireQuestCompletedEventInternal(QuestProcessor questProcessor)
        {
            EventOnQuestCompleted?.Invoke(questProcessor);
        }

        #endregion

    }
}
