

using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    public partial class QuestProcessor
    {
        public void ProcessQuestActions(IList<Int32> actionIds, Boolean pushAction)
        {
            foreach (Int32 questActionId in actionIds)
            {
                ProcessQuestAction(questActionId, pushAction);
            }
        }

        protected void ProcessQuestAction(Int32 questActionId, Boolean pushAction)
        {
            QuestActionConfigData questAction = ConfigDataManager.instance.GetQuestAction(questActionId);
            if (questAction == null)
            {
                return;
            }
            var argvs = questAction.Paramters;
            switch (questAction.ActionId)
            {
                case QuestActionId.AddEntity:
                    if (argvs.Count >= 1)
                    {
                        m_owner.AddWorldEntity(argvs[0]);
                    }
                    break;
                case QuestActionId.RemoveEntity:
                    if (argvs.Count >= 1)
                    {
                        m_owner.RemoveWorldEntity(argvs[0]);
                    }
                    break;
                case QuestActionId.AddEntityInteraction:
                    if (argvs.Count >= 2)
                    {
                        m_owner.AddWorldEntityInteraction(argvs[0], argvs[1]);
                    }
                    break;
                case QuestActionId.RemoveEntityInteraction:
                    if (argvs.Count >= 2)
                    {
                        m_owner.RemoveWorldEntityInteraction(argvs[0], argvs[1]);
                    }
                    break;
                case QuestActionId.ExecuteWorldAction:
                    // 此处为临时写法，事件触发要放在事件模块里，监听任务完成或者接受来抛出事件
                    if (pushAction)
                    {
                        if (argvs.Count >= 1)
                        {
                            GamePlayerContext.instance.ModuleProvider.WorldModule.PushWorldAction(argvs[0]);
                        }
                    }
                    break;
            }
        }

    }
}
