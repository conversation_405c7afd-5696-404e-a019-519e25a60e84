using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using pbc = global::Google.Protobuf.Collections;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.Exchange)]
    public class GamePlayerExchangeModule : GamePlayerModule, IGameEventPipeListener
    {
        public GamePlayerExchangeModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.Exchange;
        }

        protected override void OnInit()
        {
            
        }

        protected override void OnPostInit()
        {
            
        }

        protected override void OnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().AddListener(this);
        }

        protected override void OnUnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().RemoveListener(this);
        }

        protected override void OnUnInit()
        {
        }

        void IGameEventPipeListener.OnEvent(GameEvent e)
        {

        }

        public bool CheckExchangeEnough(pbc::RepeatedField<ExchangeItemCountConfigData> listExchangeItemInfo)
        {
            for (int i = 0; i < listExchangeItemInfo.Count; i++)
            {
                ExchangeItemCountConfigData exchangeItemCountConfigData = listExchangeItemInfo[i];
                ExchangeConfigData exchangeConfigData = ConfigDataManager.instance.GetExchange(exchangeItemCountConfigData.ExchangeId);
                if (exchangeConfigData == null)
                {
                    Debug.LogError($"ExchangeModule.CheckExchangeEnough: exchangeConfigData is null idExplore = {exchangeItemCountConfigData.ExchangeId}");
                    return false;
                }

                return CheckExchangeEnough(exchangeConfigData);
            }

            return true;
        }

        public bool CheckExchangeEnough(ExchangeConfigData exchangeConfigData)
        {
            for (int i = 0; i < exchangeConfigData.ExchangeSourceItemInfo.Count; i++)
            {
                ExchangeSourceItemConfigData exchangeSourceItemConfigData = exchangeConfigData.ExchangeSourceItemInfo[i];
                Int32 itemCurrentCount = GamePlayerContext.instance.BagModule.GetItemCount(exchangeSourceItemConfigData.SourceItemId);
                if (itemCurrentCount < exchangeSourceItemConfigData.Count)
                {
                    return false;
                }
            }
            return true;
        }

    }
}