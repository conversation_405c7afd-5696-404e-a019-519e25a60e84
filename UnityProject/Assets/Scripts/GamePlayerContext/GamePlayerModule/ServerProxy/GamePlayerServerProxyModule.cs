


using System;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.ServerProxy)]
    public class GamePlayerServerProxyModule : GamePlayerModule, IGameEventPipeListener
    {
        public GamePlayerBasicModule m_basicModule;
        public GamePlayerBattleModule m_battleModule;
        public GamePlayerQuestModule m_questModule;
        public GamePlayerBagModule m_bagModule;
        public GamePlayerWorldModule m_worldModule;
        public GamePlayerEventDispatchModule m_eventDispatchModule;

        public GamePlayerServerProxyModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.ServerProxy;
        }

        protected override void OnInit()
        {
            m_basicModule = m_owner.GetPlayerModule<GamePlayerBasicModule>();
            m_battleModule = m_owner.GetPlayerModule<GamePlayerBattleModule>();
            m_questModule = m_owner.GetPlayerModule<GamePlayerQuestModule>();
            m_worldModule = m_owner.GetPlayerModule<GamePlayerWorldModule>();
            m_bagModule = m_owner.GetPlayerModule<GamePlayerBagModule>();
            m_eventDispatchModule = m_owner.GetPlayerModule<GamePlayerEventDispatchModule>();
        }

        protected override void OnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().AddListener(this);
        }

        protected override void OnUnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().RemoveListener(this);
        }

        protected override void OnUnInit()
        {
            m_basicModule = null;
            m_battleModule = null;
            m_questModule = null;
            m_worldModule = null;
            m_bagModule = null;
            m_eventDispatchModule = null;
        }

        void IGameEventPipeListener.OnEvent(GameEvent e)
        {

        }


        public void SendMessageChangeTracingQuestGroupId(Int32 tracingQuestGroupId)
        {
            m_questModule.SetTracingQuestGroupId(tracingQuestGroupId);
        }

        public void SendMessagePlayerTalkEnd(Int32 dialogId)
        {
            m_eventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.TalkEnd, dialogId));
        }
        public void SendMessagePlayerReachArea(Int32 entityRid)
        {
            m_eventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.Reach, entityRid));
        }
        public void SendMessageBattleResult(Int32 battleId, Boolean isWin)
        {

        }
        public void SendMessageGetTreasure(Int32 treasureId)
        {
            m_worldModule.ReceiveWorldTreasure(treasureId);
        }

        public void SendSubmitItemCompleteQuest(ItemInfo item, Int32 questId)
        {
            m_questModule.SetQuestCompleted(questId);
            m_bagModule.RemoveItem(item.m_itemId, item.m_count);
        }
    }
}
