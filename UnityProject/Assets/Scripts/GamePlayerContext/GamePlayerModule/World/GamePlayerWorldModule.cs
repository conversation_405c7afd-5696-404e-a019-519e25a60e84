

using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.World)]
    public class GamePlayerWorldModule : GamePlayerModule, IGameEventPipeListener
    {
        private WorldDataSection m_dataSection;
        private WorldBase m_worldBase;

        public GamePlayerBasicModule m_basicModule;
        public GamePlayerQuestModule m_questModule;
        public GamePlayerEventDispatchModule m_eventDispatchModule;




        public event Action<Int32, List<RewardItem>> EventOnGetTreasureReward;
        public event Action<Int32> EventOnExecuteWorldAction;


        public List<EnterWorldActionBase> m_enterWorldActions = new List<EnterWorldActionBase>();






        public GamePlayerWorldModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.World;
        }

        protected override void OnInit()
        {
            m_dataSection = m_owner.GetDataSection<WorldDataSection>();
            m_basicModule = m_owner.GetPlayerModule<GamePlayerBasicModule>();
            m_questModule = m_owner.GetPlayerModule<GamePlayerQuestModule>();
            m_eventDispatchModule = m_owner.GetPlayerModule<GamePlayerEventDispatchModule>();
        }

        protected override void OnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().AddListener(this);
        }

        protected override void OnUnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().RemoveListener(this);
        }

        protected override void OnUnInit()
        {
            m_dataSection = null;
            m_basicModule = null;
            m_questModule = null;
            m_eventDispatchModule = null;
            m_worldBase = null;
        }

        protected override void OnTick(TimeSlice ts)
        {
        }



        public WorldBase EnterWorld(Int32 worldId)
        {
            if (m_worldBase != null)
            {
                if (m_worldBase.WorldId == worldId)
                {
                    return m_worldBase;
                }
                else
                {
                    ExitWorld(m_worldBase.WorldId);
                }
            }

            WorldInitInfo worldInitInfo = GetWorldInitInfo(worldId);
            if (worldInitInfo != null)
            {
                m_worldBase = new WorldBase();
                m_worldBase.Init(this, worldInitInfo);
                m_worldBase.Enter();
            }

            if (worldId != m_basicModule.GetWorldId())
            {
                m_basicModule.SetWorldId(worldId);
                LocationConfigData location = m_worldBase.WorldConfigData.BornLocation;
                m_basicModule.SetPlayerPosition(location.X, location.Y, location.Z);
            }

            return m_worldBase;
        }
        public WorldBase GetCurrentWorld()
        {
            if (m_worldBase != null)
            {
                return m_worldBase;
            }
            return null;
        }
        public WorldBase GetWorld(Int32 worldId)
        {
            if (m_worldBase != null && m_worldBase.WorldId == worldId)
            {
                return m_worldBase;
            }
            return null;
        }
        public void ExitWorld(Int32 worldId)
        {
            if (m_worldBase != null && m_worldBase.WorldId == worldId)
            {
                m_worldBase.UnInit();
                m_worldBase.Exit();
                m_worldBase = null;
            }
        }


        public void AddWorldEntity(Int32 worldEntityId)
        {
            if (m_worldBase == null || !m_worldBase.IsRunning)
            {
                return;
            }
            WorldEntityConfigData worldEntity = ConfigDataManager.instance.GetWorldEntity(worldEntityId);
            if (worldEntity == null && worldEntity.WorldId != m_worldBase.WorldId)
            {
                return;
            }
            m_worldBase.AddOrRemoveEntity(true, worldEntityId, false);
        }

        public void RemoveWorldEntity(Int32 worldEntityId)
        {
            if (m_worldBase == null || !m_worldBase.IsRunning)
            {
                return;
            }
            WorldEntityConfigData worldEntity = ConfigDataManager.instance.GetWorldEntity(worldEntityId);
            if (worldEntity == null && worldEntity.WorldId != m_worldBase.WorldId)
            {
                return;
            }
            m_worldBase.AddOrRemoveEntity(false, worldEntityId, false);
        }

        public void AddWorldEntityInteraction(Int32 worldEntityId, Int32 interactionId)
        {
            if (m_worldBase == null || !m_worldBase.IsRunning)
            {
                return;
            }
            m_worldBase.AddOrRemoveEntityInteraction(true, worldEntityId, interactionId);
        }

        public void RemoveWorldEntityInteraction(Int32 worldEntityId, Int32 interactionId)
        {
            if (m_worldBase == null || !m_worldBase.IsRunning)
            {
                return;
            }
            m_worldBase.AddOrRemoveEntityInteraction(false, worldEntityId, interactionId);
        }


        public void SetWorldBattleCompleted(Int32 worldBattleId)
        {
            m_dataSection.SetWorldBattleCompleted(worldBattleId);
        }
        public Boolean IsWorldBattleCompleted(Int32 worldBattleId)
        {
            return m_dataSection.IsWorldBattleCompleted(worldBattleId);
        }

        public void ReceiveWorldTreasure(Int32 treasureId)
        {
            WorldEntityConfigData worldEntityConfigData = ConfigDataManager.instance.GetWorldEntity(treasureId);
            if (worldEntityConfigData != null)
            {
                m_dataSection.SetTreasureReceived(treasureId);
                GamePlayerBagModule bagModule = m_owner.GetPlayerModule<GamePlayerBagModule>();
                DropGroupConfigData dropGroupConfigData = ConfigDataManager.instance.GetDropGroup(worldEntityConfigData.Paramter);
                if(dropGroupConfigData != null)
                {
                    bagModule.AddItems(dropGroupConfigData.Rewards);
                    EventOnGetTreasureReward?.Invoke(treasureId, ItemUtility.GetRewardItems(dropGroupConfigData.Rewards));
                }
                RemoveWorldEntity(treasureId);
            }
        }

        public Boolean IsFirstEnterWorld(Int32 worldId)
        {
            return m_dataSection.IsFirstEnterWorld(worldId);
        }

        public void SetEnterWorldFlag(Int32 worldId)
        {
            m_dataSection.SetEnterWorldFlag(worldId);
        }



        public Boolean IsExistEnterWorldAction()
        {
            return m_enterWorldActions.Count > 0;
        }

        public void AddEnterWorldPostAction(EnterWorldActionBase enterWorldPostAction)
        {
            m_enterWorldActions.Add(enterWorldPostAction);
        }
        public List<EnterWorldActionBase> GetEnterWorldActions()
        {
            return m_enterWorldActions;
        }
        public void ResetEnterWorldActions()
        {
            m_enterWorldActions.Clear();
        }

        public void PushWorldAction(Int32 actionGroupId)
        {
            if (m_worldBase != null)
            {
                EventOnExecuteWorldAction?.Invoke(actionGroupId);
            }
            else
            {
                EnterWorldPostActionWorldAction worldAction = new EnterWorldPostActionWorldAction();
                worldAction.m_actionType = EnterWorldActionType.ShowReward;
                worldAction.m_worldActionGroupId = actionGroupId;
                AddEnterWorldPostAction(worldAction);
            }
        }

        #region Internal

        private void CollectWorldEntityNpcInitInfos(Int32 worldId, ref List<WorldEntityInitInfo> entityInitInfos)
        {
            if (entityInitInfos == null)
            {
                entityInitInfos = new List<WorldEntityInitInfo>();
            }
            CollectWorldEntityPlayerInitInfo(ref entityInitInfos);
            CollectWorldEntityInitInfo(worldId, ref entityInitInfos);
        }

        /// <summary> 主角 </summary>
        private void CollectWorldEntityPlayerInitInfo(ref List<WorldEntityInitInfo> entityInitInfos)
        {
            Int32 playerEntityId = m_basicModule.GetPlayerEntityId();
            Int32 playerEntitySkinId = m_basicModule.GetPlayerEntitySkinId();
            LocationConfigData location = new LocationConfigData();
            WorldEntityInitInfo playerEntityInfo = new WorldEntityInitInfo(playerEntityId, playerEntitySkinId, location, 0, WorldEntityType.Player);
            entityInitInfos.Add(playerEntityInfo);
        }

        /// <summary> 箱庭初始配置（默认角色 + 任务添加的角色） </summary>
        private void CollectWorldEntityInitInfo(Int32 worldId, ref List<WorldEntityInitInfo> entityInitInfos)
        {
            // 1 箱庭初始配置
            WorldConfigData worldConfigData = ConfigDataManager.instance.GetWorld(worldId);
            if (worldConfigData != null && worldConfigData.m_entityIds.Count > 0)
            {
                foreach (Int32 entityId in worldConfigData.m_entityIds)
                {
                    if (CheckEntityVisible(entityId))
                    {
                        WorldEntityInitInfo worldEntityInfo = new WorldEntityInitInfo(entityId);
                        entityInitInfos.Add(worldEntityInfo);
                    }
                }
            }

            // 2 进行中的任务初始化配置
            List<Int32> questLinkEntityIds = m_questModule.GetRunningQuestLinkWorldEntity();
            if (questLinkEntityIds != null && questLinkEntityIds.Count > 0)
            {
                foreach (Int32 entityId in questLinkEntityIds)
                {
                    WorldEntityConfigData worldEntityConfigData = ConfigDataManager.instance.GetWorldEntity(entityId);
                    if (worldEntityConfigData != null && worldEntityConfigData.WorldId == worldId)
                    {
                        WorldEntityInitInfo worldEntityInfo = new WorldEntityInitInfo(entityId);
                        entityInitInfos.Add(worldEntityInfo);
                    }
                }
            }

        }


        private Int32 GetActiveWorldStateId(Int32 worldId)
        {
            Int32 stateId = 0;
            WorldConfigData worldConfig = ConfigDataManager.instance.GetWorld(worldId);
            if (worldConfig != null && worldConfig.WorldStates.Count > 0)
            {
                Int32 priority = Int32.MinValue;
                foreach(Int32 worldStateId in worldConfig.WorldStates)
                {
                    WorldStateConfigData worldState = ConfigDataManager.instance.GetWorldState(worldStateId);
                    if (worldState != null)
                    {
                        if (ConditionUtility.CheckConditons(worldState.Conditions))
                        {
                            if (priority < worldState.Priority)
                            {
                                priority = worldState.Priority;
                                stateId = worldState.Id;
                            }
                        }
                    }
                }
            }
            return stateId;
        }


        private Boolean CheckEntityVisible(Int32 entityRid)
        {
            WorldEntityConfigData config = ConfigDataManager.instance.GetWorldEntity(entityRid);
            if (config.EntityType == WorldEntityType.Treasure)
            {
                if (m_dataSection.IsTreasureReceived(entityRid))
                {
                    return false;
                }
            }
            else if (config.EntityType == WorldEntityType.Monster)
            {
                if (m_dataSection.IsWorldBattleCompleted(config.Paramter))
                {
                    return false;
                }
            }
            if (!ConditionUtility.CheckConditons(config.ShowCondition))
            {
                return false;
            }
            return true;
        }

        private WorldInitInfo GetWorldInitInfo(Int32 worldId)
        {
            WorldInitInfo worldInitInfo = new WorldInitInfo();
            worldInitInfo.worldId = worldId;
            CollectWorldEntityNpcInitInfos(worldId, ref worldInitInfo.entityInitInfos);
            worldInitInfo.worldStateId = GetActiveWorldStateId(worldId);
            worldInitInfo.npcInitInteractionIds = m_questModule.GetRunningQuestLinkWorldEntityInteraction();
            return worldInitInfo;
        }


        #endregion


        void IGameEventPipeListener.OnEvent(GameEvent e)
        {
            if (e.m_eventId == GameEventId.QuestAccept
                || e.m_eventId == GameEventId.QuestCompleted
                || e.m_eventId == GameEventId.WorldEntityExploreUpdate)
            {
                CheckWorldEntityInteractionShowState(e);
                CheckWorldEntityShowState(e);
                CheckWorldState(e);
            }
        }

        /// <summary> 在角色身上的交互行为显示和隐藏逻辑 </summary>
        private void CheckWorldEntityInteractionShowState(GameEvent e)
        {
            if (m_worldBase != null)
            {
                foreach (WorldEntity entity in m_worldBase.m_worldEntitys)
                {
                    if (entity is WorldEntityNpc entityNpc)
                    {
                        entityNpc.UpdateEntityInteractionVisibleState();
                    }
                }
            }
        }


        private void CheckWorldEntityShowState(GameEvent e)
        {
            if (m_worldBase != null)
            {
                WorldConfigData worldConfigData = ConfigDataManager.instance.GetWorld(m_worldBase.WorldId);
                if (worldConfigData != null && worldConfigData.m_entityIds.Count > 0)
                {
                    foreach (Int32 entityId in worldConfigData.m_entityIds)
                    {
                        Boolean visible = CheckEntityVisible(entityId);
                        WorldEntity worldEntity = m_worldBase.GetWorldEntity(entityId);
                        if (visible ^ worldEntity != null)
                        {
                            m_worldBase.AddOrRemoveEntity(visible, entityId, true);
                        }
                    }
                }
            }
        }

        private void CheckWorldState(GameEvent e)
        {
            if (m_worldBase != null)
            {
                Int32 activeWorldStateId = GetActiveWorldStateId(m_worldBase.WorldId);
                if (m_worldBase.m_worldStateId != activeWorldStateId)
                {
                    m_worldBase.SetWorldState(activeWorldStateId);
                }
            }
        }
    }
}
