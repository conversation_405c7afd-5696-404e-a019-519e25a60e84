
using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    public class WorldEntityInteraction
    {
        public Int32 Id;
        public String Name;
        public EntityInteractionId InteractionType;
        public Boolean AutoFlag;
        public Int32 RelationQuestId;
        public List<int> Paramters = new List<int>();

        public Boolean Dynamic { get; private set; }
        public Boolean Visible { get; private set; }


        public void Init(EntityInteractionConfigData interactionConfigData, Boolean dynamic)
        {
            Id = interactionConfigData.Id;
            Name = interactionConfigData.Name;
            InteractionType = interactionConfigData.InteractionType;
            AutoFlag = interactionConfigData.AutoFlag;
            RelationQuestId = interactionConfigData.RelationQuestId;
            Paramters = new List<int>(interactionConfigData.Paramters);
            Dynamic = dynamic;
            UpdateInteractionVisibleState();
        }


        public Int32 GetParamValue(Int32 index, Int32 defaultValue = 0)
        {
            if (Paramters.Count > index)
            {
                return Paramters[index];
            }
            return defaultValue;
        }

        private Int32 m_randomIndex = 0;
        public Int32 GetRandomIndexValue()
        {
            Int32 count = Paramters.Count;
            if (count > 0)
            {
                return Paramters[m_randomIndex++ % count];
            }
            return 0;
        }

        public Boolean UpdateInteractionVisibleState()
        {
            Boolean visibleStateChange = false;
            if (Dynamic)
            {
                Visible = true;
            }
            else
            {
                Boolean visible;
                EntityInteractionConfigData interactionConfigData = ConfigDataManager.instance.GetEntityInteraction(Id);
                if (interactionConfigData != null )
                {
                    visible = ConditionUtility.CheckConditons(interactionConfigData.ShowConditions);
                }
                else
                {
                    visible = true;
                }

                if (visible ^ Visible)
                {
                    visibleStateChange = true;
                    Visible = visible;
                }
            }
            return visibleStateChange;
        }
    }
}
