

using System;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    public class WorldEntityInitInfo
    {
        public WorldEntityType entityType;
        public Int32 rid;
        public Int32 skinId;
        public LocationConfigData location;
        public Int32 direction;
        public String defaultAnimation;

        public WorldEntityInitInfo(int rid, int skinId, LocationConfigData location, int direction, WorldEntityType entityType)
        {
            this.rid = rid;
            this.skinId = skinId;
            this.location = location;
            this.direction = direction;
            this.entityType = entityType;
            this.defaultAnimation = "NormalIdle";
        }
        public WorldEntityInitInfo(Int32 rid)
        {
            this.rid = rid;
            WorldEntityConfigData worldEntityConfigData = ConfigDataManager.instance.GetWorldEntity(rid);
            if (worldEntityConfigData != null)
            {
                entityType = worldEntityConfigData.EntityType;
                skinId = worldEntityConfigData.EntitySkinId;
                location = worldEntityConfigData.Location;
                direction = worldEntityConfigData.Direction;
                defaultAnimation = worldEntityConfigData.AnimationName;
            }
        }

        public override string ToString()
        {
            return string.Format($"{rid}, {skinId}");
        }

    }
}
