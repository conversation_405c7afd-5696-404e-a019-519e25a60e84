

using System;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    public class WorldBase
    {
        private GamePlayerWorldModule m_ownerHandler;

        private Boolean m_isRunning = false;
        private Int32 m_worldId = 0;
        public Int32 m_worldStateId = 0;
        public List<WorldEntity> m_worldEntitys = new List<WorldEntity>();



        public event Action<WorldEntity, Boolean> EventOnWorldEntityAdd;
        public event Action<Int32, Boolean> EventOnWorldEntityRemove;
        public event Action<Int32, WorldEntityInteraction> EventOnWorldEntityInteractionAdd;
        public event Action<Int32, Int32> EventOnWorldEntityInteractionRemove;
        public event Action<Int32> EventOnWorldStateChange;
        public event Action<Int32> EventOnWorldTransformationToOther;
        public event Action EventOnWorldTransformationRevert;






        public Boolean IsRunning { get { return m_isRunning; } }
        public Int32 WorldId { get { return m_worldId; } }
        public Int32 PlayerEntityId { get { return m_ownerHandler.m_basicModule.GetPlayerEntityId(); } }
        public WorldConfigData WorldConfigData { get; private set; }


        public void Init(GamePlayerWorldModule handler, WorldInitInfo worldInitInfo)
        {
            m_ownerHandler = handler;
            m_worldId = worldInitInfo.worldId;
            m_worldStateId = worldInitInfo.worldStateId;
            WorldConfigData = ConfigDataManager.instance.GetWorld(worldInitInfo.worldId);

            foreach (WorldEntityInitInfo entityInfo in worldInitInfo.entityInitInfos)
            {
                WorldEntity worldEntity = CreateWorldEntityInternal(entityInfo);
                PostInitWorldEntityNpcInteraction(worldEntity, worldInitInfo);
            }
        }

        public void Enter()
        {
            m_isRunning = true;
        }

        public void UnInit()
        {
            m_worldId = 0;
            WorldConfigData = null;
            m_worldEntitys.Clear();
            EventOnWorldEntityAdd = null;
            EventOnWorldEntityRemove = null;
            EventOnWorldEntityInteractionAdd = null;
            EventOnWorldEntityInteractionRemove = null;
            EventOnWorldTransformationToOther = null;
            EventOnWorldTransformationRevert = null;
            m_ownerHandler = null;
        }

        public void Exit()
        {
            m_isRunning = false;
        }

        public void AddOrRemoveEntity(Boolean add, Int32 entityId, Boolean immediately)
        {
            if (add)
            {
                AddEntityInternal(entityId, immediately);
            }
            else
            {
                RemoveEntityInternal(entityId, immediately);
            }
        }

        public void AddOrRemoveEntityInteraction(Boolean add, Int32 entityId, Int32 entityInteractionId)
        {
            if (add)
            {
                AddEntityInteractionInternal(entityId, entityInteractionId);
            }
            else
            {
                RemoveEntityInteractionInternal(entityId, entityInteractionId);
            }
        }

        public void TransformationToOther(Int32 transformationSkinId)
        {
            TransformationToOtherInternal(transformationSkinId);
        }

        public void TransformationRevert()
        {
            TransformationRevertInternal();
        }

        public WorldEntity GetWorldEntity(Int32 entityId)
        {
            return GetWorldEntityInternal(entityId);
        }

        public void SetWorldState(Int32 worldStateId)
        {
            if (worldStateId != m_worldStateId)
            {
                m_worldStateId = worldStateId;
                EventOnWorldStateChange?.Invoke(m_worldStateId);
            }
        }

        #region Internal

        protected void AddEntityInternal(Int32 entityId, Boolean immediately)
        {
            WorldEntityInitInfo worldEntityInfo = new WorldEntityInitInfo(entityId);
            WorldEntity worldEntity = CreateWorldEntityInternal(worldEntityInfo);
            EventOnWorldEntityAdd?.Invoke(worldEntity, immediately);
        }

        protected void RemoveEntityInternal(Int32 entityId, Boolean immediately)
        {
            Boolean result = DeleteWorldEntityInternal(entityId);
            if (result)
            {
                EventOnWorldEntityRemove?.Invoke(entityId, immediately);
            }
        }

        protected void TransformationToOtherInternal(Int32 transformationSkinId)
        {
            EventOnWorldTransformationToOther?.Invoke(transformationSkinId);
        }

        protected void TransformationRevertInternal()
        {
            EventOnWorldTransformationRevert?.Invoke();
        }

        protected void AddEntityInteractionInternal(Int32 entityId, Int32 entityInteractionId)
        {
            WorldEntity worldEntity = GetWorldEntityInternal(entityId);
            if (worldEntity is WorldEntityNpc entityNpc)
            {
                WorldEntityInteraction interaction = entityNpc.AddEntityInteraction(entityInteractionId, true);
                if (interaction != null)
                {
                    EventOnWorldEntityInteractionAdd?.Invoke(entityId, interaction);
                }
            }
        }

        protected void RemoveEntityInteractionInternal(Int32 entityId, Int32 entityInteractionId)
        {
            WorldEntity worldEntity = GetWorldEntityInternal(entityId);
            if (worldEntity is WorldEntityNpc entityNpc)
            {
                entityNpc.RemoveEntityInteraction(entityInteractionId);
                EventOnWorldEntityInteractionRemove?.Invoke(entityId, entityInteractionId);
            }
        }


        protected WorldEntity GetWorldEntityInternal(Int32 entityRid)
        {
            foreach(WorldEntity entity in m_worldEntitys)
            {
                if (entity.Rid == entityRid)
                {
                    return entity;
                }
            }
            return null;
        }

        protected WorldEntity CreateWorldEntityInternal(WorldEntityInitInfo worldEntityInfo)
        {
            WorldEntity worldEntity = GetWorldEntityInternal(worldEntityInfo.rid);
            if (worldEntity != null)
            {
                DebugUtility.LogError($"角色重复添加报错 id={worldEntityInfo.rid}");
                return worldEntity;
            }
            worldEntity = WorldEntityFactory.Create(worldEntityInfo) as WorldEntity;
            m_worldEntitys.Add(worldEntity);
            return worldEntity;
        }


        protected Boolean DeleteWorldEntityInternal(Int32 rid)
        {
            WorldEntity worldEntity = GetWorldEntityInternal(rid);
            if (worldEntity != null)
            {
                m_worldEntitys.Remove(worldEntity);
                return true;
            }
            else
            {
                DebugUtility.LogError($"角色不存在 id={rid}");
                return false;
            }
        }

        protected void PostInitWorldEntityNpcInteraction(WorldEntity worldEntity, WorldInitInfo worldInitInfo)
        {
            if (worldEntity.EntityType == WorldEntityType.Npc || worldEntity.EntityType == WorldEntityType.Monster)
            {
                WorldEntityNpc worldEntityNpc = worldEntity as WorldEntityNpc;
                if (worldEntityNpc != null)
                {
                    if (worldInitInfo.npcInitInteractionIds.TryGetValue(worldEntityNpc.Rid, out List<Int32> entityInteractionIds))
                    {
                        foreach (Int32 interactionId in entityInteractionIds)
                        {
                            worldEntityNpc.AddEntityInteraction(interactionId, true);
                        }
                    }
                }
            }

        }

        #endregion
    }

    public class WorldInitInfo
    {
        public Int32 worldId;
        public List<WorldEntityInitInfo> entityInitInfos;
        public Dictionary<Int32, List<Int32>> npcInitInteractionIds;
        public Int32 worldStateId;
    }

}
