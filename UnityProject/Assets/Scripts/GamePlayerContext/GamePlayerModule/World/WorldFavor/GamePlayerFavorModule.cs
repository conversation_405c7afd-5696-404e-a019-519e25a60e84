using System;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.ConfigData;
using pbc = global::Google.Protobuf.Collections;

namespace Phoenix.GameLogic.GameContext
{
    public enum FavorTapShow
    {
        ShowAll,
        HideExplore, // 隐藏情报按钮&页签
        HideExchange, // 隐藏异物按钮&页签
        HideExploreAndExchange, // 隐藏情报和异物按钮&页签
    }

    [GamePlayerModule(GameModuleId.Favor)]
    public class GamePlayerFavorModule : GamePlayerModule, IGameEventPipeListener
    {
        private FavorDataSection m_favorDataSection;
        private FavorData m_curFavorData;

        private FavorTapShow m_curFavorTapShow = FavorTapShow.ShowAll;
        public FavorTapShow EFavorTapShow { get { return m_curFavorTapShow; } set { m_curFavorTapShow = value; } }

        public GamePlayerFavorModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.Favor;
        }

        protected override void OnInit()
        {
            m_favorDataSection = m_owner.GetDataSection<FavorDataSection>();
        }

        protected override void OnPostInit()
        {
            InitFavorData();
        }

        protected override void OnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().AddListener(this);
        }

        protected override void OnUnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().RemoveListener(this);
        }

        protected override void OnUnInit()
        {
        }

        void IGameEventPipeListener.OnEvent(GameEvent e)
        {

        }

        public FavorData CurFavorData { get { return m_curFavorData; } set { m_curFavorData = value; } }

        private void InitFavorData()
        {
            Dictionary<string, FavorData> dictFavorData = GetFavorDatas();
            foreach (KeyValuePair<string, FavorData> kv in dictFavorData)
            {
                if (kv.Value.m_count > 0)
                {
                    continue;
                }
                FavorConfigData favorConfigData = ConfigDataManager.instance.GetFavor(kv.Value.m_favorId);
                for (int i = 0; i < favorConfigData.QuestProvideInfo.Count; i++)
                {
                    QuestProvideConfigData questProvideInfo = favorConfigData.QuestProvideInfo[i];
                    bool isCompleted = GamePlayerContext.instance.QuestModule.IsQuestCompleted(questProvideInfo.QuestId);
                    if (isCompleted)
                    {
                        kv.Value.m_count = kv.Value.m_count + questProvideInfo.FavorCount;
                        if (kv.Value.m_count > GetMaxFavorCount())
                        {
                            kv.Value.m_count = GetMaxFavorCount();
                        }
                    }
                }
            }
        }

        public Dictionary<string, FavorData> GetFavorDatas()
        {
            return m_favorDataSection.GetFavorDatas();
        }

        public FavorData GetFavorData(Int32 idFavor, Int32 idEntity = -1)
        {
            return m_favorDataSection.GetFavorData(idFavor, idEntity);
        }

        public void AddFavorData(Int32 idFavor, Int32 idEntity)
        {
            m_favorDataSection.AddFavorData(idFavor, idEntity);
        }

        public void AddFavorCount(Int32 idFavor, Int32 idEntity, Int32 count)
        {
            m_favorDataSection.AddFavorCount(idFavor, idEntity, count);
        }

        public int GetFavorCount(Int32 idFavor, Int32 idEntity)
        {
            return m_favorDataSection.GetFavorCount(idFavor, idEntity);
        }

        public int GetMaxFavorCount()
        {
            return m_favorDataSection.GetMaxFavorCount();
        }

        public void UpdateFavorExploreStatus(Int32 idFavor, Int32 idEntity, Int32 idExplore, Int32 status)
        {
            m_favorDataSection.UpdateFavorExploreStatus(idFavor, idEntity, idExplore, status);
            m_owner.ModuleProvider.EventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.WorldEntityExploreUpdate, idExplore, status));
        }

        public bool IsFavorExploreStatusNone(Int32 idExplore)
        {
            return m_favorDataSection.IsFavorExploreStatusNone(idExplore);
        }

        public bool IsFavorExploreStatusShowTip(Int32 idExplore)
        {
            return m_favorDataSection.IsFavorExploreStatusShowTip(idExplore);
        }

        public bool IsFavorExploreStatusFinish(Int32 idExplore)
        {
            return m_favorDataSection.IsFavorExploreStatusFinish(idExplore);
        }

        public void UpdateFavorExchangeStatus(Int32 idFavor, Int32 idEntity, Int32 idExchange, Int32 status)
        {
            m_favorDataSection.UpdateFavorExchangeStatus(idFavor, idEntity, idExchange, status);
            m_owner.ModuleProvider.EventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.WorldEntityExploreUpdate, idExchange, status));
        }

        public bool IsFavorExchangeStatusNone(Int32 idExchange)
        {
            return m_favorDataSection.IsFavorExchangeStatusNone(idExchange);
        }

        public bool IsFavorExchangeStatusShowTip(Int32 idExchange)
        {
            return m_favorDataSection.IsFavorExchangeStatusShowTip(idExchange);
        }

        public bool IsFavorExchangeStatusFinish(Int32 idExchange)
        {
            return m_favorDataSection.IsFavorExchangeStatusFinish(idExchange);
        }

        public bool IsAllFavorExchangeStatusFinish(pbc::RepeatedField<ExchangeItemCountConfigData> listExchangeItemInfo, Int32 idFavor, Int32 idEntity = -1)
        {
            return m_favorDataSection.IsAllFavorExchangeStatusFinish(listExchangeItemInfo, idFavor, idEntity);
        }

        public void UpdateFavorExchangeCount(Int32 idFavor, Int32 idEntity, Int32 idExchange, Int32 count)
        {
            m_favorDataSection.UpdateFavorExchangeCount(idFavor, idEntity, idExchange, count);
        }

        public void RemoveFavorData(Int32 idFavor, Int32 idEntity)
        {
            m_favorDataSection.RemoveFavorData(idFavor, idEntity);
        }

        public void ClearFavorData()
        {
            m_tempFavorCount = -1;
            m_favorDataSection.ClearFavorData();
        }


        public bool CheckInteractionType(Int32 idEntity)
        {
            WorldEntityConfigData worldEntityConfigData = ConfigDataManager.instance.GetWorldEntity(idEntity);
            if (worldEntityConfigData != null)
            {
                if (worldEntityConfigData.InteractionTypeCondition.Count > 0)
                {
                    for (int i = 0; i < worldEntityConfigData.InteractionTypeCondition.Count; i++)
                    {
                        InteractionConditionConfigData conditionConfigData = worldEntityConfigData.InteractionTypeCondition[i];
                        if (conditionConfigData.InteractionMode == InteractionType.Favor)
                        {
                            AddFavorData(conditionConfigData.InteractionId, idEntity);
                            return CheckUnlockFavor(conditionConfigData.InteractionId);
                        }
                    }
                }
            }

            return false;
        }

        public FavorData GetFavorData(Int32 idEntity)
        {
            FavorData data = null;
            WorldEntityConfigData worldEntityConfigData = ConfigDataManager.instance.GetWorldEntity(idEntity);
            if (worldEntityConfigData != null)
            {
                if (worldEntityConfigData.InteractionTypeCondition.Count > 0)
                {
                    for (int i = 0; i < worldEntityConfigData.InteractionTypeCondition.Count; i++)
                    {
                        InteractionConditionConfigData conditionConfigData = worldEntityConfigData.InteractionTypeCondition[i];
                        if (conditionConfigData.InteractionMode == InteractionType.Favor)
                        {
                            data = GetFavorData(conditionConfigData.InteractionId, idEntity);
                            break;
                        }
                    }
                }
            }

            return data;
        }

        public bool CheckUnlockFavor(Int32 idFavor)
        {
            FavorConfigData favorConfigData = ConfigDataManager.instance.GetFavor(idFavor);
            if (favorConfigData == null)
            {
                Debug.LogError($"CheckUnlockFavor: favorConfigData is null idFavor = {idFavor}");
                return false;
            }

            EFavorTapShow = FavorTapShow.ShowAll;
            if (favorConfigData.ExploreClueInfo.Count == 0 && favorConfigData.ExchangeItemInfo.Count == 0)
            {
                EFavorTapShow = FavorTapShow.HideExploreAndExchange;
            }
            else if (favorConfigData.ExploreClueInfo.Count == 0)
            {
                EFavorTapShow = FavorTapShow.HideExplore;
            }
            else if (favorConfigData.ExchangeItemInfo.Count == 0)
            {
                EFavorTapShow = FavorTapShow.HideExchange;
            }

            bool isUnlock = true;

            for (int j = 0; j < favorConfigData.UnlockConditionInfo.Count; j++)
            {
                UnlockConditionConfigData unlockCondition = favorConfigData.UnlockConditionInfo[j];
                if (unlockCondition.ConditionType == InteractionType.Transformation)
                {
                    if (GamePlayerContext.instance.TransformationModule.IsTransformation())
                    {
                        Int32 skinId = GamePlayerContext.instance.TransformationModule.GetCurTransformationSkinId();
                        TransformationConfigData transformationConfigData = ConfigDataManager.instance.GetTransformation(unlockCondition.ConfigId);
                        if (skinId != transformationConfigData.EntitySkinId)
                        {
                            isUnlock = false;
                            break;
                        }
                    }
                    else
                    {
                        isUnlock = false;
                    }
                }
            }
            return isUnlock;
        }

        #region GM
        private Int32 m_tempFavorCount = -1;
        public void SetAllFavorCount(Int32 count)
        {
            m_tempFavorCount = count;
            Dictionary<string, FavorData> dictFavorData = GetFavorDatas();
            foreach (KeyValuePair<string, FavorData> kv in dictFavorData)
            {
                kv.Value.m_count = m_tempFavorCount;
            }
        }

        public Int32 GetTempFavorCount()
        {
            return m_tempFavorCount;
        }

        #endregion

    }
}

