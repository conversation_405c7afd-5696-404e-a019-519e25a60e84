

using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    public class GamePlayerModule
    {
        public IGamePlayerContext m_owner;
        public GameModuleId m_moduleId;
        public GamePlayerModule(IGamePlayerContext playerContext)
        {
            m_owner = playerContext;
        }


        public void Init()
        {
            OnInit();
            OnRegisterListener();
        }

        public void PostInit()
        {
            OnPostInit();
        }

        public void Tick(TimeSlice ts)
        {
            OnTick(ts);
        }

        public void UnInit()
        {
            OnUnRegisterListener();
            OnUnInit();
        }

        protected virtual void OnInit() { }
        protected virtual void OnPostInit() { }
        protected virtual void OnRegisterListener() { }
        protected virtual void OnTick(TimeSlice ts) { }
        protected virtual void OnUnRegisterListener() { }
        protected virtual void OnUnInit() { }
    }
}
