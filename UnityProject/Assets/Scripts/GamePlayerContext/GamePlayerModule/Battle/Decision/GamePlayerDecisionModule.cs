using System;
using System.Collections.Generic;
using Phoenix.GameLogic.UI;
using Phoenix.ConfigData;
using UnityEngine;
using UnityEngine.Events;
using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.Decision)]
    public class GamePlayerDecisionModule : GamePlayerModule
    {
        private DecisionDataSection m_dataSection;

        public GamePlayerDecisionModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.Decision;
        }

        protected override void OnInit()
        {
            m_dataSection = m_owner.GetDataSection<DecisionDataSection>();
        }

        public Boolean NotRecommendDecisionData(Int32 decisionId)
        {
            return NotRecommendDecisionData(m_dataSection.GetDecisionData(decisionId));
        }

        public Boolean NotRecommendDecisionData(DecisionData data)
        {
            for(int i = 0; i < data.m_orders.Count; i++)
            {
                if((TeamDecisionOriginSelectId)data.m_orders[i].m_actorRid == 0)
                {
                    return true;
                }
            }
            return false;
        }

        public Int32 MaxCount {
            get {
                return m_dataSection.MaxCount;
            }
        }

        public Int32 MaxFormationCount(int formationId) 
        {
            return m_dataSection.MaxFormationCount(formationId);
        }

        public Int32 GetApplayCount(int decisionId)
        {
            return m_dataSection.GetApplayCount(decisionId);
        }

        public Boolean IsMax()
        {
            return m_dataSection.IsMax();
        }

        public Boolean IsMaxFormation(Int32 formationId)
        {
            return m_dataSection.IsMaxFormation(formationId);
        }

        public void GetEditDecisionDatas(out List<DecisionData> decisionDatas, out List<DecisionData> chooseDecisionDatas, Int32 formationId = -1)
        {
            m_dataSection.GetEditDecisionDatas(out decisionDatas, out chooseDecisionDatas, formationId);
        }

        public List<DecisionData> GetLoadDecisionDatas(Int32 formationId = -1)
        {
            return m_dataSection.GetFormationDecisionDatas(formationId);
        }

        public List<DecisionData> GetStoreDecisionDatas()
        {
            return m_dataSection.GetDecisionDatas();
        }

        public DecisionData GetDecisionData(Int32 decisionId)
        {
            return m_dataSection.GetDecisionData(decisionId);
        }

        public DecisionData CreateDecisionData()
        {
            return m_dataSection.CreateDecisionData();
        }

        public DecisionOrderData CreateDecisionOrderData()
        {
            return m_dataSection.CreateDecisionOrderData();
        }

        public void SaveDecisionData(DecisionData data)
        {
            Boolean isAdd = data.m_decisionId == 0;
            Int32 result = isAdd ? m_dataSection.AddDecisionData(data) : m_dataSection.UpdateDecisionData(data);
            
            if(result == 0)
            {
                TipUI.ShowTip("保存成功");
                EventOnDecisionDataSave?.Invoke(data, isAdd);
            }
            else
            {
                //todo 显示提示文字
            }
        }

        public void UpdateDecisionDataName(Int32 decisionId, String name)
        {
            if(m_dataSection.UpdateDecisionDataName(decisionId, name))
            {
                EventOnDecisionSaveName?.Invoke(decisionId, name);
            }
            else
            {
                //todo 显示提示文字
            }
        }

        public void DeleteDecisionData(DecisionData data)
        {
            m_dataSection.DeleteDecisionData(data.m_decisionId);
            EventOnDecisionDataDelete(data.m_decisionId);
        }

        private void ConfirmLoad(Int32 decisionId, Int32 formationId = -1)
        {
            int result = m_dataSection.AddToFromation(formationId, decisionId);
            
            if(result == 0)
            {
                EventOnLoadDecisionData?.Invoke(GetDecisionData(decisionId));
            }
            else
            {
                Debug.LogError($"GamePlayerDecisionModule.ConfirmAddToSelect decisionId{decisionId} formationId{formationId} result{result}");
            }
        }

        public void LoadDecision(Int32 decisionId, Int32 formationId = -1)
        {
            if(formationId == -1)
            {
                if(IsMaxFormation(formationId))
                {
                    TipUI.ShowTip("公共战术槽已满，请先卸下已装配的战术");
                    return;
                }
            }
            else
            {
                if(IsMaxFormation(formationId))
                {
                    TipUI.ShowTip("预设战术槽已满，请先卸下已装配的战术");
                    return;
                }
            }

            if(NotRecommendDecisionData(decisionId))
            {
                String desc = "该战术包含特定角色，若设定为公共战术可能会导致部分指定不生效。";
                String title = "提示";
                String confirmStr = "确认";
                MessageBox.Show(desc, title, confirmStr, ()=>{
                    ConfirmLoad(decisionId, formationId);
                }, String.Empty, null, MessageBoxType.Normal);
                return;
            }
            
            ConfirmLoad(decisionId, formationId);
        }

        public void UnLoadDecision(Int32 decisionId, Int32 formationId = -1)
        {
            int result = m_dataSection.RemoveFromFormation(formationId, decisionId);

            if(result == 0)
            {
                EventOnUnLoadDecisionData(GetDecisionData(decisionId));
            }
            else
            {
                Debug.LogError($"GamePlayerDecisionModule.RemoveFromSelected decisionId{decisionId} formationId{formationId} result{result}");
            }
        }

        public void SaveLoadDecisionDatas(Int32 formationId, List<DecisionData> datas)
        {
            m_dataSection.SaveLoadDecisionDatas(formationId, datas);
        }

        public Boolean IsChange(DecisionData data)
        {
            if(data.m_decisionId == 0)
            {
                return true;
            }
            var oriData = m_dataSection.GetDecisionData(data.m_decisionId);
            return !data.Equals(oriData);
        }

        public String ShareDecision(DecisionData data)
        {
            string json = null;
            var decisionId = data.m_decisionId;
            data.m_decisionId = 0;
            json = JsonUtility.ToJson(data);
            data.m_decisionId = decisionId;
            return json;
        }

        public DecisionData CopyDecision(DecisionData data)
        {
            var newData = (DecisionData)data.Clone();
            newData.m_decisionId = 0;
            return newData;
        }

        public DecisionData CopyDecision(String json)
        {
            return JsonUtility.FromJson<DecisionData>(json);
        }
        
        public string DecisionData2Base64(DecisionData data)
        {
            var json = JsonUtility.ToJson(data);
            var bytes = System.Text.Encoding.UTF8.GetBytes(json);
            return Convert.ToBase64String(bytes);
        }

        public DecisionData Base642DecisionData(string strBase64)
        {
            var bytes = Convert.FromBase64String(strBase64);
            var json = System.Text.Encoding.UTF8.GetString(bytes);
            return JsonUtility.FromJson<DecisionData>(json);
        }

        public event Action<DecisionData, Boolean> EventOnDecisionDataSave;
        public event Action<Int32, String> EventOnDecisionSaveName;
        public event Action<Int32> EventOnDecisionDataDelete;
        public event Action<DecisionData> EventOnLoadDecisionData;
        public event Action<DecisionData> EventOnUnLoadDecisionData;

    }
}
