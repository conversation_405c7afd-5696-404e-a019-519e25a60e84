


using System;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.Bag)]
    public class GamePlayerBagModule : GamePlayerModule, IGameEventPipeListener
    {
        private BagDataSection m_bagData;

        public GamePlayerBasicModule m_basicModule;
        public GamePlayerQuestModule m_questModule;
        public GamePlayerEventDispatchModule m_eventDispatchModule;

        public GamePlayerBagModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.Bag;
        }

        protected override void OnInit()
        {
            m_bagData = m_owner.GetDataSection<BagDataSection>();
            m_basicModule = m_owner.GetPlayerModule<GamePlayerBasicModule>();
            m_questModule = m_owner.GetPlayerModule<GamePlayerQuestModule>();
            m_eventDispatchModule = m_owner.GetPlayerModule<GamePlayerEventDispatchModule>();


            //m_bagData.AddItem(10001, 2);
            //m_bagData.AddItem(10002, 3);
            //m_bagData.AddItem(10003, 7);
            //m_bagData.AddItem(10001, 2);
            //m_bagData.AddItem(10002, 3);
            //m_bagData.AddItem(10003, 7);
            //m_bagData.AddItem(10001, 2);
            //m_bagData.AddItem(10002, 3);
            //m_bagData.AddItem(10003, 7);
        }

        protected override void OnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().AddListener(this);
        }

        protected override void OnUnRegisterListener()
        {
            m_owner.GetPlayerModule<GamePlayerEventDispatchModule>().RemoveListener(this);
        }

        protected override void OnUnInit()
        {
            m_bagData = null;
            m_basicModule = null;
            m_questModule = null;
            m_eventDispatchModule = null;
        }

        void IGameEventPipeListener.OnEvent(GameEvent e)
        {

        }


        public BagItem GetItem(Int32 id)
        {
            return m_bagData.GetItem(id);
        }
        public Int32 GetItemCount(Int32 id)
        {
            BagItem item = m_bagData.GetItem(id);
            if (item != null)
            {
                return item.m_count;
            }
            return 0;
        }

        public List<BagItem> GetItems()
        {
            return m_bagData.GetItems();
        }

        public void AddItems(Int32 dropId)
        {
            DropGroupConfigData dropGroupConfigData = ConfigDataManager.instance.GetDropGroup(dropId);
            if (dropGroupConfigData != null)
            {
                AddItems(dropGroupConfigData.Rewards);
            }
        }

        public void AddItems(IList<RewardConfigData> rewards)
        {
            if (rewards == null || rewards.Count == 0)
            {
                return;
            }

            foreach(RewardConfigData item in rewards)
            {
                AddItem(item.ItemId, item.Count);
            }
        }
        public void AddItem(Int32 itemId, Int32 count)
        {
            m_bagData.AddItem(itemId, count);
            m_eventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.ItemIncrease, itemId, count));
        }

        public void RemoveItem(Int32 itemId, Int32 count)
        {
            m_bagData.RemoveItem(itemId, count);
            m_eventDispatchModule.PushEvent(GameEventDefault.Gen(GameEventId.ItemDecrease, itemId, count));
        }

        public void AddAllItem4GM(Int32 count)
        {
            foreach(var kv in ConfigDataManager.instance.itemMap)
            {
                AddItem(kv.Value.Id, count);
            }
        }

    }
}
