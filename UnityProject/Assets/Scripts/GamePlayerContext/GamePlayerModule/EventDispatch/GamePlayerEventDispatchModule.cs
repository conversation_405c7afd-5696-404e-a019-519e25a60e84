

using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    [GamePlayerModule(GameModuleId.EventDispatch)]
    public class GamePlayerEventDispatchModule : GamePlayerModule
    {

        public GameEventPipe m_pipe = new GameEventPipe();


        public GamePlayerEventDispatchModule(IGamePlayerContext playerContext) : base(playerContext)
        {
            m_moduleId = GameModuleId.EventDispatch;
        }

        public void AddListener(IGameEventPipeListener listener)
        {
            m_pipe.AddListener(listener);
        }
        
        public void RemoveListener(IGameEventPipeListener listener)
        {
            m_pipe.RemoveListener(listener);
        }

        public void PushEvent(GameEvent e)
        {
            m_pipe.PushEvent(e);
        }

        protected override void OnTick(TimeSlice ts)
        {
            m_pipe.Tick(ts);
        }
    }
}
