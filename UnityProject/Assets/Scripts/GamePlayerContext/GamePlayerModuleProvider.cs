
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.GameLogic.GameContext
{
    public class GamePlayerModuleProvider
    {
        public GamePlayerEventDispatchModule EventDispatchModule;
        public GamePlayerServerProxyModule ServerProxyModule;
        public GamePlayerBasicModule BasicModule;
        public GamePlayerQuestModule QuestModule;
        public GamePlayerWorldModule WorldModule;
        public GamePlayerBattleModule BattleModule;
        public GamePlayerBagModule BagModule;
        public GamePlayerSettingModule SettingModule;
        public GamePlayerRewardModule RewardModule;
        public GamePlayerFavorModule FavorModule;
        public GamePlayerTransformationModule TransformationModule;
        public GamePlayerDecisionModule DecisionModule;
        public GamePlayerExploreModule ExploreModule;
        public GamePlayerExchangeModule ExchangeModule;
        public GamePlayerHakoniwaModule HakoniwaModule;


        public void InitModules(List<GamePlayerModule> modules)
        {
            foreach(GamePlayerModule module in modules)
            {
                InitModuleInternal(module);
            }
        }

        public void InitModule(GamePlayerModule module)
        {
            InitModuleInternal(module);
        }

        private void InitModuleInternal(GamePlayerModule module)
        {
            switch (module.m_moduleId)
            {
                case GameModuleId.EventDispatch:
                    EventDispatchModule = module as GamePlayerEventDispatchModule;
                    break;
                case GameModuleId.ServerProxy:
                    ServerProxyModule = module as GamePlayerServerProxyModule;
                    break;
                case GameModuleId.Basic:
                    BasicModule = module as GamePlayerBasicModule;
                    break;
                case GameModuleId.Quest:
                    QuestModule = module as GamePlayerQuestModule;
                    break;
                case GameModuleId.World:
                    WorldModule = module as GamePlayerWorldModule;
                    break;
                case GameModuleId.Battle:
                    BattleModule = module as GamePlayerBattleModule;
                    break;
                case GameModuleId.Bag:
                    BagModule = module as GamePlayerBagModule;
                    break;
                case GameModuleId.Setting:
                    SettingModule = module as GamePlayerSettingModule;
                    break;
                case GameModuleId.Reward:
                    RewardModule = module as GamePlayerRewardModule;
                    break;
                case GameModuleId.Favor:
                    FavorModule = module as GamePlayerFavorModule;
                    break;
                case GameModuleId.Decision:
                    DecisionModule = module as GamePlayerDecisionModule;
                    break;
                case GameModuleId.Transformation:
                    TransformationModule = module as GamePlayerTransformationModule;
                    break;
                case GameModuleId.Explore:
                    ExploreModule = module as GamePlayerExploreModule;
                    break;
                case GameModuleId.Exchange:
                    ExchangeModule = module as GamePlayerExchangeModule;
                    break;
                case GameModuleId.Hakoniwa:
                    HakoniwaModule = module as GamePlayerHakoniwaModule;
                    break;
                default:
                    DebugUtility.LogError("GamePlayerModule is not in Provider, id = " + module.m_moduleId.ToString());
                    break;
            }
        }
    }
}
