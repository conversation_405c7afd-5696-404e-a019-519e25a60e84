// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.Battle
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleInitDataMsgFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.Battle.BattleInitDataMsg>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.Battle.BattleInitDataMsg value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(13);
            writer.Write(value.battleRid);
            writer.Write(value.randomSeed);
            writer.Write(value.needExpand);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.PlayerInitDataMsg>>().Serialize(ref writer, value.playerInitDataList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.BattleDisposedActorInitDataMsg>>().Serialize(ref writer, value.disposedActorInitDataList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.BattleDisposedActorPosInitDataMsg>>().Serialize(ref writer, value.disposedActorPosInitDataList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.gainedTreasureBoxIdList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.completedAchievementIdList, options);
            writer.Write(value.groundStartX);
            writer.Write(value.groundStartY);
            writer.Write(value.groundEndX);
            writer.Write(value.groundEndY);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.startTime, options);
        }

        public global::Phoenix.Battle.BattleInitDataMsg Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __battleRid__ = default(int);
            var __randomSeed__ = default(int);
            var __needExpand__ = default(bool);
            var __playerInitDataList__ = default(global::System.Collections.Generic.List<global::Phoenix.Battle.PlayerInitDataMsg>);
            var __disposedActorInitDataList__ = default(global::System.Collections.Generic.List<global::Phoenix.Battle.BattleDisposedActorInitDataMsg>);
            var __disposedActorPosInitDataList__ = default(global::System.Collections.Generic.List<global::Phoenix.Battle.BattleDisposedActorPosInitDataMsg>);
            var __gainedTreasureBoxIdList__ = default(global::System.Collections.Generic.List<int>);
            var __completedAchievementIdList__ = default(global::System.Collections.Generic.List<int>);
            var __groundStartX__ = default(int);
            var __groundStartY__ = default(int);
            var __groundEndX__ = default(int);
            var __groundEndY__ = default(int);
            var __startTime__ = default(string);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __battleRid__ = reader.ReadInt32();
                        break;
                    case 1:
                        __randomSeed__ = reader.ReadInt32();
                        break;
                    case 2:
                        __needExpand__ = reader.ReadBoolean();
                        break;
                    case 3:
                        __playerInitDataList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.PlayerInitDataMsg>>().Deserialize(ref reader, options);
                        break;
                    case 4:
                        __disposedActorInitDataList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.BattleDisposedActorInitDataMsg>>().Deserialize(ref reader, options);
                        break;
                    case 5:
                        __disposedActorPosInitDataList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.BattleDisposedActorPosInitDataMsg>>().Deserialize(ref reader, options);
                        break;
                    case 6:
                        __gainedTreasureBoxIdList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 7:
                        __completedAchievementIdList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 8:
                        __groundStartX__ = reader.ReadInt32();
                        break;
                    case 9:
                        __groundStartY__ = reader.ReadInt32();
                        break;
                    case 10:
                        __groundEndX__ = reader.ReadInt32();
                        break;
                    case 11:
                        __groundEndY__ = reader.ReadInt32();
                        break;
                    case 12:
                        __startTime__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.Battle.BattleInitDataMsg();
            ____result.battleRid = __battleRid__;
            ____result.randomSeed = __randomSeed__;
            ____result.needExpand = __needExpand__;
            ____result.playerInitDataList = __playerInitDataList__;
            ____result.disposedActorInitDataList = __disposedActorInitDataList__;
            ____result.disposedActorPosInitDataList = __disposedActorPosInitDataList__;
            ____result.gainedTreasureBoxIdList = __gainedTreasureBoxIdList__;
            ____result.completedAchievementIdList = __completedAchievementIdList__;
            ____result.groundStartX = __groundStartX__;
            ____result.groundStartY = __groundStartY__;
            ____result.groundEndX = __groundEndX__;
            ____result.groundEndY = __groundEndY__;
            ____result.startTime = __startTime__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
