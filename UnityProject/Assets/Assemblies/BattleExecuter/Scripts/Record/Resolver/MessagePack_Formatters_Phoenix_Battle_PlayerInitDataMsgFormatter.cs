// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.Battle
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class PlayerInitDataMsgFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.Battle.PlayerInitDataMsg>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.Battle.PlayerInitDataMsg value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(5);
            writer.Write(value.playerId);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.disposedActorRidList, options);
            writer.Write(value.posX);
            writer.Write(value.posY);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.Battle.GridDirType>().Serialize(ref writer, value.dirType, options);
        }

        public global::Phoenix.Battle.PlayerInitDataMsg Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __playerId__ = default(ulong);
            var __disposedActorRidList__ = default(global::System.Collections.Generic.List<int>);
            var __posX__ = default(int);
            var __posY__ = default(int);
            var __dirType__ = default(global::Phoenix.Battle.GridDirType);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __playerId__ = reader.ReadUInt64();
                        break;
                    case 1:
                        __disposedActorRidList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __posX__ = reader.ReadInt32();
                        break;
                    case 3:
                        __posY__ = reader.ReadInt32();
                        break;
                    case 4:
                        __dirType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.Battle.GridDirType>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.Battle.PlayerInitDataMsg();
            ____result.playerId = __playerId__;
            ____result.disposedActorRidList = __disposedActorRidList__;
            ____result.posX = __posX__;
            ____result.posY = __posY__;
            ____result.dirType = __dirType__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
