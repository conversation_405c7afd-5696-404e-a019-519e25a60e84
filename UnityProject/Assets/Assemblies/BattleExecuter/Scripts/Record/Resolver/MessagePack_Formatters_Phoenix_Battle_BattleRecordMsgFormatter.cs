// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.Battle
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleRecordMsgFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.Battle.BattleRecordMsg>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.Battle.BattleRecordMsg value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(4);
            writer.Write(value.randomSeed);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.Battle.BattleInitDataMsg>().Serialize(ref writer, value.battleInitData, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.FrameCutMsg>>().Serialize(ref writer, value.frameCutList, options);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.saveTime, options);
        }

        public global::Phoenix.Battle.BattleRecordMsg Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __randomSeed__ = default(int);
            var __battleInitData__ = default(global::Phoenix.Battle.BattleInitDataMsg);
            var __frameCutList__ = default(global::System.Collections.Generic.List<global::Phoenix.Battle.FrameCutMsg>);
            var __saveTime__ = default(string);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __randomSeed__ = reader.ReadInt32();
                        break;
                    case 1:
                        __battleInitData__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.Battle.BattleInitDataMsg>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __frameCutList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.Battle.FrameCutMsg>>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __saveTime__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.Battle.BattleRecordMsg();
            ____result.randomSeed = __randomSeed__;
            ____result.battleInitData = __battleInitData__;
            ____result.frameCutList = __frameCutList__;
            ____result.saveTime = __saveTime__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
