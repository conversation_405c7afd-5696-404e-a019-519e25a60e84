using System.Collections.Generic;
using Phoenix.Core;
using MessagePack;

namespace Phoenix.Battle
{
    [MessagePackObject]
    public partial class EntityWaitFrameCommandMsg : FrameCommandMsg
    {
        [Key(0)]
        public int entityUid;
        [Key(1)]
        public short movePosX;
        [Key(2)]
        public short movePosY;
        
        [IgnoreMember]
        public override FrameCommandType commandType
        {
            get { return FrameCommandType.EntityWait; }
        }
    }
}
