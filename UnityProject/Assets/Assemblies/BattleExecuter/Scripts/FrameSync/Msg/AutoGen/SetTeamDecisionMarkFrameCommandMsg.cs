using System.Collections.Generic;
using Phoenix.Core;
using MessagePack;

namespace Phoenix.Battle
{
    [MessagePackObject]
    public partial class SetTeamDecisionMarkFrameCommandMsg : FrameCommandMsg
    {
        [Key(0)]
        public int teamUid;
        [Key(1)]
        public int entityUid;
        [Key(2)]
        public short posX;
        [Key(3)]
        public short posY;
        [Key(4)]
        public int markId;
        
        [IgnoreMember]
        public override FrameCommandType commandType
        {
            get { return FrameCommandType.SetTeamDecisionMark; }
        }
    }
}
