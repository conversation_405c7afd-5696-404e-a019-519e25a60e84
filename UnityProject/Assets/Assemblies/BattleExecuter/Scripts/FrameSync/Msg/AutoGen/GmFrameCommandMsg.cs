using System.Collections.Generic;
using Phoenix.Core;
using MessagePack;

namespace Phoenix.Battle
{
    [MessagePackObject]
    public partial class GmFrameCommandMsg : FrameCommandMsg
    {
        [Key(0)]
        public int gmId;
        [Key(1)]
        public List<int> paramList = new List<int>();
        
        [IgnoreMember]
        public override FrameCommandType commandType
        {
            get { return FrameCommandType.Gm; }
        }
    }
}
