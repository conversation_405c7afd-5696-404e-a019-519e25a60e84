// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168
#pragma warning disable CS1591 // document public APIs

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.Battle
{
    public sealed class SetTeamDecisionMarkFrameCommandMsgFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg>
    {

        public void Serialize(ref global::MessagePack.MessagePackWriter writer, global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            writer.WriteArrayHeader(5);
            writer.Write(value.teamUid);
            writer.Write(value.entityUid);
            writer.Write(value.posX);
            writer.Write(value.posY);
            writer.Write(value.markId);
        }

        public global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg Deserialize(ref global::MessagePack.MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            var length = reader.ReadArrayHeader();
            var ____result = new global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg();

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        ____result.teamUid = reader.ReadInt32();
                        break;
                    case 1:
                        ____result.entityUid = reader.ReadInt32();
                        break;
                    case 2:
                        ____result.posX = reader.ReadInt16();
                        break;
                    case 3:
                        ____result.posY = reader.ReadInt16();
                        break;
                    case 4:
                        ____result.markId = reader.ReadInt32();
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            reader.Depth--;
            return ____result;
        }
    }

}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
