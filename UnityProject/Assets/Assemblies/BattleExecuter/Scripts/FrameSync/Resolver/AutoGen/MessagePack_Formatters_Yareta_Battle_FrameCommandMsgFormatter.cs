// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168
#pragma warning disable CS1591 // document public APIs

#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.Battle
{
    public sealed class FrameCommandMsgFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.Battle.FrameCommandMsg>
    {
        private readonly global::System.Collections.Generic.Dictionary<global::System.RuntimeTypeHandle, global::System.Collections.Generic.KeyValuePair<int, int>> typeToKeyAndJumpMap;
        private readonly global::System.Collections.Generic.Dictionary<int, int> keyToJumpMap;

        public FrameCommandMsgFormatter()
        {
            this.typeToKeyAndJumpMap = new global::System.Collections.Generic.Dictionary<global::System.RuntimeTypeHandle, global::System.Collections.Generic.KeyValuePair<int, int>>(14, global::MessagePack.Internal.RuntimeTypeHandleEqualityComparer.Default)
            {
                { typeof(global::Phoenix.Battle.FormationExchangeFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(0, 0) },
                { typeof(global::Phoenix.Battle.FormationRetreatFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(1, 1) },
                { typeof(global::Phoenix.Battle.FormationSetupFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(2, 2) },
                { typeof(global::Phoenix.Battle.FormationEndFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(3, 3) },
                { typeof(global::Phoenix.Battle.ChangeBattleFormationFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(4, 4) },
                { typeof(global::Phoenix.Battle.EntityWaitFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(5, 5) },
                { typeof(global::Phoenix.Battle.EntityCastSkillFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(6, 6) },
                { typeof(global::Phoenix.Battle.DialogSelectFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(7, 7) },
                { typeof(global::Phoenix.Battle.ChangeTeamDecisionFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(8, 8) },
                { typeof(global::Phoenix.Battle.ChangeTeamAutoStateFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(9, 9) },
                { typeof(global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(10, 10) },
                { typeof(global::Phoenix.Battle.CancelTeamDecisionMarkFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(11, 11) },
                { typeof(global::Phoenix.Battle.SetupTeamDcisionDatasFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(12, 12) },
                { typeof(global::Phoenix.Battle.GmFrameCommandMsg).TypeHandle, new global::System.Collections.Generic.KeyValuePair<int, int>(13, 13) },
            };
            this.keyToJumpMap = new global::System.Collections.Generic.Dictionary<int, int>(14)
            {
                { 0, 0 },
                { 1, 1 },
                { 2, 2 },
                { 3, 3 },
                { 4, 4 },
                { 5, 5 },
                { 6, 6 },
                { 7, 7 },
                { 8, 8 },
                { 9, 9 },
                { 10, 10 },
                { 11, 11 },
                { 12, 12 },
                { 13, 13 },
            };
        }

        public void Serialize(ref global::MessagePack.MessagePackWriter writer, global::Phoenix.Battle.FrameCommandMsg value, global::MessagePack.MessagePackSerializerOptions options)
        {
            global::System.Collections.Generic.KeyValuePair<int, int> keyValuePair;
            if (value != null && this.typeToKeyAndJumpMap.TryGetValue(value.GetType().TypeHandle, out keyValuePair))
            {
                writer.WriteArrayHeader(2);
                writer.WriteInt32(keyValuePair.Key);
                switch (keyValuePair.Value)
                {
                    case 0:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationExchangeFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.FormationExchangeFrameCommandMsg)value, options);
                        break;
                    case 1:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationRetreatFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.FormationRetreatFrameCommandMsg)value, options);
                        break;
                    case 2:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationSetupFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.FormationSetupFrameCommandMsg)value, options);
                        break;
                    case 3:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationEndFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.FormationEndFrameCommandMsg)value, options);
                        break;
                    case 4:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.ChangeBattleFormationFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.ChangeBattleFormationFrameCommandMsg)value, options);
                        break;
                    case 5:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.EntityWaitFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.EntityWaitFrameCommandMsg)value, options);
                        break;
                    case 6:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.EntityCastSkillFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.EntityCastSkillFrameCommandMsg)value, options);
                        break;
                    case 7:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.DialogSelectFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.DialogSelectFrameCommandMsg)value, options);
                        break;
                    case 8:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.ChangeTeamDecisionFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.ChangeTeamDecisionFrameCommandMsg)value, options);
                        break;
                    case 9:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.ChangeTeamAutoStateFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.ChangeTeamAutoStateFrameCommandMsg)value, options);
                        break;
                    case 10:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg)value, options);
                        break;
                    case 11:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.CancelTeamDecisionMarkFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.CancelTeamDecisionMarkFrameCommandMsg)value, options);
                        break;
                    case 12:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.SetupTeamDcisionDatasFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.SetupTeamDcisionDatasFrameCommandMsg)value, options);
                        break;
                    case 13:
                        global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.GmFrameCommandMsg>(options.Resolver).Serialize(ref writer, (global::Phoenix.Battle.GmFrameCommandMsg)value, options);
                        break;
                    default:
                        break;
                }

                return;
            }

            writer.WriteNil();
        }

        public global::Phoenix.Battle.FrameCommandMsg Deserialize(ref global::MessagePack.MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            if (reader.ReadArrayHeader() != 2)
            {
                throw new global::System.InvalidOperationException("Invalid Union data was detected. Type:global::Phoenix.Battle.FrameCommandMsg");
            }

            options.Security.DepthStep(ref reader);
            var key = reader.ReadInt32();

            if (!this.keyToJumpMap.TryGetValue(key, out key))
            {
                key = -1;
            }

            global::Phoenix.Battle.FrameCommandMsg result = null;
            switch (key)
            {
                case 0:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationExchangeFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 1:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationRetreatFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 2:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationSetupFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 3:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.FormationEndFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 4:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.ChangeBattleFormationFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 5:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.EntityWaitFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 6:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.EntityCastSkillFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 7:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.DialogSelectFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 8:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.ChangeTeamDecisionFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 9:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.ChangeTeamAutoStateFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 10:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.SetTeamDecisionMarkFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 11:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.CancelTeamDecisionMarkFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 12:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.SetupTeamDcisionDatasFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                case 13:
                    result = (global::Phoenix.Battle.FrameCommandMsg)global::MessagePack.FormatterResolverExtensions.GetFormatterWithVerify<global::Phoenix.Battle.GmFrameCommandMsg>(options.Resolver).Deserialize(ref reader, options);
                    break;
                default:
                    reader.Skip();
                    break;
            }

            reader.Depth--;
            return result;
        }
    }


}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
