using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using MessagePack;
using MessagePack.Resolvers;

namespace Phoenix.Battle
{
    public static partial class FrameSyncConvertUtility
    {
        private static readonly MessagePackSerializerOptions m_options;

        static FrameSyncConvertUtility()
        {
            var resolver = CompositeResolver.Create(FrameSyncMsgResolver.Instance, StandardResolver.Instance);
            m_options = MessagePackSerializerOptions.Standard.WithResolver(resolver);
        }

        public static FrameCommandWrap ConvertToFrameCommandWrap(FrameCommandWrapMsg msg)
        {
            FrameCommandWrap wrap = new FrameCommandWrap();
            wrap.playerId = msg.playerId;
            wrap.cmd = ConvertToFrameCommand(msg.cmd);
            return wrap;
        }

        public static FrameCut ConvertToFrameCut(FrameCutMsg msg)
        {
            FrameCut cut = new FrameCut();
            cut.frameIndex = msg.frameIndex;
            cut.cmd = ConvertToFrameCommand(msg.cmd);
            return cut;
        }

        public static FrameCommandWrapMsg ConvertToFrameCommandWrapMsg(FrameCommandWrap wrap)
        {
            FrameCommandWrapMsg msg = new FrameCommandWrapMsg();
            msg.playerId = wrap.playerId;
            msg.cmd = ConvertToFrameCommandMsg(wrap.cmd);
            return msg;
        }

        public static FrameCutMsg ConvertToFrameCutMsg(FrameCut cut)
        {
            FrameCutMsg msg = new FrameCutMsg();
            msg.frameIndex = cut.frameIndex;
            msg.cmd = ConvertToFrameCommandMsg(cut.cmd);
            return msg;
        }

        public static byte[] ConvertToBuffer(FrameCutMsg msg)
        {
            return MessagePackSerializer.Serialize(msg, m_options);
        }

        public static byte[] ConvertToBuffer(FrameCut cut)
        {
            return ConvertToBuffer(ConvertToFrameCutMsg(cut));
        }

        public static byte[] ConvertToBuffer(List<FrameCut> cutList)
        {
            FrameCutListMsg msgList = new FrameCutListMsg();
            foreach (var cut in cutList)
            {
                msgList.list.Add(ConvertToFrameCutMsg(cut));
            }
            return MessagePackSerializer.Serialize(msgList, m_options);
        }

        public static FrameCutMsg ConvertToFrameCutMsg(byte[] buffer)
        {
            return MessagePackSerializer.Deserialize<FrameCutMsg>(buffer, m_options);
        }

        public static FrameCut ConvertToFrameCut(byte[] buffer)
        {
            return ConvertToFrameCut(ConvertToFrameCutMsg(buffer));
        }

        public static List<FrameCut> ConvertToFrameCutList(List<FrameCutMsg> msgList)
        {
            var cutList = new List<FrameCut>();
            foreach (var msg in msgList)
            {
                cutList.Add(ConvertToFrameCut(msg));
            }
            return cutList;
        }

        public static List<FrameCut> ConvertToFrameCutList(byte[] buffer)
        {
            var msgList = MessagePackSerializer.Deserialize<FrameCutListMsg>(buffer, m_options);
            return ConvertToFrameCutList(msgList.list);
        }

        public static FrameCommandWrapMsg ConvertToFrameCommandWrapMsg(byte[] buffer)
        {
            return MessagePackSerializer.Deserialize<FrameCommandWrapMsg>(buffer, m_options);
        }

        public static FrameCommandWrap ConvertToFrameCommandWrap(byte[] buffer)
        {
            return ConvertToFrameCommandWrap(ConvertToFrameCommandWrapMsg(buffer));
        }

        public static byte[] ConvertToBuffer(FrameCommandWrapMsg msg)
        {
            return MessagePackSerializer.Serialize(msg, m_options);
        }

        public static byte[] ConvertToBuffer(FrameCommandWrap wrap)
        {
            return ConvertToBuffer(ConvertToFrameCommandWrapMsg(wrap));
        }
    }
}
