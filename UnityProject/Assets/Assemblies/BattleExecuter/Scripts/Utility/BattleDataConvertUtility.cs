using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public static partial class BattleDataConvertUtility
    {
        public static BattleInfo GetBattleInfo(Phoenix.ConfigData.BattleConfigData battle)
        {
            if (battle == null)
            {
                return null;
            }
            BattleInfo battleInfo = new BattleInfo();
            battleInfo.id = battle.id;
            battleInfo.name = battle.name;
            battleInfo.dispositionGroupId = battle.dispositionGroupId;
            foreach(var disposedActor in battle.disposedActorList)
            {
                battleInfo.disposedActorList.Add(disposedActor);
            }
            foreach(var playerSlot in battle.playerSlotList)
            {
                battleInfo.playerSlotList.Add(GetBattlePlayerSlotInfo(playerSlot));
            }
            foreach(var stage in battle.stageList)
            {
                battleInfo.stageList.Add(GetBattleStageInfo(stage));
            }
            return battleInfo;
        }
        
        public static BattlePlayerSlotInfo GetBattlePlayerSlotInfo(Phoenix.ConfigData.BattlePlayerSlotConfigData battlePlayerSlot)
        {
            if (battlePlayerSlot == null)
            {
                return null;
            }
            BattlePlayerSlotInfo battlePlayerSlotInfo = new BattlePlayerSlotInfo();
            battlePlayerSlotInfo.slotId = battlePlayerSlot.slotId;
            return battlePlayerSlotInfo;
        }
        
        public static BattleStageInfo GetBattleStageInfo(Phoenix.ConfigData.BattleStageConfigData battleStage)
        {
            if (battleStage == null)
            {
                return null;
            }
            BattleStageInfo battleStageInfo = new BattleStageInfo();
            battleStageInfo.stageType = (BattleStageType)battleStage.stageType;
            battleStageInfo.sceneAssetPath = battleStage.sceneAssetPath;
            battleStageInfo.sceneCombatAssetPath = battleStage.sceneCombatAssetPath;
            battleStageInfo.groundAssetPath = battleStage.groundAssetPath;
            battleStageInfo.groundIndex = battleStage.groundIndex;
            battleStageInfo.bgm = battleStage.bgm;
            battleStageInfo.initCameraPos = GetGridPosition(battleStage.initCameraPos);
            battleStageInfo.initCameraRotateOffset = battleStage.initCameraRotateOffset;
            battleStageInfo.roundRobinType = (BattleRoundRobinType)battleStage.roundRobinType;
            foreach(var team in battleStage.teamList)
            {
                battleStageInfo.teamList.Add(GetBattleTeamInfo(team));
            }
            foreach(var disposedActor in battleStage.disposedActorList)
            {
                battleStageInfo.disposedActorList.Add(GetBattleStageDisposedActorInfo(disposedActor));
            }
            foreach(var disposedTerrainBuff in battleStage.disposedTerrainBuffList)
            {
                battleStageInfo.disposedTerrainBuffList.Add(GetBattleStageDisposedTerrainBuffInfo(disposedTerrainBuff));
            }
            foreach(var disposition in battleStage.dispositionList)
            {
                battleStageInfo.dispositionList.Add(GetBattleStageDispositionInfo(disposition));
            }
            foreach(var mapping in battleStage.mappingList)
            {
                battleStageInfo.mappingList.Add(GetBattleStageDisposedActorIdMappingInfo(mapping));
            }
            battleStageInfo.dispositionMaxCount = battleStage.dispositionMaxCount;
            battleStageInfo.stamp = GetBattleStarStampInfo(battleStage.stamp);
            foreach(var treasureBox in battleStage.treasureBoxList)
            {
                battleStageInfo.treasureBoxList.Add(GetBattleTreasureBoxInfo(treasureBox));
            }
            foreach(var achievement in battleStage.achievementList)
            {
                battleStageInfo.achievementList.Add(GetBattleAchievementInfo(achievement));
            }
            battleStageInfo.stageRefreree = GetBattleStageRefereeInfo(battleStage.stageRefreree);
            foreach(var trigger in battleStage.triggerList)
            {
                battleStageInfo.triggerList.Add(GetBattleStageTriggerInfo(trigger));
            }
            battleStageInfo.functionEnable = GetBattleFunctionEnableInfo(battleStage.functionEnable);
            return battleStageInfo;
        }
        
        public static BattleTeamInfo GetBattleTeamInfo(Phoenix.ConfigData.BattleTeamConfigData battleTeam)
        {
            if (battleTeam == null)
            {
                return null;
            }
            BattleTeamInfo battleTeamInfo = new BattleTeamInfo();
            battleTeamInfo.id = battleTeam.id;
            battleTeamInfo.configId = battleTeam.configId;
            battleTeamInfo.campId = battleTeam.campId;
            battleTeamInfo.playerSlotId = battleTeam.playerSlotId;
            battleTeamInfo.formationId = battleTeam.formationId;
            battleTeamInfo.isAggressive = battleTeam.isAggressive;
            return battleTeamInfo;
        }
        
        public static BattleStageDisposedActorInfo GetBattleStageDisposedActorInfo(Phoenix.ConfigData.BattleStageDisposedActorConfigData battleStageDisposedActor)
        {
            if (battleStageDisposedActor == null)
            {
                return null;
            }
            BattleStageDisposedActorInfo battleStageDisposedActorInfo = new BattleStageDisposedActorInfo();
            battleStageDisposedActorInfo.uid = battleStageDisposedActor.uid;
            battleStageDisposedActorInfo.rid = battleStageDisposedActor.rid;
            battleStageDisposedActorInfo.level = battleStageDisposedActor.level;
            battleStageDisposedActorInfo.teamId = battleStageDisposedActor.teamId;
            battleStageDisposedActorInfo.position = GetGridPosition(battleStageDisposedActor.position);
            battleStageDisposedActorInfo.initHpRate = new FixedValue(battleStageDisposedActor.initHpRate, 100);
            battleStageDisposedActorInfo.gridDir = GetGridDirType(battleStageDisposedActor.gridDir);
            return battleStageDisposedActorInfo;
        }
        
        public static BattleStageDisposedTerrainBuffInfo GetBattleStageDisposedTerrainBuffInfo(Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData battleStageDisposedTerrainBuff)
        {
            if (battleStageDisposedTerrainBuff == null)
            {
                return null;
            }
            BattleStageDisposedTerrainBuffInfo battleStageDisposedTerrainBuffInfo = new BattleStageDisposedTerrainBuffInfo();
            battleStageDisposedTerrainBuffInfo.uid = battleStageDisposedTerrainBuff.uid;
            battleStageDisposedTerrainBuffInfo.rid = battleStageDisposedTerrainBuff.rid;
            battleStageDisposedTerrainBuffInfo.teamId = battleStageDisposedTerrainBuff.teamId;
            battleStageDisposedTerrainBuffInfo.position = GetGridPosition(battleStageDisposedTerrainBuff.position);
            battleStageDisposedTerrainBuffInfo.gridDir = GetGridDirType(battleStageDisposedTerrainBuff.gridDir);
            return battleStageDisposedTerrainBuffInfo;
        }
        
        public static BattleStageDispositionInfo GetBattleStageDispositionInfo(Phoenix.ConfigData.BattleStageDispositionConfigData battleStageDisposition)
        {
            if (battleStageDisposition == null)
            {
                return null;
            }
            BattleStageDispositionInfo battleStageDispositionInfo = new BattleStageDispositionInfo();
            battleStageDispositionInfo.id = battleStageDisposition.id;
            battleStageDispositionInfo.teamId = battleStageDisposition.teamId;
            battleStageDispositionInfo.position = GetGridPosition(battleStageDisposition.position);
            battleStageDispositionInfo.gridDir = GetGridDirType(battleStageDisposition.gridDir);
            return battleStageDispositionInfo;
        }
        
        public static BattleStageDisposedActorIdMappingInfo GetBattleStageDisposedActorIdMappingInfo(Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData battleStageDisposedActorIdMapping)
        {
            if (battleStageDisposedActorIdMapping == null)
            {
                return null;
            }
            BattleStageDisposedActorIdMappingInfo battleStageDisposedActorIdMappingInfo = new BattleStageDisposedActorIdMappingInfo();
            battleStageDisposedActorIdMappingInfo.uid = battleStageDisposedActorIdMapping.uid;
            battleStageDisposedActorIdMappingInfo.rid = battleStageDisposedActorIdMapping.rid;
            return battleStageDisposedActorIdMappingInfo;
        }
        
        public static BattleStarStampInfo GetBattleStarStampInfo(Phoenix.ConfigData.BattleStarStampConfigData battleStarStamp)
        {
            if (battleStarStamp == null)
            {
                return null;
            }
            BattleStarStampInfo battleStarStampInfo = new BattleStarStampInfo();
            battleStarStampInfo.turnIndex = battleStarStamp.turnIndex;
            battleStarStampInfo.deadCount = battleStarStamp.deadCount;
            return battleStarStampInfo;
        }
        
        public static BattleTreasureBoxInfo GetBattleTreasureBoxInfo(Phoenix.ConfigData.BattleTreasureBoxConfigData battleTreasureBox)
        {
            if (battleTreasureBox == null)
            {
                return null;
            }
            BattleTreasureBoxInfo battleTreasureBoxInfo = new BattleTreasureBoxInfo();
            battleTreasureBoxInfo.treasureBoxId = battleTreasureBox.treasureBoxId;
            battleTreasureBoxInfo.skinId = battleTreasureBox.skinId;
            battleTreasureBoxInfo.pos = GetGridPosition(battleTreasureBox.pos);
            battleTreasureBoxInfo.rewardId = battleTreasureBox.rewardId;
            return battleTreasureBoxInfo;
        }
        
        public static BattleAchievementInfo GetBattleAchievementInfo(Phoenix.ConfigData.BattleAchievementConfigData battleAchievement)
        {
            if (battleAchievement == null)
            {
                return null;
            }
            BattleAchievementInfo battleAchievementInfo = null;
            switch (battleAchievement.achievementType)
            {
                case Phoenix.ConfigData.BattleAchievementType.WinBeforeTurn:
                    battleAchievementInfo = GetBattleAchievementInfo_WinBeforeTurn(battleAchievement as Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn);
                    break;
                case Phoenix.ConfigData.BattleAchievementType.DeadActorCountLessThan:
                    battleAchievementInfo = GetBattleAchievementInfo_DeadActorCountLessThan(battleAchievement as Phoenix.ConfigData.BattleAchievementConfigData_DeadActorCountLessThan);
                    break;
                case Phoenix.ConfigData.BattleAchievementType.KillActorCountBeforeTurn:
                    battleAchievementInfo = GetBattleAchievementInfo_KillActorCountBeforeTurn(battleAchievement as Phoenix.ConfigData.BattleAchievementConfigData_KillActorCountBeforeTurn);
                    break;
                case Phoenix.ConfigData.BattleAchievementType.ActorKillAnyActor:
                    battleAchievementInfo = GetBattleAchievementInfo_ActorKillAnyActor(battleAchievement as Phoenix.ConfigData.BattleAchievementConfigData_ActorKillAnyActor);
                    break;
                case Phoenix.ConfigData.BattleAchievementType.EnemyDeadCountBeforeTurn:
                    battleAchievementInfo = GetBattleAchievementInfo_EnemyDeadCountBeforeTurn(battleAchievement as Phoenix.ConfigData.BattleAchievementConfigData_EnemyDeadCountBeforeTurn);
                    break;
            }
            if (battleAchievementInfo != null)
            {
                battleAchievementInfo.achievementId = battleAchievement.achievementId;
                battleAchievementInfo.name = battleAchievement.name;
                battleAchievementInfo.desc = battleAchievement.desc;
                battleAchievementInfo.rewardId = battleAchievement.rewardId;
            }
            return battleAchievementInfo;
        }
        
        public static BattleAchievementInfo_WinBeforeTurn GetBattleAchievementInfo_WinBeforeTurn(Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn battleAchievement_WinBeforeTurn)
        {
            if (battleAchievement_WinBeforeTurn == null)
            {
                return null;
            }
            BattleAchievementInfo_WinBeforeTurn battleAchievementInfo_WinBeforeTurn = new BattleAchievementInfo_WinBeforeTurn();
            battleAchievementInfo_WinBeforeTurn.turnIndex = battleAchievement_WinBeforeTurn.turnIndex;
            battleAchievementInfo_WinBeforeTurn.achievementId = battleAchievement_WinBeforeTurn.achievementId;
            battleAchievementInfo_WinBeforeTurn.name = battleAchievement_WinBeforeTurn.name;
            battleAchievementInfo_WinBeforeTurn.desc = battleAchievement_WinBeforeTurn.desc;
            battleAchievementInfo_WinBeforeTurn.rewardId = battleAchievement_WinBeforeTurn.rewardId;
            return battleAchievementInfo_WinBeforeTurn;
        }
        
        public static BattleAchievementInfo_DeadActorCountLessThan GetBattleAchievementInfo_DeadActorCountLessThan(Phoenix.ConfigData.BattleAchievementConfigData_DeadActorCountLessThan battleAchievement_DeadActorCountLessThan)
        {
            if (battleAchievement_DeadActorCountLessThan == null)
            {
                return null;
            }
            BattleAchievementInfo_DeadActorCountLessThan battleAchievementInfo_DeadActorCountLessThan = new BattleAchievementInfo_DeadActorCountLessThan();
            battleAchievementInfo_DeadActorCountLessThan.teamUid = battleAchievement_DeadActorCountLessThan.teamUid;
            battleAchievementInfo_DeadActorCountLessThan.deadCount = battleAchievement_DeadActorCountLessThan.deadCount;
            battleAchievementInfo_DeadActorCountLessThan.achievementId = battleAchievement_DeadActorCountLessThan.achievementId;
            battleAchievementInfo_DeadActorCountLessThan.name = battleAchievement_DeadActorCountLessThan.name;
            battleAchievementInfo_DeadActorCountLessThan.desc = battleAchievement_DeadActorCountLessThan.desc;
            battleAchievementInfo_DeadActorCountLessThan.rewardId = battleAchievement_DeadActorCountLessThan.rewardId;
            return battleAchievementInfo_DeadActorCountLessThan;
        }
        
        public static BattleAchievementInfo_KillActorCountBeforeTurn GetBattleAchievementInfo_KillActorCountBeforeTurn(Phoenix.ConfigData.BattleAchievementConfigData_KillActorCountBeforeTurn battleAchievement_KillActorCountBeforeTurn)
        {
            if (battleAchievement_KillActorCountBeforeTurn == null)
            {
                return null;
            }
            BattleAchievementInfo_KillActorCountBeforeTurn battleAchievementInfo_KillActorCountBeforeTurn = new BattleAchievementInfo_KillActorCountBeforeTurn();
            battleAchievementInfo_KillActorCountBeforeTurn.teamUid = battleAchievement_KillActorCountBeforeTurn.teamUid;
            battleAchievementInfo_KillActorCountBeforeTurn.turnIndex = battleAchievement_KillActorCountBeforeTurn.turnIndex;
            battleAchievementInfo_KillActorCountBeforeTurn.killCount = battleAchievement_KillActorCountBeforeTurn.killCount;
            battleAchievementInfo_KillActorCountBeforeTurn.achievementId = battleAchievement_KillActorCountBeforeTurn.achievementId;
            battleAchievementInfo_KillActorCountBeforeTurn.name = battleAchievement_KillActorCountBeforeTurn.name;
            battleAchievementInfo_KillActorCountBeforeTurn.desc = battleAchievement_KillActorCountBeforeTurn.desc;
            battleAchievementInfo_KillActorCountBeforeTurn.rewardId = battleAchievement_KillActorCountBeforeTurn.rewardId;
            return battleAchievementInfo_KillActorCountBeforeTurn;
        }
        
        public static BattleAchievementInfo_ActorKillAnyActor GetBattleAchievementInfo_ActorKillAnyActor(Phoenix.ConfigData.BattleAchievementConfigData_ActorKillAnyActor battleAchievement_ActorKillAnyActor)
        {
            if (battleAchievement_ActorKillAnyActor == null)
            {
                return null;
            }
            BattleAchievementInfo_ActorKillAnyActor battleAchievementInfo_ActorKillAnyActor = new BattleAchievementInfo_ActorKillAnyActor();
            battleAchievementInfo_ActorKillAnyActor.actorUid = battleAchievement_ActorKillAnyActor.actorUid;
            foreach(var actorUid in battleAchievement_ActorKillAnyActor.actorUidList)
            {
                battleAchievementInfo_ActorKillAnyActor.actorUidList.Add(actorUid);
            }
            battleAchievementInfo_ActorKillAnyActor.achievementId = battleAchievement_ActorKillAnyActor.achievementId;
            battleAchievementInfo_ActorKillAnyActor.name = battleAchievement_ActorKillAnyActor.name;
            battleAchievementInfo_ActorKillAnyActor.desc = battleAchievement_ActorKillAnyActor.desc;
            battleAchievementInfo_ActorKillAnyActor.rewardId = battleAchievement_ActorKillAnyActor.rewardId;
            return battleAchievementInfo_ActorKillAnyActor;
        }
        
        public static BattleAchievementInfo_EnemyDeadCountBeforeTurn GetBattleAchievementInfo_EnemyDeadCountBeforeTurn(Phoenix.ConfigData.BattleAchievementConfigData_EnemyDeadCountBeforeTurn battleAchievement_EnemyDeadCountBeforeTurn)
        {
            if (battleAchievement_EnemyDeadCountBeforeTurn == null)
            {
                return null;
            }
            BattleAchievementInfo_EnemyDeadCountBeforeTurn battleAchievementInfo_EnemyDeadCountBeforeTurn = new BattleAchievementInfo_EnemyDeadCountBeforeTurn();
            battleAchievementInfo_EnemyDeadCountBeforeTurn.teamUid = battleAchievement_EnemyDeadCountBeforeTurn.teamUid;
            battleAchievementInfo_EnemyDeadCountBeforeTurn.turnIndex = battleAchievement_EnemyDeadCountBeforeTurn.turnIndex;
            battleAchievementInfo_EnemyDeadCountBeforeTurn.killCount = battleAchievement_EnemyDeadCountBeforeTurn.killCount;
            battleAchievementInfo_EnemyDeadCountBeforeTurn.achievementId = battleAchievement_EnemyDeadCountBeforeTurn.achievementId;
            battleAchievementInfo_EnemyDeadCountBeforeTurn.name = battleAchievement_EnemyDeadCountBeforeTurn.name;
            battleAchievementInfo_EnemyDeadCountBeforeTurn.desc = battleAchievement_EnemyDeadCountBeforeTurn.desc;
            battleAchievementInfo_EnemyDeadCountBeforeTurn.rewardId = battleAchievement_EnemyDeadCountBeforeTurn.rewardId;
            return battleAchievementInfo_EnemyDeadCountBeforeTurn;
        }
        
        public static BattleStageRefereeInfo GetBattleStageRefereeInfo(Phoenix.ConfigData.BattleStageRefereeConfigData battleStageReferee)
        {
            if (battleStageReferee == null)
            {
                return null;
            }
            BattleStageRefereeInfo battleStageRefereeInfo = new BattleStageRefereeInfo();
            battleStageRefereeInfo.maxTurnCount = battleStageReferee.maxTurnCount;
            foreach(var campReferee in battleStageReferee.campRefereeList)
            {
                battleStageRefereeInfo.campRefereeList.Add(GetBattleCampRefereeInfo(campReferee));
            }
            return battleStageRefereeInfo;
        }
        
        public static BattleCampRefereeInfo GetBattleCampRefereeInfo(Phoenix.ConfigData.BattleCampRefereeConfigData battleCampReferee)
        {
            if (battleCampReferee == null)
            {
                return null;
            }
            BattleCampRefereeInfo battleCampRefereeInfo = new BattleCampRefereeInfo();
            battleCampRefereeInfo.campId = battleCampReferee.campId;
            foreach(var winSituation in battleCampReferee.winSituationList)
            {
                battleCampRefereeInfo.winSituationList.Add(GetBattleCampRefereeSituationInfo(winSituation));
            }
            foreach(var loseSituation in battleCampReferee.loseSituationList)
            {
                battleCampRefereeInfo.loseSituationList.Add(GetBattleCampRefereeSituationInfo(loseSituation));
            }
            return battleCampRefereeInfo;
        }
        
        public static BattleCampRefereeSituationInfo GetBattleCampRefereeSituationInfo(Phoenix.ConfigData.BattleCampRefereeSituationConfigData battleCampRefereeSituation)
        {
            if (battleCampRefereeSituation == null)
            {
                return null;
            }
            BattleCampRefereeSituationInfo battleCampRefereeSituationInfo = null;
            switch (battleCampRefereeSituation.refereeType)
            {
                case Phoenix.ConfigData.BattleCampRefereeSituationType.TurnEnd:
                    battleCampRefereeSituationInfo = GetBattleCampRefereeSituationInfo_TurnEnd(battleCampRefereeSituation as Phoenix.ConfigData.BattleCampRefereeSituationConfigData_TurnEnd);
                    break;
                case Phoenix.ConfigData.BattleCampRefereeSituationType.EnemyAllDead:
                    battleCampRefereeSituationInfo = GetBattleCampRefereeSituationInfo_EnemyAllDead(battleCampRefereeSituation as Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EnemyAllDead);
                    break;
                case Phoenix.ConfigData.BattleCampRefereeSituationType.SelfAllDead:
                    battleCampRefereeSituationInfo = GetBattleCampRefereeSituationInfo_SelfAllDead(battleCampRefereeSituation as Phoenix.ConfigData.BattleCampRefereeSituationConfigData_SelfAllDead);
                    break;
                case Phoenix.ConfigData.BattleCampRefereeSituationType.EntityListAllDead:
                    battleCampRefereeSituationInfo = GetBattleCampRefereeSituationInfo_EntityListAllDead(battleCampRefereeSituation as Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EntityListAllDead);
                    break;
                case Phoenix.ConfigData.BattleCampRefereeSituationType.AnyActorOccupyAnyGrid:
                    battleCampRefereeSituationInfo = GetBattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid(battleCampRefereeSituation as Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid);
                    break;
            }
            if (battleCampRefereeSituationInfo != null)
            {
                battleCampRefereeSituationInfo.enabled = battleCampRefereeSituation.enabled;
                battleCampRefereeSituationInfo.desc = battleCampRefereeSituation.desc;
            }
            return battleCampRefereeSituationInfo;
        }
        
        public static BattleCampRefereeSituationInfo_TurnEnd GetBattleCampRefereeSituationInfo_TurnEnd(Phoenix.ConfigData.BattleCampRefereeSituationConfigData_TurnEnd battleCampRefereeSituation_TurnEnd)
        {
            if (battleCampRefereeSituation_TurnEnd == null)
            {
                return null;
            }
            BattleCampRefereeSituationInfo_TurnEnd battleCampRefereeSituationInfo_TurnEnd = new BattleCampRefereeSituationInfo_TurnEnd();
            battleCampRefereeSituationInfo_TurnEnd.turnIndex = battleCampRefereeSituation_TurnEnd.turnIndex;
            battleCampRefereeSituationInfo_TurnEnd.enabled = battleCampRefereeSituation_TurnEnd.enabled;
            battleCampRefereeSituationInfo_TurnEnd.desc = battleCampRefereeSituation_TurnEnd.desc;
            return battleCampRefereeSituationInfo_TurnEnd;
        }
        
        public static BattleCampRefereeSituationInfo_EnemyAllDead GetBattleCampRefereeSituationInfo_EnemyAllDead(Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EnemyAllDead battleCampRefereeSituation_EnemyAllDead)
        {
            if (battleCampRefereeSituation_EnemyAllDead == null)
            {
                return null;
            }
            BattleCampRefereeSituationInfo_EnemyAllDead battleCampRefereeSituationInfo_EnemyAllDead = new BattleCampRefereeSituationInfo_EnemyAllDead();
            battleCampRefereeSituationInfo_EnemyAllDead.enabled = battleCampRefereeSituation_EnemyAllDead.enabled;
            battleCampRefereeSituationInfo_EnemyAllDead.desc = battleCampRefereeSituation_EnemyAllDead.desc;
            return battleCampRefereeSituationInfo_EnemyAllDead;
        }
        
        public static BattleCampRefereeSituationInfo_SelfAllDead GetBattleCampRefereeSituationInfo_SelfAllDead(Phoenix.ConfigData.BattleCampRefereeSituationConfigData_SelfAllDead battleCampRefereeSituation_SelfAllDead)
        {
            if (battleCampRefereeSituation_SelfAllDead == null)
            {
                return null;
            }
            BattleCampRefereeSituationInfo_SelfAllDead battleCampRefereeSituationInfo_SelfAllDead = new BattleCampRefereeSituationInfo_SelfAllDead();
            battleCampRefereeSituationInfo_SelfAllDead.enabled = battleCampRefereeSituation_SelfAllDead.enabled;
            battleCampRefereeSituationInfo_SelfAllDead.desc = battleCampRefereeSituation_SelfAllDead.desc;
            return battleCampRefereeSituationInfo_SelfAllDead;
        }
        
        public static BattleCampRefereeSituationInfo_EntityListAllDead GetBattleCampRefereeSituationInfo_EntityListAllDead(Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EntityListAllDead battleCampRefereeSituation_EntityListAllDead)
        {
            if (battleCampRefereeSituation_EntityListAllDead == null)
            {
                return null;
            }
            BattleCampRefereeSituationInfo_EntityListAllDead battleCampRefereeSituationInfo_EntityListAllDead = new BattleCampRefereeSituationInfo_EntityListAllDead();
            foreach(var entityUid in battleCampRefereeSituation_EntityListAllDead.entityUidList)
            {
                battleCampRefereeSituationInfo_EntityListAllDead.entityUidList.Add(entityUid);
            }
            battleCampRefereeSituationInfo_EntityListAllDead.enabled = battleCampRefereeSituation_EntityListAllDead.enabled;
            battleCampRefereeSituationInfo_EntityListAllDead.desc = battleCampRefereeSituation_EntityListAllDead.desc;
            return battleCampRefereeSituationInfo_EntityListAllDead;
        }
        
        public static BattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid GetBattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid(Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid battleCampRefereeSituation_AnyActorOccupyAnyGrid)
        {
            if (battleCampRefereeSituation_AnyActorOccupyAnyGrid == null)
            {
                return null;
            }
            BattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid battleCampRefereeSituationInfo_AnyActorOccupyAnyGrid = new BattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid();
            foreach(var actorUid in battleCampRefereeSituation_AnyActorOccupyAnyGrid.actorUidList)
            {
                battleCampRefereeSituationInfo_AnyActorOccupyAnyGrid.actorUidList.Add(actorUid);
            }
            battleCampRefereeSituationInfo_AnyActorOccupyAnyGrid.pos = GetGridPosition(battleCampRefereeSituation_AnyActorOccupyAnyGrid.pos);
            battleCampRefereeSituationInfo_AnyActorOccupyAnyGrid.enabled = battleCampRefereeSituation_AnyActorOccupyAnyGrid.enabled;
            battleCampRefereeSituationInfo_AnyActorOccupyAnyGrid.desc = battleCampRefereeSituation_AnyActorOccupyAnyGrid.desc;
            return battleCampRefereeSituationInfo_AnyActorOccupyAnyGrid;
        }
        
        public static BattleStageTriggerInfo GetBattleStageTriggerInfo(Phoenix.ConfigData.BattleStageTriggerConfigData battleStageTrigger)
        {
            if (battleStageTrigger == null)
            {
                return null;
            }
            BattleStageTriggerInfo battleStageTriggerInfo = new BattleStageTriggerInfo();
            battleStageTriggerInfo.trigger = GetBattleTriggerInfo(battleStageTrigger.trigger);
            battleStageTriggerInfo.limit = GetBattleTriggerLimitInfo(battleStageTrigger.limit);
            foreach(var action in battleStageTrigger.actionList)
            {
                battleStageTriggerInfo.actionList.Add(GetBattleStageActionInfo(action));
            }
            return battleStageTriggerInfo;
        }
        
        public static BattleTriggerInfo GetBattleTriggerInfo(Phoenix.ConfigData.BattleTriggerConfigData battleTrigger)
        {
            if (battleTrigger == null)
            {
                return null;
            }
            BattleTriggerInfo battleTriggerInfo = null;
            switch (battleTrigger.funcType)
            {
                case Phoenix.ConfigData.BattleTriggerType.StageEnter:
                    battleTriggerInfo = GetBattleTriggerInfo_StageEnter(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_StageEnter);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.StageWin:
                    battleTriggerInfo = GetBattleTriggerInfo_StageWin(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_StageWin);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.StageLose:
                    battleTriggerInfo = GetBattleTriggerInfo_StageLose(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_StageLose);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.TurnStart:
                    battleTriggerInfo = GetBattleTriggerInfo_TurnStart(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_TurnStart);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.TurnEnd:
                    battleTriggerInfo = GetBattleTriggerInfo_TurnEnd(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_TurnEnd);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.AnyEntityOccupyPos:
                    battleTriggerInfo = GetBattleTriggerInfo_AnyEntityOccupyPos(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyPos);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.AnyEntityOccupyArea:
                    battleTriggerInfo = GetBattleTriggerInfo_AnyEntityOccupyArea(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyArea);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.AnyEntityOccupyGridOpened:
                    battleTriggerInfo = GetBattleTriggerInfo_AnyEntityOccupyGridOpened(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyGridOpened);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.EntityHpChange:
                    battleTriggerInfo = GetBattleTriggerInfo_EntityHpChange(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChange);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.EntityHpChangeOpened:
                    battleTriggerInfo = GetBattleTriggerInfo_EntityHpChangeOpened(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeOpened);
                    break;
                case Phoenix.ConfigData.BattleTriggerType.EntityDead:
                    battleTriggerInfo = GetBattleTriggerInfo_EntityDead(battleTrigger as Phoenix.ConfigData.BattleTriggerConfigData_EntityDead);
                    break;
            }
            if (battleTriggerInfo != null)
            {
                foreach(var condition in battleTrigger.conditionList)
                {
                    battleTriggerInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
                }
            }
            return battleTriggerInfo;
        }
        
        public static BattleArgumentInfo_Condition GetBattleArgumentInfo_Condition(Phoenix.ConfigData.BattleArgumentConfigData_Condition battleArgument_Condition)
        {
            if (battleArgument_Condition == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition battleArgumentInfo_Condition = null;
            switch (battleArgument_Condition.funcType)
            {
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Not:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Not(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Not);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.And:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_And(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_And);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Or:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Or(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Or);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Random:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Random(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Random);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Action_During:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Action_During(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Action_Caused:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Action_Caused(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_Caused);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Compare_Variable:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Compare_Variable(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_Variable);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Compare_VariableOpend:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Compare_VariableOpend(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableOpend);
                    break;
                case Phoenix.ConfigData.BattleArgumentConditionFuncType.Check_GlobalVar:
                    battleArgumentInfo_Condition = GetBattleArgumentInfo_Condition_Check_GlobalVar(battleArgument_Condition as Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar);
                    break;
            }
            if (battleArgumentInfo_Condition != null)
            {
            }
            return battleArgumentInfo_Condition;
        }
        
        public static BattleArgumentInfo_Condition_Not GetBattleArgumentInfo_Condition_Not(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Not battleArgument_Condition_Not)
        {
            if (battleArgument_Condition_Not == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Not battleArgumentInfo_Condition_Not = new BattleArgumentInfo_Condition_Not();
            battleArgumentInfo_Condition_Not.condition = GetBattleArgumentInfo_Condition(battleArgument_Condition_Not.condition);
            return battleArgumentInfo_Condition_Not;
        }
        
        public static BattleArgumentInfo_Condition_And GetBattleArgumentInfo_Condition_And(Phoenix.ConfigData.BattleArgumentConfigData_Condition_And battleArgument_Condition_And)
        {
            if (battleArgument_Condition_And == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_And battleArgumentInfo_Condition_And = new BattleArgumentInfo_Condition_And();
            foreach(var condition in battleArgument_Condition_And.conditionList)
            {
                battleArgumentInfo_Condition_And.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleArgumentInfo_Condition_And;
        }
        
        public static BattleArgumentInfo_Condition_Or GetBattleArgumentInfo_Condition_Or(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Or battleArgument_Condition_Or)
        {
            if (battleArgument_Condition_Or == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Or battleArgumentInfo_Condition_Or = new BattleArgumentInfo_Condition_Or();
            foreach(var condition in battleArgument_Condition_Or.conditionList)
            {
                battleArgumentInfo_Condition_Or.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleArgumentInfo_Condition_Or;
        }
        
        public static BattleArgumentInfo_Condition_Random GetBattleArgumentInfo_Condition_Random(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Random battleArgument_Condition_Random)
        {
            if (battleArgument_Condition_Random == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Random battleArgumentInfo_Condition_Random = new BattleArgumentInfo_Condition_Random();
            battleArgumentInfo_Condition_Random.random = new FixedValue(battleArgument_Condition_Random.random, 100);
            return battleArgumentInfo_Condition_Random;
        }
        
        public static BattleArgumentInfo_Condition_Action_During GetBattleArgumentInfo_Condition_Action_During(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During battleArgument_Condition_Action_During)
        {
            if (battleArgument_Condition_Action_During == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Action_During battleArgumentInfo_Condition_Action_During = new BattleArgumentInfo_Condition_Action_During();
            battleArgumentInfo_Condition_Action_During.subject = GetBattleArgumentInfo_Entity(battleArgument_Condition_Action_During.subject);
            battleArgumentInfo_Condition_Action_During.isActive = battleArgument_Condition_Action_During.isActive;
            battleArgumentInfo_Condition_Action_During.isSkill = battleArgument_Condition_Action_During.isSkill;
            return battleArgumentInfo_Condition_Action_During;
        }
        
        public static BattleArgumentInfo_Entity GetBattleArgumentInfo_Entity(Phoenix.ConfigData.BattleArgumentConfigData_Entity battleArgument_Entity)
        {
            if (battleArgument_Entity == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity battleArgumentInfo_Entity = null;
            switch (battleArgument_Entity.funcType)
            {
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Uid:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_Uid(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.UidList:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_UidList(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Self:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_Self(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Union:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_Union(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Summoner:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_Summoner(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.CombatTarget:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_CombatTarget(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.SkillCaster:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Entity_SkillCaster(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Destruct_Range_Self:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Destruct_Range_Self(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Destruct_Pos1:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Destruct_Pos1(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Destruct_Range_Select1:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Destruct_Range_Select1(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Destruct_Pos_Custom:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Destruct_Pos_Custom(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.Destruct_Range_Custom:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_Destruct_Range_Custom(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom);
                    break;
                case Phoenix.ConfigData.BattleArgumentEntityFuncType.LastSkillEffectTarget:
                    battleArgumentInfo_Entity = GetBattleArgumentInfo_LastSkillEffectTarget(battleArgument_Entity as Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget);
                    break;
            }
            if (battleArgumentInfo_Entity != null)
            {
            }
            return battleArgumentInfo_Entity;
        }
        
        public static BattleArgumentInfo_Entity_Uid GetBattleArgumentInfo_Entity_Uid(Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid battleArgument_Entity_Uid)
        {
            if (battleArgument_Entity_Uid == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_Uid battleArgumentInfo_Entity_Uid = new BattleArgumentInfo_Entity_Uid();
            battleArgumentInfo_Entity_Uid.uid = battleArgument_Entity_Uid.uid;
            return battleArgumentInfo_Entity_Uid;
        }
        
        public static BattleArgumentInfo_Entity_UidList GetBattleArgumentInfo_Entity_UidList(Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList battleArgument_Entity_UidList)
        {
            if (battleArgument_Entity_UidList == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_UidList battleArgumentInfo_Entity_UidList = new BattleArgumentInfo_Entity_UidList();
            foreach(var uid in battleArgument_Entity_UidList.uidList)
            {
                battleArgumentInfo_Entity_UidList.uidList.Add(uid);
            }
            return battleArgumentInfo_Entity_UidList;
        }
        
        public static BattleArgumentInfo_Entity_Self GetBattleArgumentInfo_Entity_Self(Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self battleArgument_Entity_Self)
        {
            if (battleArgument_Entity_Self == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_Self battleArgumentInfo_Entity_Self = new BattleArgumentInfo_Entity_Self();
            return battleArgumentInfo_Entity_Self;
        }
        
        public static BattleArgumentInfo_Entity_Union GetBattleArgumentInfo_Entity_Union(Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union battleArgument_Entity_Union)
        {
            if (battleArgument_Entity_Union == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_Union battleArgumentInfo_Entity_Union = new BattleArgumentInfo_Entity_Union();
            foreach(var entity in battleArgument_Entity_Union.entityList)
            {
                battleArgumentInfo_Entity_Union.entityList.Add(GetBattleArgumentInfo_Entity(entity));
            }
            return battleArgumentInfo_Entity_Union;
        }
        
        public static BattleArgumentInfo_Entity_Summoner GetBattleArgumentInfo_Entity_Summoner(Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner battleArgument_Entity_Summoner)
        {
            if (battleArgument_Entity_Summoner == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_Summoner battleArgumentInfo_Entity_Summoner = new BattleArgumentInfo_Entity_Summoner();
            return battleArgumentInfo_Entity_Summoner;
        }
        
        public static BattleArgumentInfo_Entity_CombatTarget GetBattleArgumentInfo_Entity_CombatTarget(Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget battleArgument_Entity_CombatTarget)
        {
            if (battleArgument_Entity_CombatTarget == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_CombatTarget battleArgumentInfo_Entity_CombatTarget = new BattleArgumentInfo_Entity_CombatTarget();
            return battleArgumentInfo_Entity_CombatTarget;
        }
        
        public static BattleArgumentInfo_Entity_SkillCaster GetBattleArgumentInfo_Entity_SkillCaster(Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster battleArgument_Entity_SkillCaster)
        {
            if (battleArgument_Entity_SkillCaster == null)
            {
                return null;
            }
            BattleArgumentInfo_Entity_SkillCaster battleArgumentInfo_Entity_SkillCaster = new BattleArgumentInfo_Entity_SkillCaster();
            return battleArgumentInfo_Entity_SkillCaster;
        }
        
        public static BattleArgumentInfo_Destruct_Range_Self GetBattleArgumentInfo_Destruct_Range_Self(Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self battleArgument_Destruct_Range_Self)
        {
            if (battleArgument_Destruct_Range_Self == null)
            {
                return null;
            }
            BattleArgumentInfo_Destruct_Range_Self battleArgumentInfo_Destruct_Range_Self = new BattleArgumentInfo_Destruct_Range_Self();
            battleArgumentInfo_Destruct_Range_Self.rangeId = (TargetSelectRangeId)battleArgument_Destruct_Range_Self.rangeId;
            battleArgumentInfo_Destruct_Range_Self.campRefType = (BattleCampRefType)battleArgument_Destruct_Range_Self.campRefType;
            return battleArgumentInfo_Destruct_Range_Self;
        }
        
        public static BattleArgumentInfo_Destruct_Pos1 GetBattleArgumentInfo_Destruct_Pos1(Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1 battleArgument_Destruct_Pos1)
        {
            if (battleArgument_Destruct_Pos1 == null)
            {
                return null;
            }
            BattleArgumentInfo_Destruct_Pos1 battleArgumentInfo_Destruct_Pos1 = new BattleArgumentInfo_Destruct_Pos1();
            return battleArgumentInfo_Destruct_Pos1;
        }
        
        public static BattleArgumentInfo_Destruct_Range_Select1 GetBattleArgumentInfo_Destruct_Range_Select1(Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1 battleArgument_Destruct_Range_Select1)
        {
            if (battleArgument_Destruct_Range_Select1 == null)
            {
                return null;
            }
            BattleArgumentInfo_Destruct_Range_Select1 battleArgumentInfo_Destruct_Range_Select1 = new BattleArgumentInfo_Destruct_Range_Select1();
            battleArgumentInfo_Destruct_Range_Select1.rangeId = (TargetSelectRangeId)battleArgument_Destruct_Range_Select1.rangeId;
            battleArgumentInfo_Destruct_Range_Select1.campRefType = (BattleCampRefType)battleArgument_Destruct_Range_Select1.campRefType;
            return battleArgumentInfo_Destruct_Range_Select1;
        }
        
        public static BattleArgumentInfo_Destruct_Pos_Custom GetBattleArgumentInfo_Destruct_Pos_Custom(Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom battleArgument_Destruct_Pos_Custom)
        {
            if (battleArgument_Destruct_Pos_Custom == null)
            {
                return null;
            }
            BattleArgumentInfo_Destruct_Pos_Custom battleArgumentInfo_Destruct_Pos_Custom = new BattleArgumentInfo_Destruct_Pos_Custom();
            battleArgumentInfo_Destruct_Pos_Custom.centerGrid = GetBattleArgumentInfo_Grid(battleArgument_Destruct_Pos_Custom.centerGrid);
            return battleArgumentInfo_Destruct_Pos_Custom;
        }
        
        public static BattleArgumentInfo_Grid GetBattleArgumentInfo_Grid(Phoenix.ConfigData.BattleArgumentConfigData_Grid battleArgument_Grid)
        {
            if (battleArgument_Grid == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid battleArgumentInfo_Grid = null;
            switch (battleArgument_Grid.funcType)
            {
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Pos:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Pos(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.PosList:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_PosList(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosList);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Area:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Area(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Area);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Range_Self:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Range_Self(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Self);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Range_Custom:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Range_Custom(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Custom);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Pos1:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Pos1(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos1);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Range_Select1:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Range_Select1(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Select1);
                    break;
                case Phoenix.ConfigData.BattleArgumentGridFuncType.Pos_Teleport1:
                    battleArgumentInfo_Grid = GetBattleArgumentInfo_Grid_Pos_Teleport1(battleArgument_Grid as Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos_Teleport1);
                    break;
            }
            if (battleArgumentInfo_Grid != null)
            {
            }
            return battleArgumentInfo_Grid;
        }
        
        public static BattleArgumentInfo_Grid_Pos GetBattleArgumentInfo_Grid_Pos(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos battleArgument_Grid_Pos)
        {
            if (battleArgument_Grid_Pos == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Pos battleArgumentInfo_Grid_Pos = new BattleArgumentInfo_Grid_Pos();
            battleArgumentInfo_Grid_Pos.pos = GetGridPosition(battleArgument_Grid_Pos.pos);
            return battleArgumentInfo_Grid_Pos;
        }
        
        public static BattleArgumentInfo_Grid_PosList GetBattleArgumentInfo_Grid_PosList(Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosList battleArgument_Grid_PosList)
        {
            if (battleArgument_Grid_PosList == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_PosList battleArgumentInfo_Grid_PosList = new BattleArgumentInfo_Grid_PosList();
            foreach(var pos in battleArgument_Grid_PosList.pos)
            {
                battleArgumentInfo_Grid_PosList.pos.Add(GetGridPosition(pos));
            }
            return battleArgumentInfo_Grid_PosList;
        }
        
        public static BattleArgumentInfo_Grid_Area GetBattleArgumentInfo_Grid_Area(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Area battleArgument_Grid_Area)
        {
            if (battleArgument_Grid_Area == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Area battleArgumentInfo_Grid_Area = new BattleArgumentInfo_Grid_Area();
            battleArgumentInfo_Grid_Area.startPos = GetGridPosition(battleArgument_Grid_Area.startPos);
            battleArgumentInfo_Grid_Area.endPos = GetGridPosition(battleArgument_Grid_Area.endPos);
            return battleArgumentInfo_Grid_Area;
        }
        
        public static BattleArgumentInfo_Grid_Range_Self GetBattleArgumentInfo_Grid_Range_Self(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Self battleArgument_Grid_Range_Self)
        {
            if (battleArgument_Grid_Range_Self == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Range_Self battleArgumentInfo_Grid_Range_Self = new BattleArgumentInfo_Grid_Range_Self();
            battleArgumentInfo_Grid_Range_Self.rangeId = (TargetSelectRangeId)battleArgument_Grid_Range_Self.rangeId;
            return battleArgumentInfo_Grid_Range_Self;
        }
        
        public static BattleArgumentInfo_Grid_Range_Custom GetBattleArgumentInfo_Grid_Range_Custom(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Custom battleArgument_Grid_Range_Custom)
        {
            if (battleArgument_Grid_Range_Custom == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Range_Custom battleArgumentInfo_Grid_Range_Custom = new BattleArgumentInfo_Grid_Range_Custom();
            battleArgumentInfo_Grid_Range_Custom.basedGrid = GetBattleArgumentInfo_Grid(battleArgument_Grid_Range_Custom.basedGrid);
            battleArgumentInfo_Grid_Range_Custom.dir = GetBattleArgumentInfo_Dir(battleArgument_Grid_Range_Custom.dir);
            battleArgumentInfo_Grid_Range_Custom.rangeId = (TargetSelectRangeId)battleArgument_Grid_Range_Custom.rangeId;
            return battleArgumentInfo_Grid_Range_Custom;
        }
        
        public static BattleArgumentInfo_Dir GetBattleArgumentInfo_Dir(Phoenix.ConfigData.BattleArgumentConfigData_Dir battleArgument_Dir)
        {
            if (battleArgument_Dir == null)
            {
                return null;
            }
            BattleArgumentInfo_Dir battleArgumentInfo_Dir = null;
            switch (battleArgument_Dir.funcType)
            {
                case Phoenix.ConfigData.BattleArgumentDirFuncType.Const:
                    battleArgumentInfo_Dir = GetBattleArgumentInfo_Dir_Const(battleArgument_Dir as Phoenix.ConfigData.BattleArgumentConfigData_Dir_Const);
                    break;
            }
            if (battleArgumentInfo_Dir != null)
            {
            }
            return battleArgumentInfo_Dir;
        }
        
        public static BattleArgumentInfo_Dir_Const GetBattleArgumentInfo_Dir_Const(Phoenix.ConfigData.BattleArgumentConfigData_Dir_Const battleArgument_Dir_Const)
        {
            if (battleArgument_Dir_Const == null)
            {
                return null;
            }
            BattleArgumentInfo_Dir_Const battleArgumentInfo_Dir_Const = new BattleArgumentInfo_Dir_Const();
            battleArgumentInfo_Dir_Const.dirType = GetGridDirType(battleArgument_Dir_Const.dirType);
            return battleArgumentInfo_Dir_Const;
        }
        
        public static BattleArgumentInfo_Grid_Pos1 GetBattleArgumentInfo_Grid_Pos1(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos1 battleArgument_Grid_Pos1)
        {
            if (battleArgument_Grid_Pos1 == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Pos1 battleArgumentInfo_Grid_Pos1 = new BattleArgumentInfo_Grid_Pos1();
            return battleArgumentInfo_Grid_Pos1;
        }
        
        public static BattleArgumentInfo_Grid_Range_Select1 GetBattleArgumentInfo_Grid_Range_Select1(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Select1 battleArgument_Grid_Range_Select1)
        {
            if (battleArgument_Grid_Range_Select1 == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Range_Select1 battleArgumentInfo_Grid_Range_Select1 = new BattleArgumentInfo_Grid_Range_Select1();
            battleArgumentInfo_Grid_Range_Select1.rangeId = (TargetSelectRangeId)battleArgument_Grid_Range_Select1.rangeId;
            return battleArgumentInfo_Grid_Range_Select1;
        }
        
        public static BattleArgumentInfo_Grid_Pos_Teleport1 GetBattleArgumentInfo_Grid_Pos_Teleport1(Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos_Teleport1 battleArgument_Grid_Pos_Teleport1)
        {
            if (battleArgument_Grid_Pos_Teleport1 == null)
            {
                return null;
            }
            BattleArgumentInfo_Grid_Pos_Teleport1 battleArgumentInfo_Grid_Pos_Teleport1 = new BattleArgumentInfo_Grid_Pos_Teleport1();
            return battleArgumentInfo_Grid_Pos_Teleport1;
        }
        
        public static BattleArgumentInfo_Destruct_Range_Custom GetBattleArgumentInfo_Destruct_Range_Custom(Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom battleArgument_Destruct_Range_Custom)
        {
            if (battleArgument_Destruct_Range_Custom == null)
            {
                return null;
            }
            BattleArgumentInfo_Destruct_Range_Custom battleArgumentInfo_Destruct_Range_Custom = new BattleArgumentInfo_Destruct_Range_Custom();
            battleArgumentInfo_Destruct_Range_Custom.centerGrid = GetBattleArgumentInfo_Grid(battleArgument_Destruct_Range_Custom.centerGrid);
            battleArgumentInfo_Destruct_Range_Custom.dir = GetBattleArgumentInfo_Dir(battleArgument_Destruct_Range_Custom.dir);
            battleArgumentInfo_Destruct_Range_Custom.rangeId = (TargetSelectRangeId)battleArgument_Destruct_Range_Custom.rangeId;
            battleArgumentInfo_Destruct_Range_Custom.campRefType = (BattleCampRefType)battleArgument_Destruct_Range_Custom.campRefType;
            return battleArgumentInfo_Destruct_Range_Custom;
        }
        
        public static BattleArgumentInfo_LastSkillEffectTarget GetBattleArgumentInfo_LastSkillEffectTarget(Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget battleArgument_LastSkillEffectTarget)
        {
            if (battleArgument_LastSkillEffectTarget == null)
            {
                return null;
            }
            BattleArgumentInfo_LastSkillEffectTarget battleArgumentInfo_LastSkillEffectTarget = new BattleArgumentInfo_LastSkillEffectTarget();
            return battleArgumentInfo_LastSkillEffectTarget;
        }
        
        public static BattleArgumentInfo_Condition_Action_Caused GetBattleArgumentInfo_Condition_Action_Caused(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_Caused battleArgument_Condition_Action_Caused)
        {
            if (battleArgument_Condition_Action_Caused == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Action_Caused battleArgumentInfo_Condition_Action_Caused = new BattleArgumentInfo_Condition_Action_Caused();
            battleArgumentInfo_Condition_Action_Caused.subject = GetBattleArgumentInfo_Entity(battleArgument_Condition_Action_Caused.subject);
            battleArgumentInfo_Condition_Action_Caused.criticalHit = battleArgument_Condition_Action_Caused.criticalHit;
            battleArgumentInfo_Condition_Action_Caused.damage = battleArgument_Condition_Action_Caused.damage;
            battleArgumentInfo_Condition_Action_Caused.kill = battleArgument_Condition_Action_Caused.kill;
            battleArgumentInfo_Condition_Action_Caused.assistGuard = battleArgument_Condition_Action_Caused.assistGuard;
            return battleArgumentInfo_Condition_Action_Caused;
        }
        
        public static BattleArgumentInfo_Condition_Compare_Variable GetBattleArgumentInfo_Condition_Compare_Variable(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_Variable battleArgument_Condition_Compare_Variable)
        {
            if (battleArgument_Condition_Compare_Variable == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Compare_Variable battleArgumentInfo_Condition_Compare_Variable = new BattleArgumentInfo_Condition_Compare_Variable();
            battleArgumentInfo_Condition_Compare_Variable.srcValue = GetBattleArgumentInfo_Value(battleArgument_Condition_Compare_Variable.srcValue);
            battleArgumentInfo_Condition_Compare_Variable.compareType = (CompareType)battleArgument_Condition_Compare_Variable.compareType;
            battleArgumentInfo_Condition_Compare_Variable.destValue = battleArgument_Condition_Compare_Variable.destValue;
            return battleArgumentInfo_Condition_Compare_Variable;
        }
        
        public static BattleArgumentInfo_Value GetBattleArgumentInfo_Value(Phoenix.ConfigData.BattleArgumentConfigData_Value battleArgument_Value)
        {
            if (battleArgument_Value == null)
            {
                return null;
            }
            BattleArgumentInfo_Value battleArgumentInfo_Value = null;
            switch (battleArgument_Value.funcType)
            {
                case Phoenix.ConfigData.BattleArgumentValueFuncType.MoveDist:
                    battleArgumentInfo_Value = GetBattleArgumentInfo_Value_MoveDist(battleArgument_Value as Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist);
                    break;
                case Phoenix.ConfigData.BattleArgumentValueFuncType.TargetDist:
                    battleArgumentInfo_Value = GetBattleArgumentInfo_Value_TargetDist(battleArgument_Value as Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist);
                    break;
                case Phoenix.ConfigData.BattleArgumentValueFuncType.TurnIndex:
                    battleArgumentInfo_Value = GetBattleArgumentInfo_Value_TurnIndex(battleArgument_Value as Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex);
                    break;
                case Phoenix.ConfigData.BattleArgumentValueFuncType.StepIndex:
                    battleArgumentInfo_Value = GetBattleArgumentInfo_Value_StepIndex(battleArgument_Value as Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex);
                    break;
                case Phoenix.ConfigData.BattleArgumentValueFuncType.TeamEnergy:
                    battleArgumentInfo_Value = GetBattleArgumentInfo_Value_TeamEnergy(battleArgument_Value as Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy);
                    break;
                case Phoenix.ConfigData.BattleArgumentValueFuncType.HpRate:
                    battleArgumentInfo_Value = GetBattleArgumentInfo_Value_HpRate(battleArgument_Value as Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate);
                    break;
            }
            if (battleArgumentInfo_Value != null)
            {
            }
            return battleArgumentInfo_Value;
        }
        
        public static BattleArgumentInfo_Value_MoveDist GetBattleArgumentInfo_Value_MoveDist(Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist battleArgument_Value_MoveDist)
        {
            if (battleArgument_Value_MoveDist == null)
            {
                return null;
            }
            BattleArgumentInfo_Value_MoveDist battleArgumentInfo_Value_MoveDist = new BattleArgumentInfo_Value_MoveDist();
            return battleArgumentInfo_Value_MoveDist;
        }
        
        public static BattleArgumentInfo_Value_TargetDist GetBattleArgumentInfo_Value_TargetDist(Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist battleArgument_Value_TargetDist)
        {
            if (battleArgument_Value_TargetDist == null)
            {
                return null;
            }
            BattleArgumentInfo_Value_TargetDist battleArgumentInfo_Value_TargetDist = new BattleArgumentInfo_Value_TargetDist();
            return battleArgumentInfo_Value_TargetDist;
        }
        
        public static BattleArgumentInfo_Value_TurnIndex GetBattleArgumentInfo_Value_TurnIndex(Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex battleArgument_Value_TurnIndex)
        {
            if (battleArgument_Value_TurnIndex == null)
            {
                return null;
            }
            BattleArgumentInfo_Value_TurnIndex battleArgumentInfo_Value_TurnIndex = new BattleArgumentInfo_Value_TurnIndex();
            return battleArgumentInfo_Value_TurnIndex;
        }
        
        public static BattleArgumentInfo_Value_StepIndex GetBattleArgumentInfo_Value_StepIndex(Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex battleArgument_Value_StepIndex)
        {
            if (battleArgument_Value_StepIndex == null)
            {
                return null;
            }
            BattleArgumentInfo_Value_StepIndex battleArgumentInfo_Value_StepIndex = new BattleArgumentInfo_Value_StepIndex();
            return battleArgumentInfo_Value_StepIndex;
        }
        
        public static BattleArgumentInfo_Value_TeamEnergy GetBattleArgumentInfo_Value_TeamEnergy(Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy battleArgument_Value_TeamEnergy)
        {
            if (battleArgument_Value_TeamEnergy == null)
            {
                return null;
            }
            BattleArgumentInfo_Value_TeamEnergy battleArgumentInfo_Value_TeamEnergy = new BattleArgumentInfo_Value_TeamEnergy();
            battleArgumentInfo_Value_TeamEnergy.team = GetBattleArgumentInfo_Team(battleArgument_Value_TeamEnergy.team);
            return battleArgumentInfo_Value_TeamEnergy;
        }
        
        public static BattleArgumentInfo_Team GetBattleArgumentInfo_Team(Phoenix.ConfigData.BattleArgumentConfigData_Team battleArgument_Team)
        {
            if (battleArgument_Team == null)
            {
                return null;
            }
            BattleArgumentInfo_Team battleArgumentInfo_Team = null;
            switch (battleArgument_Team.funcType)
            {
                case Phoenix.ConfigData.BattleArgumentTeamFuncType.Uid:
                    battleArgumentInfo_Team = GetBattleArgumentInfo_Team_Uid(battleArgument_Team as Phoenix.ConfigData.BattleArgumentConfigData_Team_Uid);
                    break;
                case Phoenix.ConfigData.BattleArgumentTeamFuncType.Self:
                    battleArgumentInfo_Team = GetBattleArgumentInfo_Team_Self(battleArgument_Team as Phoenix.ConfigData.BattleArgumentConfigData_Team_Self);
                    break;
                case Phoenix.ConfigData.BattleArgumentTeamFuncType.Entity:
                    battleArgumentInfo_Team = GetBattleArgumentInfo_Team_Entity(battleArgument_Team as Phoenix.ConfigData.BattleArgumentConfigData_Team_Entity);
                    break;
            }
            if (battleArgumentInfo_Team != null)
            {
            }
            return battleArgumentInfo_Team;
        }
        
        public static BattleArgumentInfo_Team_Uid GetBattleArgumentInfo_Team_Uid(Phoenix.ConfigData.BattleArgumentConfigData_Team_Uid battleArgument_Team_Uid)
        {
            if (battleArgument_Team_Uid == null)
            {
                return null;
            }
            BattleArgumentInfo_Team_Uid battleArgumentInfo_Team_Uid = new BattleArgumentInfo_Team_Uid();
            battleArgumentInfo_Team_Uid.uid = battleArgument_Team_Uid.uid;
            return battleArgumentInfo_Team_Uid;
        }
        
        public static BattleArgumentInfo_Team_Self GetBattleArgumentInfo_Team_Self(Phoenix.ConfigData.BattleArgumentConfigData_Team_Self battleArgument_Team_Self)
        {
            if (battleArgument_Team_Self == null)
            {
                return null;
            }
            BattleArgumentInfo_Team_Self battleArgumentInfo_Team_Self = new BattleArgumentInfo_Team_Self();
            return battleArgumentInfo_Team_Self;
        }
        
        public static BattleArgumentInfo_Team_Entity GetBattleArgumentInfo_Team_Entity(Phoenix.ConfigData.BattleArgumentConfigData_Team_Entity battleArgument_Team_Entity)
        {
            if (battleArgument_Team_Entity == null)
            {
                return null;
            }
            BattleArgumentInfo_Team_Entity battleArgumentInfo_Team_Entity = new BattleArgumentInfo_Team_Entity();
            battleArgumentInfo_Team_Entity.entity = GetBattleArgumentInfo_Entity(battleArgument_Team_Entity.entity);
            return battleArgumentInfo_Team_Entity;
        }
        
        public static BattleArgumentInfo_Value_HpRate GetBattleArgumentInfo_Value_HpRate(Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate battleArgument_Value_HpRate)
        {
            if (battleArgument_Value_HpRate == null)
            {
                return null;
            }
            BattleArgumentInfo_Value_HpRate battleArgumentInfo_Value_HpRate = new BattleArgumentInfo_Value_HpRate();
            battleArgumentInfo_Value_HpRate.entity = GetBattleArgumentInfo_Entity(battleArgument_Value_HpRate.entity);
            return battleArgumentInfo_Value_HpRate;
        }
        
        public static BattleArgumentInfo_Condition_Compare_VariableOpend GetBattleArgumentInfo_Condition_Compare_VariableOpend(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableOpend battleArgument_Condition_Compare_VariableOpend)
        {
            if (battleArgument_Condition_Compare_VariableOpend == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Compare_VariableOpend battleArgumentInfo_Condition_Compare_VariableOpend = new BattleArgumentInfo_Condition_Compare_VariableOpend();
            battleArgumentInfo_Condition_Compare_VariableOpend.srcValue = GetBattleArgumentInfo_Value(battleArgument_Condition_Compare_VariableOpend.srcValue);
            battleArgumentInfo_Condition_Compare_VariableOpend.compareType = (CompareType)battleArgument_Condition_Compare_VariableOpend.compareType;
            battleArgumentInfo_Condition_Compare_VariableOpend.destValue = GetBattleArgumentInfo_Value(battleArgument_Condition_Compare_VariableOpend.destValue);
            return battleArgumentInfo_Condition_Compare_VariableOpend;
        }
        
        public static BattleArgumentInfo_Condition_Check_GlobalVar GetBattleArgumentInfo_Condition_Check_GlobalVar(Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar battleArgument_Condition_Check_GlobalVar)
        {
            if (battleArgument_Condition_Check_GlobalVar == null)
            {
                return null;
            }
            BattleArgumentInfo_Condition_Check_GlobalVar battleArgumentInfo_Condition_Check_GlobalVar = new BattleArgumentInfo_Condition_Check_GlobalVar();
            battleArgumentInfo_Condition_Check_GlobalVar.id = battleArgument_Condition_Check_GlobalVar.id;
            battleArgumentInfo_Condition_Check_GlobalVar.boolValue = battleArgument_Condition_Check_GlobalVar.boolValue;
            return battleArgumentInfo_Condition_Check_GlobalVar;
        }
        
        public static BattleTriggerInfo_StageEnter GetBattleTriggerInfo_StageEnter(Phoenix.ConfigData.BattleTriggerConfigData_StageEnter battleTrigger_StageEnter)
        {
            if (battleTrigger_StageEnter == null)
            {
                return null;
            }
            BattleTriggerInfo_StageEnter battleTriggerInfo_StageEnter = new BattleTriggerInfo_StageEnter();
            foreach(var condition in battleTrigger_StageEnter.conditionList)
            {
                battleTriggerInfo_StageEnter.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_StageEnter;
        }
        
        public static BattleTriggerInfo_StageWin GetBattleTriggerInfo_StageWin(Phoenix.ConfigData.BattleTriggerConfigData_StageWin battleTrigger_StageWin)
        {
            if (battleTrigger_StageWin == null)
            {
                return null;
            }
            BattleTriggerInfo_StageWin battleTriggerInfo_StageWin = new BattleTriggerInfo_StageWin();
            foreach(var condition in battleTrigger_StageWin.conditionList)
            {
                battleTriggerInfo_StageWin.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_StageWin;
        }
        
        public static BattleTriggerInfo_StageLose GetBattleTriggerInfo_StageLose(Phoenix.ConfigData.BattleTriggerConfigData_StageLose battleTrigger_StageLose)
        {
            if (battleTrigger_StageLose == null)
            {
                return null;
            }
            BattleTriggerInfo_StageLose battleTriggerInfo_StageLose = new BattleTriggerInfo_StageLose();
            foreach(var condition in battleTrigger_StageLose.conditionList)
            {
                battleTriggerInfo_StageLose.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_StageLose;
        }
        
        public static BattleTriggerInfo_TurnStart GetBattleTriggerInfo_TurnStart(Phoenix.ConfigData.BattleTriggerConfigData_TurnStart battleTrigger_TurnStart)
        {
            if (battleTrigger_TurnStart == null)
            {
                return null;
            }
            BattleTriggerInfo_TurnStart battleTriggerInfo_TurnStart = new BattleTriggerInfo_TurnStart();
            battleTriggerInfo_TurnStart.compareType = (CompareType)battleTrigger_TurnStart.compareType;
            battleTriggerInfo_TurnStart.turnIndex = battleTrigger_TurnStart.turnIndex;
            foreach(var condition in battleTrigger_TurnStart.conditionList)
            {
                battleTriggerInfo_TurnStart.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_TurnStart;
        }
        
        public static BattleTriggerInfo_TurnEnd GetBattleTriggerInfo_TurnEnd(Phoenix.ConfigData.BattleTriggerConfigData_TurnEnd battleTrigger_TurnEnd)
        {
            if (battleTrigger_TurnEnd == null)
            {
                return null;
            }
            BattleTriggerInfo_TurnEnd battleTriggerInfo_TurnEnd = new BattleTriggerInfo_TurnEnd();
            battleTriggerInfo_TurnEnd.compareType = (CompareType)battleTrigger_TurnEnd.compareType;
            battleTriggerInfo_TurnEnd.turnIndex = battleTrigger_TurnEnd.turnIndex;
            foreach(var condition in battleTrigger_TurnEnd.conditionList)
            {
                battleTriggerInfo_TurnEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_TurnEnd;
        }
        
        public static BattleTriggerInfo_AnyEntityOccupyPos GetBattleTriggerInfo_AnyEntityOccupyPos(Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyPos battleTrigger_AnyEntityOccupyPos)
        {
            if (battleTrigger_AnyEntityOccupyPos == null)
            {
                return null;
            }
            BattleTriggerInfo_AnyEntityOccupyPos battleTriggerInfo_AnyEntityOccupyPos = new BattleTriggerInfo_AnyEntityOccupyPos();
            battleTriggerInfo_AnyEntityOccupyPos.entityUidList = battleTrigger_AnyEntityOccupyPos.entityUidList;
            battleTriggerInfo_AnyEntityOccupyPos.pos = GetGridPosition(battleTrigger_AnyEntityOccupyPos.pos);
            foreach(var condition in battleTrigger_AnyEntityOccupyPos.conditionList)
            {
                battleTriggerInfo_AnyEntityOccupyPos.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_AnyEntityOccupyPos;
        }
        
        public static BattleTriggerInfo_AnyEntityOccupyArea GetBattleTriggerInfo_AnyEntityOccupyArea(Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyArea battleTrigger_AnyEntityOccupyArea)
        {
            if (battleTrigger_AnyEntityOccupyArea == null)
            {
                return null;
            }
            BattleTriggerInfo_AnyEntityOccupyArea battleTriggerInfo_AnyEntityOccupyArea = new BattleTriggerInfo_AnyEntityOccupyArea();
            battleTriggerInfo_AnyEntityOccupyArea.entityUidList = battleTrigger_AnyEntityOccupyArea.entityUidList;
            battleTriggerInfo_AnyEntityOccupyArea.startPos = GetGridPosition(battleTrigger_AnyEntityOccupyArea.startPos);
            battleTriggerInfo_AnyEntityOccupyArea.endPos = GetGridPosition(battleTrigger_AnyEntityOccupyArea.endPos);
            foreach(var condition in battleTrigger_AnyEntityOccupyArea.conditionList)
            {
                battleTriggerInfo_AnyEntityOccupyArea.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_AnyEntityOccupyArea;
        }
        
        public static BattleTriggerInfo_AnyEntityOccupyGridOpened GetBattleTriggerInfo_AnyEntityOccupyGridOpened(Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyGridOpened battleTrigger_AnyEntityOccupyGridOpened)
        {
            if (battleTrigger_AnyEntityOccupyGridOpened == null)
            {
                return null;
            }
            BattleTriggerInfo_AnyEntityOccupyGridOpened battleTriggerInfo_AnyEntityOccupyGridOpened = new BattleTriggerInfo_AnyEntityOccupyGridOpened();
            battleTriggerInfo_AnyEntityOccupyGridOpened.entity = GetBattleObjCheckInfo_Entity(battleTrigger_AnyEntityOccupyGridOpened.entity);
            battleTriggerInfo_AnyEntityOccupyGridOpened.grid = GetBattleArgumentInfo_Grid(battleTrigger_AnyEntityOccupyGridOpened.grid);
            foreach(var condition in battleTrigger_AnyEntityOccupyGridOpened.conditionList)
            {
                battleTriggerInfo_AnyEntityOccupyGridOpened.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_AnyEntityOccupyGridOpened;
        }
        
        public static BattleObjCheckInfo_Entity GetBattleObjCheckInfo_Entity(Phoenix.ConfigData.BattleObjCheckConfigData_Entity battleObjCheck_Entity)
        {
            if (battleObjCheck_Entity == null)
            {
                return null;
            }
            BattleObjCheckInfo_Entity battleObjCheckInfo_Entity = null;
            switch (battleObjCheck_Entity.checkType)
            {
                case Phoenix.ConfigData.BattleObjCheckEntityType.Self:
                    battleObjCheckInfo_Entity = GetBattleObjCheckInfo_Entity_Self(battleObjCheck_Entity as Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Self);
                    break;
                case Phoenix.ConfigData.BattleObjCheckEntityType.Uid:
                    battleObjCheckInfo_Entity = GetBattleObjCheckInfo_Entity_Uid(battleObjCheck_Entity as Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Uid);
                    break;
                case Phoenix.ConfigData.BattleObjCheckEntityType.CampRef:
                    battleObjCheckInfo_Entity = GetBattleObjCheckInfo_Entity_CampRef(battleObjCheck_Entity as Phoenix.ConfigData.BattleObjCheckConfigData_Entity_CampRef);
                    break;
            }
            if (battleObjCheckInfo_Entity != null)
            {
            }
            return battleObjCheckInfo_Entity;
        }
        
        public static BattleObjCheckInfo_Entity_Self GetBattleObjCheckInfo_Entity_Self(Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Self battleObjCheck_Entity_Self)
        {
            if (battleObjCheck_Entity_Self == null)
            {
                return null;
            }
            BattleObjCheckInfo_Entity_Self battleObjCheckInfo_Entity_Self = new BattleObjCheckInfo_Entity_Self();
            return battleObjCheckInfo_Entity_Self;
        }
        
        public static BattleObjCheckInfo_Entity_Uid GetBattleObjCheckInfo_Entity_Uid(Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Uid battleObjCheck_Entity_Uid)
        {
            if (battleObjCheck_Entity_Uid == null)
            {
                return null;
            }
            BattleObjCheckInfo_Entity_Uid battleObjCheckInfo_Entity_Uid = new BattleObjCheckInfo_Entity_Uid();
            battleObjCheckInfo_Entity_Uid.uid = battleObjCheck_Entity_Uid.uid;
            return battleObjCheckInfo_Entity_Uid;
        }
        
        public static BattleObjCheckInfo_Entity_CampRef GetBattleObjCheckInfo_Entity_CampRef(Phoenix.ConfigData.BattleObjCheckConfigData_Entity_CampRef battleObjCheck_Entity_CampRef)
        {
            if (battleObjCheck_Entity_CampRef == null)
            {
                return null;
            }
            BattleObjCheckInfo_Entity_CampRef battleObjCheckInfo_Entity_CampRef = new BattleObjCheckInfo_Entity_CampRef();
            battleObjCheckInfo_Entity_CampRef.campRef = (BattleCampRefType)battleObjCheck_Entity_CampRef.campRef;
            return battleObjCheckInfo_Entity_CampRef;
        }
        
        public static BattleTriggerInfo_EntityHpChange GetBattleTriggerInfo_EntityHpChange(Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChange battleTrigger_EntityHpChange)
        {
            if (battleTrigger_EntityHpChange == null)
            {
                return null;
            }
            BattleTriggerInfo_EntityHpChange battleTriggerInfo_EntityHpChange = new BattleTriggerInfo_EntityHpChange();
            battleTriggerInfo_EntityHpChange.entityUid = battleTrigger_EntityHpChange.entityUid;
            battleTriggerInfo_EntityHpChange.compareType = (CompareType)battleTrigger_EntityHpChange.compareType;
            battleTriggerInfo_EntityHpChange.hpRate = new FixedValue(battleTrigger_EntityHpChange.hpRate, 100);
            foreach(var condition in battleTrigger_EntityHpChange.conditionList)
            {
                battleTriggerInfo_EntityHpChange.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_EntityHpChange;
        }
        
        public static BattleTriggerInfo_EntityHpChangeOpened GetBattleTriggerInfo_EntityHpChangeOpened(Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeOpened battleTrigger_EntityHpChangeOpened)
        {
            if (battleTrigger_EntityHpChangeOpened == null)
            {
                return null;
            }
            BattleTriggerInfo_EntityHpChangeOpened battleTriggerInfo_EntityHpChangeOpened = new BattleTriggerInfo_EntityHpChangeOpened();
            battleTriggerInfo_EntityHpChangeOpened.entity = GetBattleObjCheckInfo_Entity(battleTrigger_EntityHpChangeOpened.entity);
            battleTriggerInfo_EntityHpChangeOpened.compareType = (CompareType)battleTrigger_EntityHpChangeOpened.compareType;
            battleTriggerInfo_EntityHpChangeOpened.hpRate = new FixedValue(battleTrigger_EntityHpChangeOpened.hpRate, 100);
            foreach(var condition in battleTrigger_EntityHpChangeOpened.conditionList)
            {
                battleTriggerInfo_EntityHpChangeOpened.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_EntityHpChangeOpened;
        }
        
        public static BattleTriggerInfo_EntityDead GetBattleTriggerInfo_EntityDead(Phoenix.ConfigData.BattleTriggerConfigData_EntityDead battleTrigger_EntityDead)
        {
            if (battleTrigger_EntityDead == null)
            {
                return null;
            }
            BattleTriggerInfo_EntityDead battleTriggerInfo_EntityDead = new BattleTriggerInfo_EntityDead();
            battleTriggerInfo_EntityDead.entityUid = battleTrigger_EntityDead.entityUid;
            foreach(var condition in battleTrigger_EntityDead.conditionList)
            {
                battleTriggerInfo_EntityDead.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return battleTriggerInfo_EntityDead;
        }
        
        public static BattleTriggerLimitInfo GetBattleTriggerLimitInfo(Phoenix.ConfigData.BattleTriggerLimitConfigData battleTriggerLimit)
        {
            if (battleTriggerLimit == null)
            {
                return null;
            }
            BattleTriggerLimitInfo battleTriggerLimitInfo = null;
            switch (battleTriggerLimit.limitType)
            {
                case Phoenix.ConfigData.BattleTriggerLimitType.Count:
                    battleTriggerLimitInfo = GetBattleTriggerLimitInfo_Count(battleTriggerLimit as Phoenix.ConfigData.BattleTriggerLimitConfigData_Count);
                    break;
                case Phoenix.ConfigData.BattleTriggerLimitType.Cd_Turn:
                    battleTriggerLimitInfo = GetBattleTriggerLimitInfo_Cd_Turn(battleTriggerLimit as Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Turn);
                    break;
                case Phoenix.ConfigData.BattleTriggerLimitType.Cd_Step:
                    battleTriggerLimitInfo = GetBattleTriggerLimitInfo_Cd_Step(battleTriggerLimit as Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Step);
                    break;
            }
            if (battleTriggerLimitInfo != null)
            {
            }
            return battleTriggerLimitInfo;
        }
        
        public static BattleTriggerLimitInfo_Count GetBattleTriggerLimitInfo_Count(Phoenix.ConfigData.BattleTriggerLimitConfigData_Count battleTriggerLimit_Count)
        {
            if (battleTriggerLimit_Count == null)
            {
                return null;
            }
            BattleTriggerLimitInfo_Count battleTriggerLimitInfo_Count = new BattleTriggerLimitInfo_Count();
            battleTriggerLimitInfo_Count.count = battleTriggerLimit_Count.count;
            return battleTriggerLimitInfo_Count;
        }
        
        public static BattleTriggerLimitInfo_Cd_Turn GetBattleTriggerLimitInfo_Cd_Turn(Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Turn battleTriggerLimit_Cd_Turn)
        {
            if (battleTriggerLimit_Cd_Turn == null)
            {
                return null;
            }
            BattleTriggerLimitInfo_Cd_Turn battleTriggerLimitInfo_Cd_Turn = new BattleTriggerLimitInfo_Cd_Turn();
            battleTriggerLimitInfo_Cd_Turn.cd = battleTriggerLimit_Cd_Turn.cd;
            return battleTriggerLimitInfo_Cd_Turn;
        }
        
        public static BattleTriggerLimitInfo_Cd_Step GetBattleTriggerLimitInfo_Cd_Step(Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Step battleTriggerLimit_Cd_Step)
        {
            if (battleTriggerLimit_Cd_Step == null)
            {
                return null;
            }
            BattleTriggerLimitInfo_Cd_Step battleTriggerLimitInfo_Cd_Step = new BattleTriggerLimitInfo_Cd_Step();
            battleTriggerLimitInfo_Cd_Step.cd = battleTriggerLimit_Cd_Step.cd;
            return battleTriggerLimitInfo_Cd_Step;
        }
        
        public static BattleStageActionInfo GetBattleStageActionInfo(Phoenix.ConfigData.BattleStageActionConfigData battleStageAction)
        {
            if (battleStageAction == null)
            {
                return null;
            }
            BattleStageActionInfo battleStageActionInfo = null;
            switch (battleStageAction.actionType)
            {
                case Phoenix.ConfigData.BattleStageActionType.Sequence:
                    battleStageActionInfo = GetBattleStageActionInfo_Sequence(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Sequence);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Parallel:
                    battleStageActionInfo = GetBattleStageActionInfo_Parallel(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Parallel);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Delay:
                    battleStageActionInfo = GetBattleStageActionInfo_Delay(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Delay);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Dialog:
                    battleStageActionInfo = GetBattleStageActionInfo_Dialog(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Dialog);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.DialogSelection:
                    battleStageActionInfo = GetBattleStageActionInfo_DialogSelection(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.DialogBubble:
                    battleStageActionInfo = GetBattleStageActionInfo_DialogBubble(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.AttachBuff_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_AttachBuff_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.AttachBuff_Actor_TeamUid:
                    battleStageActionInfo = GetBattleStageActionInfo_AttachBuff_Actor_TeamUid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.AttachBuff_Actor_CampUid:
                    battleStageActionInfo = GetBattleStageActionInfo_AttachBuff_Actor_CampUid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.DetachBuff_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_DetachBuff_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Summon_Actor:
                    battleStageActionInfo = GetBattleStageActionInfo_Summon_Actor(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Teleport_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_Teleport_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Retreat_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_Retreat_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Damage_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_Damage_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Heal_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_Heal_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.ChangeTeam_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_ChangeTeam_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.ChangeBehavior_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_ChangeBehavior_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Move_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_Move_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.PlayAnim_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_PlayAnim_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.TurnDir_Actor_Uid_Dir:
                    battleStageActionInfo = GetBattleStageActionInfo_TurnDir_Actor_Uid_Dir(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.TurnDir_Actor_Uid_LookPos:
                    battleStageActionInfo = GetBattleStageActionInfo_TurnDir_Actor_Uid_LookPos(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.Select_Actor_Uid:
                    battleStageActionInfo = GetBattleStageActionInfo_Select_Actor_Uid(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.CameraFocusPosition:
                    battleStageActionInfo = GetBattleStageActionInfo_CameraFocusPosition(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.CameraFocusEntity:
                    battleStageActionInfo = GetBattleStageActionInfo_CameraFocusEntity(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.CameraFollowEntity:
                    battleStageActionInfo = GetBattleStageActionInfo_CameraFollow(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.CameraShake:
                    battleStageActionInfo = GetBattleStageActionInfo_CameraShake(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_CameraShake);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.SpawnTerrainEffect:
                    battleStageActionInfo = GetBattleStageActionInfo_SpawnTerrainEffect(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.DespawnTerrainEffect:
                    battleStageActionInfo = GetBattleStageActionInfo_DespawnTerrainEffect(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.PlayTimeline:
                    battleStageActionInfo = GetBattleStageActionInfo_PlayTimeline(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.PlaySound:
                    battleStageActionInfo = GetBattleStageActionInfo_PlaySound(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_PlaySound);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.PlayBgm:
                    battleStageActionInfo = GetBattleStageActionInfo_PlayBgm(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.AddTeamDecisionMark:
                    battleStageActionInfo = GetBattleStageActionInfo_AddTeamDecisionMark(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.SetWinConditionEnabled:
                    battleStageActionInfo = GetBattleStageActionInfo_SetWinConditionEnabled(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled);
                    break;
                case Phoenix.ConfigData.BattleStageActionType.ToggleHudVisibility:
                    battleStageActionInfo = GetBattleStageActionInfo_ToggleHudVisibility(battleStageAction as Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility);
                    break;
            }
            if (battleStageActionInfo != null)
            {
            }
            return battleStageActionInfo;
        }
        
        public static BattleStageActionInfo_Sequence GetBattleStageActionInfo_Sequence(Phoenix.ConfigData.BattleStageActionConfigData_Sequence battleStageAction_Sequence)
        {
            if (battleStageAction_Sequence == null)
            {
                return null;
            }
            BattleStageActionInfo_Sequence battleStageActionInfo_Sequence = new BattleStageActionInfo_Sequence();
            foreach(var action in battleStageAction_Sequence.actionList)
            {
                battleStageActionInfo_Sequence.actionList.Add(GetBattleStageActionInfo(action));
            }
            return battleStageActionInfo_Sequence;
        }
        
        public static BattleStageActionInfo_Parallel GetBattleStageActionInfo_Parallel(Phoenix.ConfigData.BattleStageActionConfigData_Parallel battleStageAction_Parallel)
        {
            if (battleStageAction_Parallel == null)
            {
                return null;
            }
            BattleStageActionInfo_Parallel battleStageActionInfo_Parallel = new BattleStageActionInfo_Parallel();
            foreach(var action in battleStageAction_Parallel.actionList)
            {
                battleStageActionInfo_Parallel.actionList.Add(GetBattleStageActionInfo(action));
            }
            return battleStageActionInfo_Parallel;
        }
        
        public static BattleStageActionInfo_Delay GetBattleStageActionInfo_Delay(Phoenix.ConfigData.BattleStageActionConfigData_Delay battleStageAction_Delay)
        {
            if (battleStageAction_Delay == null)
            {
                return null;
            }
            BattleStageActionInfo_Delay battleStageActionInfo_Delay = new BattleStageActionInfo_Delay();
            battleStageActionInfo_Delay.delayTime = battleStageAction_Delay.delayTime;
            return battleStageActionInfo_Delay;
        }
        
        public static BattleStageActionInfo_Dialog GetBattleStageActionInfo_Dialog(Phoenix.ConfigData.BattleStageActionConfigData_Dialog battleStageAction_Dialog)
        {
            if (battleStageAction_Dialog == null)
            {
                return null;
            }
            BattleStageActionInfo_Dialog battleStageActionInfo_Dialog = new BattleStageActionInfo_Dialog();
            battleStageActionInfo_Dialog.dialogId = battleStageAction_Dialog.dialogId;
            return battleStageActionInfo_Dialog;
        }
        
        public static BattleStageActionInfo_DialogSelection GetBattleStageActionInfo_DialogSelection(Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection battleStageAction_DialogSelection)
        {
            if (battleStageAction_DialogSelection == null)
            {
                return null;
            }
            BattleStageActionInfo_DialogSelection battleStageActionInfo_DialogSelection = new BattleStageActionInfo_DialogSelection();
            battleStageActionInfo_DialogSelection.dialogId = battleStageAction_DialogSelection.dialogId;
            battleStageActionInfo_DialogSelection.selectionGroupId = battleStageAction_DialogSelection.selectionGroupId;
            return battleStageActionInfo_DialogSelection;
        }
        
        public static BattleStageActionInfo_DialogBubble GetBattleStageActionInfo_DialogBubble(Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble battleStageAction_DialogBubble)
        {
            if (battleStageAction_DialogBubble == null)
            {
                return null;
            }
            BattleStageActionInfo_DialogBubble battleStageActionInfo_DialogBubble = new BattleStageActionInfo_DialogBubble();
            battleStageActionInfo_DialogBubble.bubbleId = battleStageAction_DialogBubble.bubbleId;
            foreach(var actorUid in battleStageAction_DialogBubble.actorUid)
            {
                battleStageActionInfo_DialogBubble.actorUid.Add(actorUid);
            }
            return battleStageActionInfo_DialogBubble;
        }
        
        public static BattleStageActionInfo_AttachBuff_Actor_Uid GetBattleStageActionInfo_AttachBuff_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid battleStageAction_AttachBuff_Actor_Uid)
        {
            if (battleStageAction_AttachBuff_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_AttachBuff_Actor_Uid battleStageActionInfo_AttachBuff_Actor_Uid = new BattleStageActionInfo_AttachBuff_Actor_Uid();
            foreach(var actorUid in battleStageAction_AttachBuff_Actor_Uid.actorUidList)
            {
                battleStageActionInfo_AttachBuff_Actor_Uid.actorUidList.Add(actorUid);
            }
            foreach(var buff in battleStageAction_AttachBuff_Actor_Uid.buffList)
            {
                battleStageActionInfo_AttachBuff_Actor_Uid.buffList.Add(GetBattleAttachBuffItemInfo(buff));
            }
            battleStageActionInfo_AttachBuff_Actor_Uid.skipEffect = battleStageAction_AttachBuff_Actor_Uid.skipEffect;
            return battleStageActionInfo_AttachBuff_Actor_Uid;
        }
        
        public static BattleAttachBuffItemInfo GetBattleAttachBuffItemInfo(Phoenix.ConfigData.BattleAttachBuffItemConfigData battleAttachBuffItem)
        {
            if (battleAttachBuffItem == null)
            {
                return null;
            }
            BattleAttachBuffItemInfo battleAttachBuffItemInfo = new BattleAttachBuffItemInfo();
            battleAttachBuffItemInfo.buffRid = battleAttachBuffItem.buffRid;
            battleAttachBuffItemInfo.level = battleAttachBuffItem.level;
            battleAttachBuffItemInfo.lifeTime = battleAttachBuffItem.lifeTime;
            return battleAttachBuffItemInfo;
        }
        
        public static BattleStageActionInfo_AttachBuff_Actor_TeamUid GetBattleStageActionInfo_AttachBuff_Actor_TeamUid(Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid battleStageAction_AttachBuff_Actor_TeamUid)
        {
            if (battleStageAction_AttachBuff_Actor_TeamUid == null)
            {
                return null;
            }
            BattleStageActionInfo_AttachBuff_Actor_TeamUid battleStageActionInfo_AttachBuff_Actor_TeamUid = new BattleStageActionInfo_AttachBuff_Actor_TeamUid();
            foreach(var teamUid in battleStageAction_AttachBuff_Actor_TeamUid.teamUidList)
            {
                battleStageActionInfo_AttachBuff_Actor_TeamUid.teamUidList.Add(teamUid);
            }
            foreach(var buff in battleStageAction_AttachBuff_Actor_TeamUid.buffList)
            {
                battleStageActionInfo_AttachBuff_Actor_TeamUid.buffList.Add(GetBattleAttachBuffItemInfo(buff));
            }
            battleStageActionInfo_AttachBuff_Actor_TeamUid.skipEffect = battleStageAction_AttachBuff_Actor_TeamUid.skipEffect;
            return battleStageActionInfo_AttachBuff_Actor_TeamUid;
        }
        
        public static BattleStageActionInfo_AttachBuff_Actor_CampUid GetBattleStageActionInfo_AttachBuff_Actor_CampUid(Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid battleStageAction_AttachBuff_Actor_CampUid)
        {
            if (battleStageAction_AttachBuff_Actor_CampUid == null)
            {
                return null;
            }
            BattleStageActionInfo_AttachBuff_Actor_CampUid battleStageActionInfo_AttachBuff_Actor_CampUid = new BattleStageActionInfo_AttachBuff_Actor_CampUid();
            battleStageActionInfo_AttachBuff_Actor_CampUid.campUid = battleStageAction_AttachBuff_Actor_CampUid.campUid;
            foreach(var buff in battleStageAction_AttachBuff_Actor_CampUid.buffList)
            {
                battleStageActionInfo_AttachBuff_Actor_CampUid.buffList.Add(GetBattleAttachBuffItemInfo(buff));
            }
            battleStageActionInfo_AttachBuff_Actor_CampUid.skipEffect = battleStageAction_AttachBuff_Actor_CampUid.skipEffect;
            return battleStageActionInfo_AttachBuff_Actor_CampUid;
        }
        
        public static BattleStageActionInfo_DetachBuff_Actor_Uid GetBattleStageActionInfo_DetachBuff_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid battleStageAction_DetachBuff_Actor_Uid)
        {
            if (battleStageAction_DetachBuff_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_DetachBuff_Actor_Uid battleStageActionInfo_DetachBuff_Actor_Uid = new BattleStageActionInfo_DetachBuff_Actor_Uid();
            return battleStageActionInfo_DetachBuff_Actor_Uid;
        }
        
        public static BattleStageActionInfo_Summon_Actor GetBattleStageActionInfo_Summon_Actor(Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor battleStageAction_Summon_Actor)
        {
            if (battleStageAction_Summon_Actor == null)
            {
                return null;
            }
            BattleStageActionInfo_Summon_Actor battleStageActionInfo_Summon_Actor = new BattleStageActionInfo_Summon_Actor();
            foreach(var item in battleStageAction_Summon_Actor.itemList)
            {
                battleStageActionInfo_Summon_Actor.itemList.Add(GetBattleStageActionSummonActorItemInfo(item));
            }
            return battleStageActionInfo_Summon_Actor;
        }
        
        public static BattleStageActionSummonActorItemInfo GetBattleStageActionSummonActorItemInfo(Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData battleStageActionSummonActorItem)
        {
            if (battleStageActionSummonActorItem == null)
            {
                return null;
            }
            BattleStageActionSummonActorItemInfo battleStageActionSummonActorItemInfo = new BattleStageActionSummonActorItemInfo();
            battleStageActionSummonActorItemInfo.entityRid = battleStageActionSummonActorItem.entityRid;
            battleStageActionSummonActorItemInfo.entityUid = battleStageActionSummonActorItem.entityUid;
            battleStageActionSummonActorItemInfo.pos = GetGridPosition(battleStageActionSummonActorItem.pos);
            battleStageActionSummonActorItemInfo.dirType = GetGridDirType(battleStageActionSummonActorItem.dirType);
            battleStageActionSummonActorItemInfo.level = battleStageActionSummonActorItem.level;
            battleStageActionSummonActorItemInfo.teamId = battleStageActionSummonActorItem.teamId;
            battleStageActionSummonActorItemInfo.hpRate = new FixedValue(battleStageActionSummonActorItem.hpRate, 100);
            battleStageActionSummonActorItemInfo.aiType = (EntityAIType)battleStageActionSummonActorItem.aiType;
            battleStageActionSummonActorItemInfo.hasActionChance = battleStageActionSummonActorItem.hasActionChance;
            battleStageActionSummonActorItemInfo.performanceType = (SummonPerformanceType)battleStageActionSummonActorItem.performanceType;
            battleStageActionSummonActorItemInfo.dramaName = battleStageActionSummonActorItem.dramaName;
            return battleStageActionSummonActorItemInfo;
        }
        
        public static BattleStageActionInfo_Teleport_Actor_Uid GetBattleStageActionInfo_Teleport_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid battleStageAction_Teleport_Actor_Uid)
        {
            if (battleStageAction_Teleport_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_Teleport_Actor_Uid battleStageActionInfo_Teleport_Actor_Uid = new BattleStageActionInfo_Teleport_Actor_Uid();
            return battleStageActionInfo_Teleport_Actor_Uid;
        }
        
        public static BattleStageActionInfo_Retreat_Actor_Uid GetBattleStageActionInfo_Retreat_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid battleStageAction_Retreat_Actor_Uid)
        {
            if (battleStageAction_Retreat_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_Retreat_Actor_Uid battleStageActionInfo_Retreat_Actor_Uid = new BattleStageActionInfo_Retreat_Actor_Uid();
            foreach(var actorUid in battleStageAction_Retreat_Actor_Uid.actorUidList)
            {
                battleStageActionInfo_Retreat_Actor_Uid.actorUidList.Add(actorUid);
            }
            return battleStageActionInfo_Retreat_Actor_Uid;
        }
        
        public static BattleStageActionInfo_Damage_Actor_Uid GetBattleStageActionInfo_Damage_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid battleStageAction_Damage_Actor_Uid)
        {
            if (battleStageAction_Damage_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_Damage_Actor_Uid battleStageActionInfo_Damage_Actor_Uid = new BattleStageActionInfo_Damage_Actor_Uid();
            foreach(var actorUid in battleStageAction_Damage_Actor_Uid.actorUidList)
            {
                battleStageActionInfo_Damage_Actor_Uid.actorUidList.Add(actorUid);
            }
            battleStageActionInfo_Damage_Actor_Uid.damageValue = GetBattleValueBasedActorHpInfo(battleStageAction_Damage_Actor_Uid.damageValue);
            return battleStageActionInfo_Damage_Actor_Uid;
        }
        
        public static BattleValueBasedActorHpInfo GetBattleValueBasedActorHpInfo(Phoenix.ConfigData.BattleValueBasedActorHpConfigData battleValueBasedActorHp)
        {
            if (battleValueBasedActorHp == null)
            {
                return null;
            }
            BattleValueBasedActorHpInfo battleValueBasedActorHpInfo = new BattleValueBasedActorHpInfo();
            battleValueBasedActorHpInfo.value = battleValueBasedActorHp.value;
            battleValueBasedActorHpInfo.rateByMax = new FixedValue(battleValueBasedActorHp.rateByMax, 100);
            battleValueBasedActorHpInfo.rateByCur = new FixedValue(battleValueBasedActorHp.rateByCur, 100);
            battleValueBasedActorHpInfo.rateByLost = new FixedValue(battleValueBasedActorHp.rateByLost, 100);
            return battleValueBasedActorHpInfo;
        }
        
        public static BattleStageActionInfo_Heal_Actor_Uid GetBattleStageActionInfo_Heal_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid battleStageAction_Heal_Actor_Uid)
        {
            if (battleStageAction_Heal_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_Heal_Actor_Uid battleStageActionInfo_Heal_Actor_Uid = new BattleStageActionInfo_Heal_Actor_Uid();
            foreach(var actorUid in battleStageAction_Heal_Actor_Uid.actorUidList)
            {
                battleStageActionInfo_Heal_Actor_Uid.actorUidList.Add(actorUid);
            }
            battleStageActionInfo_Heal_Actor_Uid.healValue = GetBattleValueBasedActorHpInfo(battleStageAction_Heal_Actor_Uid.healValue);
            return battleStageActionInfo_Heal_Actor_Uid;
        }
        
        public static BattleStageActionInfo_ChangeTeam_Actor_Uid GetBattleStageActionInfo_ChangeTeam_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid battleStageAction_ChangeTeam_Actor_Uid)
        {
            if (battleStageAction_ChangeTeam_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_ChangeTeam_Actor_Uid battleStageActionInfo_ChangeTeam_Actor_Uid = new BattleStageActionInfo_ChangeTeam_Actor_Uid();
            foreach(var actorUid in battleStageAction_ChangeTeam_Actor_Uid.actorUidList)
            {
                battleStageActionInfo_ChangeTeam_Actor_Uid.actorUidList.Add(actorUid);
            }
            battleStageActionInfo_ChangeTeam_Actor_Uid.teamUid = battleStageAction_ChangeTeam_Actor_Uid.teamUid;
            return battleStageActionInfo_ChangeTeam_Actor_Uid;
        }
        
        public static BattleStageActionInfo_ChangeBehavior_Actor_Uid GetBattleStageActionInfo_ChangeBehavior_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid battleStageAction_ChangeBehavior_Actor_Uid)
        {
            if (battleStageAction_ChangeBehavior_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_ChangeBehavior_Actor_Uid battleStageActionInfo_ChangeBehavior_Actor_Uid = new BattleStageActionInfo_ChangeBehavior_Actor_Uid();
            foreach(var actorUid in battleStageAction_ChangeBehavior_Actor_Uid.actorUidList)
            {
                battleStageActionInfo_ChangeBehavior_Actor_Uid.actorUidList.Add(actorUid);
            }
            battleStageActionInfo_ChangeBehavior_Actor_Uid.aiType = (EntityAIType)battleStageAction_ChangeBehavior_Actor_Uid.aiType;
            return battleStageActionInfo_ChangeBehavior_Actor_Uid;
        }
        
        public static BattleStageActionInfo_Move_Actor_Uid GetBattleStageActionInfo_Move_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid battleStageAction_Move_Actor_Uid)
        {
            if (battleStageAction_Move_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_Move_Actor_Uid battleStageActionInfo_Move_Actor_Uid = new BattleStageActionInfo_Move_Actor_Uid();
            battleStageActionInfo_Move_Actor_Uid.actorUid = battleStageAction_Move_Actor_Uid.actorUid;
            battleStageActionInfo_Move_Actor_Uid.movePos = GetGridPosition(battleStageAction_Move_Actor_Uid.movePos);
            battleStageActionInfo_Move_Actor_Uid.moveBack = battleStageAction_Move_Actor_Uid.moveBack;
            return battleStageActionInfo_Move_Actor_Uid;
        }
        
        public static BattleStageActionInfo_PlayAnim_Actor_Uid GetBattleStageActionInfo_PlayAnim_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid battleStageAction_PlayAnim_Actor_Uid)
        {
            if (battleStageAction_PlayAnim_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_PlayAnim_Actor_Uid battleStageActionInfo_PlayAnim_Actor_Uid = new BattleStageActionInfo_PlayAnim_Actor_Uid();
            battleStageActionInfo_PlayAnim_Actor_Uid.actorUid = battleStageAction_PlayAnim_Actor_Uid.actorUid;
            battleStageActionInfo_PlayAnim_Actor_Uid.animationName = battleStageAction_PlayAnim_Actor_Uid.animationName;
            battleStageActionInfo_PlayAnim_Actor_Uid.speed = battleStageAction_PlayAnim_Actor_Uid.speed;
            battleStageActionInfo_PlayAnim_Actor_Uid.fadeTime = battleStageAction_PlayAnim_Actor_Uid.fadeTime;
            battleStageActionInfo_PlayAnim_Actor_Uid.loop = battleStageAction_PlayAnim_Actor_Uid.loop;
            battleStageActionInfo_PlayAnim_Actor_Uid.returnIdle = battleStageAction_PlayAnim_Actor_Uid.returnIdle;
            battleStageActionInfo_PlayAnim_Actor_Uid.waitEnd = battleStageAction_PlayAnim_Actor_Uid.waitEnd;
            return battleStageActionInfo_PlayAnim_Actor_Uid;
        }
        
        public static BattleStageActionInfo_TurnDir_Actor_Uid_Dir GetBattleStageActionInfo_TurnDir_Actor_Uid_Dir(Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir battleStageAction_TurnDir_Actor_Uid_Dir)
        {
            if (battleStageAction_TurnDir_Actor_Uid_Dir == null)
            {
                return null;
            }
            BattleStageActionInfo_TurnDir_Actor_Uid_Dir battleStageActionInfo_TurnDir_Actor_Uid_Dir = new BattleStageActionInfo_TurnDir_Actor_Uid_Dir();
            foreach(var actorUid in battleStageAction_TurnDir_Actor_Uid_Dir.actorUidList)
            {
                battleStageActionInfo_TurnDir_Actor_Uid_Dir.actorUidList.Add(actorUid);
            }
            battleStageActionInfo_TurnDir_Actor_Uid_Dir.dir = GetGridDirType(battleStageAction_TurnDir_Actor_Uid_Dir.dir);
            battleStageActionInfo_TurnDir_Actor_Uid_Dir.time = battleStageAction_TurnDir_Actor_Uid_Dir.time;
            battleStageActionInfo_TurnDir_Actor_Uid_Dir.showAnimation = battleStageAction_TurnDir_Actor_Uid_Dir.showAnimation;
            battleStageActionInfo_TurnDir_Actor_Uid_Dir.waitEnd = battleStageAction_TurnDir_Actor_Uid_Dir.waitEnd;
            return battleStageActionInfo_TurnDir_Actor_Uid_Dir;
        }
        
        public static BattleStageActionInfo_TurnDir_Actor_Uid_LookPos GetBattleStageActionInfo_TurnDir_Actor_Uid_LookPos(Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos battleStageAction_TurnDir_Actor_Uid_LookPos)
        {
            if (battleStageAction_TurnDir_Actor_Uid_LookPos == null)
            {
                return null;
            }
            BattleStageActionInfo_TurnDir_Actor_Uid_LookPos battleStageActionInfo_TurnDir_Actor_Uid_LookPos = new BattleStageActionInfo_TurnDir_Actor_Uid_LookPos();
            foreach(var actorUid in battleStageAction_TurnDir_Actor_Uid_LookPos.actorUidList)
            {
                battleStageActionInfo_TurnDir_Actor_Uid_LookPos.actorUidList.Add(actorUid);
            }
            battleStageActionInfo_TurnDir_Actor_Uid_LookPos.pos = GetBattleArgumentInfo_Grid(battleStageAction_TurnDir_Actor_Uid_LookPos.pos);
            battleStageActionInfo_TurnDir_Actor_Uid_LookPos.time = battleStageAction_TurnDir_Actor_Uid_LookPos.time;
            battleStageActionInfo_TurnDir_Actor_Uid_LookPos.showAnimation = battleStageAction_TurnDir_Actor_Uid_LookPos.showAnimation;
            battleStageActionInfo_TurnDir_Actor_Uid_LookPos.waitEnd = battleStageAction_TurnDir_Actor_Uid_LookPos.waitEnd;
            return battleStageActionInfo_TurnDir_Actor_Uid_LookPos;
        }
        
        public static BattleStageActionInfo_Select_Actor_Uid GetBattleStageActionInfo_Select_Actor_Uid(Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid battleStageAction_Select_Actor_Uid)
        {
            if (battleStageAction_Select_Actor_Uid == null)
            {
                return null;
            }
            BattleStageActionInfo_Select_Actor_Uid battleStageActionInfo_Select_Actor_Uid = new BattleStageActionInfo_Select_Actor_Uid();
            battleStageActionInfo_Select_Actor_Uid.actorUid = battleStageAction_Select_Actor_Uid.actorUid;
            return battleStageActionInfo_Select_Actor_Uid;
        }
        
        public static BattleStageActionInfo_CameraFocusPosition GetBattleStageActionInfo_CameraFocusPosition(Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition battleStageAction_CameraFocusPosition)
        {
            if (battleStageAction_CameraFocusPosition == null)
            {
                return null;
            }
            BattleStageActionInfo_CameraFocusPosition battleStageActionInfo_CameraFocusPosition = new BattleStageActionInfo_CameraFocusPosition();
            battleStageActionInfo_CameraFocusPosition.pos = GetGridPosition(battleStageAction_CameraFocusPosition.pos);
            battleStageActionInfo_CameraFocusPosition.immediately = battleStageAction_CameraFocusPosition.immediately;
            return battleStageActionInfo_CameraFocusPosition;
        }
        
        public static BattleStageActionInfo_CameraFocusEntity GetBattleStageActionInfo_CameraFocusEntity(Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity battleStageAction_CameraFocusEntity)
        {
            if (battleStageAction_CameraFocusEntity == null)
            {
                return null;
            }
            BattleStageActionInfo_CameraFocusEntity battleStageActionInfo_CameraFocusEntity = new BattleStageActionInfo_CameraFocusEntity();
            battleStageActionInfo_CameraFocusEntity.actorUid = battleStageAction_CameraFocusEntity.actorUid;
            battleStageActionInfo_CameraFocusEntity.immediately = battleStageAction_CameraFocusEntity.immediately;
            return battleStageActionInfo_CameraFocusEntity;
        }
        
        public static BattleStageActionInfo_CameraFollow GetBattleStageActionInfo_CameraFollow(Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow battleStageAction_CameraFollow)
        {
            if (battleStageAction_CameraFollow == null)
            {
                return null;
            }
            BattleStageActionInfo_CameraFollow battleStageActionInfo_CameraFollow = new BattleStageActionInfo_CameraFollow();
            return battleStageActionInfo_CameraFollow;
        }
        
        public static BattleStageActionInfo_CameraShake GetBattleStageActionInfo_CameraShake(Phoenix.ConfigData.BattleStageActionConfigData_CameraShake battleStageAction_CameraShake)
        {
            if (battleStageAction_CameraShake == null)
            {
                return null;
            }
            BattleStageActionInfo_CameraShake battleStageActionInfo_CameraShake = new BattleStageActionInfo_CameraShake();
            battleStageActionInfo_CameraShake.pattern = (CameraShakePattern)battleStageAction_CameraShake.pattern;
            battleStageActionInfo_CameraShake.duration = battleStageAction_CameraShake.duration;
            battleStageActionInfo_CameraShake.amplitude = battleStageAction_CameraShake.amplitude;
            battleStageActionInfo_CameraShake.frequency = battleStageAction_CameraShake.frequency;
            battleStageActionInfo_CameraShake.immediately = battleStageAction_CameraShake.immediately;
            return battleStageActionInfo_CameraShake;
        }
        
        public static BattleStageActionInfo_SpawnTerrainEffect GetBattleStageActionInfo_SpawnTerrainEffect(Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect battleStageAction_SpawnTerrainEffect)
        {
            if (battleStageAction_SpawnTerrainEffect == null)
            {
                return null;
            }
            BattleStageActionInfo_SpawnTerrainEffect battleStageActionInfo_SpawnTerrainEffect = new BattleStageActionInfo_SpawnTerrainEffect();
            battleStageActionInfo_SpawnTerrainEffect.rid = battleStageAction_SpawnTerrainEffect.rid;
            battleStageActionInfo_SpawnTerrainEffect.uid = battleStageAction_SpawnTerrainEffect.uid;
            battleStageActionInfo_SpawnTerrainEffect.pos = GetGridPosition(battleStageAction_SpawnTerrainEffect.pos);
            battleStageActionInfo_SpawnTerrainEffect.isLoop = battleStageAction_SpawnTerrainEffect.isLoop;
            return battleStageActionInfo_SpawnTerrainEffect;
        }
        
        public static BattleStageActionInfo_DespawnTerrainEffect GetBattleStageActionInfo_DespawnTerrainEffect(Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect battleStageAction_DespawnTerrainEffect)
        {
            if (battleStageAction_DespawnTerrainEffect == null)
            {
                return null;
            }
            BattleStageActionInfo_DespawnTerrainEffect battleStageActionInfo_DespawnTerrainEffect = new BattleStageActionInfo_DespawnTerrainEffect();
            battleStageActionInfo_DespawnTerrainEffect.uid = battleStageAction_DespawnTerrainEffect.uid;
            return battleStageActionInfo_DespawnTerrainEffect;
        }
        
        public static BattleStageActionInfo_PlayTimeline GetBattleStageActionInfo_PlayTimeline(Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline battleStageAction_PlayTimeline)
        {
            if (battleStageAction_PlayTimeline == null)
            {
                return null;
            }
            BattleStageActionInfo_PlayTimeline battleStageActionInfo_PlayTimeline = new BattleStageActionInfo_PlayTimeline();
            return battleStageActionInfo_PlayTimeline;
        }
        
        public static BattleStageActionInfo_PlaySound GetBattleStageActionInfo_PlaySound(Phoenix.ConfigData.BattleStageActionConfigData_PlaySound battleStageAction_PlaySound)
        {
            if (battleStageAction_PlaySound == null)
            {
                return null;
            }
            BattleStageActionInfo_PlaySound battleStageActionInfo_PlaySound = new BattleStageActionInfo_PlaySound();
            battleStageActionInfo_PlaySound.path = battleStageAction_PlaySound.path;
            return battleStageActionInfo_PlaySound;
        }
        
        public static BattleStageActionInfo_PlayBgm GetBattleStageActionInfo_PlayBgm(Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm battleStageAction_PlayBgm)
        {
            if (battleStageAction_PlayBgm == null)
            {
                return null;
            }
            BattleStageActionInfo_PlayBgm battleStageActionInfo_PlayBgm = new BattleStageActionInfo_PlayBgm();
            battleStageActionInfo_PlayBgm.path = battleStageAction_PlayBgm.path;
            return battleStageActionInfo_PlayBgm;
        }
        
        public static BattleStageActionInfo_AddTeamDecisionMark GetBattleStageActionInfo_AddTeamDecisionMark(Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark battleStageAction_AddTeamDecisionMark)
        {
            if (battleStageAction_AddTeamDecisionMark == null)
            {
                return null;
            }
            BattleStageActionInfo_AddTeamDecisionMark battleStageActionInfo_AddTeamDecisionMark = new BattleStageActionInfo_AddTeamDecisionMark();
            battleStageActionInfo_AddTeamDecisionMark.teamUid = battleStageAction_AddTeamDecisionMark.teamUid;
            battleStageActionInfo_AddTeamDecisionMark.markId = (TeamDecisionMarkId)battleStageAction_AddTeamDecisionMark.markId;
            battleStageActionInfo_AddTeamDecisionMark.actorUid = battleStageAction_AddTeamDecisionMark.actorUid;
            battleStageActionInfo_AddTeamDecisionMark.pos = GetGridPosition(battleStageAction_AddTeamDecisionMark.pos);
            return battleStageActionInfo_AddTeamDecisionMark;
        }
        
        public static BattleStageActionInfo_SetWinConditionEnabled GetBattleStageActionInfo_SetWinConditionEnabled(Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled battleStageAction_SetWinConditionEnabled)
        {
            if (battleStageAction_SetWinConditionEnabled == null)
            {
                return null;
            }
            BattleStageActionInfo_SetWinConditionEnabled battleStageActionInfo_SetWinConditionEnabled = new BattleStageActionInfo_SetWinConditionEnabled();
            return battleStageActionInfo_SetWinConditionEnabled;
        }
        
        public static BattleStageActionInfo_ToggleHudVisibility GetBattleStageActionInfo_ToggleHudVisibility(Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility battleStageAction_ToggleHudVisibility)
        {
            if (battleStageAction_ToggleHudVisibility == null)
            {
                return null;
            }
            BattleStageActionInfo_ToggleHudVisibility battleStageActionInfo_ToggleHudVisibility = new BattleStageActionInfo_ToggleHudVisibility();
            battleStageActionInfo_ToggleHudVisibility.isShow = battleStageAction_ToggleHudVisibility.isShow;
            return battleStageActionInfo_ToggleHudVisibility;
        }
        
        public static BattleFunctionEnableInfo GetBattleFunctionEnableInfo(Phoenix.ConfigData.BattleFunctionEnableConfigData battleFunctionEnable)
        {
            if (battleFunctionEnable == null)
            {
                return null;
            }
            BattleFunctionEnableInfo battleFunctionEnableInfo = new BattleFunctionEnableInfo();
            battleFunctionEnableInfo.autoBattle = battleFunctionEnable.autoBattle;
            battleFunctionEnableInfo.retract = battleFunctionEnable.retract;
            battleFunctionEnableInfo.recommand = battleFunctionEnable.recommand;
            return battleFunctionEnableInfo;
        }
        
        public static SkillInfo GetSkillInfo(Phoenix.ConfigData.SkillConfigData skill)
        {
            if (skill == null)
            {
                return null;
            }
            SkillInfo skillInfo = new SkillInfo();
            skillInfo.id = skill.id;
            skillInfo.name = skill.name;
            skillInfo.tag = skill.tag;
            skillInfo.summaryDesc = skill.summaryDesc;
            skillInfo.desc = skill.desc;
            skillInfo.iconName = skill.iconName;
            skillInfo.engageType = (SkillEngageType)skill.engageType;
            skillInfo.indicatorType = (SkillIndicatorType)skill.indicatorType;
            skillInfo.elementId = (EntityElementId)skill.elementId;
            skillInfo.coolTime = skill.coolTime;
            skillInfo.energyCost = skill.energyCost;
            skillInfo.energyGain = skill.energyGain;
            skillInfo.decisionPriority = skill.decisionPriority;
            skillInfo.needPreAnnounce = skill.needPreAnnounce;
            foreach(var tag in skill.tagList)
            {
                skillInfo.tagList.Add((SkillTagType)tag);
            }
            foreach(var attributeChange in skill.attributeChangeList)
            {
                skillInfo.attributeChangeList.Add(GetSkillAttributeChangeInfo(attributeChange));
            }
            foreach(var buffAttachBefore in skill.buffAttachBeforeList)
            {
                skillInfo.buffAttachBeforeList.Add(buffAttachBefore);
            }
            foreach(var passiveSkillRid in skill.passiveSkillRidList)
            {
                skillInfo.passiveSkillRidList.Add(passiveSkillRid);
            }
            skillInfo.selectStep = GetTargetSelectStepInfo(skill.selectStep);
            foreach(var effect in skill.effectList)
            {
                skillInfo.effectList.Add(GetSkillEffectInfo(effect));
            }
            skillInfo.mainEffectIndex = skill.mainEffectIndex;
            skillInfo.dramaName = skill.dramaName;
            skillInfo.dramaNameForSimple = skill.dramaNameForSimple;
            skillInfo.forceUseSimple = skill.forceUseSimple;
            skillInfo.announceType = (SkillAnnounceType)skill.announceType;
            return skillInfo;
        }
        
        public static SkillAttributeChangeInfo GetSkillAttributeChangeInfo(Phoenix.ConfigData.SkillAttributeChangeConfigData skillAttributeChange)
        {
            if (skillAttributeChange == null)
            {
                return null;
            }
            SkillAttributeChangeInfo skillAttributeChangeInfo = new SkillAttributeChangeInfo();
            foreach(var condition in skillAttributeChange.conditionList)
            {
                skillAttributeChangeInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            skillAttributeChangeInfo.partId = (AttributePartId)skillAttributeChange.partId;
            skillAttributeChangeInfo.rate = new FixedValue(skillAttributeChange.rate, 100);
            return skillAttributeChangeInfo;
        }
        
        public static TargetSelectStepInfo GetTargetSelectStepInfo(Phoenix.ConfigData.TargetSelectStepConfigData targetSelectStep)
        {
            if (targetSelectStep == null)
            {
                return null;
            }
            TargetSelectStepInfo targetSelectStepInfo = null;
            switch (targetSelectStep.funcType)
            {
                case Phoenix.ConfigData.TargetSelectStepFuncType.Self:
                    targetSelectStepInfo = GetTargetSelectStepInfo_Self(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_Self);
                    break;
                case Phoenix.ConfigData.TargetSelectStepFuncType.Grid:
                    targetSelectStepInfo = GetTargetSelectStepInfo_Grid(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_Grid);
                    break;
                case Phoenix.ConfigData.TargetSelectStepFuncType.Target:
                    targetSelectStepInfo = GetTargetSelectStepInfo_Target(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_Target);
                    break;
                case Phoenix.ConfigData.TargetSelectStepFuncType.Dir:
                    targetSelectStepInfo = GetTargetSelectStepInfo_Dir(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_Dir);
                    break;
                case Phoenix.ConfigData.TargetSelectStepFuncType.TeleportSelf:
                    targetSelectStepInfo = GetTargetSelectStepInfo_TeleportSelf(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_TeleportSelf);
                    break;
                case Phoenix.ConfigData.TargetSelectStepFuncType.TeleportTarget:
                    targetSelectStepInfo = GetTargetSelectStepInfo_TeleportTarget(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_TeleportTarget);
                    break;
                case Phoenix.ConfigData.TargetSelectStepFuncType.Summon:
                    targetSelectStepInfo = GetTargetSelectStepInfo_Summon(targetSelectStep as Phoenix.ConfigData.TargetSelectStepConfigData_Summon);
                    break;
            }
            if (targetSelectStepInfo != null)
            {
            }
            return targetSelectStepInfo;
        }
        
        public static TargetSelectStepInfo_Self GetTargetSelectStepInfo_Self(Phoenix.ConfigData.TargetSelectStepConfigData_Self targetSelectStep_Self)
        {
            if (targetSelectStep_Self == null)
            {
                return null;
            }
            TargetSelectStepInfo_Self targetSelectStepInfo_Self = new TargetSelectStepInfo_Self();
            return targetSelectStepInfo_Self;
        }
        
        public static TargetSelectStepInfo_Grid GetTargetSelectStepInfo_Grid(Phoenix.ConfigData.TargetSelectStepConfigData_Grid targetSelectStep_Grid)
        {
            if (targetSelectStep_Grid == null)
            {
                return null;
            }
            TargetSelectStepInfo_Grid targetSelectStepInfo_Grid = new TargetSelectStepInfo_Grid();
            targetSelectStepInfo_Grid.rangeId = (TargetSelectRangeId)targetSelectStep_Grid.rangeId;
            targetSelectStepInfo_Grid.filterType = (TargetSelectGridFilterType)targetSelectStep_Grid.filterType;
            return targetSelectStepInfo_Grid;
        }
        
        public static TargetSelectStepInfo_Target GetTargetSelectStepInfo_Target(Phoenix.ConfigData.TargetSelectStepConfigData_Target targetSelectStep_Target)
        {
            if (targetSelectStep_Target == null)
            {
                return null;
            }
            TargetSelectStepInfo_Target targetSelectStepInfo_Target = new TargetSelectStepInfo_Target();
            targetSelectStepInfo_Target.rangeId = (TargetSelectRangeId)targetSelectStep_Target.rangeId;
            targetSelectStepInfo_Target.filterType = (TargetSelectTargetFilterType)targetSelectStep_Target.filterType;
            return targetSelectStepInfo_Target;
        }
        
        public static TargetSelectStepInfo_Dir GetTargetSelectStepInfo_Dir(Phoenix.ConfigData.TargetSelectStepConfigData_Dir targetSelectStep_Dir)
        {
            if (targetSelectStep_Dir == null)
            {
                return null;
            }
            TargetSelectStepInfo_Dir targetSelectStepInfo_Dir = new TargetSelectStepInfo_Dir();
            return targetSelectStepInfo_Dir;
        }
        
        public static TargetSelectStepInfo_TeleportSelf GetTargetSelectStepInfo_TeleportSelf(Phoenix.ConfigData.TargetSelectStepConfigData_TeleportSelf targetSelectStep_TeleportSelf)
        {
            if (targetSelectStep_TeleportSelf == null)
            {
                return null;
            }
            TargetSelectStepInfo_TeleportSelf targetSelectStepInfo_TeleportSelf = new TargetSelectStepInfo_TeleportSelf();
            targetSelectStepInfo_TeleportSelf.rangeId = (TargetSelectRangeId)targetSelectStep_TeleportSelf.rangeId;
            return targetSelectStepInfo_TeleportSelf;
        }
        
        public static TargetSelectStepInfo_TeleportTarget GetTargetSelectStepInfo_TeleportTarget(Phoenix.ConfigData.TargetSelectStepConfigData_TeleportTarget targetSelectStep_TeleportTarget)
        {
            if (targetSelectStep_TeleportTarget == null)
            {
                return null;
            }
            TargetSelectStepInfo_TeleportTarget targetSelectStepInfo_TeleportTarget = new TargetSelectStepInfo_TeleportTarget();
            targetSelectStepInfo_TeleportTarget.rangeId = (TargetSelectRangeId)targetSelectStep_TeleportTarget.rangeId;
            targetSelectStepInfo_TeleportTarget.filterType = (TargetSelectTargetFilterType)targetSelectStep_TeleportTarget.filterType;
            targetSelectStepInfo_TeleportTarget.teleportRangeId = (TargetSelectRangeId)targetSelectStep_TeleportTarget.teleportRangeId;
            targetSelectStepInfo_TeleportTarget.fromSelfOrTarget = targetSelectStep_TeleportTarget.fromSelfOrTarget;
            return targetSelectStepInfo_TeleportTarget;
        }
        
        public static TargetSelectStepInfo_Summon GetTargetSelectStepInfo_Summon(Phoenix.ConfigData.TargetSelectStepConfigData_Summon targetSelectStep_Summon)
        {
            if (targetSelectStep_Summon == null)
            {
                return null;
            }
            TargetSelectStepInfo_Summon targetSelectStepInfo_Summon = new TargetSelectStepInfo_Summon();
            targetSelectStepInfo_Summon.rangeId = (TargetSelectRangeId)targetSelectStep_Summon.rangeId;
            targetSelectStepInfo_Summon.actorRid = targetSelectStep_Summon.actorRid;
            return targetSelectStepInfo_Summon;
        }
        
        public static SkillEffectInfo GetSkillEffectInfo(Phoenix.ConfigData.SkillEffectConfigData skillEffect)
        {
            if (skillEffect == null)
            {
                return null;
            }
            SkillEffectInfo skillEffectInfo = null;
            switch (skillEffect.effectType)
            {
                case Phoenix.ConfigData.SkillEffectFuncType.AttachBuff_Rid:
                    skillEffectInfo = GetSkillEffectInfo_AttachBuff_Rid(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_Rid);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.AttachBuff_RandomRid:
                    skillEffectInfo = GetSkillEffectInfo_AttachBuff_RandomRid(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomRid);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.AttachBuff_RandomCustom:
                    skillEffectInfo = GetSkillEffectInfo_AttachBuff_RandomCustom(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomCustom);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.DetachBuff_Rid:
                    skillEffectInfo = GetSkillEffectInfo_DetachBuff_Rid(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Rid);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.DetachBuff_Tag:
                    skillEffectInfo = GetSkillEffectInfo_DetachBuff_Tag(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Tag);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Damage:
                    skillEffectInfo = GetSkillEffectInfo_Damage(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Damage);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Heal:
                    skillEffectInfo = GetSkillEffectInfo_Heal(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Heal);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Summon_Actor_RidToPos:
                    skillEffectInfo = GetSkillEffectInfo_Summon_Actor_RidToPos(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Summon_Actor_RidToPos_ConstLevel:
                    skillEffectInfo = GetSkillEffectInfo_Summon_Actor_RidToPos_ConstLevel(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevel);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Summon_TerrainBuff_FullRange:
                    skillEffectInfo = GetSkillEffectInfo_Summon_TerrainBuff_FullRange(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Summon_TerrainBuff_FullRange);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Move_Actor:
                    skillEffectInfo = GetSkillEffectInfo_Move_Actor(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Move_Actor);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Teleport_Actor:
                    skillEffectInfo = GetSkillEffectInfo_Teleport_Actor(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Teleport_Actor);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Control_Actor:
                    skillEffectInfo = GetSkillEffectInfo_Control_Actor(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Control_Actor);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.Transform_Actor:
                    skillEffectInfo = GetSkillEffectInfo_Transform_Actor(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_Transform_Actor);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.ExtraMove_ConstMovePoint:
                    skillEffectInfo = GetSkillEffectInfo_ExtraMove_ConstMovePoint(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_ConstMovePoint);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.ExtraMove_LeftMovePoint:
                    skillEffectInfo = GetSkillEffectInfo_ExtraMove_LeftMovePoint(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_LeftMovePoint);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.ExtraAction:
                    skillEffectInfo = GetSkillEffectInfo_ExtraAction(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_ExtraAction);
                    break;
                case Phoenix.ConfigData.SkillEffectFuncType.ChangeTeamEnergy:
                    skillEffectInfo = GetSkillEffectInfo_ChangeTeamEnergy(skillEffect as Phoenix.ConfigData.SkillEffectConfigData_ChangeTeamEnergy);
                    break;
            }
            if (skillEffectInfo != null)
            {
                foreach(var condition in skillEffect.conditionList)
                {
                    skillEffectInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
                }
            }
            return skillEffectInfo;
        }
        
        public static SkillEffectInfo_AttachBuff_Rid GetSkillEffectInfo_AttachBuff_Rid(Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_Rid skillEffect_AttachBuff_Rid)
        {
            if (skillEffect_AttachBuff_Rid == null)
            {
                return null;
            }
            SkillEffectInfo_AttachBuff_Rid skillEffectInfo_AttachBuff_Rid = new SkillEffectInfo_AttachBuff_Rid();
            foreach(var item in skillEffect_AttachBuff_Rid.itemList)
            {
                skillEffectInfo_AttachBuff_Rid.itemList.Add(GetBattleAttachBuffItemInfo(item));
            }
            skillEffectInfo_AttachBuff_Rid.target = GetBattleArgumentInfo_Entity(skillEffect_AttachBuff_Rid.target);
            foreach(var condition in skillEffect_AttachBuff_Rid.conditionList)
            {
                skillEffectInfo_AttachBuff_Rid.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_AttachBuff_Rid;
        }
        
        public static SkillEffectInfo_AttachBuff_RandomRid GetSkillEffectInfo_AttachBuff_RandomRid(Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomRid skillEffect_AttachBuff_RandomRid)
        {
            if (skillEffect_AttachBuff_RandomRid == null)
            {
                return null;
            }
            SkillEffectInfo_AttachBuff_RandomRid skillEffectInfo_AttachBuff_RandomRid = new SkillEffectInfo_AttachBuff_RandomRid();
            skillEffectInfo_AttachBuff_RandomRid.buffBatchRid = skillEffect_AttachBuff_RandomRid.buffBatchRid;
            skillEffectInfo_AttachBuff_RandomRid.count = skillEffect_AttachBuff_RandomRid.count;
            skillEffectInfo_AttachBuff_RandomRid.target = GetBattleArgumentInfo_Entity(skillEffect_AttachBuff_RandomRid.target);
            foreach(var condition in skillEffect_AttachBuff_RandomRid.conditionList)
            {
                skillEffectInfo_AttachBuff_RandomRid.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_AttachBuff_RandomRid;
        }
        
        public static SkillEffectInfo_AttachBuff_RandomCustom GetSkillEffectInfo_AttachBuff_RandomCustom(Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomCustom skillEffect_AttachBuff_RandomCustom)
        {
            if (skillEffect_AttachBuff_RandomCustom == null)
            {
                return null;
            }
            SkillEffectInfo_AttachBuff_RandomCustom skillEffectInfo_AttachBuff_RandomCustom = new SkillEffectInfo_AttachBuff_RandomCustom();
            foreach(var item in skillEffect_AttachBuff_RandomCustom.itemList)
            {
                skillEffectInfo_AttachBuff_RandomCustom.itemList.Add(GetBattleAttachBuffItemInfo_Random(item));
            }
            skillEffectInfo_AttachBuff_RandomCustom.count = skillEffect_AttachBuff_RandomCustom.count;
            skillEffectInfo_AttachBuff_RandomCustom.target = GetBattleArgumentInfo_Entity(skillEffect_AttachBuff_RandomCustom.target);
            foreach(var condition in skillEffect_AttachBuff_RandomCustom.conditionList)
            {
                skillEffectInfo_AttachBuff_RandomCustom.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_AttachBuff_RandomCustom;
        }
        
        public static BattleAttachBuffItemInfo_Random GetBattleAttachBuffItemInfo_Random(Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random battleAttachBuffItem_Random)
        {
            if (battleAttachBuffItem_Random == null)
            {
                return null;
            }
            BattleAttachBuffItemInfo_Random battleAttachBuffItemInfo_Random = new BattleAttachBuffItemInfo_Random();
            battleAttachBuffItemInfo_Random.buffRid = battleAttachBuffItem_Random.buffRid;
            battleAttachBuffItemInfo_Random.level = battleAttachBuffItem_Random.level;
            battleAttachBuffItemInfo_Random.weight = battleAttachBuffItem_Random.weight;
            battleAttachBuffItemInfo_Random.lifeTime = battleAttachBuffItem_Random.lifeTime;
            return battleAttachBuffItemInfo_Random;
        }
        
        public static SkillEffectInfo_DetachBuff_Rid GetSkillEffectInfo_DetachBuff_Rid(Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Rid skillEffect_DetachBuff_Rid)
        {
            if (skillEffect_DetachBuff_Rid == null)
            {
                return null;
            }
            SkillEffectInfo_DetachBuff_Rid skillEffectInfo_DetachBuff_Rid = new SkillEffectInfo_DetachBuff_Rid();
            foreach(var item in skillEffect_DetachBuff_Rid.itemList)
            {
                skillEffectInfo_DetachBuff_Rid.itemList.Add(GetBattleDetachBuffItemInfo(item));
            }
            skillEffectInfo_DetachBuff_Rid.target = GetBattleArgumentInfo_Entity(skillEffect_DetachBuff_Rid.target);
            foreach(var condition in skillEffect_DetachBuff_Rid.conditionList)
            {
                skillEffectInfo_DetachBuff_Rid.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_DetachBuff_Rid;
        }
        
        public static BattleDetachBuffItemInfo GetBattleDetachBuffItemInfo(Phoenix.ConfigData.BattleDetachBuffItemConfigData battleDetachBuffItem)
        {
            if (battleDetachBuffItem == null)
            {
                return null;
            }
            BattleDetachBuffItemInfo battleDetachBuffItemInfo = new BattleDetachBuffItemInfo();
            battleDetachBuffItemInfo.buffRid = battleDetachBuffItem.buffRid;
            battleDetachBuffItemInfo.level = battleDetachBuffItem.level;
            return battleDetachBuffItemInfo;
        }
        
        public static SkillEffectInfo_DetachBuff_Tag GetSkillEffectInfo_DetachBuff_Tag(Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Tag skillEffect_DetachBuff_Tag)
        {
            if (skillEffect_DetachBuff_Tag == null)
            {
                return null;
            }
            SkillEffectInfo_DetachBuff_Tag skillEffectInfo_DetachBuff_Tag = new SkillEffectInfo_DetachBuff_Tag();
            foreach(var tag in skillEffect_DetachBuff_Tag.tagList)
            {
                skillEffectInfo_DetachBuff_Tag.tagList.Add((BuffTagId)tag);
            }
            skillEffectInfo_DetachBuff_Tag.count = skillEffect_DetachBuff_Tag.count;
            skillEffectInfo_DetachBuff_Tag.target = GetBattleArgumentInfo_Entity(skillEffect_DetachBuff_Tag.target);
            foreach(var condition in skillEffect_DetachBuff_Tag.conditionList)
            {
                skillEffectInfo_DetachBuff_Tag.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_DetachBuff_Tag;
        }
        
        public static SkillEffectInfo_Damage GetSkillEffectInfo_Damage(Phoenix.ConfigData.SkillEffectConfigData_Damage skillEffect_Damage)
        {
            if (skillEffect_Damage == null)
            {
                return null;
            }
            SkillEffectInfo_Damage skillEffectInfo_Damage = new SkillEffectInfo_Damage();
            skillEffectInfo_Damage.damageType = (SkillDamageType)skillEffect_Damage.damageType;
            skillEffectInfo_Damage.elementType = (EntityElementId)skillEffect_Damage.elementType;
            skillEffectInfo_Damage.argument = GetSkillEffectArgumentInfo(skillEffect_Damage.argument);
            skillEffectInfo_Damage.target = GetBattleArgumentInfo_Entity(skillEffect_Damage.target);
            foreach(var condition in skillEffect_Damage.conditionList)
            {
                skillEffectInfo_Damage.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Damage;
        }
        
        public static SkillEffectArgumentInfo GetSkillEffectArgumentInfo(Phoenix.ConfigData.SkillEffectArgumentConfigData skillEffectArgument)
        {
            if (skillEffectArgument == null)
            {
                return null;
            }
            SkillEffectArgumentInfo skillEffectArgumentInfo = new SkillEffectArgumentInfo();
            skillEffectArgumentInfo.baseValue = new FixedValue(skillEffectArgument.baseValue, 100);
            skillEffectArgumentInfo.physicalAttackRate = new FixedValue(skillEffectArgument.physicalAttackRate, 100);
            skillEffectArgumentInfo.magicalAttackRate = new FixedValue(skillEffectArgument.magicalAttackRate, 100);
            skillEffectArgumentInfo.maxTargetHpRate = new FixedValue(skillEffectArgument.maxTargetHpRate, 100);
            skillEffectArgumentInfo.curTargetHpRate = new FixedValue(skillEffectArgument.curTargetHpRate, 100);
            return skillEffectArgumentInfo;
        }
        
        public static SkillEffectInfo_Heal GetSkillEffectInfo_Heal(Phoenix.ConfigData.SkillEffectConfigData_Heal skillEffect_Heal)
        {
            if (skillEffect_Heal == null)
            {
                return null;
            }
            SkillEffectInfo_Heal skillEffectInfo_Heal = new SkillEffectInfo_Heal();
            skillEffectInfo_Heal.argument = GetSkillEffectArgumentInfo(skillEffect_Heal.argument);
            skillEffectInfo_Heal.target = GetBattleArgumentInfo_Entity(skillEffect_Heal.target);
            foreach(var condition in skillEffect_Heal.conditionList)
            {
                skillEffectInfo_Heal.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Heal;
        }
        
        public static SkillEffectInfo_Summon_Actor_RidToPos GetSkillEffectInfo_Summon_Actor_RidToPos(Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos skillEffect_Summon_Actor_RidToPos)
        {
            if (skillEffect_Summon_Actor_RidToPos == null)
            {
                return null;
            }
            SkillEffectInfo_Summon_Actor_RidToPos skillEffectInfo_Summon_Actor_RidToPos = new SkillEffectInfo_Summon_Actor_RidToPos();
            skillEffectInfo_Summon_Actor_RidToPos.grid = GetBattleArgumentInfo_Grid(skillEffect_Summon_Actor_RidToPos.grid);
            skillEffectInfo_Summon_Actor_RidToPos.rid = skillEffect_Summon_Actor_RidToPos.rid;
            foreach(var condition in skillEffect_Summon_Actor_RidToPos.conditionList)
            {
                skillEffectInfo_Summon_Actor_RidToPos.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Summon_Actor_RidToPos;
        }
        
        public static SkillEffectInfo_Summon_Actor_RidToPos_ConstLevel GetSkillEffectInfo_Summon_Actor_RidToPos_ConstLevel(Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevel skillEffect_Summon_Actor_RidToPos_ConstLevel)
        {
            if (skillEffect_Summon_Actor_RidToPos_ConstLevel == null)
            {
                return null;
            }
            SkillEffectInfo_Summon_Actor_RidToPos_ConstLevel skillEffectInfo_Summon_Actor_RidToPos_ConstLevel = new SkillEffectInfo_Summon_Actor_RidToPos_ConstLevel();
            skillEffectInfo_Summon_Actor_RidToPos_ConstLevel.rid = skillEffect_Summon_Actor_RidToPos_ConstLevel.rid;
            skillEffectInfo_Summon_Actor_RidToPos_ConstLevel.level = skillEffect_Summon_Actor_RidToPos_ConstLevel.level;
            skillEffectInfo_Summon_Actor_RidToPos_ConstLevel.grid = GetBattleArgumentInfo_Grid(skillEffect_Summon_Actor_RidToPos_ConstLevel.grid);
            foreach(var condition in skillEffect_Summon_Actor_RidToPos_ConstLevel.conditionList)
            {
                skillEffectInfo_Summon_Actor_RidToPos_ConstLevel.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Summon_Actor_RidToPos_ConstLevel;
        }
        
        public static SkillEffectInfo_Summon_TerrainBuff_FullRange GetSkillEffectInfo_Summon_TerrainBuff_FullRange(Phoenix.ConfigData.SkillEffectConfigData_Summon_TerrainBuff_FullRange skillEffect_Summon_TerrainBuff_FullRange)
        {
            if (skillEffect_Summon_TerrainBuff_FullRange == null)
            {
                return null;
            }
            SkillEffectInfo_Summon_TerrainBuff_FullRange skillEffectInfo_Summon_TerrainBuff_FullRange = new SkillEffectInfo_Summon_TerrainBuff_FullRange();
            skillEffectInfo_Summon_TerrainBuff_FullRange.rid = skillEffect_Summon_TerrainBuff_FullRange.rid;
            skillEffectInfo_Summon_TerrainBuff_FullRange.lifeTime = skillEffect_Summon_TerrainBuff_FullRange.lifeTime;
            skillEffectInfo_Summon_TerrainBuff_FullRange.grid = GetBattleArgumentInfo_Grid(skillEffect_Summon_TerrainBuff_FullRange.grid);
            foreach(var condition in skillEffect_Summon_TerrainBuff_FullRange.conditionList)
            {
                skillEffectInfo_Summon_TerrainBuff_FullRange.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Summon_TerrainBuff_FullRange;
        }
        
        public static SkillEffectInfo_Move_Actor GetSkillEffectInfo_Move_Actor(Phoenix.ConfigData.SkillEffectConfigData_Move_Actor skillEffect_Move_Actor)
        {
            if (skillEffect_Move_Actor == null)
            {
                return null;
            }
            SkillEffectInfo_Move_Actor skillEffectInfo_Move_Actor = new SkillEffectInfo_Move_Actor();
            skillEffectInfo_Move_Actor.distance = skillEffect_Move_Actor.distance;
            foreach(var bumpEffect in skillEffect_Move_Actor.bumpEffectList)
            {
                skillEffectInfo_Move_Actor.bumpEffectList.Add(GetSkillEffectInfo(bumpEffect));
            }
            skillEffectInfo_Move_Actor.target = GetBattleArgumentInfo_Entity(skillEffect_Move_Actor.target);
            foreach(var condition in skillEffect_Move_Actor.conditionList)
            {
                skillEffectInfo_Move_Actor.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Move_Actor;
        }
        
        public static SkillEffectInfo_Teleport_Actor GetSkillEffectInfo_Teleport_Actor(Phoenix.ConfigData.SkillEffectConfigData_Teleport_Actor skillEffect_Teleport_Actor)
        {
            if (skillEffect_Teleport_Actor == null)
            {
                return null;
            }
            SkillEffectInfo_Teleport_Actor skillEffectInfo_Teleport_Actor = new SkillEffectInfo_Teleport_Actor();
            skillEffectInfo_Teleport_Actor.targetGrid = GetBattleArgumentInfo_Grid(skillEffect_Teleport_Actor.targetGrid);
            skillEffectInfo_Teleport_Actor.target = GetBattleArgumentInfo_Entity(skillEffect_Teleport_Actor.target);
            foreach(var condition in skillEffect_Teleport_Actor.conditionList)
            {
                skillEffectInfo_Teleport_Actor.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Teleport_Actor;
        }
        
        public static SkillEffectInfo_Control_Actor GetSkillEffectInfo_Control_Actor(Phoenix.ConfigData.SkillEffectConfigData_Control_Actor skillEffect_Control_Actor)
        {
            if (skillEffect_Control_Actor == null)
            {
                return null;
            }
            SkillEffectInfo_Control_Actor skillEffectInfo_Control_Actor = new SkillEffectInfo_Control_Actor();
            skillEffectInfo_Control_Actor.target = GetBattleArgumentInfo_Entity(skillEffect_Control_Actor.target);
            foreach(var condition in skillEffect_Control_Actor.conditionList)
            {
                skillEffectInfo_Control_Actor.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Control_Actor;
        }
        
        public static SkillEffectInfo_Transform_Actor GetSkillEffectInfo_Transform_Actor(Phoenix.ConfigData.SkillEffectConfigData_Transform_Actor skillEffect_Transform_Actor)
        {
            if (skillEffect_Transform_Actor == null)
            {
                return null;
            }
            SkillEffectInfo_Transform_Actor skillEffectInfo_Transform_Actor = new SkillEffectInfo_Transform_Actor();
            skillEffectInfo_Transform_Actor.actorRid = skillEffect_Transform_Actor.actorRid;
            skillEffectInfo_Transform_Actor.lifeTime = skillEffect_Transform_Actor.lifeTime;
            skillEffectInfo_Transform_Actor.target = GetBattleArgumentInfo_Entity(skillEffect_Transform_Actor.target);
            foreach(var condition in skillEffect_Transform_Actor.conditionList)
            {
                skillEffectInfo_Transform_Actor.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_Transform_Actor;
        }
        
        public static SkillEffectInfo_ExtraMove_ConstMovePoint GetSkillEffectInfo_ExtraMove_ConstMovePoint(Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_ConstMovePoint skillEffect_ExtraMove_ConstMovePoint)
        {
            if (skillEffect_ExtraMove_ConstMovePoint == null)
            {
                return null;
            }
            SkillEffectInfo_ExtraMove_ConstMovePoint skillEffectInfo_ExtraMove_ConstMovePoint = new SkillEffectInfo_ExtraMove_ConstMovePoint();
            skillEffectInfo_ExtraMove_ConstMovePoint.movePoint = skillEffect_ExtraMove_ConstMovePoint.movePoint;
            skillEffectInfo_ExtraMove_ConstMovePoint.target = GetBattleArgumentInfo_Entity(skillEffect_ExtraMove_ConstMovePoint.target);
            foreach(var condition in skillEffect_ExtraMove_ConstMovePoint.conditionList)
            {
                skillEffectInfo_ExtraMove_ConstMovePoint.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_ExtraMove_ConstMovePoint;
        }
        
        public static SkillEffectInfo_ExtraMove_LeftMovePoint GetSkillEffectInfo_ExtraMove_LeftMovePoint(Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_LeftMovePoint skillEffect_ExtraMove_LeftMovePoint)
        {
            if (skillEffect_ExtraMove_LeftMovePoint == null)
            {
                return null;
            }
            SkillEffectInfo_ExtraMove_LeftMovePoint skillEffectInfo_ExtraMove_LeftMovePoint = new SkillEffectInfo_ExtraMove_LeftMovePoint();
            skillEffectInfo_ExtraMove_LeftMovePoint.target = GetBattleArgumentInfo_Entity(skillEffect_ExtraMove_LeftMovePoint.target);
            foreach(var condition in skillEffect_ExtraMove_LeftMovePoint.conditionList)
            {
                skillEffectInfo_ExtraMove_LeftMovePoint.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_ExtraMove_LeftMovePoint;
        }
        
        public static SkillEffectInfo_ExtraAction GetSkillEffectInfo_ExtraAction(Phoenix.ConfigData.SkillEffectConfigData_ExtraAction skillEffect_ExtraAction)
        {
            if (skillEffect_ExtraAction == null)
            {
                return null;
            }
            SkillEffectInfo_ExtraAction skillEffectInfo_ExtraAction = new SkillEffectInfo_ExtraAction();
            skillEffectInfo_ExtraAction.target = GetBattleArgumentInfo_Entity(skillEffect_ExtraAction.target);
            foreach(var condition in skillEffect_ExtraAction.conditionList)
            {
                skillEffectInfo_ExtraAction.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_ExtraAction;
        }
        
        public static SkillEffectInfo_ChangeTeamEnergy GetSkillEffectInfo_ChangeTeamEnergy(Phoenix.ConfigData.SkillEffectConfigData_ChangeTeamEnergy skillEffect_ChangeTeamEnergy)
        {
            if (skillEffect_ChangeTeamEnergy == null)
            {
                return null;
            }
            SkillEffectInfo_ChangeTeamEnergy skillEffectInfo_ChangeTeamEnergy = new SkillEffectInfo_ChangeTeamEnergy();
            skillEffectInfo_ChangeTeamEnergy.deltaEnergy = skillEffect_ChangeTeamEnergy.deltaEnergy;
            skillEffectInfo_ChangeTeamEnergy.campRef = (BattleCampRefType)skillEffect_ChangeTeamEnergy.campRef;
            foreach(var condition in skillEffect_ChangeTeamEnergy.conditionList)
            {
                skillEffectInfo_ChangeTeamEnergy.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return skillEffectInfo_ChangeTeamEnergy;
        }
        
        public static BuffInfo GetBuffInfo(Phoenix.ConfigData.BuffConfigData buff)
        {
            if (buff == null)
            {
                return null;
            }
            BuffInfo buffInfo = new BuffInfo();
            buffInfo.id = buff.id;
            buffInfo.name = buff.name;
            buffInfo.desc = buff.desc;
            buffInfo.maxLevel = buff.maxLevel;
            buffInfo.lifeTimeType = (BuffLifeTimeType)buff.lifeTimeType;
            buffInfo.isShow = buff.isShow;
            buffInfo.iconName = buff.iconName;
            buffInfo.needAnnounce = buff.needAnnounce;
            foreach(var tag in buff.tagList)
            {
                buffInfo.tagList.Add((BuffTagId)tag);
            }
            foreach(var effect in buff.effectList)
            {
                buffInfo.effectList.Add(GetBuffEffectInfo(effect));
            }
            buffInfo.attachEffectId = buff.attachEffectId;
            buffInfo.loopEffectId = buff.loopEffectId;
            buffInfo.loopAnimName = buff.loopAnimName;
            return buffInfo;
        }
        
        public static BuffEffectInfo GetBuffEffectInfo(Phoenix.ConfigData.BuffEffectConfigData buffEffect)
        {
            if (buffEffect == null)
            {
                return null;
            }
            BuffEffectInfo buffEffectInfo = null;
            switch (buffEffect.effectType)
            {
                case Phoenix.ConfigData.BuffEffectType.AttributeChange:
                    buffEffectInfo = GetBuffEffectInfo_AttributeChange(buffEffect as Phoenix.ConfigData.BuffEffectConfigData_AttributeChange);
                    break;
                case Phoenix.ConfigData.BuffEffectType.AttributeAccumulate:
                    buffEffectInfo = GetBuffEffectInfo_AttributeAccumulate(buffEffect as Phoenix.ConfigData.BuffEffectConfigData_AttributeAccumulate);
                    break;
                case Phoenix.ConfigData.BuffEffectType.AttributeDepend:
                    buffEffectInfo = GetBuffEffectInfo_AttributeDepend(buffEffect as Phoenix.ConfigData.BuffEffectConfigData_AttributeDepend);
                    break;
                case Phoenix.ConfigData.BuffEffectType.StateApply:
                    buffEffectInfo = GetBuffEffectInfo_StateApply(buffEffect as Phoenix.ConfigData.BuffEffectConfigData_StateApply);
                    break;
                case Phoenix.ConfigData.BuffEffectType.SkillTrigger:
                    buffEffectInfo = GetBuffEffectInfo_SkillTrigger(buffEffect as Phoenix.ConfigData.BuffEffectConfigData_SkillTrigger);
                    break;
                case Phoenix.ConfigData.BuffEffectType.AuraApply:
                    buffEffectInfo = GetBuffEffectInfo_AuraApply(buffEffect as Phoenix.ConfigData.BuffEffectConfigData_AuraApply);
                    break;
            }
            if (buffEffectInfo != null)
            {
                foreach(var condition in buffEffect.conditionList)
                {
                    buffEffectInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
                }
            }
            return buffEffectInfo;
        }
        
        public static BuffEffectInfo_AttributeChange GetBuffEffectInfo_AttributeChange(Phoenix.ConfigData.BuffEffectConfigData_AttributeChange buffEffect_AttributeChange)
        {
            if (buffEffect_AttributeChange == null)
            {
                return null;
            }
            BuffEffectInfo_AttributeChange buffEffectInfo_AttributeChange = new BuffEffectInfo_AttributeChange();
            buffEffectInfo_AttributeChange.partId = (AttributePartId)buffEffect_AttributeChange.partId;
            buffEffectInfo_AttributeChange.value = new FixedValue(buffEffect_AttributeChange.value, 100);
            foreach(var condition in buffEffect_AttributeChange.conditionList)
            {
                buffEffectInfo_AttributeChange.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_AttributeChange;
        }
        
        public static BuffEffectInfo_AttributeAccumulate GetBuffEffectInfo_AttributeAccumulate(Phoenix.ConfigData.BuffEffectConfigData_AttributeAccumulate buffEffect_AttributeAccumulate)
        {
            if (buffEffect_AttributeAccumulate == null)
            {
                return null;
            }
            BuffEffectInfo_AttributeAccumulate buffEffectInfo_AttributeAccumulate = new BuffEffectInfo_AttributeAccumulate();
            buffEffectInfo_AttributeAccumulate.partId = (AttributePartId)buffEffect_AttributeAccumulate.partId;
            buffEffectInfo_AttributeAccumulate.value = new FixedValue(buffEffect_AttributeAccumulate.value, 100);
            buffEffectInfo_AttributeAccumulate.accumulateType = (BuffEffectAttributeAccumulateType)buffEffect_AttributeAccumulate.accumulateType;
            buffEffectInfo_AttributeAccumulate.accumulateMult = new FixedValue(buffEffect_AttributeAccumulate.accumulateMult, 100);
            buffEffectInfo_AttributeAccumulate.minAccumulate = buffEffect_AttributeAccumulate.minAccumulate;
            buffEffectInfo_AttributeAccumulate.maxAccumulate = buffEffect_AttributeAccumulate.maxAccumulate;
            foreach(var condition in buffEffect_AttributeAccumulate.conditionList)
            {
                buffEffectInfo_AttributeAccumulate.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_AttributeAccumulate;
        }
        
        public static BuffEffectInfo_AttributeDepend GetBuffEffectInfo_AttributeDepend(Phoenix.ConfigData.BuffEffectConfigData_AttributeDepend buffEffect_AttributeDepend)
        {
            if (buffEffect_AttributeDepend == null)
            {
                return null;
            }
            BuffEffectInfo_AttributeDepend buffEffectInfo_AttributeDepend = new BuffEffectInfo_AttributeDepend();
            buffEffectInfo_AttributeDepend.attributeId = (AttributeId)buffEffect_AttributeDepend.attributeId;
            buffEffectInfo_AttributeDepend.dependAttributeId = (AttributeId)buffEffect_AttributeDepend.dependAttributeId;
            buffEffectInfo_AttributeDepend.rate = new FixedValue(buffEffect_AttributeDepend.rate, 100);
            buffEffectInfo_AttributeDepend.isInverse = buffEffect_AttributeDepend.isInverse;
            foreach(var condition in buffEffect_AttributeDepend.conditionList)
            {
                buffEffectInfo_AttributeDepend.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_AttributeDepend;
        }
        
        public static BuffEffectInfo_StateApply GetBuffEffectInfo_StateApply(Phoenix.ConfigData.BuffEffectConfigData_StateApply buffEffect_StateApply)
        {
            if (buffEffect_StateApply == null)
            {
                return null;
            }
            BuffEffectInfo_StateApply buffEffectInfo_StateApply = null;
            switch (buffEffect_StateApply.stateType)
            {
                case Phoenix.ConfigData.BuffEffectStateType.Stun:
                    buffEffectInfo_StateApply = GetBuffEffectInfo_Stun(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectConfigData_Stun);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.AssistGuard:
                    buffEffectInfo_StateApply = GetBuffEffectInfo_AssistGuard(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectConfigData_AssistGuard);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.Dodge:
                    buffEffectInfo_StateApply = GetBuffEffectInfo_Dodge(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectConfigData_Dodge);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.Immune_SkillEffect:
                    buffEffectInfo_StateApply = GetBuffEffectInfo_Immune_SkillEffect(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectConfigData_Immune_SkillEffect);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.Immune_AttachBuff_Tag:
                    buffEffectInfo_StateApply = GetBuffEffectInfo_Immune_AttachBuff_Tag(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectConfigData_Immune_AttachBuff_Tag);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.GuardEffect:
                    buffEffectInfo_StateApply = GetBuffEffectGuardEffectInfo(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectGuardEffectConfigData);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.BanSkill:
                    buffEffectInfo_StateApply = GetBuffEffectBanSkillInfo(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectBanSkillConfigData);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.TriggerContinuousAttack:
                    buffEffectInfo_StateApply = GetBuffEffectTriggerContinuousAttackInfo(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectTriggerContinuousAttackConfigData);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.TriggerAdditionalAttack:
                    buffEffectInfo_StateApply = GetBuffEffectTriggerAdditionalAttackInfo(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectTriggerAdditionalAttackConfigData);
                    break;
                case Phoenix.ConfigData.BuffEffectStateType.CanResurrect:
                    buffEffectInfo_StateApply = GetBuffEffectCanResurrectInfo(buffEffect_StateApply as Phoenix.ConfigData.BuffEffectCanResurrectConfigData);
                    break;
            }
            if (buffEffectInfo_StateApply != null)
            {
                foreach(var condition in buffEffect_StateApply.conditionList)
                {
                    buffEffectInfo_StateApply.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
                }
            }
            return buffEffectInfo_StateApply;
        }
        
        public static BuffEffectInfo_Stun GetBuffEffectInfo_Stun(Phoenix.ConfigData.BuffEffectConfigData_Stun buffEffect_Stun)
        {
            if (buffEffect_Stun == null)
            {
                return null;
            }
            BuffEffectInfo_Stun buffEffectInfo_Stun = new BuffEffectInfo_Stun();
            foreach(var condition in buffEffect_Stun.conditionList)
            {
                buffEffectInfo_Stun.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_Stun;
        }
        
        public static BuffEffectInfo_AssistGuard GetBuffEffectInfo_AssistGuard(Phoenix.ConfigData.BuffEffectConfigData_AssistGuard buffEffect_AssistGuard)
        {
            if (buffEffect_AssistGuard == null)
            {
                return null;
            }
            BuffEffectInfo_AssistGuard buffEffectInfo_AssistGuard = new BuffEffectInfo_AssistGuard();
            buffEffectInfo_AssistGuard.rangeId = (TargetSelectRangeId)buffEffect_AssistGuard.rangeId;
            buffEffectInfo_AssistGuard.filterFuncType = (TargetSelectTargetFilterType)buffEffect_AssistGuard.filterFuncType;
            foreach(var condition in buffEffect_AssistGuard.conditionList)
            {
                buffEffectInfo_AssistGuard.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_AssistGuard;
        }
        
        public static BuffEffectInfo_Dodge GetBuffEffectInfo_Dodge(Phoenix.ConfigData.BuffEffectConfigData_Dodge buffEffect_Dodge)
        {
            if (buffEffect_Dodge == null)
            {
                return null;
            }
            BuffEffectInfo_Dodge buffEffectInfo_Dodge = new BuffEffectInfo_Dodge();
            buffEffectInfo_Dodge.count = buffEffect_Dodge.count;
            foreach(var condition in buffEffect_Dodge.conditionList)
            {
                buffEffectInfo_Dodge.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_Dodge;
        }
        
        public static BuffEffectInfo_Immune_SkillEffect GetBuffEffectInfo_Immune_SkillEffect(Phoenix.ConfigData.BuffEffectConfigData_Immune_SkillEffect buffEffect_Immune_SkillEffect)
        {
            if (buffEffect_Immune_SkillEffect == null)
            {
                return null;
            }
            BuffEffectInfo_Immune_SkillEffect buffEffectInfo_Immune_SkillEffect = new BuffEffectInfo_Immune_SkillEffect();
            buffEffectInfo_Immune_SkillEffect.effectType = (SkillEffectFuncType)buffEffect_Immune_SkillEffect.effectType;
            foreach(var condition in buffEffect_Immune_SkillEffect.conditionList)
            {
                buffEffectInfo_Immune_SkillEffect.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_Immune_SkillEffect;
        }
        
        public static BuffEffectInfo_Immune_AttachBuff_Tag GetBuffEffectInfo_Immune_AttachBuff_Tag(Phoenix.ConfigData.BuffEffectConfigData_Immune_AttachBuff_Tag buffEffect_Immune_AttachBuff_Tag)
        {
            if (buffEffect_Immune_AttachBuff_Tag == null)
            {
                return null;
            }
            BuffEffectInfo_Immune_AttachBuff_Tag buffEffectInfo_Immune_AttachBuff_Tag = new BuffEffectInfo_Immune_AttachBuff_Tag();
            foreach(var tag in buffEffect_Immune_AttachBuff_Tag.tagList)
            {
                buffEffectInfo_Immune_AttachBuff_Tag.tagList.Add((BuffTagId)tag);
            }
            foreach(var condition in buffEffect_Immune_AttachBuff_Tag.conditionList)
            {
                buffEffectInfo_Immune_AttachBuff_Tag.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_Immune_AttachBuff_Tag;
        }
        
        public static BuffEffectGuardEffectInfo GetBuffEffectGuardEffectInfo(Phoenix.ConfigData.BuffEffectGuardEffectConfigData buffEffectGuardEffect)
        {
            if (buffEffectGuardEffect == null)
            {
                return null;
            }
            BuffEffectGuardEffectInfo buffEffectGuardEffectInfo = new BuffEffectGuardEffectInfo();
            buffEffectGuardEffectInfo.count = buffEffectGuardEffect.count;
            foreach(var condition in buffEffectGuardEffect.conditionList)
            {
                buffEffectGuardEffectInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectGuardEffectInfo;
        }
        
        public static BuffEffectBanSkillInfo GetBuffEffectBanSkillInfo(Phoenix.ConfigData.BuffEffectBanSkillConfigData buffEffectBanSkill)
        {
            if (buffEffectBanSkill == null)
            {
                return null;
            }
            BuffEffectBanSkillInfo buffEffectBanSkillInfo = new BuffEffectBanSkillInfo();
            foreach(var condition in buffEffectBanSkill.conditionList)
            {
                buffEffectBanSkillInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectBanSkillInfo;
        }
        
        public static BuffEffectTriggerContinuousAttackInfo GetBuffEffectTriggerContinuousAttackInfo(Phoenix.ConfigData.BuffEffectTriggerContinuousAttackConfigData buffEffectTriggerContinuousAttack)
        {
            if (buffEffectTriggerContinuousAttack == null)
            {
                return null;
            }
            BuffEffectTriggerContinuousAttackInfo buffEffectTriggerContinuousAttackInfo = new BuffEffectTriggerContinuousAttackInfo();
            buffEffectTriggerContinuousAttackInfo.rate = new FixedValue(buffEffectTriggerContinuousAttack.rate, 100);
            foreach(var condition in buffEffectTriggerContinuousAttack.conditionList)
            {
                buffEffectTriggerContinuousAttackInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectTriggerContinuousAttackInfo;
        }
        
        public static BuffEffectTriggerAdditionalAttackInfo GetBuffEffectTriggerAdditionalAttackInfo(Phoenix.ConfigData.BuffEffectTriggerAdditionalAttackConfigData buffEffectTriggerAdditionalAttack)
        {
            if (buffEffectTriggerAdditionalAttack == null)
            {
                return null;
            }
            BuffEffectTriggerAdditionalAttackInfo buffEffectTriggerAdditionalAttackInfo = new BuffEffectTriggerAdditionalAttackInfo();
            buffEffectTriggerAdditionalAttackInfo.rate = new FixedValue(buffEffectTriggerAdditionalAttack.rate, 100);
            foreach(var condition in buffEffectTriggerAdditionalAttack.conditionList)
            {
                buffEffectTriggerAdditionalAttackInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectTriggerAdditionalAttackInfo;
        }
        
        public static BuffEffectCanResurrectInfo GetBuffEffectCanResurrectInfo(Phoenix.ConfigData.BuffEffectCanResurrectConfigData buffEffectCanResurrect)
        {
            if (buffEffectCanResurrect == null)
            {
                return null;
            }
            BuffEffectCanResurrectInfo buffEffectCanResurrectInfo = new BuffEffectCanResurrectInfo();
            foreach(var condition in buffEffectCanResurrect.conditionList)
            {
                buffEffectCanResurrectInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectCanResurrectInfo;
        }
        
        public static BuffEffectInfo_SkillTrigger GetBuffEffectInfo_SkillTrigger(Phoenix.ConfigData.BuffEffectConfigData_SkillTrigger buffEffect_SkillTrigger)
        {
            if (buffEffect_SkillTrigger == null)
            {
                return null;
            }
            BuffEffectInfo_SkillTrigger buffEffectInfo_SkillTrigger = new BuffEffectInfo_SkillTrigger();
            buffEffectInfo_SkillTrigger.trigger = GetBuffTriggerInfo(buffEffect_SkillTrigger.trigger);
            buffEffectInfo_SkillTrigger.limit = GetBattleTriggerLimitInfo(buffEffect_SkillTrigger.limit);
            foreach(var effect in buffEffect_SkillTrigger.effectList)
            {
                buffEffectInfo_SkillTrigger.effectList.Add(GetSkillEffectInfo(effect));
            }
            buffEffectInfo_SkillTrigger.dramaName = buffEffect_SkillTrigger.dramaName;
            foreach(var condition in buffEffect_SkillTrigger.conditionList)
            {
                buffEffectInfo_SkillTrigger.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_SkillTrigger;
        }
        
        public static BuffTriggerInfo GetBuffTriggerInfo(Phoenix.ConfigData.BuffTriggerConfigData buffTrigger)
        {
            if (buffTrigger == null)
            {
                return null;
            }
            BuffTriggerInfo buffTriggerInfo = null;
            switch (buffTrigger.triggerType)
            {
                case Phoenix.ConfigData.BuffTriggerType.AfterLocateEnd:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterLocateEnd(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.BeforeEngageBegin:
                    buffTriggerInfo = GetBuffTriggerInfo_BeforeEngageBegin(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.AfterEngageBegin:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterEngageBegin(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.BeforeCastSkill:
                    buffTriggerInfo = GetBuffTriggerInfo_BeforeCastSkill(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.AfterCastSkill:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterCastSkill(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.BeforeEngageEnd:
                    buffTriggerInfo = GetBuffTriggerInfo_BeforeEngageEnd(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.AfterEngageEnd:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterEngageEnd(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.BeforeActionEnd:
                    buffTriggerInfo = GetBuffTriggerInfo_BeforeActionEnd(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.AfterActionEnd:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterActionEnd(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.MainBattleStart:
                    buffTriggerInfo = GetBuffTriggerInfo_MainBattleStart(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.TurnStart:
                    buffTriggerInfo = GetBuffTriggerInfo_TurnStart(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_TurnStart);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.TurnEnd:
                    buffTriggerInfo = GetBuffTriggerInfo_TurnEnd(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.AfterEntityDead:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterEntityDead(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead);
                    break;
                case Phoenix.ConfigData.BuffTriggerType.AfterEntityMove:
                    buffTriggerInfo = GetBuffTriggerInfo_AfterEntityMove(buffTrigger as Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove);
                    break;
            }
            if (buffTriggerInfo != null)
            {
                foreach(var condition in buffTrigger.conditionList)
                {
                    buffTriggerInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
                }
            }
            return buffTriggerInfo;
        }
        
        public static BuffTriggerInfo_AfterLocateEnd GetBuffTriggerInfo_AfterLocateEnd(Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd buffTrigger_AfterLocateEnd)
        {
            if (buffTrigger_AfterLocateEnd == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterLocateEnd buffTriggerInfo_AfterLocateEnd = new BuffTriggerInfo_AfterLocateEnd();
            buffTriggerInfo_AfterLocateEnd.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterLocateEnd.entityCheck);
            foreach(var condition in buffTrigger_AfterLocateEnd.conditionList)
            {
                buffTriggerInfo_AfterLocateEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterLocateEnd;
        }
        
        public static BuffTriggerInfo_BeforeEngageBegin GetBuffTriggerInfo_BeforeEngageBegin(Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin buffTrigger_BeforeEngageBegin)
        {
            if (buffTrigger_BeforeEngageBegin == null)
            {
                return null;
            }
            BuffTriggerInfo_BeforeEngageBegin buffTriggerInfo_BeforeEngageBegin = new BuffTriggerInfo_BeforeEngageBegin();
            buffTriggerInfo_BeforeEngageBegin.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_BeforeEngageBegin.entityCheck);
            buffTriggerInfo_BeforeEngageBegin.skillCheck = GetBattleObjCheckInfo_Skill(buffTrigger_BeforeEngageBegin.skillCheck);
            foreach(var condition in buffTrigger_BeforeEngageBegin.conditionList)
            {
                buffTriggerInfo_BeforeEngageBegin.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_BeforeEngageBegin;
        }
        
        public static BattleObjCheckInfo_Skill GetBattleObjCheckInfo_Skill(Phoenix.ConfigData.BattleObjCheckConfigData_Skill battleObjCheck_Skill)
        {
            if (battleObjCheck_Skill == null)
            {
                return null;
            }
            BattleObjCheckInfo_Skill battleObjCheckInfo_Skill = null;
            switch (battleObjCheck_Skill.checkType)
            {
                case Phoenix.ConfigData.BattleObjCheckSkillType.Rid:
                    battleObjCheckInfo_Skill = GetBattleObjCheckInfo_Skill_Rid(battleObjCheck_Skill as Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Rid);
                    break;
                case Phoenix.ConfigData.BattleObjCheckSkillType.Tag:
                    battleObjCheckInfo_Skill = GetBattleObjCheckInfo_Skill_Tag(battleObjCheck_Skill as Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Tag);
                    break;
            }
            if (battleObjCheckInfo_Skill != null)
            {
            }
            return battleObjCheckInfo_Skill;
        }
        
        public static BattleObjCheckInfo_Skill_Rid GetBattleObjCheckInfo_Skill_Rid(Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Rid battleObjCheck_Skill_Rid)
        {
            if (battleObjCheck_Skill_Rid == null)
            {
                return null;
            }
            BattleObjCheckInfo_Skill_Rid battleObjCheckInfo_Skill_Rid = new BattleObjCheckInfo_Skill_Rid();
            battleObjCheckInfo_Skill_Rid.rid = battleObjCheck_Skill_Rid.rid;
            return battleObjCheckInfo_Skill_Rid;
        }
        
        public static BattleObjCheckInfo_Skill_Tag GetBattleObjCheckInfo_Skill_Tag(Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Tag battleObjCheck_Skill_Tag)
        {
            if (battleObjCheck_Skill_Tag == null)
            {
                return null;
            }
            BattleObjCheckInfo_Skill_Tag battleObjCheckInfo_Skill_Tag = new BattleObjCheckInfo_Skill_Tag();
            foreach(var tag in battleObjCheck_Skill_Tag.tagList)
            {
                battleObjCheckInfo_Skill_Tag.tagList.Add((SkillTagType)tag);
            }
            return battleObjCheckInfo_Skill_Tag;
        }
        
        public static BuffTriggerInfo_AfterEngageBegin GetBuffTriggerInfo_AfterEngageBegin(Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin buffTrigger_AfterEngageBegin)
        {
            if (buffTrigger_AfterEngageBegin == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterEngageBegin buffTriggerInfo_AfterEngageBegin = new BuffTriggerInfo_AfterEngageBegin();
            buffTriggerInfo_AfterEngageBegin.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterEngageBegin.entityCheck);
            buffTriggerInfo_AfterEngageBegin.skillCheck = GetBattleObjCheckInfo_Skill(buffTrigger_AfterEngageBegin.skillCheck);
            foreach(var condition in buffTrigger_AfterEngageBegin.conditionList)
            {
                buffTriggerInfo_AfterEngageBegin.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterEngageBegin;
        }
        
        public static BuffTriggerInfo_BeforeCastSkill GetBuffTriggerInfo_BeforeCastSkill(Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill buffTrigger_BeforeCastSkill)
        {
            if (buffTrigger_BeforeCastSkill == null)
            {
                return null;
            }
            BuffTriggerInfo_BeforeCastSkill buffTriggerInfo_BeforeCastSkill = new BuffTriggerInfo_BeforeCastSkill();
            buffTriggerInfo_BeforeCastSkill.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_BeforeCastSkill.entityCheck);
            buffTriggerInfo_BeforeCastSkill.skillCheck = GetBattleObjCheckInfo_Skill(buffTrigger_BeforeCastSkill.skillCheck);
            foreach(var condition in buffTrigger_BeforeCastSkill.conditionList)
            {
                buffTriggerInfo_BeforeCastSkill.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_BeforeCastSkill;
        }
        
        public static BuffTriggerInfo_AfterCastSkill GetBuffTriggerInfo_AfterCastSkill(Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill buffTrigger_AfterCastSkill)
        {
            if (buffTrigger_AfterCastSkill == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterCastSkill buffTriggerInfo_AfterCastSkill = new BuffTriggerInfo_AfterCastSkill();
            buffTriggerInfo_AfterCastSkill.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterCastSkill.entityCheck);
            buffTriggerInfo_AfterCastSkill.skillCheck = GetBattleObjCheckInfo_Skill(buffTrigger_AfterCastSkill.skillCheck);
            foreach(var condition in buffTrigger_AfterCastSkill.conditionList)
            {
                buffTriggerInfo_AfterCastSkill.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterCastSkill;
        }
        
        public static BuffTriggerInfo_BeforeEngageEnd GetBuffTriggerInfo_BeforeEngageEnd(Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd buffTrigger_BeforeEngageEnd)
        {
            if (buffTrigger_BeforeEngageEnd == null)
            {
                return null;
            }
            BuffTriggerInfo_BeforeEngageEnd buffTriggerInfo_BeforeEngageEnd = new BuffTriggerInfo_BeforeEngageEnd();
            buffTriggerInfo_BeforeEngageEnd.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_BeforeEngageEnd.entityCheck);
            buffTriggerInfo_BeforeEngageEnd.skillCheck = GetBattleObjCheckInfo_Skill(buffTrigger_BeforeEngageEnd.skillCheck);
            foreach(var condition in buffTrigger_BeforeEngageEnd.conditionList)
            {
                buffTriggerInfo_BeforeEngageEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_BeforeEngageEnd;
        }
        
        public static BuffTriggerInfo_AfterEngageEnd GetBuffTriggerInfo_AfterEngageEnd(Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd buffTrigger_AfterEngageEnd)
        {
            if (buffTrigger_AfterEngageEnd == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterEngageEnd buffTriggerInfo_AfterEngageEnd = new BuffTriggerInfo_AfterEngageEnd();
            buffTriggerInfo_AfterEngageEnd.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterEngageEnd.entityCheck);
            buffTriggerInfo_AfterEngageEnd.skillCheck = GetBattleObjCheckInfo_Skill(buffTrigger_AfterEngageEnd.skillCheck);
            foreach(var condition in buffTrigger_AfterEngageEnd.conditionList)
            {
                buffTriggerInfo_AfterEngageEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterEngageEnd;
        }
        
        public static BuffTriggerInfo_BeforeActionEnd GetBuffTriggerInfo_BeforeActionEnd(Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd buffTrigger_BeforeActionEnd)
        {
            if (buffTrigger_BeforeActionEnd == null)
            {
                return null;
            }
            BuffTriggerInfo_BeforeActionEnd buffTriggerInfo_BeforeActionEnd = new BuffTriggerInfo_BeforeActionEnd();
            buffTriggerInfo_BeforeActionEnd.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_BeforeActionEnd.entityCheck);
            foreach(var condition in buffTrigger_BeforeActionEnd.conditionList)
            {
                buffTriggerInfo_BeforeActionEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_BeforeActionEnd;
        }
        
        public static BuffTriggerInfo_AfterActionEnd GetBuffTriggerInfo_AfterActionEnd(Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd buffTrigger_AfterActionEnd)
        {
            if (buffTrigger_AfterActionEnd == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterActionEnd buffTriggerInfo_AfterActionEnd = new BuffTriggerInfo_AfterActionEnd();
            buffTriggerInfo_AfterActionEnd.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterActionEnd.entityCheck);
            foreach(var condition in buffTrigger_AfterActionEnd.conditionList)
            {
                buffTriggerInfo_AfterActionEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterActionEnd;
        }
        
        public static BuffTriggerInfo_MainBattleStart GetBuffTriggerInfo_MainBattleStart(Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart buffTrigger_MainBattleStart)
        {
            if (buffTrigger_MainBattleStart == null)
            {
                return null;
            }
            BuffTriggerInfo_MainBattleStart buffTriggerInfo_MainBattleStart = new BuffTriggerInfo_MainBattleStart();
            foreach(var condition in buffTrigger_MainBattleStart.conditionList)
            {
                buffTriggerInfo_MainBattleStart.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_MainBattleStart;
        }
        
        public static BuffTriggerInfo_TurnStart GetBuffTriggerInfo_TurnStart(Phoenix.ConfigData.BuffTriggerConfigData_TurnStart buffTrigger_TurnStart)
        {
            if (buffTrigger_TurnStart == null)
            {
                return null;
            }
            BuffTriggerInfo_TurnStart buffTriggerInfo_TurnStart = new BuffTriggerInfo_TurnStart();
            buffTriggerInfo_TurnStart.compareType = (CompareType)buffTrigger_TurnStart.compareType;
            buffTriggerInfo_TurnStart.turnIndex = buffTrigger_TurnStart.turnIndex;
            foreach(var condition in buffTrigger_TurnStart.conditionList)
            {
                buffTriggerInfo_TurnStart.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_TurnStart;
        }
        
        public static BuffTriggerInfo_TurnEnd GetBuffTriggerInfo_TurnEnd(Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd buffTrigger_TurnEnd)
        {
            if (buffTrigger_TurnEnd == null)
            {
                return null;
            }
            BuffTriggerInfo_TurnEnd buffTriggerInfo_TurnEnd = new BuffTriggerInfo_TurnEnd();
            buffTriggerInfo_TurnEnd.compareType = (CompareType)buffTrigger_TurnEnd.compareType;
            buffTriggerInfo_TurnEnd.turnIndex = buffTrigger_TurnEnd.turnIndex;
            foreach(var condition in buffTrigger_TurnEnd.conditionList)
            {
                buffTriggerInfo_TurnEnd.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_TurnEnd;
        }
        
        public static BuffTriggerInfo_AfterEntityDead GetBuffTriggerInfo_AfterEntityDead(Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead buffTrigger_AfterEntityDead)
        {
            if (buffTrigger_AfterEntityDead == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterEntityDead buffTriggerInfo_AfterEntityDead = new BuffTriggerInfo_AfterEntityDead();
            buffTriggerInfo_AfterEntityDead.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterEntityDead.entityCheck);
            foreach(var condition in buffTrigger_AfterEntityDead.conditionList)
            {
                buffTriggerInfo_AfterEntityDead.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterEntityDead;
        }
        
        public static BuffTriggerInfo_AfterEntityMove GetBuffTriggerInfo_AfterEntityMove(Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove buffTrigger_AfterEntityMove)
        {
            if (buffTrigger_AfterEntityMove == null)
            {
                return null;
            }
            BuffTriggerInfo_AfterEntityMove buffTriggerInfo_AfterEntityMove = new BuffTriggerInfo_AfterEntityMove();
            buffTriggerInfo_AfterEntityMove.entityCheck = GetBattleObjCheckInfo_Entity(buffTrigger_AfterEntityMove.entityCheck);
            foreach(var condition in buffTrigger_AfterEntityMove.conditionList)
            {
                buffTriggerInfo_AfterEntityMove.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffTriggerInfo_AfterEntityMove;
        }
        
        public static BuffEffectInfo_AuraApply GetBuffEffectInfo_AuraApply(Phoenix.ConfigData.BuffEffectConfigData_AuraApply buffEffect_AuraApply)
        {
            if (buffEffect_AuraApply == null)
            {
                return null;
            }
            BuffEffectInfo_AuraApply buffEffectInfo_AuraApply = new BuffEffectInfo_AuraApply();
            buffEffectInfo_AuraApply.rangeId = (TargetSelectRangeId)buffEffect_AuraApply.rangeId;
            buffEffectInfo_AuraApply.filterFuncType = (TargetSelectTargetFilterType)buffEffect_AuraApply.filterFuncType;
            buffEffectInfo_AuraApply.buffRid = buffEffect_AuraApply.buffRid;
            foreach(var condition in buffEffect_AuraApply.conditionList)
            {
                buffEffectInfo_AuraApply.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            return buffEffectInfo_AuraApply;
        }
        
        public static TerrainLogicInfo GetTerrainLogicInfo(Phoenix.ConfigData.TerrainLogicConfigData terrainLogic)
        {
            if (terrainLogic == null)
            {
                return null;
            }
            TerrainLogicInfo terrainLogicInfo = new TerrainLogicInfo();
            terrainLogicInfo.id = terrainLogic.id;
            foreach(var buff in terrainLogic.buffList)
            {
                terrainLogicInfo.buffList.Add(GetTerrainEffectBuffInfo(buff));
            }
            terrainLogicInfo.trigger = GetTerrainEffectTriggerInfo(terrainLogic.trigger);
            terrainLogicInfo.dramaName = terrainLogic.dramaName;
            return terrainLogicInfo;
        }
        
        public static TerrainEffectBuffInfo GetTerrainEffectBuffInfo(Phoenix.ConfigData.TerrainEffectBuffConfigData terrainEffectBuff)
        {
            if (terrainEffectBuff == null)
            {
                return null;
            }
            TerrainEffectBuffInfo terrainEffectBuffInfo = new TerrainEffectBuffInfo();
            foreach(var condition in terrainEffectBuff.conditionList)
            {
                terrainEffectBuffInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            foreach(var buffId in terrainEffectBuff.buffIdList)
            {
                terrainEffectBuffInfo.buffIdList.Add(buffId);
            }
            return terrainEffectBuffInfo;
        }
        
        public static TerrainEffectTriggerInfo GetTerrainEffectTriggerInfo(Phoenix.ConfigData.TerrainEffectTriggerConfigData terrainEffectTrigger)
        {
            if (terrainEffectTrigger == null)
            {
                return null;
            }
            TerrainEffectTriggerInfo terrainEffectTriggerInfo = new TerrainEffectTriggerInfo();
            foreach(var condition in terrainEffectTrigger.conditionList)
            {
                terrainEffectTriggerInfo.conditionList.Add(GetBattleArgumentInfo_Condition(condition));
            }
            terrainEffectTriggerInfo.momentType = (TerrainTriggerMomentType)terrainEffectTrigger.momentType;
            foreach(var effect in terrainEffectTrigger.effectList)
            {
                terrainEffectTriggerInfo.effectList.Add(GetSkillEffectInfo(effect));
            }
            return terrainEffectTriggerInfo;
        }
        
    }
}
