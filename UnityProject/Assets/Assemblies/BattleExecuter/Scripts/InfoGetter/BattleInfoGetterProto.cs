using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleInfoGetterProto : BattleInfoGetterBase
    {
        private Dictionary<string, BattleGroundStaticData> m_battleGroundDataMap = new Dictionary<string, BattleGroundStaticData>();

        public void ReadAllFormFolderByFile(string folderPath)
        {
            ReadAllFormFolder(folderPath, (path) =>
            {
                using (FileStream fs = File.Open(path, FileMode.Open))
                {
                    byte[] buffer = new byte[fs.Length];
                    fs.Read(buffer, 0, buffer.Length);
                    return buffer;
                }
            });
        }

        public void ReadAllFormFolder(string folderPath, Func<string, byte[]> funcOnReadBuffer)
        {
            ReadMap(folderPath, "BattleGround", funcOnReadBuffer, m_battleGroundDataMap, BattleGroundStaticData.Parser.ParseFrom, ReadStringKey);
        }

        private string ReadStringKey(Google.Protobuf.CodedInputStream stream)
        {
            return stream.ReadString();
        }

        private int ReadIntKey(Google.Protobuf.CodedInputStream stream)
        {
            return stream.ReadInt32();
        }

        private void ReadMap<TKey, TValue>(string folderPath, string name, Func<string, byte[]> funcOnReadBuffer, Dictionary<TKey, TValue> map, Func<Google.Protobuf.CodedInputStream, TValue> fucnOnParse, Func<Google.Protobuf.CodedInputStream, TKey> funcOnReadKey)
            where TValue : Google.Protobuf.IMessage
        {
            string path = Path.Combine(folderPath, name);
            byte[] buffer = funcOnReadBuffer(path);

            using (var memoryStream = new MemoryStream(buffer, 0, buffer.Length))
            {
                using (var inputStream = new Google.Protobuf.CodedInputStream(memoryStream))
                {
                    int capacity = inputStream.ReadInt32();
                    for (int i = 0; i < capacity; ++i)
                    {
                        var key = funcOnReadKey(inputStream);

                        var dataBytes = inputStream.ReadBytes();
                        int length = dataBytes.Length;

                        using (var inputStream2 = new Google.Protobuf.CodedInputStream(dataBytes.ToByteArray(), 0, length))
                        {
                            var data = fucnOnParse(inputStream2);
                            map.Add(key, data);
                        }
                    }
                }
            }
        }

        protected override BattleGroundStaticData GetGroundStaticData(string path)
        {
            BattleGroundStaticData data;
            m_battleGroundDataMap.TryGetValue(path, out data);
            return data;
        }

        protected override ActorConfigDataAnalyzer GetActorInfoInternal_Player(ulong playerId, int actorRid)
        {
            return null;
        }

        protected override TacticianConfigDataAnalyzer GetTacticianInfoInternal_Player(ulong playerId, int tacticianRid)
        {
            return null;
        }

        protected override BattleTeamDecisionInfo GetTeamDecisionInfoInternal_Player(ulong playerId, int id)
        {
            return null;
        }
    }
}
