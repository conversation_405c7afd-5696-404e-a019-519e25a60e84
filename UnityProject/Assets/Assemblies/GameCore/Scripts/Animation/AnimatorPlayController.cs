using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core
{
    public class AnimatorPlayController
    {
        private Animator m_animator; 
        private AnimatorOverrideController m_animOverCtrl;
        private AnimatorOverrideController m_initAnimOverCtrl;
        private List<Layer> m_layerList = new List<Layer>();
        private IAnimationClipGetter m_clipGetter;
        private List<string> m_overrideKeyList = new List<string>();
        private List<AnimatorPlayCommand> m_commandList = new List<AnimatorPlayCommand>();
        private List<Action<bool>> m_interruptInvokeList = new List<Action<bool>>();
        private bool isHandling;

        public Vector3 rootMotionDeltaPos
        {
            get { return m_animator != null ? m_animator.deltaPosition : Vector3.zero; }
        }

        public Quaternion rootMotionTargetRotation
        {
            get { return m_animator != null ? m_animator.targetRotation : Quaternion.identity; }
        }

        public void SetAnimator(Animator animator)
        {
            m_animator = animator;
            m_animator.keepAnimatorStateOnDisable = true;
            if (m_animOverCtrl == null)
            {
                if(m_animator.runtimeAnimatorController == null)
                {
                    Debug.LogError(m_animator.name + " 没有AnimatorController");
                    return;
                }
                var overrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                AnimatorOverrideController initOverCtrl = m_animator.runtimeAnimatorController as AnimatorOverrideController;
                if (initOverCtrl == null)
                {
                    m_initAnimOverCtrl = new AnimatorOverrideController(m_animator.runtimeAnimatorController);
                    m_animOverCtrl = m_initAnimOverCtrl;
                    m_initAnimOverCtrl.GetOverrides(overrides);
                }
                else
                {
                    initOverCtrl.GetOverrides(overrides);
                    m_animOverCtrl = new AnimatorOverrideController(initOverCtrl.runtimeAnimatorController);
                    foreach (var kv in overrides)
                    {
                        m_animOverCtrl[kv.Key.name] = kv.Value;
                    }
                }
                m_overrideKeyList.Clear();
                foreach (var kv in overrides)
                {
                    m_overrideKeyList.Add(kv.Key.name);
                }
            }
            else
            {

            }
            if (m_animator != null && m_animator.runtimeAnimatorController != m_animOverCtrl)
            {
                m_animator.runtimeAnimatorController = m_animOverCtrl;
            }
        }

        public void AddStateGroup(int layerIndex, AnimatorStateType stateType, params string[] stateNames)
        {
            Layer layer = GetOrCreateLayer(layerIndex);
            StateGroup stateGroup = layer.GetOrCreateStateGroup(stateType);
            if (stateNames != null && stateNames.Length > 0)
            {
                for (int i = 0; i < stateNames.Length; ++i)
                {
                    stateGroup.stateNameList.AddNotContainsNotNull(stateNames[i]);
                }
            }
        }

        public void SetAnimationClipGetter(IAnimationClipGetter clipGetter)
        {
            m_clipGetter = clipGetter;
        }

        public AnimationClip GetClip(string name)
        {
            return m_clipGetter.GetClip(name);
        }

        public bool CanPlay(string name)
        {
            return m_clipGetter.GetClip(name) != null;
        }

        public void SetSpeed(float speed)
        {
            m_animator.speed = speed;
        }

        public void Play(params AnimatorPlayCommand[] commands)
        {
            if (isHandling)
            {
                Debug.LogError("isHandling");
                return;
            }
            isHandling = true;
            for (int i = 0; i < m_layerList.Count; ++i)
            {
                AnimatorPlayCommand command = m_layerList[i].command;
                m_layerList[i].ResetPlayState();
                command.ReadyInvokeEndIfAlwaysCall(m_interruptInvokeList);
            }
            for (int i = 0; i < m_commandList.Count; ++i)
            {
                m_commandList[i].ReadyInvokeEndIfAlwaysCall(m_interruptInvokeList);
            }
            m_commandList.Clear();
            if (m_interruptInvokeList.Count > 0)
            {
                List<Action<bool>> tempList = ClassPoolManager.instance.Fetch<List<Action<bool>>>();
                tempList.AddRange(m_interruptInvokeList);
                m_interruptInvokeList.Clear();
                foreach (var action in tempList)
                {
                    action.Invoke(true);
                }
                tempList.Release();
            }
            AppendPlayCommandInternal(commands);
            if (m_interruptInvokeList.Count > 0)
            {
                List<Action<bool>> tempList = ClassPoolManager.instance.Fetch<List<Action<bool>>>();
                tempList.AddRange(m_interruptInvokeList);
                m_interruptInvokeList.Clear();
                foreach (var action in tempList)
                {
                    action.Invoke(true);
                }
                tempList.Release();
            }
            isHandling = false;
        }

        public void AppendPlayCommand(params AnimatorPlayCommand[] commands)
        {
            AppendPlayCommandInternal(commands);
        }

        public void Tick(TimeSlice timeSlice)
        {
            for (int i = 0; i < m_layerList.Count; ++i)
            {
                m_layerList[i].Tick(timeSlice);
            }
        }

        private void AppendPlayCommandInternal(AnimatorPlayCommand[] commands)
        {
            if (commands == null || commands.Length == 0)
            {
                return;
            }
            for (int i = 0; i < commands.Length; ++i)
            {
                m_commandList.Add(commands[i]);
            }
            HandleNextCommand();
        }

        private void HandleNextCommand()
        {
            for (int i = 0; i < m_layerList.Count; ++i)
            {
                Layer layer = m_layerList[i];
                bool hasOnceCommand = HasOnceCommand(m_commandList, layer.index);
                if (layer.isPlaying)
                {
                    if (!layer.isCurStateLoop || !hasOnceCommand)
                    {
                        continue;
                    }
                }
                int index = 0;
                for (int j = 0; j < m_commandList.Count; ++j)
                {
                    AnimatorPlayCommand command = m_commandList[index];
                    if (command.layerIndex != layer.index)
                    {
                        index++;
                        continue;
                    }
                    m_commandList.RemoveAt(index);
                    if (AnimatorPlayUtility.CheckLoop(command.stateType) && hasOnceCommand)
                    {
                        continue;
                    }
                    if (PlayAnimationInternal(command))
                    {
                        break;
                    }
                }
            }
        }

        private bool HasOnceCommand(List<AnimatorPlayCommand> commandList, int layerIndex)
        {
            for (int i = 0; i < commandList.Count; ++i)
            {
                AnimatorPlayCommand command = commandList[i];
                if (command.layerIndex != layerIndex)
                {
                    continue;
                }
                if (!AnimatorPlayUtility.CheckLoop(command.stateType))
                {
                    return true;
                }
            }
            return false;
        }

        private bool PlayAnimationInternal(AnimatorPlayCommand command)
        {
            int layerIndex = command.layerIndex;
            AnimatorStateType stateType = command.stateType;
            float fadeTime = command.fadeTime;
            string clipName = command.name;
            Layer layer = GetLayer(layerIndex);
            if (layer == null)
            {
                Debug.LogError(string.Format("[{0}] Layer:{1} is not exist", typeof(AnimatorPlayController).Name, layerIndex.ToString()));
                command.ReadyInvokeEndIfAlwaysCall(m_interruptInvokeList);
                return false;
            }
            StateGroup stateGroup = layer.GetStateGroup(stateType);
            if (stateGroup == null)
            {
                Debug.LogError(string.Format("[{0}] StateGroup:{1} Layer:{2} is not exist", typeof(AnimatorPlayController).Name, stateType.ToString(), layerIndex.ToString()));
                command.ReadyInvokeEndIfAlwaysCall(m_interruptInvokeList);
                return false;
            }
            string stateName = stateGroup.TickAndGetNextStateName();
            if (!ReplaceOverrideClip(stateName, clipName))
            {
                command.ReadyInvokeEndIfAlwaysCall(m_interruptInvokeList);
                return false;
            }
            if (m_animator != null)
            {
                m_animator.CrossFadeInFixedTime(stateName, fadeTime, layerIndex);
            }
            float clipLength = GetClip(clipName).length;
            if (m_animator.speed > 0)
            {
                clipLength /= m_animator.speed;
            }
            layer.StartPlay(stateGroup, clipLength, command);
            return true;
        }

        private bool ReplaceOverrideClip(string keyName, string clipName)
        {
            if (!CanReplaceClip(keyName, clipName))
            {
                Debug.LogError(string.Format("[{0}] {1} Cannot replace {2} to {3}", typeof(AnimatorPlayController).Name, m_animator.name, keyName, clipName));
                return false;
            }
            m_animOverCtrl[keyName] = GetClip(clipName);
            return true;
        }

        public bool CanReplaceClip(string keyName, string clipName)
        {
            if (!m_overrideKeyList.Contains(keyName))
            {
                return false;
            }
            if (GetClip(clipName) == null)
            {
                return false;
            }
            return true;
        }

        private Layer GetLayer(int layerIndex)
        {
            for (int i = 0; i < m_layerList.Count; ++i)
            {
                if (m_layerList[i].index == layerIndex)
                {
                    return m_layerList[i];
                }
            }
            return null;
        }

        private Layer GetOrCreateLayer(int layerIndex)
        {
            Layer layer = GetLayer(layerIndex);
            if (layer == null)
            {
                layer = new Layer();
                layer.index = layerIndex;
                layer.actionOnPlayEnd = OnLayerPlayStateEnd;
                m_layerList.Add(layer);
            }
            return layer;
        }

        private StateGroup GetStateGroup(int layerIndex, AnimatorStateType stateType)
        {
            Layer layer = GetLayer(layerIndex);
            if (layer != null)
            {
                return layer.GetStateGroup(stateType);
            }
            return null;
        }

        private void OnLayerPlayStateEnd(Layer layer)
        {
            HandleNextCommand();
        }

        private class Layer
        {
            public int index;
            public Action<Layer> actionOnPlayEnd;

            private List<StateGroup> m_stateGroupList = new List<StateGroup>();
            private Timer m_stateTimer = new Timer();
            private Timer m_transitionTimer = new Timer();
            private StateGroup m_curStateGroup;
            private bool m_isPlaying;
            private AnimatorPlayCommand m_command;

            public bool isPlaying
            {
                get { return m_isPlaying; }
            }

            public AnimatorPlayCommand command
            {
                get { return m_command; }
            }

            public StateGroup curStateGroup
            {
                get { return m_curStateGroup; }
            }

            public bool isCurStateLoop
            {
                get { return m_curStateGroup != null && m_curStateGroup.isLoop; }
            }

            public StateGroup GetStateGroup(AnimatorStateType stateType)
            {
                for (int i = 0; i < m_stateGroupList.Count; ++i)
                {
                    if (m_stateGroupList[i].stateType == stateType)
                    {
                        return m_stateGroupList[i];
                    }
                }
                return null;
            }

            public StateGroup GetOrCreateStateGroup(AnimatorStateType stateType)
            {
                StateGroup stateGroup = GetStateGroup(stateType);
                if (stateGroup == null)
                {
                    stateGroup = new StateGroup();
                    stateGroup.stateType = stateType;
                    m_stateGroupList.Add(stateGroup);
                }
                return stateGroup;
            }

            public void StartPlay(StateGroup stateGroup, float time, AnimatorPlayCommand command)
            {
                m_isPlaying = true;
                m_curStateGroup = stateGroup;
                m_command = command;
                if (m_curStateGroup.stateType == AnimatorStateType.Once)
                {
                    m_stateTimer.Start(time, OnOnceTimerEnd);
                }
            }

            public void ResetPlayState()
            {
                m_isPlaying = default;
                m_curStateGroup = default;
                m_command = default;
                m_stateTimer.Reset();
                m_transitionTimer.Reset();
            }

            public void Tick(TimeSlice timeSlice)
            {
                m_stateTimer.Tick(timeSlice);
                m_transitionTimer.Tick(timeSlice);
            }

            private void OnOnceTimerEnd()
            {
                m_isPlaying = false;
                Action<bool> temp = m_command.actionOnEnd;
                m_command = default;
                if (temp != null)
                {
                    temp(false);
                }
                if (actionOnPlayEnd != null)
                {
                    actionOnPlayEnd(this);
                }
            }
        }

        private class StateGroup
        {
            public AnimatorStateType stateType;
            public int index;
            public List<string> stateNameList = new List<string>();
            public float m_curTime;
            public float m_targetTime;
            public Action actionOnEnd;

            public bool isLoop
            {
                get { return stateType == AnimatorStateType.Loop; }
            }

            public string GetCurStateName()
            {
                return stateNameList.GetValueSafely(index);
            }

            public string TickAndGetNextStateName()
            {
                index++;
                if (index >= stateNameList.Count)
                {
                    index = 0;
                }
                return stateNameList.GetValueSafely(index);
            }

            public void Tick(float deltaTime)
            {
                if (stateType == AnimatorStateType.Once)
                {
                    m_curTime += deltaTime;
                    if (m_curTime > m_targetTime)
                    {
                        if (actionOnEnd != null)
                        {
                            Action temp = actionOnEnd;
                            actionOnEnd = null;
                            temp();
                        }
                    }
                }
            }
        }
    }
}
