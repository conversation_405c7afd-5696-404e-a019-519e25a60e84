using System;
using System.Collections.Generic;

namespace Phoenix.Core
{
    public class RuntimeSceneUnloadOperation : RuntimeSceneOperation
    {
        public Action<RuntimeSceneOperation, RuntimeSceneIdentify> actionOnSceneUnLoadEnd;

        private List<UnloadContext> m_contextList = new List<UnloadContext>();
        private State m_state;
        private List<RuntimeSceneUnloader> m_unloaderList = new List<RuntimeSceneUnloader>();
        private List<RuntimeSceneUnloader> m_tempUnloaderList = new List<RuntimeSceneUnloader>();

        public void Init(RuntimeScene scene, Action<bool> actionOnUnloadEnd)
        {
            UnloadContext context = new UnloadContext();
            context.scene = scene;
            context.actionOnUnloadEnd = actionOnUnloadEnd;
            m_contextList.Add(context);
        }

        public bool AddUnloadScene(RuntimeScene scene, Action<bool> actionOnUnloadEnd)
        {
            if (m_isDone)
            {
                return false;
            }
            UnloadContext context = new UnloadContext();
            context.scene = scene;
            context.actionOnUnloadEnd = actionOnUnloadEnd;
            m_contextList.Add(context);
            return true;
        }

        protected override void OnStart()
        {
            m_state = State.Start;
        }

        protected override void OnTick()
        {
            base.OnTick();
            TickState();
            TickUnloaderList();
        }

        private void TickUnloaderList()
        {
            m_tempUnloaderList.AddRange(m_unloaderList);
            foreach (var unloader in m_tempUnloaderList)
            {
                unloader.Tick();
            }
            m_tempUnloaderList.Clear();
        }

        private void TickState()
        {
            switch (m_state)
            {
                case State.Start:
                    foreach (var context in m_contextList)
                    {
                        m_unloaderList.Add(StartUnload(context.scene, OnSceneUnloadEnd));
                    }
                    m_state = State.WaitCallback;
                    break;
                case State.End:
                    m_isDone = true;
                    break;
            }
        }

        private void OnSceneUnloadEnd(RuntimeSceneUnloader unloader)
        {
            actionOnSceneUnLoadEnd.InvokeSafely(this, unloader.identify);
            for (int i = 0; i < m_contextList.Count; ++i)
            {
                if (m_contextList[i].scene.identify == unloader.identify)
                {
                    m_contextList[i].actionOnUnloadEnd.InvokeSafely(unloader.success);
                }
            }
            unloader.Release();
            m_unloaderList.Remove(unloader);
            if (m_unloaderList.Count == 0)
            {
                m_state = State.End;
                TickState();
            }
        }

        private struct UnloadContext
        {
            public RuntimeScene scene;
            public Action<bool> actionOnUnloadEnd;
        }

        private enum State
        {
            None,
            Start,
            WaitCallback,
            End,
        }
    }
}
