using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using YooAsset;

namespace Phoenix.Core
{
    public class RuntimeScene : ClassPoolObj
    {
        protected RuntimeSceneIdentify m_identify;
        protected Scene m_unityScene;

        public SceneHandle sceneHandle;

        public RuntimeSceneIdentify identify
        {
            get { return m_identify; }
        }

        public Scene unityScene
        {
            get { return m_unityScene; }
        }

        public override void OnRelease()
        {
            sceneHandle = default;
            m_identify = default;
            m_unityScene = default;
            base.OnRelease();
        }

        public void Init(RuntimeSceneIdentify identify, Scene scene)
        {
            m_identify = identify;
            m_unityScene = scene;
            OnInit();
        }

        public void UnInit()
        {
            OnUnInit();
        }

        protected virtual void OnInit() { }
        protected virtual void OnUnInit() { }
    }
}
