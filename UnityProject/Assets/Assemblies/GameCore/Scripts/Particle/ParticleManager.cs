using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Phoenix.Core;
using YooAsset;

namespace Phoenix.GameLogic
{
    public class ParticleManager : Singleton<ParticleManager>
    {
        private List<ParticleRuntimeInfo> m_runtimeInfoList = new List<ParticleRuntimeInfo>();
        private static readonly List<ParticleSystem> m_particleSystemList = new List<ParticleSystem>();

        protected override void OnInit()
        {
            base.OnInit();
            TickManager.instance.RegisterGlobalTick(OnTick);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            TickManager.instance.UnRegisterGlobalTick(OnTick);
        }

        public void Clear()
        {
            foreach (var info in m_runtimeInfoList)
            {
                ResourceHandleManager.instance.DespawnGameObject(info.go);
                info.Release();
            }
            m_runtimeInfoList.Clear();
        }


        public ParticleRuntimeInfo Spawn(string path, Action<ParticleRuntimeInfo> actionOnEnd = null)
        {
            return Spawn(path, null, false, false, 1f, actionOnEnd);
        }

        public ParticleRuntimeInfo Spawn(string path, GameObject parent, bool isFollow, bool useAttachScale, float extraScale, Action<ParticleRuntimeInfo> actionOnEnd = null)
        {
            if (string.IsNullOrEmpty(path))
            {
                return null;
            }
            AssetHandle handle = ResourceManager.instance.LoadSync<GameObject>(path);
            if (handle == null)
            {
                Debug.LogError(string.Format("[ParticleManager]��Ч·������{0}", path));
                return null;
            }
            GameObject prefab = handle.AssetObject as GameObject;
            if (prefab == null)
            {
                Debug.LogError(string.Format("[ParticleManager]��Ч·������{0}", path));
                return null;
            }
            bool isLoop = false;
            float lifeTime = 0f;
            ParticleConfig config = prefab.GetComponent<ParticleConfig>();
            if (config != null)
            {
                isLoop = config.isLoop;
                lifeTime = config.lifeTime;
            }
            else
            {
                ParticleSystem firstPs = null;
                for (int i = 0; i < prefab.transform.childCount; ++i)
                {
                    firstPs = prefab.transform.GetChild(i).GetComponent<ParticleSystem>();
                    if (firstPs != null)
                    {
                        break;
                    }
                }
                if (firstPs != null)
                {
                    isLoop = firstPs.main.loop;
                    lifeTime = firstPs.main.startDelayMultiplier + firstPs.main.duration;
                }
                else
                {
                    prefab.GetComponentsInChildren(m_particleSystemList);
                    isLoop = false;
                    foreach(var ps in m_particleSystemList)
                    {
                        lifeTime = Mathf.Max(lifeTime, ps.main.startDelayMultiplier + ps.main.duration);
                    }
                    m_particleSystemList.Clear();
                }
            }
            GameObject particleObj = ResourceHandleManager.instance.SpawnGameObject(path, isFollow ? parent : null);
            if (particleObj != null && parent != null)
            {
                if (isFollow)
                {
                    particleObj.transform.localPosition = Vector3.zero;
                    particleObj.transform.localEulerAngles = Vector3.zero;
                    if (useAttachScale)
                    {
                        particleObj.transform.localScale = Vector3.one * extraScale;
                    }
                    else
                    {
                        var lossyScale = parent.transform.lossyScale;
                        particleObj.transform.localScale = new Vector3(1 / lossyScale.x, 1 / lossyScale.y, 1 / lossyScale.y) * extraScale;
                    }
                }
                else
                {
                    particleObj.transform.position = parent.transform.position;
                    particleObj.transform.rotation = parent.transform.rotation;
                    if (useAttachScale)
                    {
                        particleObj.transform.localScale = parent.transform.lossyScale * extraScale;
                    }
                    else
                    {
                        particleObj.transform.localScale = Vector3.one * extraScale;
                    }
                }
            }
            /*
            ParticleAudioConfig audioConfig = particleObj.GetComponent<ParticleAudioConfig>();
            if (audioConfig != null)
            {
                AudioManager.instance.PlayOnce(audioConfig.path);
            }
            */
            ParticleRuntimeInfo runtimeInfo = ClassPoolManager.instance.Fetch<ParticleRuntimeInfo>();
            runtimeInfo.go = particleObj;
            runtimeInfo.isLoop = isLoop;
            runtimeInfo.lifeTime = lifeTime;
            runtimeInfo.actionOnEnd = actionOnEnd;
            m_runtimeInfoList.Add(runtimeInfo);
            return runtimeInfo;
        }

        public void DeSpawn(GameObject go)
        {
            if (go == null)
            {
                return;
            }
            ParticleRuntimeInfo runtimeInfo = null;
            for (int i = 0; i < m_runtimeInfoList.Count; i++)
            {
                var info = m_runtimeInfoList[i];
                if (info.go == go)
                {
                    runtimeInfo = info;
                    break;
                }
            }
            if(runtimeInfo == null)
            {
                return;
            }
            m_runtimeInfoList.Remove(runtimeInfo);
            runtimeInfo.actionOnEnd.InvokeSafely(runtimeInfo);
            ResourceHandleManager.instance.DespawnGameObject(runtimeInfo.go);
            runtimeInfo.Release();
        }

        private void OnTick(TimeSlice timeSlice)
        {
            for (int i = 0; i < m_runtimeInfoList.Count;)
            {
                var info = m_runtimeInfoList[i];
                info.Tick(timeSlice);
                if (info.isOver)
                {
                    m_runtimeInfoList.RemoveAt(i);
                    info.actionOnEnd.InvokeSafely(info);
                    ResourceHandleManager.instance.DespawnGameObject(info.go);
                    info.Release();
                    continue;
                }
                i++;
            }
        }
    }
}
