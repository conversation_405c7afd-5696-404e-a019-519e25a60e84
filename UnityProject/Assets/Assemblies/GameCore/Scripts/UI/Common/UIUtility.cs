using UnityEngine;
using UnityEngine.UI;

namespace Phoenix.Core
{
    public static class UIUtility
    {
        public static string GetTimeString(int sec, bool seperateDay = true, int exactShowCount = 0)
        {
            if (sec < 0)
            {
                sec = 0;
            }
            int minute = sec / 60;
            int hour = minute / 60;
            minute = minute % 60;
            sec = sec % 60;
            if (seperateDay && hour > 24)
            {
                return string.Format("{0}天", hour / 24);
            }
            if (hour > 0 || exactShowCount >= 3)
            {
                return string.Format("{0:D2}:{1:D2}:{2:D2}", hour, minute, sec);
            }
            if (minute > 0 || exactShowCount >= 2)
            {

                return string.Format("{0:D2}:{1:D2}", minute, sec);
            }
            if (sec > 0 || exactShowCount >= 1)
            {
                return string.Format("{0:D2}", sec);
            }
            return string.Empty;
        }

        public static void RefreshTipPos(RectTransform tipTrans, Camera tipCamera, RectTransform tarObjTrans, Camera tarObjCamera, float margin, bool dependTopBottomEdge, bool alignTop, bool alignRight, float borderDelta = 0.0f)
        {
            if (tipTrans == null) return;

            if (tarObjTrans == null)
            {
                tipTrans.localPosition = Vector3.zero;
                return;
            }

            tipTrans.pivot = new Vector2(0.5f, 0.5f);
            LayoutRebuilder.ForceRebuildLayoutImmediate(tipTrans);
            Vector2 tarObjScreenMin = Vector2.zero;
            Vector2 tarObjScreenMax = Vector2.zero;
            GetScreenMinMax(tarObjTrans, tarObjCamera, out tarObjScreenMin, out tarObjScreenMax);
            Vector2 tarObjAnchoredMin = Vector2.zero;
            Vector2 tarObjAnchoredMax = Vector2.zero;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(tipTrans.parent as RectTransform, tarObjScreenMin, tipCamera, out tarObjAnchoredMin);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(tipTrans.parent as RectTransform, tarObjScreenMax, tipCamera, out tarObjAnchoredMax);

            Vector2 tipAnchoredPos = Vector3.zero;
            Vector2 tipSize = tipTrans.rect.size;
            Vector2 tipHalfSize = tipSize / 2f;
            Vector2 tipParentSize = (tipTrans.parent as RectTransform).rect.size;
            Vector2 tipParentHalfSize = tipParentSize / 2f;
            tipParentHalfSize.x -= borderDelta;
            tipParentHalfSize.y -= borderDelta;
            if (dependTopBottomEdge)
            {
                if (alignTop)
                {
                    if (tarObjAnchoredMax.y + margin + tipSize.y > tipParentHalfSize.y && tarObjAnchoredMin.y - margin - tipSize.y > -tipParentHalfSize.y)
                    {
                        alignTop = false;
                    }
                }
                else
                {
                    if (tarObjAnchoredMin.y - margin - tipSize.y < -tipParentHalfSize.y && tarObjAnchoredMax.y + margin + tipSize.y < tipParentHalfSize.y)
                    {
                        alignTop = true;
                    }
                }
                if (alignTop)
                {
                    tipAnchoredPos.y = Mathf.Min(tarObjAnchoredMax.y + margin + tipHalfSize.y, tipParentHalfSize.y - tipHalfSize.y);
                }
                else
                {
                    tipAnchoredPos.y = Mathf.Max(tarObjAnchoredMin.y - margin - tipHalfSize.y, -tipParentHalfSize.y + tipHalfSize.y);
                }
                if (alignRight)
                {
                    tipAnchoredPos.x = Mathf.Max(tarObjAnchoredMax.x - tipHalfSize.x, -tipParentHalfSize.x + tipHalfSize.x);
                }
                else
                {
                    tipAnchoredPos.x = Mathf.Min(tarObjAnchoredMin.x + tipHalfSize.x, tipParentHalfSize.x - tipHalfSize.x);
                }
            }
            else
            {

                if (alignRight)
                {
                    if (tarObjAnchoredMax.x + margin + tipSize.x > tipParentHalfSize.x && tarObjAnchoredMin.x - margin - tipSize.x > -tipParentHalfSize.x)
                    {
                        alignRight = false;
                    }
                }
                else
                {
                    if (tarObjAnchoredMin.x - margin - tipSize.x < -tipParentHalfSize.x && tarObjAnchoredMax.x + margin + tipSize.x < tipParentHalfSize.x)
                    {
                        alignRight = true;
                    }
                }
                if (alignRight)
                {
                    tipAnchoredPos.x = Mathf.Min(tarObjAnchoredMax.x + margin + tipHalfSize.x, tipParentHalfSize.x - tipHalfSize.x);
                }
                else
                {
                    tipAnchoredPos.x = Mathf.Max(tarObjAnchoredMin.x - margin - tipHalfSize.x, -tipParentHalfSize.x + tipHalfSize.x);
                }

                if (alignTop && tarObjAnchoredMax.y - margin - tipSize.y < -tipParentHalfSize.y &&
                    tarObjAnchoredMin.y + margin + tipSize.y < tipParentHalfSize.y)
                {
                    alignTop = false;
                }
                else if (!alignTop && tarObjAnchoredMin.y + margin + tipSize.y > tipParentHalfSize.y &&
                         tarObjAnchoredMax.y - margin - tipSize.y > -tipParentHalfSize.y)
                {
                    alignTop = true;
                }
                if (alignTop)
                {
                    tipAnchoredPos.y = Mathf.Max(tarObjAnchoredMax.y - margin - tipHalfSize.y, -tipParentHalfSize.y + tipHalfSize.y);
                    tipAnchoredPos.y = Mathf.Min(tipAnchoredPos.y, tipParentHalfSize.y - tipHalfSize.y);
                }
                else
                {
                    tipAnchoredPos.y = Mathf.Min(tarObjAnchoredMin.y + margin + tipHalfSize.y, tipParentHalfSize.y - tipHalfSize.y);
                    tipAnchoredPos.y = Mathf.Max(tipAnchoredPos.y, -tipParentHalfSize.y + tipHalfSize.y);
                }
            }
            var anchorPoint = (tipTrans.anchorMin + tipTrans.anchorMax) / 2;
            tipAnchoredPos.x = (float)(tipAnchoredPos.x - (anchorPoint.x - 0.5) * tipParentSize.x);
            tipAnchoredPos.y = (float)(tipAnchoredPos.y - (anchorPoint.y - 0.5) * tipParentSize.y);
            tipTrans.anchoredPosition = tipAnchoredPos;
        }

        private static Vector3[] m_corners = new Vector3[4];

        public static void GetScreenMinMax(RectTransform rTrans, Camera camera, out Vector2 min, out Vector2 max)
        {
            rTrans.GetWorldCorners(m_corners);
            min = new Vector2(float.MaxValue, float.MaxValue);
            max = new Vector2(float.MinValue, float.MinValue);
            for (int i = 0; i < m_corners.Length; ++i)
            {
                Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(camera, m_corners[i]);
                min = Vector2.Min(screenPoint, min);
                max = Vector2.Max(screenPoint, max);
            }
        }
    }
}
