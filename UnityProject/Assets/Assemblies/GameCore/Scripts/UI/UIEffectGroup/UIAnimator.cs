
using System;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace Phoenix.Core.UIEffect
{
    [Serializable]
    public class UIAnimator
    {
        public Animator m_animator;
        public AnimationClip m_AnimationClip;


        private String m_groupName;
        private Action<String> m_onPlayEnd;
        private Boolean m_isPlaying;
        private Boolean m_editorSimulate;
        private Single m_duration;
        private Single m_time;
        private Boolean m_isLooping;


        private PlayableGraph m_playableGraph;

        private GameObject Go => m_animator.gameObject;

        public void OnTick(float dt)
        {
            if (m_isPlaying == false)
            {
                return;
            }
            m_time += dt;
            if (m_editorSimulate)
            {
                m_AnimationClip.SampleAnimation(Go, Mathf.Clamp(m_time, 0, m_duration));
            }

            if (m_time > m_duration)
            {
                OnPlayFinishInternal();
                if (m_isLooping)
                {
                    m_time -= m_duration;
                }
            }
        }

        public void PlayAnimation(Action<String> onEnd, UpdateMode updateMode, String groupName, Boolean immediately)
        {
            if (m_animator == null)
            {
                Debug.LogError($"UIAnimator  PlayAnimation Failed: m_animator = null,  GroupName:{groupName}.");
                onEnd?.Invoke(groupName);
                return;
            }
            if (m_AnimationClip == null)
            {
                Debug.LogError($"UIAnimator  PlayAnimation Failed: AnimationClip = null,  GroupName:{groupName}.");
                onEnd?.Invoke(groupName);
                return;
            }

            m_isLooping = m_AnimationClip.isLooping;
            m_duration = m_AnimationClip.length;
            m_time = 0;
            if (immediately)
            {
                if (Application.isPlaying)
                {
                    if (m_isLooping)
                    {
                        m_isPlaying = true;
                    }
                    m_playableGraph = CreatePlayableGraph(updateMode);
                    m_playableGraph.Evaluate(m_duration);
                }
                else
                {
                    m_AnimationClip.SampleAnimation(Go, m_duration);
                }
                onEnd?.Invoke(groupName);
            }
            else
            {
                m_onPlayEnd = onEnd;
                m_groupName = groupName;
                if (Application.isPlaying)
                {
                    m_playableGraph = CreatePlayableGraph(updateMode);
                    m_playableGraph.Play();
                    m_editorSimulate = false;
                }
                else
                {
                    m_AnimationClip.SampleAnimation(Go, m_time);
                    m_editorSimulate = true;
                }
                m_isPlaying = true;
            }
        }

        /// <summary> �ֶ�������ǰ���ڲ��ŵĶ���״̬�����ᴥ��OnPlayEnd��Callback </summary>
        public void StopPlayAnimation()
        {
            if (m_isPlaying)
            {
                m_isPlaying = false;
                if (m_playableGraph.IsValid())
                {
                    m_playableGraph.Destroy();
                }
                m_onPlayEnd = null;
                m_duration = 0;
                m_time = 0;
            }
        }

        private void OnPlayFinishInternal()
        {
            if (m_isPlaying)
            {
                if (m_isLooping == false)
                {
                    if (m_playableGraph.IsValid())
                    {
                        m_playableGraph.Destroy();
                    }
                    m_isPlaying = false;
                }
                Action<String> onEnd = m_onPlayEnd;
                m_onPlayEnd = null;
                onEnd?.Invoke(m_groupName);
            }
        }


        private PlayableGraph CreatePlayableGraph(UpdateMode updateMode)
        {
            PlayableGraph graph = PlayableGraph.Create();
            AnimationPlayableOutput output = AnimationPlayableOutput.Create(graph, m_AnimationClip.name, m_animator);
            AnimationClipPlayable animationClipPlayable = AnimationClipPlayable.Create(graph, m_AnimationClip);
            output.SetSourcePlayable(animationClipPlayable);
            graph.SetTimeUpdateMode(GetDirectorUpdateMode(updateMode));
            return graph;
        }

        private DirectorUpdateMode GetDirectorUpdateMode(UpdateMode updateMode)
        {
            DirectorUpdateMode directorUpdateMode = DirectorUpdateMode.GameTime;
            switch (updateMode)
            {
                case UpdateMode.Normal:
                    directorUpdateMode = DirectorUpdateMode.GameTime;
                    break;
                case UpdateMode.UnscaledTime:
                    directorUpdateMode = DirectorUpdateMode.UnscaledGameTime;
                    break;
            }
            return directorUpdateMode;
        }

    }
}
