using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Phoenix.Core.UIEffect
{
    [Serializable]
    public class UIColor
    {
        public List<Graphic> m_GraphicList = new List<Graphic>();
        public Color m_Color;

        public void ApplyColor()
        {
            foreach (Graphic graphic in m_GraphicList)
            {
                if (graphic is Image image)
                {
                    image.color = m_Color;
                }
                else if (graphic is Text text)
                {
                    text.color = m_Color;
                }
#if UNITY_EDITOR
                Boolean tempEnabled = graphic.enabled;
                graphic.enabled = !tempEnabled;
                graphic.enabled = tempEnabled;
#endif
            }
        }
    }
}
