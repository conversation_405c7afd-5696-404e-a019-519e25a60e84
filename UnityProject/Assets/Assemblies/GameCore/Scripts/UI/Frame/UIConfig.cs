using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core
{
    [CreateAssetMenu(fileName = "UIConfig", menuName = "UI Asset/UI/UIConfig")]
    public class UIConfig : ScriptableObject
    {
        public List<EnumString<UITagId>> tagIdList = new List<EnumString<UITagId>>();
        public bool stackOptimizable = true;
        public string typeName;
        public AssetWeakRef scriptWeakRef;
        public List<UIViewConfig> viewConfigList = new List<UIViewConfig>();

        public UIViewConfig GetViewConfigByName(string name)
        {
            for (int i = 0; i < viewConfigList.Count; ++i)
            {
                UIViewConfig viewConfig = viewConfigList[i];
                if (viewConfig != null && viewConfig.nameStr == name)
                {
                    return viewConfig;
                }
            }
            return null;
        }
    }
}
