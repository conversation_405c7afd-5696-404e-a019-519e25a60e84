using System;

namespace Phoenix.Core
{
    public class UICommandRefresh : UICommand
    {
        public UIContext context;
        public bool isAsync;
        public bool immediately;
        public Action<UIBase> actionOnOpenEnd;
        public Action<UIContext, bool, bool, Action<UIBase>> actionOnOpen;

        public override void OnRelease()
        {
            base.OnRelease();
            isAsync = default;
            immediately = default;
            context = null;
            actionOnOpen = null;
            actionOnOpenEnd = null;
        }

        protected override void OnExecute()
        {
            if (actionOnOpen != null)
            {
                actionOnOpen(context, isAsync, immediately, OnUIOpenEnd);
            }
            else
            {
                OnUIOpenEnd(null);
            }
        }

        private void OnUIOpenEnd(UIBase ui)
        {
            if (actionOnOpenEnd != null)
            {
                actionOnOpenEnd(ui);
            }
            End();
        }
    }
}
