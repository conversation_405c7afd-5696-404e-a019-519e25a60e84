using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Phoenix.Core
{
    [CreateAssetMenu(fileName = "UIConfigInventory", menuName = "UI Asset/UIConfigInventory")]
    public class UIConfigInventory : ScriptableObject
    {
        public const string path = "Assets/Res/UI/Common/UIConfig/UIConfigInventory.asset";

        public List<UIConfig> configList = new List<UIConfig>();
    }
}
