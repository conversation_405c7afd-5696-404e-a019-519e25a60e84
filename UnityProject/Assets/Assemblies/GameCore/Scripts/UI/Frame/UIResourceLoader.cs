using System;
using YooAsset;

namespace Phoenix.Core
{
    public class UIResourceLoader<T> : UIResourceLoader where T : UnityEngine.Object
    {
        public override void OnLoad(bool isAsync)
        {
            if (isAsync)
            {
                if (string.IsNullOrEmpty(m_context.subName))
                {
                    ResourceManager.instance.LoadAsync<T>(m_context.path, OnAssetHandleLoadEnd);
                }
                else
                {
                    ResourceManager.instance.LoadSubAsync<T>(m_context.path, OnSubAssetHandleLoadEnd);
                }
            }
            else
            {
                if (string.IsNullOrEmpty(m_context.subName))
                {
                    AssetHandle handle = ResourceManager.instance.LoadSync<T>(m_context.path);
                    OnAssetHandleLoadEnd(handle);
                }
                else
                {
                    SubAssetsHandle handle = ResourceManager.instance.LoadSubSync<T>(m_context.path);
                    OnSubAssetHandleLoadEnd(handle);
                }
            }
        }

        private void OnAssetHandleLoadEnd(AssetHandle handle)
        {
            m_context.handle = handle;
            m_context.resObj = handle.GetAssetObject<T>();
            NotifyLoadEnd();
        }

        private void OnSubAssetHandleLoadEnd(SubAssetsHandle handle)
        {
            m_context.handle = handle;
            m_context.resObj = handle.GetSubAssetObject<T>(m_context.subName);
            NotifyLoadEnd();
        }
    }

    public abstract class UIResourceLoader : ClassPoolObj
    {
        protected UIResourceLoadContext m_context;
        protected Action<UIResourceLoadContext> m_actionOnLoadEnd;

        public UIResourceLoadContext context
        {
            get { return m_context; }
        }

        public override void OnRelease()
        {
            m_context = default;
            m_actionOnLoadEnd = default;
            base.OnRelease();
        }

        public void Set(UIResourceLoadContext context)
        {
            m_context = context;
        }

        public void StartLoad(bool isAsync, Action<UIResourceLoadContext> actionOnLoadEnd)
        {
            m_actionOnLoadEnd = actionOnLoadEnd;
            OnLoad(isAsync);
        }

        public abstract void OnLoad(bool isAsync);

        protected void NotifyLoadEnd()
        {
            if (m_actionOnLoadEnd != null)
            {
                m_actionOnLoadEnd(m_context);
            }
        }
    }
}
