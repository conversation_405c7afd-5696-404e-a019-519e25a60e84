using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.U2D;

namespace Phoenix.Core
{
    public class UIBase
    {
        private Int32 m_instantiateTimes;
        private EventMap m_eventMap = new EventMap();
        private CommandMachine m_cmdMachine = new CommandMachine();

        private UIState m_state;
        private float m_openedTime;

        protected UIContext m_currentContext;
        private UIConfig m_config;
        private Dictionary<string, UIView> m_viewMap = new Dictionary<string, UIView>();

        private List<UIResourceLoader> m_loaderList = new List<UIResourceLoader>();
        private List<UIResourceLoadContext> m_loadContextList = new List<UIResourceLoadContext>();

        private Action<UIBase> m_actionOnOpenEnd;
        private Action<UIBase> m_actionOnCloseEnd;
        private bool m_immediately;
        private bool m_async;
        private bool m_destroy;

        public UIState state { get { return m_state; } }
        public float openedTime { get { return m_openedTime; } }
        public bool stackOptimizable { get { return m_config.stackOptimizable; } }


        #region Public

        public void Init(UIConfig config)
        {
            if (m_state == UIState.Init)
            {
                return;
            }
            m_state = UIState.Init;
            m_config = config;
            OnInit();
        }

        public void UnInit()
        {
            if (m_state == UIState.None)
            {
                return;
            }
            m_state = UIState.None;
            OnUnInit();
            foreach (var kv in m_viewMap)
            {
                kv.Value.UnInit();
            }
            m_viewMap.Clear();
            OnClearViews();
        }

        public void Open(UIContext context, bool isAsync, bool immediately = false, Action<UIBase> actionOnOpenEnd = null)
        {
            DebugUtility.Log($"[UIBase] OpenUI ---> {m_config.typeName}, Async: {isAsync}, Immediate: {immediately}.");
            UICommandOpen openCmd = CreateCommand<UICommandOpen>();
            openCmd.context = context;
            openCmd.isAsync = isAsync;
            openCmd.immediately = immediately;
            openCmd.actionOnOpen = OpenInternal;
            openCmd.actionOnOpenEnd = actionOnOpenEnd;
            PushCommand(openCmd);
        }

        public void Refresh(UIContext context, bool isAsync, bool immediately = false, Action<UIBase> actionOnOpenEnd = null)
        {
            DebugUtility.Log($"[UIBase] RefreshUI ---> {m_config.typeName}, Async: {isAsync}, Immediate: {immediately}.");
            UICommandRefresh openCmd = CreateCommand<UICommandRefresh>();
            openCmd.context = context;
            openCmd.isAsync = isAsync;
            openCmd.immediately = immediately;
            openCmd.actionOnOpen = RefreshInternal;
            openCmd.actionOnOpenEnd = actionOnOpenEnd;
            PushCommand(openCmd);
        }

        public void Close(bool immediately = false, bool destroy = false, Action<UIBase> actionOnCloseEnd = null)
        {
            DebugUtility.Log($"[UIBase] CloseUI ---> {m_config.typeName}, Immediate: {immediately}, Destroy: {destroy}.");
            UICommandClose closeCmd = CreateCommand<UICommandClose>();
            closeCmd.immediately = immediately;
            closeCmd.destroy = destroy;
            closeCmd.actionOnClose = CloseInternal;
            closeCmd.actionOnCloseEnd = actionOnCloseEnd;
            PushCommand(closeCmd);
        }

        /// <summary> UITag �Ƿ��н��� </summary>
        public bool ContainIntersectionTag(UITagId tag)
        {
            Boolean result = false;
            if (m_config.tagIdList.Count > 0)
            {
                for (int i = 0; i < m_config.tagIdList.Count; ++i)
                {
                    if ((m_config.tagIdList[i] & tag) != 0)
                    {
                        result = true;
                        break;
                    }
                }
            }
            return result;
        }

        public void Tick(TimeSlice timeSlice)
        {
            OnTick(timeSlice);
        }

        public UIContext GetPreContext()
        {
            if (m_currentContext != null)
            {
                return m_currentContext.preContext;
            }
            return null;
        }

        #endregion

        private T CreateCommand<T>() where T : UICommand, new()
        {
            T cmd = ClassPoolManager.instance.Fetch<T>();
            cmd.Init(this);
            return cmd;
        }

        private void PushCommand(Command cmd)
        {
            m_cmdMachine.PushCommand(cmd, null);
        }


        protected UIBase TransferOpen(UIContext context, bool isAsync, bool immediately)
        {
            context.preContext = m_currentContext;
            UIBase ui = UIManager.instance.Open(context, isAsync, immediately, TransferOpenEnd);
            return ui;
        }

        private void TransferOpenEnd(UIBase uiBase)
        {
            Close(true, false);
        }

        protected void ReturnToPreUI()
        {
            UIContext preContext = GetPreContext();
            if (preContext != null)
            {
                UIManager.instance.Open(preContext, true);
            }
        }


        protected void ReadyUpdateView()
        {
            CreateViews();
            OnRefreshViews();
            PlayOpen();
        }

        protected void PlayOpen()
        {
            if (m_immediately)
            {
                PlayOpenEnd();
            }
            else
            {
                OnPlayOpen();
            }
        }

        protected virtual void OnPlayOpen()
        {
            PlayOpenEnd();
        }

        protected void PlayOpenEnd()
        {
            m_state = UIState.Opened;
            OnPlayOpenEnd();
            if (m_actionOnOpenEnd != null)
            {
                m_actionOnOpenEnd(this);
                m_actionOnOpenEnd = null;
            }
            m_openedTime = Time.realtimeSinceStartup;
            UIManager.instance.RefreshStack();
        }

        protected void PlayClose()
        {
            if (m_immediately)
            {
                PlayCloseEnd();
            }
            else
            {
                OnPlayClose();
            }
        }

        protected virtual void OnPlayClose()
        {
            PlayCloseEnd();
        }

        protected void PlayCloseEnd()
        {
            if (m_state != UIState.UnInit)
            {
                m_state = UIState.Closed;
                List<UIView> tempList = new List<UIView>();
                foreach (var kv in m_viewMap)
                {
                    tempList.Add(kv.Value);
                }
                foreach (var view in tempList)
                {
                    if (view == null)
                    {
                        continue;
                    }
                    view.SetActiveSafely(false);
                    UIMultiLayerConfig multiLayerConfig = view.GetComponent<UIMultiLayerConfig>();
                    if (multiLayerConfig != null)
                    {
                        SceneLayerManager.instance.PopLayerList(multiLayerConfig.layerList, false);
                    }
                    else
                    {
                        SceneLayerBase layerBase = view.GetComponent<SceneLayerBase>();
                        SceneLayerManager.instance.PopLayer(layerBase, false);
                    }
                    if (m_destroy)
                    {
                        DestroyView(view);
                        UnloadAllResource();
                        m_state = UIState.UnInit;
                    }
                }
            }
            OnPlayCloseEnd();
            if (m_actionOnCloseEnd != null)
            {
                m_actionOnCloseEnd(this);
                m_actionOnCloseEnd = null;
            }
        }


        private void OpenInternal(UIContext context, bool isAsync, bool immediately, Action<UIBase> actionOnOpenEnd)
        {
            // m_immediately = immediately;
            // m_actionOnOpenEnd = actionOnOpenEnd;
            // if (state == UIState.Opening || state == UIState.Opened)
            // {
            //     Debug.LogError(string.Format("[UI] CloseInternal Cannot Open Not Closed:{0} state:{1}", GetType().Name, m_state));
            //     PlayOpenEnd();
            //     return;
            // }
            // ProcessUIContext(context);
            // m_async = isAsync;
            // m_state = UIState.Opening;

            // OnCollectViewLoader();
            // OnCollectResourceLoader();
            // StartLoad(isAsync);
            
            ProcessUIContext(context);
            m_async = isAsync;
            m_state = UIState.Opening;
            m_immediately = immediately;
            m_actionOnOpenEnd = actionOnOpenEnd;
            OnCollectViewLoader();
            OnCollectResourceLoader();
            StartLoad(isAsync);
        }
        private void RefreshInternal(UIContext context, bool isAsync, bool immediately, Action<UIBase> actionOnOpenEnd)
        {
            ProcessUIContext(context);
            m_async = isAsync;
            m_state = UIState.Opening;

            m_immediately = immediately;
            m_actionOnOpenEnd = actionOnOpenEnd;

            //OnCollectViewLoader();
            OnCollectResourceLoader();
            StartLoad(isAsync);
        }

        private void CloseInternal(bool immediately, bool destroy, Action<UIBase> actionOnCloseEnd)
        {
            m_immediately = immediately;
            m_destroy = destroy;
            m_actionOnCloseEnd = actionOnCloseEnd;
            if (m_state == UIState.Closed || m_state == UIState.Closing || m_state == UIState.UnInit)
            {
                Debug.LogError(string.Format("[UI] CloseInternal Cannot Close Not Opened:{0} state:{1}", GetType().Name, m_state));
                PlayCloseEnd();
                return;
            }
            m_state = UIState.Closing;
            PlayClose();
        }

        private void CreateViews()
        {
            for (int i = 0; i < m_config.viewConfigList.Count; ++i)
            {
                UIViewConfig viewConfig = m_config.viewConfigList[i];
                if (!NeedLoadView(viewConfig))
                {
                    continue;
                }
                CreateView(viewConfig.nameStr);
                //uiView.SetActiveSafely(true);
            }
        }

        private UIView CreateView(string name)
        {
            GameObject uiViewRoot;
            bool needInit = false;
            UIMultiLayerConfig multiLayerConfig = null;
            if (m_viewMap.TryGetValue(name, out UIView ui))
            {
                uiViewRoot = ui.gameObject;
            }
            else
            {
                needInit = true;
                string prefabName;
                uiViewRoot = InstantiateView(name, out prefabName);
                multiLayerConfig = uiViewRoot.GetComponent<UIMultiLayerConfig>();
                if (multiLayerConfig == null)
                {
                    var layer = uiViewRoot.GetComponent<SceneLayerBase>();
                    if (layer != null)
                    {
                        layer.InitLayerName(prefabName);
                    }
                }
                else
                {
                    for (int i = 0; i < multiLayerConfig.layerList.Count; ++i)
                    {
                        var layer = multiLayerConfig.layerList[i];
                        if (layer != null)
                        {
                            layer.InitLayerName(prefabName);
                        }
                    }
                }
            }
            if (multiLayerConfig == null)
            {
                multiLayerConfig = uiViewRoot.GetComponent<UIMultiLayerConfig>();
            }
            if (multiLayerConfig != null)
            {
                SceneLayerManager.instance.PushLayerList(multiLayerConfig.layerList, true);
            }
            else
            {
                SceneLayerBase layerBase = uiViewRoot.GetComponent<SceneLayerBase>();
                SceneLayerManager.instance.PushLayer(layerBase, true);
            }
            uiViewRoot.SetActiveSafely(true);
            if (needInit)
            {
                ui = OnAddView(uiViewRoot, name);
                ui.BindOwner(this);
                ui.Init(null);
                m_viewMap.Add(name, ui);
                OnAddView(ui);
            }
            return ui;
        }

        private GameObject InstantiateView(string name, out string prefabName)
        {
            UIViewConfig viewConfig = m_config.GetViewConfigByName(name);
            if (viewConfig == null)
            {
                prefabName = string.Empty;
                return null;
            }
            string path = viewConfig.prefabWeakRef.path;
            GameObject prefab = GetResource<GameObject>(path);
            if (prefab == null)
            {
                prefabName = string.Empty;
                return null;
            }
            GameObject root = GameObject.Instantiate(prefab);

#if UNITY_EDITOR
            root.RemoveUnityCloneTerms(true);
            root.name = String.Format("{0}_UIView_{1}", root.name, ++m_instantiateTimes);
#endif
            prefabName = prefab.name;
            return root;
        }

        private void DestroyView(UIView view)
        {
            if (m_viewMap.ContainsValue(view))
            {
                m_viewMap.RemoveValue(view);
                OnRemoveView(view);
                view.UnInit();
                GameObject.Destroy(view.gameObject);
            }
        }

        private void ProcessUIContext(UIContext context)
        {
            bool newContext = false;
            if (m_currentContext != context)
            {
                m_currentContext = context;
                newContext = true;
            }
            OnProcessingUIContext(newContext);
        }

        #region Virtual Methods

        protected virtual void OnInit() { }
        protected virtual void OnUnInit() { }
        protected virtual void OnRefreshViews() { }
        protected virtual void OnCollectResourceLoader() { }
        protected virtual void OnClearViews() { }
        protected virtual void OnPlayOpenEnd() { }
        protected virtual void OnPlayCloseEnd() { }
        protected virtual void OnTick(TimeSlice timeSlice) { }
        protected virtual UIView OnAddView(GameObject viewObj, string name) { return null; }
        protected virtual void OnAddView(UIView view) { }
        protected virtual void OnRemoveView(UIView view) { }
        protected virtual bool NeedLoadView(UIViewConfig config) { return true; }
        protected virtual void OnProcessingUIContext(Boolean newContext) { }

        #endregion


        #region Resource Loader


        protected virtual void OnCollectViewLoader()
        {
            for (int i = 0; i < m_config.viewConfigList.Count; ++i)
            {
                UIViewConfig viewConfig = m_config.viewConfigList[i];
                if (!NeedLoadView(viewConfig))
                {
                    continue;
                }
                if (!m_viewMap.ContainsKey(viewConfig.nameStr))
                {
                    AddResourceLoader<GameObject>(viewConfig.prefabWeakRef.path);
                }
            }
        }

        private void StartLoad(bool isAsync)
        {
            if (m_loaderList.Count > 0)
            {
                List<UIResourceLoader> tempList = new List<UIResourceLoader>(m_loaderList);
                for (int i = 0; i < tempList.Count; ++i)
                {
                    tempList[i].StartLoad(isAsync, OnLoaderLoadEnd);
                }
            }
            else
            {
                ReadyUpdateView();
            }
        }

        private void OnLoaderLoadEnd(UIResourceLoadContext context)
        {
            for (int i = 0; i < m_loaderList.Count; ++i)
            {
                if (m_loaderList[i].context == context)
                {
                    m_loaderList[i].Release();
                    m_loaderList.RemoveAt(i);
                    break;
                }
            }
            if (m_loaderList.Count == 0)
            {
                ReadyUpdateView();
            }
        }

        public Sprite GetSprite(string path)
        {
            Sprite sprite = GetResource<Sprite>(path);
            return sprite;
        }

        public Sprite GetSprite(string atlasPath, string spriteName)
        {
            SpriteAtlas atlas = GetResource<SpriteAtlas>(atlasPath);
            if (atlas != null)
            {
                return atlas.GetSprite(spriteName);
            }
            return null;
        }

        public T GetResource<T>(string path, string subName = null) where T : UnityEngine.Object
        {
            UIResourceLoadContext loadContext = GetResourceLoadContext(path, subName);
            if (loadContext != null)
            {
                return loadContext.resObj as T;
            }
            DebugUtility.LogWarning(string.Format("[{0}]��Դ�����ڣ���ҪԤ����: {1}@{2}", GetType().Name, path, subName));
            loadContext = ClassPoolManager.instance.Fetch<UIResourceLoadContext>();
            loadContext.path = path;
            loadContext.subName = subName;
            m_loadContextList.Add(loadContext);

            UIResourceLoader<T> loader = ClassPoolManager.instance.Fetch<UIResourceLoader<T>>();
            loader.Set(loadContext);
            loader.StartLoad(false, null);
            loader.Release();

            return loadContext.resObj as T;
        }

        protected void AddResourceLoader<T>(string path, int groupId = 0) where T : UnityEngine.Object
        {
            AddResourceLoader<T>(path, null, groupId);
        }

        protected void AddResourceLoader<T>(string path, string subName, int groupId = 0) where T : UnityEngine.Object
        {
            UIResourceLoadContext loadContext = GetResourceLoadContext(path, subName);
            if (loadContext != null)
            {
                loadContext.refGroupIdList.AddNotContains(groupId);
                return;
            }
            loadContext = ClassPoolManager.instance.Fetch<UIResourceLoadContext>();
            loadContext.path = path;
            loadContext.subName = subName;
            m_loadContextList.Add(loadContext);

            UIResourceLoader<T> loader = ClassPoolManager.instance.Fetch<UIResourceLoader<T>>();
            loader.Set(loadContext);
            m_loaderList.Add(loader);
        }

        protected UIResourceLoadContext GetResourceLoadContext(string path, string subName = null)
        {
            for (int i = 0; i < m_loadContextList.Count; ++i)
            {
                UIResourceLoadContext loadContext = m_loadContextList[i];
                if (loadContext.path != path)
                {
                    continue;
                }
                if (string.IsNullOrEmpty(subName) ^ string.IsNullOrEmpty(loadContext.subName))
                {
                    continue;
                }
                if (string.IsNullOrEmpty(subName) || subName == loadContext.subName)
                {
                    return loadContext;
                }
            }
            return null;
        }

        public void UnloadResourceGroup(int groupId)
        {
            for (int i = 0; i < m_loadContextList.Count;)
            {
                UIResourceLoadContext loadContext = m_loadContextList[i];
                if (loadContext.refGroupIdList.Contains(groupId))
                {
                    loadContext.refGroupIdList.Remove(groupId);
                    if (loadContext.refGroupIdList.Count == 0)
                    {
                        loadContext.Release();
                        m_loadContextList.RemoveAt(i);
                        continue;
                    }
                }
                ++i;
            }
        }

        public void UnloadAllResource()
        {
            for (int i = 0; i < m_loadContextList.Count; ++i)
            {
                UIResourceLoadContext loadContext = m_loadContextList[i];
                loadContext.Release();
            }
            m_loadContextList.Clear();
        }

        #endregion


        #region EventMap

        public void RegisterListener(int eventId, Action action)
        {
            m_eventMap.RegisterListener(eventId, action);
        }

        public void RegisterListener<T0>(int eventId, Action<T0> action)
        {
            m_eventMap.RegisterListener(eventId, action);
        }

        public void RegisterListener<T0, T1>(int eventId, Action<T0, T1> action)
        {
            m_eventMap.RegisterListener(eventId, action);
        }

        public void RegisterListener<T0, T1, T2>(int eventId, Action<T0, T1, T2> action)
        {
            m_eventMap.RegisterListener(eventId, action);
        }

        public void RegisterListener<T0, T1, T2, T3>(int eventId, Action<T0, T1, T2, T3> action)
        {
            m_eventMap.RegisterListener(eventId, action);
        }

        public void UnRegisterListener(int eventId, Action action)
        {
            m_eventMap.UnRegisterListener(eventId, action);
        }

        public void UnRegisterListener<T0>(int eventId, Action<T0> action)
        {
            m_eventMap.UnRegisterListener(eventId, action);
        }

        public void UnRegisterListener<T0, T1>(int eventId, Action<T0, T1> action)
        {
            m_eventMap.UnRegisterListener(eventId, action);
        }

        public void UnRegisterListener<T0, T1, T2>(int eventId, Action<T0, T1, T2> action)
        {
            m_eventMap.UnRegisterListener(eventId, action);
        }

        public void UnRegisterListener<T0, T1, T2, T3>(int eventId, Action<T0, T1, T2, T3> action)
        {
            m_eventMap.UnRegisterListener(eventId, action);
        }

        public void Broadcast(int eventId)
        {
            m_eventMap.Broadcast(eventId);
        }

        public void Broadcast<T0>(int eventId, T0 arg0)
        {
            m_eventMap.Broadcast(eventId, arg0);
        }

        public void Broadcast<T0, T1>(int eventId, T0 arg0, T1 arg1)
        {
            m_eventMap.Broadcast(eventId, arg0, arg1);
        }

        public void Broadcast<T0, T1, T2>(int eventId, T0 arg0, T1 arg1, T2 arg2)
        {
            m_eventMap.Broadcast(eventId, arg0, arg1, arg2);
        }

        public void Broadcast<T0, T1, T2, T3>(int eventId, T0 arg0, T1 arg1, T2 arg2, T3 arg3)
        {
            m_eventMap.Broadcast(eventId, arg0, arg1, arg2, arg3);
        }

        public string GetLogOfDelegateRegisterCount(Type enumType = null)
        {
            return m_eventMap.GetLogOfDelegateRegisterCount(enumType);
        }

        #endregion

    }
}
