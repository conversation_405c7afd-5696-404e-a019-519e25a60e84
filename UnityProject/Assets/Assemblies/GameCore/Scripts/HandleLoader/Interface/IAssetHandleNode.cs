
//using YooAsset;

//namespace Phoenix.Core
//{
//    /// <summary>
//    /// Asset 资源 内存镜像 Handle持有对象
//    /// </summary>
//    public interface IAssetHandleNode
//    {
//        /// <summary>
//        /// 设置AssetHandle
//        /// </summary>
//        /// <param name="handle"></param>
//        void SetAssetHandle(HandleBase handle);

//        /// <summary>
//        /// 获取AssetHandle
//        /// </summary>
//        /// <returns></returns>
//        HandleBase GetAssetHandle();

//        /// <summary>
//        /// 释放资源
//        /// </summary>
//        void Release();
//    }
//}
