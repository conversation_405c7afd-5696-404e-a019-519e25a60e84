using YooAsset;

namespace Phoenix.Core 
{
    /// <summary>
    /// A set Contain of AnimationClip, Material, Texture2D, ScriptableObjectAsset and etc.
    /// 
    /// </summary>
    public class AssetNode : AssetNodeBase
    {

        public AssetHandle AssetHandle { get { return m_handleBase as AssetHandle; } }

        public override TAsset GetAsset<TAsset>(string subName)
        {
            if (AssetHandle != null)
            {
                return AssetHandle.GetAssetObject<TAsset>();
            }
            return base.GetAsset<TAsset>(subName);
        }
    }
}
