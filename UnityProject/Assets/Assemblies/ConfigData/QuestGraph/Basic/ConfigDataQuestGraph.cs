
using System;
using System.Collections.Generic;


namespace Phoenix.ConfigData.QuestGraph
{
    public partial class ConfigDataQuestGraph
    {
        /// <summary> key: Index (unique in single graph)，Value: Node </summary>
        public Dictionary<Int32, QuestNode> Nodes = new Dictionary<Int32, QuestNode>();

        /// <summary>
        /// 开始节点必是0
        /// </summary>
        public QuestNode StartNode => Nodes[0];
    }
}
