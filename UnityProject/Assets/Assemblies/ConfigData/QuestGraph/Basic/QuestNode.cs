
using System;
using System.Collections.Generic;
using System.Linq;


namespace Phoenix.ConfigData.QuestGraph
{
    public abstract class QuestNode
    {
        public Int32 NodeUid { get; set; }
        public readonly List<Connection> PreConnections = new List<Connection>();
        public readonly List<Connection> NextConnections = new List<Connection>();


        public IEnumerable<T> GetNextQuestNode<T>() where T : QuestNode
        {
            return NextConnections.Select(c => c.NextNode).OfType<T>();
        }
    }


    /// <summary> 任务需求基类 </summary>
    public abstract class QuestCompleteCondition
    {
    }

    /// <summary> 任务条件基类（附加于CustomConnection） </summary>
    public abstract class QuestActiveCondition
    {
    }

    /// <summary> 行为基类 </summary>
    public abstract class QuestAction
    {
    }
}
