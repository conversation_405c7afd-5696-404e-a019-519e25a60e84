
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;


namespace Phoenix.ConfigData.QuestGraph
{
    public partial class DeserializeUtility
    {
        public static ConfigDataQuestGraph DeserializeNodeGraph(JObject jo)
        {
            ConfigDataQuestGraph data = new ConfigDataQuestGraph();
            if (jo["nodes"] != null)
            {
                foreach (var item in jo["nodes"].Values<JObject>())
                {
                    QuestNode node = DeserializeUtility.DeserializeQuestNode(item);
                    if (node != null && !data.Nodes.ContainsKey(node.NodeUid))
                    {
                        data.Nodes.Add(node.NodeUid, node);
                    }
                }
            }
            if (jo["connections"] != null)
            {
                foreach (var item in jo["connections"].Values<JObject>())
                {
                    Connection connection = DeserializeCustomConnection(item);
                    if (data.Nodes.TryGetValue(connection.PreNodeUid, out QuestNode preNode))
                    {
                        preNode.NextConnections.Add(connection);
                        connection.PreNode = preNode;
                    }
                    if (data.Nodes.TryGetValue(connection.NextNodeUid, out QuestNode nextNode))
                    {
                        nextNode.PreConnections.Add(connection);
                        connection.NextNode = nextNode;
                    }
                }
            }
            return data;
        }

        public static Connection DeserializeCustomConnection(JObject jo)
        {
            Connection data = new Connection();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "_sourceNode") { data.PreNodeUid = item1.Value["$ref"].Value<Int32>(); continue; }
                if (item1.Key.ToString() == "_targetNode") { data.NextNodeUid = item1.Value["$ref"].Value<Int32>(); continue; }
                if (item1.Key.ToString() == "ExecuteMode") { data.ExecuteMode = (ExecuteMode)item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "LogicalMode") { data.LogicalMode = (LogicalMode)item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "Conditions")
                {
                    data.Conditions = new List<QuestActiveCondition>();
                    foreach (JObject item2 in item1.Value.Values<JObject>())
                    {
                        QuestActiveCondition elem = DeserializeUtility.DeserializeQuestActiveCondition(item2);
                        data.Conditions.Add(elem);
                    }
                    continue;
                }
            }
            return data;
        }
    }


}
