using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */


namespace Phoenix.ConfigData.QuestGraph
{
    /// <summary> Json反序列化辅助类 </summary>
    public partial class DeserializeUtility
    {
        
        public static QuestNode DeserializeQuestNode(JObject jo)
        {
            QuestNode result = null;
            String typeFullName = jo["$type"].Value<String>();
            switch (typeFullName)
            {
                case "Phoenix.QuestGraphEditor.QuestNodeBegin":
                    result = DeserializeUtility.DeserializeQuestNodeBegin(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestNodeEnd":
                    result = DeserializeUtility.DeserializeQuestNodeEnd(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestNodeTemplate":
                    result = DeserializeUtility.DeserializeQuestNodeTemplate(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestNodeAction":
                    result = DeserializeUtility.DeserializeQuestNodeAction(jo);
                    break;
                default: break;
            }
            return result;
        }
        
        public static QuestCompleteCondition DeserializeQuestCompleteCondition(JObject jo)
        {
            QuestCompleteCondition result = null;
            String typeFullName = jo["$type"].Value<String>();
            switch (typeFullName)
            {
                case "Phoenix.QuestGraphEditor.QuestCompleteConditionTalk":
                    result = DeserializeUtility.DeserializeQuestCompleteConditionTalk(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestCompleteConditionSubmit":
                    result = DeserializeUtility.DeserializeQuestCompleteConditionSubmit(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestCompleteConditionReach":
                    result = DeserializeUtility.DeserializeQuestCompleteConditionReach(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestCompleteConditionBattle":
                    result = DeserializeUtility.DeserializeQuestCompleteConditionBattle(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestCompleteConditionKillMonster":
                    result = DeserializeUtility.DeserializeQuestCompleteConditionKillMonster(jo);
                    break;
                default: break;
            }
            return result;
        }
        
        public static QuestActiveCondition DeserializeQuestActiveCondition(JObject jo)
        {
            QuestActiveCondition result = null;
            String typeFullName = jo["$type"].Value<String>();
            switch (typeFullName)
            {
                case "Phoenix.QuestGraphEditor.QuestConditionPlayerLevel":
                    result = DeserializeUtility.DeserializeQuestConditionPlayerLevel(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestConditionQuestStatus":
                    result = DeserializeUtility.DeserializeQuestConditionQuestStatus(jo);
                    break;
                default: break;
            }
            return result;
        }
        
        public static QuestAction DeserializeQuestAction(JObject jo)
        {
            QuestAction result = null;
            String typeFullName = jo["$type"].Value<String>();
            switch (typeFullName)
            {
                case "Phoenix.QuestGraphEditor.QuestActionNpcAdd":
                    result = DeserializeUtility.DeserializeQuestActionNpcAdd(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestActionNpcRemove":
                    result = DeserializeUtility.DeserializeQuestActionNpcRemove(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestActionBehaviorAdd":
                    result = DeserializeUtility.DeserializeQuestActionBehaviorAdd(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestActionBehaviorRemove":
                    result = DeserializeUtility.DeserializeQuestActionBehaviorRemove(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestActionHakoniwaWaypointActive":
                    result = DeserializeUtility.DeserializeQuestActionHakoniwaWaypointActive(jo);
                    break;
                case "Phoenix.QuestGraphEditor.QuestActionPlayStory":
                    result = DeserializeUtility.DeserializeQuestActionPlayStory(jo);
                    break;
                default: break;
            }
            return result;
        }
        
        
        #region StructureDefine
        
        public static Interact DeserializeInteract(JObject jo)
        {
            Interact data = new Interact();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "Title") { data.Title = item1.Value.Value<String>(); continue; }
                if (item1.Key.ToString() == "Status") { data.Status = item1.Value.Value<String>(); continue; }
            }
            return data;
        }
        
        #endregion
        
        
        #region QuestNode
        
        public static ConfigDataQuestNodeBegin DeserializeQuestNodeBegin(JObject jo)
        {
            ConfigDataQuestNodeBegin data = new ConfigDataQuestNodeBegin();
            data.NodeUid = jo["$id"].Value<Int32>();
            
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
            }
            return data;
        }
        public static ConfigDataQuestNodeEnd DeserializeQuestNodeEnd(JObject jo)
        {
            ConfigDataQuestNodeEnd data = new ConfigDataQuestNodeEnd();
            data.NodeUid = jo["$id"].Value<Int32>();
            
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
            }
            return data;
        }
        public static ConfigDataQuestNodeTemplate DeserializeQuestNodeTemplate(JObject jo)
        {
            ConfigDataQuestNodeTemplate data = new ConfigDataQuestNodeTemplate();
            data.NodeUid = jo["$id"].Value<Int32>();
            
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "QuestId") { data.QuestId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "QuestName") { data.QuestName = item1.Value.Value<String>(); continue; }
                if (item1.Key.ToString() == "QuestDesc") { data.QuestDesc = item1.Value.Value<String>(); continue; }
                if (item1.Key.ToString() == "QuestConditionMode") { data.QuestConditionMode = (LogicalMode)item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "CompleteConditions")
                {
                    data.CompleteConditions = new List<QuestCompleteCondition>();
                    foreach (JObject item2 in item1.Value.Values<JObject>())
                    {
                        QuestCompleteCondition elem = DeserializeUtility.DeserializeQuestCompleteCondition(item2);
                        data.CompleteConditions.Add(elem);
                    }
                    continue;
                }
            }
            return data;
        }
        public static ConfigDataQuestNodeAction DeserializeQuestNodeAction(JObject jo)
        {
            ConfigDataQuestNodeAction data = new ConfigDataQuestNodeAction();
            data.NodeUid = jo["$id"].Value<Int32>();
            
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "Actions")
                {
                    data.Actions = new List<QuestAction>();
                    foreach (JObject item2 in item1.Value.Values<JObject>())
                    {
                        QuestAction elem = DeserializeUtility.DeserializeQuestAction(item2);
                        data.Actions.Add(elem);
                    }
                    continue;
                }
            }
            return data;
        }
        
        #endregion
        
        
        #region QuestCompleteCondition
        
        public static ConfigDataQuestCompleteConditionTalk DeserializeQuestCompleteConditionTalk(JObject jo)
        {
            ConfigDataQuestCompleteConditionTalk data = new ConfigDataQuestCompleteConditionTalk();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "NpcId") { data.NpcId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "DialogueId") { data.DialogueId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestCompleteConditionSubmit DeserializeQuestCompleteConditionSubmit(JObject jo)
        {
            ConfigDataQuestCompleteConditionSubmit data = new ConfigDataQuestCompleteConditionSubmit();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "NpcId") { data.NpcId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "ItemId") { data.ItemId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "Count") { data.Count = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestCompleteConditionReach DeserializeQuestCompleteConditionReach(JObject jo)
        {
            ConfigDataQuestCompleteConditionReach data = new ConfigDataQuestCompleteConditionReach();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "SceneId") { data.SceneId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "PositionX") { data.PositionX = item1.Value.Value<Single>(); continue; }
                if (item1.Key.ToString() == "PositionY") { data.PositionY = item1.Value.Value<Single>(); continue; }
                if (item1.Key.ToString() == "PositionZ") { data.PositionZ = item1.Value.Value<Single>(); continue; }
                if (item1.Key.ToString() == "ReachRadius") { data.ReachRadius = item1.Value.Value<Single>(); continue; }
                if (item1.Key.ToString() == "EntitySkinId") { data.EntitySkinId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "EntityDirection") { data.EntityDirection = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestCompleteConditionBattle DeserializeQuestCompleteConditionBattle(JObject jo)
        {
            ConfigDataQuestCompleteConditionBattle data = new ConfigDataQuestCompleteConditionBattle();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "LevelId") { data.LevelId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "MustWin") { data.MustWin = item1.Value.Value<Boolean>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestCompleteConditionKillMonster DeserializeQuestCompleteConditionKillMonster(JObject jo)
        {
            ConfigDataQuestCompleteConditionKillMonster data = new ConfigDataQuestCompleteConditionKillMonster();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "MonsterEntityId") { data.MonsterEntityId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        
        #endregion
        
        
        #region QuestActiveCondition
        
        public static ConfigDataQuestConditionPlayerLevel DeserializeQuestConditionPlayerLevel(JObject jo)
        {
            ConfigDataQuestConditionPlayerLevel data = new ConfigDataQuestConditionPlayerLevel();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "Level") { data.Level = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "CompareMode") { data.CompareMode = (CompareMode)item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestConditionQuestStatus DeserializeQuestConditionQuestStatus(JObject jo)
        {
            ConfigDataQuestConditionQuestStatus data = new ConfigDataQuestConditionQuestStatus();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "QuestId") { data.QuestId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "CompareMode") { data.CompareMode = (CompareMode)item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "Status") { data.Status = (QuestStatus)item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        
        #endregion
        
        
        #region QuestAction
        
        public static ConfigDataQuestActionNpcAdd DeserializeQuestActionNpcAdd(JObject jo)
        {
            ConfigDataQuestActionNpcAdd data = new ConfigDataQuestActionNpcAdd();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "NpcId") { data.NpcId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "CharacterId") { data.CharacterId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "Name") { data.Name = item1.Value.Value<String>(); continue; }
                if (item1.Key.ToString() == "HakoniwaSceneId") { data.HakoniwaSceneId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "PositionX") { data.PositionX = item1.Value.Value<Single>(); continue; }
                if (item1.Key.ToString() == "PositionY") { data.PositionY = item1.Value.Value<Single>(); continue; }
                if (item1.Key.ToString() == "PositionZ") { data.PositionZ = item1.Value.Value<Single>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestActionNpcRemove DeserializeQuestActionNpcRemove(JObject jo)
        {
            ConfigDataQuestActionNpcRemove data = new ConfigDataQuestActionNpcRemove();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "NpcId") { data.NpcId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestActionBehaviorAdd DeserializeQuestActionBehaviorAdd(JObject jo)
        {
            ConfigDataQuestActionBehaviorAdd data = new ConfigDataQuestActionBehaviorAdd();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "NpcId") { data.NpcId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "BehaviorId") { data.BehaviorId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestActionBehaviorRemove DeserializeQuestActionBehaviorRemove(JObject jo)
        {
            ConfigDataQuestActionBehaviorRemove data = new ConfigDataQuestActionBehaviorRemove();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "NpcId") { data.NpcId = item1.Value.Value<Int32>(); continue; }
                if (item1.Key.ToString() == "BehaviorId") { data.BehaviorId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestActionHakoniwaWaypointActive DeserializeQuestActionHakoniwaWaypointActive(JObject jo)
        {
            ConfigDataQuestActionHakoniwaWaypointActive data = new ConfigDataQuestActionHakoniwaWaypointActive();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "HakoniwaWaypointId") { data.HakoniwaWaypointId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        public static ConfigDataQuestActionPlayStory DeserializeQuestActionPlayStory(JObject jo)
        {
            ConfigDataQuestActionPlayStory data = new ConfigDataQuestActionPlayStory();
            foreach (KeyValuePair<String, JToken> item1 in jo)
            {
                if (item1.Key.ToString() == "StoryId") { data.StoryId = item1.Value.Value<Int32>(); continue; }
            }
            return data;
        }
        
        #endregion
        
        
    }
}
