
using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */

namespace Phoenix.ConfigData.QuestGraph
{
    /// <summary> 抵达 - 抵达目标位置 </summary>
    public partial class ConfigDataQuestCompleteConditionReach : QuestCompleteCondition
    {
        /// <summary> 箱庭地图ID </summary>
        public Int32 SceneId { get; set; }
        /// <summary> 位置X </summary>
        public Single PositionX { get; set; }
        /// <summary> 位置Y </summary>
        public Single PositionY { get; set; }
        /// <summary> 位置Z </summary>
        public Single PositionZ { get; set; }
        /// <summary> 范围半径 </summary>
        public Single ReachRadius { get; set; }
        /// <summary> 目标模型ID </summary>
        public Int32 EntitySkinId { get; set; }
        /// <summary> 目标模型朝向 </summary>
        public Int32 EntityDirection { get; set; }
    }
}
