
using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */

namespace Phoenix.ConfigData.QuestGraph
{
    /// <summary> 收集 - 收集物品 </summary>
    public partial class ConfigDataQuestCompleteConditionSubmit : QuestCompleteCondition
    {
        /// <summary> 目标角色ID - 提交物品目标角色 </summary>
        public Int32 NpcId { get; set; }
        /// <summary> 物品ID </summary>
        public Int32 ItemId { get; set; }
        /// <summary> 数量 </summary>
        public Int32 Count { get; set; }
    }
}
