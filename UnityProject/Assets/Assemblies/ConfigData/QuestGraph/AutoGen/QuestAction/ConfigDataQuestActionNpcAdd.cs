
using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */

namespace Phoenix.ConfigData.QuestGraph
{
    /// <summary> 添加角色 - 添加角色到场景中 </summary>
    public partial class ConfigDataQuestActionNpcAdd : QuestAction
    {
        /// <summary> 角色ID </summary>
        public Int32 NpcId { get; set; }
        /// <summary> 模型ID </summary>
        public Int32 CharacterId { get; set; }
        /// <summary> 角色名字 </summary>
        public String Name { get; set; }
        /// <summary> 地图ID </summary>
        public Int32 MapId { get; set; }
        /// <summary> 位置ID </summary>
        public Int32 PositionId { get; set; }
    }
}
