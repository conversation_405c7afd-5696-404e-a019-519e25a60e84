
using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */

namespace Phoenix.ConfigData.QuestGraph
{
    /// <summary> 添加角色 - 添加角色到场景中 </summary>
    public partial class ConfigDataQuestActionNpcAdd : QuestAction
    {
        /// <summary> 角色ID </summary>
        public Int32 NpcId { get; set; }
        /// <summary> 模型ID </summary>
        public Int32 CharacterId { get; set; }
        /// <summary> 角色名字 </summary>
        public String Name { get; set; }
        /// <summary> 箱庭场景ID </summary>
        public Int32 HakoniwaSceneId { get; set; }
        /// <summary> 位置X </summary>
        public Single PositionX { get; set; }
        /// <summary> 位置Y </summary>
        public Single PositionY { get; set; }
        /// <summary> 位置Z </summary>
        public Single PositionZ { get; set; }
    }
}
