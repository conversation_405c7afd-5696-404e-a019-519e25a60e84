
using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */

namespace Phoenix.ConfigData.QuestGraph
{
    /// <summary> 任务状态 - 与任务状态关联的条件 </summary>
    public partial class ConfigDataQuestConditionQuestStatus : QuestActiveCondition
    {
        /// <summary> 任务ID </summary>
        public Int32 QuestId { get; set; }
        /// <summary> 比较关系 </summary>
        public CompareMode CompareMode { get; set; }
        /// <summary> 任务状态 </summary>
        public QuestStatus Status { get; set; }
    }
}
