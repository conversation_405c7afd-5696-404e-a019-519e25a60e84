
using System;
using System.Collections.Generic;


/* Auto generated code, please do not modify. 
 * 自动生成代码，请勿更改
 * Code Generate Tool: GraphCodeGenerate.cs
 */



namespace Phoenix.ConfigData.QuestGraph
{
    
    /// <summary> 任务模板 - 通用基础任务模板 </summary>
    public partial class ConfigDataQuestNodeTemplate : QuestNode
    {
        /// <summary> 任务ID </summary>
        public Int32 QuestId { get; set; }
        /// <summary> 任务名字 </summary>
        public String QuestName { get; set; }
        /// <summary> 任务描述 </summary>
        public String QuestDesc { get; set; }
        /// <summary> 完成条件关系 </summary>
        public LogicalMode QuestConditionMode { get; set; }
        /// <summary> 完成条件列表 </summary>
        public List<QuestCompleteCondition> CompleteConditions { get; set; }
    }
}
