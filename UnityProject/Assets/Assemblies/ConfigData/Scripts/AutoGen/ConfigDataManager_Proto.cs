using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    public partial class ConfigDataManager
    {
        partial void OnInitLoaders()
        {
            m_loaderMap.Add("PhoenixWorldPoint", new ProtoDataLoader<PhoenixWorldPointConfigData>(PhoenixWorldPointConfigData.Parser.ParseFrom, d => d.Id, l => m_phoenixWorldPointMap = l.map));
            m_loaderMap.Add("PhoenixWorldPointStory", new ProtoDataLoader<PhoenixWorldPointStoryConfigData>(PhoenixWorldPointStoryConfigData.Parser.ParseFrom, d => d.Id, l => m_phoenixWorldPointStoryMap = l.map));
            m_loaderMap.Add("PhoenixHakoniwa", new ProtoDataLoader<PhoenixHakoniwaConfigData>(PhoenixHakoniwaConfigData.Parser.ParseFrom, d => d.Id, l => m_phoenixHakoniwaMap = l.map));
            m_loaderMap.Add("PhoenixHakoniwaQuestGraph", new ProtoDataLoader<PhoenixHakoniwaQuestGraphConfigData>(PhoenixHakoniwaQuestGraphConfigData.Parser.ParseFrom, d => d.Id, l => m_phoenixHakoniwaQuestGraphMap = l.map));
            m_loaderMap.Add("HakoniwaLocation", new ProtoDataLoader<HakoniwaLocationConfigData>(HakoniwaLocationConfigData.Parser.ParseFrom, d => d.Id, l => m_hakoniwaLocationMap = l.map));
            m_loaderMap.Add("HakoniwaScene", new ProtoDataLoader<HakoniwaSceneConfigData>(HakoniwaSceneConfigData.Parser.ParseFrom, d => d.Id, l => m_hakoniwaSceneMap = l.map));
            m_loaderMap.Add("HakoniwaStory", new ProtoDataLoader<HakoniwaStoryConfigData>(HakoniwaStoryConfigData.Parser.ParseFrom, d => d.Id, l => m_hakoniwaStoryMap = l.map));
            m_loaderMap.Add("HakoniwaEntity", new ProtoDataLoader<HakoniwaEntityConfigData>(HakoniwaEntityConfigData.Parser.ParseFrom, d => d.Id, l => m_hakoniwaEntityMap = l.map));
            m_loaderMap.Add("HakoniwaEntityInteraction", new ProtoDataLoader<HakoniwaEntityInteractionConfigData>(HakoniwaEntityInteractionConfigData.Parser.ParseFrom, d => d.Id, l => m_hakoniwaEntityInteractionMap = l.map));
            m_loaderMap.Add("SpriteAtlas", new ProtoDataLoader<SpriteAtlasConfigData>(SpriteAtlasConfigData.Parser.ParseFrom, d => d.Id, l => m_spriteAtlasMap = l.map));
            m_loaderMap.Add("ConstString", new ProtoDataLoader<ConstStringConfigData>(ConstStringConfigData.Parser.ParseFrom, d => d.Id, l => m_constStringMap = l.map));
            m_loaderMap.Add("Number", new ProtoDataLoader<NumberConfigData>(NumberConfigData.Parser.ParseFrom, d => d.Id, l => m_numberMap = l.map));
            m_loaderMap.Add("Quest", new ProtoDataLoader<QuestConfigData>(QuestConfigData.Parser.ParseFrom, d => d.Id, l => m_questMap = l.map));
            m_loaderMap.Add("QuestAction", new ProtoDataLoader<QuestActionConfigData>(QuestActionConfigData.Parser.ParseFrom, d => d.Id, l => m_questActionMap = l.map));
            m_loaderMap.Add("QuestGroup", new ProtoDataLoader<QuestGroupConfigData>(QuestGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_questGroupMap = l.map));
            m_loaderMap.Add("TimelineInfo", new ProtoDataLoader<TimelineInfoConfigData>(TimelineInfoConfigData.Parser.ParseFrom, d => d.Id, l => m_timelineInfoMap = l.map));
            m_loaderMap.Add("Dialog", new ProtoDataLoader<DialogConfigData>(DialogConfigData.Parser.ParseFrom, d => d.Id, l => m_dialogMap = l.map));
            m_loaderMap.Add("DialogBG", new ProtoDataLoader<DialogBGConfigData>(DialogBGConfigData.Parser.ParseFrom, d => d.Id, l => m_dialogBGMap = l.map));
            m_loaderMap.Add("DialogEffect", new ProtoDataLoader<DialogEffectConfigData>(DialogEffectConfigData.Parser.ParseFrom, d => d.Id, l => m_dialogEffectMap = l.map));
            m_loaderMap.Add("SelectionGroup", new ProtoDataLoader<SelectionGroupConfigData>(SelectionGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_selectionGroupMap = l.map));
            m_loaderMap.Add("Selection", new ProtoDataLoader<SelectionConfigData>(SelectionConfigData.Parser.ParseFrom, d => d.Id, l => m_selectionMap = l.map));
            m_loaderMap.Add("GuideBattle", new ProtoDataLoader<GuideBattleConfigData>(GuideBattleConfigData.Parser.ParseFrom, d => d.Id, l => m_guideBattleMap = l.map));
            m_loaderMap.Add("TeamDecision", new ProtoDataLoader<TeamDecisionConfigData>(TeamDecisionConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionMap = l.map));
            m_loaderMap.Add("TeamDecisionItem", new ProtoDataLoader<TeamDecisionItemConfigData>(TeamDecisionItemConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionItemMap = l.map));
            m_loaderMap.Add("TeamDecisionOriginSelect", new ProtoDataLoader<TeamDecisionOriginSelectConfigData>(TeamDecisionOriginSelectConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionOriginSelectMap = l.map));
            m_loaderMap.Add("TeamDecisionCondition", new ProtoDataLoader<TeamDecisionConditionConfigData>(TeamDecisionConditionConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionConditionMap = l.map));
            m_loaderMap.Add("TeamDecisionConditionFunc", new ProtoDataLoader<TeamDecisionConditionFuncConfigData>(TeamDecisionConditionFuncConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionConditionFuncMap = l.map));
            m_loaderMap.Add("TeamDecisionMark", new ProtoDataLoader<TeamDecisionMarkConfigData>(TeamDecisionMarkConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionMarkMap = l.map));
            m_loaderMap.Add("TeamDecisionLocateSelect", new ProtoDataLoader<TeamDecisionLocateSelectConfigData>(TeamDecisionLocateSelectConfigData.Parser.ParseFrom, d => d.Id, l => m_teamDecisionLocateSelectMap = l.map));
            m_loaderMap.Add("BattleTerrain", new ProtoDataLoader<BattleTerrainConfigData>(BattleTerrainConfigData.Parser.ParseFrom, d => d.Id, l => m_battleTerrainMap = l.map));
            m_loaderMap.Add("TerrainBuff", new ProtoDataLoader<TerrainBuffConfigData>(TerrainBuffConfigData.Parser.ParseFrom, d => d.Id, l => m_terrainBuffMap = l.map));
            m_loaderMap.Add("Attribute", new ProtoDataLoader<AttributeConfigData>(AttributeConfigData.Parser.ParseFrom, d => d.Id, l => m_attributeMap = l.map));
            m_loaderMap.Add("AttributePart", new ProtoDataLoader<AttributePartConfigData>(AttributePartConfigData.Parser.ParseFrom, d => d.Id, l => m_attributePartMap = l.map));
            m_loaderMap.Add("BattleConstValue", new ProtoDataLoader<BattleConstValueConfigData>(BattleConstValueConfigData.Parser.ParseFrom, d => d.Id, l => m_battleConstValueMap = l.map));
            m_loaderMap.Add("PassiveSkill", new ProtoDataLoader<PassiveSkillConfigData>(PassiveSkillConfigData.Parser.ParseFrom, d => d.Id, l => m_passiveSkillMap = l.map));
            m_loaderMap.Add("Actor", new ProtoDataLoader<ActorConfigData>(ActorConfigData.Parser.ParseFrom, d => d.Id, l => m_actorMap = l.map));
            m_loaderMap.Add("EntityBlockGroup", new ProtoDataLoader<EntityBlockGroupConfigData>(EntityBlockGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_entityBlockGroupMap = l.map));
            m_loaderMap.Add("EntityBlock", new ProtoDataLoader<EntityBlockConfigData>(EntityBlockConfigData.Parser.ParseFrom, d => d.Id, l => m_entityBlockMap = l.map));
            m_loaderMap.Add("EntityRarity", new ProtoDataLoader<EntityRarityConfigData>(EntityRarityConfigData.Parser.ParseFrom, d => d.Id, l => m_entityRarityMap = l.map));
            m_loaderMap.Add("EntityElement", new ProtoDataLoader<EntityElementConfigData>(EntityElementConfigData.Parser.ParseFrom, d => d.Id, l => m_entityElementMap = l.map));
            m_loaderMap.Add("EntityCareer", new ProtoDataLoader<EntityCareerConfigData>(EntityCareerConfigData.Parser.ParseFrom, d => d.Id, l => m_entityCareerMap = l.map));
            m_loaderMap.Add("EntityRace", new ProtoDataLoader<EntityRaceConfigData>(EntityRaceConfigData.Parser.ParseFrom, d => d.Id, l => m_entityRaceMap = l.map));
            m_loaderMap.Add("EntityGender", new ProtoDataLoader<EntityGenderConfigData>(EntityGenderConfigData.Parser.ParseFrom, d => d.Id, l => m_entityGenderMap = l.map));
            m_loaderMap.Add("EntityMoveRule", new ProtoDataLoader<EntityMoveRuleConfigData>(EntityMoveRuleConfigData.Parser.ParseFrom, d => d.Id, l => m_entityMoveRuleMap = l.map));
            m_loaderMap.Add("ActorPassiveFormation", new ProtoDataLoader<ActorPassiveFormationConfigData>(ActorPassiveFormationConfigData.Parser.ParseFrom, d => d.Id, l => m_actorPassiveFormationMap = l.map));
            m_loaderMap.Add("TargetSelectRange", new ProtoDataLoader<TargetSelectRangeConfigData>(TargetSelectRangeConfigData.Parser.ParseFrom, d => d.Id, l => m_targetSelectRangeMap = l.map));
            m_loaderMap.Add("BattleCamp", new ProtoDataLoader<BattleCampConfigData>(BattleCampConfigData.Parser.ParseFrom, d => d.Id, l => m_battleCampMap = l.map));
            m_loaderMap.Add("BattleFormation", new ProtoDataLoader<BattleFormationConfigData>(BattleFormationConfigData.Parser.ParseFrom, d => d.Id, l => m_battleFormationMap = l.map));
            m_loaderMap.Add("BattleFormationEffect", new ProtoDataLoader<BattleFormationEffectConfigData>(BattleFormationEffectConfigData.Parser.ParseFrom, d => d.Id, l => m_battleFormationEffectMap = l.map));
            m_loaderMap.Add("BattleFormationCondition", new ProtoDataLoader<BattleFormationConditionConfigData>(BattleFormationConditionConfigData.Parser.ParseFrom, d => d.Id, l => m_battleFormationConditionMap = l.map));
            m_loaderMap.Add("UserGuideGroup", new ProtoDataLoader<UserGuideGroupConfigData>(UserGuideGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_userGuideGroupMap = l.map));
            m_loaderMap.Add("UserGuideTipStr", new ProtoDataLoader<UserGuideTipStrConfigData>(UserGuideTipStrConfigData.Parser.ParseFrom, d => d.Id, l => m_userGuideTipStrMap = l.map));
            m_loaderMap.Add("UserGuideGraphic", new ProtoDataLoader<UserGuideGraphicConfigData>(UserGuideGraphicConfigData.Parser.ParseFrom, d => d.Id, l => m_userGuideGraphicMap = l.map));
            m_loaderMap.Add("TestBattle", new ProtoDataLoader<TestBattleConfigData>(TestBattleConfigData.Parser.ParseFrom, d => d.Id, l => m_testBattleMap = l.map));
            m_loaderMap.Add("Item", new ProtoDataLoader<ItemConfigData>(ItemConfigData.Parser.ParseFrom, d => d.Id, l => m_itemMap = l.map));
            m_loaderMap.Add("DropGroup", new ProtoDataLoader<DropGroupConfigData>(DropGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_dropGroupMap = l.map));
            m_loaderMap.Add("Particle", new ProtoDataLoader<ParticleConfigData>(ParticleConfigData.Parser.ParseFrom, d => d.Id, l => m_particleMap = l.map));
            m_loaderMap.Add("World", new ProtoDataLoader<WorldConfigData>(WorldConfigData.Parser.ParseFrom, d => d.Id, l => m_worldMap = l.map));
            m_loaderMap.Add("WorldState", new ProtoDataLoader<WorldStateConfigData>(WorldStateConfigData.Parser.ParseFrom, d => d.Id, l => m_worldStateMap = l.map));
            m_loaderMap.Add("WorldActionGroup", new ProtoDataLoader<WorldActionGroupConfigData>(WorldActionGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_worldActionGroupMap = l.map));
            m_loaderMap.Add("Transformation", new ProtoDataLoader<TransformationConfigData>(TransformationConfigData.Parser.ParseFrom, d => d.Id, l => m_transformationMap = l.map));
            m_loaderMap.Add("Favor", new ProtoDataLoader<FavorConfigData>(FavorConfigData.Parser.ParseFrom, d => d.Id, l => m_favorMap = l.map));
            m_loaderMap.Add("WorldBattle", new ProtoDataLoader<WorldBattleConfigData>(WorldBattleConfigData.Parser.ParseFrom, d => d.Id, l => m_worldBattleMap = l.map));
            m_loaderMap.Add("ExploreSystem", new ProtoDataLoader<ExploreSystemConfigData>(ExploreSystemConfigData.Parser.ParseFrom, d => d.Id, l => m_exploreSystemMap = l.map));
            m_loaderMap.Add("ExploreClue", new ProtoDataLoader<ExploreClueConfigData>(ExploreClueConfigData.Parser.ParseFrom, d => d.Id, l => m_exploreClueMap = l.map));
            m_loaderMap.Add("ExploreString", new ProtoDataLoader<ExploreStringConfigData>(ExploreStringConfigData.Parser.ParseFrom, d => d.Id, l => m_exploreStringMap = l.map));
            m_loaderMap.Add("Exchange", new ProtoDataLoader<ExchangeConfigData>(ExchangeConfigData.Parser.ParseFrom, d => d.Id, l => m_exchangeMap = l.map));
            m_loaderMap.Add("WorldIntroduction", new ProtoDataLoader<WorldIntroductionConfigData>(WorldIntroductionConfigData.Parser.ParseFrom, d => d.Id, l => m_worldIntroductionMap = l.map));
            m_loaderMap.Add("WorldEntity", new ProtoDataLoader<WorldEntityConfigData>(WorldEntityConfigData.Parser.ParseFrom, d => d.Id, l => m_worldEntityMap = l.map));
            m_loaderMap.Add("EntityDialog", new ProtoDataLoader<EntityDialogConfigData>(EntityDialogConfigData.Parser.ParseFrom, d => d.Id, l => m_entityDialogMap = l.map));
            m_loaderMap.Add("EntityInteraction", new ProtoDataLoader<EntityInteractionConfigData>(EntityInteractionConfigData.Parser.ParseFrom, d => d.Id, l => m_entityInteractionMap = l.map));
            m_loaderMap.Add("WorldEntityInformation", new ProtoDataLoader<WorldEntityInformationConfigData>(WorldEntityInformationConfigData.Parser.ParseFrom, d => d.Id, l => m_worldEntityInformationMap = l.map));
            m_loaderMap.Add("Entity2DSkin", new ProtoDataLoader<Entity2DSkinConfigData>(Entity2DSkinConfigData.Parser.ParseFrom, d => d.Id, l => m_entity2DSkinMap = l.map));
            m_loaderMap.Add("EntitySkin", new ProtoDataLoader<EntitySkinConfigData>(EntitySkinConfigData.Parser.ParseFrom, d => d.Id, l => m_entitySkinMap = l.map));
            m_loaderMap.Add("EntityPerformance", new ProtoDataLoader<EntityPerformanceConfigData>(EntityPerformanceConfigData.Parser.ParseFrom, d => d.Id, l => m_entityPerformanceMap = l.map));
            m_loaderMap.Add("DialogBubbleGroup", new ProtoDataLoader<DialogBubbleGroupConfigData>(DialogBubbleGroupConfigData.Parser.ParseFrom, d => d.Id, l => m_dialogBubbleGroupMap = l.map));
            m_loaderMap.Add("DialogBubble", new ProtoDataLoader<DialogBubbleConfigData>(DialogBubbleConfigData.Parser.ParseFrom, d => d.Id, l => m_dialogBubbleMap = l.map));
            m_loaderMap.Add("DialogBubbleGlobalTrigger", new ProtoDataLoader<DialogBubbleGlobalTriggerConfigData>(DialogBubbleGlobalTriggerConfigData.Parser.ParseFrom, d => d.Id, l => m_dialogBubbleGlobalTriggerMap = l.map));
            m_loaderMap.Add("CollectableActor", new ProtoDataLoader<CollectableActorConfigData>(CollectableActorConfigData.Parser.ParseFrom, d => d.Id, l => m_collectableActorMap = l.map));
        }
        
        //PhoenixWorldPoint
        private Dictionary<int, PhoenixWorldPointConfigData> m_phoenixWorldPointMap = new Dictionary<int, PhoenixWorldPointConfigData>();
        public PhoenixWorldPointConfigData GetPhoenixWorldPoint(int id) { PhoenixWorldPointConfigData data; m_phoenixWorldPointMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, PhoenixWorldPointConfigData> phoenixWorldPointMap { get { return m_phoenixWorldPointMap; } }
        public int phoenixWorldPointCount { get { return m_phoenixWorldPointMap.Count; } }
        
        //PhoenixWorldPointStory
        private Dictionary<int, PhoenixWorldPointStoryConfigData> m_phoenixWorldPointStoryMap = new Dictionary<int, PhoenixWorldPointStoryConfigData>();
        public PhoenixWorldPointStoryConfigData GetPhoenixWorldPointStory(int id) { PhoenixWorldPointStoryConfigData data; m_phoenixWorldPointStoryMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, PhoenixWorldPointStoryConfigData> phoenixWorldPointStoryMap { get { return m_phoenixWorldPointStoryMap; } }
        public int phoenixWorldPointStoryCount { get { return m_phoenixWorldPointStoryMap.Count; } }
        
        //PhoenixHakoniwa
        private Dictionary<int, PhoenixHakoniwaConfigData> m_phoenixHakoniwaMap = new Dictionary<int, PhoenixHakoniwaConfigData>();
        public PhoenixHakoniwaConfigData GetPhoenixHakoniwa(int id) { PhoenixHakoniwaConfigData data; m_phoenixHakoniwaMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, PhoenixHakoniwaConfigData> phoenixHakoniwaMap { get { return m_phoenixHakoniwaMap; } }
        public int phoenixHakoniwaCount { get { return m_phoenixHakoniwaMap.Count; } }
        
        //PhoenixHakoniwaQuestGraph
        private Dictionary<int, PhoenixHakoniwaQuestGraphConfigData> m_phoenixHakoniwaQuestGraphMap = new Dictionary<int, PhoenixHakoniwaQuestGraphConfigData>();
        public PhoenixHakoniwaQuestGraphConfigData GetPhoenixHakoniwaQuestGraph(int id) { PhoenixHakoniwaQuestGraphConfigData data; m_phoenixHakoniwaQuestGraphMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, PhoenixHakoniwaQuestGraphConfigData> phoenixHakoniwaQuestGraphMap { get { return m_phoenixHakoniwaQuestGraphMap; } }
        public int phoenixHakoniwaQuestGraphCount { get { return m_phoenixHakoniwaQuestGraphMap.Count; } }
        
        //HakoniwaLocation
        private Dictionary<int, HakoniwaLocationConfigData> m_hakoniwaLocationMap = new Dictionary<int, HakoniwaLocationConfigData>();
        public HakoniwaLocationConfigData GetHakoniwaLocation(int id) { HakoniwaLocationConfigData data; m_hakoniwaLocationMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, HakoniwaLocationConfigData> hakoniwaLocationMap { get { return m_hakoniwaLocationMap; } }
        public int hakoniwaLocationCount { get { return m_hakoniwaLocationMap.Count; } }
        
        //HakoniwaScene
        private Dictionary<int, HakoniwaSceneConfigData> m_hakoniwaSceneMap = new Dictionary<int, HakoniwaSceneConfigData>();
        public HakoniwaSceneConfigData GetHakoniwaScene(int id) { HakoniwaSceneConfigData data; m_hakoniwaSceneMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, HakoniwaSceneConfigData> hakoniwaSceneMap { get { return m_hakoniwaSceneMap; } }
        public int hakoniwaSceneCount { get { return m_hakoniwaSceneMap.Count; } }
        
        //HakoniwaStory
        private Dictionary<int, HakoniwaStoryConfigData> m_hakoniwaStoryMap = new Dictionary<int, HakoniwaStoryConfigData>();
        public HakoniwaStoryConfigData GetHakoniwaStory(int id) { HakoniwaStoryConfigData data; m_hakoniwaStoryMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, HakoniwaStoryConfigData> hakoniwaStoryMap { get { return m_hakoniwaStoryMap; } }
        public int hakoniwaStoryCount { get { return m_hakoniwaStoryMap.Count; } }
        
        //HakoniwaEntity
        private Dictionary<int, HakoniwaEntityConfigData> m_hakoniwaEntityMap = new Dictionary<int, HakoniwaEntityConfigData>();
        public HakoniwaEntityConfigData GetHakoniwaEntity(int id) { HakoniwaEntityConfigData data; m_hakoniwaEntityMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, HakoniwaEntityConfigData> hakoniwaEntityMap { get { return m_hakoniwaEntityMap; } }
        public int hakoniwaEntityCount { get { return m_hakoniwaEntityMap.Count; } }
        
        //HakoniwaEntityInteraction
        private Dictionary<int, HakoniwaEntityInteractionConfigData> m_hakoniwaEntityInteractionMap = new Dictionary<int, HakoniwaEntityInteractionConfigData>();
        public HakoniwaEntityInteractionConfigData GetHakoniwaEntityInteraction(int id) { HakoniwaEntityInteractionConfigData data; m_hakoniwaEntityInteractionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, HakoniwaEntityInteractionConfigData> hakoniwaEntityInteractionMap { get { return m_hakoniwaEntityInteractionMap; } }
        public int hakoniwaEntityInteractionCount { get { return m_hakoniwaEntityInteractionMap.Count; } }
        
        //SpriteAtlas
        private Dictionary<int, SpriteAtlasConfigData> m_spriteAtlasMap = new Dictionary<int, SpriteAtlasConfigData>();
        public SpriteAtlasConfigData GetSpriteAtlas(SpriteAtlasId id) { SpriteAtlasConfigData data; m_spriteAtlasMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, SpriteAtlasConfigData> spriteAtlasMap { get { return m_spriteAtlasMap; } }
        public int spriteAtlasCount { get { return m_spriteAtlasMap.Count; } }
        
        //ConstString
        private Dictionary<int, ConstStringConfigData> m_constStringMap = new Dictionary<int, ConstStringConfigData>();
        public ConstStringConfigData GetConstString(ConstStringId id) { ConstStringConfigData data; m_constStringMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, ConstStringConfigData> constStringMap { get { return m_constStringMap; } }
        public int constStringCount { get { return m_constStringMap.Count; } }
        
        //Number
        private Dictionary<int, NumberConfigData> m_numberMap = new Dictionary<int, NumberConfigData>();
        public NumberConfigData GetNumber(int id) { NumberConfigData data; m_numberMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, NumberConfigData> numberMap { get { return m_numberMap; } }
        public int numberCount { get { return m_numberMap.Count; } }
        
        //Quest
        private Dictionary<int, QuestConfigData> m_questMap = new Dictionary<int, QuestConfigData>();
        public QuestConfigData GetQuest(int id) { QuestConfigData data; m_questMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, QuestConfigData> questMap { get { return m_questMap; } }
        public int questCount { get { return m_questMap.Count; } }
        
        //QuestAction
        private Dictionary<int, QuestActionConfigData> m_questActionMap = new Dictionary<int, QuestActionConfigData>();
        public QuestActionConfigData GetQuestAction(int id) { QuestActionConfigData data; m_questActionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, QuestActionConfigData> questActionMap { get { return m_questActionMap; } }
        public int questActionCount { get { return m_questActionMap.Count; } }
        
        //QuestGroup
        private Dictionary<int, QuestGroupConfigData> m_questGroupMap = new Dictionary<int, QuestGroupConfigData>();
        public QuestGroupConfigData GetQuestGroup(int id) { QuestGroupConfigData data; m_questGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, QuestGroupConfigData> questGroupMap { get { return m_questGroupMap; } }
        public int questGroupCount { get { return m_questGroupMap.Count; } }
        
        //TimelineInfo
        private Dictionary<int, TimelineInfoConfigData> m_timelineInfoMap = new Dictionary<int, TimelineInfoConfigData>();
        public TimelineInfoConfigData GetTimelineInfo(int id) { TimelineInfoConfigData data; m_timelineInfoMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TimelineInfoConfigData> timelineInfoMap { get { return m_timelineInfoMap; } }
        public int timelineInfoCount { get { return m_timelineInfoMap.Count; } }
        
        //Dialog
        private Dictionary<int, DialogConfigData> m_dialogMap = new Dictionary<int, DialogConfigData>();
        public DialogConfigData GetDialog(int id) { DialogConfigData data; m_dialogMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DialogConfigData> dialogMap { get { return m_dialogMap; } }
        public int dialogCount { get { return m_dialogMap.Count; } }
        
        //DialogBG
        private Dictionary<int, DialogBGConfigData> m_dialogBGMap = new Dictionary<int, DialogBGConfigData>();
        public DialogBGConfigData GetDialogBG(int id) { DialogBGConfigData data; m_dialogBGMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DialogBGConfigData> dialogBGMap { get { return m_dialogBGMap; } }
        public int dialogBGCount { get { return m_dialogBGMap.Count; } }
        
        //DialogEffect
        private Dictionary<int, DialogEffectConfigData> m_dialogEffectMap = new Dictionary<int, DialogEffectConfigData>();
        public DialogEffectConfigData GetDialogEffect(int id) { DialogEffectConfigData data; m_dialogEffectMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DialogEffectConfigData> dialogEffectMap { get { return m_dialogEffectMap; } }
        public int dialogEffectCount { get { return m_dialogEffectMap.Count; } }
        
        //SelectionGroup
        private Dictionary<int, SelectionGroupConfigData> m_selectionGroupMap = new Dictionary<int, SelectionGroupConfigData>();
        public SelectionGroupConfigData GetSelectionGroup(int id) { SelectionGroupConfigData data; m_selectionGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, SelectionGroupConfigData> selectionGroupMap { get { return m_selectionGroupMap; } }
        public int selectionGroupCount { get { return m_selectionGroupMap.Count; } }
        
        //Selection
        private Dictionary<int, SelectionConfigData> m_selectionMap = new Dictionary<int, SelectionConfigData>();
        public SelectionConfigData GetSelection(int id) { SelectionConfigData data; m_selectionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, SelectionConfigData> selectionMap { get { return m_selectionMap; } }
        public int selectionCount { get { return m_selectionMap.Count; } }
        
        //GuideBattle
        private Dictionary<int, GuideBattleConfigData> m_guideBattleMap = new Dictionary<int, GuideBattleConfigData>();
        public GuideBattleConfigData GetGuideBattle(int id) { GuideBattleConfigData data; m_guideBattleMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, GuideBattleConfigData> guideBattleMap { get { return m_guideBattleMap; } }
        public int guideBattleCount { get { return m_guideBattleMap.Count; } }
        
        //TeamDecision
        private Dictionary<int, TeamDecisionConfigData> m_teamDecisionMap = new Dictionary<int, TeamDecisionConfigData>();
        public TeamDecisionConfigData GetTeamDecision(TeamDecisionId id) { TeamDecisionConfigData data; m_teamDecisionMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, TeamDecisionConfigData> teamDecisionMap { get { return m_teamDecisionMap; } }
        public int teamDecisionCount { get { return m_teamDecisionMap.Count; } }
        
        //TeamDecisionItem
        private Dictionary<int, TeamDecisionItemConfigData> m_teamDecisionItemMap = new Dictionary<int, TeamDecisionItemConfigData>();
        public TeamDecisionItemConfigData GetTeamDecisionItem(int id) { TeamDecisionItemConfigData data; m_teamDecisionItemMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TeamDecisionItemConfigData> teamDecisionItemMap { get { return m_teamDecisionItemMap; } }
        public int teamDecisionItemCount { get { return m_teamDecisionItemMap.Count; } }
        
        //TeamDecisionOriginSelect
        private Dictionary<int, TeamDecisionOriginSelectConfigData> m_teamDecisionOriginSelectMap = new Dictionary<int, TeamDecisionOriginSelectConfigData>();
        public TeamDecisionOriginSelectConfigData GetTeamDecisionOriginSelect(TeamDecisionOriginSelectId id) { TeamDecisionOriginSelectConfigData data; m_teamDecisionOriginSelectMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, TeamDecisionOriginSelectConfigData> teamDecisionOriginSelectMap { get { return m_teamDecisionOriginSelectMap; } }
        public int teamDecisionOriginSelectCount { get { return m_teamDecisionOriginSelectMap.Count; } }
        
        //TeamDecisionCondition
        private Dictionary<int, TeamDecisionConditionConfigData> m_teamDecisionConditionMap = new Dictionary<int, TeamDecisionConditionConfigData>();
        public TeamDecisionConditionConfigData GetTeamDecisionCondition(int id) { TeamDecisionConditionConfigData data; m_teamDecisionConditionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TeamDecisionConditionConfigData> teamDecisionConditionMap { get { return m_teamDecisionConditionMap; } }
        public int teamDecisionConditionCount { get { return m_teamDecisionConditionMap.Count; } }
        
        //TeamDecisionConditionFunc
        private Dictionary<int, TeamDecisionConditionFuncConfigData> m_teamDecisionConditionFuncMap = new Dictionary<int, TeamDecisionConditionFuncConfigData>();
        public TeamDecisionConditionFuncConfigData GetTeamDecisionConditionFunc(TeamDecisionConditionFuncId id) { TeamDecisionConditionFuncConfigData data; m_teamDecisionConditionFuncMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, TeamDecisionConditionFuncConfigData> teamDecisionConditionFuncMap { get { return m_teamDecisionConditionFuncMap; } }
        public int teamDecisionConditionFuncCount { get { return m_teamDecisionConditionFuncMap.Count; } }
        
        //TeamDecisionMark
        private Dictionary<int, TeamDecisionMarkConfigData> m_teamDecisionMarkMap = new Dictionary<int, TeamDecisionMarkConfigData>();
        public TeamDecisionMarkConfigData GetTeamDecisionMark(TeamDecisionMarkId id) { TeamDecisionMarkConfigData data; m_teamDecisionMarkMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, TeamDecisionMarkConfigData> teamDecisionMarkMap { get { return m_teamDecisionMarkMap; } }
        public int teamDecisionMarkCount { get { return m_teamDecisionMarkMap.Count; } }
        
        //TeamDecisionLocateSelect
        private Dictionary<int, TeamDecisionLocateSelectConfigData> m_teamDecisionLocateSelectMap = new Dictionary<int, TeamDecisionLocateSelectConfigData>();
        public TeamDecisionLocateSelectConfigData GetTeamDecisionLocateSelect(int id) { TeamDecisionLocateSelectConfigData data; m_teamDecisionLocateSelectMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TeamDecisionLocateSelectConfigData> teamDecisionLocateSelectMap { get { return m_teamDecisionLocateSelectMap; } }
        public int teamDecisionLocateSelectCount { get { return m_teamDecisionLocateSelectMap.Count; } }
        
        //BattleTerrain
        private Dictionary<int, BattleTerrainConfigData> m_battleTerrainMap = new Dictionary<int, BattleTerrainConfigData>();
        public BattleTerrainConfigData GetBattleTerrain(BattleTerrainId id) { BattleTerrainConfigData data; m_battleTerrainMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, BattleTerrainConfigData> battleTerrainMap { get { return m_battleTerrainMap; } }
        public int battleTerrainCount { get { return m_battleTerrainMap.Count; } }
        
        //TerrainBuff
        private Dictionary<int, TerrainBuffConfigData> m_terrainBuffMap = new Dictionary<int, TerrainBuffConfigData>();
        public TerrainBuffConfigData GetTerrainBuff(int id) { TerrainBuffConfigData data; m_terrainBuffMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TerrainBuffConfigData> terrainBuffMap { get { return m_terrainBuffMap; } }
        public int terrainBuffCount { get { return m_terrainBuffMap.Count; } }
        
        //Attribute
        private Dictionary<int, AttributeConfigData> m_attributeMap = new Dictionary<int, AttributeConfigData>();
        public AttributeConfigData GetAttribute(AttributeId id) { AttributeConfigData data; m_attributeMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, AttributeConfigData> attributeMap { get { return m_attributeMap; } }
        public int attributeCount { get { return m_attributeMap.Count; } }
        
        //AttributePart
        private Dictionary<int, AttributePartConfigData> m_attributePartMap = new Dictionary<int, AttributePartConfigData>();
        public AttributePartConfigData GetAttributePart(AttributePartId id) { AttributePartConfigData data; m_attributePartMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, AttributePartConfigData> attributePartMap { get { return m_attributePartMap; } }
        public int attributePartCount { get { return m_attributePartMap.Count; } }
        
        //BattleConstValue
        private Dictionary<int, BattleConstValueConfigData> m_battleConstValueMap = new Dictionary<int, BattleConstValueConfigData>();
        public BattleConstValueConfigData GetBattleConstValue(BattleConstValueId id) { BattleConstValueConfigData data; m_battleConstValueMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, BattleConstValueConfigData> battleConstValueMap { get { return m_battleConstValueMap; } }
        public int battleConstValueCount { get { return m_battleConstValueMap.Count; } }
        
        //PassiveSkill
        private Dictionary<int, PassiveSkillConfigData> m_passiveSkillMap = new Dictionary<int, PassiveSkillConfigData>();
        public PassiveSkillConfigData GetPassiveSkill(int id) { PassiveSkillConfigData data; m_passiveSkillMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, PassiveSkillConfigData> passiveSkillMap { get { return m_passiveSkillMap; } }
        public int passiveSkillCount { get { return m_passiveSkillMap.Count; } }
        
        //Actor
        private Dictionary<int, ActorConfigData> m_actorMap = new Dictionary<int, ActorConfigData>();
        public ActorConfigData GetActor(int id) { ActorConfigData data; m_actorMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ActorConfigData> actorMap { get { return m_actorMap; } }
        public int actorCount { get { return m_actorMap.Count; } }
        
        //EntityBlockGroup
        private Dictionary<int, EntityBlockGroupConfigData> m_entityBlockGroupMap = new Dictionary<int, EntityBlockGroupConfigData>();
        public EntityBlockGroupConfigData GetEntityBlockGroup(int id) { EntityBlockGroupConfigData data; m_entityBlockGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, EntityBlockGroupConfigData> entityBlockGroupMap { get { return m_entityBlockGroupMap; } }
        public int entityBlockGroupCount { get { return m_entityBlockGroupMap.Count; } }
        
        //EntityBlock
        private Dictionary<int, EntityBlockConfigData> m_entityBlockMap = new Dictionary<int, EntityBlockConfigData>();
        public EntityBlockConfigData GetEntityBlock(int id) { EntityBlockConfigData data; m_entityBlockMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, EntityBlockConfigData> entityBlockMap { get { return m_entityBlockMap; } }
        public int entityBlockCount { get { return m_entityBlockMap.Count; } }
        
        //EntityRarity
        private Dictionary<int, EntityRarityConfigData> m_entityRarityMap = new Dictionary<int, EntityRarityConfigData>();
        public EntityRarityConfigData GetEntityRarity(EntityRarityId id) { EntityRarityConfigData data; m_entityRarityMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, EntityRarityConfigData> entityRarityMap { get { return m_entityRarityMap; } }
        public int entityRarityCount { get { return m_entityRarityMap.Count; } }
        
        //EntityElement
        private Dictionary<int, EntityElementConfigData> m_entityElementMap = new Dictionary<int, EntityElementConfigData>();
        public EntityElementConfigData GetEntityElement(EntityElementId id) { EntityElementConfigData data; m_entityElementMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, EntityElementConfigData> entityElementMap { get { return m_entityElementMap; } }
        public int entityElementCount { get { return m_entityElementMap.Count; } }
        
        //EntityCareer
        private Dictionary<int, EntityCareerConfigData> m_entityCareerMap = new Dictionary<int, EntityCareerConfigData>();
        public EntityCareerConfigData GetEntityCareer(EntityCareerId id) { EntityCareerConfigData data; m_entityCareerMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, EntityCareerConfigData> entityCareerMap { get { return m_entityCareerMap; } }
        public int entityCareerCount { get { return m_entityCareerMap.Count; } }
        
        //EntityRace
        private Dictionary<int, EntityRaceConfigData> m_entityRaceMap = new Dictionary<int, EntityRaceConfigData>();
        public EntityRaceConfigData GetEntityRace(EntityRaceId id) { EntityRaceConfigData data; m_entityRaceMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, EntityRaceConfigData> entityRaceMap { get { return m_entityRaceMap; } }
        public int entityRaceCount { get { return m_entityRaceMap.Count; } }
        
        //EntityGender
        private Dictionary<int, EntityGenderConfigData> m_entityGenderMap = new Dictionary<int, EntityGenderConfigData>();
        public EntityGenderConfigData GetEntityGender(EntityGenderId id) { EntityGenderConfigData data; m_entityGenderMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, EntityGenderConfigData> entityGenderMap { get { return m_entityGenderMap; } }
        public int entityGenderCount { get { return m_entityGenderMap.Count; } }
        
        //EntityMoveRule
        private Dictionary<int, EntityMoveRuleConfigData> m_entityMoveRuleMap = new Dictionary<int, EntityMoveRuleConfigData>();
        public EntityMoveRuleConfigData GetEntityMoveRule(EntityMoveRuleId id) { EntityMoveRuleConfigData data; m_entityMoveRuleMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, EntityMoveRuleConfigData> entityMoveRuleMap { get { return m_entityMoveRuleMap; } }
        public int entityMoveRuleCount { get { return m_entityMoveRuleMap.Count; } }
        
        //ActorPassiveFormation
        private Dictionary<int, ActorPassiveFormationConfigData> m_actorPassiveFormationMap = new Dictionary<int, ActorPassiveFormationConfigData>();
        public ActorPassiveFormationConfigData GetActorPassiveFormation(int id) { ActorPassiveFormationConfigData data; m_actorPassiveFormationMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ActorPassiveFormationConfigData> actorPassiveFormationMap { get { return m_actorPassiveFormationMap; } }
        public int actorPassiveFormationCount { get { return m_actorPassiveFormationMap.Count; } }
        
        //TargetSelectRange
        private Dictionary<int, TargetSelectRangeConfigData> m_targetSelectRangeMap = new Dictionary<int, TargetSelectRangeConfigData>();
        public TargetSelectRangeConfigData GetTargetSelectRange(TargetSelectRangeId id) { TargetSelectRangeConfigData data; m_targetSelectRangeMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, TargetSelectRangeConfigData> targetSelectRangeMap { get { return m_targetSelectRangeMap; } }
        public int targetSelectRangeCount { get { return m_targetSelectRangeMap.Count; } }
        
        //BattleCamp
        private Dictionary<int, BattleCampConfigData> m_battleCampMap = new Dictionary<int, BattleCampConfigData>();
        public BattleCampConfigData GetBattleCamp(int id) { BattleCampConfigData data; m_battleCampMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, BattleCampConfigData> battleCampMap { get { return m_battleCampMap; } }
        public int battleCampCount { get { return m_battleCampMap.Count; } }
        
        //BattleFormation
        private Dictionary<int, BattleFormationConfigData> m_battleFormationMap = new Dictionary<int, BattleFormationConfigData>();
        public BattleFormationConfigData GetBattleFormation(int id) { BattleFormationConfigData data; m_battleFormationMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, BattleFormationConfigData> battleFormationMap { get { return m_battleFormationMap; } }
        public int battleFormationCount { get { return m_battleFormationMap.Count; } }
        
        //BattleFormationEffect
        private Dictionary<int, BattleFormationEffectConfigData> m_battleFormationEffectMap = new Dictionary<int, BattleFormationEffectConfigData>();
        public BattleFormationEffectConfigData GetBattleFormationEffect(int id) { BattleFormationEffectConfigData data; m_battleFormationEffectMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, BattleFormationEffectConfigData> battleFormationEffectMap { get { return m_battleFormationEffectMap; } }
        public int battleFormationEffectCount { get { return m_battleFormationEffectMap.Count; } }
        
        //BattleFormationCondition
        private Dictionary<int, BattleFormationConditionConfigData> m_battleFormationConditionMap = new Dictionary<int, BattleFormationConditionConfigData>();
        public BattleFormationConditionConfigData GetBattleFormationCondition(int id) { BattleFormationConditionConfigData data; m_battleFormationConditionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, BattleFormationConditionConfigData> battleFormationConditionMap { get { return m_battleFormationConditionMap; } }
        public int battleFormationConditionCount { get { return m_battleFormationConditionMap.Count; } }
        
        //UserGuideGroup
        private Dictionary<int, UserGuideGroupConfigData> m_userGuideGroupMap = new Dictionary<int, UserGuideGroupConfigData>();
        public UserGuideGroupConfigData GetUserGuideGroup(int id) { UserGuideGroupConfigData data; m_userGuideGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, UserGuideGroupConfigData> userGuideGroupMap { get { return m_userGuideGroupMap; } }
        public int userGuideGroupCount { get { return m_userGuideGroupMap.Count; } }
        
        //UserGuideTipStr
        private Dictionary<int, UserGuideTipStrConfigData> m_userGuideTipStrMap = new Dictionary<int, UserGuideTipStrConfigData>();
        public UserGuideTipStrConfigData GetUserGuideTipStr(int id) { UserGuideTipStrConfigData data; m_userGuideTipStrMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, UserGuideTipStrConfigData> userGuideTipStrMap { get { return m_userGuideTipStrMap; } }
        public int userGuideTipStrCount { get { return m_userGuideTipStrMap.Count; } }
        
        //UserGuideGraphic
        private Dictionary<int, UserGuideGraphicConfigData> m_userGuideGraphicMap = new Dictionary<int, UserGuideGraphicConfigData>();
        public UserGuideGraphicConfigData GetUserGuideGraphic(int id) { UserGuideGraphicConfigData data; m_userGuideGraphicMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, UserGuideGraphicConfigData> userGuideGraphicMap { get { return m_userGuideGraphicMap; } }
        public int userGuideGraphicCount { get { return m_userGuideGraphicMap.Count; } }
        
        //TestBattle
        private Dictionary<int, TestBattleConfigData> m_testBattleMap = new Dictionary<int, TestBattleConfigData>();
        public TestBattleConfigData GetTestBattle(int id) { TestBattleConfigData data; m_testBattleMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TestBattleConfigData> testBattleMap { get { return m_testBattleMap; } }
        public int testBattleCount { get { return m_testBattleMap.Count; } }
        
        //Item
        private Dictionary<int, ItemConfigData> m_itemMap = new Dictionary<int, ItemConfigData>();
        public ItemConfigData GetItem(int id) { ItemConfigData data; m_itemMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ItemConfigData> itemMap { get { return m_itemMap; } }
        public int itemCount { get { return m_itemMap.Count; } }
        
        //DropGroup
        private Dictionary<int, DropGroupConfigData> m_dropGroupMap = new Dictionary<int, DropGroupConfigData>();
        public DropGroupConfigData GetDropGroup(int id) { DropGroupConfigData data; m_dropGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DropGroupConfigData> dropGroupMap { get { return m_dropGroupMap; } }
        public int dropGroupCount { get { return m_dropGroupMap.Count; } }
        
        //Particle
        private Dictionary<int, ParticleConfigData> m_particleMap = new Dictionary<int, ParticleConfigData>();
        public ParticleConfigData GetParticle(int id) { ParticleConfigData data; m_particleMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ParticleConfigData> particleMap { get { return m_particleMap; } }
        public int particleCount { get { return m_particleMap.Count; } }
        
        //World
        private Dictionary<int, WorldConfigData> m_worldMap = new Dictionary<int, WorldConfigData>();
        public WorldConfigData GetWorld(int id) { WorldConfigData data; m_worldMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldConfigData> worldMap { get { return m_worldMap; } }
        public int worldCount { get { return m_worldMap.Count; } }
        
        //WorldState
        private Dictionary<int, WorldStateConfigData> m_worldStateMap = new Dictionary<int, WorldStateConfigData>();
        public WorldStateConfigData GetWorldState(int id) { WorldStateConfigData data; m_worldStateMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldStateConfigData> worldStateMap { get { return m_worldStateMap; } }
        public int worldStateCount { get { return m_worldStateMap.Count; } }
        
        //WorldActionGroup
        private Dictionary<int, WorldActionGroupConfigData> m_worldActionGroupMap = new Dictionary<int, WorldActionGroupConfigData>();
        public WorldActionGroupConfigData GetWorldActionGroup(int id) { WorldActionGroupConfigData data; m_worldActionGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldActionGroupConfigData> worldActionGroupMap { get { return m_worldActionGroupMap; } }
        public int worldActionGroupCount { get { return m_worldActionGroupMap.Count; } }
        
        //Transformation
        private Dictionary<int, TransformationConfigData> m_transformationMap = new Dictionary<int, TransformationConfigData>();
        public TransformationConfigData GetTransformation(int id) { TransformationConfigData data; m_transformationMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, TransformationConfigData> transformationMap { get { return m_transformationMap; } }
        public int transformationCount { get { return m_transformationMap.Count; } }
        
        //Favor
        private Dictionary<int, FavorConfigData> m_favorMap = new Dictionary<int, FavorConfigData>();
        public FavorConfigData GetFavor(int id) { FavorConfigData data; m_favorMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, FavorConfigData> favorMap { get { return m_favorMap; } }
        public int favorCount { get { return m_favorMap.Count; } }
        
        //WorldBattle
        private Dictionary<int, WorldBattleConfigData> m_worldBattleMap = new Dictionary<int, WorldBattleConfigData>();
        public WorldBattleConfigData GetWorldBattle(int id) { WorldBattleConfigData data; m_worldBattleMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldBattleConfigData> worldBattleMap { get { return m_worldBattleMap; } }
        public int worldBattleCount { get { return m_worldBattleMap.Count; } }
        
        //ExploreSystem
        private Dictionary<int, ExploreSystemConfigData> m_exploreSystemMap = new Dictionary<int, ExploreSystemConfigData>();
        public ExploreSystemConfigData GetExploreSystem(int id) { ExploreSystemConfigData data; m_exploreSystemMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ExploreSystemConfigData> exploreSystemMap { get { return m_exploreSystemMap; } }
        public int exploreSystemCount { get { return m_exploreSystemMap.Count; } }
        
        //ExploreClue
        private Dictionary<int, ExploreClueConfigData> m_exploreClueMap = new Dictionary<int, ExploreClueConfigData>();
        public ExploreClueConfigData GetExploreClue(int id) { ExploreClueConfigData data; m_exploreClueMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ExploreClueConfigData> exploreClueMap { get { return m_exploreClueMap; } }
        public int exploreClueCount { get { return m_exploreClueMap.Count; } }
        
        //ExploreString
        private Dictionary<int, ExploreStringConfigData> m_exploreStringMap = new Dictionary<int, ExploreStringConfigData>();
        public ExploreStringConfigData GetExploreString(ExploreStringId id) { ExploreStringConfigData data; m_exploreStringMap.TryGetValue((int)id, out data); return data; }
        public Dictionary<int, ExploreStringConfigData> exploreStringMap { get { return m_exploreStringMap; } }
        public int exploreStringCount { get { return m_exploreStringMap.Count; } }
        
        //Exchange
        private Dictionary<int, ExchangeConfigData> m_exchangeMap = new Dictionary<int, ExchangeConfigData>();
        public ExchangeConfigData GetExchange(int id) { ExchangeConfigData data; m_exchangeMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, ExchangeConfigData> exchangeMap { get { return m_exchangeMap; } }
        public int exchangeCount { get { return m_exchangeMap.Count; } }
        
        //WorldIntroduction
        private Dictionary<int, WorldIntroductionConfigData> m_worldIntroductionMap = new Dictionary<int, WorldIntroductionConfigData>();
        public WorldIntroductionConfigData GetWorldIntroduction(int id) { WorldIntroductionConfigData data; m_worldIntroductionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldIntroductionConfigData> worldIntroductionMap { get { return m_worldIntroductionMap; } }
        public int worldIntroductionCount { get { return m_worldIntroductionMap.Count; } }
        
        //WorldEntity
        private Dictionary<int, WorldEntityConfigData> m_worldEntityMap = new Dictionary<int, WorldEntityConfigData>();
        public WorldEntityConfigData GetWorldEntity(int id) { WorldEntityConfigData data; m_worldEntityMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldEntityConfigData> worldEntityMap { get { return m_worldEntityMap; } }
        public int worldEntityCount { get { return m_worldEntityMap.Count; } }
        
        //EntityDialog
        private Dictionary<int, EntityDialogConfigData> m_entityDialogMap = new Dictionary<int, EntityDialogConfigData>();
        public EntityDialogConfigData GetEntityDialog(int id) { EntityDialogConfigData data; m_entityDialogMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, EntityDialogConfigData> entityDialogMap { get { return m_entityDialogMap; } }
        public int entityDialogCount { get { return m_entityDialogMap.Count; } }
        
        //EntityInteraction
        private Dictionary<int, EntityInteractionConfigData> m_entityInteractionMap = new Dictionary<int, EntityInteractionConfigData>();
        public EntityInteractionConfigData GetEntityInteraction(int id) { EntityInteractionConfigData data; m_entityInteractionMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, EntityInteractionConfigData> entityInteractionMap { get { return m_entityInteractionMap; } }
        public int entityInteractionCount { get { return m_entityInteractionMap.Count; } }
        
        //WorldEntityInformation
        private Dictionary<int, WorldEntityInformationConfigData> m_worldEntityInformationMap = new Dictionary<int, WorldEntityInformationConfigData>();
        public WorldEntityInformationConfigData GetWorldEntityInformation(int id) { WorldEntityInformationConfigData data; m_worldEntityInformationMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, WorldEntityInformationConfigData> worldEntityInformationMap { get { return m_worldEntityInformationMap; } }
        public int worldEntityInformationCount { get { return m_worldEntityInformationMap.Count; } }
        
        //Entity2DSkin
        private Dictionary<int, Entity2DSkinConfigData> m_entity2DSkinMap = new Dictionary<int, Entity2DSkinConfigData>();
        public Entity2DSkinConfigData GetEntity2DSkin(int id) { Entity2DSkinConfigData data; m_entity2DSkinMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, Entity2DSkinConfigData> entity2DSkinMap { get { return m_entity2DSkinMap; } }
        public int entity2DSkinCount { get { return m_entity2DSkinMap.Count; } }
        
        //EntitySkin
        private Dictionary<int, EntitySkinConfigData> m_entitySkinMap = new Dictionary<int, EntitySkinConfigData>();
        public EntitySkinConfigData GetEntitySkin(int id) { EntitySkinConfigData data; m_entitySkinMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, EntitySkinConfigData> entitySkinMap { get { return m_entitySkinMap; } }
        public int entitySkinCount { get { return m_entitySkinMap.Count; } }
        
        //EntityPerformance
        private Dictionary<int, EntityPerformanceConfigData> m_entityPerformanceMap = new Dictionary<int, EntityPerformanceConfigData>();
        public EntityPerformanceConfigData GetEntityPerformance(int id) { EntityPerformanceConfigData data; m_entityPerformanceMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, EntityPerformanceConfigData> entityPerformanceMap { get { return m_entityPerformanceMap; } }
        public int entityPerformanceCount { get { return m_entityPerformanceMap.Count; } }
        
        //DialogBubbleGroup
        private Dictionary<int, DialogBubbleGroupConfigData> m_dialogBubbleGroupMap = new Dictionary<int, DialogBubbleGroupConfigData>();
        public DialogBubbleGroupConfigData GetDialogBubbleGroup(int id) { DialogBubbleGroupConfigData data; m_dialogBubbleGroupMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DialogBubbleGroupConfigData> dialogBubbleGroupMap { get { return m_dialogBubbleGroupMap; } }
        public int dialogBubbleGroupCount { get { return m_dialogBubbleGroupMap.Count; } }
        
        //DialogBubble
        private Dictionary<int, DialogBubbleConfigData> m_dialogBubbleMap = new Dictionary<int, DialogBubbleConfigData>();
        public DialogBubbleConfigData GetDialogBubble(int id) { DialogBubbleConfigData data; m_dialogBubbleMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DialogBubbleConfigData> dialogBubbleMap { get { return m_dialogBubbleMap; } }
        public int dialogBubbleCount { get { return m_dialogBubbleMap.Count; } }
        
        //DialogBubbleGlobalTrigger
        private Dictionary<int, DialogBubbleGlobalTriggerConfigData> m_dialogBubbleGlobalTriggerMap = new Dictionary<int, DialogBubbleGlobalTriggerConfigData>();
        public DialogBubbleGlobalTriggerConfigData GetDialogBubbleGlobalTrigger(int id) { DialogBubbleGlobalTriggerConfigData data; m_dialogBubbleGlobalTriggerMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, DialogBubbleGlobalTriggerConfigData> dialogBubbleGlobalTriggerMap { get { return m_dialogBubbleGlobalTriggerMap; } }
        public int dialogBubbleGlobalTriggerCount { get { return m_dialogBubbleGlobalTriggerMap.Count; } }
        
        //CollectableActor
        private Dictionary<int, CollectableActorConfigData> m_collectableActorMap = new Dictionary<int, CollectableActorConfigData>();
        public CollectableActorConfigData GetCollectableActor(int id) { CollectableActorConfigData data; m_collectableActorMap.TryGetValue(id, out data); return data; }
        public Dictionary<int, CollectableActorConfigData> collectableActorMap { get { return m_collectableActorMap; } }
        public int collectableActorCount { get { return m_collectableActorMap.Count; } }
    }
}
