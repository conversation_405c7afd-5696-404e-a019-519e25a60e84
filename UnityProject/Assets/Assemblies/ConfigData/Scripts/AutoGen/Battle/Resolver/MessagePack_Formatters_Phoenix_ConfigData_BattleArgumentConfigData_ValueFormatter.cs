// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using System.Collections.Generic;
    using MessagePack;

    public sealed class BattleArgumentConfigData_ValueFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Value>
    {
        private readonly Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>> typeToKeyAndJumpMap;
        private readonly Dictionary<int, int> keyToJumpMap;

        public BattleArgumentConfigData_ValueFormatter()
        {
            this.typeToKeyAndJumpMap = new Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>>(7, global::MessagePack.Internal.RuntimeTypeHandleEqualityComparer.Default)
            {
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist).TypeHandle, new KeyValuePair<int, int>(0, 0) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist).TypeHandle, new KeyValuePair<int, int>(1, 1) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex).TypeHandle, new KeyValuePair<int, int>(2, 2) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex).TypeHandle, new KeyValuePair<int, int>(3, 3) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy).TypeHandle, new KeyValuePair<int, int>(4, 4) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate).TypeHandle, new KeyValuePair<int, int>(5, 5) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_EntityCountInRange_Self).TypeHandle, new KeyValuePair<int, int>(6, 6) },
            };
            this.keyToJumpMap = new Dictionary<int, int>(7)
            {
                { 0, 0 },
                { 1, 1 },
                { 2, 2 },
                { 3, 3 },
                { 4, 4 },
                { 5, 5 },
                { 6, 6 },
            };
        }

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleArgumentConfigData_Value value, global::MessagePack.MessagePackSerializerOptions options)
        {
            KeyValuePair<int, int> keyValuePair;
            if (value != null && this.typeToKeyAndJumpMap.TryGetValue(value.GetType().TypeHandle, out keyValuePair))
            {
                writer.WriteArrayHeader(2);
                writer.WriteInt32(keyValuePair.Key);
                switch (keyValuePair.Value)
                {
                    case 0:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist)value, options);
                        break;
                    case 1:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist)value, options);
                        break;
                    case 2:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex)value, options);
                        break;
                    case 3:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex)value, options);
                        break;
                    case 4:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy)value, options);
                        break;
                    case 5:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate)value, options);
                        break;
                    case 6:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_EntityCountInRange_Self>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Value_EntityCountInRange_Self)value, options);
                        break;
                    default:
                        break;
                }

                return;
            }

            writer.WriteNil();
        }

        public global::Phoenix.ConfigData.BattleArgumentConfigData_Value Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            if (reader.ReadArrayHeader() != 2)
            {
                throw new InvalidOperationException("Invalid Union data was detected. Type:global::Phoenix.ConfigData.BattleArgumentConfigData_Value");
            }

            options.Security.DepthStep(ref reader);
            var key = reader.ReadInt32();

            if (!this.keyToJumpMap.TryGetValue(key, out key))
            {
                key = -1;
            }

            global::Phoenix.ConfigData.BattleArgumentConfigData_Value result = null;
            switch (key)
            {
                case 0:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist>().Deserialize(ref reader, options);
                    break;
                case 1:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist>().Deserialize(ref reader, options);
                    break;
                case 2:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex>().Deserialize(ref reader, options);
                    break;
                case 3:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex>().Deserialize(ref reader, options);
                    break;
                case 4:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy>().Deserialize(ref reader, options);
                    break;
                case 5:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate>().Deserialize(ref reader, options);
                    break;
                case 6:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Value)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Value_EntityCountInRange_Self>().Deserialize(ref reader, options);
                    break;
                default:
                    reader.Skip();
                    break;
            }

            reader.Depth--;
            return result;
        }
    }


}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
