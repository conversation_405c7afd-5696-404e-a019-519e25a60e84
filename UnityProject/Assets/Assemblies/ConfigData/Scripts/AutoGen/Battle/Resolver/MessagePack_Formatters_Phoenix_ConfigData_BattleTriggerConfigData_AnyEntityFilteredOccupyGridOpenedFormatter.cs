// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpenedFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpened>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpened value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(3);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>>().Serialize(ref writer, value.conditionList, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity>().Serialize(ref writer, value.entity, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Grid>().Serialize(ref writer, value.grid, options);
        }

        public global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpened Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __entity__ = default(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity);
            var __grid__ = default(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid);
            var __conditionList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 1:
                        __entity__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __grid__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Grid>().Deserialize(ref reader, options);
                        break;
                    case 0:
                        __conditionList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpened();
            ____result.entity = __entity__;
            ____result.grid = __grid__;
            ____result.conditionList = __conditionList__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
