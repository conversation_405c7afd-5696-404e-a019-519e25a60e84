// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleConfigDataFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleConfigData>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleConfigData value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(6);
            writer.Write(value.id);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.name, options);
            writer.Write(value.dispositionGroupId);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.disposedActorList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>>().Serialize(ref writer, value.playerSlotList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageConfigData>>().Serialize(ref writer, value.stageList, options);
        }

        public global::Phoenix.ConfigData.BattleConfigData Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __id__ = default(int);
            var __name__ = default(string);
            var __dispositionGroupId__ = default(int);
            var __disposedActorList__ = default(global::System.Collections.Generic.List<int>);
            var __playerSlotList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>);
            var __stageList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageConfigData>);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __id__ = reader.ReadInt32();
                        break;
                    case 1:
                        __name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __dispositionGroupId__ = reader.ReadInt32();
                        break;
                    case 3:
                        __disposedActorList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 4:
                        __playerSlotList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>>().Deserialize(ref reader, options);
                        break;
                    case 5:
                        __stageList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageConfigData>>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BattleConfigData();
            ____result.id = __id__;
            ____result.name = __name__;
            ____result.dispositionGroupId = __dispositionGroupId__;
            ____result.disposedActorList = __disposedActorList__;
            ____result.playerSlotList = __playerSlotList__;
            ____result.stageList = __stageList__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
