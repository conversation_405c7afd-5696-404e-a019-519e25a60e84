// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using System.Collections.Generic;
    using MessagePack;

    public sealed class BattleStageActionConfigDataFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleStageActionConfigData>
    {
        private readonly Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>> typeToKeyAndJumpMap;
        private readonly Dictionary<int, int> keyToJumpMap;

        public BattleStageActionConfigDataFormatter()
        {
            this.typeToKeyAndJumpMap = new Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>>(37, global::MessagePack.Internal.RuntimeTypeHandleEqualityComparer.Default)
            {
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Sequence).TypeHandle, new KeyValuePair<int, int>(0, 0) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Parallel).TypeHandle, new KeyValuePair<int, int>(1, 1) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Delay).TypeHandle, new KeyValuePair<int, int>(2, 2) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Dialog).TypeHandle, new KeyValuePair<int, int>(3, 3) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection).TypeHandle, new KeyValuePair<int, int>(4, 4) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble).TypeHandle, new KeyValuePair<int, int>(5, 5) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff).TypeHandle, new KeyValuePair<int, int>(6, 6) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(7, 7) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor).TypeHandle, new KeyValuePair<int, int>(8, 8) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(9, 9) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(10, 10) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(11, 11) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(12, 12) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(13, 13) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(14, 14) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(15, 15) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(16, 16) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir).TypeHandle, new KeyValuePair<int, int>(17, 17) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(18, 18) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition).TypeHandle, new KeyValuePair<int, int>(19, 19) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity).TypeHandle, new KeyValuePair<int, int>(20, 20) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow).TypeHandle, new KeyValuePair<int, int>(21, 21) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraShake).TypeHandle, new KeyValuePair<int, int>(22, 22) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect).TypeHandle, new KeyValuePair<int, int>(23, 23) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect).TypeHandle, new KeyValuePair<int, int>(24, 24) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline).TypeHandle, new KeyValuePair<int, int>(25, 25) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlaySound).TypeHandle, new KeyValuePair<int, int>(26, 26) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm).TypeHandle, new KeyValuePair<int, int>(27, 27) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark).TypeHandle, new KeyValuePair<int, int>(28, 28) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled).TypeHandle, new KeyValuePair<int, int>(29, 29) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility).TypeHandle, new KeyValuePair<int, int>(30, 30) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Default).TypeHandle, new KeyValuePair<int, int>(31, 31) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir).TypeHandle, new KeyValuePair<int, int>(32, 32) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos).TypeHandle, new KeyValuePair<int, int>(33, 33) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid).TypeHandle, new KeyValuePair<int, int>(34, 34) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid).TypeHandle, new KeyValuePair<int, int>(35, 35) },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid).TypeHandle, new KeyValuePair<int, int>(36, 36) },
            };
            this.keyToJumpMap = new Dictionary<int, int>(37)
            {
                { 0, 0 },
                { 1, 1 },
                { 2, 2 },
                { 3, 3 },
                { 4, 4 },
                { 5, 5 },
                { 6, 6 },
                { 7, 7 },
                { 8, 8 },
                { 9, 9 },
                { 10, 10 },
                { 11, 11 },
                { 12, 12 },
                { 13, 13 },
                { 14, 14 },
                { 15, 15 },
                { 16, 16 },
                { 17, 17 },
                { 18, 18 },
                { 19, 19 },
                { 20, 20 },
                { 21, 21 },
                { 22, 22 },
                { 23, 23 },
                { 24, 24 },
                { 25, 25 },
                { 26, 26 },
                { 27, 27 },
                { 28, 28 },
                { 29, 29 },
                { 30, 30 },
                { 31, 31 },
                { 32, 32 },
                { 33, 33 },
                { 34, 34 },
                { 35, 35 },
                { 36, 36 },
            };
        }

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleStageActionConfigData value, global::MessagePack.MessagePackSerializerOptions options)
        {
            KeyValuePair<int, int> keyValuePair;
            if (value != null && this.typeToKeyAndJumpMap.TryGetValue(value.GetType().TypeHandle, out keyValuePair))
            {
                writer.WriteArrayHeader(2);
                writer.WriteInt32(keyValuePair.Key);
                switch (keyValuePair.Value)
                {
                    case 0:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Sequence>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Sequence)value, options);
                        break;
                    case 1:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Parallel>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Parallel)value, options);
                        break;
                    case 2:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Delay>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Delay)value, options);
                        break;
                    case 3:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Dialog>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Dialog)value, options);
                        break;
                    case 4:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection)value, options);
                        break;
                    case 5:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble)value, options);
                        break;
                    case 6:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff)value, options);
                        break;
                    case 7:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid)value, options);
                        break;
                    case 8:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor)value, options);
                        break;
                    case 9:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid)value, options);
                        break;
                    case 10:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid)value, options);
                        break;
                    case 11:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid)value, options);
                        break;
                    case 12:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid)value, options);
                        break;
                    case 13:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid)value, options);
                        break;
                    case 14:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid)value, options);
                        break;
                    case 15:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid)value, options);
                        break;
                    case 16:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid)value, options);
                        break;
                    case 17:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir)value, options);
                        break;
                    case 18:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid)value, options);
                        break;
                    case 19:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition)value, options);
                        break;
                    case 20:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity)value, options);
                        break;
                    case 21:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow)value, options);
                        break;
                    case 22:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraShake>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_CameraShake)value, options);
                        break;
                    case 23:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect)value, options);
                        break;
                    case 24:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect)value, options);
                        break;
                    case 25:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline)value, options);
                        break;
                    case 26:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlaySound>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_PlaySound)value, options);
                        break;
                    case 27:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm)value, options);
                        break;
                    case 28:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark)value, options);
                        break;
                    case 29:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled)value, options);
                        break;
                    case 30:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility)value, options);
                        break;
                    case 31:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Default>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Default)value, options);
                        break;
                    case 32:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir)value, options);
                        break;
                    case 33:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos)value, options);
                        break;
                    case 34:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid)value, options);
                        break;
                    case 35:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid)value, options);
                        break;
                    case 36:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid)value, options);
                        break;
                    default:
                        break;
                }

                return;
            }

            writer.WriteNil();
        }

        public global::Phoenix.ConfigData.BattleStageActionConfigData Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            if (reader.ReadArrayHeader() != 2)
            {
                throw new InvalidOperationException("Invalid Union data was detected. Type:global::Phoenix.ConfigData.BattleStageActionConfigData");
            }

            options.Security.DepthStep(ref reader);
            var key = reader.ReadInt32();

            if (!this.keyToJumpMap.TryGetValue(key, out key))
            {
                key = -1;
            }

            global::Phoenix.ConfigData.BattleStageActionConfigData result = null;
            switch (key)
            {
                case 0:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Sequence>().Deserialize(ref reader, options);
                    break;
                case 1:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Parallel>().Deserialize(ref reader, options);
                    break;
                case 2:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Delay>().Deserialize(ref reader, options);
                    break;
                case 3:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Dialog>().Deserialize(ref reader, options);
                    break;
                case 4:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection>().Deserialize(ref reader, options);
                    break;
                case 5:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble>().Deserialize(ref reader, options);
                    break;
                case 6:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff>().Deserialize(ref reader, options);
                    break;
                case 7:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 8:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor>().Deserialize(ref reader, options);
                    break;
                case 9:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 10:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 11:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 12:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 13:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 14:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 15:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 16:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 17:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir>().Deserialize(ref reader, options);
                    break;
                case 18:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 19:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition>().Deserialize(ref reader, options);
                    break;
                case 20:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity>().Deserialize(ref reader, options);
                    break;
                case 21:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow>().Deserialize(ref reader, options);
                    break;
                case 22:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_CameraShake>().Deserialize(ref reader, options);
                    break;
                case 23:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect>().Deserialize(ref reader, options);
                    break;
                case 24:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect>().Deserialize(ref reader, options);
                    break;
                case 25:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline>().Deserialize(ref reader, options);
                    break;
                case 26:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlaySound>().Deserialize(ref reader, options);
                    break;
                case 27:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm>().Deserialize(ref reader, options);
                    break;
                case 28:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark>().Deserialize(ref reader, options);
                    break;
                case 29:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled>().Deserialize(ref reader, options);
                    break;
                case 30:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility>().Deserialize(ref reader, options);
                    break;
                case 31:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Default>().Deserialize(ref reader, options);
                    break;
                case 32:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir>().Deserialize(ref reader, options);
                    break;
                case 33:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos>().Deserialize(ref reader, options);
                    break;
                case 34:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid>().Deserialize(ref reader, options);
                    break;
                case 35:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid>().Deserialize(ref reader, options);
                    break;
                case 36:
                    result = (global::Phoenix.ConfigData.BattleStageActionConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid>().Deserialize(ref reader, options);
                    break;
                default:
                    reader.Skip();
                    break;
            }

            reader.Depth--;
            return result;
        }
    }


}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
