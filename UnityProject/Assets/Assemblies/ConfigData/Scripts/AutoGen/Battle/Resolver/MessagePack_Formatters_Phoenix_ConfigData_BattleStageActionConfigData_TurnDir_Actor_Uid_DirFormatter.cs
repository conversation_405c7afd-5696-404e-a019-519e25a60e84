// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleStageActionConfigData_TurnDir_Actor_Uid_DirFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(5);
            writer.Write(value.time);
            writer.Write(value.showAnimation);
            writer.Write(value.waitEnd);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.actorUidList, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleDirType>().Serialize(ref writer, value.dir, options);
        }

        public global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __actorUidList__ = default(global::System.Collections.Generic.List<int>);
            var __dir__ = default(global::Phoenix.ConfigData.BattleDirType);
            var __time__ = default(float);
            var __showAnimation__ = default(bool);
            var __waitEnd__ = default(bool);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 3:
                        __actorUidList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 4:
                        __dir__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleDirType>().Deserialize(ref reader, options);
                        break;
                    case 0:
                        __time__ = reader.ReadSingle();
                        break;
                    case 1:
                        __showAnimation__ = reader.ReadBoolean();
                        break;
                    case 2:
                        __waitEnd__ = reader.ReadBoolean();
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir();
            ____result.actorUidList = __actorUidList__;
            ____result.dir = __dir__;
            ____result.time = __time__;
            ____result.showAnimation = __showAnimation__;
            ____result.waitEnd = __waitEnd__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
