// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BuffEffectConfigData_AuraApply_AreaFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply_Area>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply_Area value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(4);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>>().Serialize(ref writer, value.conditionList, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectRangeId>().Serialize(ref writer, value.rangeId, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectTargetFilterType>().Serialize(ref writer, value.filterFuncType, options);
            writer.Write(value.buffRid);
        }

        public global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply_Area Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __rangeId__ = default(global::Phoenix.ConfigData.TargetSelectRangeId);
            var __filterFuncType__ = default(global::Phoenix.ConfigData.TargetSelectTargetFilterType);
            var __buffRid__ = default(int);
            var __conditionList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 1:
                        __rangeId__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectRangeId>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __filterFuncType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectTargetFilterType>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __buffRid__ = reader.ReadInt32();
                        break;
                    case 0:
                        __conditionList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply_Area();
            ____result.rangeId = __rangeId__;
            ____result.filterFuncType = __filterFuncType__;
            ____result.buffRid = __buffRid__;
            ____result.conditionList = __conditionList__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
