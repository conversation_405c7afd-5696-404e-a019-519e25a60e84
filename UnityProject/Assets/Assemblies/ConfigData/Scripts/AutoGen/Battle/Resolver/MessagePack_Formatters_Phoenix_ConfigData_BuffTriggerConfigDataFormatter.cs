// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using System.Collections.Generic;
    using MessagePack;

    public sealed class BuffTriggerConfigDataFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BuffTriggerConfigData>
    {
        private readonly Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>> typeToKeyAndJumpMap;
        private readonly Dictionary<int, int> keyToJumpMap;

        public BuffTriggerConfigDataFormatter()
        {
            this.typeToKeyAndJumpMap = new Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>>(22, global::MessagePack.Internal.RuntimeTypeHandleEqualityComparer.Default)
            {
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubject).TypeHandle, new KeyValuePair<int, int>(0, 0) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkill).TypeHandle, new KeyValuePair<int, int>(1, 1) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_Turn).TypeHandle, new KeyValuePair<int, int>(2, 2) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart).TypeHandle, new KeyValuePair<int, int>(3, 3) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCombat).TypeHandle, new KeyValuePair<int, int>(4, 4) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_FriendDead).TypeHandle, new KeyValuePair<int, int>(5, 5) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_EnemyDead).TypeHandle, new KeyValuePair<int, int>(6, 6) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_FirstAct).TypeHandle, new KeyValuePair<int, int>(7, 7) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_FriendAttackAssist).TypeHandle, new KeyValuePair<int, int>(8, 8) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd).TypeHandle, new KeyValuePair<int, int>(9, 9) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd).TypeHandle, new KeyValuePair<int, int>(10, 10) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd).TypeHandle, new KeyValuePair<int, int>(11, 11) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead).TypeHandle, new KeyValuePair<int, int>(12, 12) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove).TypeHandle, new KeyValuePair<int, int>(13, 13) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin).TypeHandle, new KeyValuePair<int, int>(14, 14) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin).TypeHandle, new KeyValuePair<int, int>(15, 15) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill).TypeHandle, new KeyValuePair<int, int>(16, 16) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill).TypeHandle, new KeyValuePair<int, int>(17, 17) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd).TypeHandle, new KeyValuePair<int, int>(18, 18) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd).TypeHandle, new KeyValuePair<int, int>(19, 19) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_TurnStart).TypeHandle, new KeyValuePair<int, int>(20, 20) },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd).TypeHandle, new KeyValuePair<int, int>(21, 21) },
            };
            this.keyToJumpMap = new Dictionary<int, int>(22)
            {
                { 0, 0 },
                { 1, 1 },
                { 2, 2 },
                { 3, 3 },
                { 4, 4 },
                { 5, 5 },
                { 6, 6 },
                { 7, 7 },
                { 8, 8 },
                { 9, 9 },
                { 10, 10 },
                { 11, 11 },
                { 12, 12 },
                { 13, 13 },
                { 14, 14 },
                { 15, 15 },
                { 16, 16 },
                { 17, 17 },
                { 18, 18 },
                { 19, 19 },
                { 20, 20 },
                { 21, 21 },
            };
        }

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BuffTriggerConfigData value, global::MessagePack.MessagePackSerializerOptions options)
        {
            KeyValuePair<int, int> keyValuePair;
            if (value != null && this.typeToKeyAndJumpMap.TryGetValue(value.GetType().TypeHandle, out keyValuePair))
            {
                writer.WriteArrayHeader(2);
                writer.WriteInt32(keyValuePair.Key);
                switch (keyValuePair.Value)
                {
                    case 0:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubject>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubject)value, options);
                        break;
                    case 1:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkill>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkill)value, options);
                        break;
                    case 2:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_Turn>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_Turn)value, options);
                        break;
                    case 3:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart)value, options);
                        break;
                    case 4:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCombat>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCombat)value, options);
                        break;
                    case 5:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_FriendDead>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_FriendDead)value, options);
                        break;
                    case 6:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_EnemyDead>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_EnemyDead)value, options);
                        break;
                    case 7:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_FirstAct>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_FirstAct)value, options);
                        break;
                    case 8:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_FriendAttackAssist>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_FriendAttackAssist)value, options);
                        break;
                    case 9:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd)value, options);
                        break;
                    case 10:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd)value, options);
                        break;
                    case 11:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd)value, options);
                        break;
                    case 12:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead)value, options);
                        break;
                    case 13:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove)value, options);
                        break;
                    case 14:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin)value, options);
                        break;
                    case 15:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin)value, options);
                        break;
                    case 16:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill)value, options);
                        break;
                    case 17:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill)value, options);
                        break;
                    case 18:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd)value, options);
                        break;
                    case 19:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd)value, options);
                        break;
                    case 20:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_TurnStart>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_TurnStart)value, options);
                        break;
                    case 21:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd>().Serialize(ref writer, (global::Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd)value, options);
                        break;
                    default:
                        break;
                }

                return;
            }

            writer.WriteNil();
        }

        public global::Phoenix.ConfigData.BuffTriggerConfigData Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            if (reader.ReadArrayHeader() != 2)
            {
                throw new InvalidOperationException("Invalid Union data was detected. Type:global::Phoenix.ConfigData.BuffTriggerConfigData");
            }

            options.Security.DepthStep(ref reader);
            var key = reader.ReadInt32();

            if (!this.keyToJumpMap.TryGetValue(key, out key))
            {
                key = -1;
            }

            global::Phoenix.ConfigData.BuffTriggerConfigData result = null;
            switch (key)
            {
                case 0:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubject>().Deserialize(ref reader, options);
                    break;
                case 1:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkill>().Deserialize(ref reader, options);
                    break;
                case 2:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_Turn>().Deserialize(ref reader, options);
                    break;
                case 3:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart>().Deserialize(ref reader, options);
                    break;
                case 4:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCombat>().Deserialize(ref reader, options);
                    break;
                case 5:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_FriendDead>().Deserialize(ref reader, options);
                    break;
                case 6:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_EnemyDead>().Deserialize(ref reader, options);
                    break;
                case 7:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_FirstAct>().Deserialize(ref reader, options);
                    break;
                case 8:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_FriendAttackAssist>().Deserialize(ref reader, options);
                    break;
                case 9:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd>().Deserialize(ref reader, options);
                    break;
                case 10:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd>().Deserialize(ref reader, options);
                    break;
                case 11:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd>().Deserialize(ref reader, options);
                    break;
                case 12:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead>().Deserialize(ref reader, options);
                    break;
                case 13:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove>().Deserialize(ref reader, options);
                    break;
                case 14:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin>().Deserialize(ref reader, options);
                    break;
                case 15:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin>().Deserialize(ref reader, options);
                    break;
                case 16:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill>().Deserialize(ref reader, options);
                    break;
                case 17:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill>().Deserialize(ref reader, options);
                    break;
                case 18:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd>().Deserialize(ref reader, options);
                    break;
                case 19:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd>().Deserialize(ref reader, options);
                    break;
                case 20:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_TurnStart>().Deserialize(ref reader, options);
                    break;
                case 21:
                    result = (global::Phoenix.ConfigData.BuffTriggerConfigData)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd>().Deserialize(ref reader, options);
                    break;
                default:
                    reader.Skip();
                    break;
            }

            reader.Depth--;
            return result;
        }
    }


}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
