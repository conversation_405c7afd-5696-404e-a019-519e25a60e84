// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class StrategySkillConfigDataFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.StrategySkillConfigData>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.StrategySkillConfigData value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(10);
            writer.Write(value.id);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.name, options);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.iconName, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.SkillIndicatorType>().Serialize(ref writer, value.indicatorType, options);
            writer.Write(value.coolTime);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>>().Serialize(ref writer, value.conditionList, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleTriggerConfigData>().Serialize(ref writer, value.trigger, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectStepConfigData>().Serialize(ref writer, value.selectStep, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillEffectConfigData>>().Serialize(ref writer, value.effectList, options);
            writer.Write(value.mainEffectIndex);
        }

        public global::Phoenix.ConfigData.StrategySkillConfigData Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __id__ = default(int);
            var __name__ = default(string);
            var __iconName__ = default(string);
            var __indicatorType__ = default(global::Phoenix.ConfigData.SkillIndicatorType);
            var __coolTime__ = default(int);
            var __conditionList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>);
            var __trigger__ = default(global::Phoenix.ConfigData.BattleTriggerConfigData);
            var __selectStep__ = default(global::Phoenix.ConfigData.TargetSelectStepConfigData);
            var __effectList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillEffectConfigData>);
            var __mainEffectIndex__ = default(int);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __id__ = reader.ReadInt32();
                        break;
                    case 1:
                        __name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __iconName__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __indicatorType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.SkillIndicatorType>().Deserialize(ref reader, options);
                        break;
                    case 4:
                        __coolTime__ = reader.ReadInt32();
                        break;
                    case 5:
                        __conditionList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>>().Deserialize(ref reader, options);
                        break;
                    case 6:
                        __trigger__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleTriggerConfigData>().Deserialize(ref reader, options);
                        break;
                    case 7:
                        __selectStep__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectStepConfigData>().Deserialize(ref reader, options);
                        break;
                    case 8:
                        __effectList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillEffectConfigData>>().Deserialize(ref reader, options);
                        break;
                    case 9:
                        __mainEffectIndex__ = reader.ReadInt32();
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.StrategySkillConfigData();
            ____result.id = __id__;
            ____result.name = __name__;
            ____result.iconName = __iconName__;
            ____result.indicatorType = __indicatorType__;
            ____result.coolTime = __coolTime__;
            ____result.conditionList = __conditionList__;
            ____result.trigger = __trigger__;
            ____result.selectStep = __selectStep__;
            ____result.effectList = __effectList__;
            ____result.mainEffectIndex = __mainEffectIndex__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
