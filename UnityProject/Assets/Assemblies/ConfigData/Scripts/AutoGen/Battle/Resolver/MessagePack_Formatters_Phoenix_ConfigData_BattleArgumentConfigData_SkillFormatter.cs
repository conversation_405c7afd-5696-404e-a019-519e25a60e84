// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using System.Collections.Generic;
    using MessagePack;

    public sealed class BattleArgumentConfigData_SkillFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill>
    {
        private readonly Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>> typeToKeyAndJumpMap;
        private readonly Dictionary<int, int> keyToJumpMap;

        public BattleArgumentConfigData_SkillFormatter()
        {
            this.typeToKeyAndJumpMap = new Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>>(3, global::MessagePack.Internal.RuntimeTypeHandleEqualityComparer.Default)
            {
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Running).TypeHandle, new KeyValuePair<int, int>(0, 0) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Self).TypeHandle, new KeyValuePair<int, int>(1, 1) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Entity).TypeHandle, new KeyValuePair<int, int>(2, 2) },
            };
            this.keyToJumpMap = new Dictionary<int, int>(3)
            {
                { 0, 0 },
                { 1, 1 },
                { 2, 2 },
            };
        }

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleArgumentConfigData_Skill value, global::MessagePack.MessagePackSerializerOptions options)
        {
            KeyValuePair<int, int> keyValuePair;
            if (value != null && this.typeToKeyAndJumpMap.TryGetValue(value.GetType().TypeHandle, out keyValuePair))
            {
                writer.WriteArrayHeader(2);
                writer.WriteInt32(keyValuePair.Key);
                switch (keyValuePair.Value)
                {
                    case 0:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Running>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Running)value, options);
                        break;
                    case 1:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Self>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Self)value, options);
                        break;
                    case 2:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Entity>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Entity)value, options);
                        break;
                    default:
                        break;
                }

                return;
            }

            writer.WriteNil();
        }

        public global::Phoenix.ConfigData.BattleArgumentConfigData_Skill Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            if (reader.ReadArrayHeader() != 2)
            {
                throw new InvalidOperationException("Invalid Union data was detected. Type:global::Phoenix.ConfigData.BattleArgumentConfigData_Skill");
            }

            options.Security.DepthStep(ref reader);
            var key = reader.ReadInt32();

            if (!this.keyToJumpMap.TryGetValue(key, out key))
            {
                key = -1;
            }

            global::Phoenix.ConfigData.BattleArgumentConfigData_Skill result = null;
            switch (key)
            {
                case 0:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Skill)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Running>().Deserialize(ref reader, options);
                    break;
                case 1:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Skill)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Self>().Deserialize(ref reader, options);
                    break;
                case 2:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Skill)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Entity>().Deserialize(ref reader, options);
                    break;
                default:
                    reader.Skip();
                    break;
            }

            reader.Depth--;
            return result;
        }
    }


}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
