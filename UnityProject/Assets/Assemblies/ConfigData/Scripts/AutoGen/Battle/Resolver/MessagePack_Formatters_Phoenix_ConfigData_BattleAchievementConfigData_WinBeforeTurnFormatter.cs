// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleAchievementConfigData_WinBeforeTurnFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(5);
            writer.Write(value.achievementId);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.name, options);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.desc, options);
            writer.Write(value.rewardId);
            writer.Write(value.turnIndex);
        }

        public global::Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __turnIndex__ = default(int);
            var __achievementId__ = default(int);
            var __name__ = default(string);
            var __desc__ = default(string);
            var __rewardId__ = default(int);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 4:
                        __turnIndex__ = reader.ReadInt32();
                        break;
                    case 0:
                        __achievementId__ = reader.ReadInt32();
                        break;
                    case 1:
                        __name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __desc__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __rewardId__ = reader.ReadInt32();
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn();
            ____result.turnIndex = __turnIndex__;
            ____result.achievementId = __achievementId__;
            ____result.name = __name__;
            ____result.desc = __desc__;
            ____result.rewardId = __rewardId__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
