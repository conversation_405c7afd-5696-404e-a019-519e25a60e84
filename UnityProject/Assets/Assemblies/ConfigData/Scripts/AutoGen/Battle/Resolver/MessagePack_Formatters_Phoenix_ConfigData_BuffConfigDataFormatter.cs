// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BuffConfigDataFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BuffConfigData>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BuffConfigData value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(13);
            writer.Write(value.id);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.name, options);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.desc, options);
            writer.Write(value.maxLevel);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffLifeTimeType>().Serialize(ref writer, value.lifeTimeType, options);
            writer.Write(value.isShow);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.iconName, options);
            writer.Write(value.needAnnounce);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffTagId>>().Serialize(ref writer, value.tagList, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffEffectConfigData>>().Serialize(ref writer, value.effectList, options);
            writer.Write(value.attachEffectId);
            writer.Write(value.loopEffectId);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.loopAnimName, options);
        }

        public global::Phoenix.ConfigData.BuffConfigData Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __id__ = default(int);
            var __name__ = default(string);
            var __desc__ = default(string);
            var __maxLevel__ = default(int);
            var __lifeTimeType__ = default(global::Phoenix.ConfigData.BuffLifeTimeType);
            var __isShow__ = default(bool);
            var __iconName__ = default(string);
            var __needAnnounce__ = default(bool);
            var __tagList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffTagId>);
            var __effectList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffEffectConfigData>);
            var __attachEffectId__ = default(int);
            var __loopEffectId__ = default(int);
            var __loopAnimName__ = default(string);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __id__ = reader.ReadInt32();
                        break;
                    case 1:
                        __name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __desc__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __maxLevel__ = reader.ReadInt32();
                        break;
                    case 4:
                        __lifeTimeType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BuffLifeTimeType>().Deserialize(ref reader, options);
                        break;
                    case 5:
                        __isShow__ = reader.ReadBoolean();
                        break;
                    case 6:
                        __iconName__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 7:
                        __needAnnounce__ = reader.ReadBoolean();
                        break;
                    case 8:
                        __tagList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffTagId>>().Deserialize(ref reader, options);
                        break;
                    case 9:
                        __effectList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffEffectConfigData>>().Deserialize(ref reader, options);
                        break;
                    case 10:
                        __attachEffectId__ = reader.ReadInt32();
                        break;
                    case 11:
                        __loopEffectId__ = reader.ReadInt32();
                        break;
                    case 12:
                        __loopAnimName__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BuffConfigData();
            ____result.id = __id__;
            ____result.name = __name__;
            ____result.desc = __desc__;
            ____result.maxLevel = __maxLevel__;
            ____result.lifeTimeType = __lifeTimeType__;
            ____result.isShow = __isShow__;
            ____result.iconName = __iconName__;
            ____result.needAnnounce = __needAnnounce__;
            ____result.tagList = __tagList__;
            ____result.effectList = __effectList__;
            ____result.attachEffectId = __attachEffectId__;
            ____result.loopEffectId = __loopEffectId__;
            ____result.loopAnimName = __loopAnimName__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
