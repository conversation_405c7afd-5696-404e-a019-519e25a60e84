// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleStageActionConfigData_AttachBuff_Actor_CampUidFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(3);
            writer.Write(value.skipEffect);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>>().Serialize(ref writer, value.buffList, options);
            writer.Write(value.campUid);
        }

        public global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __campUid__ = default(int);
            var __buffList__ = default(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>);
            var __skipEffect__ = default(bool);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 2:
                        __campUid__ = reader.ReadInt32();
                        break;
                    case 1:
                        __buffList__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>>().Deserialize(ref reader, options);
                        break;
                    case 0:
                        __skipEffect__ = reader.ReadBoolean();
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid();
            ____result.campUid = __campUid__;
            ____result.buffList = __buffList__;
            ____result.skipEffect = __skipEffect__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
