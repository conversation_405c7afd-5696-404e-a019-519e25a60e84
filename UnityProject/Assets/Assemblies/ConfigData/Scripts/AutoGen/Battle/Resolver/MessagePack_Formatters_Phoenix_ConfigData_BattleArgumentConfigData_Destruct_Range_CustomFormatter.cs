// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class BattleArgumentConfigData_Destruct_Range_CustomFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(4);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Grid>().Serialize(ref writer, value.centerGrid, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Dir>().Serialize(ref writer, value.dir, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectRangeId>().Serialize(ref writer, value.rangeId, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleCampRefType>().Serialize(ref writer, value.campRefType, options);
        }

        public global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __centerGrid__ = default(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid);
            var __dir__ = default(global::Phoenix.ConfigData.BattleArgumentConfigData_Dir);
            var __rangeId__ = default(global::Phoenix.ConfigData.TargetSelectRangeId);
            var __campRefType__ = default(global::Phoenix.ConfigData.BattleCampRefType);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __centerGrid__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Grid>().Deserialize(ref reader, options);
                        break;
                    case 1:
                        __dir__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Dir>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __rangeId__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.TargetSelectRangeId>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __campRefType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleCampRefType>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom();
            ____result.centerGrid = __centerGrid__;
            ____result.dir = __dir__;
            ____result.rangeId = __rangeId__;
            ____result.campRefType = __campRefType__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
