// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Formatters.Phoenix.ConfigData
{
    using System;
    using System.Buffers;
    using System.Collections.Generic;
    using MessagePack;

    public sealed class BattleArgumentConfigData_EntityFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity>
    {
        private readonly Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>> typeToKeyAndJumpMap;
        private readonly Dictionary<int, int> keyToJumpMap;

        public BattleArgumentConfigData_EntityFormatter()
        {
            this.typeToKeyAndJumpMap = new Dictionary<RuntimeTypeHandle, KeyValuePair<int, int>>(13, global::MessagePack.Internal.RuntimeTypeHandleEqualityComparer.Default)
            {
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid).TypeHandle, new KeyValuePair<int, int>(0, 0) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList).TypeHandle, new KeyValuePair<int, int>(1, 1) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self).TypeHandle, new KeyValuePair<int, int>(2, 2) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union).TypeHandle, new KeyValuePair<int, int>(3, 3) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner).TypeHandle, new KeyValuePair<int, int>(4, 4) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget).TypeHandle, new KeyValuePair<int, int>(5, 5) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster).TypeHandle, new KeyValuePair<int, int>(6, 6) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self).TypeHandle, new KeyValuePair<int, int>(7, 7) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1).TypeHandle, new KeyValuePair<int, int>(8, 8) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1).TypeHandle, new KeyValuePair<int, int>(9, 9) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom).TypeHandle, new KeyValuePair<int, int>(10, 10) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom).TypeHandle, new KeyValuePair<int, int>(11, 11) },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget).TypeHandle, new KeyValuePair<int, int>(12, 12) },
            };
            this.keyToJumpMap = new Dictionary<int, int>(13)
            {
                { 0, 0 },
                { 1, 1 },
                { 2, 2 },
                { 3, 3 },
                { 4, 4 },
                { 5, 5 },
                { 6, 6 },
                { 7, 7 },
                { 8, 8 },
                { 9, 9 },
                { 10, 10 },
                { 11, 11 },
                { 12, 12 },
            };
        }

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.ConfigData.BattleArgumentConfigData_Entity value, global::MessagePack.MessagePackSerializerOptions options)
        {
            KeyValuePair<int, int> keyValuePair;
            if (value != null && this.typeToKeyAndJumpMap.TryGetValue(value.GetType().TypeHandle, out keyValuePair))
            {
                writer.WriteArrayHeader(2);
                writer.WriteInt32(keyValuePair.Key);
                switch (keyValuePair.Value)
                {
                    case 0:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid)value, options);
                        break;
                    case 1:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList)value, options);
                        break;
                    case 2:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self)value, options);
                        break;
                    case 3:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union)value, options);
                        break;
                    case 4:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner)value, options);
                        break;
                    case 5:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget)value, options);
                        break;
                    case 6:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster)value, options);
                        break;
                    case 7:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self)value, options);
                        break;
                    case 8:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1)value, options);
                        break;
                    case 9:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1)value, options);
                        break;
                    case 10:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom)value, options);
                        break;
                    case 11:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom)value, options);
                        break;
                    case 12:
                        options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget>().Serialize(ref writer, (global::Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget)value, options);
                        break;
                    default:
                        break;
                }

                return;
            }

            writer.WriteNil();
        }

        public global::Phoenix.ConfigData.BattleArgumentConfigData_Entity Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            if (reader.ReadArrayHeader() != 2)
            {
                throw new InvalidOperationException("Invalid Union data was detected. Type:global::Phoenix.ConfigData.BattleArgumentConfigData_Entity");
            }

            options.Security.DepthStep(ref reader);
            var key = reader.ReadInt32();

            if (!this.keyToJumpMap.TryGetValue(key, out key))
            {
                key = -1;
            }

            global::Phoenix.ConfigData.BattleArgumentConfigData_Entity result = null;
            switch (key)
            {
                case 0:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid>().Deserialize(ref reader, options);
                    break;
                case 1:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList>().Deserialize(ref reader, options);
                    break;
                case 2:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self>().Deserialize(ref reader, options);
                    break;
                case 3:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union>().Deserialize(ref reader, options);
                    break;
                case 4:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner>().Deserialize(ref reader, options);
                    break;
                case 5:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget>().Deserialize(ref reader, options);
                    break;
                case 6:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster>().Deserialize(ref reader, options);
                    break;
                case 7:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self>().Deserialize(ref reader, options);
                    break;
                case 8:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1>().Deserialize(ref reader, options);
                    break;
                case 9:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1>().Deserialize(ref reader, options);
                    break;
                case 10:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom>().Deserialize(ref reader, options);
                    break;
                case 11:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom>().Deserialize(ref reader, options);
                    break;
                case 12:
                    result = (global::Phoenix.ConfigData.BattleArgumentConfigData_Entity)options.Resolver.GetFormatterWithVerify<global::Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget>().Deserialize(ref reader, options);
                    break;
                default:
                    reader.Skip();
                    break;
            }

            reader.Depth--;
            return result;
        }
    }


}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
