// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Resolvers
{
    using System;

    public class BattleResolver : global::MessagePack.IFormatterResolver
    {
        public static readonly global::MessagePack.IFormatterResolver Instance = new BattleResolver();

        private BattleResolver()
        {
        }

        public global::MessagePack.Formatters.IMessagePackFormatter<T> GetFormatter<T>()
        {
            return FormatterCache<T>.Formatter;
        }

        private static class FormatterCache<T>
        {
            internal static readonly global::MessagePack.Formatters.IMessagePackFormatter<T> Formatter;

            static FormatterCache()
            {
                var f = BattleResolverGetFormatterHelper.GetFormatter(typeof(T));
                if (f != null)
                {
                    Formatter = (global::MessagePack.Formatters.IMessagePackFormatter<T>)f;
                }
            }
        }
    }

    internal static class BattleResolverGetFormatterHelper
    {
        private static readonly global::System.Collections.Generic.Dictionary<Type, int> lookup;

        static BattleResolverGetFormatterHelper()
        {
            lookup = new global::System.Collections.Generic.Dictionary<Type, int>(272)
            {
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAchievementConfigData>), 0 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>), 1 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity>), 2 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random>), 3 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>), 4 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleCampRefereeConfigData>), 5 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData>), 6 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleConfigData>), 7 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleDetachBuffItemConfigData>), 8 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>), 9 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePos2ConfigData>), 10 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageActionConfigData>), 11 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData>), 12 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageConfigData>), 13 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDisposedActorConfigData>), 14 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData>), 15 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData>), 16 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDispositionConfigData>), 17 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageTriggerConfigData>), 18 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleTeamConfigData>), 19 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleTreasureBoxConfigData>), 20 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffConfigData>), 21 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffEffectConfigData>), 22 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffTagId>), 23 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillAttributeChangeConfigData>), 24 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillConfigData>), 25 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillEffectConfigData>), 26 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillTagType>), 27 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.TerrainEffectBuffConfigData>), 28 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.TerrainLogicConfigData>), 29 },
                { typeof(global::System.Collections.Generic.List<int>), 30 },
                { typeof(global::Phoenix.ConfigData.AttributeId), 31 },
                { typeof(global::Phoenix.ConfigData.AttributePartId), 32 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefType), 33 },
                { typeof(global::Phoenix.ConfigData.BattleDirType), 34 },
                { typeof(global::Phoenix.ConfigData.BattleRoundRobinType), 35 },
                { typeof(global::Phoenix.ConfigData.BattleStageType), 36 },
                { typeof(global::Phoenix.ConfigData.BuffEffectAttributeAccumulateType), 37 },
                { typeof(global::Phoenix.ConfigData.BuffLifeTimeType), 38 },
                { typeof(global::Phoenix.ConfigData.BuffTagId), 39 },
                { typeof(global::Phoenix.ConfigData.CameraShakePattern), 40 },
                { typeof(global::Phoenix.ConfigData.CompareType), 41 },
                { typeof(global::Phoenix.ConfigData.EntityAIType), 42 },
                { typeof(global::Phoenix.ConfigData.EntityElementId), 43 },
                { typeof(global::Phoenix.ConfigData.SkillAnnounceType), 44 },
                { typeof(global::Phoenix.ConfigData.SkillDamageType), 45 },
                { typeof(global::Phoenix.ConfigData.SkillEffectFuncType), 46 },
                { typeof(global::Phoenix.ConfigData.SkillEngageType), 47 },
                { typeof(global::Phoenix.ConfigData.SkillIndicatorType), 48 },
                { typeof(global::Phoenix.ConfigData.SkillTagType), 49 },
                { typeof(global::Phoenix.ConfigData.SummonPerformanceType), 50 },
                { typeof(global::Phoenix.ConfigData.TargetSelectGridFilterType), 51 },
                { typeof(global::Phoenix.ConfigData.TargetSelectRangeId), 52 },
                { typeof(global::Phoenix.ConfigData.TargetSelectTargetFilterType), 53 },
                { typeof(global::Phoenix.ConfigData.TeamDecisionMarkId), 54 },
                { typeof(global::Phoenix.ConfigData.TerrainTriggerMomentType), 55 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData), 56 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition), 57 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Dir), 58 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity), 59 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid), 60 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team), 61 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value), 62 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData), 63 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity), 64 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill), 65 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData), 66 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff), 67 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Default), 68 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir), 69 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData), 70 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_Turn), 71 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData), 72 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData), 73 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_StateApply), 74 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData), 75 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubject), 76 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkill), 77 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_Turn), 78 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData), 79 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_SingleGrid), 80 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_SingleTarget), 81 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_TeamByCampRef), 82 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData), 83 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_ActorKillAnyActor), 84 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_DeadActorCountLessThan), 85 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_EnemyDeadCountBeforeTurn), 86 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_KillActorCountBeforeTurn), 87 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn), 88 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_Caused), 89 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During), 90 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_And), 91 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar), 92 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_Variable), 93 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableOpend), 94 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Not), 95 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Or), 96 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Random), 97 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom), 98 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1), 99 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom), 100 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1), 101 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self), 102 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Dir_Const), 103 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget), 104 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self), 105 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster), 106 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner), 107 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid), 108 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList), 109 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union), 110 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Area), 111 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos), 112 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos_Teleport1), 113 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos1), 114 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosList), 115 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Custom), 116 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Select1), 117 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Self), 118 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget), 119 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_Entity), 120 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_Self), 121 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_Uid), 122 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate), 123 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist), 124 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex), 125 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist), 126 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy), 127 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex), 128 },
                { typeof(global::Phoenix.ConfigData.BattleAttachBuffItemConfigData), 129 },
                { typeof(global::Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random), 130 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeConfigData), 131 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid), 132 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EnemyAllDead), 133 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EntityListAllDead), 134 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_SelfAllDead), 135 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_TurnEnd), 136 },
                { typeof(global::Phoenix.ConfigData.BattleConfigData), 137 },
                { typeof(global::Phoenix.ConfigData.BattleDetachBuffItemConfigData), 138 },
                { typeof(global::Phoenix.ConfigData.BattleFunctionEnableConfigData), 139 },
                { typeof(global::Phoenix.ConfigData.BattleListConfigData), 140 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_CampRef), 141 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Self), 142 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Uid), 143 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Rid), 144 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Tag), 145 },
                { typeof(global::Phoenix.ConfigData.BattlePlayerSlotConfigData), 146 },
                { typeof(global::Phoenix.ConfigData.BattlePos2ConfigData), 147 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark), 148 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid), 149 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid), 150 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid), 151 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity), 152 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition), 153 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow), 154 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraShake), 155 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid), 156 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid), 157 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid), 158 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Delay), 159 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect), 160 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid), 161 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Dialog), 162 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble), 163 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection), 164 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid), 165 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid), 166 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Parallel), 167 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid), 168 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm), 169 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlaySound), 170 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline), 171 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid), 172 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid), 173 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Sequence), 174 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled), 175 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect), 176 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor), 177 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid), 178 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility), 179 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir), 180 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos), 181 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData), 182 },
                { typeof(global::Phoenix.ConfigData.BattleStageConfigData), 183 },
                { typeof(global::Phoenix.ConfigData.BattleStageDisposedActorConfigData), 184 },
                { typeof(global::Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData), 185 },
                { typeof(global::Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData), 186 },
                { typeof(global::Phoenix.ConfigData.BattleStageDispositionConfigData), 187 },
                { typeof(global::Phoenix.ConfigData.BattleStageRefereeConfigData), 188 },
                { typeof(global::Phoenix.ConfigData.BattleStageTriggerConfigData), 189 },
                { typeof(global::Phoenix.ConfigData.BattleStarStampConfigData), 190 },
                { typeof(global::Phoenix.ConfigData.BattleTeamConfigData), 191 },
                { typeof(global::Phoenix.ConfigData.BattleTreasureBoxConfigData), 192 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyArea), 193 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyGridOpened), 194 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyPos), 195 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_EntityDead), 196 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChange), 197 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeOpened), 198 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_StageEnter), 199 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_StageLose), 200 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_StageWin), 201 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_TurnEnd), 202 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_TurnStart), 203 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Step), 204 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Turn), 205 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Count), 206 },
                { typeof(global::Phoenix.ConfigData.BattleValueBasedActorHpConfigData), 207 },
                { typeof(global::Phoenix.ConfigData.BuffConfigData), 208 },
                { typeof(global::Phoenix.ConfigData.BuffEffectBanSkillConfigData), 209 },
                { typeof(global::Phoenix.ConfigData.BuffEffectCanResurrectConfigData), 210 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AssistGuard), 211 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AttributeAccumulate), 212 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AttributeChange), 213 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AttributeDepend), 214 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply), 215 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Dodge), 216 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Immune_AttachBuff_Tag), 217 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Immune_SkillEffect), 218 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_SkillTrigger), 219 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Stun), 220 },
                { typeof(global::Phoenix.ConfigData.BuffEffectGuardEffectConfigData), 221 },
                { typeof(global::Phoenix.ConfigData.BuffEffectTriggerAdditionalAttackConfigData), 222 },
                { typeof(global::Phoenix.ConfigData.BuffEffectTriggerContinuousAttackConfigData), 223 },
                { typeof(global::Phoenix.ConfigData.BuffListConfigData), 224 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd), 225 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill), 226 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin), 227 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd), 228 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead), 229 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove), 230 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd), 231 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd), 232 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill), 233 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin), 234 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd), 235 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart), 236 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd), 237 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_TurnStart), 238 },
                { typeof(global::Phoenix.ConfigData.SkillAttributeChangeConfigData), 239 },
                { typeof(global::Phoenix.ConfigData.SkillConfigData), 240 },
                { typeof(global::Phoenix.ConfigData.SkillEffectArgumentConfigData), 241 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomCustom), 242 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomRid), 243 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_Rid), 244 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ChangeTeamEnergy), 245 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Control_Actor), 246 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Damage), 247 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Rid), 248 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Tag), 249 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ExtraAction), 250 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_ConstMovePoint), 251 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_LeftMovePoint), 252 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Heal), 253 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Move_Actor), 254 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos), 255 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevel), 256 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Summon_TerrainBuff_FullRange), 257 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Teleport_Actor), 258 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Transform_Actor), 259 },
                { typeof(global::Phoenix.ConfigData.SkillListConfigData), 260 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Dir), 261 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Grid), 262 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Self), 263 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Summon), 264 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Target), 265 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_TeleportSelf), 266 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_TeleportTarget), 267 },
                { typeof(global::Phoenix.ConfigData.TerrainEffectBuffConfigData), 268 },
                { typeof(global::Phoenix.ConfigData.TerrainEffectTriggerConfigData), 269 },
                { typeof(global::Phoenix.ConfigData.TerrainLogicConfigData), 270 },
                { typeof(global::Phoenix.ConfigData.TerrainLogicListConfigData), 271 },
            };
        }

        internal static object GetFormatter(Type t)
        {
            int key;
            if (!lookup.TryGetValue(t, out key))
            {
                return null;
            }

            switch (key)
            {
                case 0: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleAchievementConfigData>();
                case 1: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>();
                case 2: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity>();
                case 3: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random>();
                case 4: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>();
                case 5: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleCampRefereeConfigData>();
                case 6: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData>();
                case 7: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleConfigData>();
                case 8: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleDetachBuffItemConfigData>();
                case 9: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>();
                case 10: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattlePos2ConfigData>();
                case 11: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageActionConfigData>();
                case 12: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData>();
                case 13: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageConfigData>();
                case 14: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDisposedActorConfigData>();
                case 15: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData>();
                case 16: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData>();
                case 17: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDispositionConfigData>();
                case 18: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageTriggerConfigData>();
                case 19: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleTeamConfigData>();
                case 20: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleTreasureBoxConfigData>();
                case 21: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BuffConfigData>();
                case 22: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BuffEffectConfigData>();
                case 23: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BuffTagId>();
                case 24: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillAttributeChangeConfigData>();
                case 25: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillConfigData>();
                case 26: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillEffectConfigData>();
                case 27: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillTagType>();
                case 28: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.TerrainEffectBuffConfigData>();
                case 29: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.TerrainLogicConfigData>();
                case 30: return new global::MessagePack.Formatters.ListFormatter<int>();
                case 31: return new MessagePack.Formatters.Phoenix.ConfigData.AttributeIdFormatter();
                case 32: return new MessagePack.Formatters.Phoenix.ConfigData.AttributePartIdFormatter();
                case 33: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefTypeFormatter();
                case 34: return new MessagePack.Formatters.Phoenix.ConfigData.BattleDirTypeFormatter();
                case 35: return new MessagePack.Formatters.Phoenix.ConfigData.BattleRoundRobinTypeFormatter();
                case 36: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageTypeFormatter();
                case 37: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectAttributeAccumulateTypeFormatter();
                case 38: return new MessagePack.Formatters.Phoenix.ConfigData.BuffLifeTimeTypeFormatter();
                case 39: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTagIdFormatter();
                case 40: return new MessagePack.Formatters.Phoenix.ConfigData.CameraShakePatternFormatter();
                case 41: return new MessagePack.Formatters.Phoenix.ConfigData.CompareTypeFormatter();
                case 42: return new MessagePack.Formatters.Phoenix.ConfigData.EntityAITypeFormatter();
                case 43: return new MessagePack.Formatters.Phoenix.ConfigData.EntityElementIdFormatter();
                case 44: return new MessagePack.Formatters.Phoenix.ConfigData.SkillAnnounceTypeFormatter();
                case 45: return new MessagePack.Formatters.Phoenix.ConfigData.SkillDamageTypeFormatter();
                case 46: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectFuncTypeFormatter();
                case 47: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEngageTypeFormatter();
                case 48: return new MessagePack.Formatters.Phoenix.ConfigData.SkillIndicatorTypeFormatter();
                case 49: return new MessagePack.Formatters.Phoenix.ConfigData.SkillTagTypeFormatter();
                case 50: return new MessagePack.Formatters.Phoenix.ConfigData.SummonPerformanceTypeFormatter();
                case 51: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectGridFilterTypeFormatter();
                case 52: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectRangeIdFormatter();
                case 53: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectTargetFilterTypeFormatter();
                case 54: return new MessagePack.Formatters.Phoenix.ConfigData.TeamDecisionMarkIdFormatter();
                case 55: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainTriggerMomentTypeFormatter();
                case 56: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigDataFormatter();
                case 57: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_ConditionFormatter();
                case 58: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_DirFormatter();
                case 59: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_EntityFormatter();
                case 60: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_GridFormatter();
                case 61: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_TeamFormatter();
                case 62: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_ValueFormatter();
                case 63: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigDataFormatter();
                case 64: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_EntityFormatter();
                case 65: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_SkillFormatter();
                case 66: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigDataFormatter();
                case 67: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuffFormatter();
                case 68: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_DefaultFormatter();
                case 69: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_TurnDirFormatter();
                case 70: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigDataFormatter();
                case 71: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_TurnFormatter();
                case 72: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigDataFormatter();
                case 73: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigDataFormatter();
                case 74: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_StateApplyFormatter();
                case 75: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigDataFormatter();
                case 76: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectFormatter();
                case 77: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkillFormatter();
                case 78: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_TurnFormatter();
                case 79: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigDataFormatter();
                case 80: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_SingleGridFormatter();
                case 81: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_SingleTargetFormatter();
                case 82: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_TeamByCampRefFormatter();
                case 83: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigDataFormatter();
                case 84: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_ActorKillAnyActorFormatter();
                case 85: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_DeadActorCountLessThanFormatter();
                case 86: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_EnemyDeadCountBeforeTurnFormatter();
                case 87: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_KillActorCountBeforeTurnFormatter();
                case 88: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurnFormatter();
                case 89: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_CausedFormatter();
                case 90: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_DuringFormatter();
                case 91: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_AndFormatter();
                case 92: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVarFormatter();
                case 93: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableFormatter();
                case 94: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableOpendFormatter();
                case 95: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_NotFormatter();
                case 96: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_OrFormatter();
                case 97: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_RandomFormatter();
                case 98: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_CustomFormatter();
                case 99: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1Formatter();
                case 100: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_CustomFormatter();
                case 101: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1Formatter();
                case 102: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_SelfFormatter();
                case 103: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Dir_ConstFormatter();
                case 104: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTargetFormatter();
                case 105: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_SelfFormatter();
                case 106: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCasterFormatter();
                case 107: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_SummonerFormatter();
                case 108: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidFormatter();
                case 109: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidListFormatter();
                case 110: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_UnionFormatter();
                case 111: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_AreaFormatter();
                case 112: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosFormatter();
                case 113: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos_Teleport1Formatter();
                case 114: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos1Formatter();
                case 115: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosListFormatter();
                case 116: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_CustomFormatter();
                case 117: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Select1Formatter();
                case 118: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_SelfFormatter();
                case 119: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTargetFormatter();
                case 120: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_EntityFormatter();
                case 121: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_SelfFormatter();
                case 122: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_UidFormatter();
                case 123: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRateFormatter();
                case 124: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDistFormatter();
                case 125: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndexFormatter();
                case 126: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDistFormatter();
                case 127: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergyFormatter();
                case 128: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndexFormatter();
                case 129: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAttachBuffItemConfigDataFormatter();
                case 130: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAttachBuffItemConfigData_RandomFormatter();
                case 131: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeConfigDataFormatter();
                case 132: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGridFormatter();
                case 133: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EnemyAllDeadFormatter();
                case 134: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EntityListAllDeadFormatter();
                case 135: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_SelfAllDeadFormatter();
                case 136: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_TurnEndFormatter();
                case 137: return new MessagePack.Formatters.Phoenix.ConfigData.BattleConfigDataFormatter();
                case 138: return new MessagePack.Formatters.Phoenix.ConfigData.BattleDetachBuffItemConfigDataFormatter();
                case 139: return new MessagePack.Formatters.Phoenix.ConfigData.BattleFunctionEnableConfigDataFormatter();
                case 140: return new MessagePack.Formatters.Phoenix.ConfigData.BattleListConfigDataFormatter();
                case 141: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_CampRefFormatter();
                case 142: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_SelfFormatter();
                case 143: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_UidFormatter();
                case 144: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Skill_RidFormatter();
                case 145: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Skill_TagFormatter();
                case 146: return new MessagePack.Formatters.Phoenix.ConfigData.BattlePlayerSlotConfigDataFormatter();
                case 147: return new MessagePack.Formatters.Phoenix.ConfigData.BattlePos2ConfigDataFormatter();
                case 148: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMarkFormatter();
                case 149: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUidFormatter();
                case 150: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUidFormatter();
                case 151: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_UidFormatter();
                case 152: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntityFormatter();
                case 153: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPositionFormatter();
                case 154: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraFollowFormatter();
                case 155: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraShakeFormatter();
                case 156: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_UidFormatter();
                case 157: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_UidFormatter();
                case 158: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_UidFormatter();
                case 159: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DelayFormatter();
                case 160: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffectFormatter();
                case 161: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_UidFormatter();
                case 162: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DialogFormatter();
                case 163: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DialogBubbleFormatter();
                case 164: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DialogSelectionFormatter();
                case 165: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_UidFormatter();
                case 166: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_UidFormatter();
                case 167: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ParallelFormatter();
                case 168: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_UidFormatter();
                case 169: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlayBgmFormatter();
                case 170: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlaySoundFormatter();
                case 171: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlayTimelineFormatter();
                case 172: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_UidFormatter();
                case 173: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_UidFormatter();
                case 174: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_SequenceFormatter();
                case 175: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabledFormatter();
                case 176: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffectFormatter();
                case 177: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Summon_ActorFormatter();
                case 178: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_UidFormatter();
                case 179: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibilityFormatter();
                case 180: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_DirFormatter();
                case 181: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPosFormatter();
                case 182: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionSummonActorItemConfigDataFormatter();
                case 183: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageConfigDataFormatter();
                case 184: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDisposedActorConfigDataFormatter();
                case 185: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigDataFormatter();
                case 186: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigDataFormatter();
                case 187: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDispositionConfigDataFormatter();
                case 188: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageRefereeConfigDataFormatter();
                case 189: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageTriggerConfigDataFormatter();
                case 190: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStarStampConfigDataFormatter();
                case 191: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTeamConfigDataFormatter();
                case 192: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTreasureBoxConfigDataFormatter();
                case 193: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyAreaFormatter();
                case 194: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyGridOpenedFormatter();
                case 195: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyPosFormatter();
                case 196: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_EntityDeadFormatter();
                case 197: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeFormatter();
                case 198: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeOpenedFormatter();
                case 199: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_StageEnterFormatter();
                case 200: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_StageLoseFormatter();
                case 201: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_StageWinFormatter();
                case 202: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_TurnEndFormatter();
                case 203: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_TurnStartFormatter();
                case 204: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_StepFormatter();
                case 205: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_TurnFormatter();
                case 206: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_CountFormatter();
                case 207: return new MessagePack.Formatters.Phoenix.ConfigData.BattleValueBasedActorHpConfigDataFormatter();
                case 208: return new MessagePack.Formatters.Phoenix.ConfigData.BuffConfigDataFormatter();
                case 209: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectBanSkillConfigDataFormatter();
                case 210: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectCanResurrectConfigDataFormatter();
                case 211: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AssistGuardFormatter();
                case 212: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AttributeAccumulateFormatter();
                case 213: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AttributeChangeFormatter();
                case 214: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AttributeDependFormatter();
                case 215: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AuraApplyFormatter();
                case 216: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_DodgeFormatter();
                case 217: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_Immune_AttachBuff_TagFormatter();
                case 218: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_Immune_SkillEffectFormatter();
                case 219: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_SkillTriggerFormatter();
                case 220: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_StunFormatter();
                case 221: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectGuardEffectConfigDataFormatter();
                case 222: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectTriggerAdditionalAttackConfigDataFormatter();
                case 223: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectTriggerContinuousAttackConfigDataFormatter();
                case 224: return new MessagePack.Formatters.Phoenix.ConfigData.BuffListConfigDataFormatter();
                case 225: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEndFormatter();
                case 226: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkillFormatter();
                case 227: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBeginFormatter();
                case 228: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEndFormatter();
                case 229: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDeadFormatter();
                case 230: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMoveFormatter();
                case 231: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEndFormatter();
                case 232: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEndFormatter();
                case 233: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkillFormatter();
                case 234: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBeginFormatter();
                case 235: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEndFormatter();
                case 236: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStartFormatter();
                case 237: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_TurnEndFormatter();
                case 238: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_TurnStartFormatter();
                case 239: return new MessagePack.Formatters.Phoenix.ConfigData.SkillAttributeChangeConfigDataFormatter();
                case 240: return new MessagePack.Formatters.Phoenix.ConfigData.SkillConfigDataFormatter();
                case 241: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectArgumentConfigDataFormatter();
                case 242: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomCustomFormatter();
                case 243: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomRidFormatter();
                case 244: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RidFormatter();
                case 245: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ChangeTeamEnergyFormatter();
                case 246: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Control_ActorFormatter();
                case 247: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_DamageFormatter();
                case 248: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_RidFormatter();
                case 249: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_TagFormatter();
                case 250: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ExtraActionFormatter();
                case 251: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_ConstMovePointFormatter();
                case 252: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_LeftMovePointFormatter();
                case 253: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_HealFormatter();
                case 254: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Move_ActorFormatter();
                case 255: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPosFormatter();
                case 256: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevelFormatter();
                case 257: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Summon_TerrainBuff_FullRangeFormatter();
                case 258: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Teleport_ActorFormatter();
                case 259: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Transform_ActorFormatter();
                case 260: return new MessagePack.Formatters.Phoenix.ConfigData.SkillListConfigDataFormatter();
                case 261: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_DirFormatter();
                case 262: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_GridFormatter();
                case 263: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_SelfFormatter();
                case 264: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_SummonFormatter();
                case 265: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_TargetFormatter();
                case 266: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_TeleportSelfFormatter();
                case 267: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_TeleportTargetFormatter();
                case 268: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainEffectBuffConfigDataFormatter();
                case 269: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainEffectTriggerConfigDataFormatter();
                case 270: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainLogicConfigDataFormatter();
                case 271: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainLogicListConfigDataFormatter();
                default: return null;
            }
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1649 // File name should match first type name
