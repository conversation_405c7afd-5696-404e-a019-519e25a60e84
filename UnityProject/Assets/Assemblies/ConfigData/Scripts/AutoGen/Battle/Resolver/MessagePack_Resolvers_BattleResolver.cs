// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1649 // File name should match first type name

namespace MessagePack.Resolvers
{
    using System;

    public class BattleResolver : global::MessagePack.IFormatterResolver
    {
        public static readonly global::MessagePack.IFormatterResolver Instance = new BattleResolver();

        private BattleResolver()
        {
        }

        public global::MessagePack.Formatters.IMessagePackFormatter<T> GetFormatter<T>()
        {
            return FormatterCache<T>.Formatter;
        }

        private static class FormatterCache<T>
        {
            internal static readonly global::MessagePack.Formatters.IMessagePackFormatter<T> Formatter;

            static FormatterCache()
            {
                var f = BattleResolverGetFormatterHelper.GetFormatter(typeof(T));
                if (f != null)
                {
                    Formatter = (global::MessagePack.Formatters.IMessagePackFormatter<T>)f;
                }
            }
        }
    }

    internal static class BattleResolverGetFormatterHelper
    {
        private static readonly global::System.Collections.Generic.Dictionary<Type, int> lookup;

        static BattleResolverGetFormatterHelper()
        {
            lookup = new global::System.Collections.Generic.Dictionary<Type, int>(305)
            {
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAchievementConfigData>), 0 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>), 1 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity>), 2 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random>), 3 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>), 4 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleCampRefereeConfigData>), 5 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData>), 6 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleConfigData>), 7 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleDetachBuffItemConfigData>), 8 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>), 9 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattlePos2ConfigData>), 10 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageActionConfigData>), 11 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData>), 12 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageConfigData>), 13 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDisposedActorConfigData>), 14 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData>), 15 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData>), 16 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageDispositionConfigData>), 17 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleStageTriggerConfigData>), 18 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleTeamConfigData>), 19 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BattleTreasureBoxConfigData>), 20 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffConfigData>), 21 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffEffectConfigData>), 22 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.BuffTagId>), 23 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillAttributeChangeConfigData>), 24 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillConfigData>), 25 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillEffectConfigData>), 26 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.SkillTagType>), 27 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.TacticianSkillConfigData>), 28 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.TerrainEffectBuffConfigData>), 29 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.ConfigData.TerrainLogicConfigData>), 30 },
                { typeof(global::System.Collections.Generic.List<int>), 31 },
                { typeof(global::Phoenix.ConfigData.AttributeId), 32 },
                { typeof(global::Phoenix.ConfigData.AttributePartId), 33 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefType), 34 },
                { typeof(global::Phoenix.ConfigData.BattleDirType), 35 },
                { typeof(global::Phoenix.ConfigData.BattleRoundRobinType), 36 },
                { typeof(global::Phoenix.ConfigData.BattleStageType), 37 },
                { typeof(global::Phoenix.ConfigData.BuffEffectAttributeAccumulateType), 38 },
                { typeof(global::Phoenix.ConfigData.BuffLifeTimeType), 39 },
                { typeof(global::Phoenix.ConfigData.BuffTagId), 40 },
                { typeof(global::Phoenix.ConfigData.CameraShakePattern), 41 },
                { typeof(global::Phoenix.ConfigData.CompareType), 42 },
                { typeof(global::Phoenix.ConfigData.EntityAIType), 43 },
                { typeof(global::Phoenix.ConfigData.EntityElementId), 44 },
                { typeof(global::Phoenix.ConfigData.SkillActiveFilterType), 45 },
                { typeof(global::Phoenix.ConfigData.SkillAnnounceType), 46 },
                { typeof(global::Phoenix.ConfigData.SkillDamageType), 47 },
                { typeof(global::Phoenix.ConfigData.SkillEffectFuncType), 48 },
                { typeof(global::Phoenix.ConfigData.SkillEngageFilterType), 49 },
                { typeof(global::Phoenix.ConfigData.SkillEngageType), 50 },
                { typeof(global::Phoenix.ConfigData.SkillIndicatorType), 51 },
                { typeof(global::Phoenix.ConfigData.SkillTagType), 52 },
                { typeof(global::Phoenix.ConfigData.SummonPerformanceType), 53 },
                { typeof(global::Phoenix.ConfigData.TacticianSkillSpeedType), 54 },
                { typeof(global::Phoenix.ConfigData.TacticianSkillType), 55 },
                { typeof(global::Phoenix.ConfigData.TargetSelectGridFilterType), 56 },
                { typeof(global::Phoenix.ConfigData.TargetSelectRangeId), 57 },
                { typeof(global::Phoenix.ConfigData.TargetSelectTargetFilterType), 58 },
                { typeof(global::Phoenix.ConfigData.TeamDecisionMarkId), 59 },
                { typeof(global::Phoenix.ConfigData.TerrainTriggerMomentType), 60 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData), 61 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition), 62 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Dir), 63 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity), 64 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid), 65 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill), 66 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team), 67 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value), 68 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData), 69 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity), 70 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill), 71 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData), 72 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff), 73 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Default), 74 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir), 75 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData), 76 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_Turn), 77 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData), 78 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData), 79 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_StateApply), 80 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData), 81 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubject), 82 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkill), 83 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_Turn), 84 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData), 85 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData), 86 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_ActorKillAnyActor), 87 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_DeadActorCountLessThan), 88 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_EnemyDeadCountBeforeTurn), 89 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_KillActorCountBeforeTurn), 90 },
                { typeof(global::Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurn), 91 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_Caused_BySelf), 92 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During_Combat), 93 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During_Skill), 94 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_And), 95 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_Buff_Rid), 96 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar_Bool), 97 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar_Int), 98 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_Variable), 99 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableOpend), 100 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Not), 101 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Or), 102 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Condition_Random), 103 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_Custom), 104 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1), 105 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Custom), 106 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1), 107 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Self), 108 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Dir_Const), 109 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTarget), 110 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Self), 111 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCaster), 112 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Summoner), 113 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Uid), 114 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidList), 115 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Entity_Union), 116 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Area), 117 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos), 118 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos_Teleport1), 119 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos1), 120 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosList), 121 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Custom), 122 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Select1), 123 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Self), 124 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTarget), 125 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Entity), 126 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Running), 127 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Skill_Self), 128 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_CampRef_Self), 129 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_Entity), 130 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_Self), 131 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Team_Uid), 132 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_EntityCountInRange_Self), 133 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRate), 134 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDist), 135 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndex), 136 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDist), 137 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergy), 138 },
                { typeof(global::Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndex), 139 },
                { typeof(global::Phoenix.ConfigData.BattleAttachBuffItemConfigData), 140 },
                { typeof(global::Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random), 141 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeConfigData), 142 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid), 143 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid2), 144 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EnemyAllDead), 145 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EntityListAllDead), 146 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_SelfAllDead), 147 },
                { typeof(global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData_TurnEnd), 148 },
                { typeof(global::Phoenix.ConfigData.BattleConfigData), 149 },
                { typeof(global::Phoenix.ConfigData.BattleDamageConfigData), 150 },
                { typeof(global::Phoenix.ConfigData.BattleDetachBuffItemConfigData), 151 },
                { typeof(global::Phoenix.ConfigData.BattleFunctionEnableConfigData), 152 },
                { typeof(global::Phoenix.ConfigData.BattleGroundLimitConfigData), 153 },
                { typeof(global::Phoenix.ConfigData.BattleHealConfigData), 154 },
                { typeof(global::Phoenix.ConfigData.BattleListConfigData), 155 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_CampRef), 156 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Rid), 157 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Self), 158 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Entity_Uid), 159 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Any), 160 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Rid), 161 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Running), 162 },
                { typeof(global::Phoenix.ConfigData.BattleObjCheckConfigData_Skill_Tag), 163 },
                { typeof(global::Phoenix.ConfigData.BattlePlayerSlotConfigData), 164 },
                { typeof(global::Phoenix.ConfigData.BattlePos2ConfigData), 165 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMark), 166 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUid), 167 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUid), 168 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_Uid), 169 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntity), 170 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPosition), 171 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraFollow), 172 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_CameraShake), 173 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_Uid), 174 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_Uid), 175 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_Uid), 176 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Delay), 177 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffect), 178 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_Uid), 179 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Dialog), 180 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DialogBubble), 181 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_DialogSelection), 182 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_Uid), 183 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_Uid), 184 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Parallel), 185 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_Uid), 186 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayBgm), 187 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlaySound), 188 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_PlayTimeline), 189 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_Uid), 190 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_Uid), 191 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Sequence), 192 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabled), 193 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffect), 194 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Summon_Actor), 195 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_Uid), 196 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibility), 197 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_Dir), 198 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos), 199 },
                { typeof(global::Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData), 200 },
                { typeof(global::Phoenix.ConfigData.BattleStageConfigData), 201 },
                { typeof(global::Phoenix.ConfigData.BattleStageDisposedActorConfigData), 202 },
                { typeof(global::Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData), 203 },
                { typeof(global::Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData), 204 },
                { typeof(global::Phoenix.ConfigData.BattleStageDispositionConfigData), 205 },
                { typeof(global::Phoenix.ConfigData.BattleStageRefereeConfigData), 206 },
                { typeof(global::Phoenix.ConfigData.BattleStageTriggerConfigData), 207 },
                { typeof(global::Phoenix.ConfigData.BattleStarStampConfigData), 208 },
                { typeof(global::Phoenix.ConfigData.BattleTeamConfigData), 209 },
                { typeof(global::Phoenix.ConfigData.BattleTreasureBoxConfigData), 210 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpened), 211 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyArea), 212 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyGridOpened), 213 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyPos), 214 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_EntityDead), 215 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChange), 216 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeOpened), 217 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_StageEnter), 218 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_StageLose), 219 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_StageWin), 220 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_TurnEnd), 221 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_TurnStart), 222 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerConfigData_WaitTurn_Start), 223 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Step), 224 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_Turn), 225 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Count), 226 },
                { typeof(global::Phoenix.ConfigData.BattleTriggerLimitConfigData_Count_PerTurn), 227 },
                { typeof(global::Phoenix.ConfigData.BattleValueBasedActorHpConfigData), 228 },
                { typeof(global::Phoenix.ConfigData.BuffConfigData), 229 },
                { typeof(global::Phoenix.ConfigData.BuffEffectBanSkillConfigData), 230 },
                { typeof(global::Phoenix.ConfigData.BuffEffectCanResurrectConfigData), 231 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AssistGuard), 232 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AttributeAccumulate), 233 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AttributeChange), 234 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AttributeDepend), 235 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply_Area), 236 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_AuraApply_EntityCheck), 237 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Dodge), 238 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Immune_AttachBuff_Tag), 239 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Immune_SkillEffect), 240 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_SkillTrigger), 241 },
                { typeof(global::Phoenix.ConfigData.BuffEffectConfigData_Stun), 242 },
                { typeof(global::Phoenix.ConfigData.BuffEffectGuardEffectConfigData), 243 },
                { typeof(global::Phoenix.ConfigData.BuffEffectTriggerAdditionalAttackConfigData), 244 },
                { typeof(global::Phoenix.ConfigData.BuffEffectTriggerContinuousAttackConfigData), 245 },
                { typeof(global::Phoenix.ConfigData.BuffListConfigData), 246 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEnd), 247 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkill), 248 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterCombat), 249 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBegin), 250 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEnd), 251 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDead), 252 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMove), 253 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEnd), 254 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEnd), 255 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkill), 256 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBegin), 257 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEnd), 258 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_EnemyDead), 259 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_FirstAct), 260 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_FriendAttackAssist), 261 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_FriendDead), 262 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStart), 263 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_TurnEnd), 264 },
                { typeof(global::Phoenix.ConfigData.BuffTriggerConfigData_TurnStart), 265 },
                { typeof(global::Phoenix.ConfigData.SkillAttributeChangeConfigData), 266 },
                { typeof(global::Phoenix.ConfigData.SkillConfigData), 267 },
                { typeof(global::Phoenix.ConfigData.SkillEffectArgumentConfigData), 268 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomCustom), 269 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomRid), 270 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_Rid), 271 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ChangeSkillCd), 272 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ChangeTeamEnergy), 273 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Control_Actor), 274 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Damage_Default), 275 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Damage_RandomInRange), 276 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Rid), 277 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_Tag), 278 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ExtraAction), 279 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_ConstMovePoint), 280 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_LeftMovePoint), 281 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Heal_Default), 282 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Move_Actor), 283 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos), 284 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevel), 285 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Summon_TerrainBuff_FullRange), 286 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Teleport_Actor), 287 },
                { typeof(global::Phoenix.ConfigData.SkillEffectConfigData_Transform_Actor), 288 },
                { typeof(global::Phoenix.ConfigData.SkillListConfigData), 289 },
                { typeof(global::Phoenix.ConfigData.TacticianSkillConfigData), 290 },
                { typeof(global::Phoenix.ConfigData.TacticianSkillListConfigData), 291 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Dir), 292 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Grid), 293 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_None), 294 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_OutDanger), 295 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Self), 296 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Summon), 297 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_Target), 298 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_TeleportSelf), 299 },
                { typeof(global::Phoenix.ConfigData.TargetSelectStepConfigData_TeleportTarget), 300 },
                { typeof(global::Phoenix.ConfigData.TerrainEffectBuffConfigData), 301 },
                { typeof(global::Phoenix.ConfigData.TerrainEffectTriggerConfigData), 302 },
                { typeof(global::Phoenix.ConfigData.TerrainLogicConfigData), 303 },
                { typeof(global::Phoenix.ConfigData.TerrainLogicListConfigData), 304 },
            };
        }

        internal static object GetFormatter(Type t)
        {
            int key;
            if (!lookup.TryGetValue(t, out key))
            {
                return null;
            }

            switch (key)
            {
                case 0: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleAchievementConfigData>();
                case 1: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Condition>();
                case 2: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleArgumentConfigData_Entity>();
                case 3: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData_Random>();
                case 4: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleAttachBuffItemConfigData>();
                case 5: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleCampRefereeConfigData>();
                case 6: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleCampRefereeSituationConfigData>();
                case 7: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleConfigData>();
                case 8: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleDetachBuffItemConfigData>();
                case 9: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattlePlayerSlotConfigData>();
                case 10: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattlePos2ConfigData>();
                case 11: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageActionConfigData>();
                case 12: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageActionSummonActorItemConfigData>();
                case 13: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageConfigData>();
                case 14: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDisposedActorConfigData>();
                case 15: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigData>();
                case 16: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigData>();
                case 17: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageDispositionConfigData>();
                case 18: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleStageTriggerConfigData>();
                case 19: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleTeamConfigData>();
                case 20: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BattleTreasureBoxConfigData>();
                case 21: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BuffConfigData>();
                case 22: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BuffEffectConfigData>();
                case 23: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.BuffTagId>();
                case 24: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillAttributeChangeConfigData>();
                case 25: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillConfigData>();
                case 26: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillEffectConfigData>();
                case 27: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.SkillTagType>();
                case 28: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.TacticianSkillConfigData>();
                case 29: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.TerrainEffectBuffConfigData>();
                case 30: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.ConfigData.TerrainLogicConfigData>();
                case 31: return new global::MessagePack.Formatters.ListFormatter<int>();
                case 32: return new MessagePack.Formatters.Phoenix.ConfigData.AttributeIdFormatter();
                case 33: return new MessagePack.Formatters.Phoenix.ConfigData.AttributePartIdFormatter();
                case 34: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefTypeFormatter();
                case 35: return new MessagePack.Formatters.Phoenix.ConfigData.BattleDirTypeFormatter();
                case 36: return new MessagePack.Formatters.Phoenix.ConfigData.BattleRoundRobinTypeFormatter();
                case 37: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageTypeFormatter();
                case 38: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectAttributeAccumulateTypeFormatter();
                case 39: return new MessagePack.Formatters.Phoenix.ConfigData.BuffLifeTimeTypeFormatter();
                case 40: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTagIdFormatter();
                case 41: return new MessagePack.Formatters.Phoenix.ConfigData.CameraShakePatternFormatter();
                case 42: return new MessagePack.Formatters.Phoenix.ConfigData.CompareTypeFormatter();
                case 43: return new MessagePack.Formatters.Phoenix.ConfigData.EntityAITypeFormatter();
                case 44: return new MessagePack.Formatters.Phoenix.ConfigData.EntityElementIdFormatter();
                case 45: return new MessagePack.Formatters.Phoenix.ConfigData.SkillActiveFilterTypeFormatter();
                case 46: return new MessagePack.Formatters.Phoenix.ConfigData.SkillAnnounceTypeFormatter();
                case 47: return new MessagePack.Formatters.Phoenix.ConfigData.SkillDamageTypeFormatter();
                case 48: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectFuncTypeFormatter();
                case 49: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEngageFilterTypeFormatter();
                case 50: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEngageTypeFormatter();
                case 51: return new MessagePack.Formatters.Phoenix.ConfigData.SkillIndicatorTypeFormatter();
                case 52: return new MessagePack.Formatters.Phoenix.ConfigData.SkillTagTypeFormatter();
                case 53: return new MessagePack.Formatters.Phoenix.ConfigData.SummonPerformanceTypeFormatter();
                case 54: return new MessagePack.Formatters.Phoenix.ConfigData.TacticianSkillSpeedTypeFormatter();
                case 55: return new MessagePack.Formatters.Phoenix.ConfigData.TacticianSkillTypeFormatter();
                case 56: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectGridFilterTypeFormatter();
                case 57: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectRangeIdFormatter();
                case 58: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectTargetFilterTypeFormatter();
                case 59: return new MessagePack.Formatters.Phoenix.ConfigData.TeamDecisionMarkIdFormatter();
                case 60: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainTriggerMomentTypeFormatter();
                case 61: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigDataFormatter();
                case 62: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_ConditionFormatter();
                case 63: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_DirFormatter();
                case 64: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_EntityFormatter();
                case 65: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_GridFormatter();
                case 66: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_SkillFormatter();
                case 67: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_TeamFormatter();
                case 68: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_ValueFormatter();
                case 69: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigDataFormatter();
                case 70: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_EntityFormatter();
                case 71: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_SkillFormatter();
                case 72: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigDataFormatter();
                case 73: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuffFormatter();
                case 74: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_DefaultFormatter();
                case 75: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_TurnDirFormatter();
                case 76: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigDataFormatter();
                case 77: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_TurnFormatter();
                case 78: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigDataFormatter();
                case 79: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigDataFormatter();
                case 80: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_StateApplyFormatter();
                case 81: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigDataFormatter();
                case 82: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectFormatter();
                case 83: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_CheckSubjectAndSkillFormatter();
                case 84: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_TurnFormatter();
                case 85: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigDataFormatter();
                case 86: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigDataFormatter();
                case 87: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_ActorKillAnyActorFormatter();
                case 88: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_DeadActorCountLessThanFormatter();
                case 89: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_EnemyDeadCountBeforeTurnFormatter();
                case 90: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_KillActorCountBeforeTurnFormatter();
                case 91: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAchievementConfigData_WinBeforeTurnFormatter();
                case 92: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_Caused_BySelfFormatter();
                case 93: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During_CombatFormatter();
                case 94: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Action_During_SkillFormatter();
                case 95: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_AndFormatter();
                case 96: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_Buff_RidFormatter();
                case 97: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar_BoolFormatter();
                case 98: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Check_GlobalVar_IntFormatter();
                case 99: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableFormatter();
                case 100: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_Compare_VariableOpendFormatter();
                case 101: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_NotFormatter();
                case 102: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_OrFormatter();
                case 103: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Condition_RandomFormatter();
                case 104: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos_CustomFormatter();
                case 105: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Pos1Formatter();
                case 106: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_CustomFormatter();
                case 107: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_Select1Formatter();
                case 108: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Destruct_Range_SelfFormatter();
                case 109: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Dir_ConstFormatter();
                case 110: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_CombatTargetFormatter();
                case 111: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_SelfFormatter();
                case 112: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_SkillCasterFormatter();
                case 113: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_SummonerFormatter();
                case 114: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidFormatter();
                case 115: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_UidListFormatter();
                case 116: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Entity_UnionFormatter();
                case 117: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_AreaFormatter();
                case 118: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosFormatter();
                case 119: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos_Teleport1Formatter();
                case 120: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Pos1Formatter();
                case 121: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_PosListFormatter();
                case 122: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_CustomFormatter();
                case 123: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_Select1Formatter();
                case 124: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Grid_Range_SelfFormatter();
                case 125: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_LastSkillEffectTargetFormatter();
                case 126: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Skill_EntityFormatter();
                case 127: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Skill_RunningFormatter();
                case 128: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Skill_SelfFormatter();
                case 129: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_CampRef_SelfFormatter();
                case 130: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_EntityFormatter();
                case 131: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_SelfFormatter();
                case 132: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Team_UidFormatter();
                case 133: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_EntityCountInRange_SelfFormatter();
                case 134: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_HpRateFormatter();
                case 135: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_MoveDistFormatter();
                case 136: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_StepIndexFormatter();
                case 137: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_TargetDistFormatter();
                case 138: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_TeamEnergyFormatter();
                case 139: return new MessagePack.Formatters.Phoenix.ConfigData.BattleArgumentConfigData_Value_TurnIndexFormatter();
                case 140: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAttachBuffItemConfigDataFormatter();
                case 141: return new MessagePack.Formatters.Phoenix.ConfigData.BattleAttachBuffItemConfigData_RandomFormatter();
                case 142: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeConfigDataFormatter();
                case 143: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGridFormatter();
                case 144: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid2Formatter();
                case 145: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EnemyAllDeadFormatter();
                case 146: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_EntityListAllDeadFormatter();
                case 147: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_SelfAllDeadFormatter();
                case 148: return new MessagePack.Formatters.Phoenix.ConfigData.BattleCampRefereeSituationConfigData_TurnEndFormatter();
                case 149: return new MessagePack.Formatters.Phoenix.ConfigData.BattleConfigDataFormatter();
                case 150: return new MessagePack.Formatters.Phoenix.ConfigData.BattleDamageConfigDataFormatter();
                case 151: return new MessagePack.Formatters.Phoenix.ConfigData.BattleDetachBuffItemConfigDataFormatter();
                case 152: return new MessagePack.Formatters.Phoenix.ConfigData.BattleFunctionEnableConfigDataFormatter();
                case 153: return new MessagePack.Formatters.Phoenix.ConfigData.BattleGroundLimitConfigDataFormatter();
                case 154: return new MessagePack.Formatters.Phoenix.ConfigData.BattleHealConfigDataFormatter();
                case 155: return new MessagePack.Formatters.Phoenix.ConfigData.BattleListConfigDataFormatter();
                case 156: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_CampRefFormatter();
                case 157: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_RidFormatter();
                case 158: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_SelfFormatter();
                case 159: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Entity_UidFormatter();
                case 160: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Skill_AnyFormatter();
                case 161: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Skill_RidFormatter();
                case 162: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Skill_RunningFormatter();
                case 163: return new MessagePack.Formatters.Phoenix.ConfigData.BattleObjCheckConfigData_Skill_TagFormatter();
                case 164: return new MessagePack.Formatters.Phoenix.ConfigData.BattlePlayerSlotConfigDataFormatter();
                case 165: return new MessagePack.Formatters.Phoenix.ConfigData.BattlePos2ConfigDataFormatter();
                case 166: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AddTeamDecisionMarkFormatter();
                case 167: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_CampUidFormatter();
                case 168: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_TeamUidFormatter();
                case 169: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_AttachBuff_Actor_UidFormatter();
                case 170: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusEntityFormatter();
                case 171: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraFocusPositionFormatter();
                case 172: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraFollowFormatter();
                case 173: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_CameraShakeFormatter();
                case 174: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ChangeBehavior_Actor_UidFormatter();
                case 175: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ChangeTeam_Actor_UidFormatter();
                case 176: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Damage_Actor_UidFormatter();
                case 177: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DelayFormatter();
                case 178: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DespawnTerrainEffectFormatter();
                case 179: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DetachBuff_Actor_UidFormatter();
                case 180: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DialogFormatter();
                case 181: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DialogBubbleFormatter();
                case 182: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_DialogSelectionFormatter();
                case 183: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Heal_Actor_UidFormatter();
                case 184: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Move_Actor_UidFormatter();
                case 185: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ParallelFormatter();
                case 186: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlayAnim_Actor_UidFormatter();
                case 187: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlayBgmFormatter();
                case 188: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlaySoundFormatter();
                case 189: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_PlayTimelineFormatter();
                case 190: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Retreat_Actor_UidFormatter();
                case 191: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Select_Actor_UidFormatter();
                case 192: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_SequenceFormatter();
                case 193: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_SetWinConditionEnabledFormatter();
                case 194: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_SpawnTerrainEffectFormatter();
                case 195: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Summon_ActorFormatter();
                case 196: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_Teleport_Actor_UidFormatter();
                case 197: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_ToggleHudVisibilityFormatter();
                case 198: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_DirFormatter();
                case 199: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionConfigData_TurnDir_Actor_Uid_LookPosFormatter();
                case 200: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageActionSummonActorItemConfigDataFormatter();
                case 201: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageConfigDataFormatter();
                case 202: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDisposedActorConfigDataFormatter();
                case 203: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDisposedActorIdMappingConfigDataFormatter();
                case 204: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDisposedTerrainBuffConfigDataFormatter();
                case 205: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageDispositionConfigDataFormatter();
                case 206: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageRefereeConfigDataFormatter();
                case 207: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStageTriggerConfigDataFormatter();
                case 208: return new MessagePack.Formatters.Phoenix.ConfigData.BattleStarStampConfigDataFormatter();
                case 209: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTeamConfigDataFormatter();
                case 210: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTreasureBoxConfigDataFormatter();
                case 211: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityFilteredOccupyGridOpenedFormatter();
                case 212: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyAreaFormatter();
                case 213: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyGridOpenedFormatter();
                case 214: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_AnyEntityOccupyPosFormatter();
                case 215: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_EntityDeadFormatter();
                case 216: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeFormatter();
                case 217: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_EntityHpChangeOpenedFormatter();
                case 218: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_StageEnterFormatter();
                case 219: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_StageLoseFormatter();
                case 220: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_StageWinFormatter();
                case 221: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_TurnEndFormatter();
                case 222: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_TurnStartFormatter();
                case 223: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerConfigData_WaitTurn_StartFormatter();
                case 224: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_StepFormatter();
                case 225: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_Cd_TurnFormatter();
                case 226: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_CountFormatter();
                case 227: return new MessagePack.Formatters.Phoenix.ConfigData.BattleTriggerLimitConfigData_Count_PerTurnFormatter();
                case 228: return new MessagePack.Formatters.Phoenix.ConfigData.BattleValueBasedActorHpConfigDataFormatter();
                case 229: return new MessagePack.Formatters.Phoenix.ConfigData.BuffConfigDataFormatter();
                case 230: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectBanSkillConfigDataFormatter();
                case 231: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectCanResurrectConfigDataFormatter();
                case 232: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AssistGuardFormatter();
                case 233: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AttributeAccumulateFormatter();
                case 234: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AttributeChangeFormatter();
                case 235: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AttributeDependFormatter();
                case 236: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AuraApply_AreaFormatter();
                case 237: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_AuraApply_EntityCheckFormatter();
                case 238: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_DodgeFormatter();
                case 239: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_Immune_AttachBuff_TagFormatter();
                case 240: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_Immune_SkillEffectFormatter();
                case 241: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_SkillTriggerFormatter();
                case 242: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectConfigData_StunFormatter();
                case 243: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectGuardEffectConfigDataFormatter();
                case 244: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectTriggerAdditionalAttackConfigDataFormatter();
                case 245: return new MessagePack.Formatters.Phoenix.ConfigData.BuffEffectTriggerContinuousAttackConfigDataFormatter();
                case 246: return new MessagePack.Formatters.Phoenix.ConfigData.BuffListConfigDataFormatter();
                case 247: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterActionEndFormatter();
                case 248: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterCastSkillFormatter();
                case 249: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterCombatFormatter();
                case 250: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageBeginFormatter();
                case 251: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEngageEndFormatter();
                case 252: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityDeadFormatter();
                case 253: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterEntityMoveFormatter();
                case 254: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_AfterLocateEndFormatter();
                case 255: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeActionEndFormatter();
                case 256: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeCastSkillFormatter();
                case 257: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageBeginFormatter();
                case 258: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_BeforeEngageEndFormatter();
                case 259: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_EnemyDeadFormatter();
                case 260: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_FirstActFormatter();
                case 261: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_FriendAttackAssistFormatter();
                case 262: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_FriendDeadFormatter();
                case 263: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_MainBattleStartFormatter();
                case 264: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_TurnEndFormatter();
                case 265: return new MessagePack.Formatters.Phoenix.ConfigData.BuffTriggerConfigData_TurnStartFormatter();
                case 266: return new MessagePack.Formatters.Phoenix.ConfigData.SkillAttributeChangeConfigDataFormatter();
                case 267: return new MessagePack.Formatters.Phoenix.ConfigData.SkillConfigDataFormatter();
                case 268: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectArgumentConfigDataFormatter();
                case 269: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomCustomFormatter();
                case 270: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RandomRidFormatter();
                case 271: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_AttachBuff_RidFormatter();
                case 272: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ChangeSkillCdFormatter();
                case 273: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ChangeTeamEnergyFormatter();
                case 274: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Control_ActorFormatter();
                case 275: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Damage_DefaultFormatter();
                case 276: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Damage_RandomInRangeFormatter();
                case 277: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_RidFormatter();
                case 278: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_DetachBuff_TagFormatter();
                case 279: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ExtraActionFormatter();
                case 280: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_ConstMovePointFormatter();
                case 281: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_ExtraMove_LeftMovePointFormatter();
                case 282: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Heal_DefaultFormatter();
                case 283: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Move_ActorFormatter();
                case 284: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPosFormatter();
                case 285: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevelFormatter();
                case 286: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Summon_TerrainBuff_FullRangeFormatter();
                case 287: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Teleport_ActorFormatter();
                case 288: return new MessagePack.Formatters.Phoenix.ConfigData.SkillEffectConfigData_Transform_ActorFormatter();
                case 289: return new MessagePack.Formatters.Phoenix.ConfigData.SkillListConfigDataFormatter();
                case 290: return new MessagePack.Formatters.Phoenix.ConfigData.TacticianSkillConfigDataFormatter();
                case 291: return new MessagePack.Formatters.Phoenix.ConfigData.TacticianSkillListConfigDataFormatter();
                case 292: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_DirFormatter();
                case 293: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_GridFormatter();
                case 294: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_NoneFormatter();
                case 295: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_OutDangerFormatter();
                case 296: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_SelfFormatter();
                case 297: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_SummonFormatter();
                case 298: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_TargetFormatter();
                case 299: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_TeleportSelfFormatter();
                case 300: return new MessagePack.Formatters.Phoenix.ConfigData.TargetSelectStepConfigData_TeleportTargetFormatter();
                case 301: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainEffectBuffConfigDataFormatter();
                case 302: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainEffectTriggerConfigDataFormatter();
                case 303: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainLogicConfigDataFormatter();
                case 304: return new MessagePack.Formatters.Phoenix.ConfigData.TerrainLogicListConfigDataFormatter();
                default: return null;
            }
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1649 // File name should match first type name
