namespace Phoenix.ConfigData
{
    public enum SkillEffectFuncType
    {
        None = 0,
        AttachBuff_Rid = 1,
        AttachBuff_RandomRid = 2,
        AttachBuff_RandomCustom = 3,
        DetachBuff_Rid = 4,
        DetachBuff_Tag = 5,
        Damage = 6,
        <PERSON><PERSON> = 7,
        <PERSON><PERSON><PERSON>_Actor_RidToPos = 8,
        <PERSON><PERSON><PERSON>_Actor_RidToPos_ConstLevel = 9,
        Su<PERSON>on_TerrainBuff_FullRange = 10,
        Move_Actor = 11,
        Teleport_Actor = 12,
        Control_Actor = 13,
        Transform_Actor = 14,
        ExtraMove_ConstMovePoint = 15,
        ExtraMove_LeftMovePoint = 16,
        ExtraAction = 17,
        ChangeTeamEnergy = 18,
        
    }
}
