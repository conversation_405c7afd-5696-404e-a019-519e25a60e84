namespace Phoenix.ConfigData
{
    public enum BattleStageActionType
    {
        None = 0,
        Sequence = 1,
        <PERSON><PERSON><PERSON> = 2,
        Delay = 3,
        Dialog = 4,
        DialogSelection = 5,
        DialogBubble = 6,
        AttachB<PERSON>_Actor_Uid = 7,
        Attach<PERSON><PERSON>_Actor_TeamUid = 8,
        Atta<PERSON><PERSON><PERSON>_Actor_CampUid = 9,
        <PERSON><PERSON><PERSON><PERSON>_Actor_Uid = 10,
        <PERSON><PERSON><PERSON>_Actor = 11,
        Tel<PERSON><PERSON>_Actor_Uid = 12,
        Retreat_Actor_Uid = 13,
        <PERSON><PERSON>_Actor_Uid = 14,
        Heal_Actor_Uid = 15,
        ChangeTeam_Actor_Uid = 16,
        Change<PERSON><PERSON><PERSON><PERSON>_Actor_Uid = 17,
        Move_Actor_Uid = 18,
        PlayAnim_Actor_Uid = 19,
        TurnDir_Actor_Uid_Dir = 20,
        TurnDir_Actor_Uid_LookPos = 21,
        Select_Actor_Uid = 22,
        AddTeamDecisionMark = 23,
        CameraFocusPosition = 24,
        CameraFocusEntity = 25,
        CameraFollowEntity = 26,
        CameraShake = 27,
        SpawnTerrainEffect = 28,
        DespawnTerrainEffect = 29,
        PlayTimeline = 30,
        PlaySound = 31,
        PlayBgm = 32,
        SetWinConditionEnabled = 33,
        ToggleHudVisibility = 34,
        
    }
}
