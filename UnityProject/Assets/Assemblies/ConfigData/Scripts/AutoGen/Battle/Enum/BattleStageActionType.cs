namespace Phoenix.ConfigData
{
    public enum BattleStageActionType
    {
        None = 0,
        Sequence = 1,
        <PERSON><PERSON><PERSON> = 2,
        Delay = 3,
        Dialog = 4,
        DialogSelection = 5,
        DialogBubble = 6,
        AttachB<PERSON>_Actor_Uid = 7,
        Attach<PERSON><PERSON>_Actor_TeamUid = 8,
        Atta<PERSON><PERSON><PERSON>_Actor_CampUid = 9,
        <PERSON><PERSON><PERSON><PERSON>_Actor_Uid = 10,
        <PERSON><PERSON><PERSON>_Actor = 11,
        Tel<PERSON><PERSON>_Actor_Uid = 12,
        Retreat_Actor_Uid = 13,
        <PERSON><PERSON>_Actor_Uid = 14,
        Heal_Actor_Uid = 15,
        ChangeTeam_Actor_Uid = 16,
        Change<PERSON><PERSON><PERSON><PERSON>_Actor_Uid = 17,
        Move_Actor_Uid = 18,
        PlayAnim_Actor_Uid = 19,
        TurnDir_Actor_Uid = 20,
        Select_Actor_Uid = 21,
        AddTeamDecisionMark = 22,
        CameraFocusPosition = 23,
        CameraFocusEntity = 24,
        CameraFollowEntity = 25,
        CameraShake = 26,
        SpawnTerrainEffect = 27,
        DespawnTerrainEffect = 28,
        PlayTimeline = 29,
        PlaySound = 30,
        PlayBgm = 31,
        SetWinConditionEnabled = 32,
        ToggleHudVisibility = 33,
        
    }
}
