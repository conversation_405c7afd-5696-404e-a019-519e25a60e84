using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid : BattleCampRefereeSituationConfigData
    {
        [Key(2)]
        public List<int> actorUidList = new List<int>();
        
        [Key(3)]
        public List<BattlePos2ConfigData> posList = new List<BattlePos2ConfigData>();
        
        [IgnoreMember]
        public override BattleCampRefereeSituationType refereeType
        {
            get { return BattleCampRefereeSituationType.AnyActorOccupyAnyGrid; }
        }
    }
}
