using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_SetWinConditionEnabled : BattleStageActionConfigData
    {
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.SetWinConditionEnabled; }
        }
    }
}
