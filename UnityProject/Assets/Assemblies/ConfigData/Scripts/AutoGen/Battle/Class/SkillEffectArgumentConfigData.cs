using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectArgumentConfigData
    {
        [Key(0)]
        public int baseValue;
        
        [Key(1)]
        public int defendSubSrcRate;
        
        [Key(2)]
        public int physicalAttackRate;
        
        [Key(3)]
        public int magicalAttackRate;
        
        [Key(4)]
        public int maxTargetHpRate;
        
        [Key(5)]
        public int curTargetHpRate;
        
    }
}
