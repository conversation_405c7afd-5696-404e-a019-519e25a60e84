using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BuffTriggerConfigData_AfterLocateEnd))]
    [Union(1, typeof(BuffTriggerConfigData_BeforeActionEnd))]
    [Union(2, typeof(BuffTriggerConfigData_AfterActionEnd))]
    [Union(3, typeof(BuffTriggerConfigData_AfterEntityDead))]
    [Union(4, typeof(BuffTriggerConfigData_AfterEntityMove))]
    public abstract partial class BuffTriggerConfigData_CheckSubject : BuffTriggerConfigData
    {
        [Key(1)]
        public BattleObjCheckConfigData_Entity entityCheck;
        
    }
}
