using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleArgumentConfigData_Grid_Pos))]
    [Union(1, typeof(BattleArgumentConfigData_Grid_PosList))]
    [Union(2, typeof(BattleArgumentConfigData_Grid_Area))]
    [Union(3, typeof(BattleArgumentConfigData_Grid_Range_Self))]
    [Union(4, typeof(BattleArgumentConfigData_Grid_Range_Custom))]
    [Union(5, typeof(BattleArgumentConfigData_Grid_Pos1))]
    [Union(6, typeof(BattleArgumentConfigData_Grid_Range_Select1))]
    [Union(7, typeof(BattleArgumentConfigData_Grid_Pos_Teleport1))]
    public abstract partial class BattleArgumentConfigData_Grid
    {
        [IgnoreMember]
        public abstract BattleArgumentGridFuncType funcType { get; }
    }
}
