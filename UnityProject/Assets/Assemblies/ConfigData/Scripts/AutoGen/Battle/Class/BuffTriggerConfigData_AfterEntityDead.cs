using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffTriggerConfigData_AfterEntityDead : BuffTriggerConfigData_CheckSubject
    {
        [IgnoreMember]
        public override BuffTriggerType triggerType
        {
            get { return BuffTriggerType.AfterEntityDead; }
        }
    }
}
