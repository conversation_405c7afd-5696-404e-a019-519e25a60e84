using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Team_Entity : BattleArgumentConfigData_Team
    {
        [Key(0)]
        public BattleArgumentConfigData_Entity entity;
        
        [IgnoreMember]
        public override BattleArgumentTeamFuncType funcType
        {
            get { return BattleArgumentTeamFuncType.Entity; }
        }
    }
}
