using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_Heal : SkillEffectConfigData_SingleTarget
    {
        [Key(2)]
        public SkillEffectArgumentConfigData argument;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Heal; }
        }
    }
}
