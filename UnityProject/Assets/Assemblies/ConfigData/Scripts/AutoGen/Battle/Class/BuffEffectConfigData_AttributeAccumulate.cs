using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_AttributeAccumulate : BuffEffectConfigData
    {
        [Key(1)]
        public AttributePartId partId;
        
        [Key(2)]
        public int value;
        
        [Key(3)]
        public BuffEffectAttributeAccumulateType accumulateType;
        
        [Key(4)]
        public int accumulateMult;
        
        [Key(5)]
        public int minAccumulate;
        
        [Key(6)]
        public int maxAccumulate;
        
        [IgnoreMember]
        public override BuffEffectType effectType
        {
            get { return BuffEffectType.AttributeAccumulate; }
        }
    }
}
