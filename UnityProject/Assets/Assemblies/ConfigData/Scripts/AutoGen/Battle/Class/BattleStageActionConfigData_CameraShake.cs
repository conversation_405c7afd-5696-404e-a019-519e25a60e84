using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_CameraShake : BattleStageActionConfigData
    {
        [Key(0)]
        public CameraShakePattern pattern;
        
        [Key(1)]
        public float duration;
        
        [Key(2)]
        public float amplitude;
        
        [Key(3)]
        public float frequency;
        
        [Key(4)]
        public bool immediately;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.CameraShake; }
        }
    }
}
