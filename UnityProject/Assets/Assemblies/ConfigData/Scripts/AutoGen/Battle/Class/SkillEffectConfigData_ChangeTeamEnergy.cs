using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_ChangeTeamEnergy : SkillEffectConfigData_TeamByCampRef
    {
        [Key(2)]
        public int deltaEnergy;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.ChangeTeamEnergy; }
        }
    }
}
