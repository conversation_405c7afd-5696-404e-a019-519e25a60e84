using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffConfigData
    {
        [Key(0)]
        public int id;
        
        [Key(1)]
        public string name;
        
        [Key(2)]
        public string desc;
        
        [Key(3)]
        public int maxLevel;
        
        [Key(4)]
        public BuffLifeTimeType lifeTimeType;
        
        [Key(5)]
        public bool isShow;
        
        [Key(6)]
        public string iconName;
        
        [Key(7)]
        public bool needAnnounce;
        
        [Key(8)]
        public List<BuffTagId> tagList = new List<BuffTagId>();
        
        [Key(9)]
        public List<BuffEffectConfigData> effectList = new List<BuffEffectConfigData>();
        
        [Key(10)]
        public int attachEffectId;
        
        [Key(11)]
        public int loopEffectId;
        
        [Key(12)]
        public string loopAnimName;
        
    }
}
