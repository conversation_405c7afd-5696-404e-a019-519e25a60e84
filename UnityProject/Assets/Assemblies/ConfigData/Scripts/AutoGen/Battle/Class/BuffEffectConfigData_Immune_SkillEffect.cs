using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_Immune_SkillEffect : BuffEffectConfigData_StateApply
    {
        [Key(1)]
        public SkillEffectFuncType effectType;
        
        [IgnoreMember]
        public override BuffEffectStateType stateType
        {
            get { return BuffEffectStateType.Immune_SkillEffect; }
        }
    }
}
