using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_PlayTimeline : BattleStageActionConfigData
    {
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.PlayTimeline; }
        }
    }
}
