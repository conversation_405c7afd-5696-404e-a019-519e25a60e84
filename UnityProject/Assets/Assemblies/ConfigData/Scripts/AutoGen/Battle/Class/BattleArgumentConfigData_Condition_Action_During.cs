using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Action_During : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public BattleArgumentConfigData_Entity subject;
        
        [Key(1)]
        public bool isActive;
        
        [Key(2)]
        public bool isSkill;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Action_During; }
        }
    }
}
