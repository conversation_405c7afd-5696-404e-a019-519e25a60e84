using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Grid_Range_Select1 : BattleArgumentConfigData_Grid
    {
        [Key(0)]
        public TargetSelectRangeId rangeId;
        
        [IgnoreMember]
        public override BattleArgumentGridFuncType funcType
        {
            get { return BattleArgumentGridFuncType.Range_Select1; }
        }
    }
}
