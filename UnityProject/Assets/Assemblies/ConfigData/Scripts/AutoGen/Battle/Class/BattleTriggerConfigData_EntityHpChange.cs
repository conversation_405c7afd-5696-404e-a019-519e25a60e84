using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTriggerConfigData_EntityHpChange : BattleTriggerConfigData
    {
        [Key(1)]
        public int entityUid;
        
        [Key(2)]
        public CompareType compareType;
        
        [Key(3)]
        public int hpRate;
        
        [IgnoreMember]
        public override BattleTriggerType funcType
        {
            get { return BattleTriggerType.EntityHpChange; }
        }
    }
}
