using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleStageActionConfigData_TurnDir_Actor_Uid_Dir))]
    [Union(1, typeof(BattleStageActionConfigData_TurnDir_Actor_Uid_LookPos))]
    public abstract partial class BattleStageActionConfigData_TurnDir : BattleStageActionConfigData
    {
        [Key(0)]
        public float time;
        
        [Key(1)]
        public bool showAnimation;
        
        [Key(2)]
        public bool waitEnd;
        
    }
}
