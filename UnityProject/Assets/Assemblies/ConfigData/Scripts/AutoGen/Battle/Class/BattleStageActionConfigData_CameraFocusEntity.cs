using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_CameraFocusEntity : BattleStageActionConfigData
    {
        [Key(0)]
        public int actorUid;
        
        [Key(1)]
        public bool immediately;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.CameraFocusEntity; }
        }
    }
}
