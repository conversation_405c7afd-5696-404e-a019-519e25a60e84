using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTriggerConfigData_EntityDead : BattleTriggerConfigData
    {
        [Key(1)]
        public int entityUid;
        
        [IgnoreMember]
        public override BattleTriggerType funcType
        {
            get { return BattleTriggerType.EntityDead; }
        }
    }
}
