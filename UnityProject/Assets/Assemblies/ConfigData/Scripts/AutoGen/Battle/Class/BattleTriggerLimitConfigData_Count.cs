using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTriggerLimitConfigData_Count : BattleTriggerLimitConfigData
    {
        [Key(0)]
        public int count;
        
        [IgnoreMember]
        public override BattleTriggerLimitType limitType
        {
            get { return BattleTriggerLimitType.Count; }
        }
    }
}
