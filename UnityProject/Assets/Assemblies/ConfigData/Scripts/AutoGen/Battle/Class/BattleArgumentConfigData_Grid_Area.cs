using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Grid_Area : BattleArgumentConfigData_Grid
    {
        [Key(0)]
        public BattlePos2ConfigData startPos;
        
        [Key(1)]
        public BattlePos2ConfigData endPos;
        
        [IgnoreMember]
        public override BattleArgumentGridFuncType funcType
        {
            get { return BattleArgumentGridFuncType.Area; }
        }
    }
}
