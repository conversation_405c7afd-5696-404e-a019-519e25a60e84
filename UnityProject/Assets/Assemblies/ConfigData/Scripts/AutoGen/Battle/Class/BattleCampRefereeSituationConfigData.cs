using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleCampRefereeSituationConfigData_TurnEnd))]
    [Union(1, typeof(BattleCampRefereeSituationConfigData_EnemyAllDead))]
    [Union(2, typeof(BattleCampRefereeSituationConfigData_SelfAllDead))]
    [Union(3, typeof(BattleCampRefereeSituationConfigData_EntityListAllDead))]
    [Union(4, typeof(BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid))]
    [Union(5, typeof(BattleCampRefereeSituationConfigData_AnyActorOccupyAnyGrid2))]
    public abstract partial class BattleCampRefereeSituationConfigData
    {
        [Key(0)]
        public bool enabled;
        
        [Key(1)]
        public string desc;
        
        [IgnoreMember]
        public abstract BattleCampRefereeSituationType refereeType { get; }
    }
}
