using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BuffEffectConfigData_Stun))]
    [Union(1, typeof(BuffEffectConfigData_AssistGuard))]
    [Union(2, typeof(BuffEffectConfigData_Dodge))]
    [Union(3, typeof(BuffEffectConfigData_Immune_SkillEffect))]
    [Union(4, typeof(BuffEffectConfigData_Immune_AttachBuff_Tag))]
    [Union(5, typeof(BuffEffectGuardEffectConfigData))]
    [Union(6, typeof(BuffEffectBanSkillConfigData))]
    [Union(7, typeof(BuffEffectTriggerContinuousAttackConfigData))]
    [Union(8, typeof(BuffEffectTriggerAdditionalAttackConfigData))]
    [Union(9, typeof(BuffEffectCanResurrectConfigData))]
    public abstract partial class BuffEffectConfigData_StateApply : BuffEffectConfigData
    {
        [IgnoreMember]
        public abstract BuffEffectStateType stateType { get; }
        [IgnoreMember]
        public override BuffEffectType effectType
        {
            get { return BuffEffectType.StateApply; }
        }
    }
}
