using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectGuardEffectConfigData : BuffEffectConfigData_StateApply
    {
        [Key(1)]
        public int count;
        
        [IgnoreMember]
        public override BuffEffectStateType stateType
        {
            get { return BuffEffectStateType.GuardEffect; }
        }
    }
}
