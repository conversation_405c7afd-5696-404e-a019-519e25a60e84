using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleStageActionConfigData_Sequence))]
    [Union(1, typeof(BattleStageActionConfigData_Parallel))]
    [Union(2, typeof(BattleStageActionConfigData_Delay))]
    [Union(3, typeof(BattleStageActionConfigData_Dialog))]
    [Union(4, typeof(BattleStageActionConfigData_DialogSelection))]
    [Union(5, typeof(BattleStageActionConfigData_DialogBubble))]
    [Union(6, typeof(BattleStageActionConfigData_AttachBuff))]
    [Union(7, typeof(BattleStageActionConfigData_DetachBuff_Actor_Uid))]
    [Union(8, typeof(BattleStageActionConfigData_Summon_Actor))]
    [Union(9, typeof(BattleStageActionConfigData_Teleport_Actor_Uid))]
    [Union(10, typeof(BattleStageActionConfigData_Retreat_Actor_Uid))]
    [Union(11, typeof(BattleStageActionConfigData_Damage_Actor_Uid))]
    [Union(12, typeof(BattleStageActionConfigData_Heal_Actor_Uid))]
    [Union(13, typeof(BattleStageActionConfigData_ChangeTeam_Actor_Uid))]
    [Union(14, typeof(BattleStageActionConfigData_ChangeBehavior_Actor_Uid))]
    [Union(15, typeof(BattleStageActionConfigData_Move_Actor_Uid))]
    [Union(16, typeof(BattleStageActionConfigData_PlayAnim_Actor_Uid))]
    [Union(17, typeof(BattleStageActionConfigData_TurnDir_Actor_Uid))]
    [Union(18, typeof(BattleStageActionConfigData_Select_Actor_Uid))]
    [Union(19, typeof(BattleStageActionConfigData_CameraFocusPosition))]
    [Union(20, typeof(BattleStageActionConfigData_CameraFocusEntity))]
    [Union(21, typeof(BattleStageActionConfigData_CameraFollow))]
    [Union(22, typeof(BattleStageActionConfigData_CameraShake))]
    [Union(23, typeof(BattleStageActionConfigData_SpawnTerrainEffect))]
    [Union(24, typeof(BattleStageActionConfigData_DespawnTerrainEffect))]
    [Union(25, typeof(BattleStageActionConfigData_PlayTimeline))]
    [Union(26, typeof(BattleStageActionConfigData_PlaySound))]
    [Union(27, typeof(BattleStageActionConfigData_PlayBgm))]
    [Union(28, typeof(BattleStageActionConfigData_AddTeamDecisionMark))]
    [Union(29, typeof(BattleStageActionConfigData_SetWinConditionEnabled))]
    [Union(30, typeof(BattleStageActionConfigData_ToggleHudVisibility))]
    [Union(31, typeof(BattleStageActionConfigData_AttachBuff_Default))]
    [Union(32, typeof(BattleStageActionConfigData_AttachBuff_Actor_Uid))]
    [Union(33, typeof(BattleStageActionConfigData_AttachBuff_Actor_TeamUid))]
    [Union(34, typeof(BattleStageActionConfigData_AttachBuff_Actor_CampUid))]
    public abstract partial class BattleStageActionConfigData
    {
        [IgnoreMember]
        public abstract BattleStageActionType actionType { get; }
    }
}
