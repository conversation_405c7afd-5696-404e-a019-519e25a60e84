using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Destruct_Range_Self : BattleArgumentConfigData_Entity
    {
        [Key(0)]
        public TargetSelectRangeId rangeId;
        
        [Key(1)]
        public BattleCampRefType campRefType;
        
        [IgnoreMember]
        public override BattleArgumentEntityFuncType funcType
        {
            get { return BattleArgumentEntityFuncType.Destruct_Range_Self; }
        }
    }
}
