using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevel))]
    [Union(1, typeof(SkillEffectConfigData_Summon_TerrainBuff_FullRange))]
    public abstract partial class SkillEffectConfigData_SingleGrid : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Grid grid;
        
    }
}
