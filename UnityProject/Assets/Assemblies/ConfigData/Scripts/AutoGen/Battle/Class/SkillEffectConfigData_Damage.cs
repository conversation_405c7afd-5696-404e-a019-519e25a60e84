using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_Damage : SkillEffectConfigData_SingleTarget
    {
        [Key(2)]
        public SkillDamageType damageType;
        
        [Key(3)]
        public EntityElementId elementType;
        
        [Key(4)]
        public SkillEffectArgumentConfigData argument;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Damage; }
        }
    }
}
