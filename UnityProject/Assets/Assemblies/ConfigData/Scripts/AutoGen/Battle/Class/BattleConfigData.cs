using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleConfigData
    {
        [Key(0)]
        public int id;
        
        [Key(1)]
        public string name;
        
        [Key(2)]
        public int dispositionGroupId;
        
        [Key(3)]
        public List<int> disposedActorList = new List<int>();
        
        [Key(4)]
        public List<BattlePlayerSlotConfigData> playerSlotList = new List<BattlePlayerSlotConfigData>();
        
        [Key(5)]
        public List<BattleStageConfigData> stageList = new List<BattleStageConfigData>();
        
    }
}
