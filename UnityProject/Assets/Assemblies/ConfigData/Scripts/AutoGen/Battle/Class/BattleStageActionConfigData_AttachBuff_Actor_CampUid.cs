using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_AttachBuff_Actor_CampUid : BattleStageActionConfigData_AttachBuff_Default
    {
        [Key(2)]
        public int campUid;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.AttachBuff_Actor_CampUid; }
        }
    }
}
