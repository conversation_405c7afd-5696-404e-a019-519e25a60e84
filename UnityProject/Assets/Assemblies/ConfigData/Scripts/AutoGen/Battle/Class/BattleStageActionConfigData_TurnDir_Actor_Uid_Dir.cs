using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_TurnDir_Actor_Uid_Dir : BattleStageActionConfigData_TurnDir
    {
        [Key(3)]
        public List<int> actorUidList = new List<int>();
        
        [Key(4)]
        public BattleDirType dir;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.TurnDir_Actor_Uid_Dir; }
        }
    }
}
