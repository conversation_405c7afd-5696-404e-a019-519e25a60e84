using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_Parallel : BattleStageActionConfigData
    {
        [Key(0)]
        public List<BattleStageActionConfigData> actionList = new List<BattleStageActionConfigData>();
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.Parallel; }
        }
    }
}
