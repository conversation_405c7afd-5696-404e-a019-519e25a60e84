using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Team_Uid : BattleArgumentConfigData_Team
    {
        [Key(0)]
        public int uid;
        
        [IgnoreMember]
        public override BattleArgumentTeamFuncType funcType
        {
            get { return BattleArgumentTeamFuncType.Uid; }
        }
    }
}
