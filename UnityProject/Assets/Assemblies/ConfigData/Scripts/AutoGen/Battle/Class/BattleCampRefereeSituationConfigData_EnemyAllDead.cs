using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleCampRefereeSituationConfigData_EnemyAllDead : BattleCampRefereeSituationConfigData
    {
        [IgnoreMember]
        public override BattleCampRefereeSituationType refereeType
        {
            get { return BattleCampRefereeSituationType.EnemyAllDead; }
        }
    }
}
