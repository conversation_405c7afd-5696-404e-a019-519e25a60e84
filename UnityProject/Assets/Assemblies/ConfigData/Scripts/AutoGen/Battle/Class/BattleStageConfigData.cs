using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageConfigData
    {
        [Key(0)]
        public BattleStageType stageType;
        
        [Key(1)]
        public string sceneAssetPath;
        
        [Key(2)]
        public string sceneCombatAssetPath;
        
        [Key(3)]
        public string groundAssetPath;
        
        [Key(4)]
        public int groundIndex;
        
        [Key(5)]
        public string bgm;
        
        [Key(6)]
        public BattlePos2ConfigData initCameraPos;
        
        [Key(7)]
        public BattleGroundLimitConfigData groundLimit;
        
        [Key(8)]
        public int initCameraRotateOffset;
        
        [Key(9)]
        public BattleRoundRobinType roundRobinType;
        
        [Key(10)]
        public List<BattleTeamConfigData> teamList = new List<BattleTeamConfigData>();
        
        [Key(11)]
        public List<BattleStageDisposedActorConfigData> disposedActorList = new List<BattleStageDisposedActorConfigData>();
        
        [Key(12)]
        public List<BattleStageDisposedTerrainBuffConfigData> disposedTerrainBuffList = new List<BattleStageDisposedTerrainBuffConfigData>();
        
        [Key(13)]
        public List<BattleStageDispositionConfigData> dispositionList = new List<BattleStageDispositionConfigData>();
        
        [Key(14)]
        public List<BattleStageDisposedActorIdMappingConfigData> mappingList = new List<BattleStageDisposedActorIdMappingConfigData>();
        
        [Key(15)]
        public int dispositionMaxCount;
        
        [Key(16)]
        public BattleStarStampConfigData stamp;
        
        [Key(17)]
        public List<BattleTreasureBoxConfigData> treasureBoxList = new List<BattleTreasureBoxConfigData>();
        
        [Key(18)]
        public List<BattleAchievementConfigData> achievementList = new List<BattleAchievementConfigData>();
        
        [Key(19)]
        public BattleStageRefereeConfigData stageRefreree;
        
        [Key(20)]
        public List<BattleStageTriggerConfigData> triggerList = new List<BattleStageTriggerConfigData>();
        
        [Key(21)]
        public BattleFunctionEnableConfigData functionEnable;
        
    }
}
