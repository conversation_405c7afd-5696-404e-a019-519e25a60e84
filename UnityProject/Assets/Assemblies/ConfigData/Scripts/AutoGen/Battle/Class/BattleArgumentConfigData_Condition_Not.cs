using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Not : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public BattleArgumentConfigData_Condition condition;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Not; }
        }
    }
}
