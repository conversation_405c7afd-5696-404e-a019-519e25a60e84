using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_Retreat_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public List<int> actorUidList = new List<int>();
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.Retreat_Actor_Uid; }
        }
    }
}
