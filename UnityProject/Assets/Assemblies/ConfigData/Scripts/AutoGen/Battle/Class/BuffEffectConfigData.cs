using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BuffEffectConfigData_AttributeChange))]
    [Union(1, typeof(BuffEffectConfigData_AttributeAccumulate))]
    [Union(2, typeof(BuffEffectConfigData_AttributeDepend))]
    [Union(3, typeof(BuffEffectConfigData_StateApply))]
    [Union(4, typeof(BuffEffectConfigData_SkillTrigger))]
    [Union(5, typeof(BuffEffectConfigData_AuraApply))]
    [Union(6, typeof(BuffEffectConfigData_Stun))]
    [Union(7, typeof(BuffEffectConfigData_AssistGuard))]
    [Union(8, typeof(BuffEffectConfigData_Dodge))]
    [Union(9, typeof(BuffEffectConfigData_Immune_SkillEffect))]
    [Union(10, typeof(BuffEffectConfigData_Immune_AttachBuff_Tag))]
    [Union(11, typeof(BuffEffectGuardEffectConfigData))]
    [Union(12, typeof(BuffEffectBanSkillConfigData))]
    [Union(13, typeof(BuffEffectTriggerContinuousAttackConfigData))]
    [Union(14, typeof(BuffEffectTriggerAdditionalAttackConfigData))]
    [Union(15, typeof(BuffEffectCanResurrectConfigData))]
    public abstract partial class BuffEffectConfigData
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [IgnoreMember]
        public abstract BuffEffectType effectType { get; }
    }
}
