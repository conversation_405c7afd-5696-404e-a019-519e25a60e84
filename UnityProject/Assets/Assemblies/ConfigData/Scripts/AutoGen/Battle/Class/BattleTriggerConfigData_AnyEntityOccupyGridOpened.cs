using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTriggerConfigData_AnyEntityOccupyGridOpened : BattleTriggerConfigData
    {
        [Key(1)]
        public BattleObjCheckConfigData_Entity entity;
        
        [Key(2)]
        public BattleArgumentConfigData_Grid grid;
        
        [IgnoreMember]
        public override BattleTriggerType funcType
        {
            get { return BattleTriggerType.AnyEntityOccupyGridOpened; }
        }
    }
}
