using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectTriggerContinuousAttackConfigData : BuffEffectConfigData_StateApply
    {
        [Key(1)]
        public int rate;
        
        [IgnoreMember]
        public override BuffEffectStateType stateType
        {
            get { return BuffEffectStateType.TriggerContinuousAttack; }
        }
    }
}
