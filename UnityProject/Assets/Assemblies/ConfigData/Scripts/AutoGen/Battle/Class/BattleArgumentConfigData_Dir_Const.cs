using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Dir_Const : BattleArgumentConfigData_Dir
    {
        [Key(0)]
        public BattleDirType dirType;
        
        [IgnoreMember]
        public override BattleArgumentDirFuncType funcType
        {
            get { return BattleArgumentDirFuncType.Const; }
        }
    }
}
