using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_DetachBuff_Tag : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Entity target;
        
        [Key(2)]
        public List<BuffTagId> tagList = new List<BuffTagId>();
        
        [Key(3)]
        public int count;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.DetachBuff_Tag; }
        }
    }
}
