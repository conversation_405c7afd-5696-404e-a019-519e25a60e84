using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleObjCheckConfigData_Entity_Uid : BattleObjCheckConfigData_Entity
    {
        [Key(0)]
        public int uid;
        
        [IgnoreMember]
        public override BattleObjCheckEntityType checkType
        {
            get { return BattleObjCheckEntityType.Uid; }
        }
    }
}
