using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BuffTriggerConfigData_CheckSubject))]
    [Union(1, typeof(BuffTriggerConfigData_CheckSubjectAndSkill))]
    [Union(2, typeof(BuffTriggerConfigData_Turn))]
    [Union(3, typeof(BuffTriggerConfigData_MainBattleStart))]
    [Union(4, typeof(BuffTriggerConfigData_AfterLocateEnd))]
    [Union(5, typeof(BuffTriggerConfigData_BeforeActionEnd))]
    [Union(6, typeof(BuffTriggerConfigData_AfterActionEnd))]
    [Union(7, typeof(BuffTriggerConfigData_AfterEntityDead))]
    [Union(8, typeof(BuffTriggerConfigData_AfterEntityMove))]
    [Union(9, typeof(BuffTriggerConfigData_BeforeEngageBegin))]
    [Union(10, typeof(BuffTriggerConfigData_AfterEngageBegin))]
    [Union(11, typeof(BuffTriggerConfigData_BeforeCastSkill))]
    [Union(12, typeof(BuffTriggerConfigData_AfterCastSkill))]
    [Union(13, typeof(BuffTriggerConfigData_BeforeEngageEnd))]
    [Union(14, typeof(BuffTriggerConfigData_AfterEngageEnd))]
    [Union(15, typeof(BuffTriggerConfigData_TurnStart))]
    [Union(16, typeof(BuffTriggerConfigData_TurnEnd))]
    public abstract partial class BuffTriggerConfigData
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [IgnoreMember]
        public abstract BuffTriggerType triggerType { get; }
    }
}
