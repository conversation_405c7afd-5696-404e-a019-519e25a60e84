using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleCampRefereeSituationConfigData_EntityListAllDead : BattleCampRefereeSituationConfigData
    {
        [Key(2)]
        public List<int> entityUidList = new List<int>();
        
        [IgnoreMember]
        public override BattleCampRefereeSituationType refereeType
        {
            get { return BattleCampRefereeSituationType.EntityListAllDead; }
        }
    }
}
