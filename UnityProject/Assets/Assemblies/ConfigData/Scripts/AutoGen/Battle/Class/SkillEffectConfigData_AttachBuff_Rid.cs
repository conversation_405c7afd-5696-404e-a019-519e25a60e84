using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_AttachBuff_Rid : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Entity target;
        
        [Key(2)]
        public List<BattleAttachBuffItemConfigData> itemList = new List<BattleAttachBuffItemConfigData>();
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.AttachBuff_Rid; }
        }
    }
}
