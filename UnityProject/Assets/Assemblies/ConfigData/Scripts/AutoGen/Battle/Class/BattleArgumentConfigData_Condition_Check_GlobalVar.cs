using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Check_GlobalVar : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public int id;
        
        [Key(1)]
        public bool boolValue;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Check_GlobalVar; }
        }
    }
}
