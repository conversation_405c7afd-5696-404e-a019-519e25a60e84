using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleTriggerLimitConfigData_Count))]
    [Union(1, typeof(BattleTriggerLimitConfigData_Count_PerTurn))]
    [Union(2, typeof(BattleTriggerLimitConfigData_Cd_Turn))]
    [Union(3, typeof(BattleTriggerLimitConfigData_Cd_Step))]
    public abstract partial class BattleTriggerLimitConfigData
    {
        [IgnoreMember]
        public abstract BattleTriggerLimitType limitType { get; }
    }
}
