using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Entity_Self : BattleArgumentConfigData_Entity
    {
        [IgnoreMember]
        public override BattleArgumentEntityFuncType funcType
        {
            get { return BattleArgumentEntityFuncType.Self; }
        }
    }
}
