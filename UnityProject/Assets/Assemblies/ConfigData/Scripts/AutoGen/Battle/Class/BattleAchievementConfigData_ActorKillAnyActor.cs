using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleAchievementConfigData_ActorKillAnyActor : BattleAchievementConfigData
    {
        [Key(4)]
        public int actorUid;
        
        [Key(5)]
        public List<int> actorUidList = new List<int>();
        
        [IgnoreMember]
        public override BattleAchievementType achievementType
        {
            get { return BattleAchievementType.ActorKillAnyActor; }
        }
    }
}
