using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(TargetSelectStepConfigData_Self))]
    [Union(1, typeof(TargetSelectStepConfigData_Grid))]
    [Union(2, typeof(TargetSelectStepConfigData_Target))]
    [Union(3, typeof(TargetSelectStepConfigData_Dir))]
    [Union(4, typeof(TargetSelectStepConfigData_TeleportSelf))]
    [Union(5, typeof(TargetSelectStepConfigData_TeleportTarget))]
    [Union(6, typeof(TargetSelectStepConfigData_Summon))]
    public abstract partial class TargetSelectStepConfigData
    {
        [IgnoreMember]
        public abstract TargetSelectStepFuncType funcType { get; }
    }
}
