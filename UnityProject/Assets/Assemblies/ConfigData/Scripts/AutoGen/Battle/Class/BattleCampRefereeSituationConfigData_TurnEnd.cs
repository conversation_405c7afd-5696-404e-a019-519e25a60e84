using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleCampRefereeSituationConfigData_TurnEnd : BattleCampRefereeSituationConfigData
    {
        [Key(2)]
        public int turnIndex;
        
        [IgnoreMember]
        public override BattleCampRefereeSituationType refereeType
        {
            get { return BattleCampRefereeSituationType.TurnEnd; }
        }
    }
}
