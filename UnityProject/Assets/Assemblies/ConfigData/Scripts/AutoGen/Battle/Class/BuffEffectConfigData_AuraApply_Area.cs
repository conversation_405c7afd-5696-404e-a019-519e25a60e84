using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_AuraApply_Area : BuffEffectConfigData
    {
        [Key(1)]
        public TargetSelectRangeId rangeId;
        
        [Key(2)]
        public TargetSelectTargetFilterType filterFuncType;
        
        [Key(3)]
        public int buffRid;
        
        [IgnoreMember]
        public override BuffEffectType effectType
        {
            get { return BuffEffectType.AuraApply_Area; }
        }
    }
}
