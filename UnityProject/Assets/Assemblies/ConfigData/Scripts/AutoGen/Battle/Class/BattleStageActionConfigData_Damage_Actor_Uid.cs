using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_Damage_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public List<int> actorUidList = new List<int>();
        
        [Key(1)]
        public BattleValueBasedActorHpConfigData damageValue;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.Damage_Actor_Uid; }
        }
    }
}
