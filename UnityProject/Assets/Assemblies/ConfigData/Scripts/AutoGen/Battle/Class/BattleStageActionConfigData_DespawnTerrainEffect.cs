using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_DespawnTerrainEffect : BattleStageActionConfigData
    {
        [Key(0)]
        public int uid;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.DespawnTerrainEffect; }
        }
    }
}
