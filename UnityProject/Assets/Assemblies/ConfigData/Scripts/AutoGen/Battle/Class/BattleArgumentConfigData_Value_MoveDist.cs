using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Value_MoveDist : BattleArgumentConfigData_Value
    {
        [IgnoreMember]
        public override BattleArgumentValueFuncType funcType
        {
            get { return BattleArgumentValueFuncType.MoveDist; }
        }
    }
}
