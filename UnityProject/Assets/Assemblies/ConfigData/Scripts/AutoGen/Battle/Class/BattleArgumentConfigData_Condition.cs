using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleArgumentConfigData_Condition_Not))]
    [Union(1, typeof(BattleArgumentConfigData_Condition_And))]
    [Union(2, typeof(BattleArgumentConfigData_Condition_Or))]
    [Union(3, typeof(BattleArgumentConfigData_Condition_Random))]
    [Union(4, typeof(BattleArgumentConfigData_Condition_Action_During))]
    [Union(5, typeof(BattleArgumentConfigData_Condition_Action_Caused))]
    [Union(6, typeof(BattleArgumentConfigData_Condition_Compare_Variable))]
    [Union(7, typeof(BattleArgumentConfigData_Condition_Compare_VariableOpend))]
    [Union(8, typeof(BattleArgumentConfigData_Condition_Check_GlobalVar))]
    public abstract partial class BattleArgumentConfigData_Condition
    {
        [IgnoreMember]
        public abstract BattleArgumentConditionFuncType funcType { get; }
    }
}
