using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_ChangeBehavior_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public List<int> actorUidList = new List<int>();
        
        [Key(1)]
        public EntityAIType aiType;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.ChangeBehavior_Actor_Uid; }
        }
    }
}
