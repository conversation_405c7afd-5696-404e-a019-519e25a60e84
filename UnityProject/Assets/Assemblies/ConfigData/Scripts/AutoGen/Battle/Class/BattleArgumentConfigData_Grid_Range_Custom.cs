using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Grid_Range_Custom : BattleArgumentConfigData_Grid
    {
        [Key(0)]
        public BattleArgumentConfigData_Grid basedGrid;
        
        [Key(1)]
        public BattleArgumentConfigData_Dir dir;
        
        [Key(2)]
        public TargetSelectRangeId rangeId;
        
        [IgnoreMember]
        public override BattleArgumentGridFuncType funcType
        {
            get { return BattleArgumentGridFuncType.Range_Custom; }
        }
    }
}
