using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleAchievementConfigData_WinBeforeTurn : BattleAchievementConfigData
    {
        [Key(4)]
        public int turnIndex;
        
        [IgnoreMember]
        public override BattleAchievementType achievementType
        {
            get { return BattleAchievementType.WinBeforeTurn; }
        }
    }
}
