using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_AttachBuff_Actor_TeamUid : BattleStageActionConfigData_AttachBuff_Default
    {
        [Key(2)]
        public List<int> teamUidList = new List<int>();
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.AttachB<PERSON>_Actor_TeamUid; }
        }
    }
}
