using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleObjCheckConfigData_Entity_CampRef : BattleObjCheckConfigData_Entity
    {
        [Key(0)]
        public BattleCampRefType campRef;
        
        [IgnoreMember]
        public override BattleObjCheckEntityType checkType
        {
            get { return BattleObjCheckEntityType.CampRef; }
        }
    }
}
