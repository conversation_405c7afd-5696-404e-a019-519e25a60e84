using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Action_Caused : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public BattleArgumentConfigData_Entity subject;
        
        [Key(1)]
        public bool criticalHit;
        
        [Key(2)]
        public bool damage;
        
        [Key(3)]
        public bool kill;
        
        [Key(4)]
        public bool assistGuard;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Action_Caused; }
        }
    }
}
