using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_SkillTrigger : BuffEffectConfigData
    {
        [Key(1)]
        public BuffTriggerConfigData trigger;
        
        [Key(2)]
        public BattleTriggerLimitConfigData limit;
        
        [Key(3)]
        public bool costLevel;
        
        [Key(4)]
        public List<SkillEffectConfigData> effectList = new List<SkillEffectConfigData>();
        
        [Key(5)]
        public string dramaName;
        
        [IgnoreMember]
        public override BuffEffectType effectType
        {
            get { return BuffEffectType.SkillTrigger; }
        }
    }
}
