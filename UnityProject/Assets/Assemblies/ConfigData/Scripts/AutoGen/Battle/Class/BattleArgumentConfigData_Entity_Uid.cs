using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Entity_Uid : BattleArgumentConfigData_Entity
    {
        [Key(0)]
        public int uid;
        
        [IgnoreMember]
        public override BattleArgumentEntityFuncType funcType
        {
            get { return BattleArgumentEntityFuncType.Uid; }
        }
    }
}
