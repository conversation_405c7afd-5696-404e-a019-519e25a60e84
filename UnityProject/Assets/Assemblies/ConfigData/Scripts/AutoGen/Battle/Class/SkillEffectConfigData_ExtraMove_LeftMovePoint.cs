using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_ExtraMove_LeftMovePoint : SkillEffectConfigData_SingleTarget
    {
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.ExtraMove_LeftMovePoint; }
        }
    }
}
