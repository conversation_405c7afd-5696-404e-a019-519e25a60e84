using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_Summon_Actor_RidToPos : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Grid grid;
        
        [Key(2)]
        public int rid;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Summon_Actor_RidToPos; }
        }
    }
}
