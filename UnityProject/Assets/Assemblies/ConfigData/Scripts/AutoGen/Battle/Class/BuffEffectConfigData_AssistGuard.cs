using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_AssistGuard : BuffEffectConfigData_StateApply
    {
        [Key(1)]
        public TargetSelectRangeId rangeId;
        
        [Key(2)]
        public TargetSelectTargetFilterType filterFuncType;
        
        [IgnoreMember]
        public override BuffEffectStateType stateType
        {
            get { return BuffEffectStateType.AssistGuard; }
        }
    }
}
