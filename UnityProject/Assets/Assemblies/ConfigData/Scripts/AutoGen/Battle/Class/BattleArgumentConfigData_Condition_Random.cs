using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Random : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public int random;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Random; }
        }
    }
}
