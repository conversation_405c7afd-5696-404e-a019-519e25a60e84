using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Or : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Or; }
        }
    }
}
