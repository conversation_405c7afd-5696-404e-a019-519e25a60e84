using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_DetachBuff_Rid : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Entity target;
        
        [Key(2)]
        public List<BattleDetachBuffItemConfigData> itemList = new List<BattleDetachBuffItemConfigData>();
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.DetachBuff_Rid; }
        }
    }
}
