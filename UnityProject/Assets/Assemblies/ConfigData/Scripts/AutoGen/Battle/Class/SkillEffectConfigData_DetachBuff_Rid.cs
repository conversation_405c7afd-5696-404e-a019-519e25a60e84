using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_DetachBuff_Rid : SkillEffectConfigData_SingleTarget
    {
        [Key(2)]
        public List<BattleDetachBuffItemConfigData> itemList = new List<BattleDetachBuffItemConfigData>();
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.DetachBuff_Rid; }
        }
    }
}
