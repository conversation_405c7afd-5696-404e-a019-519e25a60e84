using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_CameraFocusPosition : BattleStageActionConfigData
    {
        [Key(0)]
        public BattlePos2ConfigData pos;
        
        [Key(1)]
        public bool immediately;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.CameraFocusPosition; }
        }
    }
}
