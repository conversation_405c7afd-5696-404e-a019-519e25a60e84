using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Compare_VariableOpend : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public BattleArgumentConfigData_Value srcValue;
        
        [Key(1)]
        public CompareType compareType;
        
        [Key(2)]
        public BattleArgumentConfigData_Value destValue;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Compare_VariableOpend; }
        }
    }
}
