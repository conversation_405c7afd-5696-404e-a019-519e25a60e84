using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Action_Caused_BySelf : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public bool normalAttack;
        
        [Key(1)]
        public bool combat;
        
        [Key(2)]
        public bool criticalHit;
        
        [Key(3)]
        public bool damage;
        
        [Key(4)]
        public bool kill;
        
        [Key(5)]
        public bool assistGuard;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Action_Caused_BySelf; }
        }
    }
}
