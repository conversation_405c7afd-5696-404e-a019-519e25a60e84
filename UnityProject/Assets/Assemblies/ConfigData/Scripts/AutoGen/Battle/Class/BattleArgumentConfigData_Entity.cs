using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleArgumentConfigData_Entity_Uid))]
    [Union(1, typeof(BattleArgumentConfigData_Entity_UidList))]
    [Union(2, typeof(BattleArgumentConfigData_Entity_Self))]
    [Union(3, typeof(BattleArgumentConfigData_Entity_Union))]
    [Union(4, typeof(BattleArgumentConfigData_Entity_Summoner))]
    [Union(5, typeof(BattleArgumentConfigData_Entity_CombatTarget))]
    [Union(6, typeof(BattleArgumentConfigData_Entity_SkillCaster))]
    [Union(7, typeof(BattleArgumentConfigData_Destruct_Range_Self))]
    [Union(8, typeof(BattleArgumentConfigData_Destruct_Pos1))]
    [Union(9, typeof(BattleArgumentConfigData_Destruct_Range_Select1))]
    [Union(10, typeof(BattleArgumentConfigData_Destruct_Pos_Custom))]
    [Union(11, typeof(BattleArgumentConfigData_Destruct_Range_Custom))]
    [Union(12, typeof(BattleArgumentConfigData_LastSkillEffectTarget))]
    public abstract partial class BattleArgumentConfigData_Entity
    {
        [IgnoreMember]
        public abstract BattleArgumentEntityFuncType funcType { get; }
    }
}
