using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_Immune_AttachBuff_Tag : BuffEffectConfigData_StateApply
    {
        [Key(1)]
        public List<BuffTagId> tagList = new List<BuffTagId>();
        
        [IgnoreMember]
        public override BuffEffectStateType stateType
        {
            get { return BuffEffectStateType.Immune_AttachBuff_Tag; }
        }
    }
}
