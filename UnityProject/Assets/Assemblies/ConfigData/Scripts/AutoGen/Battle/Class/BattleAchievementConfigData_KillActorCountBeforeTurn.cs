using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleAchievementConfigData_KillActorCountBeforeTurn : BattleAchievementConfigData
    {
        [Key(4)]
        public int teamUid;
        
        [Key(5)]
        public int turnIndex;
        
        [Key(6)]
        public int killCount;
        
        [IgnoreMember]
        public override BattleAchievementType achievementType
        {
            get { return BattleAchievementType.KillActorCountBeforeTurn; }
        }
    }
}
