using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_Transform_Actor : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Entity target;
        
        [Key(2)]
        public int actorRid;
        
        [Key(3)]
        public int lifeTime;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Transform_Actor; }
        }
    }
}
