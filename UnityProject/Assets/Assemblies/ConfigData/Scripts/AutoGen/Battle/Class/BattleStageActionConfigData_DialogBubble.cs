using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_DialogBubble : BattleStageActionConfigData
    {
        [Key(0)]
        public int bubbleId;
        
        [Key(1)]
        public List<int> actorUid = new List<int>();
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.DialogBubble; }
        }
    }
}
