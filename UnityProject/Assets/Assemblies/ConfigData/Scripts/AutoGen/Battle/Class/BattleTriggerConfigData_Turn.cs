using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleTriggerConfigData_TurnStart))]
    [Union(1, typeof(BattleTriggerConfigData_TurnEnd))]
    public abstract partial class BattleTriggerConfigData_Turn : BattleTriggerConfigData
    {
        [Key(1)]
        public CompareType compareType;
        
        [Key(2)]
        public int turnIndex;
        
    }
}
