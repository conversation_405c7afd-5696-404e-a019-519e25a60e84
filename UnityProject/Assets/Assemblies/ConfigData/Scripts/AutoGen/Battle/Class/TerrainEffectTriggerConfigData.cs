using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class TerrainEffectTriggerConfigData
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [Key(1)]
        public TerrainTriggerMomentType momentType;
        
        [Key(2)]
        public List<SkillEffectConfigData> effectList = new List<SkillEffectConfigData>();
        
    }
}
