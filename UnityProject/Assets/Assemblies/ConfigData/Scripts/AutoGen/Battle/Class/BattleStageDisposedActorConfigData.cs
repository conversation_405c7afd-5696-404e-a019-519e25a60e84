using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageDisposedActorConfigData
    {
        [Key(0)]
        public int uid;
        
        [Key(1)]
        public int rid;
        
        [Key(2)]
        public int level;
        
        [Key(3)]
        public int teamId;
        
        [Key(4)]
        public BattlePos2ConfigData position;
        
        [Key(5)]
        public int initHpRate;
        
        [Key(6)]
        public BattleDirType gridDir;
        
    }
}
