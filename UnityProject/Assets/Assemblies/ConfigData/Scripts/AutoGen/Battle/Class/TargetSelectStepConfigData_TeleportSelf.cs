using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class TargetSelectStepConfigData_TeleportSelf : TargetSelectStepConfigData
    {
        [Key(0)]
        public TargetSelectRangeId rangeId;
        
        [IgnoreMember]
        public override TargetSelectStepFuncType funcType
        {
            get { return TargetSelectStepFuncType.TeleportSelf; }
        }
    }
}
