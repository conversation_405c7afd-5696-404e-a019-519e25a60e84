using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class SkillEffectConfigData_AttachBuff_RandomRid : SkillEffectConfigData
    {
        [Key(1)]
        public BattleArgumentConfigData_Entity target;
        
        [Key(2)]
        public int buffBatchRid;
        
        [Key(3)]
        public int count;
        
        [IgnoreMember]
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.AttachBuff_RandomRid; }
        }
    }
}
