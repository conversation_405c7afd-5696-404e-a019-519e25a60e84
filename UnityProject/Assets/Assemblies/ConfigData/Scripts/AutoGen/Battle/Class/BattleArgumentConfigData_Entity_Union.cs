using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Entity_Union : BattleArgumentConfigData_Entity
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Entity> entityList = new List<BattleArgumentConfigData_Entity>();
        
        [IgnoreMember]
        public override BattleArgumentEntityFuncType funcType
        {
            get { return BattleArgumentEntityFuncType.Union; }
        }
    }
}
