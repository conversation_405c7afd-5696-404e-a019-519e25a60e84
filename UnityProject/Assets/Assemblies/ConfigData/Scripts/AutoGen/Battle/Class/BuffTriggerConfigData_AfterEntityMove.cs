using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffTriggerConfigData_AfterEntityMove : BuffTriggerConfigData_CheckSubject
    {
        [IgnoreMember]
        public override BuffTriggerType triggerType
        {
            get { return BuffTriggerType.AfterEntityMove; }
        }
    }
}
