using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Condition_Action_During_Combat : BattleArgumentConfigData_Condition
    {
        [Key(0)]
        public BattleObjCheckConfigData_Entity subjectCheck;
        
        [IgnoreMember]
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Action_During_Combat; }
        }
    }
}
