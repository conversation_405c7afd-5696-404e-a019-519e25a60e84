using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleStageActionConfigData_AttachBuff_Default))]
    [Union(1, typeof(BattleStageActionConfigData_AttachBuff_Actor_Uid))]
    [Union(2, typeof(BattleStageActionConfigData_AttachBuff_Actor_TeamUid))]
    [Union(3, typeof(BattleStageActionConfigData_AttachBuff_Actor_CampUid))]
    public abstract partial class BattleStageActionConfigData_AttachBuff : BattleStageActionConfigData
    {
        [Key(0)]
        public bool skipEffect;
        
    }
}
