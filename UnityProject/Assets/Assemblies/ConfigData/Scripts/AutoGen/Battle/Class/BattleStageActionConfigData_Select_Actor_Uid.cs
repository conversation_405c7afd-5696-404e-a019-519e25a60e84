using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_Select_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public int actorUid;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.Select_Actor_Uid; }
        }
    }
}
