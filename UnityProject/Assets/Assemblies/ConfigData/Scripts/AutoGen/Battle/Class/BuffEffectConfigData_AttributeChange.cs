using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BuffEffectConfigData_AttributeChange : BuffEffectConfigData
    {
        [Key(1)]
        public AttributePartId partId;
        
        [Key(2)]
        public int value;
        
        [IgnoreMember]
        public override BuffEffectType effectType
        {
            get { return BuffEffectType.AttributeChange; }
        }
    }
}
