using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Grid_PosList : BattleArgumentConfigData_Grid
    {
        [Key(0)]
        public List<BattlePos2ConfigData> pos = new List<BattlePos2ConfigData>();
        
        [IgnoreMember]
        public override BattleArgumentGridFuncType funcType
        {
            get { return BattleArgumentGridFuncType.PosList; }
        }
    }
}
