using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleArgumentConfigData_Value_TeamEnergy : BattleArgumentConfigData_Value
    {
        [Key(0)]
        public BattleArgumentConfigData_Team team;
        
        [IgnoreMember]
        public override BattleArgumentValueFuncType funcType
        {
            get { return BattleArgumentValueFuncType.TeamEnergy; }
        }
    }
}
