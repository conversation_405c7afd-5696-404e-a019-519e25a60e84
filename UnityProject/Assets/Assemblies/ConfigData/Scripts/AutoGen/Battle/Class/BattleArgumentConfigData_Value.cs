using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleArgumentConfigData_Value_MoveDist))]
    [Union(1, typeof(BattleArgumentConfigData_Value_TargetDist))]
    [Union(2, typeof(BattleArgumentConfigData_Value_TurnIndex))]
    [Union(3, typeof(BattleArgumentConfigData_Value_StepIndex))]
    [Union(4, typeof(BattleArgumentConfigData_Value_TeamEnergy))]
    [Union(5, typeof(BattleArgumentConfigData_Value_HpRate))]
    public abstract partial class BattleArgumentConfigData_Value
    {
        [IgnoreMember]
        public abstract BattleArgumentValueFuncType funcType { get; }
    }
}
