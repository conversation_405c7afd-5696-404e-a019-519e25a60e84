using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleArgumentConfigData_Team_Uid))]
    [Union(1, typeof(BattleArgumentConfigData_Team_Self))]
    [Union(2, typeof(BattleArgumentConfigData_Team_Entity))]
    [Union(3, typeof(BattleArgumentConfigData_Team_CampRef_Self))]
    public abstract partial class BattleArgumentConfigData_Team
    {
        [IgnoreMember]
        public abstract BattleArgumentTeamFuncType funcType { get; }
    }
}
