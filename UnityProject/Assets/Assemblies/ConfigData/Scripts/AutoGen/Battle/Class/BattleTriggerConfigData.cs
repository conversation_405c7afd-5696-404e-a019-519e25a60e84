using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleTriggerConfigData_StageEnter))]
    [Union(1, typeof(BattleTriggerConfigData_StageWin))]
    [Union(2, typeof(BattleTriggerConfigData_StageLose))]
    [Union(3, typeof(BattleTriggerConfigData_Turn))]
    [Union(4, typeof(BattleTriggerConfigData_AnyEntityOccupyPos))]
    [Union(5, typeof(BattleTriggerConfigData_AnyEntityOccupyArea))]
    [Union(6, typeof(BattleTriggerConfigData_AnyEntityOccupyGridOpened))]
    [Union(7, typeof(BattleTriggerConfigData_EntityHpChange))]
    [Union(8, typeof(BattleTriggerConfigData_EntityHpChangeOpened))]
    [Union(9, typeof(BattleTriggerConfigData_EntityDead))]
    [Union(10, typeof(BattleTriggerConfigData_TurnStart))]
    [Union(11, typeof(BattleTriggerConfigData_TurnEnd))]
    public abstract partial class BattleTriggerConfigData
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [IgnoreMember]
        public abstract BattleTriggerType funcType { get; }
    }
}
