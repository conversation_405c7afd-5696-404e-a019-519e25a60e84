using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleCampRefereeConfigData
    {
        [Key(0)]
        public int campId;
        
        [Key(1)]
        public List<BattleCampRefereeSituationConfigData> winSituationList = new List<BattleCampRefereeSituationConfigData>();
        
        [Key(2)]
        public List<BattleCampRefereeSituationConfigData> loseSituationList = new List<BattleCampRefereeSituationConfigData>();
        
    }
}
