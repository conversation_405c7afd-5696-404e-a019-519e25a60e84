using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTriggerConfigData_AnyEntityOccupyPos : BattleTriggerConfigData
    {
        [Key(1)]
        public int entityUidList;
        
        [Key(2)]
        public BattlePos2ConfigData pos;
        
        [IgnoreMember]
        public override BattleTriggerType funcType
        {
            get { return BattleTriggerType.AnyEntityOccupyPos; }
        }
    }
}
