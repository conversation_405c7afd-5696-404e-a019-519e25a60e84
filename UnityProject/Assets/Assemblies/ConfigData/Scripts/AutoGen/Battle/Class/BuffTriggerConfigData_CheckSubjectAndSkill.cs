using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BuffTriggerConfigData_BeforeEngageBegin))]
    [Union(1, typeof(BuffTriggerConfigData_AfterEngageBegin))]
    [Union(2, typeof(BuffTriggerConfigData_BeforeCastSkill))]
    [Union(3, typeof(BuffTriggerConfigData_AfterCastSkill))]
    [Union(4, typeof(BuffTriggerConfigData_BeforeEngageEnd))]
    [Union(5, typeof(BuffTriggerConfigData_AfterEngageEnd))]
    public abstract partial class BuffTriggerConfigData_CheckSubjectAndSkill : BuffTriggerConfigData
    {
        [Key(1)]
        public BattleObjCheckConfigData_Entity entityCheck;
        
        [Key(2)]
        public BattleObjCheckConfigData_Skill skillCheck;
        
    }
}
