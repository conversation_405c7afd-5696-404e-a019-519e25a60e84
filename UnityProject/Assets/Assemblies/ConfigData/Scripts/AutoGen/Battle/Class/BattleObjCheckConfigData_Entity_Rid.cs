using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleObjCheckConfigData_Entity_Rid : BattleObjCheckConfigData_Entity
    {
        [Key(0)]
        public List<int> ridList = new List<int>();
        
        [IgnoreMember]
        public override BattleObjCheckEntityType checkType
        {
            get { return BattleObjCheckEntityType.Rid; }
        }
    }
}
