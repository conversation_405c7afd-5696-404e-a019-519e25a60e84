using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_ChangeTeam_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public List<int> actorUidList = new List<int>();
        
        [Key(1)]
        public int teamUid;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.ChangeTeam_Actor_Uid; }
        }
    }
}
