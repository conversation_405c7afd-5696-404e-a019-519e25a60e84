using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleObjCheckConfigData_Skill_Rid))]
    [Union(1, typeof(BattleObjCheckConfigData_Skill_Tag))]
    public abstract partial class BattleObjCheckConfigData_Skill
    {
        [IgnoreMember]
        public abstract BattleObjCheckSkillType checkType { get; }
    }
}
