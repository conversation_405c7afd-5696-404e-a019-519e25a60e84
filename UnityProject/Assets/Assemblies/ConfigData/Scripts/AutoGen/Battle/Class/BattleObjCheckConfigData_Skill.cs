using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleObjCheckConfigData_Skill_Any))]
    [Union(1, typeof(BattleObjCheckConfigData_Skill_Rid))]
    [Union(2, typeof(BattleObjCheckConfigData_Skill_Tag))]
    [Union(3, typeof(BattleObjCheckConfigData_Skill_Running))]
    public abstract partial class BattleObjCheckConfigData_Skill
    {
        [IgnoreMember]
        public abstract BattleObjCheckSkillType checkType { get; }
    }
}
