using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BuffTriggerConfigData_TurnStart))]
    [Union(1, typeof(BuffTriggerConfigData_TurnEnd))]
    public abstract partial class BuffTriggerConfigData_Turn : BuffTriggerConfigData
    {
        [Key(1)]
        public CompareType compareType;
        
        [Key(2)]
        public int turnIndex;
        
    }
}
