using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleObjCheckConfigData_Skill_Tag : BattleObjCheckConfigData_Skill
    {
        [Key(0)]
        public List<SkillTagType> tagList = new List<SkillTagType>();
        
        [IgnoreMember]
        public override BattleObjCheckSkillType checkType
        {
            get { return BattleObjCheckSkillType.Tag; }
        }
    }
}
