using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageTriggerConfigData
    {
        [Key(0)]
        public string desc;
        
        [Key(1)]
        public BattleTriggerConfigData trigger;
        
        [Key(2)]
        public BattleTriggerLimitConfigData limit;
        
        [Key(3)]
        public List<BattleStageActionConfigData> actionList = new List<BattleStageActionConfigData>();
        
    }
}
