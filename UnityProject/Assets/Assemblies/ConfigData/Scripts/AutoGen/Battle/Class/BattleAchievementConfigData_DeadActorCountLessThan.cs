using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleAchievementConfigData_DeadActorCountLessThan : BattleAchievementConfigData
    {
        [Key(4)]
        public int teamUid;
        
        [Key(5)]
        public int deadCount;
        
        [IgnoreMember]
        public override BattleAchievementType achievementType
        {
            get { return BattleAchievementType.DeadActorCountLessThan; }
        }
    }
}
