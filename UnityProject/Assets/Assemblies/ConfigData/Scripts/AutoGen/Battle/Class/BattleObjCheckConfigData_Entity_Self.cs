using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleObjCheckConfigData_Entity_Self : BattleObjCheckConfigData_Entity
    {
        [IgnoreMember]
        public override BattleObjCheckEntityType checkType
        {
            get { return BattleObjCheckEntityType.Self; }
        }
    }
}
