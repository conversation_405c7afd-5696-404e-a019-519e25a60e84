using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTriggerLimitConfigData_Cd_Turn : BattleTriggerLimitConfigData
    {
        [Key(0)]
        public int cd;
        
        [IgnoreMember]
        public override BattleTriggerLimitType limitType
        {
            get { return BattleTriggerLimitType.Cd_Turn; }
        }
    }
}
