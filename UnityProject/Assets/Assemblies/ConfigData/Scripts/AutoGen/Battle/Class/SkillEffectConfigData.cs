using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(SkillEffectConfigData_AttachBuff_Rid))]
    [Union(1, typeof(SkillEffectConfigData_AttachBuff_RandomRid))]
    [Union(2, typeof(SkillEffectConfigData_AttachBuff_RandomCustom))]
    [Union(3, typeof(SkillEffectConfigData_DetachBuff_Rid))]
    [Union(4, typeof(SkillEffectConfigData_DetachBuff_Tag))]
    [Union(5, typeof(SkillEffectConfigData_Damage_Default))]
    [Union(6, typeof(SkillEffectConfigData_Damage_RandomInRange))]
    [Union(7, typeof(SkillEffectConfigData_Heal_Default))]
    [Union(8, typeof(SkillEffectConfigData_Summon_Actor_RidToPos))]
    [Union(9, typeof(SkillEffectConfigData_Summon_Actor_RidToPos_ConstLevel))]
    [Union(10, typeof(SkillEffectConfigData_Summon_TerrainBuff_FullRange))]
    [Union(11, typeof(SkillEffectConfigData_Move_Actor))]
    [Union(12, typeof(SkillEffectConfigData_Teleport_Actor))]
    [Union(13, typeof(SkillEffectConfigData_Control_Actor))]
    [Union(14, typeof(SkillEffectConfigData_Transform_Actor))]
    [Union(15, typeof(SkillEffectConfigData_ExtraMove_ConstMovePoint))]
    [Union(16, typeof(SkillEffectConfigData_ExtraMove_LeftMovePoint))]
    [Union(17, typeof(SkillEffectConfigData_ExtraAction))]
    [Union(18, typeof(SkillEffectConfigData_ChangeTeamEnergy))]
    [Union(19, typeof(SkillEffectConfigData_ChangeSkillCd))]
    public abstract partial class SkillEffectConfigData
    {
        [Key(0)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [IgnoreMember]
        public abstract SkillEffectFuncType effectType { get; }
    }
}
