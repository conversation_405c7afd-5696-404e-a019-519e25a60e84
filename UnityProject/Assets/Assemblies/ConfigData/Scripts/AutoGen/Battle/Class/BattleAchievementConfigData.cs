using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    [Union(0, typeof(BattleAchievementConfigData_WinBeforeTurn))]
    [Union(1, typeof(BattleAchievementConfigData_DeadActorCountLessThan))]
    [Union(2, typeof(BattleAchievementConfigData_KillActorCountBeforeTurn))]
    [Union(3, typeof(BattleAchievementConfigData_ActorKillAnyActor))]
    [Union(4, typeof(BattleAchievementConfigData_EnemyDeadCountBeforeTurn))]
    public abstract partial class BattleAchievementConfigData
    {
        [Key(0)]
        public int achievementId;
        
        [Key(1)]
        public string name;
        
        [Key(2)]
        public string desc;
        
        [Key(3)]
        public int rewardId;
        
        [IgnoreMember]
        public abstract BattleAchievementType achievementType { get; }
    }
}
