using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleTeamConfigData
    {
        [Key(0)]
        public int id;
        
        [Key(1)]
        public int configId;
        
        [Key(2)]
        public int campId;
        
        [Key(3)]
        public int playerSlotId;
        
        [Key(4)]
        public int formationId;
        
        [Key(5)]
        public bool isAggressive;
        
    }
}
