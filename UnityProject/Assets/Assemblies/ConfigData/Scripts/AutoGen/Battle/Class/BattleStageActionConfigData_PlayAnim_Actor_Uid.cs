using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_PlayAnim_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public int actorUid;
        
        [Key(1)]
        public string animationName;
        
        [Key(2)]
        public float speed;
        
        [Key(3)]
        public float fadeTime;
        
        [Key(4)]
        public bool loop;
        
        [Key(5)]
        public bool returnIdle;
        
        [Key(6)]
        public bool waitEnd;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.PlayAnim_Actor_Uid; }
        }
    }
}
