using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class StrategySkillConfigData
    {
        [Key(0)]
        public int id;
        
        [Key(1)]
        public string name;
        
        [Key(2)]
        public string iconName;
        
        [Key(3)]
        public SkillIndicatorType indicatorType;
        
        [Key(4)]
        public int coolTime;
        
        [Key(5)]
        public List<BattleArgumentConfigData_Condition> conditionList = new List<BattleArgumentConfigData_Condition>();
        
        [Key(6)]
        public BattleTriggerConfigData trigger;
        
        [Key(7)]
        public TargetSelectStepConfigData selectStep;
        
        [Key(8)]
        public List<SkillEffectConfigData> effectList = new List<SkillEffectConfigData>();
        
        [Key(9)]
        public int mainEffectIndex;
        
    }
}
