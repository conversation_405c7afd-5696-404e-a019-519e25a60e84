using System.Collections.Generic;
using MessagePack;
using Phoenix.ConfigData;

namespace Phoenix.ConfigData
{
    [MessagePackObject]
    public partial class BattleStageActionConfigData_Move_Actor_Uid : BattleStageActionConfigData
    {
        [Key(0)]
        public int actorUid;
        
        [Key(1)]
        public BattlePos2ConfigData movePos;
        
        [Key(2)]
        public bool moveBack;
        
        [IgnoreMember]
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.Move_Actor_Uid; }
        }
    }
}
