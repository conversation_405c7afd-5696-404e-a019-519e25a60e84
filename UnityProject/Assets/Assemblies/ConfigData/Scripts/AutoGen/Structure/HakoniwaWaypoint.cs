// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: HakoniwaWaypoint.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from HakoniwaWaypoint.proto</summary>
  public static partial class HakoniwaWaypointReflection {

    #region Descriptor
    /// <summary>File descriptor for HakoniwaWaypoint.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HakoniwaWaypointReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChZIYWtvbml3YVdheXBvaW50LnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEa",
            "E1NjZW5lTG9jYXRpb24ucHJvdG8iigEKGkhha29uaXdhV2F5cG9pbnRDb25m",
            "aWdEYXRhEgoKAklkGAEgASgFEgwKBE5hbWUYAiABKAkSPQoITG9jYXRpb24Y",
            "AyABKAsyKy5QaG9lbml4LkNvbmZpZ0RhdGEuU2NlbmVMb2NhdGlvbkNvbmZp",
            "Z0RhdGESEwoLSGFrb1NjZW5lSWQYBCABKAViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.SceneLocationReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.HakoniwaWaypointConfigData), global::Phoenix.ConfigData.HakoniwaWaypointConfigData.Parser, new[]{ "Id", "Name", "Location", "HakoSceneId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class HakoniwaWaypointConfigData : pb::IMessage<HakoniwaWaypointConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HakoniwaWaypointConfigData> _parser = new pb::MessageParser<HakoniwaWaypointConfigData>(() => new HakoniwaWaypointConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<HakoniwaWaypointConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.HakoniwaWaypointReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaWaypointConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaWaypointConfigData(HakoniwaWaypointConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      location_ = other.location_ != null ? other.location_.Clone() : null;
      hakoSceneId_ = other.hakoSceneId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaWaypointConfigData Clone() {
      return new HakoniwaWaypointConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Location" field.</summary>
    public const int LocationFieldNumber = 3;
    private global::Phoenix.ConfigData.SceneLocationConfigData location_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.SceneLocationConfigData Location {
      get { return location_; }
      set {
        location_ = value;
      }
    }

    /// <summary>Field number for the "HakoSceneId" field.</summary>
    public const int HakoSceneIdFieldNumber = 4;
    private int hakoSceneId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int HakoSceneId {
      get { return hakoSceneId_; }
      set {
        hakoSceneId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as HakoniwaWaypointConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(HakoniwaWaypointConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (!object.Equals(Location, other.Location)) return false;
      if (HakoSceneId != other.HakoSceneId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (location_ != null) hash ^= Location.GetHashCode();
      if (HakoSceneId != 0) hash ^= HakoSceneId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (location_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Location);
      }
      if (HakoSceneId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(HakoSceneId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (location_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Location);
      }
      if (HakoSceneId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(HakoSceneId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (location_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Location);
      }
      if (HakoSceneId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HakoSceneId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(HakoniwaWaypointConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.location_ != null) {
        if (location_ == null) {
          Location = new global::Phoenix.ConfigData.SceneLocationConfigData();
        }
        Location.MergeFrom(other.Location);
      }
      if (other.HakoSceneId != 0) {
        HakoSceneId = other.HakoSceneId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            if (location_ == null) {
              Location = new global::Phoenix.ConfigData.SceneLocationConfigData();
            }
            input.ReadMessage(Location);
            break;
          }
          case 32: {
            HakoSceneId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            if (location_ == null) {
              Location = new global::Phoenix.ConfigData.SceneLocationConfigData();
            }
            input.ReadMessage(Location);
            break;
          }
          case 32: {
            HakoSceneId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
