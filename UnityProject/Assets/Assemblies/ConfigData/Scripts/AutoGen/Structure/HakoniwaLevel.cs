// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: HakoniwaLevel.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from HakoniwaLevel.proto</summary>
  public static partial class HakoniwaLevelReflection {

    #region Descriptor
    /// <summary>File descriptor for HakoniwaLevel.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HakoniwaLevelReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChNIYWtvbml3YUxldmVsLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEaHlBo",
            "b2VuaXhIYWtvbml3YUxldmVsVHlwZS5wcm90byKkAQoXSGFrb25pd2FMZXZl",
            "bENvbmZpZ0RhdGESCgoCSWQYASABKAUSOgoEVHlwZRgCIAEoDjIsLlBob2Vu",
            "aXguQ29uZmlnRGF0YS5QaG9lbml4SGFrb25pd2FMZXZlbFR5cGUSEAoIQmF0",
            "dGxlSWQYAyABKAUSGQoRSXNIYWtvU2NlbmVCYXR0bGUYBCABKAgSFAoMUmV3",
            "YXJkRHJvcElkGAUgASgFYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.PhoenixHakoniwaLevelTypeReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.HakoniwaLevelConfigData), global::Phoenix.ConfigData.HakoniwaLevelConfigData.Parser, new[]{ "Id", "Type", "BattleId", "IsHakoSceneBattle", "RewardDropId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class HakoniwaLevelConfigData : pb::IMessage<HakoniwaLevelConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HakoniwaLevelConfigData> _parser = new pb::MessageParser<HakoniwaLevelConfigData>(() => new HakoniwaLevelConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<HakoniwaLevelConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.HakoniwaLevelReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaLevelConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaLevelConfigData(HakoniwaLevelConfigData other) : this() {
      id_ = other.id_;
      type_ = other.type_;
      battleId_ = other.battleId_;
      isHakoSceneBattle_ = other.isHakoSceneBattle_;
      rewardDropId_ = other.rewardDropId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaLevelConfigData Clone() {
      return new HakoniwaLevelConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Type" field.</summary>
    public const int TypeFieldNumber = 2;
    private global::Phoenix.ConfigData.PhoenixHakoniwaLevelType type_ = global::Phoenix.ConfigData.PhoenixHakoniwaLevelType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.PhoenixHakoniwaLevelType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "BattleId" field.</summary>
    public const int BattleIdFieldNumber = 3;
    private int battleId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattleId {
      get { return battleId_; }
      set {
        battleId_ = value;
      }
    }

    /// <summary>Field number for the "IsHakoSceneBattle" field.</summary>
    public const int IsHakoSceneBattleFieldNumber = 4;
    private bool isHakoSceneBattle_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool IsHakoSceneBattle {
      get { return isHakoSceneBattle_; }
      set {
        isHakoSceneBattle_ = value;
      }
    }

    /// <summary>Field number for the "RewardDropId" field.</summary>
    public const int RewardDropIdFieldNumber = 5;
    private int rewardDropId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RewardDropId {
      get { return rewardDropId_; }
      set {
        rewardDropId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as HakoniwaLevelConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(HakoniwaLevelConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Type != other.Type) return false;
      if (BattleId != other.BattleId) return false;
      if (IsHakoSceneBattle != other.IsHakoSceneBattle) return false;
      if (RewardDropId != other.RewardDropId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaLevelType.None) hash ^= Type.GetHashCode();
      if (BattleId != 0) hash ^= BattleId.GetHashCode();
      if (IsHakoSceneBattle != false) hash ^= IsHakoSceneBattle.GetHashCode();
      if (RewardDropId != 0) hash ^= RewardDropId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaLevelType.None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Type);
      }
      if (BattleId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(BattleId);
      }
      if (IsHakoSceneBattle != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsHakoSceneBattle);
      }
      if (RewardDropId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(RewardDropId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaLevelType.None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Type);
      }
      if (BattleId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(BattleId);
      }
      if (IsHakoSceneBattle != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsHakoSceneBattle);
      }
      if (RewardDropId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(RewardDropId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaLevelType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (BattleId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattleId);
      }
      if (IsHakoSceneBattle != false) {
        size += 1 + 1;
      }
      if (RewardDropId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RewardDropId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(HakoniwaLevelConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Type != global::Phoenix.ConfigData.PhoenixHakoniwaLevelType.None) {
        Type = other.Type;
      }
      if (other.BattleId != 0) {
        BattleId = other.BattleId;
      }
      if (other.IsHakoSceneBattle != false) {
        IsHakoSceneBattle = other.IsHakoSceneBattle;
      }
      if (other.RewardDropId != 0) {
        RewardDropId = other.RewardDropId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Type = (global::Phoenix.ConfigData.PhoenixHakoniwaLevelType) input.ReadEnum();
            break;
          }
          case 24: {
            BattleId = input.ReadInt32();
            break;
          }
          case 32: {
            IsHakoSceneBattle = input.ReadBool();
            break;
          }
          case 40: {
            RewardDropId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Type = (global::Phoenix.ConfigData.PhoenixHakoniwaLevelType) input.ReadEnum();
            break;
          }
          case 24: {
            BattleId = input.ReadInt32();
            break;
          }
          case 32: {
            IsHakoSceneBattle = input.ReadBool();
            break;
          }
          case 40: {
            RewardDropId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
