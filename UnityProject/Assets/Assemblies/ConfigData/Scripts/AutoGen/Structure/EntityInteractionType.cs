// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: EntityInteractionType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from EntityInteractionType.proto</summary>
  public static partial class EntityInteractionTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for EntityInteractionType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EntityInteractionTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChtFbnRpdHlJbnRlcmFjdGlvblR5cGUucHJvdG8SElBob2VuaXguQ29uZmln",
            "RGF0YSqcAQoVRW50aXR5SW50ZXJhY3Rpb25UeXBlEh4KGkVudGl0eUludGVy",
            "YWN0aW9uVHlwZV9Ob25lEAASHgoaRW50aXR5SW50ZXJhY3Rpb25UeXBlX1Rh",
            "bGsQARIfChtFbnRpdHlJbnRlcmFjdGlvblR5cGVfU3RvcnkQAhIiCh5FbnRp",
            "dHlJbnRlcmFjdGlvblR5cGVfVGVsZXBvcnQQA2IGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.EntityInteractionType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum EntityInteractionType {
    [pbr::OriginalName("EntityInteractionType_None")] None = 0,
    [pbr::OriginalName("EntityInteractionType_Talk")] Talk = 1,
    [pbr::OriginalName("EntityInteractionType_Story")] Story = 2,
    [pbr::OriginalName("EntityInteractionType_Teleport")] Teleport = 3,
  }

  #endregion

}

#endregion Designer generated code
