// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: SelectionFuncType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from SelectionFuncType.proto</summary>
  public static partial class SelectionFuncTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for SelectionFuncType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static SelectionFuncTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChdTZWxlY3Rpb25GdW5jVHlwZS5wcm90bxISUGhvZW5peC5Db25maWdEYXRh",
            "KpUBChFTZWxlY3Rpb25GdW5jVHlwZRIaChZTZWxlY3Rpb25GdW5jVHlwZV9O",
            "b25lEAASIQodU2VsZWN0aW9uRnVuY1R5cGVfRGlhbG9nR3JvdXAQARIcChhT",
            "ZWxlY3Rpb25GdW5jVHlwZV9EaWFsb2cQAhIjCh9TZWxlY3Rpb25GdW5jVHlw",
            "ZV9FeHBsb3JlU3lzdGVtEANiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.SelectionFuncType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum SelectionFuncType {
    [pbr::OriginalName("SelectionFuncType_None")] None = 0,
    [pbr::OriginalName("SelectionFuncType_DialogGroup")] DialogGroup = 1,
    [pbr::OriginalName("SelectionFuncType_Dialog")] Dialog = 2,
    [pbr::OriginalName("SelectionFuncType_ExploreSystem")] ExploreSystem = 3,
  }

  #endregion

}

#endregion Designer generated code
