// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PhoenixWorldPointStoryExhibit.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from PhoenixWorldPointStoryExhibit.proto</summary>
  public static partial class PhoenixWorldPointStoryExhibitReflection {

    #region Descriptor
    /// <summary>File descriptor for PhoenixWorldPointStoryExhibit.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PhoenixWorldPointStoryExhibitReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiNQaG9lbml4V29ybGRQb2ludFN0b3J5RXhoaWJpdC5wcm90bxISUGhvZW5p",
            "eC5Db25maWdEYXRhGhJTdG9yeUV4aGliaXQucHJvdG8aFVN0b3J5RHJvcFJl",
            "d2FyZC5wcm90byLEAQonUGhvZW5peFdvcmxkUG9pbnRTdG9yeUV4aGliaXRD",
            "b25maWdEYXRhEgoKAklkGAEgASgFEkUKEVN0b3J5RXhoaWJpdFBhcmFtGAIg",
            "AygLMiouUGhvZW5peC5Db25maWdEYXRhLlN0b3J5RXhoaWJpdENvbmZpZ0Rh",
            "dGESRgoPU3RvcnlEcm9wUmV3YXJkGAMgAygLMi0uUGhvZW5peC5Db25maWdE",
            "YXRhLlN0b3J5RHJvcFJld2FyZENvbmZpZ0RhdGFiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.StoryExhibitReflection.Descriptor, global::Phoenix.ConfigData.StoryDropRewardReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.PhoenixWorldPointStoryExhibitConfigData), global::Phoenix.ConfigData.PhoenixWorldPointStoryExhibitConfigData.Parser, new[]{ "Id", "StoryExhibitParam", "StoryDropReward" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class PhoenixWorldPointStoryExhibitConfigData : pb::IMessage<PhoenixWorldPointStoryExhibitConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PhoenixWorldPointStoryExhibitConfigData> _parser = new pb::MessageParser<PhoenixWorldPointStoryExhibitConfigData>(() => new PhoenixWorldPointStoryExhibitConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PhoenixWorldPointStoryExhibitConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.PhoenixWorldPointStoryExhibitReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointStoryExhibitConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointStoryExhibitConfigData(PhoenixWorldPointStoryExhibitConfigData other) : this() {
      id_ = other.id_;
      storyExhibitParam_ = other.storyExhibitParam_.Clone();
      storyDropReward_ = other.storyDropReward_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointStoryExhibitConfigData Clone() {
      return new PhoenixWorldPointStoryExhibitConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "StoryExhibitParam" field.</summary>
    public const int StoryExhibitParamFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.StoryExhibitConfigData> _repeated_storyExhibitParam_codec
        = pb::FieldCodec.ForMessage(18, global::Phoenix.ConfigData.StoryExhibitConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.StoryExhibitConfigData> storyExhibitParam_ = new pbc::RepeatedField<global::Phoenix.ConfigData.StoryExhibitConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.StoryExhibitConfigData> StoryExhibitParam {
      get { return storyExhibitParam_; }
    }

    /// <summary>Field number for the "StoryDropReward" field.</summary>
    public const int StoryDropRewardFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.StoryDropRewardConfigData> _repeated_storyDropReward_codec
        = pb::FieldCodec.ForMessage(26, global::Phoenix.ConfigData.StoryDropRewardConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.StoryDropRewardConfigData> storyDropReward_ = new pbc::RepeatedField<global::Phoenix.ConfigData.StoryDropRewardConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.StoryDropRewardConfigData> StoryDropReward {
      get { return storyDropReward_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PhoenixWorldPointStoryExhibitConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PhoenixWorldPointStoryExhibitConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if(!storyExhibitParam_.Equals(other.storyExhibitParam_)) return false;
      if(!storyDropReward_.Equals(other.storyDropReward_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      hash ^= storyExhibitParam_.GetHashCode();
      hash ^= storyDropReward_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      storyExhibitParam_.WriteTo(output, _repeated_storyExhibitParam_codec);
      storyDropReward_.WriteTo(output, _repeated_storyDropReward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      storyExhibitParam_.WriteTo(ref output, _repeated_storyExhibitParam_codec);
      storyDropReward_.WriteTo(ref output, _repeated_storyDropReward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      size += storyExhibitParam_.CalculateSize(_repeated_storyExhibitParam_codec);
      size += storyDropReward_.CalculateSize(_repeated_storyDropReward_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PhoenixWorldPointStoryExhibitConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      storyExhibitParam_.Add(other.storyExhibitParam_);
      storyDropReward_.Add(other.storyDropReward_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            storyExhibitParam_.AddEntriesFrom(input, _repeated_storyExhibitParam_codec);
            break;
          }
          case 26: {
            storyDropReward_.AddEntriesFrom(input, _repeated_storyDropReward_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            storyExhibitParam_.AddEntriesFrom(ref input, _repeated_storyExhibitParam_codec);
            break;
          }
          case 26: {
            storyDropReward_.AddEntriesFrom(ref input, _repeated_storyDropReward_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
