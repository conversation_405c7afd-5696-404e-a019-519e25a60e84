// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: EntityType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from EntityType.proto</summary>
  public static partial class EntityTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for EntityType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EntityTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBFbnRpdHlUeXBlLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEqbQoKRW50",
            "aXR5VHlwZRITCg9FbnRpdHlUeXBlX05vbmUQABIUChBFbnRpdHlUeXBlX0Fj",
            "dG9yEAESGgoWRW50aXR5VHlwZV9UZXJyYWluQnVmZhACEhgKFEVudGl0eVR5",
            "cGVfVGFjdGljaWFuEANiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.EntityType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum EntityType {
    [pbr::OriginalName("EntityType_None")] None = 0,
    [pbr::OriginalName("EntityType_Actor")] Actor = 1,
    [pbr::OriginalName("EntityType_TerrainBuff")] TerrainBuff = 2,
    [pbr::OriginalName("EntityType_Tactician")] Tactician = 3,
  }

  #endregion

}

#endregion Designer generated code
