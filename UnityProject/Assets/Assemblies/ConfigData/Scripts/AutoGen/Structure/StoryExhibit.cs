// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: StoryExhibit.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from StoryExhibit.proto</summary>
  public static partial class StoryExhibitReflection {

    #region Descriptor
    /// <summary>File descriptor for StoryExhibit.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static StoryExhibitReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChJTdG9yeUV4aGliaXQucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YRoWU3Rv",
            "cnlFeGhpYml0VHlwZS5wcm90byKIAQoWU3RvcnlFeGhpYml0Q29uZmlnRGF0",
            "YRIbChNTdG9yeUV4aGliaXRQYXJhbUlEGAEgASgFEjkKC0V4aGliaXRUeXBl",
            "GAIgASgOMiQuUGhvZW5peC5Db25maWdEYXRhLlN0b3J5RXhoaWJpdFR5cGUS",
            "CgoCUDEYAyABKAUSCgoCUDIYBCABKAViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.StoryExhibitTypeReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.StoryExhibitConfigData), global::Phoenix.ConfigData.StoryExhibitConfigData.Parser, new[]{ "StoryExhibitParamID", "ExhibitType", "P1", "P2" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class StoryExhibitConfigData : pb::IMessage<StoryExhibitConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<StoryExhibitConfigData> _parser = new pb::MessageParser<StoryExhibitConfigData>(() => new StoryExhibitConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<StoryExhibitConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.StoryExhibitReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StoryExhibitConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StoryExhibitConfigData(StoryExhibitConfigData other) : this() {
      storyExhibitParamID_ = other.storyExhibitParamID_;
      exhibitType_ = other.exhibitType_;
      p1_ = other.p1_;
      p2_ = other.p2_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StoryExhibitConfigData Clone() {
      return new StoryExhibitConfigData(this);
    }

    /// <summary>Field number for the "StoryExhibitParamID" field.</summary>
    public const int StoryExhibitParamIDFieldNumber = 1;
    private int storyExhibitParamID_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int StoryExhibitParamID {
      get { return storyExhibitParamID_; }
      set {
        storyExhibitParamID_ = value;
      }
    }

    /// <summary>Field number for the "ExhibitType" field.</summary>
    public const int ExhibitTypeFieldNumber = 2;
    private global::Phoenix.ConfigData.StoryExhibitType exhibitType_ = global::Phoenix.ConfigData.StoryExhibitType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.StoryExhibitType ExhibitType {
      get { return exhibitType_; }
      set {
        exhibitType_ = value;
      }
    }

    /// <summary>Field number for the "P1" field.</summary>
    public const int P1FieldNumber = 3;
    private int p1_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int P1 {
      get { return p1_; }
      set {
        p1_ = value;
      }
    }

    /// <summary>Field number for the "P2" field.</summary>
    public const int P2FieldNumber = 4;
    private int p2_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int P2 {
      get { return p2_; }
      set {
        p2_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as StoryExhibitConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(StoryExhibitConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (StoryExhibitParamID != other.StoryExhibitParamID) return false;
      if (ExhibitType != other.ExhibitType) return false;
      if (P1 != other.P1) return false;
      if (P2 != other.P2) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (StoryExhibitParamID != 0) hash ^= StoryExhibitParamID.GetHashCode();
      if (ExhibitType != global::Phoenix.ConfigData.StoryExhibitType.None) hash ^= ExhibitType.GetHashCode();
      if (P1 != 0) hash ^= P1.GetHashCode();
      if (P2 != 0) hash ^= P2.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (StoryExhibitParamID != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(StoryExhibitParamID);
      }
      if (ExhibitType != global::Phoenix.ConfigData.StoryExhibitType.None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) ExhibitType);
      }
      if (P1 != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(P1);
      }
      if (P2 != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(P2);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (StoryExhibitParamID != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(StoryExhibitParamID);
      }
      if (ExhibitType != global::Phoenix.ConfigData.StoryExhibitType.None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) ExhibitType);
      }
      if (P1 != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(P1);
      }
      if (P2 != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(P2);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (StoryExhibitParamID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(StoryExhibitParamID);
      }
      if (ExhibitType != global::Phoenix.ConfigData.StoryExhibitType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) ExhibitType);
      }
      if (P1 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(P1);
      }
      if (P2 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(P2);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(StoryExhibitConfigData other) {
      if (other == null) {
        return;
      }
      if (other.StoryExhibitParamID != 0) {
        StoryExhibitParamID = other.StoryExhibitParamID;
      }
      if (other.ExhibitType != global::Phoenix.ConfigData.StoryExhibitType.None) {
        ExhibitType = other.ExhibitType;
      }
      if (other.P1 != 0) {
        P1 = other.P1;
      }
      if (other.P2 != 0) {
        P2 = other.P2;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            StoryExhibitParamID = input.ReadInt32();
            break;
          }
          case 16: {
            ExhibitType = (global::Phoenix.ConfigData.StoryExhibitType) input.ReadEnum();
            break;
          }
          case 24: {
            P1 = input.ReadInt32();
            break;
          }
          case 32: {
            P2 = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            StoryExhibitParamID = input.ReadInt32();
            break;
          }
          case 16: {
            ExhibitType = (global::Phoenix.ConfigData.StoryExhibitType) input.ReadEnum();
            break;
          }
          case 24: {
            P1 = input.ReadInt32();
            break;
          }
          case 32: {
            P2 = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
