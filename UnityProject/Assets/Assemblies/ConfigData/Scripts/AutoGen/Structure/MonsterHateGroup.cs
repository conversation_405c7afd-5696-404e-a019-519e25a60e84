// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: MonsterHateGroup.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from MonsterHateGroup.proto</summary>
  public static partial class MonsterHateGroupReflection {

    #region Descriptor
    /// <summary>File descriptor for MonsterHateGroup.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MonsterHateGroupReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChZNb25zdGVySGF0ZUdyb3VwLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEa",
            "E0hhdGVDb25kaXRpb24ucHJvdG8ijAEKGk1vbnN0ZXJIYXRlR3JvdXBDb25m",
            "aWdEYXRhEgoKAklkGAEgASgFEhAKCFByaW9yaXR5GAIgASgFEj8KCkNvbmRp",
            "dGlvbnMYAyADKAsyKy5QaG9lbml4LkNvbmZpZ0RhdGEuSGF0ZUNvbmRpdGlv",
            "bkNvbmZpZ0RhdGESDwoHTGV2ZWxJZBgEIAEoBWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.HateConditionReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.MonsterHateGroupConfigData), global::Phoenix.ConfigData.MonsterHateGroupConfigData.Parser, new[]{ "Id", "Priority", "Conditions", "LevelId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class MonsterHateGroupConfigData : pb::IMessage<MonsterHateGroupConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MonsterHateGroupConfigData> _parser = new pb::MessageParser<MonsterHateGroupConfigData>(() => new MonsterHateGroupConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<MonsterHateGroupConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.MonsterHateGroupReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MonsterHateGroupConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MonsterHateGroupConfigData(MonsterHateGroupConfigData other) : this() {
      id_ = other.id_;
      priority_ = other.priority_;
      conditions_ = other.conditions_.Clone();
      levelId_ = other.levelId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public MonsterHateGroupConfigData Clone() {
      return new MonsterHateGroupConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Priority" field.</summary>
    public const int PriorityFieldNumber = 2;
    private int priority_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Priority {
      get { return priority_; }
      set {
        priority_ = value;
      }
    }

    /// <summary>Field number for the "Conditions" field.</summary>
    public const int ConditionsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.HateConditionConfigData> _repeated_conditions_codec
        = pb::FieldCodec.ForMessage(26, global::Phoenix.ConfigData.HateConditionConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.HateConditionConfigData> conditions_ = new pbc::RepeatedField<global::Phoenix.ConfigData.HateConditionConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.HateConditionConfigData> Conditions {
      get { return conditions_; }
    }

    /// <summary>Field number for the "LevelId" field.</summary>
    public const int LevelIdFieldNumber = 4;
    private int levelId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int LevelId {
      get { return levelId_; }
      set {
        levelId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as MonsterHateGroupConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(MonsterHateGroupConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Priority != other.Priority) return false;
      if(!conditions_.Equals(other.conditions_)) return false;
      if (LevelId != other.LevelId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Priority != 0) hash ^= Priority.GetHashCode();
      hash ^= conditions_.GetHashCode();
      if (LevelId != 0) hash ^= LevelId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Priority != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Priority);
      }
      conditions_.WriteTo(output, _repeated_conditions_codec);
      if (LevelId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(LevelId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Priority != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Priority);
      }
      conditions_.WriteTo(ref output, _repeated_conditions_codec);
      if (LevelId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(LevelId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Priority != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Priority);
      }
      size += conditions_.CalculateSize(_repeated_conditions_codec);
      if (LevelId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LevelId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(MonsterHateGroupConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Priority != 0) {
        Priority = other.Priority;
      }
      conditions_.Add(other.conditions_);
      if (other.LevelId != 0) {
        LevelId = other.LevelId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Priority = input.ReadInt32();
            break;
          }
          case 26: {
            conditions_.AddEntriesFrom(input, _repeated_conditions_codec);
            break;
          }
          case 32: {
            LevelId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Priority = input.ReadInt32();
            break;
          }
          case 26: {
            conditions_.AddEntriesFrom(ref input, _repeated_conditions_codec);
            break;
          }
          case 32: {
            LevelId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
