// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: StoryExhibitType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from StoryExhibitType.proto</summary>
  public static partial class StoryExhibitTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for StoryExhibitType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static StoryExhibitTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChZTdG9yeUV4aGliaXRUeXBlLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEq",
            "nAEKEFN0b3J5RXhoaWJpdFR5cGUSGQoVU3RvcnlFeGhpYml0VHlwZV9Ob25l",
            "EAASGwoXU3RvcnlFeGhpYml0VHlwZV9CYXR0bGUQARImCiJTdG9yeUV4aGli",
            "aXRUeXBlX0hha29uaXdhS2V5QmF0dGxlEAISKAokU3RvcnlFeGhpYml0VHlw",
            "ZV9IYWtvbml3YUtleVRyZWFzdXJlEANiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.StoryExhibitType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum StoryExhibitType {
    [pbr::OriginalName("StoryExhibitType_None")] None = 0,
    [pbr::OriginalName("StoryExhibitType_Battle")] Battle = 1,
    [pbr::OriginalName("StoryExhibitType_HakoniwaKeyBattle")] HakoniwaKeyBattle = 2,
    [pbr::OriginalName("StoryExhibitType_HakoniwaKeyTreasure")] HakoniwaKeyTreasure = 3,
  }

  #endregion

}

#endregion Designer generated code
