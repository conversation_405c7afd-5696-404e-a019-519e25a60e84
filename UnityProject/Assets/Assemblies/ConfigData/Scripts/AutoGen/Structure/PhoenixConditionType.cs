// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PhoenixConditionType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from PhoenixConditionType.proto</summary>
  public static partial class PhoenixConditionTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for PhoenixConditionType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PhoenixConditionTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChpQaG9lbml4Q29uZGl0aW9uVHlwZS5wcm90bxISUGhvZW5peC5Db25maWdE",
            "YXRhKoYDChRQaG9lbml4Q29uZGl0aW9uVHlwZRIdChlQaG9lbml4Q29uZGl0",
            "aW9uVHlwZV9Ob25lEAASJAogUGhvZW5peENvbmRpdGlvblR5cGVfUGxheWVy",
            "TGV2ZWwQARIkCiBQaG9lbml4Q29uZGl0aW9uVHlwZV9RdWVzdFN0YXR1cxAC",
            "EiEKHVBob2VuaXhDb25kaXRpb25UeXBlX0hvbGRJdGVtEAMSKgomUGhvZW5p",
            "eENvbmRpdGlvblR5cGVfRXhwbG9yZUNsdWVTdGF0dXMQBBItCilQaG9lbml4",
            "Q29uZGl0aW9uVHlwZV9UcmFuc2Zvcm1hdGlvblN0YXR1cxAFEiwKKFBob2Vu",
            "aXhDb25kaXRpb25UeXBlX0V4cGxvcmVTeXN0ZW1TdGF0dXMQBhInCiNQaG9l",
            "bml4Q29uZGl0aW9uVHlwZV9FeGNoYW5nZVN0YXR1cxAHEi4KKlBob2VuaXhD",
            "b25kaXRpb25UeXBlX1dvcmxkUG9pbnRTdG9yeVN0YXR1cxAIYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.PhoenixConditionType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum PhoenixConditionType {
    [pbr::OriginalName("PhoenixConditionType_None")] None = 0,
    [pbr::OriginalName("PhoenixConditionType_PlayerLevel")] PlayerLevel = 1,
    [pbr::OriginalName("PhoenixConditionType_QuestStatus")] QuestStatus = 2,
    [pbr::OriginalName("PhoenixConditionType_HoldItem")] HoldItem = 3,
    [pbr::OriginalName("PhoenixConditionType_ExploreClueStatus")] ExploreClueStatus = 4,
    [pbr::OriginalName("PhoenixConditionType_TransformationStatus")] TransformationStatus = 5,
    [pbr::OriginalName("PhoenixConditionType_ExploreSystemStatus")] ExploreSystemStatus = 6,
    [pbr::OriginalName("PhoenixConditionType_ExchangeStatus")] ExchangeStatus = 7,
    [pbr::OriginalName("PhoenixConditionType_WorldPointStoryStatus")] WorldPointStoryStatus = 8,
  }

  #endregion

}

#endregion Designer generated code
