// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: QuestProvide.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from QuestProvide.proto</summary>
  public static partial class QuestProvideReflection {

    #region Descriptor
    /// <summary>File descriptor for QuestProvide.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static QuestProvideReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChJRdWVzdFByb3ZpZGUucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YSJRChZR",
            "dWVzdFByb3ZpZGVDb25maWdEYXRhEg8KB1F1ZXN0SWQYASABKAUSEgoKRmF2",
            "b3JDb3VudBgCIAEoBRISCgpEaWFsb2d1ZUlkGAMgASgFYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.QuestProvideConfigData), global::Phoenix.ConfigData.QuestProvideConfigData.Parser, new[]{ "QuestId", "FavorCount", "DialogueId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class QuestProvideConfigData : pb::IMessage<QuestProvideConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QuestProvideConfigData> _parser = new pb::MessageParser<QuestProvideConfigData>(() => new QuestProvideConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<QuestProvideConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.QuestProvideReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public QuestProvideConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public QuestProvideConfigData(QuestProvideConfigData other) : this() {
      questId_ = other.questId_;
      favorCount_ = other.favorCount_;
      dialogueId_ = other.dialogueId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public QuestProvideConfigData Clone() {
      return new QuestProvideConfigData(this);
    }

    /// <summary>Field number for the "QuestId" field.</summary>
    public const int QuestIdFieldNumber = 1;
    private int questId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int QuestId {
      get { return questId_; }
      set {
        questId_ = value;
      }
    }

    /// <summary>Field number for the "FavorCount" field.</summary>
    public const int FavorCountFieldNumber = 2;
    private int favorCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int FavorCount {
      get { return favorCount_; }
      set {
        favorCount_ = value;
      }
    }

    /// <summary>Field number for the "DialogueId" field.</summary>
    public const int DialogueIdFieldNumber = 3;
    private int dialogueId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int DialogueId {
      get { return dialogueId_; }
      set {
        dialogueId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as QuestProvideConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(QuestProvideConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QuestId != other.QuestId) return false;
      if (FavorCount != other.FavorCount) return false;
      if (DialogueId != other.DialogueId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (QuestId != 0) hash ^= QuestId.GetHashCode();
      if (FavorCount != 0) hash ^= FavorCount.GetHashCode();
      if (DialogueId != 0) hash ^= DialogueId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QuestId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestId);
      }
      if (FavorCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(FavorCount);
      }
      if (DialogueId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(DialogueId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QuestId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(QuestId);
      }
      if (FavorCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(FavorCount);
      }
      if (DialogueId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(DialogueId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (QuestId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestId);
      }
      if (FavorCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(FavorCount);
      }
      if (DialogueId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DialogueId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(QuestProvideConfigData other) {
      if (other == null) {
        return;
      }
      if (other.QuestId != 0) {
        QuestId = other.QuestId;
      }
      if (other.FavorCount != 0) {
        FavorCount = other.FavorCount;
      }
      if (other.DialogueId != 0) {
        DialogueId = other.DialogueId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QuestId = input.ReadInt32();
            break;
          }
          case 16: {
            FavorCount = input.ReadInt32();
            break;
          }
          case 24: {
            DialogueId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QuestId = input.ReadInt32();
            break;
          }
          case 16: {
            FavorCount = input.ReadInt32();
            break;
          }
          case 24: {
            DialogueId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
