// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: BuffAnnounceTag.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from BuffAnnounceTag.proto</summary>
  public static partial class BuffAnnounceTagReflection {

    #region Descriptor
    /// <summary>File descriptor for BuffAnnounceTag.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BuffAnnounceTagReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVCdWZmQW5ub3VuY2VUYWcucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YSqo",
            "AgoPQnVmZkFubm91bmNlVGFnEhgKFEJ1ZmZBbm5vdW5jZVRhZ19Ob25lEAAS",
            "HAoYQnVmZkFubm91bmNlVGFnX0V4Y2l0aW5nEAESGwoXQnVmZkFubm91bmNl",
            "VGFnX0Jlc2llZ2UQAhIZChVCdWZmQW5ub3VuY2VUYWdfRGVhdGgQAxIfChtC",
            "dWZmQW5ub3VuY2VUYWdfRXh0cmFBY3Rpb24QBBIZChVCdWZmQW5ub3VuY2VU",
            "YWdfR3VhcmQQBRIdChlCdWZmQW5ub3VuY2VUYWdfUmVzb25hbmNlEAYSJAog",
            "QnVmZkFubm91bmNlVGFnX0NvbnRpbnVvdXNBdHRhY2sQBxIkCiBCdWZmQW5u",
            "b3VuY2VUYWdfQWRkaXRpb25hbEF0dGFjaxAIYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.BuffAnnounceTag), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum BuffAnnounceTag {
    [pbr::OriginalName("BuffAnnounceTag_None")] None = 0,
    [pbr::OriginalName("BuffAnnounceTag_Exciting")] Exciting = 1,
    [pbr::OriginalName("BuffAnnounceTag_Besiege")] Besiege = 2,
    [pbr::OriginalName("BuffAnnounceTag_Death")] Death = 3,
    [pbr::OriginalName("BuffAnnounceTag_ExtraAction")] ExtraAction = 4,
    [pbr::OriginalName("BuffAnnounceTag_Guard")] Guard = 5,
    [pbr::OriginalName("BuffAnnounceTag_Resonance")] Resonance = 6,
    [pbr::OriginalName("BuffAnnounceTag_ContinuousAttack")] ContinuousAttack = 7,
    [pbr::OriginalName("BuffAnnounceTag_AdditionalAttack")] AdditionalAttack = 8,
  }

  #endregion

}

#endregion Designer generated code
