// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: ArmourType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from ArmourType.proto</summary>
  public static partial class ArmourTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for ArmourType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ArmourTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBBcm1vdXJUeXBlLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEqfQoKQXJt",
            "b3VyVHlwZRITCg9Bcm1vdXJUeXBlX05vbmUQABIUChBBcm1vdXJUeXBlX01l",
            "dGFsEAESFQoRQXJtb3VyVHlwZV9Xb29kZW4QAhIWChJBcm1vdXJUeXBlX0xl",
            "YXRoZXIQAxIVChFBcm1vdXJUeXBlX0VuZXJneRAEYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.ArmourType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum ArmourType {
    [pbr::OriginalName("ArmourType_None")] None = 0,
    [pbr::OriginalName("ArmourType_Metal")] Metal = 1,
    [pbr::OriginalName("ArmourType_Wooden")] Wooden = 2,
    [pbr::OriginalName("ArmourType_Leather")] Leather = 3,
    [pbr::OriginalName("ArmourType_Energy")] Energy = 4,
  }

  #endregion

}

#endregion Designer generated code
