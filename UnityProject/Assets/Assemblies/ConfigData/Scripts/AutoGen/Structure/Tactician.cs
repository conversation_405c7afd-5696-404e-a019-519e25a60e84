// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Tactician.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from Tactician.proto</summary>
  public static partial class TacticianReflection {

    #region Descriptor
    /// <summary>File descriptor for Tactician.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static TacticianReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg9UYWN0aWNpYW4ucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YSKCAQoTVGFj",
            "dGljaWFuQ29uZmlnRGF0YRIKCgJJZBgBIAEoBRIMCgROYW1lGAIgASgJEgoK",
            "AlppGAMgASgJEhEKCVN0cmF0ZWd5cxgEIAMoBRIOCgZTa2luSWQYBSABKAUS",
            "EAoIRmFjZUljb24YBiABKAkSEAoISGVhZEljb24YByABKAliBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.TacticianConfigData), global::Phoenix.ConfigData.TacticianConfigData.Parser, new[]{ "Id", "Name", "Zi", "Strategys", "SkinId", "FaceIcon", "HeadIcon" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class TacticianConfigData : pb::IMessage<TacticianConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TacticianConfigData> _parser = new pb::MessageParser<TacticianConfigData>(() => new TacticianConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<TacticianConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.TacticianReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TacticianConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TacticianConfigData(TacticianConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      zi_ = other.zi_;
      strategys_ = other.strategys_.Clone();
      skinId_ = other.skinId_;
      faceIcon_ = other.faceIcon_;
      headIcon_ = other.headIcon_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public TacticianConfigData Clone() {
      return new TacticianConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Zi" field.</summary>
    public const int ZiFieldNumber = 3;
    private string zi_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Zi {
      get { return zi_; }
      set {
        zi_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Strategys" field.</summary>
    public const int StrategysFieldNumber = 4;
    private static readonly pb::FieldCodec<int> _repeated_strategys_codec
        = pb::FieldCodec.ForInt32(34);
    private readonly pbc::RepeatedField<int> strategys_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> Strategys {
      get { return strategys_; }
    }

    /// <summary>Field number for the "SkinId" field.</summary>
    public const int SkinIdFieldNumber = 5;
    private int skinId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SkinId {
      get { return skinId_; }
      set {
        skinId_ = value;
      }
    }

    /// <summary>Field number for the "FaceIcon" field.</summary>
    public const int FaceIconFieldNumber = 6;
    private string faceIcon_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string FaceIcon {
      get { return faceIcon_; }
      set {
        faceIcon_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "HeadIcon" field.</summary>
    public const int HeadIconFieldNumber = 7;
    private string headIcon_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string HeadIcon {
      get { return headIcon_; }
      set {
        headIcon_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as TacticianConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(TacticianConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (Zi != other.Zi) return false;
      if(!strategys_.Equals(other.strategys_)) return false;
      if (SkinId != other.SkinId) return false;
      if (FaceIcon != other.FaceIcon) return false;
      if (HeadIcon != other.HeadIcon) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Zi.Length != 0) hash ^= Zi.GetHashCode();
      hash ^= strategys_.GetHashCode();
      if (SkinId != 0) hash ^= SkinId.GetHashCode();
      if (FaceIcon.Length != 0) hash ^= FaceIcon.GetHashCode();
      if (HeadIcon.Length != 0) hash ^= HeadIcon.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (Zi.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Zi);
      }
      strategys_.WriteTo(output, _repeated_strategys_codec);
      if (SkinId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(SkinId);
      }
      if (FaceIcon.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(FaceIcon);
      }
      if (HeadIcon.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(HeadIcon);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (Zi.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Zi);
      }
      strategys_.WriteTo(ref output, _repeated_strategys_codec);
      if (SkinId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(SkinId);
      }
      if (FaceIcon.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(FaceIcon);
      }
      if (HeadIcon.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(HeadIcon);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Zi.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Zi);
      }
      size += strategys_.CalculateSize(_repeated_strategys_codec);
      if (SkinId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SkinId);
      }
      if (FaceIcon.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FaceIcon);
      }
      if (HeadIcon.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(HeadIcon);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(TacticianConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Zi.Length != 0) {
        Zi = other.Zi;
      }
      strategys_.Add(other.strategys_);
      if (other.SkinId != 0) {
        SkinId = other.SkinId;
      }
      if (other.FaceIcon.Length != 0) {
        FaceIcon = other.FaceIcon;
      }
      if (other.HeadIcon.Length != 0) {
        HeadIcon = other.HeadIcon;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            Zi = input.ReadString();
            break;
          }
          case 34:
          case 32: {
            strategys_.AddEntriesFrom(input, _repeated_strategys_codec);
            break;
          }
          case 40: {
            SkinId = input.ReadInt32();
            break;
          }
          case 50: {
            FaceIcon = input.ReadString();
            break;
          }
          case 58: {
            HeadIcon = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            Zi = input.ReadString();
            break;
          }
          case 34:
          case 32: {
            strategys_.AddEntriesFrom(ref input, _repeated_strategys_codec);
            break;
          }
          case 40: {
            SkinId = input.ReadInt32();
            break;
          }
          case 50: {
            FaceIcon = input.ReadString();
            break;
          }
          case 58: {
            HeadIcon = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
