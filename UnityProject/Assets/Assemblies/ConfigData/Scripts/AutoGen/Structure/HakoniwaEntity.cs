// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: HakoniwaEntity.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from HakoniwaEntity.proto</summary>
  public static partial class HakoniwaEntityReflection {

    #region Descriptor
    /// <summary>File descriptor for HakoniwaEntity.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HakoniwaEntityReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChRIYWtvbml3YUVudGl0eS5wcm90bxISUGhvZW5peC5Db25maWdEYXRhGhhI",
            "YWtvbml3YUVudGl0eVR5cGUucHJvdG8aE1NjZW5lTG9jYXRpb24ucHJvdG8i",
            "/AEKGEhha29uaXdhRW50aXR5Q29uZmlnRGF0YRIKCgJJZBgBIAEoBRIMCgRO",
            "YW1lGAIgASgJEgwKBERlc2MYAyABKAkSOgoKRW50aXR5VHlwZRgEIAEoDjIm",
            "LlBob2VuaXguQ29uZmlnRGF0YS5IYWtvbml3YUVudGl0eVR5cGUSFAoMRW50",
            "aXR5U2tpbklkGAUgASgFEj0KCExvY2F0aW9uGAYgASgLMisuUGhvZW5peC5D",
            "b25maWdEYXRhLlNjZW5lTG9jYXRpb25Db25maWdEYXRhEhEKCURpcmVjdGlv",
            "bhgHIAEoBRIUCgxJbnRlcmFjdGlvbnMYCCADKAViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.HakoniwaEntityTypeReflection.Descriptor, global::Phoenix.ConfigData.SceneLocationReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.HakoniwaEntityConfigData), global::Phoenix.ConfigData.HakoniwaEntityConfigData.Parser, new[]{ "Id", "Name", "Desc", "EntityType", "EntitySkinId", "Location", "Direction", "Interactions" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class HakoniwaEntityConfigData : pb::IMessage<HakoniwaEntityConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HakoniwaEntityConfigData> _parser = new pb::MessageParser<HakoniwaEntityConfigData>(() => new HakoniwaEntityConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<HakoniwaEntityConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.HakoniwaEntityReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaEntityConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaEntityConfigData(HakoniwaEntityConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      desc_ = other.desc_;
      entityType_ = other.entityType_;
      entitySkinId_ = other.entitySkinId_;
      location_ = other.location_ != null ? other.location_.Clone() : null;
      direction_ = other.direction_;
      interactions_ = other.interactions_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaEntityConfigData Clone() {
      return new HakoniwaEntityConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Desc" field.</summary>
    public const int DescFieldNumber = 3;
    private string desc_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Desc {
      get { return desc_; }
      set {
        desc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "EntityType" field.</summary>
    public const int EntityTypeFieldNumber = 4;
    private global::Phoenix.ConfigData.HakoniwaEntityType entityType_ = global::Phoenix.ConfigData.HakoniwaEntityType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.HakoniwaEntityType EntityType {
      get { return entityType_; }
      set {
        entityType_ = value;
      }
    }

    /// <summary>Field number for the "EntitySkinId" field.</summary>
    public const int EntitySkinIdFieldNumber = 5;
    private int entitySkinId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int EntitySkinId {
      get { return entitySkinId_; }
      set {
        entitySkinId_ = value;
      }
    }

    /// <summary>Field number for the "Location" field.</summary>
    public const int LocationFieldNumber = 6;
    private global::Phoenix.ConfigData.SceneLocationConfigData location_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.SceneLocationConfigData Location {
      get { return location_; }
      set {
        location_ = value;
      }
    }

    /// <summary>Field number for the "Direction" field.</summary>
    public const int DirectionFieldNumber = 7;
    private int direction_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Direction {
      get { return direction_; }
      set {
        direction_ = value;
      }
    }

    /// <summary>Field number for the "Interactions" field.</summary>
    public const int InteractionsFieldNumber = 8;
    private static readonly pb::FieldCodec<int> _repeated_interactions_codec
        = pb::FieldCodec.ForInt32(66);
    private readonly pbc::RepeatedField<int> interactions_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> Interactions {
      get { return interactions_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as HakoniwaEntityConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(HakoniwaEntityConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (Desc != other.Desc) return false;
      if (EntityType != other.EntityType) return false;
      if (EntitySkinId != other.EntitySkinId) return false;
      if (!object.Equals(Location, other.Location)) return false;
      if (Direction != other.Direction) return false;
      if(!interactions_.Equals(other.interactions_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Desc.Length != 0) hash ^= Desc.GetHashCode();
      if (EntityType != global::Phoenix.ConfigData.HakoniwaEntityType.None) hash ^= EntityType.GetHashCode();
      if (EntitySkinId != 0) hash ^= EntitySkinId.GetHashCode();
      if (location_ != null) hash ^= Location.GetHashCode();
      if (Direction != 0) hash ^= Direction.GetHashCode();
      hash ^= interactions_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (Desc.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Desc);
      }
      if (EntityType != global::Phoenix.ConfigData.HakoniwaEntityType.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) EntityType);
      }
      if (EntitySkinId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(EntitySkinId);
      }
      if (location_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(Location);
      }
      if (Direction != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(Direction);
      }
      interactions_.WriteTo(output, _repeated_interactions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (Desc.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Desc);
      }
      if (EntityType != global::Phoenix.ConfigData.HakoniwaEntityType.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) EntityType);
      }
      if (EntitySkinId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(EntitySkinId);
      }
      if (location_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(Location);
      }
      if (Direction != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(Direction);
      }
      interactions_.WriteTo(ref output, _repeated_interactions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Desc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Desc);
      }
      if (EntityType != global::Phoenix.ConfigData.HakoniwaEntityType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) EntityType);
      }
      if (EntitySkinId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(EntitySkinId);
      }
      if (location_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Location);
      }
      if (Direction != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Direction);
      }
      size += interactions_.CalculateSize(_repeated_interactions_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(HakoniwaEntityConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Desc.Length != 0) {
        Desc = other.Desc;
      }
      if (other.EntityType != global::Phoenix.ConfigData.HakoniwaEntityType.None) {
        EntityType = other.EntityType;
      }
      if (other.EntitySkinId != 0) {
        EntitySkinId = other.EntitySkinId;
      }
      if (other.location_ != null) {
        if (location_ == null) {
          Location = new global::Phoenix.ConfigData.SceneLocationConfigData();
        }
        Location.MergeFrom(other.Location);
      }
      if (other.Direction != 0) {
        Direction = other.Direction;
      }
      interactions_.Add(other.interactions_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            Desc = input.ReadString();
            break;
          }
          case 32: {
            EntityType = (global::Phoenix.ConfigData.HakoniwaEntityType) input.ReadEnum();
            break;
          }
          case 40: {
            EntitySkinId = input.ReadInt32();
            break;
          }
          case 50: {
            if (location_ == null) {
              Location = new global::Phoenix.ConfigData.SceneLocationConfigData();
            }
            input.ReadMessage(Location);
            break;
          }
          case 56: {
            Direction = input.ReadInt32();
            break;
          }
          case 66:
          case 64: {
            interactions_.AddEntriesFrom(input, _repeated_interactions_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            Desc = input.ReadString();
            break;
          }
          case 32: {
            EntityType = (global::Phoenix.ConfigData.HakoniwaEntityType) input.ReadEnum();
            break;
          }
          case 40: {
            EntitySkinId = input.ReadInt32();
            break;
          }
          case 50: {
            if (location_ == null) {
              Location = new global::Phoenix.ConfigData.SceneLocationConfigData();
            }
            input.ReadMessage(Location);
            break;
          }
          case 56: {
            Direction = input.ReadInt32();
            break;
          }
          case 66:
          case 64: {
            interactions_.AddEntriesFrom(ref input, _repeated_interactions_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
