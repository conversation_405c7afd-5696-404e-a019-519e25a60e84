// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PhoenixWorldPointStory.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from PhoenixWorldPointStory.proto</summary>
  public static partial class PhoenixWorldPointStoryReflection {

    #region Descriptor
    /// <summary>File descriptor for PhoenixWorldPointStory.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PhoenixWorldPointStoryReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChxQaG9lbml4V29ybGRQb2ludFN0b3J5LnByb3RvEhJQaG9lbml4LkNvbmZp",
            "Z0RhdGEaFlBob2VuaXhDb25kaXRpb24ucHJvdG8aIFBob2VuaXhXb3JsZFBv",
            "aW50U3RvcnlUeXBlLnByb3RvGiRQaG9lbml4V29ybGRQb2ludFN0b3J5RnVu",
            "Y1R5cGUucHJvdG8aEFN0b3J5UG9pbnQucHJvdG8aElN0b3J5RXhoaWJpdC5w",
            "cm90byKQBAogUGhvZW5peFdvcmxkUG9pbnRTdG9yeUNvbmZpZ0RhdGESCgoC",
            "SWQYASABKAUSDAoETmFtZRgCIAEoCRJIChBVbmxvY2tDb25kaXRpb25zGAMg",
            "AygLMi4uUGhvZW5peC5Db25maWdEYXRhLlBob2VuaXhDb25kaXRpb25Db25m",
            "aWdEYXRhEkEKCVN0b3J5VHlwZRgEIAEoDjIuLlBob2VuaXguQ29uZmlnRGF0",
            "YS5QaG9lbml4V29ybGRQb2ludFN0b3J5VHlwZRISCgpXb3JsZFBvaW50GAUg",
            "ASgFEkkKDVN0b3J5RnVuY1R5cGUYBiABKA4yMi5QaG9lbml4LkNvbmZpZ0Rh",
            "dGEuUGhvZW5peFdvcmxkUG9pbnRTdG9yeUZ1bmNUeXBlEhMKC1N0b3J5UGFy",
            "YW0xGAcgASgFEhQKDFN0b3J5dFBhcmFtMhgIIAEoBRITCgtEZXNjcmlwdGlv",
            "bhgJIAEoCRIPCgdQaWN0dXJlGAogASgJEhAKCEFyZWFOYW1lGAsgASgJEjwK",
            "ClN0b3J5UG9pbnQYDCADKAsyKC5QaG9lbml4LkNvbmZpZ0RhdGEuU3RvcnlQ",
            "b2ludENvbmZpZ0RhdGESRQoRU3RvcnlFeGhpYml0UGFyYW0YDSADKAsyKi5Q",
            "aG9lbml4LkNvbmZpZ0RhdGEuU3RvcnlFeGhpYml0Q29uZmlnRGF0YWIGcHJv",
            "dG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.PhoenixConditionReflection.Descriptor, global::Phoenix.ConfigData.PhoenixWorldPointStoryTypeReflection.Descriptor, global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncTypeReflection.Descriptor, global::Phoenix.ConfigData.StoryPointReflection.Descriptor, global::Phoenix.ConfigData.StoryExhibitReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.PhoenixWorldPointStoryConfigData), global::Phoenix.ConfigData.PhoenixWorldPointStoryConfigData.Parser, new[]{ "Id", "Name", "UnlockConditions", "StoryType", "WorldPoint", "StoryFuncType", "StoryParam1", "StorytParam2", "Description", "Picture", "AreaName", "StoryPoint", "StoryExhibitParam" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class PhoenixWorldPointStoryConfigData : pb::IMessage<PhoenixWorldPointStoryConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PhoenixWorldPointStoryConfigData> _parser = new pb::MessageParser<PhoenixWorldPointStoryConfigData>(() => new PhoenixWorldPointStoryConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PhoenixWorldPointStoryConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.PhoenixWorldPointStoryReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointStoryConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointStoryConfigData(PhoenixWorldPointStoryConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      unlockConditions_ = other.unlockConditions_.Clone();
      storyType_ = other.storyType_;
      worldPoint_ = other.worldPoint_;
      storyFuncType_ = other.storyFuncType_;
      storyParam1_ = other.storyParam1_;
      storytParam2_ = other.storytParam2_;
      description_ = other.description_;
      picture_ = other.picture_;
      areaName_ = other.areaName_;
      storyPoint_ = other.storyPoint_.Clone();
      storyExhibitParam_ = other.storyExhibitParam_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointStoryConfigData Clone() {
      return new PhoenixWorldPointStoryConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "UnlockConditions" field.</summary>
    public const int UnlockConditionsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.PhoenixConditionConfigData> _repeated_unlockConditions_codec
        = pb::FieldCodec.ForMessage(26, global::Phoenix.ConfigData.PhoenixConditionConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.PhoenixConditionConfigData> unlockConditions_ = new pbc::RepeatedField<global::Phoenix.ConfigData.PhoenixConditionConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.PhoenixConditionConfigData> UnlockConditions {
      get { return unlockConditions_; }
    }

    /// <summary>Field number for the "StoryType" field.</summary>
    public const int StoryTypeFieldNumber = 4;
    private global::Phoenix.ConfigData.PhoenixWorldPointStoryType storyType_ = global::Phoenix.ConfigData.PhoenixWorldPointStoryType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.PhoenixWorldPointStoryType StoryType {
      get { return storyType_; }
      set {
        storyType_ = value;
      }
    }

    /// <summary>Field number for the "WorldPoint" field.</summary>
    public const int WorldPointFieldNumber = 5;
    private int worldPoint_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int WorldPoint {
      get { return worldPoint_; }
      set {
        worldPoint_ = value;
      }
    }

    /// <summary>Field number for the "StoryFuncType" field.</summary>
    public const int StoryFuncTypeFieldNumber = 6;
    private global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType storyFuncType_ = global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType StoryFuncType {
      get { return storyFuncType_; }
      set {
        storyFuncType_ = value;
      }
    }

    /// <summary>Field number for the "StoryParam1" field.</summary>
    public const int StoryParam1FieldNumber = 7;
    private int storyParam1_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int StoryParam1 {
      get { return storyParam1_; }
      set {
        storyParam1_ = value;
      }
    }

    /// <summary>Field number for the "StorytParam2" field.</summary>
    public const int StorytParam2FieldNumber = 8;
    private int storytParam2_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int StorytParam2 {
      get { return storytParam2_; }
      set {
        storytParam2_ = value;
      }
    }

    /// <summary>Field number for the "Description" field.</summary>
    public const int DescriptionFieldNumber = 9;
    private string description_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Description {
      get { return description_; }
      set {
        description_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Picture" field.</summary>
    public const int PictureFieldNumber = 10;
    private string picture_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Picture {
      get { return picture_; }
      set {
        picture_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "AreaName" field.</summary>
    public const int AreaNameFieldNumber = 11;
    private string areaName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string AreaName {
      get { return areaName_; }
      set {
        areaName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "StoryPoint" field.</summary>
    public const int StoryPointFieldNumber = 12;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.StoryPointConfigData> _repeated_storyPoint_codec
        = pb::FieldCodec.ForMessage(98, global::Phoenix.ConfigData.StoryPointConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.StoryPointConfigData> storyPoint_ = new pbc::RepeatedField<global::Phoenix.ConfigData.StoryPointConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.StoryPointConfigData> StoryPoint {
      get { return storyPoint_; }
    }

    /// <summary>Field number for the "StoryExhibitParam" field.</summary>
    public const int StoryExhibitParamFieldNumber = 13;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.StoryExhibitConfigData> _repeated_storyExhibitParam_codec
        = pb::FieldCodec.ForMessage(106, global::Phoenix.ConfigData.StoryExhibitConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.StoryExhibitConfigData> storyExhibitParam_ = new pbc::RepeatedField<global::Phoenix.ConfigData.StoryExhibitConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.StoryExhibitConfigData> StoryExhibitParam {
      get { return storyExhibitParam_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PhoenixWorldPointStoryConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PhoenixWorldPointStoryConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if(!unlockConditions_.Equals(other.unlockConditions_)) return false;
      if (StoryType != other.StoryType) return false;
      if (WorldPoint != other.WorldPoint) return false;
      if (StoryFuncType != other.StoryFuncType) return false;
      if (StoryParam1 != other.StoryParam1) return false;
      if (StorytParam2 != other.StorytParam2) return false;
      if (Description != other.Description) return false;
      if (Picture != other.Picture) return false;
      if (AreaName != other.AreaName) return false;
      if(!storyPoint_.Equals(other.storyPoint_)) return false;
      if(!storyExhibitParam_.Equals(other.storyExhibitParam_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      hash ^= unlockConditions_.GetHashCode();
      if (StoryType != global::Phoenix.ConfigData.PhoenixWorldPointStoryType.None) hash ^= StoryType.GetHashCode();
      if (WorldPoint != 0) hash ^= WorldPoint.GetHashCode();
      if (StoryFuncType != global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType.None) hash ^= StoryFuncType.GetHashCode();
      if (StoryParam1 != 0) hash ^= StoryParam1.GetHashCode();
      if (StorytParam2 != 0) hash ^= StorytParam2.GetHashCode();
      if (Description.Length != 0) hash ^= Description.GetHashCode();
      if (Picture.Length != 0) hash ^= Picture.GetHashCode();
      if (AreaName.Length != 0) hash ^= AreaName.GetHashCode();
      hash ^= storyPoint_.GetHashCode();
      hash ^= storyExhibitParam_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      unlockConditions_.WriteTo(output, _repeated_unlockConditions_codec);
      if (StoryType != global::Phoenix.ConfigData.PhoenixWorldPointStoryType.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) StoryType);
      }
      if (WorldPoint != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(WorldPoint);
      }
      if (StoryFuncType != global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType.None) {
        output.WriteRawTag(48);
        output.WriteEnum((int) StoryFuncType);
      }
      if (StoryParam1 != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(StoryParam1);
      }
      if (StorytParam2 != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(StorytParam2);
      }
      if (Description.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(Description);
      }
      if (Picture.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Picture);
      }
      if (AreaName.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(AreaName);
      }
      storyPoint_.WriteTo(output, _repeated_storyPoint_codec);
      storyExhibitParam_.WriteTo(output, _repeated_storyExhibitParam_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      unlockConditions_.WriteTo(ref output, _repeated_unlockConditions_codec);
      if (StoryType != global::Phoenix.ConfigData.PhoenixWorldPointStoryType.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) StoryType);
      }
      if (WorldPoint != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(WorldPoint);
      }
      if (StoryFuncType != global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType.None) {
        output.WriteRawTag(48);
        output.WriteEnum((int) StoryFuncType);
      }
      if (StoryParam1 != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(StoryParam1);
      }
      if (StorytParam2 != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(StorytParam2);
      }
      if (Description.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(Description);
      }
      if (Picture.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Picture);
      }
      if (AreaName.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(AreaName);
      }
      storyPoint_.WriteTo(ref output, _repeated_storyPoint_codec);
      storyExhibitParam_.WriteTo(ref output, _repeated_storyExhibitParam_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      size += unlockConditions_.CalculateSize(_repeated_unlockConditions_codec);
      if (StoryType != global::Phoenix.ConfigData.PhoenixWorldPointStoryType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) StoryType);
      }
      if (WorldPoint != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(WorldPoint);
      }
      if (StoryFuncType != global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) StoryFuncType);
      }
      if (StoryParam1 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(StoryParam1);
      }
      if (StorytParam2 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(StorytParam2);
      }
      if (Description.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Description);
      }
      if (Picture.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Picture);
      }
      if (AreaName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AreaName);
      }
      size += storyPoint_.CalculateSize(_repeated_storyPoint_codec);
      size += storyExhibitParam_.CalculateSize(_repeated_storyExhibitParam_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PhoenixWorldPointStoryConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      unlockConditions_.Add(other.unlockConditions_);
      if (other.StoryType != global::Phoenix.ConfigData.PhoenixWorldPointStoryType.None) {
        StoryType = other.StoryType;
      }
      if (other.WorldPoint != 0) {
        WorldPoint = other.WorldPoint;
      }
      if (other.StoryFuncType != global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType.None) {
        StoryFuncType = other.StoryFuncType;
      }
      if (other.StoryParam1 != 0) {
        StoryParam1 = other.StoryParam1;
      }
      if (other.StorytParam2 != 0) {
        StorytParam2 = other.StorytParam2;
      }
      if (other.Description.Length != 0) {
        Description = other.Description;
      }
      if (other.Picture.Length != 0) {
        Picture = other.Picture;
      }
      if (other.AreaName.Length != 0) {
        AreaName = other.AreaName;
      }
      storyPoint_.Add(other.storyPoint_);
      storyExhibitParam_.Add(other.storyExhibitParam_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            unlockConditions_.AddEntriesFrom(input, _repeated_unlockConditions_codec);
            break;
          }
          case 32: {
            StoryType = (global::Phoenix.ConfigData.PhoenixWorldPointStoryType) input.ReadEnum();
            break;
          }
          case 40: {
            WorldPoint = input.ReadInt32();
            break;
          }
          case 48: {
            StoryFuncType = (global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType) input.ReadEnum();
            break;
          }
          case 56: {
            StoryParam1 = input.ReadInt32();
            break;
          }
          case 64: {
            StorytParam2 = input.ReadInt32();
            break;
          }
          case 74: {
            Description = input.ReadString();
            break;
          }
          case 82: {
            Picture = input.ReadString();
            break;
          }
          case 90: {
            AreaName = input.ReadString();
            break;
          }
          case 98: {
            storyPoint_.AddEntriesFrom(input, _repeated_storyPoint_codec);
            break;
          }
          case 106: {
            storyExhibitParam_.AddEntriesFrom(input, _repeated_storyExhibitParam_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            unlockConditions_.AddEntriesFrom(ref input, _repeated_unlockConditions_codec);
            break;
          }
          case 32: {
            StoryType = (global::Phoenix.ConfigData.PhoenixWorldPointStoryType) input.ReadEnum();
            break;
          }
          case 40: {
            WorldPoint = input.ReadInt32();
            break;
          }
          case 48: {
            StoryFuncType = (global::Phoenix.ConfigData.PhoenixWorldPointStoryFuncType) input.ReadEnum();
            break;
          }
          case 56: {
            StoryParam1 = input.ReadInt32();
            break;
          }
          case 64: {
            StorytParam2 = input.ReadInt32();
            break;
          }
          case 74: {
            Description = input.ReadString();
            break;
          }
          case 82: {
            Picture = input.ReadString();
            break;
          }
          case 90: {
            AreaName = input.ReadString();
            break;
          }
          case 98: {
            storyPoint_.AddEntriesFrom(ref input, _repeated_storyPoint_codec);
            break;
          }
          case 106: {
            storyExhibitParam_.AddEntriesFrom(ref input, _repeated_storyExhibitParam_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
