// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: EntityElement.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from EntityElement.proto</summary>
  public static partial class EntityElementReflection {

    #region Descriptor
    /// <summary>File descriptor for EntityElement.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EntityElementReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChNFbnRpdHlFbGVtZW50LnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEaE1Nw",
            "cml0ZUF0bGFzSWQucHJvdG8i9gEKF0VudGl0eUVsZW1lbnRDb25maWdEYXRh",
            "EgoKAklkGAEgASgFEgwKBE5hbWUYAiABKAkSEQoJTWV0YWxNdWx0GAMgASgF",
            "EhAKCFdvb2RNdWx0GAQgASgFEhEKCVdhdGVyTXVsdBgFIAEoBRIQCghGaXJl",
            "TXVsdBgGIAEoBRIRCglFYXJ0aE11bHQYByABKAUSOAoNSWNvbkF0bGFzTmFt",
            "ZRgIIAEoDjIhLlBob2VuaXguQ29uZmlnRGF0YS5TcHJpdGVBdGxhc0lkEhMK",
            "C0ljb25TdWJOYW1lGAkgASgJEhUKDUljb25CR1N1Yk5hbWUYCiABKAliBnBy",
            "b3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.SpriteAtlasIdReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.EntityElementConfigData), global::Phoenix.ConfigData.EntityElementConfigData.Parser, new[]{ "Id", "Name", "MetalMult", "WoodMult", "WaterMult", "FireMult", "EarthMult", "IconAtlasName", "IconSubName", "IconBGSubName" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class EntityElementConfigData : pb::IMessage<EntityElementConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EntityElementConfigData> _parser = new pb::MessageParser<EntityElementConfigData>(() => new EntityElementConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<EntityElementConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.EntityElementReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntityElementConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntityElementConfigData(EntityElementConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      metalMult_ = other.metalMult_;
      woodMult_ = other.woodMult_;
      waterMult_ = other.waterMult_;
      fireMult_ = other.fireMult_;
      earthMult_ = other.earthMult_;
      iconAtlasName_ = other.iconAtlasName_;
      iconSubName_ = other.iconSubName_;
      iconBGSubName_ = other.iconBGSubName_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntityElementConfigData Clone() {
      return new EntityElementConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "MetalMult" field.</summary>
    public const int MetalMultFieldNumber = 3;
    private int metalMult_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int MetalMult {
      get { return metalMult_; }
      set {
        metalMult_ = value;
      }
    }

    /// <summary>Field number for the "WoodMult" field.</summary>
    public const int WoodMultFieldNumber = 4;
    private int woodMult_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int WoodMult {
      get { return woodMult_; }
      set {
        woodMult_ = value;
      }
    }

    /// <summary>Field number for the "WaterMult" field.</summary>
    public const int WaterMultFieldNumber = 5;
    private int waterMult_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int WaterMult {
      get { return waterMult_; }
      set {
        waterMult_ = value;
      }
    }

    /// <summary>Field number for the "FireMult" field.</summary>
    public const int FireMultFieldNumber = 6;
    private int fireMult_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int FireMult {
      get { return fireMult_; }
      set {
        fireMult_ = value;
      }
    }

    /// <summary>Field number for the "EarthMult" field.</summary>
    public const int EarthMultFieldNumber = 7;
    private int earthMult_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int EarthMult {
      get { return earthMult_; }
      set {
        earthMult_ = value;
      }
    }

    /// <summary>Field number for the "IconAtlasName" field.</summary>
    public const int IconAtlasNameFieldNumber = 8;
    private global::Phoenix.ConfigData.SpriteAtlasId iconAtlasName_ = global::Phoenix.ConfigData.SpriteAtlasId.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.SpriteAtlasId IconAtlasName {
      get { return iconAtlasName_; }
      set {
        iconAtlasName_ = value;
      }
    }

    /// <summary>Field number for the "IconSubName" field.</summary>
    public const int IconSubNameFieldNumber = 9;
    private string iconSubName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string IconSubName {
      get { return iconSubName_; }
      set {
        iconSubName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "IconBGSubName" field.</summary>
    public const int IconBGSubNameFieldNumber = 10;
    private string iconBGSubName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string IconBGSubName {
      get { return iconBGSubName_; }
      set {
        iconBGSubName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as EntityElementConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(EntityElementConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (MetalMult != other.MetalMult) return false;
      if (WoodMult != other.WoodMult) return false;
      if (WaterMult != other.WaterMult) return false;
      if (FireMult != other.FireMult) return false;
      if (EarthMult != other.EarthMult) return false;
      if (IconAtlasName != other.IconAtlasName) return false;
      if (IconSubName != other.IconSubName) return false;
      if (IconBGSubName != other.IconBGSubName) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (MetalMult != 0) hash ^= MetalMult.GetHashCode();
      if (WoodMult != 0) hash ^= WoodMult.GetHashCode();
      if (WaterMult != 0) hash ^= WaterMult.GetHashCode();
      if (FireMult != 0) hash ^= FireMult.GetHashCode();
      if (EarthMult != 0) hash ^= EarthMult.GetHashCode();
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) hash ^= IconAtlasName.GetHashCode();
      if (IconSubName.Length != 0) hash ^= IconSubName.GetHashCode();
      if (IconBGSubName.Length != 0) hash ^= IconBGSubName.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (MetalMult != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MetalMult);
      }
      if (WoodMult != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(WoodMult);
      }
      if (WaterMult != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(WaterMult);
      }
      if (FireMult != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(FireMult);
      }
      if (EarthMult != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(EarthMult);
      }
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        output.WriteRawTag(64);
        output.WriteEnum((int) IconAtlasName);
      }
      if (IconSubName.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(IconSubName);
      }
      if (IconBGSubName.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(IconBGSubName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (MetalMult != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MetalMult);
      }
      if (WoodMult != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(WoodMult);
      }
      if (WaterMult != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(WaterMult);
      }
      if (FireMult != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(FireMult);
      }
      if (EarthMult != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(EarthMult);
      }
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        output.WriteRawTag(64);
        output.WriteEnum((int) IconAtlasName);
      }
      if (IconSubName.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(IconSubName);
      }
      if (IconBGSubName.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(IconBGSubName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (MetalMult != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MetalMult);
      }
      if (WoodMult != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(WoodMult);
      }
      if (WaterMult != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(WaterMult);
      }
      if (FireMult != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(FireMult);
      }
      if (EarthMult != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(EarthMult);
      }
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) IconAtlasName);
      }
      if (IconSubName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(IconSubName);
      }
      if (IconBGSubName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(IconBGSubName);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(EntityElementConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.MetalMult != 0) {
        MetalMult = other.MetalMult;
      }
      if (other.WoodMult != 0) {
        WoodMult = other.WoodMult;
      }
      if (other.WaterMult != 0) {
        WaterMult = other.WaterMult;
      }
      if (other.FireMult != 0) {
        FireMult = other.FireMult;
      }
      if (other.EarthMult != 0) {
        EarthMult = other.EarthMult;
      }
      if (other.IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        IconAtlasName = other.IconAtlasName;
      }
      if (other.IconSubName.Length != 0) {
        IconSubName = other.IconSubName;
      }
      if (other.IconBGSubName.Length != 0) {
        IconBGSubName = other.IconBGSubName;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            MetalMult = input.ReadInt32();
            break;
          }
          case 32: {
            WoodMult = input.ReadInt32();
            break;
          }
          case 40: {
            WaterMult = input.ReadInt32();
            break;
          }
          case 48: {
            FireMult = input.ReadInt32();
            break;
          }
          case 56: {
            EarthMult = input.ReadInt32();
            break;
          }
          case 64: {
            IconAtlasName = (global::Phoenix.ConfigData.SpriteAtlasId) input.ReadEnum();
            break;
          }
          case 74: {
            IconSubName = input.ReadString();
            break;
          }
          case 82: {
            IconBGSubName = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            MetalMult = input.ReadInt32();
            break;
          }
          case 32: {
            WoodMult = input.ReadInt32();
            break;
          }
          case 40: {
            WaterMult = input.ReadInt32();
            break;
          }
          case 48: {
            FireMult = input.ReadInt32();
            break;
          }
          case 56: {
            EarthMult = input.ReadInt32();
            break;
          }
          case 64: {
            IconAtlasName = (global::Phoenix.ConfigData.SpriteAtlasId) input.ReadEnum();
            break;
          }
          case 74: {
            IconSubName = input.ReadString();
            break;
          }
          case 82: {
            IconBGSubName = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
