// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PhoenixHakoniwaLevelType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from PhoenixHakoniwaLevelType.proto</summary>
  public static partial class PhoenixHakoniwaLevelTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for PhoenixHakoniwaLevelType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PhoenixHakoniwaLevelTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch5QaG9lbml4SGFrb25pd2FMZXZlbFR5cGUucHJvdG8SElBob2VuaXguQ29u",
            "ZmlnRGF0YSqHAQoYUGhvZW5peEhha29uaXdhTGV2ZWxUeXBlEiEKHVBob2Vu",
            "aXhIYWtvbml3YUxldmVsVHlwZV9Ob25lEAASIgoeUGhvZW5peEhha29uaXdh",
            "TGV2ZWxUeXBlX1N0b3J5EAESJAogUGhvZW5peEhha29uaXdhTGV2ZWxUeXBl",
            "X01vbnN0ZXIQAmIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.PhoenixHakoniwaLevelType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum PhoenixHakoniwaLevelType {
    [pbr::OriginalName("PhoenixHakoniwaLevelType_None")] None = 0,
    [pbr::OriginalName("PhoenixHakoniwaLevelType_Story")] Story = 1,
    [pbr::OriginalName("PhoenixHakoniwaLevelType_Monster")] Monster = 2,
  }

  #endregion

}

#endregion Designer generated code
