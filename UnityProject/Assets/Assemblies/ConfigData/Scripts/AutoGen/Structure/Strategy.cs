// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Strategy.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from Strategy.proto</summary>
  public static partial class StrategyReflection {

    #region Descriptor
    /// <summary>File descriptor for Strategy.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static StrategyReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg5TdHJhdGVneS5wcm90bxISUGhvZW5peC5Db25maWdEYXRhGhNTdHJhdGVn",
            "eVNwZWVkLnByb3RvIu8BChJTdHJhdGVneUNvbmZpZ0RhdGESCgoCSWQYASAB",
            "KAUSDAoETmFtZRgCIAEoCRIRCglJc015c3RlcnkYAyABKAgSMAoFU3BlZWQY",
            "BCABKA4yIS5QaG9lbml4LkNvbmZpZ0RhdGEuU3RyYXRlZ3lTcGVlZBIVCg1D",
            "YXN0Q29uZGl0aW9uGAUgASgJEhMKC1ByZXBhcmVUdXJuGAYgASgFEhgKEFRy",
            "aWdnZXJDb25kaXRpb24YByABKAkSEgoKRWZmZWN0RGVzYxgIIAEoCRISCgpF",
            "ZmZlY3RUdXJuGAkgASgFEgwKBEljb24YCiABKAliBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.StrategySpeedReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.StrategyConfigData), global::Phoenix.ConfigData.StrategyConfigData.Parser, new[]{ "Id", "Name", "IsMystery", "Speed", "CastCondition", "PrepareTurn", "TriggerCondition", "EffectDesc", "EffectTurn", "Icon" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class StrategyConfigData : pb::IMessage<StrategyConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<StrategyConfigData> _parser = new pb::MessageParser<StrategyConfigData>(() => new StrategyConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<StrategyConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.StrategyReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StrategyConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StrategyConfigData(StrategyConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      isMystery_ = other.isMystery_;
      speed_ = other.speed_;
      castCondition_ = other.castCondition_;
      prepareTurn_ = other.prepareTurn_;
      triggerCondition_ = other.triggerCondition_;
      effectDesc_ = other.effectDesc_;
      effectTurn_ = other.effectTurn_;
      icon_ = other.icon_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public StrategyConfigData Clone() {
      return new StrategyConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "IsMystery" field.</summary>
    public const int IsMysteryFieldNumber = 3;
    private bool isMystery_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool IsMystery {
      get { return isMystery_; }
      set {
        isMystery_ = value;
      }
    }

    /// <summary>Field number for the "Speed" field.</summary>
    public const int SpeedFieldNumber = 4;
    private global::Phoenix.ConfigData.StrategySpeed speed_ = global::Phoenix.ConfigData.StrategySpeed.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.StrategySpeed Speed {
      get { return speed_; }
      set {
        speed_ = value;
      }
    }

    /// <summary>Field number for the "CastCondition" field.</summary>
    public const int CastConditionFieldNumber = 5;
    private string castCondition_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string CastCondition {
      get { return castCondition_; }
      set {
        castCondition_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "PrepareTurn" field.</summary>
    public const int PrepareTurnFieldNumber = 6;
    private int prepareTurn_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int PrepareTurn {
      get { return prepareTurn_; }
      set {
        prepareTurn_ = value;
      }
    }

    /// <summary>Field number for the "TriggerCondition" field.</summary>
    public const int TriggerConditionFieldNumber = 7;
    private string triggerCondition_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string TriggerCondition {
      get { return triggerCondition_; }
      set {
        triggerCondition_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "EffectDesc" field.</summary>
    public const int EffectDescFieldNumber = 8;
    private string effectDesc_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string EffectDesc {
      get { return effectDesc_; }
      set {
        effectDesc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "EffectTurn" field.</summary>
    public const int EffectTurnFieldNumber = 9;
    private int effectTurn_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int EffectTurn {
      get { return effectTurn_; }
      set {
        effectTurn_ = value;
      }
    }

    /// <summary>Field number for the "Icon" field.</summary>
    public const int IconFieldNumber = 10;
    private string icon_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Icon {
      get { return icon_; }
      set {
        icon_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as StrategyConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(StrategyConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (IsMystery != other.IsMystery) return false;
      if (Speed != other.Speed) return false;
      if (CastCondition != other.CastCondition) return false;
      if (PrepareTurn != other.PrepareTurn) return false;
      if (TriggerCondition != other.TriggerCondition) return false;
      if (EffectDesc != other.EffectDesc) return false;
      if (EffectTurn != other.EffectTurn) return false;
      if (Icon != other.Icon) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (IsMystery != false) hash ^= IsMystery.GetHashCode();
      if (Speed != global::Phoenix.ConfigData.StrategySpeed.None) hash ^= Speed.GetHashCode();
      if (CastCondition.Length != 0) hash ^= CastCondition.GetHashCode();
      if (PrepareTurn != 0) hash ^= PrepareTurn.GetHashCode();
      if (TriggerCondition.Length != 0) hash ^= TriggerCondition.GetHashCode();
      if (EffectDesc.Length != 0) hash ^= EffectDesc.GetHashCode();
      if (EffectTurn != 0) hash ^= EffectTurn.GetHashCode();
      if (Icon.Length != 0) hash ^= Icon.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (IsMystery != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsMystery);
      }
      if (Speed != global::Phoenix.ConfigData.StrategySpeed.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Speed);
      }
      if (CastCondition.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(CastCondition);
      }
      if (PrepareTurn != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PrepareTurn);
      }
      if (TriggerCondition.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(TriggerCondition);
      }
      if (EffectDesc.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(EffectDesc);
      }
      if (EffectTurn != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(EffectTurn);
      }
      if (Icon.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Icon);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (IsMystery != false) {
        output.WriteRawTag(24);
        output.WriteBool(IsMystery);
      }
      if (Speed != global::Phoenix.ConfigData.StrategySpeed.None) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Speed);
      }
      if (CastCondition.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(CastCondition);
      }
      if (PrepareTurn != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PrepareTurn);
      }
      if (TriggerCondition.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(TriggerCondition);
      }
      if (EffectDesc.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(EffectDesc);
      }
      if (EffectTurn != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(EffectTurn);
      }
      if (Icon.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Icon);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (IsMystery != false) {
        size += 1 + 1;
      }
      if (Speed != global::Phoenix.ConfigData.StrategySpeed.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Speed);
      }
      if (CastCondition.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CastCondition);
      }
      if (PrepareTurn != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PrepareTurn);
      }
      if (TriggerCondition.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(TriggerCondition);
      }
      if (EffectDesc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(EffectDesc);
      }
      if (EffectTurn != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(EffectTurn);
      }
      if (Icon.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Icon);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(StrategyConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.IsMystery != false) {
        IsMystery = other.IsMystery;
      }
      if (other.Speed != global::Phoenix.ConfigData.StrategySpeed.None) {
        Speed = other.Speed;
      }
      if (other.CastCondition.Length != 0) {
        CastCondition = other.CastCondition;
      }
      if (other.PrepareTurn != 0) {
        PrepareTurn = other.PrepareTurn;
      }
      if (other.TriggerCondition.Length != 0) {
        TriggerCondition = other.TriggerCondition;
      }
      if (other.EffectDesc.Length != 0) {
        EffectDesc = other.EffectDesc;
      }
      if (other.EffectTurn != 0) {
        EffectTurn = other.EffectTurn;
      }
      if (other.Icon.Length != 0) {
        Icon = other.Icon;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            IsMystery = input.ReadBool();
            break;
          }
          case 32: {
            Speed = (global::Phoenix.ConfigData.StrategySpeed) input.ReadEnum();
            break;
          }
          case 42: {
            CastCondition = input.ReadString();
            break;
          }
          case 48: {
            PrepareTurn = input.ReadInt32();
            break;
          }
          case 58: {
            TriggerCondition = input.ReadString();
            break;
          }
          case 66: {
            EffectDesc = input.ReadString();
            break;
          }
          case 72: {
            EffectTurn = input.ReadInt32();
            break;
          }
          case 82: {
            Icon = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            IsMystery = input.ReadBool();
            break;
          }
          case 32: {
            Speed = (global::Phoenix.ConfigData.StrategySpeed) input.ReadEnum();
            break;
          }
          case 42: {
            CastCondition = input.ReadString();
            break;
          }
          case 48: {
            PrepareTurn = input.ReadInt32();
            break;
          }
          case 58: {
            TriggerCondition = input.ReadString();
            break;
          }
          case 66: {
            EffectDesc = input.ReadString();
            break;
          }
          case 72: {
            EffectTurn = input.ReadInt32();
            break;
          }
          case 82: {
            Icon = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
