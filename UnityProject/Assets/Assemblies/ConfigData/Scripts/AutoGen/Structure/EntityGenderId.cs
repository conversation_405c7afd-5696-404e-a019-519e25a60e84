// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: EntityGenderId.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from EntityGenderId.proto</summary>
  public static partial class EntityGenderIdReflection {

    #region Descriptor
    /// <summary>File descriptor for EntityGenderId.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EntityGenderIdReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChRFbnRpdHlHZW5kZXJJZC5wcm90bxISUGhvZW5peC5Db25maWdEYXRhKl0K",
            "DkVudGl0eUdlbmRlcklkEhcKE0VudGl0eUdlbmRlcklkX05vbmUQABIXChNF",
            "bnRpdHlHZW5kZXJJZF9NYWxlEAESGQoVRW50aXR5R2VuZGVySWRfRmVtYWxl",
            "EAJiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.EntityGenderId), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum EntityGenderId {
    [pbr::OriginalName("EntityGenderId_None")] None = 0,
    [pbr::OriginalName("EntityGenderId_Male")] Male = 1,
    [pbr::OriginalName("EntityGenderId_Female")] Female = 2,
  }

  #endregion

}

#endregion Designer generated code
