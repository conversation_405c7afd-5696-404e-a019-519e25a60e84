// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: BattleType.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from BattleType.proto</summary>
  public static partial class BattleTypeReflection {

    #region Descriptor
    /// <summary>File descriptor for BattleType.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BattleTypeReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBCYXR0bGVUeXBlLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEq4wEKCkJh",
            "dHRsZVR5cGUSEwoPQmF0dGxlVHlwZV9Ob25lEAASHgoaQmF0dGxlVHlwZV9P",
            "cGVuU2NlbmVCYXR0bGUQARIbChdCYXR0bGVUeXBlX1dvcmxkTW9uc3RlchAC",
            "EhoKFkJhdHRsZVR5cGVfUXVlc3RCYXR0bGUQAxIcChhCYXR0bGVUeXBlX0Jv",
            "c3NDaGFsbGVuZ2UQBBIaChZCYXR0bGVUeXBlX0d1aWRlQmF0dGxlEAUSFwoT",
            "QmF0dGxlVHlwZV9IYWtvbml3YRAGEhQKEEJhdHRsZVR5cGVfV29ybGQQB2IG",
            "cHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.BattleType), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum BattleType {
    [pbr::OriginalName("BattleType_None")] None = 0,
    [pbr::OriginalName("BattleType_OpenSceneBattle")] OpenSceneBattle = 1,
    [pbr::OriginalName("BattleType_WorldMonster")] WorldMonster = 2,
    [pbr::OriginalName("BattleType_QuestBattle")] QuestBattle = 3,
    [pbr::OriginalName("BattleType_BossChallenge")] BossChallenge = 4,
    [pbr::OriginalName("BattleType_GuideBattle")] GuideBattle = 5,
    [pbr::OriginalName("BattleType_Hakoniwa")] Hakoniwa = 6,
    [pbr::OriginalName("BattleType_World")] World = 7,
  }

  #endregion

}

#endregion Designer generated code
