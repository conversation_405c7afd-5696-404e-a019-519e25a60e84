// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: EntityGender.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from EntityGender.proto</summary>
  public static partial class EntityGenderReflection {

    #region Descriptor
    /// <summary>File descriptor for EntityGender.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EntityGenderReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChJFbnRpdHlHZW5kZXIucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YRoTU3By",
            "aXRlQXRsYXNJZC5wcm90byKBAQoWRW50aXR5R2VuZGVyQ29uZmlnRGF0YRIK",
            "CgJJZBgBIAEoBRIMCgROYW1lGAIgASgJEjgKDUljb25BdGxhc05hbWUYAyAB",
            "KA4yIS5QaG9lbml4LkNvbmZpZ0RhdGEuU3ByaXRlQXRsYXNJZBITCgtJY29u",
            "U3ViTmFtZRgEIAEoCWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.SpriteAtlasIdReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.EntityGenderConfigData), global::Phoenix.ConfigData.EntityGenderConfigData.Parser, new[]{ "Id", "Name", "IconAtlasName", "IconSubName" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class EntityGenderConfigData : pb::IMessage<EntityGenderConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EntityGenderConfigData> _parser = new pb::MessageParser<EntityGenderConfigData>(() => new EntityGenderConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<EntityGenderConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.EntityGenderReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntityGenderConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntityGenderConfigData(EntityGenderConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      iconAtlasName_ = other.iconAtlasName_;
      iconSubName_ = other.iconSubName_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntityGenderConfigData Clone() {
      return new EntityGenderConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "IconAtlasName" field.</summary>
    public const int IconAtlasNameFieldNumber = 3;
    private global::Phoenix.ConfigData.SpriteAtlasId iconAtlasName_ = global::Phoenix.ConfigData.SpriteAtlasId.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.SpriteAtlasId IconAtlasName {
      get { return iconAtlasName_; }
      set {
        iconAtlasName_ = value;
      }
    }

    /// <summary>Field number for the "IconSubName" field.</summary>
    public const int IconSubNameFieldNumber = 4;
    private string iconSubName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string IconSubName {
      get { return iconSubName_; }
      set {
        iconSubName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as EntityGenderConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(EntityGenderConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (IconAtlasName != other.IconAtlasName) return false;
      if (IconSubName != other.IconSubName) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) hash ^= IconAtlasName.GetHashCode();
      if (IconSubName.Length != 0) hash ^= IconSubName.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) IconAtlasName);
      }
      if (IconSubName.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(IconSubName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) IconAtlasName);
      }
      if (IconSubName.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(IconSubName);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) IconAtlasName);
      }
      if (IconSubName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(IconSubName);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(EntityGenderConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.IconAtlasName != global::Phoenix.ConfigData.SpriteAtlasId.None) {
        IconAtlasName = other.IconAtlasName;
      }
      if (other.IconSubName.Length != 0) {
        IconSubName = other.IconSubName;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            IconAtlasName = (global::Phoenix.ConfigData.SpriteAtlasId) input.ReadEnum();
            break;
          }
          case 34: {
            IconSubName = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            IconAtlasName = (global::Phoenix.ConfigData.SpriteAtlasId) input.ReadEnum();
            break;
          }
          case 34: {
            IconSubName = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
