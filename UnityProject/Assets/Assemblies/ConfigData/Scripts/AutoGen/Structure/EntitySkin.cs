// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: EntitySkin.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from EntitySkin.proto</summary>
  public static partial class EntitySkinReflection {

    #region Descriptor
    /// <summary>File descriptor for EntitySkin.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static EntitySkinReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBFbnRpdHlTa2luLnByb3RvEhJQaG9lbml4LkNvbmZpZ0RhdGEaEEFybW91",
            "clR5cGUucHJvdG8ipQIKFEVudGl0eVNraW5Db25maWdEYXRhEgoKAklkGAEg",
            "ASgFEgwKBE5hbWUYAiABKAkSEgoKSWNvblNraW5JZBgDIAEoBRISCgpNb2Rl",
            "bFNjYWxlGAQgASgCEhUKDVBhcnRpY2xlU2NhbGUYBSABKAISEgoKRm9sZGVy",
            "UGF0aBgGIAEoCRIVCg1QcmVmYWJSZWZQYXRoGAcgASgJEh4KFkFuaW1hdGlv",
            "bkNvbmZpZ1JlZlBhdGgYCCABKAkSHgoWRHJhbWFDb2xsZWN0aW9uUmVmUGF0",
            "aBgJIAEoCRIVCg1QZXJmb3JtYW5jZUlkGAogASgFEjIKCkFybW91clR5cGUY",
            "CyABKA4yHi5QaG9lbml4LkNvbmZpZ0RhdGEuQXJtb3VyVHlwZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.ArmourTypeReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.EntitySkinConfigData), global::Phoenix.ConfigData.EntitySkinConfigData.Parser, new[]{ "Id", "Name", "IconSkinId", "ModelScale", "ParticleScale", "FolderPath", "PrefabRefPath", "AnimationConfigRefPath", "DramaCollectionRefPath", "PerformanceId", "ArmourType" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class EntitySkinConfigData : pb::IMessage<EntitySkinConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EntitySkinConfigData> _parser = new pb::MessageParser<EntitySkinConfigData>(() => new EntitySkinConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<EntitySkinConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.EntitySkinReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntitySkinConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntitySkinConfigData(EntitySkinConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      iconSkinId_ = other.iconSkinId_;
      modelScale_ = other.modelScale_;
      particleScale_ = other.particleScale_;
      folderPath_ = other.folderPath_;
      prefabRefPath_ = other.prefabRefPath_;
      animationConfigRefPath_ = other.animationConfigRefPath_;
      dramaCollectionRefPath_ = other.dramaCollectionRefPath_;
      performanceId_ = other.performanceId_;
      armourType_ = other.armourType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EntitySkinConfigData Clone() {
      return new EntitySkinConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "IconSkinId" field.</summary>
    public const int IconSkinIdFieldNumber = 3;
    private int iconSkinId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int IconSkinId {
      get { return iconSkinId_; }
      set {
        iconSkinId_ = value;
      }
    }

    /// <summary>Field number for the "ModelScale" field.</summary>
    public const int ModelScaleFieldNumber = 4;
    private float modelScale_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public float ModelScale {
      get { return modelScale_; }
      set {
        modelScale_ = value;
      }
    }

    /// <summary>Field number for the "ParticleScale" field.</summary>
    public const int ParticleScaleFieldNumber = 5;
    private float particleScale_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public float ParticleScale {
      get { return particleScale_; }
      set {
        particleScale_ = value;
      }
    }

    /// <summary>Field number for the "FolderPath" field.</summary>
    public const int FolderPathFieldNumber = 6;
    private string folderPath_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string FolderPath {
      get { return folderPath_; }
      set {
        folderPath_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "PrefabRefPath" field.</summary>
    public const int PrefabRefPathFieldNumber = 7;
    private string prefabRefPath_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string PrefabRefPath {
      get { return prefabRefPath_; }
      set {
        prefabRefPath_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "AnimationConfigRefPath" field.</summary>
    public const int AnimationConfigRefPathFieldNumber = 8;
    private string animationConfigRefPath_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string AnimationConfigRefPath {
      get { return animationConfigRefPath_; }
      set {
        animationConfigRefPath_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "DramaCollectionRefPath" field.</summary>
    public const int DramaCollectionRefPathFieldNumber = 9;
    private string dramaCollectionRefPath_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string DramaCollectionRefPath {
      get { return dramaCollectionRefPath_; }
      set {
        dramaCollectionRefPath_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "PerformanceId" field.</summary>
    public const int PerformanceIdFieldNumber = 10;
    private int performanceId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int PerformanceId {
      get { return performanceId_; }
      set {
        performanceId_ = value;
      }
    }

    /// <summary>Field number for the "ArmourType" field.</summary>
    public const int ArmourTypeFieldNumber = 11;
    private global::Phoenix.ConfigData.ArmourType armourType_ = global::Phoenix.ConfigData.ArmourType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.ArmourType ArmourType {
      get { return armourType_; }
      set {
        armourType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as EntitySkinConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(EntitySkinConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (IconSkinId != other.IconSkinId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(ModelScale, other.ModelScale)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(ParticleScale, other.ParticleScale)) return false;
      if (FolderPath != other.FolderPath) return false;
      if (PrefabRefPath != other.PrefabRefPath) return false;
      if (AnimationConfigRefPath != other.AnimationConfigRefPath) return false;
      if (DramaCollectionRefPath != other.DramaCollectionRefPath) return false;
      if (PerformanceId != other.PerformanceId) return false;
      if (ArmourType != other.ArmourType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (IconSkinId != 0) hash ^= IconSkinId.GetHashCode();
      if (ModelScale != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(ModelScale);
      if (ParticleScale != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(ParticleScale);
      if (FolderPath.Length != 0) hash ^= FolderPath.GetHashCode();
      if (PrefabRefPath.Length != 0) hash ^= PrefabRefPath.GetHashCode();
      if (AnimationConfigRefPath.Length != 0) hash ^= AnimationConfigRefPath.GetHashCode();
      if (DramaCollectionRefPath.Length != 0) hash ^= DramaCollectionRefPath.GetHashCode();
      if (PerformanceId != 0) hash ^= PerformanceId.GetHashCode();
      if (ArmourType != global::Phoenix.ConfigData.ArmourType.None) hash ^= ArmourType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (IconSkinId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(IconSkinId);
      }
      if (ModelScale != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(ModelScale);
      }
      if (ParticleScale != 0F) {
        output.WriteRawTag(45);
        output.WriteFloat(ParticleScale);
      }
      if (FolderPath.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(FolderPath);
      }
      if (PrefabRefPath.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(PrefabRefPath);
      }
      if (AnimationConfigRefPath.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(AnimationConfigRefPath);
      }
      if (DramaCollectionRefPath.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(DramaCollectionRefPath);
      }
      if (PerformanceId != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(PerformanceId);
      }
      if (ArmourType != global::Phoenix.ConfigData.ArmourType.None) {
        output.WriteRawTag(88);
        output.WriteEnum((int) ArmourType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (IconSkinId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(IconSkinId);
      }
      if (ModelScale != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(ModelScale);
      }
      if (ParticleScale != 0F) {
        output.WriteRawTag(45);
        output.WriteFloat(ParticleScale);
      }
      if (FolderPath.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(FolderPath);
      }
      if (PrefabRefPath.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(PrefabRefPath);
      }
      if (AnimationConfigRefPath.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(AnimationConfigRefPath);
      }
      if (DramaCollectionRefPath.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(DramaCollectionRefPath);
      }
      if (PerformanceId != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(PerformanceId);
      }
      if (ArmourType != global::Phoenix.ConfigData.ArmourType.None) {
        output.WriteRawTag(88);
        output.WriteEnum((int) ArmourType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (IconSkinId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IconSkinId);
      }
      if (ModelScale != 0F) {
        size += 1 + 4;
      }
      if (ParticleScale != 0F) {
        size += 1 + 4;
      }
      if (FolderPath.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FolderPath);
      }
      if (PrefabRefPath.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(PrefabRefPath);
      }
      if (AnimationConfigRefPath.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AnimationConfigRefPath);
      }
      if (DramaCollectionRefPath.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DramaCollectionRefPath);
      }
      if (PerformanceId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PerformanceId);
      }
      if (ArmourType != global::Phoenix.ConfigData.ArmourType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) ArmourType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(EntitySkinConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.IconSkinId != 0) {
        IconSkinId = other.IconSkinId;
      }
      if (other.ModelScale != 0F) {
        ModelScale = other.ModelScale;
      }
      if (other.ParticleScale != 0F) {
        ParticleScale = other.ParticleScale;
      }
      if (other.FolderPath.Length != 0) {
        FolderPath = other.FolderPath;
      }
      if (other.PrefabRefPath.Length != 0) {
        PrefabRefPath = other.PrefabRefPath;
      }
      if (other.AnimationConfigRefPath.Length != 0) {
        AnimationConfigRefPath = other.AnimationConfigRefPath;
      }
      if (other.DramaCollectionRefPath.Length != 0) {
        DramaCollectionRefPath = other.DramaCollectionRefPath;
      }
      if (other.PerformanceId != 0) {
        PerformanceId = other.PerformanceId;
      }
      if (other.ArmourType != global::Phoenix.ConfigData.ArmourType.None) {
        ArmourType = other.ArmourType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            IconSkinId = input.ReadInt32();
            break;
          }
          case 37: {
            ModelScale = input.ReadFloat();
            break;
          }
          case 45: {
            ParticleScale = input.ReadFloat();
            break;
          }
          case 50: {
            FolderPath = input.ReadString();
            break;
          }
          case 58: {
            PrefabRefPath = input.ReadString();
            break;
          }
          case 66: {
            AnimationConfigRefPath = input.ReadString();
            break;
          }
          case 74: {
            DramaCollectionRefPath = input.ReadString();
            break;
          }
          case 80: {
            PerformanceId = input.ReadInt32();
            break;
          }
          case 88: {
            ArmourType = (global::Phoenix.ConfigData.ArmourType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            IconSkinId = input.ReadInt32();
            break;
          }
          case 37: {
            ModelScale = input.ReadFloat();
            break;
          }
          case 45: {
            ParticleScale = input.ReadFloat();
            break;
          }
          case 50: {
            FolderPath = input.ReadString();
            break;
          }
          case 58: {
            PrefabRefPath = input.ReadString();
            break;
          }
          case 66: {
            AnimationConfigRefPath = input.ReadString();
            break;
          }
          case 74: {
            DramaCollectionRefPath = input.ReadString();
            break;
          }
          case 80: {
            PerformanceId = input.ReadInt32();
            break;
          }
          case 88: {
            ArmourType = (global::Phoenix.ConfigData.ArmourType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
