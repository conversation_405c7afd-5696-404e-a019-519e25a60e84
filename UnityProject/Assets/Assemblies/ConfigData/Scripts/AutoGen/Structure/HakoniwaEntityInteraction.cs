// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: HakoniwaEntityInteraction.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from HakoniwaEntityInteraction.proto</summary>
  public static partial class HakoniwaEntityInteractionReflection {

    #region Descriptor
    /// <summary>File descriptor for HakoniwaEntityInteraction.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HakoniwaEntityInteractionReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch9IYWtvbml3YUVudGl0eUludGVyYWN0aW9uLnByb3RvEhJQaG9lbml4LkNv",
            "bmZpZ0RhdGEaG0VudGl0eUludGVyYWN0aW9uVHlwZS5wcm90byKbAQojSGFr",
            "b25pd2FFbnRpdHlJbnRlcmFjdGlvbkNvbmZpZ0RhdGESCgoCSWQYASABKAUS",
            "DAoETmFtZRgCIAEoCRJCCg9JbnRlcmFjdGlvblR5cGUYAyABKA4yKS5QaG9l",
            "bml4LkNvbmZpZ0RhdGEuRW50aXR5SW50ZXJhY3Rpb25UeXBlEgoKAlAxGAQg",
            "ASgFEgoKAlAyGAUgASgFYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.EntityInteractionTypeReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.HakoniwaEntityInteractionConfigData), global::Phoenix.ConfigData.HakoniwaEntityInteractionConfigData.Parser, new[]{ "Id", "Name", "InteractionType", "P1", "P2" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class HakoniwaEntityInteractionConfigData : pb::IMessage<HakoniwaEntityInteractionConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HakoniwaEntityInteractionConfigData> _parser = new pb::MessageParser<HakoniwaEntityInteractionConfigData>(() => new HakoniwaEntityInteractionConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<HakoniwaEntityInteractionConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.HakoniwaEntityInteractionReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaEntityInteractionConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaEntityInteractionConfigData(HakoniwaEntityInteractionConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      interactionType_ = other.interactionType_;
      p1_ = other.p1_;
      p2_ = other.p2_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaEntityInteractionConfigData Clone() {
      return new HakoniwaEntityInteractionConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "InteractionType" field.</summary>
    public const int InteractionTypeFieldNumber = 3;
    private global::Phoenix.ConfigData.EntityInteractionType interactionType_ = global::Phoenix.ConfigData.EntityInteractionType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.EntityInteractionType InteractionType {
      get { return interactionType_; }
      set {
        interactionType_ = value;
      }
    }

    /// <summary>Field number for the "P1" field.</summary>
    public const int P1FieldNumber = 4;
    private int p1_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int P1 {
      get { return p1_; }
      set {
        p1_ = value;
      }
    }

    /// <summary>Field number for the "P2" field.</summary>
    public const int P2FieldNumber = 5;
    private int p2_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int P2 {
      get { return p2_; }
      set {
        p2_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as HakoniwaEntityInteractionConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(HakoniwaEntityInteractionConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (InteractionType != other.InteractionType) return false;
      if (P1 != other.P1) return false;
      if (P2 != other.P2) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (InteractionType != global::Phoenix.ConfigData.EntityInteractionType.None) hash ^= InteractionType.GetHashCode();
      if (P1 != 0) hash ^= P1.GetHashCode();
      if (P2 != 0) hash ^= P2.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (InteractionType != global::Phoenix.ConfigData.EntityInteractionType.None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) InteractionType);
      }
      if (P1 != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(P1);
      }
      if (P2 != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(P2);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (InteractionType != global::Phoenix.ConfigData.EntityInteractionType.None) {
        output.WriteRawTag(24);
        output.WriteEnum((int) InteractionType);
      }
      if (P1 != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(P1);
      }
      if (P2 != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(P2);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (InteractionType != global::Phoenix.ConfigData.EntityInteractionType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) InteractionType);
      }
      if (P1 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(P1);
      }
      if (P2 != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(P2);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(HakoniwaEntityInteractionConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.InteractionType != global::Phoenix.ConfigData.EntityInteractionType.None) {
        InteractionType = other.InteractionType;
      }
      if (other.P1 != 0) {
        P1 = other.P1;
      }
      if (other.P2 != 0) {
        P2 = other.P2;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            InteractionType = (global::Phoenix.ConfigData.EntityInteractionType) input.ReadEnum();
            break;
          }
          case 32: {
            P1 = input.ReadInt32();
            break;
          }
          case 40: {
            P2 = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            InteractionType = (global::Phoenix.ConfigData.EntityInteractionType) input.ReadEnum();
            break;
          }
          case 32: {
            P1 = input.ReadInt32();
            break;
          }
          case 40: {
            P2 = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
