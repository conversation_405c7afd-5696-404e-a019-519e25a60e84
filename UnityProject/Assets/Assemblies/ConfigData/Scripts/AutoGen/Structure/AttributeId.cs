// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: AttributeId.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from AttributeId.proto</summary>
  public static partial class AttributeIdReflection {

    #region Descriptor
    /// <summary>File descriptor for AttributeId.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static AttributeIdReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChFBdHRyaWJ1dGVJZC5wcm90bxISUGhvZW5peC5Db25maWdEYXRhKpwLCgtB",
            "dHRyaWJ1dGVJZBIUChBBdHRyaWJ1dGVJZF9Ob25lEAASFQoRQXR0cmlidXRl",
            "SWRfSHBNYXgQARIbChdBdHRyaWJ1dGVJZF9TaGllbGRIcE1heBACEhgKFEF0",
            "dHJpYnV0ZUlkX0FuZ2VyTWF4EAMSHgoaQXR0cmlidXRlSWRfUGh5c2ljYWxB",
            "dHRhY2sQBBIdChlBdHRyaWJ1dGVJZF9NYWdpY2FsQXR0YWNrEAUSHwobQXR0",
            "cmlidXRlSWRfUGh5c2ljYWxEZWZlbmNlEAYSHgoaQXR0cmlidXRlSWRfTWFn",
            "aWNhbERlZmVuY2UQBxIUChBBdHRyaWJ1dGVJZF9UZWNoEAgSGAoUQXR0cmli",
            "dXRlSWRfVGVjaEFudGkQCRIVChFBdHRyaWJ1dGVJZF9TcGVlZBAKEhkKFUF0",
            "dHJpYnV0ZUlkX01vdmVQb2ludBALEhwKGEF0dHJpYnV0ZUlkX0NyaXRpY2Fs",
            "UmF0ZRAMEiAKHEF0dHJpYnV0ZUlkX0NyaXRpY2FsUmF0ZUFudGkQDRIiCh5B",
            "dHRyaWJ1dGVJZF9Dcml0aWNhbERhbWFnZVJhdGUQDhImCiJBdHRyaWJ1dGVJ",
            "ZF9Dcml0aWNhbERhbWFnZVJhdGVBbnRpEA8SIgoeQXR0cmlidXRlSWRfUGh5",
            "c2ljYWxEYW1hZ2VSYXRlEBASJgoiQXR0cmlidXRlSWRfUGh5c2ljYWxEYW1h",
            "Z2VSYXRlQW50aRAREiEKHUF0dHJpYnV0ZUlkX01hZ2ljYWxEYW1hZ2VSYXRl",
            "EBISJQohQXR0cmlidXRlSWRfTWFnaWNhbERhbWFnZVJhdGVBbnRpEBMSHQoZ",
            "QXR0cmlidXRlSWRfQWxsRGFtYWdlUmF0ZRAUEiEKHUF0dHJpYnV0ZUlkX0Fs",
            "bERhbWFnZVJhdGVBbnRpEBUSIwofQXR0cmlidXRlSWRfUGh5c2ljYWxEYW1h",
            "Z2VSYXRlMhAWEicKI0F0dHJpYnV0ZUlkX1BoeXNpY2FsRGFtYWdlUmF0ZTJB",
            "bnRpEBcSIgoeQXR0cmlidXRlSWRfTWFnaWNhbERhbWFnZVJhdGUyEBgSJgoi",
            "QXR0cmlidXRlSWRfTWFnaWNhbERhbWFnZVJhdGUyQW50aRAZEh4KGkF0dHJp",
            "YnV0ZUlkX0FsbERhbWFnZVJhdGUyEBoSIgoeQXR0cmlidXRlSWRfQWxsRGFt",
            "YWdlUmF0ZTJBbnRpEBsSJAogQXR0cmlidXRlSWRfSHBSYXRlRGFtYWdlUmF0",
            "ZUFudGkQHBIiCh5BdHRyaWJ1dGVJZF9SZWFsRGFtYWdlUmF0ZUFudGkQHRIY",
            "ChRBdHRyaWJ1dGVJZF9IZWFsUmF0ZRAeEhwKGEF0dHJpYnV0ZUlkX0hlYWxS",
            "YXRlQW50aRAfEikKJUF0dHJpYnV0ZUlkX0ZpZ2h0YmFja0F0dGFja0RhbWFn",
            "ZVJhdGUQIBIoCiRBdHRyaWJ1dGVJZF9DaGFzZUhpdEF0dGFja0RhbWFnZVJh",
            "dGUQIRIpCiVBdHRyaWJ1dGVJZF9Eb3VibGVIaXRBdHRhY2tEYW1hZ2VSYXRl",
            "ECISGQoVQXR0cmlidXRlSWRfRG9kZ2VSYXRlECMSKAokQXR0cmlidXRlSWRf",
            "UGh5c2ljYWxEZWZlbmNlQnJlYWtSYXRlECQSJwojQXR0cmlidXRlSWRfTWFn",
            "aWNhbERlZmVuY2VCcmVha1JhdGUQJRImCiJBdHRyaWJ1dGVJZF9FeHRyYUF0",
            "dGFja1NlbGVjdFJhbmdlECYSJQohQXR0cmlidXRlSWRfRXh0cmFTa2lsbFNl",
            "bGVjdFJhbmdlECcSJQohQXR0cmlidXRlSWRfRXh0cmFTa2lsbEVmZmVjdFJh",
            "bmdlECgSHwobQXR0cmlidXRlSWRfSGVhbHRoU3RlYWxSYXRlECliBnByb3Rv",
            "Mw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Phoenix.ConfigData.AttributeId), }, null, null));
    }
    #endregion

  }
  #region Enums
  public enum AttributeId {
    [pbr::OriginalName("AttributeId_None")] None = 0,
    [pbr::OriginalName("AttributeId_HpMax")] HpMax = 1,
    [pbr::OriginalName("AttributeId_ShieldHpMax")] ShieldHpMax = 2,
    [pbr::OriginalName("AttributeId_AngerMax")] AngerMax = 3,
    [pbr::OriginalName("AttributeId_PhysicalAttack")] PhysicalAttack = 4,
    [pbr::OriginalName("AttributeId_MagicalAttack")] MagicalAttack = 5,
    [pbr::OriginalName("AttributeId_PhysicalDefence")] PhysicalDefence = 6,
    [pbr::OriginalName("AttributeId_MagicalDefence")] MagicalDefence = 7,
    [pbr::OriginalName("AttributeId_Tech")] Tech = 8,
    [pbr::OriginalName("AttributeId_TechAnti")] TechAnti = 9,
    [pbr::OriginalName("AttributeId_Speed")] Speed = 10,
    [pbr::OriginalName("AttributeId_MovePoint")] MovePoint = 11,
    [pbr::OriginalName("AttributeId_CriticalRate")] CriticalRate = 12,
    [pbr::OriginalName("AttributeId_CriticalRateAnti")] CriticalRateAnti = 13,
    [pbr::OriginalName("AttributeId_CriticalDamageRate")] CriticalDamageRate = 14,
    [pbr::OriginalName("AttributeId_CriticalDamageRateAnti")] CriticalDamageRateAnti = 15,
    [pbr::OriginalName("AttributeId_PhysicalDamageRate")] PhysicalDamageRate = 16,
    [pbr::OriginalName("AttributeId_PhysicalDamageRateAnti")] PhysicalDamageRateAnti = 17,
    [pbr::OriginalName("AttributeId_MagicalDamageRate")] MagicalDamageRate = 18,
    [pbr::OriginalName("AttributeId_MagicalDamageRateAnti")] MagicalDamageRateAnti = 19,
    [pbr::OriginalName("AttributeId_AllDamageRate")] AllDamageRate = 20,
    [pbr::OriginalName("AttributeId_AllDamageRateAnti")] AllDamageRateAnti = 21,
    [pbr::OriginalName("AttributeId_PhysicalDamageRate2")] PhysicalDamageRate2 = 22,
    [pbr::OriginalName("AttributeId_PhysicalDamageRate2Anti")] PhysicalDamageRate2Anti = 23,
    [pbr::OriginalName("AttributeId_MagicalDamageRate2")] MagicalDamageRate2 = 24,
    [pbr::OriginalName("AttributeId_MagicalDamageRate2Anti")] MagicalDamageRate2Anti = 25,
    [pbr::OriginalName("AttributeId_AllDamageRate2")] AllDamageRate2 = 26,
    [pbr::OriginalName("AttributeId_AllDamageRate2Anti")] AllDamageRate2Anti = 27,
    [pbr::OriginalName("AttributeId_HpRateDamageRateAnti")] HpRateDamageRateAnti = 28,
    [pbr::OriginalName("AttributeId_RealDamageRateAnti")] RealDamageRateAnti = 29,
    [pbr::OriginalName("AttributeId_HealRate")] HealRate = 30,
    [pbr::OriginalName("AttributeId_HealRateAnti")] HealRateAnti = 31,
    [pbr::OriginalName("AttributeId_FightbackAttackDamageRate")] FightbackAttackDamageRate = 32,
    [pbr::OriginalName("AttributeId_ChaseHitAttackDamageRate")] ChaseHitAttackDamageRate = 33,
    [pbr::OriginalName("AttributeId_DoubleHitAttackDamageRate")] DoubleHitAttackDamageRate = 34,
    [pbr::OriginalName("AttributeId_DodgeRate")] DodgeRate = 35,
    [pbr::OriginalName("AttributeId_PhysicalDefenceBreakRate")] PhysicalDefenceBreakRate = 36,
    [pbr::OriginalName("AttributeId_MagicalDefenceBreakRate")] MagicalDefenceBreakRate = 37,
    [pbr::OriginalName("AttributeId_ExtraAttackSelectRange")] ExtraAttackSelectRange = 38,
    [pbr::OriginalName("AttributeId_ExtraSkillSelectRange")] ExtraSkillSelectRange = 39,
    [pbr::OriginalName("AttributeId_ExtraSkillEffectRange")] ExtraSkillEffectRange = 40,
    [pbr::OriginalName("AttributeId_HealthStealRate")] HealthStealRate = 41,
  }

  #endregion

}

#endregion Designer generated code
