// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: ActorPassiveFormation.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from ActorPassiveFormation.proto</summary>
  public static partial class ActorPassiveFormationReflection {

    #region Descriptor
    /// <summary>File descriptor for ActorPassiveFormation.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ActorPassiveFormationReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChtBY3RvclBhc3NpdmVGb3JtYXRpb24ucHJvdG8SElBob2VuaXguQ29uZmln",
            "RGF0YSJgCh9BY3RvclBhc3NpdmVGb3JtYXRpb25Db25maWdEYXRhEgoKAklk",
            "GAEgASgFEhcKD09mZmVuY2VCdWZmTGlzdBgCIAMoBRIYChBEZWZmZW5jZUJ1",
            "ZmZMaXN0GAMgASgFYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.ActorPassiveFormationConfigData), global::Phoenix.ConfigData.ActorPassiveFormationConfigData.Parser, new[]{ "Id", "OffenceBuffList", "DeffenceBuffList" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class ActorPassiveFormationConfigData : pb::IMessage<ActorPassiveFormationConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActorPassiveFormationConfigData> _parser = new pb::MessageParser<ActorPassiveFormationConfigData>(() => new ActorPassiveFormationConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ActorPassiveFormationConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.ActorPassiveFormationReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActorPassiveFormationConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActorPassiveFormationConfigData(ActorPassiveFormationConfigData other) : this() {
      id_ = other.id_;
      offenceBuffList_ = other.offenceBuffList_.Clone();
      deffenceBuffList_ = other.deffenceBuffList_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ActorPassiveFormationConfigData Clone() {
      return new ActorPassiveFormationConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "OffenceBuffList" field.</summary>
    public const int OffenceBuffListFieldNumber = 2;
    private static readonly pb::FieldCodec<int> _repeated_offenceBuffList_codec
        = pb::FieldCodec.ForInt32(18);
    private readonly pbc::RepeatedField<int> offenceBuffList_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> OffenceBuffList {
      get { return offenceBuffList_; }
    }

    /// <summary>Field number for the "DeffenceBuffList" field.</summary>
    public const int DeffenceBuffListFieldNumber = 3;
    private int deffenceBuffList_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int DeffenceBuffList {
      get { return deffenceBuffList_; }
      set {
        deffenceBuffList_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ActorPassiveFormationConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ActorPassiveFormationConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if(!offenceBuffList_.Equals(other.offenceBuffList_)) return false;
      if (DeffenceBuffList != other.DeffenceBuffList) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      hash ^= offenceBuffList_.GetHashCode();
      if (DeffenceBuffList != 0) hash ^= DeffenceBuffList.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      offenceBuffList_.WriteTo(output, _repeated_offenceBuffList_codec);
      if (DeffenceBuffList != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(DeffenceBuffList);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      offenceBuffList_.WriteTo(ref output, _repeated_offenceBuffList_codec);
      if (DeffenceBuffList != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(DeffenceBuffList);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      size += offenceBuffList_.CalculateSize(_repeated_offenceBuffList_codec);
      if (DeffenceBuffList != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DeffenceBuffList);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ActorPassiveFormationConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      offenceBuffList_.Add(other.offenceBuffList_);
      if (other.DeffenceBuffList != 0) {
        DeffenceBuffList = other.DeffenceBuffList;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            offenceBuffList_.AddEntriesFrom(input, _repeated_offenceBuffList_codec);
            break;
          }
          case 24: {
            DeffenceBuffList = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18:
          case 16: {
            offenceBuffList_.AddEntriesFrom(ref input, _repeated_offenceBuffList_codec);
            break;
          }
          case 24: {
            DeffenceBuffList = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
