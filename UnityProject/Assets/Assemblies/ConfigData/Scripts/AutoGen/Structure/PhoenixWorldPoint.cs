// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PhoenixWorldPoint.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from PhoenixWorldPoint.proto</summary>
  public static partial class PhoenixWorldPointReflection {

    #region Descriptor
    /// <summary>File descriptor for PhoenixWorldPoint.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PhoenixWorldPointReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChdQaG9lbml4V29ybGRQb2ludC5wcm90bxISUGhvZW5peC5Db25maWdEYXRh",
            "IrkBChtQaG9lbml4V29ybGRQb2ludENvbmZpZ0RhdGESCgoCSWQYASABKAUS",
            "EgoKUG9pbnRJbmRleBgCIAEoBRIMCgROYW1lGAMgASgJEhAKCEFyZWFOYW1l",
            "GAQgASgJEhAKCEFyZWFUaW1lGAUgASgJEg8KB1N0b3J5SWQYBiADKAUSGgoS",
            "RmluaXNoRHJvcFJld2FyZElkGAcgASgFEhsKE1BlcmZlY3REcm9wUmV3YXJk",
            "SWQYCCABKAViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.PhoenixWorldPointConfigData), global::Phoenix.ConfigData.PhoenixWorldPointConfigData.Parser, new[]{ "Id", "PointIndex", "Name", "AreaName", "AreaTime", "StoryId", "FinishDropRewardId", "PerfectDropRewardId" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class PhoenixWorldPointConfigData : pb::IMessage<PhoenixWorldPointConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PhoenixWorldPointConfigData> _parser = new pb::MessageParser<PhoenixWorldPointConfigData>(() => new PhoenixWorldPointConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PhoenixWorldPointConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.PhoenixWorldPointReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointConfigData(PhoenixWorldPointConfigData other) : this() {
      id_ = other.id_;
      pointIndex_ = other.pointIndex_;
      name_ = other.name_;
      areaName_ = other.areaName_;
      areaTime_ = other.areaTime_;
      storyId_ = other.storyId_.Clone();
      finishDropRewardId_ = other.finishDropRewardId_;
      perfectDropRewardId_ = other.perfectDropRewardId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixWorldPointConfigData Clone() {
      return new PhoenixWorldPointConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "PointIndex" field.</summary>
    public const int PointIndexFieldNumber = 2;
    private int pointIndex_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int PointIndex {
      get { return pointIndex_; }
      set {
        pointIndex_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 3;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "AreaName" field.</summary>
    public const int AreaNameFieldNumber = 4;
    private string areaName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string AreaName {
      get { return areaName_; }
      set {
        areaName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "AreaTime" field.</summary>
    public const int AreaTimeFieldNumber = 5;
    private string areaTime_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string AreaTime {
      get { return areaTime_; }
      set {
        areaTime_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "StoryId" field.</summary>
    public const int StoryIdFieldNumber = 6;
    private static readonly pb::FieldCodec<int> _repeated_storyId_codec
        = pb::FieldCodec.ForInt32(50);
    private readonly pbc::RepeatedField<int> storyId_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> StoryId {
      get { return storyId_; }
    }

    /// <summary>Field number for the "FinishDropRewardId" field.</summary>
    public const int FinishDropRewardIdFieldNumber = 7;
    private int finishDropRewardId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int FinishDropRewardId {
      get { return finishDropRewardId_; }
      set {
        finishDropRewardId_ = value;
      }
    }

    /// <summary>Field number for the "PerfectDropRewardId" field.</summary>
    public const int PerfectDropRewardIdFieldNumber = 8;
    private int perfectDropRewardId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int PerfectDropRewardId {
      get { return perfectDropRewardId_; }
      set {
        perfectDropRewardId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PhoenixWorldPointConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PhoenixWorldPointConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (PointIndex != other.PointIndex) return false;
      if (Name != other.Name) return false;
      if (AreaName != other.AreaName) return false;
      if (AreaTime != other.AreaTime) return false;
      if(!storyId_.Equals(other.storyId_)) return false;
      if (FinishDropRewardId != other.FinishDropRewardId) return false;
      if (PerfectDropRewardId != other.PerfectDropRewardId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (PointIndex != 0) hash ^= PointIndex.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (AreaName.Length != 0) hash ^= AreaName.GetHashCode();
      if (AreaTime.Length != 0) hash ^= AreaTime.GetHashCode();
      hash ^= storyId_.GetHashCode();
      if (FinishDropRewardId != 0) hash ^= FinishDropRewardId.GetHashCode();
      if (PerfectDropRewardId != 0) hash ^= PerfectDropRewardId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (PointIndex != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(PointIndex);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (AreaName.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(AreaName);
      }
      if (AreaTime.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(AreaTime);
      }
      storyId_.WriteTo(output, _repeated_storyId_codec);
      if (FinishDropRewardId != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(FinishDropRewardId);
      }
      if (PerfectDropRewardId != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(PerfectDropRewardId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (PointIndex != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(PointIndex);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (AreaName.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(AreaName);
      }
      if (AreaTime.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(AreaTime);
      }
      storyId_.WriteTo(ref output, _repeated_storyId_codec);
      if (FinishDropRewardId != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(FinishDropRewardId);
      }
      if (PerfectDropRewardId != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(PerfectDropRewardId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (PointIndex != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PointIndex);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (AreaName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AreaName);
      }
      if (AreaTime.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AreaTime);
      }
      size += storyId_.CalculateSize(_repeated_storyId_codec);
      if (FinishDropRewardId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(FinishDropRewardId);
      }
      if (PerfectDropRewardId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PerfectDropRewardId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PhoenixWorldPointConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.PointIndex != 0) {
        PointIndex = other.PointIndex;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.AreaName.Length != 0) {
        AreaName = other.AreaName;
      }
      if (other.AreaTime.Length != 0) {
        AreaTime = other.AreaTime;
      }
      storyId_.Add(other.storyId_);
      if (other.FinishDropRewardId != 0) {
        FinishDropRewardId = other.FinishDropRewardId;
      }
      if (other.PerfectDropRewardId != 0) {
        PerfectDropRewardId = other.PerfectDropRewardId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            PointIndex = input.ReadInt32();
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 34: {
            AreaName = input.ReadString();
            break;
          }
          case 42: {
            AreaTime = input.ReadString();
            break;
          }
          case 50:
          case 48: {
            storyId_.AddEntriesFrom(input, _repeated_storyId_codec);
            break;
          }
          case 56: {
            FinishDropRewardId = input.ReadInt32();
            break;
          }
          case 64: {
            PerfectDropRewardId = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            PointIndex = input.ReadInt32();
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 34: {
            AreaName = input.ReadString();
            break;
          }
          case 42: {
            AreaTime = input.ReadString();
            break;
          }
          case 50:
          case 48: {
            storyId_.AddEntriesFrom(ref input, _repeated_storyId_codec);
            break;
          }
          case 56: {
            FinishDropRewardId = input.ReadInt32();
            break;
          }
          case 64: {
            PerfectDropRewardId = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
