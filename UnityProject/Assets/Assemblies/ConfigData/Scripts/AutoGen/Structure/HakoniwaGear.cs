// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: HakoniwaGear.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from HakoniwaGear.proto</summary>
  public static partial class HakoniwaGearReflection {

    #region Descriptor
    /// <summary>File descriptor for HakoniwaGear.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HakoniwaGearReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChJIYWtvbml3YUdlYXIucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YSJXChZI",
            "YWtvbml3YUdlYXJDb25maWdEYXRhEgoKAklkGAEgASgFEg4KBkdlYXJJZBgC",
            "IAEoCRIRCglJbml0U3RhdGUYAyABKAkSDgoGU3RhdGVzGAQgAygJYgZwcm90",
            "bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.HakoniwaGearConfigData), global::Phoenix.ConfigData.HakoniwaGearConfigData.Parser, new[]{ "Id", "GearId", "InitState", "States" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class HakoniwaGearConfigData : pb::IMessage<HakoniwaGearConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HakoniwaGearConfigData> _parser = new pb::MessageParser<HakoniwaGearConfigData>(() => new HakoniwaGearConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<HakoniwaGearConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.HakoniwaGearReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaGearConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaGearConfigData(HakoniwaGearConfigData other) : this() {
      id_ = other.id_;
      gearId_ = other.gearId_;
      initState_ = other.initState_;
      states_ = other.states_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaGearConfigData Clone() {
      return new HakoniwaGearConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "GearId" field.</summary>
    public const int GearIdFieldNumber = 2;
    private string gearId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string GearId {
      get { return gearId_; }
      set {
        gearId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "InitState" field.</summary>
    public const int InitStateFieldNumber = 3;
    private string initState_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string InitState {
      get { return initState_; }
      set {
        initState_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "States" field.</summary>
    public const int StatesFieldNumber = 4;
    private static readonly pb::FieldCodec<string> _repeated_states_codec
        = pb::FieldCodec.ForString(34);
    private readonly pbc::RepeatedField<string> states_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<string> States {
      get { return states_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as HakoniwaGearConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(HakoniwaGearConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (GearId != other.GearId) return false;
      if (InitState != other.InitState) return false;
      if(!states_.Equals(other.states_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (GearId.Length != 0) hash ^= GearId.GetHashCode();
      if (InitState.Length != 0) hash ^= InitState.GetHashCode();
      hash ^= states_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (GearId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(GearId);
      }
      if (InitState.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(InitState);
      }
      states_.WriteTo(output, _repeated_states_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (GearId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(GearId);
      }
      if (InitState.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(InitState);
      }
      states_.WriteTo(ref output, _repeated_states_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (GearId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GearId);
      }
      if (InitState.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(InitState);
      }
      size += states_.CalculateSize(_repeated_states_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(HakoniwaGearConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.GearId.Length != 0) {
        GearId = other.GearId;
      }
      if (other.InitState.Length != 0) {
        InitState = other.InitState;
      }
      states_.Add(other.states_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            GearId = input.ReadString();
            break;
          }
          case 26: {
            InitState = input.ReadString();
            break;
          }
          case 34: {
            states_.AddEntriesFrom(input, _repeated_states_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            GearId = input.ReadString();
            break;
          }
          case 26: {
            InitState = input.ReadString();
            break;
          }
          case 34: {
            states_.AddEntriesFrom(ref input, _repeated_states_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
