// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PhoenixHakoniwa.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from PhoenixHakoniwa.proto</summary>
  public static partial class PhoenixHakoniwaReflection {

    #region Descriptor
    /// <summary>File descriptor for PhoenixHakoniwa.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PhoenixHakoniwaReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVQaG9lbml4SGFrb25pd2EucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YRoZ",
            "UGhvZW5peEhha29uaXdhVHlwZS5wcm90byLPAQoZUGhvZW5peEhha29uaXdh",
            "Q29uZmlnRGF0YRIKCgJJZBgBIAEoBRI1CgRUeXBlGAIgASgOMicuUGhvZW5p",
            "eC5Db25maWdEYXRhLlBob2VuaXhIYWtvbml3YVR5cGUSDAoETmFtZRgDIAEo",
            "CRIUCgxRdWVzdEdyYXBoSWQYBCABKAUSFAoMSW5pdFdheXBvaW50GAUgASgF",
            "EhQKDFBsYXllclNraW5JZBgGIAEoBRIPCgdTa2lsbElkGAcgASgFEg4KBkFj",
            "dG9ycxgIIAMoBWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.PhoenixHakoniwaTypeReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.PhoenixHakoniwaConfigData), global::Phoenix.ConfigData.PhoenixHakoniwaConfigData.Parser, new[]{ "Id", "Type", "Name", "QuestGraphId", "InitWaypoint", "PlayerSkinId", "SkillId", "Actors" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class PhoenixHakoniwaConfigData : pb::IMessage<PhoenixHakoniwaConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PhoenixHakoniwaConfigData> _parser = new pb::MessageParser<PhoenixHakoniwaConfigData>(() => new PhoenixHakoniwaConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PhoenixHakoniwaConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.PhoenixHakoniwaReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixHakoniwaConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixHakoniwaConfigData(PhoenixHakoniwaConfigData other) : this() {
      id_ = other.id_;
      type_ = other.type_;
      name_ = other.name_;
      questGraphId_ = other.questGraphId_;
      initWaypoint_ = other.initWaypoint_;
      playerSkinId_ = other.playerSkinId_;
      skillId_ = other.skillId_;
      actors_ = other.actors_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PhoenixHakoniwaConfigData Clone() {
      return new PhoenixHakoniwaConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Type" field.</summary>
    public const int TypeFieldNumber = 2;
    private global::Phoenix.ConfigData.PhoenixHakoniwaType type_ = global::Phoenix.ConfigData.PhoenixHakoniwaType.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::Phoenix.ConfigData.PhoenixHakoniwaType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 3;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "QuestGraphId" field.</summary>
    public const int QuestGraphIdFieldNumber = 4;
    private int questGraphId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int QuestGraphId {
      get { return questGraphId_; }
      set {
        questGraphId_ = value;
      }
    }

    /// <summary>Field number for the "InitWaypoint" field.</summary>
    public const int InitWaypointFieldNumber = 5;
    private int initWaypoint_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int InitWaypoint {
      get { return initWaypoint_; }
      set {
        initWaypoint_ = value;
      }
    }

    /// <summary>Field number for the "PlayerSkinId" field.</summary>
    public const int PlayerSkinIdFieldNumber = 6;
    private int playerSkinId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int PlayerSkinId {
      get { return playerSkinId_; }
      set {
        playerSkinId_ = value;
      }
    }

    /// <summary>Field number for the "SkillId" field.</summary>
    public const int SkillIdFieldNumber = 7;
    private int skillId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SkillId {
      get { return skillId_; }
      set {
        skillId_ = value;
      }
    }

    /// <summary>Field number for the "Actors" field.</summary>
    public const int ActorsFieldNumber = 8;
    private static readonly pb::FieldCodec<int> _repeated_actors_codec
        = pb::FieldCodec.ForInt32(66);
    private readonly pbc::RepeatedField<int> actors_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> Actors {
      get { return actors_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PhoenixHakoniwaConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PhoenixHakoniwaConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Type != other.Type) return false;
      if (Name != other.Name) return false;
      if (QuestGraphId != other.QuestGraphId) return false;
      if (InitWaypoint != other.InitWaypoint) return false;
      if (PlayerSkinId != other.PlayerSkinId) return false;
      if (SkillId != other.SkillId) return false;
      if(!actors_.Equals(other.actors_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaType.None) hash ^= Type.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (QuestGraphId != 0) hash ^= QuestGraphId.GetHashCode();
      if (InitWaypoint != 0) hash ^= InitWaypoint.GetHashCode();
      if (PlayerSkinId != 0) hash ^= PlayerSkinId.GetHashCode();
      if (SkillId != 0) hash ^= SkillId.GetHashCode();
      hash ^= actors_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaType.None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Type);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (QuestGraphId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(QuestGraphId);
      }
      if (InitWaypoint != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(InitWaypoint);
      }
      if (PlayerSkinId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PlayerSkinId);
      }
      if (SkillId != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(SkillId);
      }
      actors_.WriteTo(output, _repeated_actors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaType.None) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Type);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Name);
      }
      if (QuestGraphId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(QuestGraphId);
      }
      if (InitWaypoint != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(InitWaypoint);
      }
      if (PlayerSkinId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PlayerSkinId);
      }
      if (SkillId != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(SkillId);
      }
      actors_.WriteTo(ref output, _repeated_actors_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Type != global::Phoenix.ConfigData.PhoenixHakoniwaType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (QuestGraphId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(QuestGraphId);
      }
      if (InitWaypoint != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(InitWaypoint);
      }
      if (PlayerSkinId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PlayerSkinId);
      }
      if (SkillId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SkillId);
      }
      size += actors_.CalculateSize(_repeated_actors_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PhoenixHakoniwaConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Type != global::Phoenix.ConfigData.PhoenixHakoniwaType.None) {
        Type = other.Type;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.QuestGraphId != 0) {
        QuestGraphId = other.QuestGraphId;
      }
      if (other.InitWaypoint != 0) {
        InitWaypoint = other.InitWaypoint;
      }
      if (other.PlayerSkinId != 0) {
        PlayerSkinId = other.PlayerSkinId;
      }
      if (other.SkillId != 0) {
        SkillId = other.SkillId;
      }
      actors_.Add(other.actors_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Type = (global::Phoenix.ConfigData.PhoenixHakoniwaType) input.ReadEnum();
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 32: {
            QuestGraphId = input.ReadInt32();
            break;
          }
          case 40: {
            InitWaypoint = input.ReadInt32();
            break;
          }
          case 48: {
            PlayerSkinId = input.ReadInt32();
            break;
          }
          case 56: {
            SkillId = input.ReadInt32();
            break;
          }
          case 66:
          case 64: {
            actors_.AddEntriesFrom(input, _repeated_actors_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Type = (global::Phoenix.ConfigData.PhoenixHakoniwaType) input.ReadEnum();
            break;
          }
          case 26: {
            Name = input.ReadString();
            break;
          }
          case 32: {
            QuestGraphId = input.ReadInt32();
            break;
          }
          case 40: {
            InitWaypoint = input.ReadInt32();
            break;
          }
          case 48: {
            PlayerSkinId = input.ReadInt32();
            break;
          }
          case 56: {
            SkillId = input.ReadInt32();
            break;
          }
          case 66:
          case 64: {
            actors_.AddEntriesFrom(ref input, _repeated_actors_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
