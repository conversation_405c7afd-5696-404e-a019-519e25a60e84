// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: HakoniwaMonster.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from HakoniwaMonster.proto</summary>
  public static partial class HakoniwaMonsterReflection {

    #region Descriptor
    /// <summary>File descriptor for HakoniwaMonster.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HakoniwaMonsterReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVIYWtvbml3YU1vbnN0ZXIucHJvdG8SElBob2VuaXguQ29uZmlnRGF0YRoT",
            "U2NlbmVMb2NhdGlvbi5wcm90byLVAQoZSGFrb25pd2FNb25zdGVyQ29uZmln",
            "RGF0YRIKCgJJZBgBIAEoBRIPCgdMZXZlbElkGAIgASgFEhUKDUFsZXJ0RGlz",
            "dGFuY2UYAyABKAISEAoIQWxlcnRGT1YYBCABKAUSOwoGUGF0cm9sGAUgAygL",
            "MisuUGhvZW5peC5Db25maWdEYXRhLlNjZW5lTG9jYXRpb25Db25maWdEYXRh",
            "EhEKCUhhdGVHcm91cBgGIAMoBRISCgpPbmVIaXRLaWxsGAcgASgIEg4KBklz",
            "V2FrZRgIIAEoCGIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Phoenix.ConfigData.SceneLocationReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.HakoniwaMonsterConfigData), global::Phoenix.ConfigData.HakoniwaMonsterConfigData.Parser, new[]{ "Id", "LevelId", "AlertDistance", "AlertFOV", "Patrol", "HateGroup", "OneHitKill", "IsWake" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class HakoniwaMonsterConfigData : pb::IMessage<HakoniwaMonsterConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HakoniwaMonsterConfigData> _parser = new pb::MessageParser<HakoniwaMonsterConfigData>(() => new HakoniwaMonsterConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<HakoniwaMonsterConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.HakoniwaMonsterReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaMonsterConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaMonsterConfigData(HakoniwaMonsterConfigData other) : this() {
      id_ = other.id_;
      levelId_ = other.levelId_;
      alertDistance_ = other.alertDistance_;
      alertFOV_ = other.alertFOV_;
      patrol_ = other.patrol_.Clone();
      hateGroup_ = other.hateGroup_.Clone();
      oneHitKill_ = other.oneHitKill_;
      isWake_ = other.isWake_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public HakoniwaMonsterConfigData Clone() {
      return new HakoniwaMonsterConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "LevelId" field.</summary>
    public const int LevelIdFieldNumber = 2;
    private int levelId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int LevelId {
      get { return levelId_; }
      set {
        levelId_ = value;
      }
    }

    /// <summary>Field number for the "AlertDistance" field.</summary>
    public const int AlertDistanceFieldNumber = 3;
    private float alertDistance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public float AlertDistance {
      get { return alertDistance_; }
      set {
        alertDistance_ = value;
      }
    }

    /// <summary>Field number for the "AlertFOV" field.</summary>
    public const int AlertFOVFieldNumber = 4;
    private int alertFOV_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int AlertFOV {
      get { return alertFOV_; }
      set {
        alertFOV_ = value;
      }
    }

    /// <summary>Field number for the "Patrol" field.</summary>
    public const int PatrolFieldNumber = 5;
    private static readonly pb::FieldCodec<global::Phoenix.ConfigData.SceneLocationConfigData> _repeated_patrol_codec
        = pb::FieldCodec.ForMessage(42, global::Phoenix.ConfigData.SceneLocationConfigData.Parser);
    private readonly pbc::RepeatedField<global::Phoenix.ConfigData.SceneLocationConfigData> patrol_ = new pbc::RepeatedField<global::Phoenix.ConfigData.SceneLocationConfigData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::Phoenix.ConfigData.SceneLocationConfigData> Patrol {
      get { return patrol_; }
    }

    /// <summary>Field number for the "HateGroup" field.</summary>
    public const int HateGroupFieldNumber = 6;
    private static readonly pb::FieldCodec<int> _repeated_hateGroup_codec
        = pb::FieldCodec.ForInt32(50);
    private readonly pbc::RepeatedField<int> hateGroup_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> HateGroup {
      get { return hateGroup_; }
    }

    /// <summary>Field number for the "OneHitKill" field.</summary>
    public const int OneHitKillFieldNumber = 7;
    private bool oneHitKill_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool OneHitKill {
      get { return oneHitKill_; }
      set {
        oneHitKill_ = value;
      }
    }

    /// <summary>Field number for the "IsWake" field.</summary>
    public const int IsWakeFieldNumber = 8;
    private bool isWake_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool IsWake {
      get { return isWake_; }
      set {
        isWake_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as HakoniwaMonsterConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(HakoniwaMonsterConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (LevelId != other.LevelId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(AlertDistance, other.AlertDistance)) return false;
      if (AlertFOV != other.AlertFOV) return false;
      if(!patrol_.Equals(other.patrol_)) return false;
      if(!hateGroup_.Equals(other.hateGroup_)) return false;
      if (OneHitKill != other.OneHitKill) return false;
      if (IsWake != other.IsWake) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (LevelId != 0) hash ^= LevelId.GetHashCode();
      if (AlertDistance != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(AlertDistance);
      if (AlertFOV != 0) hash ^= AlertFOV.GetHashCode();
      hash ^= patrol_.GetHashCode();
      hash ^= hateGroup_.GetHashCode();
      if (OneHitKill != false) hash ^= OneHitKill.GetHashCode();
      if (IsWake != false) hash ^= IsWake.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (LevelId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(LevelId);
      }
      if (AlertDistance != 0F) {
        output.WriteRawTag(29);
        output.WriteFloat(AlertDistance);
      }
      if (AlertFOV != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(AlertFOV);
      }
      patrol_.WriteTo(output, _repeated_patrol_codec);
      hateGroup_.WriteTo(output, _repeated_hateGroup_codec);
      if (OneHitKill != false) {
        output.WriteRawTag(56);
        output.WriteBool(OneHitKill);
      }
      if (IsWake != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsWake);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (LevelId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(LevelId);
      }
      if (AlertDistance != 0F) {
        output.WriteRawTag(29);
        output.WriteFloat(AlertDistance);
      }
      if (AlertFOV != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(AlertFOV);
      }
      patrol_.WriteTo(ref output, _repeated_patrol_codec);
      hateGroup_.WriteTo(ref output, _repeated_hateGroup_codec);
      if (OneHitKill != false) {
        output.WriteRawTag(56);
        output.WriteBool(OneHitKill);
      }
      if (IsWake != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsWake);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (LevelId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LevelId);
      }
      if (AlertDistance != 0F) {
        size += 1 + 4;
      }
      if (AlertFOV != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AlertFOV);
      }
      size += patrol_.CalculateSize(_repeated_patrol_codec);
      size += hateGroup_.CalculateSize(_repeated_hateGroup_codec);
      if (OneHitKill != false) {
        size += 1 + 1;
      }
      if (IsWake != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(HakoniwaMonsterConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.LevelId != 0) {
        LevelId = other.LevelId;
      }
      if (other.AlertDistance != 0F) {
        AlertDistance = other.AlertDistance;
      }
      if (other.AlertFOV != 0) {
        AlertFOV = other.AlertFOV;
      }
      patrol_.Add(other.patrol_);
      hateGroup_.Add(other.hateGroup_);
      if (other.OneHitKill != false) {
        OneHitKill = other.OneHitKill;
      }
      if (other.IsWake != false) {
        IsWake = other.IsWake;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            LevelId = input.ReadInt32();
            break;
          }
          case 29: {
            AlertDistance = input.ReadFloat();
            break;
          }
          case 32: {
            AlertFOV = input.ReadInt32();
            break;
          }
          case 42: {
            patrol_.AddEntriesFrom(input, _repeated_patrol_codec);
            break;
          }
          case 50:
          case 48: {
            hateGroup_.AddEntriesFrom(input, _repeated_hateGroup_codec);
            break;
          }
          case 56: {
            OneHitKill = input.ReadBool();
            break;
          }
          case 64: {
            IsWake = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            LevelId = input.ReadInt32();
            break;
          }
          case 29: {
            AlertDistance = input.ReadFloat();
            break;
          }
          case 32: {
            AlertFOV = input.ReadInt32();
            break;
          }
          case 42: {
            patrol_.AddEntriesFrom(ref input, _repeated_patrol_codec);
            break;
          }
          case 50:
          case 48: {
            hateGroup_.AddEntriesFrom(ref input, _repeated_hateGroup_codec);
            break;
          }
          case 56: {
            OneHitKill = input.ReadBool();
            break;
          }
          case 64: {
            IsWake = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
