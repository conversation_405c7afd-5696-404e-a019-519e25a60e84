// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: GuideBattle.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Phoenix.ConfigData {

  /// <summary>Holder for reflection information generated from GuideBattle.proto</summary>
  public static partial class GuideBattleReflection {

    #region Descriptor
    /// <summary>File descriptor for GuideBattle.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static GuideBattleReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChFHdWlkZUJhdHRsZS5wcm90bxISUGhvZW5peC5Db25maWdEYXRhImkKFUd1",
            "aWRlQmF0dGxlQ29uZmlnRGF0YRIKCgJJZBgBIAEoBRIMCgROYW1lGAIgASgJ",
            "EhAKCEJhdHRsZUlkGAMgASgFEg8KB0dyb3VwSWQYBCABKAUSEwoLVW5sb2Nr",
            "SW5kZXgYBSABKAViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Phoenix.ConfigData.GuideBattleConfigData), global::Phoenix.ConfigData.GuideBattleConfigData.Parser, new[]{ "Id", "Name", "BattleId", "GroupId", "UnlockIndex" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  public sealed partial class GuideBattleConfigData : pb::IMessage<GuideBattleConfigData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GuideBattleConfigData> _parser = new pb::MessageParser<GuideBattleConfigData>(() => new GuideBattleConfigData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<GuideBattleConfigData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Phoenix.ConfigData.GuideBattleReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GuideBattleConfigData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GuideBattleConfigData(GuideBattleConfigData other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      battleId_ = other.battleId_;
      groupId_ = other.groupId_;
      unlockIndex_ = other.unlockIndex_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public GuideBattleConfigData Clone() {
      return new GuideBattleConfigData(this);
    }

    /// <summary>Field number for the "Id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "Name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "BattleId" field.</summary>
    public const int BattleIdFieldNumber = 3;
    private int battleId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BattleId {
      get { return battleId_; }
      set {
        battleId_ = value;
      }
    }

    /// <summary>Field number for the "GroupId" field.</summary>
    public const int GroupIdFieldNumber = 4;
    private int groupId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int GroupId {
      get { return groupId_; }
      set {
        groupId_ = value;
      }
    }

    /// <summary>Field number for the "UnlockIndex" field.</summary>
    public const int UnlockIndexFieldNumber = 5;
    private int unlockIndex_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int UnlockIndex {
      get { return unlockIndex_; }
      set {
        unlockIndex_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as GuideBattleConfigData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(GuideBattleConfigData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (BattleId != other.BattleId) return false;
      if (GroupId != other.GroupId) return false;
      if (UnlockIndex != other.UnlockIndex) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (BattleId != 0) hash ^= BattleId.GetHashCode();
      if (GroupId != 0) hash ^= GroupId.GetHashCode();
      if (UnlockIndex != 0) hash ^= UnlockIndex.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (BattleId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(BattleId);
      }
      if (GroupId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(GroupId);
      }
      if (UnlockIndex != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(UnlockIndex);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (BattleId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(BattleId);
      }
      if (GroupId != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(GroupId);
      }
      if (UnlockIndex != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(UnlockIndex);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (BattleId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BattleId);
      }
      if (GroupId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(GroupId);
      }
      if (UnlockIndex != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(UnlockIndex);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(GuideBattleConfigData other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.BattleId != 0) {
        BattleId = other.BattleId;
      }
      if (other.GroupId != 0) {
        GroupId = other.GroupId;
      }
      if (other.UnlockIndex != 0) {
        UnlockIndex = other.UnlockIndex;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            BattleId = input.ReadInt32();
            break;
          }
          case 32: {
            GroupId = input.ReadInt32();
            break;
          }
          case 40: {
            UnlockIndex = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            BattleId = input.ReadInt32();
            break;
          }
          case 32: {
            GroupId = input.ReadInt32();
            break;
          }
          case 40: {
            UnlockIndex = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
