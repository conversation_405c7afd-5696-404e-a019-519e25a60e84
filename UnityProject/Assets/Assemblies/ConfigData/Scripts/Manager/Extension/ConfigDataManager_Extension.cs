using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;

namespace Phoenix.ConfigData
{
    public partial class ConfigDataManager
    {
        private Dictionary<int, Dictionary<int, int>> m_entityResIdMap = new Dictionary<int, Dictionary<int, int>>();



        public void AnalyzeAfterLoadAll()
        {
            AnalyzeEntityResId();
            CheckAttributePartId();

            PostInitEntitySkinConfigData();
            PostInitActorConfigData();

            PostInitWorldConfigData();
            PostInitQuestGroupConfigData();
            PostInitQuestConfigData();
            PostInitPhoenixEntityConfigData();
        }

        private void AnalyzeEntityResId()
        {
            m_entityResIdMap.Clear();

            Dictionary<int, int> resMap = new Dictionary<int, int>();
            m_entityResIdMap.Add((int)EntityType.Actor, resMap);
            foreach (var kv in m_actorMap)
            {
                resMap.Add(kv.Key, kv.Value.SkinId);
            }
            resMap = new Dictionary<int, int>();
            m_entityResIdMap.Add((int)EntityType.TerrainBuff, resMap);
            foreach (var kv in m_terrainBuffMap)
            {
                resMap.Add(kv.Key, kv.Value.ResConfigId);
            }
        }

        private void AnalyzeSkill()
        {
            //foreach (var kv in m_skillMap)
            //{
            //    if (kv.Value.ExSkillId > 0)
            //    {
            //        var exSkill = GetSkill(kv.Value.ExSkillId);
            //        exSkill.OriginSkillId = kv.Value.Id;
            //    }
            //}
        }

        private void CheckAttributePartId()
        {
            if (Enum.GetValues(typeof(AttributePartId)).Length > 255)
            {
                throw new Exception("AttributePartId超过了255个");
            }
        }

        public EntitySkinConfigData GetEntitySkin(EntityType entityType, int entityRid)
        {
            return GetEntitySkin(GetEntitySkinId(entityType, entityRid));
        }

        public int GetEntitySkinId(EntityType entityType, int entityRid)
        {
            Dictionary<int, int> resIdMap;
            if (m_entityResIdMap.TryGetValue((int)entityType, out resIdMap))
            {
                int resId;
                if (resIdMap.TryGetValue(entityRid, out resId))
                {
                    return resId;
                }
            }
            return 0;
        }


        private void PostInitWorldConfigData()
        {
            foreach (var kv in worldMap)
            {
                WorldConfigData worldConfigData = kv.Value;
                if (worldConfigData != null)
                {
                    worldConfigData.m_entityIds.Clear();
                }
            }

            foreach (var kv in m_worldEntityMap)
            {
                WorldEntityConfigData worldEntityConfigData = kv.Value;
                if (!worldEntityConfigData.IsStatic)
                {
                    continue;
                }
                PostInitWorldEntity(worldEntityConfigData);
            }

            foreach (var (k,v) in m_phoenixWorldPointStoryMap)
            {
                if (v.WorldPoint > 0)
                {
                    var worldPoint = GetPhoenixWorldPoint(v.WorldPoint);
                    worldPoint.Stories.Add(v);
                }

                if (v.StoryFuncType == PhoenixWorldPointStoryFuncType.Hakoniwa)
                {
                    var hakoniwaConf = GetPhoenixHakoniwa(v.StoryParam1);
                    Debug.Assert(hakoniwaConf != null);
                    v.HakoniwaStory = hakoniwaConf;
                }
            }
        }

        private void PostInitWorldEntity(WorldEntityConfigData worldEntity)
        {
            WorldConfigData worldConfigData = GetWorld(worldEntity.WorldId);
            if (worldConfigData == null)
            {
                return;
            }

            worldConfigData.m_entityIds.Add(worldEntity.Id);
            if (worldEntity.EntityType == WorldEntityType.Npc)
            {
                worldEntity.m_defaultInteractions.Clear();
                foreach (Int32 interactionId in worldEntity.Interactions)
                {
                    EntityInteractionConfigData interactionConfig = GetEntityInteraction(interactionId);
                    if (interactionConfig != null)
                    {
                        WorldEntityInteractionConfig worldEntityInteraction = new WorldEntityInteractionConfig(worldEntity.Id, interactionConfig);
                        worldEntity.m_defaultInteractions.Add(worldEntityInteraction);
                    }
                }
            }
        }


        private void PostInitQuestGroupConfigData()
        {
            foreach (var kv in m_questGroupMap)
            {
                var questGroup = kv.Value;
                questGroup.m_questInfoIds.Clear();
                questGroup.m_questInfos.Clear();
                Int32 questId = questGroup.BeginQuestId;
                while (questId <= questGroup.EndQuestId)
                {
                    QuestConfigData questConfigData = GetQuest(questId);
                    if (questConfigData != null)
                    {
                        questConfigData.m_questGroupType = questGroup.GroupType;
                        questConfigData.m_questGroupConfigData = questGroup;
                        questGroup.m_questInfoIds.Add(questId);
                        questGroup.m_questInfos.Add(questConfigData);
                    }
                    questId++;
                }
            }
        }

        private void PostInitQuestConfigData()
        {
            foreach (var item in questMap)
            {
                item.Value.AnalyzeCondition();
            }

            foreach (var (k,v) in phoenixHakoniwaMap)
            {
                v.QuestGraphConfig = GetPhoenixHakoniwaQuestGraph(v.QuestGraphId);
                v.InitWaypointConfig = GetHakoniwaWaypoint(v.InitWaypoint);
            }
        }

        private void PostInitPhoenixEntityConfigData()
        {
            foreach (var (k,v) in hakoniwaMonsterMap)
            {
                foreach (var hateGroupId in v.HateGroup)
                {
                    var hateGroup = GetMonsterHateGroup(hateGroupId);
                    hateGroup.MonsterList.Add(v);
                }
            }
        }


        public Int32 GetConstValue(BattleConstValueId id)
        {
            Int32 value = 0;
            BattleConstValueConfigData config = ConfigDataManager.instance.GetBattleConstValue(id);
            if (config != null)
            {
                value = config.ConstValue / config.Precision;
            }
            return value;
        }

        private void PostInitEntitySkinConfigData()
        {
            foreach(var kv in m_entitySkinMap)
            {
                var entitySkin = kv.Value;
                if (entitySkin != null)
                {
                    entitySkin.entity2DSkinConfig = GetEntity2DSkin(entitySkin.IconSkinId);
                }
            }
        }

        private void PostInitActorConfigData()
        {
            foreach (var kv in m_collectableActorMap)
            {
                var collectableActor = kv.Value;
                var actor = GetActor(collectableActor.EntityConfigId);
                if (actor != null)
                {
                    actor.IsDestiny = collectableActor.IsDestiny;
                }
            }
        }
    }
}
