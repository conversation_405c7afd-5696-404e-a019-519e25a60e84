using System;
using System.Collections;
using System.Collections.Generic;
using MessagePack;
using MessagePack.Resolvers;

namespace Phoenix.ConfigData
{
    public class MessagePackDataLoader<TItem> : ProtoDataLoader
    {
        private Dictionary<int, TItem> m_map;
        private Func<TItem, int> m_funcOnGetId;
        private Action<MessagePackDataLoader<TItem>> m_actionOnLoadEnd;

        public Dictionary<int, TItem> map
        {
            get { return m_map; }
        }

        public MessagePackDataLoader(Func<TItem, int> funcOnGetId, Action<MessagePackDataLoader<TItem>> actionOnLoadEnd)
        {
            m_funcOnGetId = funcOnGetId;
            m_actionOnLoadEnd = actionOnLoadEnd;
        }

        protected override void OnLoad(byte[] buffer)
        {
            int capacity = BitConverter.ToInt32(buffer, 0);
            capacity = System.Net.IPAddress.NetworkToHostOrder(capacity);
            int lengthTypeSize = sizeof(int);
            m_map = new Dictionary<int, TItem>(Math.Clamp(capacity, 0, buffer.Length / lengthTypeSize));
            int offset = lengthTypeSize;
            while (offset < buffer.Length)
            {
                int length = BitConverter.ToInt32(buffer, offset);
                length = System.Net.IPAddress.NetworkToHostOrder(length);
                offset += lengthTypeSize;
                if (length < 0 || length + offset > buffer.Length)
                {
                    break;
                }
                TItem dataObj = default;
                try
                {
                    var resolver = CompositeResolver.Create(BattleResolver.Instance, StandardResolver.Instance);
                    var options = MessagePackSerializerOptions.Standard.WithResolver(resolver);
                    var roBuffer = new System.Buffers.ReadOnlySequence<byte>(buffer, offset, length);
                    dataObj = MessagePackSerializer.Deserialize<TItem>(roBuffer, options);
                }
                catch //TODO:以后做一个Log输出
                {
                }
                offset += length;
                if (dataObj != null)
                {
                    m_map.Add(m_funcOnGetId(dataObj), dataObj);
                }
            }
            m_actionOnLoadEnd(this);
        }
    }

    public class MessagePackDataLoader : ConfigDataLoader
    {
        protected override void OnLoad(byte[] buffer)
        {
        }
    }
}
