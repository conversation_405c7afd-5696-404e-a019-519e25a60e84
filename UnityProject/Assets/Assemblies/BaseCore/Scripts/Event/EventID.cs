
namespace Phoenix.Core
{
    public enum EventID
    {
        None,
        
        ConfigData<PERSON>eloaded,
        Frame_Tick,



        WorldEnter_StartRequest,
        BattleStart_StartRequest,
        BattleStart_OnUpdateCameraAspect,
        BattleStart_BattleInitEnd,

        BattlePrepare_DragBegin,
        BattlePrepare_Draging,
        BattlePrepare_DragEnd,
        BattlePrepare_UpdateFormationEntityList,
        BattlePrepare_Start,
        BattlePrepare_Finish,
        BattlePrepare_EntityViewAdd,
        BattlePrepare_EntityViewRemove,
        BattlePrepare_EntityViewExchange,
        BattlePrepare_EntityViewMove,
        BattlePrepare_BattleFormationChanged,
        BattlePrepare_CharacterItemCancelSelected,


        BattlePerformance_BattleStartWinConditionShow,
        BattlePerformance_BattleStart,
        BattlePerformance_TurnStart,
        BattlePerformance_CampStart,
        BattlePerformance_TeamStart,
        BattlePerformance_SceneSkillAnnounce,
        BattlePerformance_CombatSkillAnnounce,
        BattlePerformance_ContinuousSkillAnnounce,
        BattlePerformance_AdditionalSkillAnnounce,
        BattlePerformance_SkillTriggerAnnounce,
        BattlePerformance_BuffTriggerAnnounce,
        BattlePerformance_TerrainTriggerAnnounce,
        BattlePerformance_BuffTagAnnounce,
        BattlePerformance_AssistGuardBegin,
        BattlePerformance_AssistGuardEnd,
        BattlePerformance_StepStart_SelectEntityEnd,
        BattlePerformance_SkillPreAnnounceBegin,
        BattlePerformance_SkillPreAnnounceEnd,
        BattlePerformance_RefreshBattleAfterSkip,
        BattlePerformance_CombatBegin,


        BattleMode_EntityActionEnd,
        BattleMode_GearSelected,
        BattleMode_GearCancelSelected,
        BattleMode_EntitySelected,
        BattleMode_DeactiveEntitySelected,
        BattleMode_ActiveEntitySelected,
        BattleMode_EntityCancelSelected,
        BattleMode_EntitySkillSelected,
        BattleMode_EntitySkillTargetSelect,
        BattleMode_EntityCastSkillConfirm,
        BattleMode_EntityCastSkillConfirm2, // 护卫技能确认
        BattleMode_ShowActorDetailInfo,
        BattleMode_ShowOrHideRecommendCastSkillBtn,
        BattleMode_SelectWrongSkillTarget,

        BattleMode_UIElementSubscribe,
        BattleMode_UIElementUnsubscribe,
        BattleMode_BattleUIStateActive,
        BattleMode_BattleUIStateDeactive,
        BattleMode_ShowBattleCombatPrepareUI,
        BattleMode_HideBattleCombatPrepareUI,
        BattleMode_ResetBattleUIState,

        BattleCommand_RefreshCommandList,

        BattleControl_BlockPerformance,
        BattleControl_BlockPerformanceAndWait,
        BattleControl_WaitAll,

        PrepareMode_EntityGearSelected,
        PrepareMode_EntityGearCancelSelected,
        PrepareMode_EntitySelected,
        PrepareMode_UI_EntitySelected,

        Entity_ActionChanceUpdate,
        EntityView_OnMoveSectionEnd,
        EntityView_Created,
        EntityView_Destroyed,
        EntityGhostViewAddOrRemove,
        Entity_CurHp_Changed,
        Entity_CurAnger_Changed,
        Entity_CurLifeCount_Changed,
        Entity_Attibute_Changed,
        Entity_Buff_Updated,
        Entity_Buff_Added,
        Entity_Buff_Removed,
        Entity_Command_Added,
        Entity_Command_Removed,
        Entity_Team_Changed,
        Entity_Action_End,
        Entity_BlockHitPoint_Changed,
        Entity_SignRelationshipHide,
        Entity_SignRelationshipUpdate,
        Entity_ShowBloodChangePreview,
        Entity_HideBloodChangePreview,
        Entity_BloodMaskChanged,
        Entity_BloodRecommendUpdate,
        Entity_DialogBubbleUpdate,

        Entity_SelectCommand_InOrOut,
        Entity_SelectCommand_SelectGrid,
        Entity_SelectCommand_SelectEntity,
        Entity_SelectCommand_CancelTarget,
        Entity_SelectCommand_ShowOrHideCancel,
        Entity_SelectCommand_UpdateConfirmView,
        Entity_AutoStateSkillPerformance_InOrOut,


        BattleTeam_SharedEnergy_Changed,
        BattleTeam_DecisionId_Changed,
        BattleTeam_AutoState_Changed,
        BattleTeam_DecisionMark_Added,
        BattleTeam_DecisionMark_Removed,

        AutoBattleMode_SelectEntityDetail,

        BattleTextFloat,
        BattleTextFloatPro,
        BattleDamageFloat,
        BattleHealFloat,
        BattleCombatSkillStatgeChanged,


        BattleStage_SetActive,
        BattleResult_ShowEnd,

        BattleRetract_Apply,
        BattleRetract_Cancel,

        EventOnCameraFollowBegin,
        EventOnCameraFollowEnd,
        EventOnInputClick,
        EventOnInputDown,
        EventOnInputDrag,
        EventOnInputUp,
        EventOnInputZoomInOut,


        WorldUI_ShowToastReward,

        WorldNavagentMoveBegin,
        WorldNavagentMoving,
        WorldNavagentMoveEnd,
        WorldNavagentSteering,
        WorldCameraRotateControl,
        WorldJoystickMoveBegin,
        WorldJoystickMoving,
        WorldJoystickMoveEnd,
        WorldJoystickDebugStateChange,
        WorldKeyboardMoveBegin,
        WorldKeyboardMoving,
        WorldKeyboardMoveEnd,
        WorldEntityInteractStart,
        WorldEntityInteractStop,
        SetPlayerDestination,

        ShowWorldEntityInteractionBtn,
        HideWorldEntityInteractionBtn,

        WorldEntityInteractionAdd,
        WorldEntityInteractionRemove,
        WorldPerformanceActionBegin,
        WorldPerformanceActionEnd,
        WorldUIShowOrHide,
        WorldShowSubmitItemUI,
        WorldEntityHudFunctionStateUpdate,
        WorldPlayerQuestTrackStateUpdate,
        WorldEntityTriggerNodeGraph,


        WorldEntityViewAdd,
        WorldEntityViewRemove,
        WorldEntityViewHudAdd,
        WorldEntityViewHudRemove,
        WorldEntityViewHudStateUpdate,
        WorldEntityViewEmojiHudShow,
        WorldEntityViewStatusChange,

        WorldQuestGroupAccept,
        WorldQuestGroupCompleted,
        WorldQuestAccept,
        WorldQuestCompleted,
        WorldQuestStatusChange,

        WorldQuestDataInitFinished,
        ExchangeItemFinished,
        UpdateFavorCount,
        ShowOrHideFavorDialogueMask,

        GuideStepFinished,
        GuideStepGroupFinished,
        GuideStepGroupSendComplete,
        GuideObjectRegister,
        GuideObjectUnregister,
        GuideObjectActiveChanged,
        GuideTipShow,
        GuideTipHide,
        GuideHintShow,
        GuideHintShowByGrid,
        GuideHintHide,
        GuideTouchMaskShowByRect,
        GuideTouchMaskShowByVertexList,
        GuideTouchMaskShowByBounds,
        GuideTouchMaskHide,
        GuideCenterBtnClicked,
        GuideCenterBtnClickedUp,
        GuideCenterBtnShow,
        GuideDragActorToGrid,
        GuideDragActorToGridEnd,
        GuideGraphicShow,
        GuideGraphicShowEnd,
        GuideHintAllHide,
        GuideSendEnd,
        GuideHintShowNextClick,
        GuideTipShowNextClick,
        GuideShowSkipBtn,
        GuideHideAll,
#if !RELEASE
        GuideAddLog,
#endif

        Timeline_Start,
        Timeline_Stop,
        Timeline_Skip,
        Timeline_DialogStart,
        Timeline_DialogResume,
        Timeline_DialogPause,
        Timeline_DialogStop,
        Timeline_ShowOrHideAllWorldEntity,


        SceneOverlay_Changed,

        GrassManager_Trampler_Changed,

        Login_Success,

        DisConnectWithServer,





        HakoJoystickMoveBegin,
        HakoJoystickMoving,
        HakoJoystickMoveEnd,
        HakoInputMoveBegin,
        HakoInputMoving,
        HakoInputMoveEnd,

        HakoEntityViewAdd,
        HakoEntityViewRemove,
        HakoCameraTouchTap,

        HakoStoryPerform,
        HakoSceneTimelinePlay,

        HakoniwaInteractable2Entity,
        HakoniwaUISwitchInteractEntity,
    }
}
