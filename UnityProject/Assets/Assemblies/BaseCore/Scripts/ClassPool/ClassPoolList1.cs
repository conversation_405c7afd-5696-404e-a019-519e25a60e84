using System;
using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Core
{
    public class ClassPoolList<T> : ClassPoolObj, IList<T>
    {
        private List<T> m_list = new List<T>();

        public bool needReleaseAllWhenDispose;

        public T this[int index] { get { return m_list[index]; } set { m_list[index] = value; } }

        public int Count => m_list.Count;

        public bool IsReadOnly
        {
            get { return false; }
        }

        public void Add(T item)
        {
            m_list.Add(item);
        }

        public void Clear()
        {
            m_list.Clear();
        }

        public bool Contains(T item)
        {
            return m_list.Contains(item);
        }

        public void CopyTo(T[] array, int arrayIndex)
        {
            m_list.CopyTo(array, arrayIndex);
        }

        public IEnumerator<T> GetEnumerator()
        {
            return m_list.GetEnumerator();
        }

        public int IndexOf(T item)
        {
            return m_list.IndexOf(item);
        }

        public void Insert(int index, T item)
        {
            m_list.Insert(index, item);
        }

        public bool Remove(T item)
        {
            return m_list.Remove(item);
        }

        public void RemoveAt(int index)
        {
            m_list.RemoveAt(index);
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public override void OnRelease()
        {
            if (needReleaseAllWhenDispose)
            {
                m_list.ReleaseAll();
            }
            else
            {
                m_list.Clear();
            }
            needReleaseAllWhenDispose = false;
        }
    }
}
