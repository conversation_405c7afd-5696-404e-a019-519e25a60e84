using System;
using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Core
{
    public class SequenceProcess : CollectionProcess<ProcessBase>
    {
        private int m_curIndex;

        public ProcessBase curProcess
        {
            get { return m_subProcessList.GetValueSafely(m_curIndex); }
        }

        public override void OnRelease()
        {
            m_curIndex = default;
            base.OnRelease();
        }

        public void HandleProcess()
        {
            while (m_curIndex < m_subProcessList.Count)
            {
                ProcessBase process = m_subProcessList.GetValueSafely(m_curIndex);
                if (process == null)
                {
                    continue;
                }
                if (StartSubProcessAndCheckRunning(process))
                {
                    break;
                }
                process.UnInit();
                m_curIndex++;
            }
            if (curProcess == null)
            {
                End();
            }
        }

        protected override void OnStart()
        {
            HandleProcess();
        }

        protected override void OnPause()
        {
            if (curProcess != null)
            {
                curProcess.Pause();
            }
        }

        protected override void OnResume()
        {
            if (curProcess != null)
            {
                curProcess.Resume();
            }
        }

        protected override void OnTick(TimeSlice timeSlice)
        {
            if (curProcess != null)
            {
                curProcess.Tick(timeSlice);
            }
        }

        protected override void OnSubProcessEnd(ProcessBase process)
        {
            int index = m_subProcessList.IndexOf(process);
            if (m_curIndex != index)
            {
                DebugUtility.LogError("Cannot End Process which is not Current Process. Please Remove it or Skip it.");
                return;
            }
            process.UnInit();
            m_curIndex++;
            HandleProcess();
        }
    }
}
