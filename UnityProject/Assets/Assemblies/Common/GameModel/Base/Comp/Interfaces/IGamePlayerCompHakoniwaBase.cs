// Copyright (c) Phoenix All Rights Reserved.

using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Base
{
    public interface IGamePlayerCompHakoniwaBase
    {
        /// <summary>
        /// 能否创建箱庭
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <param name="locationId"></param>
        /// <returns></returns>
        bool CanEnterHakoniwa(int hakoniwaId, int locationId);

        /// <summary>
        /// 创建箱庭成功回调
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <param name="locationId"></param>
        /// <param name="firstEnterHakoniwaInfo"></param>
        void OnEnterHakoniwaSuccess(int hakoniwaId, int locationId, ExploringHakoniwaInfo? firstEnterHakoniwaInfo);

        /// <summary>
        /// 获取指定探索的箱庭
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <returns></returns>
        ExploringHakoniwaInfo? GetExploringHakoniwaInfo(int hakoniwaId);

        /// <summary>
        /// 获取所有正在探索的Hakoniwa信息列表
        /// </summary>
        /// <returns></returns>
        IEnumerable<ExploringHakoniwaInfo> GetExploringHakoniwaList();

        /// <summary>
        /// 获取所有已完成的Hakoniwa信息列表
        /// </summary>
        /// <returns></returns>
        IEnumerable<CompletedHakoniwaInfo> GetCompletedHakoniwaList();

        /// <summary>
        /// 玩家当前是否在箱庭中
        /// </summary>
        /// <returns></returns>
        bool IsPlayerInHakoniwa();
    } 
}