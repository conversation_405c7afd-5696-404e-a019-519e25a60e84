// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Base
{
    public abstract class GamePlayerCompWorldBase : GamePlayerCompBase
    {
        public override string Name => CompNames.World;

        /// <summary>
        /// 添加已完成的故事ID
        /// </summary>
        /// <param name="storyId">故事ID</param>
        public void AddCompletedStoryId(int storyId)
        {
            if (!m_baseData.CompletedStoryIds.Contains(storyId))
            {
                m_baseData.CompletedStoryIds.Add(storyId);
                m_baseData.MarkDirty();
            }
        }

        /// <summary>
        /// 检查故事是否已完成
        /// </summary>
        /// <param name="storyId">故事ID</param>
        /// <returns>是否已完成</returns>
        public bool IsStoryCompleted(int storyId)
        {
            return m_baseData.CompletedStoryIds.Contains(storyId);
        }

        /// <summary>
        /// 获取所有已完成的故事ID
        /// </summary>
        /// <returns>已完成的故事ID列表</returns>
        public List<int> GetCompletedStoryIds()
        {
            return m_baseData.CompletedStoryIds;
        }

        protected PlayerCompDataWorldInfo m_baseData = null!;
    }
}
