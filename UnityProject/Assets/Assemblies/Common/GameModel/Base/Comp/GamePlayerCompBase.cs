using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Base
{
    public abstract class GamePlayerCompBase : IGamePlayerComp
    {
        public abstract string Name { get; }

        internal void SetOwner(GamePlayerBase owner)
        {
            Owner = owner;
        }

        public bool IsInited()
        {
            return m_isInited;
        }

        public virtual bool Init()
        {
            return true;
        }

        void IGamePlayerComp.SetOwner(GamePlayerBase gamePlayerBase) => SetOwner(gamePlayerBase);

        protected bool m_isInited = false;

        protected GamePlayerBase Owner = null!;
    }
}
