// Copyright (c) Phoenix All Rights Reserved.

using System.Collections.Generic;
using System.Linq;
using Phoenix.CommonDefine;
using Phoenix.ConfigData;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Base
{
    public abstract partial class GamePlayerCompHakoniwaBase : GamePlayerCompBase
    {
        public GamePlayerCompHakoniwaBase()
        {
        }

        public override bool Init()
        {
            if (!base.Init())
            {
                return false;
            }

            m_compBattle = Owner.GetComp<GamePlayerCompBattleBase>(CompNames.Battle);
            return true;
        }

        /// <summary>
        /// 能否进入箱庭
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <param name="wayPointId"></param>
        /// <param name="errCode"></param>
        /// <returns></returns>
        public bool CanEnterHakoniwa(int hakoniwaId, int wayPointId, out ErrCode errCode)
        {
            errCode = ErrCode.ErrCodeOk;
            var conf = ConfigDataManager.instance.GetPhoenixHakoniwa(hakoniwaId);
            if (conf == null) //配置找不到
            {
                errCode = ErrCode.ErrCodeInvalidConfigId;
                return false;
            }

            var info = GetExploringHakoniwaInfo(hakoniwaId);
            if (info == null) //首次进入
            {
            }
            else
            {
                if (!info.UnlockWayPointIdIds.Contains(wayPointId))//未解锁的地标
                {
                    errCode = ErrCode.ErrCodeHakoniwaLocationIdUnlock;
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 进入箱庭成功回调
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <param name="waypointId"></param>
        /// <param name="info"></param>
        public void OnEnterHakoniwaSuccess(int hakoniwaId, int waypointId, ExploringHakoniwaInfo info)
        {
            if (GetExploringHakoniwaInfo(hakoniwaId) == null) //首次进入箱庭
            {
                m_baseData.ExploringHakoniwaList.Add(info);
            }
            var waypointConf = ConfigDataManager.instance.GetHakoniwaWaypoint(waypointId);
            info.SceneId = waypointConf.HakoSceneId;
            info.PosInfo = waypointConf.Location;
            m_baseData.CurrEnterHakoniwaId = info.HakoniwaId;
            m_baseData.MarkDirty();
        }

        public void OnCompleteHakoniwaDialog(int hakoniwaId, int dialogId)
        {

        }

        /// <summary>
        /// 获取指定探索的箱庭
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <returns></returns>
        public ExploringHakoniwaInfo? GetExploringHakoniwaInfo(int hakoniwaId)
        {
            return m_baseData.ExploringHakoniwaList.FirstOrDefault(info => info.HakoniwaId == hakoniwaId);
        }

        /// <summary>
        /// 获取所有正在探索的Hakoniwa信息列表
        /// </summary>
        /// <returns></returns>
        public IEnumerable<ExploringHakoniwaInfo> GetExploringHakoniwaList()
        {
            return m_baseData.ExploringHakoniwaList;
        }

        /// <summary>
        /// 玩家当前是否在箱庭中
        /// </summary>
        /// <returns></returns>
        public bool IsPlayerInHakoniwa()
        {
            return m_baseData.CurrEnterHakoniwaId > 0;
        }

        /// <summary>
        /// 获取当前正在探索的箱庭ID
        /// </summary>
        /// <returns></returns>
        public int GetCurrEnterHakoniwaId()
        {
            return m_baseData.CurrEnterHakoniwaId;
        }

        public ExploringHakoniwaInfo? GetCurrExploringHakoniwaInfo()
        {
            return GetExploringHakoniwaInfo(m_baseData.CurrEnterHakoniwaId);
        }

        public bool CanStartHakoniwaBattle(int hakoniwaId, int levelId, HakoniwaBattleMonsterInfo? monsterInfo, out ErrCode errCode)
        {
            errCode = ErrCode.ErrCodeOk;
            if (!IsPlayerInHakoniwa() || GetCurrEnterHakoniwaId() != hakoniwaId)
            {
                errCode = ErrCode.ErrCodeHakoniwaPlayerNotInHakoniwa;
                return false;
            }

            var levelConf = ConfigDataManager.instance.GetHakoniwaLevel(levelId);
            if (levelConf == null)
            {
                errCode = ErrCode.ErrCodeInvalidConfigId;
                return false;
            }
            var info = GetExploringHakoniwaInfo(hakoniwaId)!;
            if (info.CompletedLevelIds.Contains(levelId))
            {
                errCode = ErrCode.ErrCodeHakoniwaLevelCompleted;
                return false;
            }

            if (levelConf.Type == PhoenixHakoniwaLevelType.Monster) //怪物关卡
            {
                if (monsterInfo == null)
                {
                    errCode = ErrCode.ErrCodeInvalidClientParam;
                    return false;
                }
                int monsterId = monsterInfo.MonsterId;
                int hateGroupId = monsterInfo.HateGroupId;
                if(!monsterInfo.CalcLevelAndMonsters(out int calcLevelId, out var monsters))
                {
                    errCode = ErrCode.ErrCodeInvalidClientParam;
                    return false;
                }

                if (calcLevelId != levelId)
                {
                    errCode = ErrCode.ErrCodeInvalidClientParam;
                    return false;
                }

                foreach (var connMonster in monsters)
                {
                    if (info.IsMonsterDead(connMonster.Id)) //怪物已死亡
                    {
                        errCode = ErrCode.ErrCodeHakoniwaMonsterDead;
                        return false;
                    }
                }
            }
            return true;
        }

        public bool CanFinishHakoniwaBattle(int hakoniwaId, int levelId, HakoniwaBattleMonsterInfo? monsterInfo, out ErrCode errCode)
        {
            errCode = ErrCode.ErrCodeOk;
            if (!IsPlayerInHakoniwa() || GetCurrEnterHakoniwaId() != hakoniwaId)
            {
                errCode = ErrCode.ErrCodeHakoniwaPlayerNotInHakoniwa;
                return false;
            }
            var levelConf = ConfigDataManager.instance.GetHakoniwaLevel(levelId);
            if (levelConf == null)
            {
                errCode = ErrCode.ErrCodeInvalidConfigId;
                return false;
            }
            var processInfo = BattleProcessInfo.Create<HakoniwaBattleProcessInfo>((int)BattleType.Hakoniwa,
                levelConf.BattleId, 0);
            processInfo.HakoniwaId = hakoniwaId;
            processInfo.LevelId = levelId;
            processInfo.MonsterInfo = monsterInfo;
            if (!m_compBattle.CheckBattleProcess(processInfo, out errCode))
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 进入箱庭战斗
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <param name="battleProcessInfo"></param>
        public void OnStartHakoniwaBattle(int hakoniwaId, HakoniwaBattleProcessInfo battleProcessInfo)
        {
            //设置战斗信息
            m_compBattle.SetBattleProcessInfo(battleProcessInfo);
        }

        public void OnFinishHakoniwaBattle(int hakoniwaId, int levelId, List<HakoniwaEntityMonster> killedMonsters)
        {
            var levelConf = ConfigDataManager.instance.GetHakoniwaLevel(levelId)!;
            var info = GetExploringHakoniwaInfo(hakoniwaId)!;
            if (levelConf.Type == PhoenixHakoniwaLevelType.Monster)
            {
                foreach (var killedMonster in killedMonsters)
                {
                    info.Entities.Add(killedMonster);
                }
            }
            //重置战斗信息
            m_compBattle.ResetBattleProcessInfo();
        }

        public void OnHakoniwaQuestCompleted(ExploringHakoniwaInfo hakoniwaInfo, HakoniwaQuestInfo questInfo, List<HakoniwaQuestInfo> nextQuestInfos)
        {
            var questGraphConf = ConfigDataManager.instance.GetPhoenixHakoniwa(hakoniwaInfo.HakoniwaId)!.QuestGraphConfig;
            var questConf = questGraphConf.QuestGraph.GetQuestNodeTemplate(questInfo.QuestId);
            questInfo.QuestState = (int)QuestStatus.Completed;
            hakoniwaInfo.QuestInfos.Remove(questInfo);
            hakoniwaInfo.CompletedQuestIds.Add(questInfo.QuestId);
            if (nextQuestInfos.Any())
            {
                hakoniwaInfo.QuestInfos.AddRange(nextQuestInfos);
            }

            //执行Action
            var execActions = questConf.GetNextQuestNode<ConfigDataQuestNodeAction>();
            foreach (var actionList in execActions)
            {
                foreach (var questAction in actionList.Actions)
                {
                    if(questAction is ConfigDataQuestActionHakoniwaWaypointActive waypointActive) //增加解锁点
                    {
                        if (hakoniwaInfo.UnlockWayPointIdIds.Contains(waypointActive.HakoniwaWaypointId))
                        {
                            continue;
                        }
                        hakoniwaInfo.UnlockWayPointIdIds.Add(waypointActive.HakoniwaWaypointId);
                    }
                }
            }
            m_baseData.MarkDirty();
        }

        /// <summary>
        /// 箱庭完成
        /// </summary>
        /// <param name="hakoniwaInfo"></param>
        protected void OnHakoniwaCompleted(ExploringHakoniwaInfo hakoniwaInfo)
        {
            m_baseData.CurrEnterHakoniwaId = 0;
            m_baseData.ExploringHakoniwaList.Remove(hakoniwaInfo);
            m_baseData.CompletedHakoniwaIds.Add(hakoniwaInfo.HakoniwaId);
            m_baseData.MarkDirty();
        }

        public void OnGiveUpHakoniwa(int hakoniwaId)
        {
            m_baseData.CurrEnterHakoniwaId = 0;
            var info = GetExploringHakoniwaInfo(hakoniwaId)!;
            m_baseData.ExploringHakoniwaList.Remove(info);
            m_baseData.MarkDirty();
        }

        public bool CanOpenHakoniwaTreasureBox(int hakoniwaId, int treasureBoxId, out ErrCode errCode)
        {
            errCode = ErrCode.ErrCodeOk;
            if (!IsPlayerInHakoniwa() || GetCurrEnterHakoniwaId() != hakoniwaId)
            {
                errCode = ErrCode.ErrCodeHakoniwaPlayerNotInHakoniwa;
                return false;
            }
            if (m_baseData.OpenTreasureBoxIds.Contains(treasureBoxId))
            {
                errCode = ErrCode.ErrCodeHakoniwaTreasureBoxOpened;
                return false;
            }
            return true;
        }

        public void OnOpenHakoniwaTreasureBox(int hakoniwaId, int treasureBoxId)
        {
            m_baseData.OpenTreasureBoxIds.Add(treasureBoxId);
            m_baseData.MarkDirty();
        }

        public bool CanSyncHakoniwaPos(int hakoniwaId, int sceneId, PosInfo posInfo, out ErrCode errCode)
        {
            errCode = ErrCode.ErrCodeOk;
            if (!IsPlayerInHakoniwa() || GetCurrEnterHakoniwaId() != hakoniwaId)
            {
                errCode = ErrCode.ErrCodeHakoniwaPlayerNotInHakoniwa;
                return false;
            }

            var sceneConf = ConfigDataManager.instance.GetHakoniwaScene(sceneId);
            if (sceneConf == null)
            {
                errCode = ErrCode.ErrCodeInvalidConfigId;
                return false;
            }

            if (!posInfo.IsValid())
            {
                errCode = ErrCode.ErrCodeInvalidClientParam;
                return false;
            }

            return true;
        }

        public void OnHakoniwaPosSync(int hakoniwaId, int sceneId, PosInfo posInfo)
        {
            var info = GetExploringHakoniwaInfo(hakoniwaId)!;
            info.SceneId = sceneId;
            info.PosInfo = posInfo;
            m_baseData.MarkDirty();
        }

        public bool CanHakoniwaMonsterOneHit(int hakoniwaId, int monsterId, out ErrCode errCode)
        {
            errCode = ErrCode.ErrCodeOk;
            if (!IsPlayerInHakoniwa() || GetCurrEnterHakoniwaId() != hakoniwaId)
            {
                errCode = ErrCode.ErrCodeHakoniwaPlayerNotInHakoniwa;
                return false;
            }

            var info = GetExploringHakoniwaInfo(hakoniwaId)!;
            if (info.IsMonsterDead(monsterId))
            {
                errCode = ErrCode.ErrCodeHakoniwaMonsterDead;
                return false;
            }

            var monsterConf = ConfigDataManager.instance.GetHakoniwaMonster(monsterId);
            if (monsterConf == null)
            {
                errCode = ErrCode.ErrCodeInvalidConfigId;
                return false;
            }

            if (!monsterConf.OneHitKill || !monsterConf.IsWake) //只有弱联动并且支持一击必杀的怪物才能一击必杀
            {
                errCode = ErrCode.ErrCodeHakoniwaMonsterNotOneHit;
                return false;
            }
            return true;
        }

        public void OnHakoniwaMonsterOneHit(int hakoniwaId, int monsterId)
        {
            var info = GetExploringHakoniwaInfo(hakoniwaId)!;
            var monsterInfo = info.GetEntity<HakoniwaEntityMonster>(monsterId);
            if (monsterInfo == null)
            {
                info.Entities.Add(new HakoniwaEntityMonster()
                {
                    EntityId = monsterId,
                    IsDead = true,
                });
            }
            else
            {
                monsterInfo.IsDead = true;
            }
            m_baseData.MarkDirty();
        }

        protected GamePlayerCompBattleBase m_compBattle = null!;

        protected PlayerCompDataHakoniwaInfo m_baseData = new();

        public override string Name => CompNames.Hakoniwa;
    }
}
