// Copyright (c) Phoenix All Rights Reserved.

using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Client
{
    public sealed class GamePlayerCompBasic : GamePlayerCompBasicBase, IClientDataComp
    {
        public void InitFromServerData(MsgPackStructBase ntf)
        {
            var basicInfoNtf = ntf as PlayerBasicCompDataNtf;
            m_baseData = basicInfoNtf!.BasicInfo;
            // others
        }
    }
}
