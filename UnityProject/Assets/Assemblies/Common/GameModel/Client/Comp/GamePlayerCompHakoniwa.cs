// Copyright (c) Phoenix All Rights Reserved.

using Phoenix.CommonDefine;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Client
{
    public sealed class GamePlayerCompHakoniwa : GamePlayerCompHakoniwaBase, IClientDataComp
    {
        public void InitFromServerData(MsgPackStructBase ntf)
        {
            if (ntf is PlayerHakoniwaCompDataNtf hakoniwaCompDataNtf)
            {
                m_baseData = hakoniwaCompDataNtf.HakoniwaInfo;
            }
        }

        public void OnHakoniwaEnterAck(HakoniwaEnterAck ack)
        {
            if (ack.ErrCode != (int)ErrCode.ErrCodeOk)
            {
                return;
            }
            var info = GetExploringHakoniwaInfo(ack.HakoniwaId);
            if (ack.FirstEnterHakoniwaInfo != null)
            {
                info = ack.FirstEnterHakoniwaInfo;
            }
            // 处理进入箱庭的响应
            OnEnterHakoniwaSuccess(ack.HakoniwaId, ack.EnterWayPointId, info!);
        }

        /// <summary>
        /// 任务进度更新
        /// </summary>
        /// <param name="ntf"></param>
        public void OnHakoniwaQuestCondChangeNtf(HakoniwaQuestCondChangeNtf ntf)
        {
            var info = GetExploringHakoniwaInfo(ntf.HakoniwaId)!;
            var questInfo = info.QuestInfos.Find(quest => quest.QuestId == ntf.QuestId)!;
            //更新任务进度
            questInfo.QuestCondInfos[ntf.CondInfo.Index] = ntf.CondInfo;
        }

        /// <summary>
        /// 任务状态变更
        /// </summary>
        /// <param name="ntf"></param>
        public void OnHakoniwaQuestStateChangeNtf(HakoniwaQuestStateChangeNtf ntf)
        {
            if(ntf.QuestState == (int)QuestStatus.Completed) //任务完成
            {
                var hakoniwaInfo = GetExploringHakoniwaInfo(ntf.HakoniwaId)!;
                var questInfo = hakoniwaInfo.QuestInfos.Find(quest => quest.QuestId == ntf.QuestId)!;
                OnHakoniwaQuestCompleted(hakoniwaInfo, questInfo, ntf.NextQuestInfos);
            }
        }

        /// <summary>
        /// 箱庭完成
        /// </summary>
        /// <param name="ntf"></param>
        public void OnHakoniwaFinishNtf(HakoniwaFinishNtf ntf)
        {
            var hakoniwaInfo = GetExploringHakoniwaInfo(ntf.HakoniwaId)!;
            OnHakoniwaCompleted(hakoniwaInfo);
        }

        public void OnHakoniwaBattleFinishAck(HakoniwaBattleFinishAck ack)
        {
            if (ack.ErrCode != (int)ErrCode.ErrCodeOk)
            {
                return;
            }
            OnFinishHakoniwaBattle(ack.HakoniwaId, ack.LevelId, ack.KilledMonsters);
        }
    }
}
