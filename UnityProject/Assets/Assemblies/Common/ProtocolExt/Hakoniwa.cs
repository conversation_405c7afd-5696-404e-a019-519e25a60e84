// Copyright (c) Phoenix All Rights Reserved.

using System.Collections.Generic;
using System.Linq;
using Phoenix.ConfigData;

namespace Phoenix.MsgPackLogic.Protocol
{
    public partial class ExploringHakoniwaInfo
    {
        /// <summary>
        /// 箱庭怪物是否已经死亡
        /// </summary>
        /// <param name="monsterId"></param>
        /// <returns></returns>
        public bool IsMonsterDead(int monsterId)
        {
            return Entities.OfType<HakoniwaEntityMonster>().Any(e => e.EntityId == monsterId && e.IsDead);
        }

        public T GetEntity<T>(int entityId) where T : HakoniwaEntity
        {
            return Entities.OfType<T>().FirstOrDefault(e => e.EntityId == entityId);
        }
    }

    public partial class PosInfo
    {
        public static implicit operator PosInfo(SceneLocationConfigData posInfo)
        {
            return new PosInfo()
            {
                X = posInfo.X,
                Y = posInfo.Y,
                Z = posInfo.Z,
            };
        }

        public bool IsValid()
        {
            if(X < 0 || Y < 0 || Z < 0)
            {
                return false;
            }
            return true;
        }
    }

    public partial class HakoniwaBattleMonsterInfo
    {
        /// <summary>
        /// 计算关联的关卡ID和关联的所有怪物
        /// </summary>
        /// <param name="levelId"></param>
        /// <param name="monsters"></param>
        /// <returns></returns>
        public bool CalcLevelAndMonsters(out int levelId, out List<HakoniwaMonsterConfigData> monsters)
        {
            levelId = 0;
            monsters = new();
            var monsterConf = ConfigDataManager.instance.GetHakoniwaMonster(MonsterId);
            if (monsterConf == null)
            {
                return false;
            }

            if(monsterConf.LevelId > 0) //怪物1:1关卡
            {
                levelId = monsterConf.LevelId;
                monsters.Add(monsterConf);
            }
            else //怪物仇恨组
            {
                var hateGroupConf = ConfigDataManager.instance.GetMonsterHateGroup(HateGroupId);
                if (hateGroupConf == null)
                {
                    return false;
                }
                levelId = hateGroupConf.LevelId;
                foreach (var v in hateGroupConf.MonsterList)
                {
                    monsters.Add(v);
                }
            }
            return true;
        }
    }
}
