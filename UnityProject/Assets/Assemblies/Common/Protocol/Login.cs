// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class LoginByAuthTokenReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.LoginByAuthTokenReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public string AuthToken { get; set; }
       [Key(4)]
       public string ClientVersion { get; set; }
       [Key(5)]
       public string ClientDeviceId { get; set; }
       [Key(6)]
       public string Localization { get; set; }
       [Key(7)]
       public string A<PERSON><PERSON>ey { get; set; }
       [Key(8)]
       public int BornChannelId { get; set; }
       [Key(9)]
       public int CurrChannelId { get; set; }
    }
    
    [MessagePackObject]
    public partial class LoginByAuthTokenAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.LoginByAuthTokenAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public string SessionToken { get; set; }
    }
    
    [MessagePackObject]
    public partial class LoginBySessionTokenReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.LoginBySessionTokenReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public string SessionToken { get; set; }
       [Key(4)]
       public string ClientVersion { get; set; }
       [Key(5)]
       public string Localization { get; set; }
       [Key(6)]
       public int BornChannelId { get; set; }
       [Key(7)]
       public int CurrChannelId { get; set; }
    }
    
    [MessagePackObject]
    public partial class LoginBySessionTokenAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.LoginBySessionTokenAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public string GameService { get; set; }
       [Key(5)]
       public byte XORCode { get; set; }
    }
    
    [MessagePackObject]
    public partial class ServerDisconnectNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerDisconnectNtf;
       }
       [Key(2)]
       public int ReasonCode { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_Logout_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Logout_Req;
       }
    }
    
}
