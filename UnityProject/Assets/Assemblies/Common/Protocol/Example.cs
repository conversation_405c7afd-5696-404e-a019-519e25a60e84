// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class MsgPack_Example_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Example_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long UidTarget { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_Example_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Example_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_ExampleData_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_ExampleData_Ntf;
       }
       [Key(2)]
       public MsgPack_DB_ExampleData ExampleData { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class MsgPack_DB_ExampleData : DBCacheObject
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("SByteValue")]
       #endif
       public sbyte SByteValue { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("ByteValue")]
       #endif
       public byte ByteValue { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("Int16Value")]
       #endif
       public short Int16Value { get; set; }
       [Key(3)]
       #if PHOENIX_SERVER
       [BsonElement("UInt16Value")]
       #endif
       public ushort UInt16Value { get; set; }
       [Key(4)]
       #if PHOENIX_SERVER
       [BsonElement("Int32Value")]
       #endif
       public int Int32Value { get; set; }
       [Key(5)]
       #if PHOENIX_SERVER
       [BsonElement("UInt32Value")]
       #endif
       public uint UInt32Value { get; set; }
       [Key(6)]
       #if PHOENIX_SERVER
       [BsonElement("Int64Value")]
       #endif
       public long Int64Value { get; set; }
       [Key(7)]
       #if PHOENIX_SERVER
       [BsonElement("UInt64Value")]
       #endif
       public ulong UInt64Value { get; set; }
       [Key(8)]
       #if PHOENIX_SERVER
       [BsonElement("FloatValue")]
       #endif
       public float FloatValue { get; set; }
       [Key(9)]
       #if PHOENIX_SERVER
       [BsonElement("SingleValue")]
       #endif
       public float SingleValue { get; set; }
       [Key(10)]
       #if PHOENIX_SERVER
       [BsonElement("DoubleValue")]
       #endif
       public double DoubleValue { get; set; }
       [Key(11)]
       #if PHOENIX_SERVER
       [BsonElement("StringValue")]
       #endif
       public string StringValue { get; set; }
       [Key(12)]
       #if PHOENIX_SERVER
       [BsonElement("BooleanValue")]
       #endif
       public bool BooleanValue { get; set; }
       [Key(13)]
       #if PHOENIX_SERVER
       [BsonElement("ListExample")]
       #endif
       public List<ulong> ListExample { get; set; }
       [Key(14)]
       #if PHOENIX_SERVER
       [BsonElement("DictExample")]
       [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
       #endif
       public Dictionary<ulong, double> DictExample { get; set; }
       [Key(15)]
       #if PHOENIX_SERVER
       [BsonElement("ListItemExample")]
       #endif
       public List<MsgPack_DB_Item> ListItemExample { get; set; }
       [Key(16)]
       #if PHOENIX_SERVER
       [BsonElement("DictItemExample")]
       [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
       #endif
       public Dictionary<string, MsgPack_DB_Item> DictItemExample { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_ExampleExternal_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_ExampleExternal_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public bool IsExternal { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_ExampleExternal_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_ExampleExternal_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public string strExternalInfo { get; set; }
    }
    
}
