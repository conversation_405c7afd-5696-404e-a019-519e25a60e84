// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif

namespace Phoenix.MsgPackLogic.Protocol
{
    public enum EResultType : SByte
    {
        SUCCESS = 0,
        FAIL = 1,
        ERROR = 2,
        TIMEOUT = 3,
        WARNING = 4,
        EXCEPTION = 5,
    }

    public enum EMsgPackOpType
    {
        // Do nothing
        OP_NULL = 0,
        // Excute default protocol logic
        OP_DEFAULT,
        // Call Client <-> Server Func
        OP_FUNC,
        // Clear all elements
        OP_CLEAR,
        // Erase key (for array, the key is index)
        OP_ERASE,
        // Set key-value
        OP_UPDATE,
        // Array operation
        OP_A_APPEND,
        OP_A_INSERT,
        OP_BITSET_SET,
        OP_BITSET_RESET,
        OP_SET_ADD,
        OP_SET_REMOVE,



        OP_END
    }

    public enum EProtoCode
    {
        // REQ Client to Server
        // ACK Server to Client
        // NTF Server to Client
        // PROTO Server to Server


        // 0 ~ 49
        EXAMPLE_START = 0,
        EXAMPLE_EXAMPLE_REQ = 1,
        EXAMPLE_EXAMPLE_ACK = 2,
        EXAMPLE_EXAMPLEDATA_NTF = 3,
        EXAMPLE_EXAMPLEEXTERNAL_REQ = 4,
        EXAMPLE_EXAMPLEEXTERNAL_ACK = 5,

        // 50 ~ 99
        TEST_START = 50,
        TEST_TEST_REQ = 51,
        TEST_TEST_ACK = 52,
        TEST_ECHO_REQ = 53,
        TEST_ECHO_ACK = 54,

        // 200 ~ 399
        CORE_START = 200,
        CORE_S_CHECKCONSISTENTDIGEST_PROTO = 201,
        CORE_S_ENGINERPC_PROTO = 202,
        CORE_S_RPCCONNECT_PROTO = 203,
        CORE_S_RPCESTABLISH_PROTO = 204,
        CORE_S_GETCONSISTENTDIGEST_PROTO = 205,
        CORE_S_NODESREADY_PROTO = 206,
        CORE_S_NODEHEARTBEAT_REQ = 207,
        CORE_S_NODEHEARTBEAT_ACK = 208,
        CORE_S_NODEINFOLIST_PROTO = 209,
        CORE_S_NODEJOIN_NTF = 210,
        CORE_S_NODELEAVE_NTF = 211,
        CORE_S_NODEREADY_PROTO = 212,
        CORE_S_REGISTERMAILBOX_REQ = 213,
        CORE_S_REGISTERMAILBOX_ACK = 214,
        CORE_S_RPCBYMAILBOX_PROTO = 215,
        CORE_S_RPC_REQ = 216,
        CORE_S_RPC_ACK = 217,
        CORE_S_RPCTONODE_PROTO = 218,
        CORE_S_RPCTOPLAYER_PROTO = 219,
        CORE_S_RPCTOPLAYERROUTER_PROTO = 220,
        CORE_S_SERVERNODEINFO_PROTO = 221,
        CORE_S_TRANSUPDATECONSISTENTHASH_REQ = 222,
        CORE_S_TRANSUPDATECONSISTENTHASH_ACK = 223,
        CORE_S_UNREGISTERMAILBOX_REQ = 224,
        CORE_S_UNREGISTERMAILBOX_ACK = 225,
        CORE_S_UPDATECONSISTENTHASH_PROTO = 226,
        CORE_S_BROADCASTDATA_PROTO = 227,

        // 400 ~ 599
        SYS_START = 400,
        SYS_C_CALLBASEPLAYERMETHOD_PROTO = 401,
        SYS_C_CALLENTITYMETHOD_PROTO = 402,
        SYS_S_BASICENTITYCREATE_PROTO = 403,
        SYS_S_CALLCLIENTMETHOD_PROTO = 404,
        SYS_S_ENTITYCREAWTE_PROTO = 405,
        SYS_S_ENTITYDESTROY_PROTO = 406,
        SYS_S_ENTITYMIGRATE_PROTO = 407,
        SYS_S_ENTITYMIGRATEREPLY_PROTO = 408,
        SYS_S_FORWARDENTITYRPC_PROTO = 409,

        // 600 ~ 799
        RPC_START = 600,
        RPC_S_FORWARDCLIENTMSGBROADCAST_PROTO = 603,
        RPC_FORWARDCLIENTMESSAGETOSERVERNTF = 604,
        RPC_FORWARDMESSAGETOCLIENTNTF = 605,

        // 800 ~ 999
        LOGIN_START = 800,
        LOGIN_LOGOUT_REQ = 806,
        LOGIN_LOGINBYAUTHTOKENREQ = 807,
        LOGIN_LOGINBYAUTHTOKENACK = 808,
        LOGIN_LOGINBYSESSIONTOKENREQ = 809,
        LOGIN_LOGINBYSESSIONTOKENACK = 810,
        LOGIN_SERVERDISCONNECTNTF = 811,

        // 1000 ~ 1499
        PLAYERINIT_START = 1000,
        PLAYERINIT_PLAYERBASICCOMPDATANTF = 1007,
        PLAYERINIT_PLAYERDATASECTIONSYNCENDNTF = 1008,
        PLAYERINIT_PLAYERINFOINITREQ = 1009,
        PLAYERINIT_PLAYERINFOINITACK = 1010,
        PLAYERINIT_CHARACTERCREATEREQ = 1011,
        PLAYERINIT_CHARACTERCREATEACK = 1012,

        // 2000 ~ 2999
        WORD_START = 2000,
        WORD_PLAYERWORLDCOMPDATANTF = 2002,

        // 3000 ~ 3999
        HAKONIWA_START = 3000,
        HAKONIWA_PLAYERHAKONIWACOMPDATANTF = 3001,
        HAKONIWA_HAKONIWAENTERREQ = 3002,
        HAKONIWA_HAKONIWAENTERACK = 3003,
        HAKONIWA_HAKONIWAGIVEUPREQ = 3006,
        HAKONIWA_HAKONIWAGIVEUPACK = 3007,

        // 4000 ~ 4999
        BATTLE_START = 4000,
        BATTLE_BATTLEVERIFY_REQ = 4001,
        BATTLE_BATTLEVERIFY_ACK = 4002,

        // 5000 ~ 5999
        DUEL_START = 5000,
        DUEL_DUEL_REQ = 5001,
        DUEL_DUEL_ACK = 5002,
        DUEL_DUEL_NTF = 5003,
        DUEL_DUELREPLY_REQ = 5004,
        DUEL_DUELREPLY_ACK = 5005,
        DUEL_DUELREPLY_NTF = 5006,
        DUEL_DUELBATTLESESSIONINIT_NTF = 5007,
        DUEL_DUELBATTLESESSIONEND_NTF = 5008,
        DUEL_S_DUELREPLY_NTF = 5009,
        DUEL_S_DUEL_NTF = 5010,

        // 6000 ~ 6999
        BATTLEPVP_START = 6000,
        BATTLEPVP_BATTLECOMMAND_REQ = 6001,
        BATTLEPVP_BATTLECOMMAND_ACK = 6002,
        BATTLEPVP_BATTLECOMMAND_NTF = 6003,

        // 7000 ~ 7999
        BAG_START = 7000,
        BAG_BAGINFO_REQ = 7001,
        BAG_BAGINFO_ACK = 7002,

        // 65000 ~ 65535
        GLOBALSYNC_START = 65000,
        GLOBALSYNC_SERVERTIME_REQ = 65002,
        GLOBALSYNC_SERVERTIME_ACK = 65003,
        GLOBALSYNC_HEARTBEAT_REQ = 65004,
        GLOBALSYNC_HEARTBEAT_ACK = 65005,

    }

    [MessagePackObject]
    public class MsgPackStructBase
    {
        [Key(0)]
        public EProtoCode ProtoCode { get; set; }

        [Key(1)]
        public EMsgPackOpType OpType { get; set; }

        public virtual bool HasToken() => false;

        public virtual uint? GetToken() => null;

        public virtual void SetToken(uint token) { }

        public MsgPackStructBase()
        {
            Init();
        }

        public virtual void Init() { }

    }

    /// <summary>
    /// db缓存对象基类,Dirty状态标记数据脏
    /// </summary>
    /// <returns></returns>
    [MessagePackObject]
    public partial class DBCacheObject
    {
        /// <summary>
        /// 获取或设置一个值，指示此模式是否处于脏状态（需要保存）
        /// </summary>
#if PHOENIX_SERVER
        [BsonIgnore]
#endif
        [IgnoreMember]
        private bool IsDirty { get;
            set; } = false;

        /// <summary>
        /// 将模式标记为脏状态
        /// </summary>
        public void MarkDirty()
        {
            IsDirty = true;
        }

        /// <summary>
        /// 重置脏状态标记
        /// </summary>
        public void ResetDirty()
        {
            IsDirty = false;
        }

        /// <summary>
        /// 检查模式是否处于脏状态
        /// </summary>
        /// <returns>如果模式处于脏状态，则为 true；否则为 false</returns>
        public bool CheckDirty()
        {
            return IsDirty;
        }
    }



}