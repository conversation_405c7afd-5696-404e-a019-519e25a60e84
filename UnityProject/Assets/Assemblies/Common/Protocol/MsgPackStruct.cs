// Generated by Tools.  EDIT will be overwritten!

using MessagePack;
using System;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif

namespace Phoenix.MsgPackLogic.Protocol
{
    public enum EResultType : SByte
    {
        SUCCESS = 0,
        FAIL = 1,
        ERROR = 2,
        TIMEOUT = 3,
        WARNING = 4,
        EXCEPTION = 5,
    }

    public enum EMsgPackOpType
    {
        // Do nothing
        OP_NULL = 0,
        // Excute default protocol logic
        OP_DEFAULT,
        // Call Client <-> Server Func
        OP_FUNC,
        // Clear all elements
        OP_CLEAR,
        // Erase key (for array, the key is index)
        OP_ERASE,
        // Set key-value
        OP_UPDATE,
        // Array operation
        OP_A_APPEND,
        OP_A_INSERT,
        OP_BITSET_SET,
        OP_BITSET_RESET,
        OP_SET_ADD,
        OP_SET_REMOVE,



        OP_END
    }

    [MessagePackObject]
    public class MsgPackStructBase
    {
        [Key(0)]
        public EProtoCode ProtoCode { get; set; }

        [Key(1)]
        public EMsgPackOpType OpType { get; set; }

        public virtual bool HasToken() => false;

        public virtual uint? GetToken() => null;

        public virtual void SetToken(uint token) { }

        public MsgPackStructBase()
        {
            Init();
        }

        public virtual void Init() { }

    }

    /// <summary>
    /// db缓存对象基类,Dirty状态标记数据脏
    /// </summary>
    /// <returns></returns>
    [MessagePackObject]
    public partial class DBCacheObject
    {
        /// <summary>
        /// 获取或设置一个值，指示此模式是否处于脏状态（需要保存）
        /// </summary>
#if PHOENIX_SERVER
        [BsonIgnore]
#endif
        private bool IsDirty { get;
            set; } = false;

        /// <summary>
        /// 将模式标记为脏状态
        /// </summary>
        public void MarkDirty()
        {
            IsDirty = true;
        }

        /// <summary>
        /// 重置脏状态标记
        /// </summary>
        public void ResetDirty()
        {
            IsDirty = false;
        }

        /// <summary>
        /// 检查模式是否处于脏状态
        /// </summary>
        /// <returns>如果模式处于脏状态，则为 true；否则为 false</returns>
        public bool CheckDirty()
        {
            return IsDirty;
        }
    }
}
