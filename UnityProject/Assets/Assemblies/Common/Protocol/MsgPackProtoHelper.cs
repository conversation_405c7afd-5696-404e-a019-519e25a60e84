// Generated by Tools.  EDIT will be overwritten!
using MessagePack;
using System;
using System.Collections.Generic;
using System.IO;

namespace Phoenix.MsgPackLogic.Protocol
{
    public enum EProtoCode
    {
        BandwidthTestAck = 1,
        BandwidthTestReq = 2,
        BattleCancelAck = 3,
        BattleCancelReq = 4,
        CharacterCreateAck = 5,
        CharacterCreateReq = 6,
        HakoniwaBattleFinishAck = 7,
        HakoniwaBattleFinishReq = 8,
        HakoniwaBattleStartAck = 9,
        HakoniwaBattleStartReq = 10,
        HakoniwaDialogCompleteAck = 11,
        HakoniwaDialogCompleteReq = 12,
        HakoniwaEnterAck = 13,
        HakoniwaEnterReq = 14,
        HakoniwaFinishNtf = 15,
        HakoniwaGiveUpAck = 16,
        HakoniwaGiveUpReq = 17,
        HakoniwaPosSyncAck = 18,
        HakoniwaPosSyncReq = 19,
        HakoniwaQuestCondChangeNtf = 20,
        HakoniwaQuestStateChangeNtf = 21,
        HakoniwaReachPointAck = 22,
        HakoniwaReachPointReq = 23,
        HakoniwaTreasureBoxOpenAck = 24,
        HakoniwaTreasureBoxOpenReq = 25,
        LoginByAuthTokenAck = 26,
        LoginByAuthTokenReq = 27,
        LoginBySessionTokenAck = 28,
        LoginBySessionTokenReq = 29,
        MsgPack_BagInfo_Ack = 30,
        MsgPack_BagInfo_Req = 31,
        MsgPack_BattleCommand_Ack = 32,
        MsgPack_BattleCommand_Ntf = 33,
        MsgPack_BattleCommand_Req = 34,
        MsgPack_BattleVerify_Ack = 35,
        MsgPack_BattleVerify_Req = 36,
        MsgPack_DuelBattleSessionEnd_Ntf = 37,
        MsgPack_DuelBattleSessionInit_Ntf = 38,
        MsgPack_DuelReply_Ack = 39,
        MsgPack_DuelReply_Ntf = 40,
        MsgPack_DuelReply_Req = 41,
        MsgPack_Duel_Ack = 42,
        MsgPack_Duel_Ntf = 43,
        MsgPack_Duel_Req = 44,
        MsgPack_Echo_Ack = 45,
        MsgPack_Echo_Req = 46,
        MsgPack_ExampleData_Ntf = 47,
        MsgPack_ExampleExternal_Ack = 48,
        MsgPack_ExampleExternal_Req = 49,
        MsgPack_Example_Ack = 50,
        MsgPack_Example_Req = 51,
        MsgPack_HeartBeat_Ack = 52,
        MsgPack_HeartBeat_Req = 53,
        MsgPack_Logout_Req = 54,
        MsgPack_ServerTime_Ack = 55,
        MsgPack_ServerTime_Req = 56,
        MsgPack_S_DuelReply_Ntf = 57,
        MsgPack_S_Duel_Ntf = 58,
        MsgPack_Test_Ack = 59,
        MsgPack_Test_Req = 60,
        PlayerBasicCompDataNtf = 61,
        PlayerBattleCompDataNtf = 62,
        PlayerDataSectionSyncEndNtf = 63,
        PlayerHakoniwaCompDataNtf = 64,
        PlayerInfoInitAck = 65,
        PlayerInfoInitReq = 66,
        PlayerWorldCompDataNtf = 67,
        ServerDisconnectNtf = 68,
        HakoniwaMonsterOneHitAck = 69,
        HakoniwaMonsterOneHitReq = 70,
        CheckConsistentDigestNtf = 10000,
        ConsistentDigestDiffNtf = 10001,
        ForwardClientMessageToServerNtf = 10002,
        ForwardClientMsgBroadcastNtf = 10003,
        ForwardMessageToClientNtf = 10004,
        MsgPack_C_CallBasePlayerMethod_Proto = 10005,
        MsgPack_C_CallEntityMethod_Proto = 10006,
        MsgPack_S_BasicEntityCreate_Proto = 10007,
        MsgPack_S_CallClientMethod_Proto = 10008,
        MsgPack_S_EngineRpc_Proto = 10009,
        MsgPack_S_EntityCreawte_Proto = 10010,
        MsgPack_S_EntityDestroy_Proto = 10011,
        MsgPack_S_EntityMigrateReply_Proto = 10012,
        MsgPack_S_EntityMigrate_Proto = 10013,
        MsgPack_S_ForwardEntityRpc_Proto = 10014,
        MsgPack_S_RpcToNode_Proto = 10015,
        MsgPack_S_RpcToPlayerRouter_Proto = 10016,
        MsgPack_S_RpcToPlayer_Proto = 10017,
        MsgPack_S_Rpc_Ack = 10018,
        MsgPack_S_Rpc_Req = 10019,
        RegisterMailBoxAck = 10020,
        RegisterMailBoxReq = 10021,
        RpcByMailBoxReq = 10022,
        RpcShakeHandAck = 10023,
        RpcShakeHandReq = 10024,
        ServerBroadcastDataNtf = 10025,
        ServerNodeHeartbeatAck = 10026,
        ServerNodeHeartbeatReq = 10027,
        ServerNodeJoinNtf = 10028,
        ServerNodeLeaveNtf = 10029,
        ServerNodeReadyNtf = 10030,
        ServerNodesJoinNtf = 10031,
        ServerNodesReadyNtf = 10032,
        TransUpdateConsistentHashAck = 10033,
        TransUpdateConsistentHashReq = 10034,
        UnregisterMailBoxAck = 10035,
        UnregisterMailBoxReq = 10036,
        UpdateConsistentHashNtf = 10037,
    }

    public static partial class MsgPackProtoHelper
    {
        static MsgPackProtoHelper()
        {
            # if PHOENIX_SERVER || PHOENIX_MINICLIENT

            var resolver = MessagePack.Resolvers.CompositeResolver.Create(
                MessagePack.Resolvers.NativeDateTimeResolver.Instance,
                MessagePack.Resolvers.StandardResolver.Instance
            );
            MessagePackSerializer.DefaultOptions = MessagePackSerializerOptions.Standard.WithResolver(resolver);
            #endif

           Add((int)EProtoCode.BandwidthTestAck, typeof(BandwidthTestAck), true, false);
           Add((int)EProtoCode.BandwidthTestReq, typeof(BandwidthTestReq), true, true);
           Add((int)EProtoCode.BattleCancelAck, typeof(BattleCancelAck), true, false);
           Add((int)EProtoCode.BattleCancelReq, typeof(BattleCancelReq), true, false);
           Add((int)EProtoCode.CharacterCreateAck, typeof(CharacterCreateAck), true, false);
           Add((int)EProtoCode.CharacterCreateReq, typeof(CharacterCreateReq), true, true);
           Add((int)EProtoCode.HakoniwaBattleFinishAck, typeof(HakoniwaBattleFinishAck), true, false);
           Add((int)EProtoCode.HakoniwaBattleFinishReq, typeof(HakoniwaBattleFinishReq), true, true);
           Add((int)EProtoCode.HakoniwaBattleStartAck, typeof(HakoniwaBattleStartAck), true, false);
           Add((int)EProtoCode.HakoniwaBattleStartReq, typeof(HakoniwaBattleStartReq), true, true);
           Add((int)EProtoCode.HakoniwaDialogCompleteAck, typeof(HakoniwaDialogCompleteAck), true, false);
           Add((int)EProtoCode.HakoniwaDialogCompleteReq, typeof(HakoniwaDialogCompleteReq), true, true);
           Add((int)EProtoCode.HakoniwaEnterAck, typeof(HakoniwaEnterAck), true, false);
           Add((int)EProtoCode.HakoniwaEnterReq, typeof(HakoniwaEnterReq), true, true);
           Add((int)EProtoCode.HakoniwaFinishNtf, typeof(HakoniwaFinishNtf), true, false);
           Add((int)EProtoCode.HakoniwaGiveUpAck, typeof(HakoniwaGiveUpAck), true, false);
           Add((int)EProtoCode.HakoniwaGiveUpReq, typeof(HakoniwaGiveUpReq), true, true);
           Add((int)EProtoCode.HakoniwaPosSyncAck, typeof(HakoniwaPosSyncAck), true, false);
           Add((int)EProtoCode.HakoniwaPosSyncReq, typeof(HakoniwaPosSyncReq), true, true);
           Add((int)EProtoCode.HakoniwaQuestCondChangeNtf, typeof(HakoniwaQuestCondChangeNtf), true, false);
           Add((int)EProtoCode.HakoniwaQuestStateChangeNtf, typeof(HakoniwaQuestStateChangeNtf), true, false);
           Add((int)EProtoCode.HakoniwaReachPointAck, typeof(HakoniwaReachPointAck), true, false);
           Add((int)EProtoCode.HakoniwaReachPointReq, typeof(HakoniwaReachPointReq), true, true);
           Add((int)EProtoCode.HakoniwaTreasureBoxOpenAck, typeof(HakoniwaTreasureBoxOpenAck), true, false);
           Add((int)EProtoCode.HakoniwaTreasureBoxOpenReq, typeof(HakoniwaTreasureBoxOpenReq), true, true);
           Add((int)EProtoCode.LoginByAuthTokenAck, typeof(LoginByAuthTokenAck), true, false);
           Add((int)EProtoCode.LoginByAuthTokenReq, typeof(LoginByAuthTokenReq), true, true);
           Add((int)EProtoCode.LoginBySessionTokenAck, typeof(LoginBySessionTokenAck), true, false);
           Add((int)EProtoCode.LoginBySessionTokenReq, typeof(LoginBySessionTokenReq), true, true);
           Add((int)EProtoCode.MsgPack_BagInfo_Ack, typeof(MsgPack_BagInfo_Ack), true, false);
           Add((int)EProtoCode.MsgPack_BagInfo_Req, typeof(MsgPack_BagInfo_Req), true, true);
           Add((int)EProtoCode.MsgPack_BattleCommand_Ack, typeof(MsgPack_BattleCommand_Ack), true, false);
           Add((int)EProtoCode.MsgPack_BattleCommand_Ntf, typeof(MsgPack_BattleCommand_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_BattleCommand_Req, typeof(MsgPack_BattleCommand_Req), true, true);
           Add((int)EProtoCode.MsgPack_BattleVerify_Ack, typeof(MsgPack_BattleVerify_Ack), true, true);
           Add((int)EProtoCode.MsgPack_BattleVerify_Req, typeof(MsgPack_BattleVerify_Req), true, true);
           Add((int)EProtoCode.MsgPack_DuelBattleSessionEnd_Ntf, typeof(MsgPack_DuelBattleSessionEnd_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_DuelBattleSessionInit_Ntf, typeof(MsgPack_DuelBattleSessionInit_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_DuelReply_Ack, typeof(MsgPack_DuelReply_Ack), true, false);
           Add((int)EProtoCode.MsgPack_DuelReply_Ntf, typeof(MsgPack_DuelReply_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_DuelReply_Req, typeof(MsgPack_DuelReply_Req), true, true);
           Add((int)EProtoCode.MsgPack_Duel_Ack, typeof(MsgPack_Duel_Ack), true, false);
           Add((int)EProtoCode.MsgPack_Duel_Ntf, typeof(MsgPack_Duel_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_Duel_Req, typeof(MsgPack_Duel_Req), true, true);
           Add((int)EProtoCode.MsgPack_Echo_Ack, typeof(MsgPack_Echo_Ack), true, false);
           Add((int)EProtoCode.MsgPack_Echo_Req, typeof(MsgPack_Echo_Req), true, true);
           Add((int)EProtoCode.MsgPack_ExampleData_Ntf, typeof(MsgPack_ExampleData_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_ExampleExternal_Ack, typeof(MsgPack_ExampleExternal_Ack), true, false);
           Add((int)EProtoCode.MsgPack_ExampleExternal_Req, typeof(MsgPack_ExampleExternal_Req), true, true);
           Add((int)EProtoCode.MsgPack_Example_Ack, typeof(MsgPack_Example_Ack), true, false);
           Add((int)EProtoCode.MsgPack_Example_Req, typeof(MsgPack_Example_Req), true, true);
           Add((int)EProtoCode.MsgPack_HeartBeat_Ack, typeof(MsgPack_HeartBeat_Ack), true, false);
           Add((int)EProtoCode.MsgPack_HeartBeat_Req, typeof(MsgPack_HeartBeat_Req), true, true);
           Add((int)EProtoCode.MsgPack_Logout_Req, typeof(MsgPack_Logout_Req), true, false);
           Add((int)EProtoCode.MsgPack_ServerTime_Ack, typeof(MsgPack_ServerTime_Ack), true, false);
           Add((int)EProtoCode.MsgPack_ServerTime_Req, typeof(MsgPack_ServerTime_Req), true, true);
           Add((int)EProtoCode.MsgPack_S_DuelReply_Ntf, typeof(MsgPack_S_DuelReply_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_S_Duel_Ntf, typeof(MsgPack_S_Duel_Ntf), true, false);
           Add((int)EProtoCode.MsgPack_Test_Ack, typeof(MsgPack_Test_Ack), true, false);
           Add((int)EProtoCode.MsgPack_Test_Req, typeof(MsgPack_Test_Req), true, false);
           Add((int)EProtoCode.PlayerBasicCompDataNtf, typeof(PlayerBasicCompDataNtf), true, false);
           Add((int)EProtoCode.PlayerBattleCompDataNtf, typeof(PlayerBattleCompDataNtf), true, false);
           Add((int)EProtoCode.PlayerDataSectionSyncEndNtf, typeof(PlayerDataSectionSyncEndNtf), true, false);
           Add((int)EProtoCode.PlayerHakoniwaCompDataNtf, typeof(PlayerHakoniwaCompDataNtf), true, false);
           Add((int)EProtoCode.PlayerInfoInitAck, typeof(PlayerInfoInitAck), true, false);
           Add((int)EProtoCode.PlayerInfoInitReq, typeof(PlayerInfoInitReq), true, true);
           Add((int)EProtoCode.PlayerWorldCompDataNtf, typeof(PlayerWorldCompDataNtf), true, false);
           Add((int)EProtoCode.ServerDisconnectNtf, typeof(ServerDisconnectNtf), true, false);
           Add((int)EProtoCode.HakoniwaMonsterOneHitAck, typeof(HakoniwaMonsterOneHitAck), true, false);
           Add((int)EProtoCode.HakoniwaMonsterOneHitReq, typeof(HakoniwaMonsterOneHitReq), true, true);
           Add((int)EProtoCode.CheckConsistentDigestNtf, typeof(CheckConsistentDigestNtf), false, false);
           Add((int)EProtoCode.ConsistentDigestDiffNtf, typeof(ConsistentDigestDiffNtf), false, false);
           Add((int)EProtoCode.ForwardClientMessageToServerNtf, typeof(ForwardClientMessageToServerNtf), false, false);
           Add((int)EProtoCode.ForwardClientMsgBroadcastNtf, typeof(ForwardClientMsgBroadcastNtf), false, false);
           Add((int)EProtoCode.ForwardMessageToClientNtf, typeof(ForwardMessageToClientNtf), false, false);
           Add((int)EProtoCode.MsgPack_C_CallBasePlayerMethod_Proto, typeof(MsgPack_C_CallBasePlayerMethod_Proto), false, false);
           Add((int)EProtoCode.MsgPack_C_CallEntityMethod_Proto, typeof(MsgPack_C_CallEntityMethod_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_BasicEntityCreate_Proto, typeof(MsgPack_S_BasicEntityCreate_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_CallClientMethod_Proto, typeof(MsgPack_S_CallClientMethod_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_EngineRpc_Proto, typeof(MsgPack_S_EngineRpc_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_EntityCreawte_Proto, typeof(MsgPack_S_EntityCreawte_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_EntityDestroy_Proto, typeof(MsgPack_S_EntityDestroy_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_EntityMigrateReply_Proto, typeof(MsgPack_S_EntityMigrateReply_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_EntityMigrate_Proto, typeof(MsgPack_S_EntityMigrate_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_ForwardEntityRpc_Proto, typeof(MsgPack_S_ForwardEntityRpc_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_RpcToNode_Proto, typeof(MsgPack_S_RpcToNode_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_RpcToPlayerRouter_Proto, typeof(MsgPack_S_RpcToPlayerRouter_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_RpcToPlayer_Proto, typeof(MsgPack_S_RpcToPlayer_Proto), false, false);
           Add((int)EProtoCode.MsgPack_S_Rpc_Ack, typeof(MsgPack_S_Rpc_Ack), false, false);
           Add((int)EProtoCode.MsgPack_S_Rpc_Req, typeof(MsgPack_S_Rpc_Req), false, false);
           Add((int)EProtoCode.RegisterMailBoxAck, typeof(RegisterMailBoxAck), false, false);
           Add((int)EProtoCode.RegisterMailBoxReq, typeof(RegisterMailBoxReq), false, false);
           Add((int)EProtoCode.RpcByMailBoxReq, typeof(RpcByMailBoxReq), false, false);
           Add((int)EProtoCode.RpcShakeHandAck, typeof(RpcShakeHandAck), false, false);
           Add((int)EProtoCode.RpcShakeHandReq, typeof(RpcShakeHandReq), false, false);
           Add((int)EProtoCode.ServerBroadcastDataNtf, typeof(ServerBroadcastDataNtf), false, false);
           Add((int)EProtoCode.ServerNodeHeartbeatAck, typeof(ServerNodeHeartbeatAck), false, false);
           Add((int)EProtoCode.ServerNodeHeartbeatReq, typeof(ServerNodeHeartbeatReq), false, false);
           Add((int)EProtoCode.ServerNodeJoinNtf, typeof(ServerNodeJoinNtf), false, false);
           Add((int)EProtoCode.ServerNodeLeaveNtf, typeof(ServerNodeLeaveNtf), false, false);
           Add((int)EProtoCode.ServerNodeReadyNtf, typeof(ServerNodeReadyNtf), false, false);
           Add((int)EProtoCode.ServerNodesJoinNtf, typeof(ServerNodesJoinNtf), false, false);
           Add((int)EProtoCode.ServerNodesReadyNtf, typeof(ServerNodesReadyNtf), false, false);
           Add((int)EProtoCode.TransUpdateConsistentHashAck, typeof(TransUpdateConsistentHashAck), false, false);
           Add((int)EProtoCode.TransUpdateConsistentHashReq, typeof(TransUpdateConsistentHashReq), false, false);
           Add((int)EProtoCode.UnregisterMailBoxAck, typeof(UnregisterMailBoxAck), false, false);
           Add((int)EProtoCode.UnregisterMailBoxReq, typeof(UnregisterMailBoxReq), false, false);
           Add((int)EProtoCode.UpdateConsistentHashNtf, typeof(UpdateConsistentHashNtf), false, false);
        }

        private static void Add(int protoId, Type type, bool isClient, bool isFromClient)
        {
           s_type2ProtoId.Add(type, protoId);
           s_protoId2Type.Add(protoId, type);
           if (isClient)
           {
                s_clientProtoId2Type.Add(protoId, type);
           }
           if (isFromClient)
           {
                s_protoIdsFromClient.Add(protoId);
           }
        }

        public static MsgPackStructBase Deserialize(int protoId, ReadOnlyMemory<byte> memory)
        {
            switch ((EProtoCode)protoId)
            {
                case EProtoCode.BandwidthTestAck:
                {
                    return MessagePackSerializer.Deserialize<BandwidthTestAck>(memory);
                }
                case EProtoCode.BandwidthTestReq:
                {
                    return MessagePackSerializer.Deserialize<BandwidthTestReq>(memory);
                }
                case EProtoCode.BattleCancelAck:
                {
                    return MessagePackSerializer.Deserialize<BattleCancelAck>(memory);
                }
                case EProtoCode.BattleCancelReq:
                {
                    return MessagePackSerializer.Deserialize<BattleCancelReq>(memory);
                }
                case EProtoCode.CharacterCreateAck:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateAck>(memory);
                }
                case EProtoCode.CharacterCreateReq:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateReq>(memory);
                }
                case EProtoCode.HakoniwaBattleFinishAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleFinishAck>(memory);
                }
                case EProtoCode.HakoniwaBattleFinishReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleFinishReq>(memory);
                }
                case EProtoCode.HakoniwaBattleStartAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleStartAck>(memory);
                }
                case EProtoCode.HakoniwaBattleStartReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleStartReq>(memory);
                }
                case EProtoCode.HakoniwaDialogCompleteAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaDialogCompleteAck>(memory);
                }
                case EProtoCode.HakoniwaDialogCompleteReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaDialogCompleteReq>(memory);
                }
                case EProtoCode.HakoniwaEnterAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterAck>(memory);
                }
                case EProtoCode.HakoniwaEnterReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterReq>(memory);
                }
                case EProtoCode.HakoniwaFinishNtf:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaFinishNtf>(memory);
                }
                case EProtoCode.HakoniwaGiveUpAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpAck>(memory);
                }
                case EProtoCode.HakoniwaGiveUpReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpReq>(memory);
                }
                case EProtoCode.HakoniwaPosSyncAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaPosSyncAck>(memory);
                }
                case EProtoCode.HakoniwaPosSyncReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaPosSyncReq>(memory);
                }
                case EProtoCode.HakoniwaQuestCondChangeNtf:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaQuestCondChangeNtf>(memory);
                }
                case EProtoCode.HakoniwaQuestStateChangeNtf:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaQuestStateChangeNtf>(memory);
                }
                case EProtoCode.HakoniwaReachPointAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaReachPointAck>(memory);
                }
                case EProtoCode.HakoniwaReachPointReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaReachPointReq>(memory);
                }
                case EProtoCode.HakoniwaTreasureBoxOpenAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaTreasureBoxOpenAck>(memory);
                }
                case EProtoCode.HakoniwaTreasureBoxOpenReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaTreasureBoxOpenReq>(memory);
                }
                case EProtoCode.LoginByAuthTokenAck:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenAck>(memory);
                }
                case EProtoCode.LoginByAuthTokenReq:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenReq>(memory);
                }
                case EProtoCode.LoginBySessionTokenAck:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenAck>(memory);
                }
                case EProtoCode.LoginBySessionTokenReq:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenReq>(memory);
                }
                case EProtoCode.MsgPack_BagInfo_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Ack>(memory);
                }
                case EProtoCode.MsgPack_BagInfo_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Req>(memory);
                }
                case EProtoCode.MsgPack_BattleCommand_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ack>(memory);
                }
                case EProtoCode.MsgPack_BattleCommand_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ntf>(memory);
                }
                case EProtoCode.MsgPack_BattleCommand_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Req>(memory);
                }
                case EProtoCode.MsgPack_BattleVerify_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Ack>(memory);
                }
                case EProtoCode.MsgPack_BattleVerify_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Req>(memory);
                }
                case EProtoCode.MsgPack_DuelBattleSessionEnd_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionEnd_Ntf>(memory);
                }
                case EProtoCode.MsgPack_DuelBattleSessionInit_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionInit_Ntf>(memory);
                }
                case EProtoCode.MsgPack_DuelReply_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ack>(memory);
                }
                case EProtoCode.MsgPack_DuelReply_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ntf>(memory);
                }
                case EProtoCode.MsgPack_DuelReply_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Req>(memory);
                }
                case EProtoCode.MsgPack_Duel_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ack>(memory);
                }
                case EProtoCode.MsgPack_Duel_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ntf>(memory);
                }
                case EProtoCode.MsgPack_Duel_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Req>(memory);
                }
                case EProtoCode.MsgPack_Echo_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Ack>(memory);
                }
                case EProtoCode.MsgPack_Echo_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Req>(memory);
                }
                case EProtoCode.MsgPack_ExampleData_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleData_Ntf>(memory);
                }
                case EProtoCode.MsgPack_ExampleExternal_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Ack>(memory);
                }
                case EProtoCode.MsgPack_ExampleExternal_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Req>(memory);
                }
                case EProtoCode.MsgPack_Example_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Ack>(memory);
                }
                case EProtoCode.MsgPack_Example_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Req>(memory);
                }
                case EProtoCode.MsgPack_HeartBeat_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_HeartBeat_Ack>(memory);
                }
                case EProtoCode.MsgPack_HeartBeat_Req:
                {
                    return MsgPack_HeartBeat_Req.Shared;
                }
                case EProtoCode.MsgPack_Logout_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Logout_Req>(memory);
                }
                case EProtoCode.MsgPack_ServerTime_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Ack>(memory);
                }
                case EProtoCode.MsgPack_ServerTime_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Req>(memory);
                }
                case EProtoCode.MsgPack_S_DuelReply_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_DuelReply_Ntf>(memory);
                }
                case EProtoCode.MsgPack_S_Duel_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Duel_Ntf>(memory);
                }
                case EProtoCode.MsgPack_Test_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Ack>(memory);
                }
                case EProtoCode.MsgPack_Test_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Req>(memory);
                }
                case EProtoCode.PlayerBasicCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerBasicCompDataNtf>(memory);
                }
                case EProtoCode.PlayerBattleCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerBattleCompDataNtf>(memory);
                }
                case EProtoCode.PlayerDataSectionSyncEndNtf:
                {
                    return PlayerDataSectionSyncEndNtf.Shared;
                }
                case EProtoCode.PlayerHakoniwaCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerHakoniwaCompDataNtf>(memory);
                }
                case EProtoCode.PlayerInfoInitAck:
                {
                    return MessagePackSerializer.Deserialize<PlayerInfoInitAck>(memory);
                }
                case EProtoCode.PlayerInfoInitReq:
                {
                    return PlayerInfoInitReq.Shared;
                }
                case EProtoCode.PlayerWorldCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerWorldCompDataNtf>(memory);
                }
                case EProtoCode.ServerDisconnectNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerDisconnectNtf>(memory);
                }
                case EProtoCode.HakoniwaMonsterOneHitAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaMonsterOneHitAck>(memory);
                }
                case EProtoCode.HakoniwaMonsterOneHitReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaMonsterOneHitReq>(memory);
                }
                case EProtoCode.CheckConsistentDigestNtf:
                {
                    return MessagePackSerializer.Deserialize<CheckConsistentDigestNtf>(memory);
                }
                case EProtoCode.ConsistentDigestDiffNtf:
                {
                    return MessagePackSerializer.Deserialize<ConsistentDigestDiffNtf>(memory);
                }
                case EProtoCode.ForwardClientMessageToServerNtf:
                {
                    return MessagePackSerializer.Deserialize<ForwardClientMessageToServerNtf>(memory);
                }
                case EProtoCode.ForwardClientMsgBroadcastNtf:
                {
                    return MessagePackSerializer.Deserialize<ForwardClientMsgBroadcastNtf>(memory);
                }
                case EProtoCode.ForwardMessageToClientNtf:
                {
                    return MessagePackSerializer.Deserialize<ForwardMessageToClientNtf>(memory);
                }
                case EProtoCode.MsgPack_C_CallBasePlayerMethod_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallBasePlayerMethod_Proto>(memory);
                }
                case EProtoCode.MsgPack_C_CallEntityMethod_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallEntityMethod_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_BasicEntityCreate_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_BasicEntityCreate_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_CallClientMethod_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_CallClientMethod_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_EngineRpc_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EngineRpc_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_EntityCreawte_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityCreawte_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_EntityDestroy_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityDestroy_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_EntityMigrateReply_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrateReply_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_EntityMigrate_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrate_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_ForwardEntityRpc_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ForwardEntityRpc_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_RpcToNode_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToNode_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_RpcToPlayerRouter_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayerRouter_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_RpcToPlayer_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayer_Proto>(memory);
                }
                case EProtoCode.MsgPack_S_Rpc_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Ack>(memory);
                }
                case EProtoCode.MsgPack_S_Rpc_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Req>(memory);
                }
                case EProtoCode.RegisterMailBoxAck:
                {
                    return MessagePackSerializer.Deserialize<RegisterMailBoxAck>(memory);
                }
                case EProtoCode.RegisterMailBoxReq:
                {
                    return MessagePackSerializer.Deserialize<RegisterMailBoxReq>(memory);
                }
                case EProtoCode.RpcByMailBoxReq:
                {
                    return MessagePackSerializer.Deserialize<RpcByMailBoxReq>(memory);
                }
                case EProtoCode.RpcShakeHandAck:
                {
                    return MessagePackSerializer.Deserialize<RpcShakeHandAck>(memory);
                }
                case EProtoCode.RpcShakeHandReq:
                {
                    return MessagePackSerializer.Deserialize<RpcShakeHandReq>(memory);
                }
                case EProtoCode.ServerBroadcastDataNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerBroadcastDataNtf>(memory);
                }
                case EProtoCode.ServerNodeHeartbeatAck:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeHeartbeatAck>(memory);
                }
                case EProtoCode.ServerNodeHeartbeatReq:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeHeartbeatReq>(memory);
                }
                case EProtoCode.ServerNodeJoinNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeJoinNtf>(memory);
                }
                case EProtoCode.ServerNodeLeaveNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeLeaveNtf>(memory);
                }
                case EProtoCode.ServerNodeReadyNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeReadyNtf>(memory);
                }
                case EProtoCode.ServerNodesJoinNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodesJoinNtf>(memory);
                }
                case EProtoCode.ServerNodesReadyNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodesReadyNtf>(memory);
                }
                case EProtoCode.TransUpdateConsistentHashAck:
                {
                    return MessagePackSerializer.Deserialize<TransUpdateConsistentHashAck>(memory);
                }
                case EProtoCode.TransUpdateConsistentHashReq:
                {
                    return MessagePackSerializer.Deserialize<TransUpdateConsistentHashReq>(memory);
                }
                case EProtoCode.UnregisterMailBoxAck:
                {
                    return MessagePackSerializer.Deserialize<UnregisterMailBoxAck>(memory);
                }
                case EProtoCode.UnregisterMailBoxReq:
                {
                    return MessagePackSerializer.Deserialize<UnregisterMailBoxReq>(memory);
                }
                case EProtoCode.UpdateConsistentHashNtf:
                {
                    return MessagePackSerializer.Deserialize<UpdateConsistentHashNtf>(memory);
                }
                default:
                {
                    return null;
                }
            }
        }

        public static MsgPackStructBase Deserialize(int protoId, byte[] data)
        {
            switch ((EProtoCode)protoId)
            {
                case EProtoCode.BandwidthTestAck:
                {
                    return MessagePackSerializer.Deserialize<BandwidthTestAck>(data);
                }
                case EProtoCode.BandwidthTestReq:
                {
                    return MessagePackSerializer.Deserialize<BandwidthTestReq>(data);
                }
                case EProtoCode.BattleCancelAck:
                {
                    return MessagePackSerializer.Deserialize<BattleCancelAck>(data);
                }
                case EProtoCode.BattleCancelReq:
                {
                    return MessagePackSerializer.Deserialize<BattleCancelReq>(data);
                }
                case EProtoCode.CharacterCreateAck:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateAck>(data);
                }
                case EProtoCode.CharacterCreateReq:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateReq>(data);
                }
                case EProtoCode.HakoniwaBattleFinishAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleFinishAck>(data);
                }
                case EProtoCode.HakoniwaBattleFinishReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleFinishReq>(data);
                }
                case EProtoCode.HakoniwaBattleStartAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleStartAck>(data);
                }
                case EProtoCode.HakoniwaBattleStartReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaBattleStartReq>(data);
                }
                case EProtoCode.HakoniwaDialogCompleteAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaDialogCompleteAck>(data);
                }
                case EProtoCode.HakoniwaDialogCompleteReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaDialogCompleteReq>(data);
                }
                case EProtoCode.HakoniwaEnterAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterAck>(data);
                }
                case EProtoCode.HakoniwaEnterReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterReq>(data);
                }
                case EProtoCode.HakoniwaFinishNtf:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaFinishNtf>(data);
                }
                case EProtoCode.HakoniwaGiveUpAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpAck>(data);
                }
                case EProtoCode.HakoniwaGiveUpReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpReq>(data);
                }
                case EProtoCode.HakoniwaPosSyncAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaPosSyncAck>(data);
                }
                case EProtoCode.HakoniwaPosSyncReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaPosSyncReq>(data);
                }
                case EProtoCode.HakoniwaQuestCondChangeNtf:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaQuestCondChangeNtf>(data);
                }
                case EProtoCode.HakoniwaQuestStateChangeNtf:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaQuestStateChangeNtf>(data);
                }
                case EProtoCode.HakoniwaReachPointAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaReachPointAck>(data);
                }
                case EProtoCode.HakoniwaReachPointReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaReachPointReq>(data);
                }
                case EProtoCode.HakoniwaTreasureBoxOpenAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaTreasureBoxOpenAck>(data);
                }
                case EProtoCode.HakoniwaTreasureBoxOpenReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaTreasureBoxOpenReq>(data);
                }
                case EProtoCode.LoginByAuthTokenAck:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenAck>(data);
                }
                case EProtoCode.LoginByAuthTokenReq:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenReq>(data);
                }
                case EProtoCode.LoginBySessionTokenAck:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenAck>(data);
                }
                case EProtoCode.LoginBySessionTokenReq:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenReq>(data);
                }
                case EProtoCode.MsgPack_BagInfo_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Ack>(data);
                }
                case EProtoCode.MsgPack_BagInfo_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Req>(data);
                }
                case EProtoCode.MsgPack_BattleCommand_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ack>(data);
                }
                case EProtoCode.MsgPack_BattleCommand_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ntf>(data);
                }
                case EProtoCode.MsgPack_BattleCommand_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Req>(data);
                }
                case EProtoCode.MsgPack_BattleVerify_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Ack>(data);
                }
                case EProtoCode.MsgPack_BattleVerify_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Req>(data);
                }
                case EProtoCode.MsgPack_DuelBattleSessionEnd_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionEnd_Ntf>(data);
                }
                case EProtoCode.MsgPack_DuelBattleSessionInit_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionInit_Ntf>(data);
                }
                case EProtoCode.MsgPack_DuelReply_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ack>(data);
                }
                case EProtoCode.MsgPack_DuelReply_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ntf>(data);
                }
                case EProtoCode.MsgPack_DuelReply_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Req>(data);
                }
                case EProtoCode.MsgPack_Duel_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ack>(data);
                }
                case EProtoCode.MsgPack_Duel_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ntf>(data);
                }
                case EProtoCode.MsgPack_Duel_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Req>(data);
                }
                case EProtoCode.MsgPack_Echo_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Ack>(data);
                }
                case EProtoCode.MsgPack_Echo_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Req>(data);
                }
                case EProtoCode.MsgPack_ExampleData_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleData_Ntf>(data);
                }
                case EProtoCode.MsgPack_ExampleExternal_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Ack>(data);
                }
                case EProtoCode.MsgPack_ExampleExternal_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Req>(data);
                }
                case EProtoCode.MsgPack_Example_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Ack>(data);
                }
                case EProtoCode.MsgPack_Example_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Req>(data);
                }
                case EProtoCode.MsgPack_HeartBeat_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_HeartBeat_Ack>(data);
                }
                case EProtoCode.MsgPack_HeartBeat_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_HeartBeat_Req>(data);
                }
                case EProtoCode.MsgPack_Logout_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Logout_Req>(data);
                }
                case EProtoCode.MsgPack_ServerTime_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Ack>(data);
                }
                case EProtoCode.MsgPack_ServerTime_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Req>(data);
                }
                case EProtoCode.MsgPack_S_DuelReply_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_DuelReply_Ntf>(data);
                }
                case EProtoCode.MsgPack_S_Duel_Ntf:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Duel_Ntf>(data);
                }
                case EProtoCode.MsgPack_Test_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Ack>(data);
                }
                case EProtoCode.MsgPack_Test_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Req>(data);
                }
                case EProtoCode.PlayerBasicCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerBasicCompDataNtf>(data);
                }
                case EProtoCode.PlayerBattleCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerBattleCompDataNtf>(data);
                }
                case EProtoCode.PlayerDataSectionSyncEndNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerDataSectionSyncEndNtf>(data);
                }
                case EProtoCode.PlayerHakoniwaCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerHakoniwaCompDataNtf>(data);
                }
                case EProtoCode.PlayerInfoInitAck:
                {
                    return MessagePackSerializer.Deserialize<PlayerInfoInitAck>(data);
                }
                case EProtoCode.PlayerInfoInitReq:
                {
                    return MessagePackSerializer.Deserialize<PlayerInfoInitReq>(data);
                }
                case EProtoCode.PlayerWorldCompDataNtf:
                {
                    return MessagePackSerializer.Deserialize<PlayerWorldCompDataNtf>(data);
                }
                case EProtoCode.ServerDisconnectNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerDisconnectNtf>(data);
                }
                case EProtoCode.HakoniwaMonsterOneHitAck:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaMonsterOneHitAck>(data);
                }
                case EProtoCode.HakoniwaMonsterOneHitReq:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaMonsterOneHitReq>(data);
                }
                case EProtoCode.CheckConsistentDigestNtf:
                {
                    return MessagePackSerializer.Deserialize<CheckConsistentDigestNtf>(data);
                }
                case EProtoCode.ConsistentDigestDiffNtf:
                {
                    return MessagePackSerializer.Deserialize<ConsistentDigestDiffNtf>(data);
                }
                case EProtoCode.ForwardClientMessageToServerNtf:
                {
                    return MessagePackSerializer.Deserialize<ForwardClientMessageToServerNtf>(data);
                }
                case EProtoCode.ForwardClientMsgBroadcastNtf:
                {
                    return MessagePackSerializer.Deserialize<ForwardClientMsgBroadcastNtf>(data);
                }
                case EProtoCode.ForwardMessageToClientNtf:
                {
                    return MessagePackSerializer.Deserialize<ForwardMessageToClientNtf>(data);
                }
                case EProtoCode.MsgPack_C_CallBasePlayerMethod_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallBasePlayerMethod_Proto>(data);
                }
                case EProtoCode.MsgPack_C_CallEntityMethod_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallEntityMethod_Proto>(data);
                }
                case EProtoCode.MsgPack_S_BasicEntityCreate_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_BasicEntityCreate_Proto>(data);
                }
                case EProtoCode.MsgPack_S_CallClientMethod_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_CallClientMethod_Proto>(data);
                }
                case EProtoCode.MsgPack_S_EngineRpc_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EngineRpc_Proto>(data);
                }
                case EProtoCode.MsgPack_S_EntityCreawte_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityCreawte_Proto>(data);
                }
                case EProtoCode.MsgPack_S_EntityDestroy_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityDestroy_Proto>(data);
                }
                case EProtoCode.MsgPack_S_EntityMigrateReply_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrateReply_Proto>(data);
                }
                case EProtoCode.MsgPack_S_EntityMigrate_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrate_Proto>(data);
                }
                case EProtoCode.MsgPack_S_ForwardEntityRpc_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ForwardEntityRpc_Proto>(data);
                }
                case EProtoCode.MsgPack_S_RpcToNode_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToNode_Proto>(data);
                }
                case EProtoCode.MsgPack_S_RpcToPlayerRouter_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayerRouter_Proto>(data);
                }
                case EProtoCode.MsgPack_S_RpcToPlayer_Proto:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayer_Proto>(data);
                }
                case EProtoCode.MsgPack_S_Rpc_Ack:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Ack>(data);
                }
                case EProtoCode.MsgPack_S_Rpc_Req:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Req>(data);
                }
                case EProtoCode.RegisterMailBoxAck:
                {
                    return MessagePackSerializer.Deserialize<RegisterMailBoxAck>(data);
                }
                case EProtoCode.RegisterMailBoxReq:
                {
                    return MessagePackSerializer.Deserialize<RegisterMailBoxReq>(data);
                }
                case EProtoCode.RpcByMailBoxReq:
                {
                    return MessagePackSerializer.Deserialize<RpcByMailBoxReq>(data);
                }
                case EProtoCode.RpcShakeHandAck:
                {
                    return MessagePackSerializer.Deserialize<RpcShakeHandAck>(data);
                }
                case EProtoCode.RpcShakeHandReq:
                {
                    return MessagePackSerializer.Deserialize<RpcShakeHandReq>(data);
                }
                case EProtoCode.ServerBroadcastDataNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerBroadcastDataNtf>(data);
                }
                case EProtoCode.ServerNodeHeartbeatAck:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeHeartbeatAck>(data);
                }
                case EProtoCode.ServerNodeHeartbeatReq:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeHeartbeatReq>(data);
                }
                case EProtoCode.ServerNodeJoinNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeJoinNtf>(data);
                }
                case EProtoCode.ServerNodeLeaveNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeLeaveNtf>(data);
                }
                case EProtoCode.ServerNodeReadyNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodeReadyNtf>(data);
                }
                case EProtoCode.ServerNodesJoinNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodesJoinNtf>(data);
                }
                case EProtoCode.ServerNodesReadyNtf:
                {
                    return MessagePackSerializer.Deserialize<ServerNodesReadyNtf>(data);
                }
                case EProtoCode.TransUpdateConsistentHashAck:
                {
                    return MessagePackSerializer.Deserialize<TransUpdateConsistentHashAck>(data);
                }
                case EProtoCode.TransUpdateConsistentHashReq:
                {
                    return MessagePackSerializer.Deserialize<TransUpdateConsistentHashReq>(data);
                }
                case EProtoCode.UnregisterMailBoxAck:
                {
                    return MessagePackSerializer.Deserialize<UnregisterMailBoxAck>(data);
                }
                case EProtoCode.UnregisterMailBoxReq:
                {
                    return MessagePackSerializer.Deserialize<UnregisterMailBoxReq>(data);
                }
                case EProtoCode.UpdateConsistentHashNtf:
                {
                    return MessagePackSerializer.Deserialize<UpdateConsistentHashNtf>(data);
                }
                default:
                {
                    return null;
                }
            }
        }

        public static void Serialize(Stream stream, MsgPackStructBase data)
        {
            switch (data.ProtoCode)
            {
                case EProtoCode.BandwidthTestAck:
                {
                    MessagePackSerializer.Serialize(stream, data as BandwidthTestAck);
                }
                break;
                case EProtoCode.BandwidthTestReq:
                {
                    MessagePackSerializer.Serialize(stream, data as BandwidthTestReq);
                }
                break;
                case EProtoCode.BattleCancelAck:
                {
                    MessagePackSerializer.Serialize(stream, data as BattleCancelAck);
                }
                break;
                case EProtoCode.BattleCancelReq:
                {
                    MessagePackSerializer.Serialize(stream, data as BattleCancelReq);
                }
                break;
                case EProtoCode.CharacterCreateAck:
                {
                    MessagePackSerializer.Serialize(stream, data as CharacterCreateAck);
                }
                break;
                case EProtoCode.CharacterCreateReq:
                {
                    MessagePackSerializer.Serialize(stream, data as CharacterCreateReq);
                }
                break;
                case EProtoCode.HakoniwaBattleFinishAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaBattleFinishAck);
                }
                break;
                case EProtoCode.HakoniwaBattleFinishReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaBattleFinishReq);
                }
                break;
                case EProtoCode.HakoniwaBattleStartAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaBattleStartAck);
                }
                break;
                case EProtoCode.HakoniwaBattleStartReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaBattleStartReq);
                }
                break;
                case EProtoCode.HakoniwaDialogCompleteAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaDialogCompleteAck);
                }
                break;
                case EProtoCode.HakoniwaDialogCompleteReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaDialogCompleteReq);
                }
                break;
                case EProtoCode.HakoniwaEnterAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaEnterAck);
                }
                break;
                case EProtoCode.HakoniwaEnterReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaEnterReq);
                }
                break;
                case EProtoCode.HakoniwaFinishNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaFinishNtf);
                }
                break;
                case EProtoCode.HakoniwaGiveUpAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaGiveUpAck);
                }
                break;
                case EProtoCode.HakoniwaGiveUpReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaGiveUpReq);
                }
                break;
                case EProtoCode.HakoniwaPosSyncAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaPosSyncAck);
                }
                break;
                case EProtoCode.HakoniwaPosSyncReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaPosSyncReq);
                }
                break;
                case EProtoCode.HakoniwaQuestCondChangeNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaQuestCondChangeNtf);
                }
                break;
                case EProtoCode.HakoniwaQuestStateChangeNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaQuestStateChangeNtf);
                }
                break;
                case EProtoCode.HakoniwaReachPointAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaReachPointAck);
                }
                break;
                case EProtoCode.HakoniwaReachPointReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaReachPointReq);
                }
                break;
                case EProtoCode.HakoniwaTreasureBoxOpenAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaTreasureBoxOpenAck);
                }
                break;
                case EProtoCode.HakoniwaTreasureBoxOpenReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaTreasureBoxOpenReq);
                }
                break;
                case EProtoCode.LoginByAuthTokenAck:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginByAuthTokenAck);
                }
                break;
                case EProtoCode.LoginByAuthTokenReq:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginByAuthTokenReq);
                }
                break;
                case EProtoCode.LoginBySessionTokenAck:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginBySessionTokenAck);
                }
                break;
                case EProtoCode.LoginBySessionTokenReq:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginBySessionTokenReq);
                }
                break;
                case EProtoCode.MsgPack_BagInfo_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BagInfo_Ack);
                }
                break;
                case EProtoCode.MsgPack_BagInfo_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BagInfo_Req);
                }
                break;
                case EProtoCode.MsgPack_BattleCommand_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleCommand_Ack);
                }
                break;
                case EProtoCode.MsgPack_BattleCommand_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleCommand_Ntf);
                }
                break;
                case EProtoCode.MsgPack_BattleCommand_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleCommand_Req);
                }
                break;
                case EProtoCode.MsgPack_BattleVerify_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleVerify_Ack);
                }
                break;
                case EProtoCode.MsgPack_BattleVerify_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleVerify_Req);
                }
                break;
                case EProtoCode.MsgPack_DuelBattleSessionEnd_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelBattleSessionEnd_Ntf);
                }
                break;
                case EProtoCode.MsgPack_DuelBattleSessionInit_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelBattleSessionInit_Ntf);
                }
                break;
                case EProtoCode.MsgPack_DuelReply_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelReply_Ack);
                }
                break;
                case EProtoCode.MsgPack_DuelReply_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelReply_Ntf);
                }
                break;
                case EProtoCode.MsgPack_DuelReply_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelReply_Req);
                }
                break;
                case EProtoCode.MsgPack_Duel_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Duel_Ack);
                }
                break;
                case EProtoCode.MsgPack_Duel_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Duel_Ntf);
                }
                break;
                case EProtoCode.MsgPack_Duel_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Duel_Req);
                }
                break;
                case EProtoCode.MsgPack_Echo_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Echo_Ack);
                }
                break;
                case EProtoCode.MsgPack_Echo_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Echo_Req);
                }
                break;
                case EProtoCode.MsgPack_ExampleData_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ExampleData_Ntf);
                }
                break;
                case EProtoCode.MsgPack_ExampleExternal_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ExampleExternal_Ack);
                }
                break;
                case EProtoCode.MsgPack_ExampleExternal_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ExampleExternal_Req);
                }
                break;
                case EProtoCode.MsgPack_Example_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Example_Ack);
                }
                break;
                case EProtoCode.MsgPack_Example_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Example_Req);
                }
                break;
                case EProtoCode.MsgPack_HeartBeat_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_HeartBeat_Ack);
                }
                break;
                case EProtoCode.MsgPack_HeartBeat_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_HeartBeat_Req);
                }
                break;
                case EProtoCode.MsgPack_Logout_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Logout_Req);
                }
                break;
                case EProtoCode.MsgPack_ServerTime_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ServerTime_Ack);
                }
                break;
                case EProtoCode.MsgPack_ServerTime_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ServerTime_Req);
                }
                break;
                case EProtoCode.MsgPack_S_DuelReply_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_DuelReply_Ntf);
                }
                break;
                case EProtoCode.MsgPack_S_Duel_Ntf:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_Duel_Ntf);
                }
                break;
                case EProtoCode.MsgPack_Test_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Test_Ack);
                }
                break;
                case EProtoCode.MsgPack_Test_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Test_Req);
                }
                break;
                case EProtoCode.PlayerBasicCompDataNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerBasicCompDataNtf);
                }
                break;
                case EProtoCode.PlayerBattleCompDataNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerBattleCompDataNtf);
                }
                break;
                case EProtoCode.PlayerDataSectionSyncEndNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerDataSectionSyncEndNtf);
                }
                break;
                case EProtoCode.PlayerHakoniwaCompDataNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerHakoniwaCompDataNtf);
                }
                break;
                case EProtoCode.PlayerInfoInitAck:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerInfoInitAck);
                }
                break;
                case EProtoCode.PlayerInfoInitReq:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerInfoInitReq);
                }
                break;
                case EProtoCode.PlayerWorldCompDataNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerWorldCompDataNtf);
                }
                break;
                case EProtoCode.ServerDisconnectNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerDisconnectNtf);
                }
                break;
                case EProtoCode.HakoniwaMonsterOneHitAck:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaMonsterOneHitAck);
                }
                break;
                case EProtoCode.HakoniwaMonsterOneHitReq:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaMonsterOneHitReq);
                }
                break;
                case EProtoCode.CheckConsistentDigestNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as CheckConsistentDigestNtf);
                }
                break;
                case EProtoCode.ConsistentDigestDiffNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ConsistentDigestDiffNtf);
                }
                break;
                case EProtoCode.ForwardClientMessageToServerNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ForwardClientMessageToServerNtf);
                }
                break;
                case EProtoCode.ForwardClientMsgBroadcastNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ForwardClientMsgBroadcastNtf);
                }
                break;
                case EProtoCode.ForwardMessageToClientNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ForwardMessageToClientNtf);
                }
                break;
                case EProtoCode.MsgPack_C_CallBasePlayerMethod_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_C_CallBasePlayerMethod_Proto);
                }
                break;
                case EProtoCode.MsgPack_C_CallEntityMethod_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_C_CallEntityMethod_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_BasicEntityCreate_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_BasicEntityCreate_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_CallClientMethod_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_CallClientMethod_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_EngineRpc_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EngineRpc_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_EntityCreawte_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityCreawte_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_EntityDestroy_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityDestroy_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_EntityMigrateReply_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityMigrateReply_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_EntityMigrate_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityMigrate_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_ForwardEntityRpc_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_ForwardEntityRpc_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_RpcToNode_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcToNode_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_RpcToPlayerRouter_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcToPlayerRouter_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_RpcToPlayer_Proto:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcToPlayer_Proto);
                }
                break;
                case EProtoCode.MsgPack_S_Rpc_Ack:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_Rpc_Ack);
                }
                break;
                case EProtoCode.MsgPack_S_Rpc_Req:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_Rpc_Req);
                }
                break;
                case EProtoCode.RegisterMailBoxAck:
                {
                    MessagePackSerializer.Serialize(stream, data as RegisterMailBoxAck);
                }
                break;
                case EProtoCode.RegisterMailBoxReq:
                {
                    MessagePackSerializer.Serialize(stream, data as RegisterMailBoxReq);
                }
                break;
                case EProtoCode.RpcByMailBoxReq:
                {
                    MessagePackSerializer.Serialize(stream, data as RpcByMailBoxReq);
                }
                break;
                case EProtoCode.RpcShakeHandAck:
                {
                    MessagePackSerializer.Serialize(stream, data as RpcShakeHandAck);
                }
                break;
                case EProtoCode.RpcShakeHandReq:
                {
                    MessagePackSerializer.Serialize(stream, data as RpcShakeHandReq);
                }
                break;
                case EProtoCode.ServerBroadcastDataNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerBroadcastDataNtf);
                }
                break;
                case EProtoCode.ServerNodeHeartbeatAck:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodeHeartbeatAck);
                }
                break;
                case EProtoCode.ServerNodeHeartbeatReq:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodeHeartbeatReq);
                }
                break;
                case EProtoCode.ServerNodeJoinNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodeJoinNtf);
                }
                break;
                case EProtoCode.ServerNodeLeaveNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodeLeaveNtf);
                }
                break;
                case EProtoCode.ServerNodeReadyNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodeReadyNtf);
                }
                break;
                case EProtoCode.ServerNodesJoinNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodesJoinNtf);
                }
                break;
                case EProtoCode.ServerNodesReadyNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerNodesReadyNtf);
                }
                break;
                case EProtoCode.TransUpdateConsistentHashAck:
                {
                    MessagePackSerializer.Serialize(stream, data as TransUpdateConsistentHashAck);
                }
                break;
                case EProtoCode.TransUpdateConsistentHashReq:
                {
                    MessagePackSerializer.Serialize(stream, data as TransUpdateConsistentHashReq);
                }
                break;
                case EProtoCode.UnregisterMailBoxAck:
                {
                    MessagePackSerializer.Serialize(stream, data as UnregisterMailBoxAck);
                }
                break;
                case EProtoCode.UnregisterMailBoxReq:
                {
                    MessagePackSerializer.Serialize(stream, data as UnregisterMailBoxReq);
                }
                break;
                case EProtoCode.UpdateConsistentHashNtf:
                {
                    MessagePackSerializer.Serialize(stream, data as UpdateConsistentHashNtf);
                }
                break;
                default:
                {
                    return;
                }
            }
        }

        public static byte[] Serialize(MsgPackStructBase data)
        {
            switch (data.ProtoCode)
            {
                case EProtoCode.BandwidthTestAck:
                {
                    return MessagePackSerializer.Serialize(data as BandwidthTestAck);
                }
                case EProtoCode.BandwidthTestReq:
                {
                    return MessagePackSerializer.Serialize(data as BandwidthTestReq);
                }
                case EProtoCode.BattleCancelAck:
                {
                    return MessagePackSerializer.Serialize(data as BattleCancelAck);
                }
                case EProtoCode.BattleCancelReq:
                {
                    return MessagePackSerializer.Serialize(data as BattleCancelReq);
                }
                case EProtoCode.CharacterCreateAck:
                {
                    return MessagePackSerializer.Serialize(data as CharacterCreateAck);
                }
                case EProtoCode.CharacterCreateReq:
                {
                    return MessagePackSerializer.Serialize(data as CharacterCreateReq);
                }
                case EProtoCode.HakoniwaBattleFinishAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaBattleFinishAck);
                }
                case EProtoCode.HakoniwaBattleFinishReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaBattleFinishReq);
                }
                case EProtoCode.HakoniwaBattleStartAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaBattleStartAck);
                }
                case EProtoCode.HakoniwaBattleStartReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaBattleStartReq);
                }
                case EProtoCode.HakoniwaDialogCompleteAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaDialogCompleteAck);
                }
                case EProtoCode.HakoniwaDialogCompleteReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaDialogCompleteReq);
                }
                case EProtoCode.HakoniwaEnterAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaEnterAck);
                }
                case EProtoCode.HakoniwaEnterReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaEnterReq);
                }
                case EProtoCode.HakoniwaFinishNtf:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaFinishNtf);
                }
                case EProtoCode.HakoniwaGiveUpAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaGiveUpAck);
                }
                case EProtoCode.HakoniwaGiveUpReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaGiveUpReq);
                }
                case EProtoCode.HakoniwaPosSyncAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaPosSyncAck);
                }
                case EProtoCode.HakoniwaPosSyncReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaPosSyncReq);
                }
                case EProtoCode.HakoniwaQuestCondChangeNtf:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaQuestCondChangeNtf);
                }
                case EProtoCode.HakoniwaQuestStateChangeNtf:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaQuestStateChangeNtf);
                }
                case EProtoCode.HakoniwaReachPointAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaReachPointAck);
                }
                case EProtoCode.HakoniwaReachPointReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaReachPointReq);
                }
                case EProtoCode.HakoniwaTreasureBoxOpenAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaTreasureBoxOpenAck);
                }
                case EProtoCode.HakoniwaTreasureBoxOpenReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaTreasureBoxOpenReq);
                }
                case EProtoCode.LoginByAuthTokenAck:
                {
                    return MessagePackSerializer.Serialize(data as LoginByAuthTokenAck);
                }
                case EProtoCode.LoginByAuthTokenReq:
                {
                    return MessagePackSerializer.Serialize(data as LoginByAuthTokenReq);
                }
                case EProtoCode.LoginBySessionTokenAck:
                {
                    return MessagePackSerializer.Serialize(data as LoginBySessionTokenAck);
                }
                case EProtoCode.LoginBySessionTokenReq:
                {
                    return MessagePackSerializer.Serialize(data as LoginBySessionTokenReq);
                }
                case EProtoCode.MsgPack_BagInfo_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BagInfo_Ack);
                }
                case EProtoCode.MsgPack_BagInfo_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BagInfo_Req);
                }
                case EProtoCode.MsgPack_BattleCommand_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleCommand_Ack);
                }
                case EProtoCode.MsgPack_BattleCommand_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleCommand_Ntf);
                }
                case EProtoCode.MsgPack_BattleCommand_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleCommand_Req);
                }
                case EProtoCode.MsgPack_BattleVerify_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleVerify_Ack);
                }
                case EProtoCode.MsgPack_BattleVerify_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleVerify_Req);
                }
                case EProtoCode.MsgPack_DuelBattleSessionEnd_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelBattleSessionEnd_Ntf);
                }
                case EProtoCode.MsgPack_DuelBattleSessionInit_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelBattleSessionInit_Ntf);
                }
                case EProtoCode.MsgPack_DuelReply_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelReply_Ack);
                }
                case EProtoCode.MsgPack_DuelReply_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelReply_Ntf);
                }
                case EProtoCode.MsgPack_DuelReply_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelReply_Req);
                }
                case EProtoCode.MsgPack_Duel_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Duel_Ack);
                }
                case EProtoCode.MsgPack_Duel_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Duel_Ntf);
                }
                case EProtoCode.MsgPack_Duel_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Duel_Req);
                }
                case EProtoCode.MsgPack_Echo_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Echo_Ack);
                }
                case EProtoCode.MsgPack_Echo_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Echo_Req);
                }
                case EProtoCode.MsgPack_ExampleData_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ExampleData_Ntf);
                }
                case EProtoCode.MsgPack_ExampleExternal_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ExampleExternal_Ack);
                }
                case EProtoCode.MsgPack_ExampleExternal_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ExampleExternal_Req);
                }
                case EProtoCode.MsgPack_Example_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Example_Ack);
                }
                case EProtoCode.MsgPack_Example_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Example_Req);
                }
                case EProtoCode.MsgPack_HeartBeat_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_HeartBeat_Ack);
                }
                case EProtoCode.MsgPack_HeartBeat_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_HeartBeat_Req);
                }
                case EProtoCode.MsgPack_Logout_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Logout_Req);
                }
                case EProtoCode.MsgPack_ServerTime_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ServerTime_Ack);
                }
                case EProtoCode.MsgPack_ServerTime_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ServerTime_Req);
                }
                case EProtoCode.MsgPack_S_DuelReply_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_DuelReply_Ntf);
                }
                case EProtoCode.MsgPack_S_Duel_Ntf:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_Duel_Ntf);
                }
                case EProtoCode.MsgPack_Test_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Test_Ack);
                }
                case EProtoCode.MsgPack_Test_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Test_Req);
                }
                case EProtoCode.PlayerBasicCompDataNtf:
                {
                    return MessagePackSerializer.Serialize(data as PlayerBasicCompDataNtf);
                }
                case EProtoCode.PlayerBattleCompDataNtf:
                {
                    return MessagePackSerializer.Serialize(data as PlayerBattleCompDataNtf);
                }
                case EProtoCode.PlayerDataSectionSyncEndNtf:
                {
                    return MessagePackSerializer.Serialize(data as PlayerDataSectionSyncEndNtf);
                }
                case EProtoCode.PlayerHakoniwaCompDataNtf:
                {
                    return MessagePackSerializer.Serialize(data as PlayerHakoniwaCompDataNtf);
                }
                case EProtoCode.PlayerInfoInitAck:
                {
                    return MessagePackSerializer.Serialize(data as PlayerInfoInitAck);
                }
                case EProtoCode.PlayerInfoInitReq:
                {
                    return MessagePackSerializer.Serialize(data as PlayerInfoInitReq);
                }
                case EProtoCode.PlayerWorldCompDataNtf:
                {
                    return MessagePackSerializer.Serialize(data as PlayerWorldCompDataNtf);
                }
                case EProtoCode.ServerDisconnectNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerDisconnectNtf);
                }
                case EProtoCode.HakoniwaMonsterOneHitAck:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaMonsterOneHitAck);
                }
                case EProtoCode.HakoniwaMonsterOneHitReq:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaMonsterOneHitReq);
                }
                case EProtoCode.CheckConsistentDigestNtf:
                {
                    return MessagePackSerializer.Serialize(data as CheckConsistentDigestNtf);
                }
                case EProtoCode.ConsistentDigestDiffNtf:
                {
                    return MessagePackSerializer.Serialize(data as ConsistentDigestDiffNtf);
                }
                case EProtoCode.ForwardClientMessageToServerNtf:
                {
                    return MessagePackSerializer.Serialize(data as ForwardClientMessageToServerNtf);
                }
                case EProtoCode.ForwardClientMsgBroadcastNtf:
                {
                    return MessagePackSerializer.Serialize(data as ForwardClientMsgBroadcastNtf);
                }
                case EProtoCode.ForwardMessageToClientNtf:
                {
                    return MessagePackSerializer.Serialize(data as ForwardMessageToClientNtf);
                }
                case EProtoCode.MsgPack_C_CallBasePlayerMethod_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_C_CallBasePlayerMethod_Proto);
                }
                case EProtoCode.MsgPack_C_CallEntityMethod_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_C_CallEntityMethod_Proto);
                }
                case EProtoCode.MsgPack_S_BasicEntityCreate_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_BasicEntityCreate_Proto);
                }
                case EProtoCode.MsgPack_S_CallClientMethod_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_CallClientMethod_Proto);
                }
                case EProtoCode.MsgPack_S_EngineRpc_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EngineRpc_Proto);
                }
                case EProtoCode.MsgPack_S_EntityCreawte_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityCreawte_Proto);
                }
                case EProtoCode.MsgPack_S_EntityDestroy_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityDestroy_Proto);
                }
                case EProtoCode.MsgPack_S_EntityMigrateReply_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityMigrateReply_Proto);
                }
                case EProtoCode.MsgPack_S_EntityMigrate_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityMigrate_Proto);
                }
                case EProtoCode.MsgPack_S_ForwardEntityRpc_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_ForwardEntityRpc_Proto);
                }
                case EProtoCode.MsgPack_S_RpcToNode_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcToNode_Proto);
                }
                case EProtoCode.MsgPack_S_RpcToPlayerRouter_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcToPlayerRouter_Proto);
                }
                case EProtoCode.MsgPack_S_RpcToPlayer_Proto:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcToPlayer_Proto);
                }
                case EProtoCode.MsgPack_S_Rpc_Ack:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_Rpc_Ack);
                }
                case EProtoCode.MsgPack_S_Rpc_Req:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_Rpc_Req);
                }
                case EProtoCode.RegisterMailBoxAck:
                {
                    return MessagePackSerializer.Serialize(data as RegisterMailBoxAck);
                }
                case EProtoCode.RegisterMailBoxReq:
                {
                    return MessagePackSerializer.Serialize(data as RegisterMailBoxReq);
                }
                case EProtoCode.RpcByMailBoxReq:
                {
                    return MessagePackSerializer.Serialize(data as RpcByMailBoxReq);
                }
                case EProtoCode.RpcShakeHandAck:
                {
                    return MessagePackSerializer.Serialize(data as RpcShakeHandAck);
                }
                case EProtoCode.RpcShakeHandReq:
                {
                    return MessagePackSerializer.Serialize(data as RpcShakeHandReq);
                }
                case EProtoCode.ServerBroadcastDataNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerBroadcastDataNtf);
                }
                case EProtoCode.ServerNodeHeartbeatAck:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodeHeartbeatAck);
                }
                case EProtoCode.ServerNodeHeartbeatReq:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodeHeartbeatReq);
                }
                case EProtoCode.ServerNodeJoinNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodeJoinNtf);
                }
                case EProtoCode.ServerNodeLeaveNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodeLeaveNtf);
                }
                case EProtoCode.ServerNodeReadyNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodeReadyNtf);
                }
                case EProtoCode.ServerNodesJoinNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodesJoinNtf);
                }
                case EProtoCode.ServerNodesReadyNtf:
                {
                    return MessagePackSerializer.Serialize(data as ServerNodesReadyNtf);
                }
                case EProtoCode.TransUpdateConsistentHashAck:
                {
                    return MessagePackSerializer.Serialize(data as TransUpdateConsistentHashAck);
                }
                case EProtoCode.TransUpdateConsistentHashReq:
                {
                    return MessagePackSerializer.Serialize(data as TransUpdateConsistentHashReq);
                }
                case EProtoCode.UnregisterMailBoxAck:
                {
                    return MessagePackSerializer.Serialize(data as UnregisterMailBoxAck);
                }
                case EProtoCode.UnregisterMailBoxReq:
                {
                    return MessagePackSerializer.Serialize(data as UnregisterMailBoxReq);
                }
                case EProtoCode.UpdateConsistentHashNtf:
                {
                    return MessagePackSerializer.Serialize(data as UpdateConsistentHashNtf);
                }
                default:
                {
                    return null;
                }
            }
        }

        private static readonly Dictionary<Type, int> s_type2ProtoId = new();
        private static readonly Dictionary<int, Type> s_protoId2Type = new();
        private static readonly Dictionary<int, Type> s_clientProtoId2Type = new();
        private static readonly HashSet<int> s_protoIdsFromClient = new();
     }
}
