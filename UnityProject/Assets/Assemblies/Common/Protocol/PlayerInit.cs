// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class PlayerInfoInitReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerInfoInitReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [IgnoreMember]
       public static readonly PlayerInfoInitReq Shared = new ();
    }
    
    [MessagePackObject]
    public partial class PlayerInfoInitAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerInfoInitAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
    }
    
    [MessagePackObject]
    public partial class CharacterCreateReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.CharacterCreateReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public string NickName { get; set; }
    }
    
    [MessagePackObject]
    public partial class CharacterCreateAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.CharacterCreateAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class PlayerCompDataBasicInfo : DBCacheObject
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("Name")]
       #endif
       public string Name { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("Lv")]
       #endif
       public int Level { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("Exp")]
       #endif
       public int Exp { get; set; }
       [Key(3)]
       #if PHOENIX_SERVER
       [BsonElement("PlayerId")]
       #endif
       public long PlayerId { get; set; }
       [Key(4)]
       #if PHOENIX_SERVER
       [BsonElement("Gender")]
       #endif
       public int Gender { get; set; }
    }
    
    [MessagePackObject]
    public partial class PlayerBasicCompDataNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerBasicCompDataNtf;
       }
       [Key(2)]
       public PlayerCompDataBasicInfo BasicInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class PlayerDataSectionSyncEndNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerDataSectionSyncEndNtf;
       }
       [IgnoreMember]
       public static readonly PlayerDataSectionSyncEndNtf Shared = new ();
    }
    
}
