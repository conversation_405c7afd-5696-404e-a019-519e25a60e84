// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class PosInfo
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("X")]
       #endif
       public float X { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("Y")]
       #endif
       public float Y { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("Z")]
       #endif
       public float Z { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class HakoniwaQuestCondInfo
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("Index")]
       #endif
       public int Index { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("Progress")]
       #endif
       public int Progress { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("IsCompleted")]
       #endif
       public bool IsCompleted { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class HakoniwaQuestInfo
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("QuestId")]
       #endif
       public int QuestId { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("QuestState")]
       #endif
       public int QuestState { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("QuestCondInfos")]
       #endif
       public List<HakoniwaQuestCondInfo> QuestCondInfos { get; set; } = new();
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    [BsonKnownTypes(typeof(HakoniwaEntityGear))]
    [BsonKnownTypes(typeof(HakoniwaEntityMonster))]
    #endif
    [MessagePackObject]
    [Union(0, typeof(HakoniwaEntityGear))]
    [Union(1, typeof(HakoniwaEntityMonster))]
    public abstract partial class HakoniwaEntity
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("EntityId")]
       #endif
       public int EntityId { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class HakoniwaEntityGear : HakoniwaEntity
    {
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("State")]
       #endif
       public int State { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class HakoniwaEntityMonster : HakoniwaEntity
    {
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("IsDead")]
       #endif
       public bool IsDead { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class ExploringHakoniwaInfo
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("HakoniwaId")]
       #endif
       public int HakoniwaId { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("QuestInfos")]
       #endif
       public List<HakoniwaQuestInfo> QuestInfos { get; set; } = new();
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("CompletedQuestIds")]
       #endif
       public List<int> CompletedQuestIds { get; set; } = new();
       [Key(3)]
       #if PHOENIX_SERVER
       [BsonElement("CreateTime")]
       [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
       #endif
       public DateTime CreateTime { get; set; }
       [Key(4)]
       #if PHOENIX_SERVER
       [BsonElement("UnlockWayPointIdIds")]
       #endif
       public List<int> UnlockWayPointIdIds { get; set; } = new();
       [Key(5)]
       #if PHOENIX_SERVER
       [BsonElement("SceneId")]
       #endif
       public int SceneId { get; set; }
       [Key(6)]
       #if PHOENIX_SERVER
       [BsonElement("PosInfo")]
       #endif
       public PosInfo PosInfo { get; set; }
       [Key(7)]
       #if PHOENIX_SERVER
       [BsonElement("Entities")]
       #endif
       public List<HakoniwaEntity> Entities { get; set; } = new();
       [Key(8)]
       #if PHOENIX_SERVER
       [BsonElement("CompletedLevelIds")]
       #endif
       public List<int> CompletedLevelIds { get; set; } = new();
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class PlayerCompDataHakoniwaInfo : DBCacheObject
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("CurrEnterHakoniwaId")]
       #endif
       public int CurrEnterHakoniwaId { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("ExploringHakoniwaList")]
       #endif
       public List<ExploringHakoniwaInfo> ExploringHakoniwaList { get; set; } = new();
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("CompletedHakoniwaIds")]
       #endif
       public List<int> CompletedHakoniwaIds { get; set; } = new();
       [Key(3)]
       #if PHOENIX_SERVER
       [BsonElement("OpenTreasureBoxIds")]
       #endif
       public List<int> OpenTreasureBoxIds { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class PlayerHakoniwaCompDataNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerHakoniwaCompDataNtf;
       }
       [Key(2)]
       public PlayerCompDataHakoniwaInfo HakoniwaInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaEnterReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaEnterReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int EnterWayPointId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaEnterAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaEnterAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int EnterWayPointId { get; set; }
       [Key(6)]
       public ExploringHakoniwaInfo FirstEnterHakoniwaInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaGiveUpReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaGiveUpReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaGiveUpAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaGiveUpAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaQuestStateChangeNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaQuestStateChangeNtf;
       }
       [Key(2)]
       public int HakoniwaId { get; set; }
       [Key(3)]
       public int QuestId { get; set; }
       [Key(4)]
       public int QuestState { get; set; }
       [Key(5)]
       public List<HakoniwaQuestInfo> NextQuestInfos { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class HakoniwaQuestCondChangeNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaQuestCondChangeNtf;
       }
       [Key(2)]
       public int HakoniwaId { get; set; }
       [Key(3)]
       public int QuestId { get; set; }
       [Key(4)]
       public HakoniwaQuestCondInfo CondInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaFinishNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaFinishNtf;
       }
       [Key(2)]
       public int HakoniwaId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaDialogCompleteReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaDialogCompleteReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int DialogId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaDialogCompleteAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaDialogCompleteAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int DialogId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaTreasureBoxOpenReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaTreasureBoxOpenReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int TreasureBoxId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaTreasureBoxOpenAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaTreasureBoxOpenAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int TreasureBoxId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaBattleStartReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaBattleStartReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int LevelId { get; set; }
       [Key(5)]
       public HakoniwaBattleMonsterInfo MonsterInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaBattleStartAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaBattleStartAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int LevelId { get; set; }
       [Key(6)]
       public BattleProcessInfo BattleProcessInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaBattleFinishReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaBattleFinishReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int LevelId { get; set; }
       [Key(5)]
       public HakoniwaBattleMonsterInfo MonsterInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaBattleFinishAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaBattleFinishAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int LevelId { get; set; }
       [Key(6)]
       public List<HakoniwaEntityMonster> KilledMonsters { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class HakoniwaReachPointReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaReachPointReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int QuestId { get; set; }
       [Key(5)]
       public int QuestCondIndex { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaReachPointAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaReachPointAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int QuestId { get; set; }
       [Key(6)]
       public int QuestCondIndex { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaPosSyncReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaPosSyncReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int SceneId { get; set; }
       [Key(5)]
       public PosInfo PosInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaPosSyncAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaPosSyncAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int SceneId { get; set; }
       [Key(6)]
       public PosInfo PosInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaMonsterOneHitReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaMonsterOneHitReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int HakoniwaId { get; set; }
       [Key(4)]
       public int MonsterId { get; set; }
    }
    
    [MessagePackObject]
    public partial class HakoniwaMonsterOneHitAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.HakoniwaMonsterOneHitAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public int HakoniwaId { get; set; }
       [Key(5)]
       public int MonsterId { get; set; }
    }
    
}
