// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class MsgPack_C_CallBasePlayerMethod_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_C_CallBasePlayerMethod_Proto;
       }
       [Key(2)]
       public string Func { get; set; } = "";
       [Key(3)]
       public byte[] Args { get; set; } = null;
       [Key(4)]
       public int FuncIndex { get; set; } = 0;
    }
    
    [MessagePackObject]
    public partial class MsgPack_C_CallEntityMethod_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_C_CallEntityMethod_Proto;
       }
       [Key(2)]
       public long EntityId { get; set; } = 0;
       [Key(3)]
       public string Func { get; set; } = "";
       [Key(4)]
       public byte[] Args { get; set; } = null;
       [Key(5)]
       public int FuncIndex { get; set; } = 0;
       [Key(6)]
       public sbyte ForwardType { get; set; } = 0;
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_BasicEntityCreate_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_BasicEntityCreate_Proto;
       }
       [Key(2)]
       public long EntityId { get; set; } = 0;
       [Key(3)]
       public string Classname { get; set; } = "";
       [Key(4)]
       public long PrefabId { get; set; } = 0;
       [Key(5)]
       public byte[] Initdata { get; set; } = null;
       [Key(6)]
       public string ModelId { get; set; } = "";
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_CallClientMethod_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_CallClientMethod_Proto;
       }
       [Key(2)]
       public long EntityId { get; set; } = 0;
       [Key(3)]
       public string FuncOrIndex { get; set; } = "";
       [Key(4)]
       public byte[] Args { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_EntityCreawte_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_EntityCreawte_Proto;
       }
       [Key(2)]
       public long EntityId { get; set; } = 0;
       [Key(3)]
       public string Classname { get; set; } = "";
       [Key(4)]
       public byte[] Initdata { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_EntityDestroy_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_EntityDestroy_Proto;
       }
       [Key(2)]
       public long EntityId { get; set; } = 0;
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_EntityMigrate_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_EntityMigrate_Proto;
       }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_EntityMigrateReply_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_EntityMigrateReply_Proto;
       }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_ForwardEntityRpc_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_ForwardEntityRpc_Proto;
       }
       [Key(2)]
       public long EntityId { get; set; } = 0;
       [Key(3)]
       public long Uid { get; set; } = 0;
       [Key(4)]
       public string Funcname { get; set; } = "";
       [Key(5)]
       public byte[] ArgsData { get; set; } = null;
    }
    
}
