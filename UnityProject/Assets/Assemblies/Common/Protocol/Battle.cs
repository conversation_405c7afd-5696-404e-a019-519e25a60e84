// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class MsgPack_BattleVerify_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_BattleVerify_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_BattleVerify_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_BattleVerify_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public sbyte Result { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class PlayerCompDataBattleInfo : DBCacheObject
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("BattleProcessInfo")]
       #endif
       public BattleProcessInfo BattleProcessInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class PlayerBattleCompDataNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerBattleCompDataNtf;
       }
       [Key(2)]
       public PlayerCompDataBattleInfo BattleInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class BattleCancelReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.BattleCancelReq;
       }
    }
    
    [MessagePackObject]
    public partial class BattleCancelAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.BattleCancelAck;
       }
       [Key(2)]
       public int ErrCode { get; set; }
    }
    
}
