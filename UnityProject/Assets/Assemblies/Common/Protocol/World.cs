// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class PlayerWorldCompDataNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.PlayerWorldCompDataNtf;
       }
       [Key(2)]
       public PlayerCompDataWorldInfo WorldInfo { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class PlayerCompDataWorldInfo : DBCacheObject
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("CompletedStoryIds")]
       #endif
       public List<int> CompletedStoryIds { get; set; } = new();
    }
    
}
