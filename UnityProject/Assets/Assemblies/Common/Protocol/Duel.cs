// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class MsgPack_Duel_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Duel_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long UidTarget { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_Duel_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Duel_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long ErrCode { get; set; }
       [Key(4)]
       public long UidTarget { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_Duel_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Duel_Ntf;
       }
       [Key(2)]
       public long SrcUid { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_DuelReply_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_DuelReply_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public bool IsAccept { get; set; }
       [Key(4)]
       public long SrcUid { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_DuelReply_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_DuelReply_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long ErrCode { get; set; }
       [Key(4)]
       public long SrcUid { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_DuelReply_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_DuelReply_Ntf;
       }
       [Key(2)]
       public bool IsAccept { get; set; }
       [Key(3)]
       public long UidTarget { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_DuelBattleSessionInit_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_DuelBattleSessionInit_Ntf;
       }
       [Key(2)]
       public long SessionId { get; set; }
       [Key(3)]
       public int BattleRid { get; set; }
       [Key(4)]
       public int RandomSeed { get; set; }
       [Key(5)]
       public List<ulong> PlayerIds { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class MsgPack_DuelBattleSessionEnd_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_DuelBattleSessionEnd_Ntf;
       }
       [Key(2)]
       public long SessionId { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public long WinnerUid { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_DuelReply_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_DuelReply_Ntf;
       }
       [Key(2)]
       public bool IsAccept { get; set; }
       [Key(3)]
       public long UidTarget { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_Duel_Ntf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_Duel_Ntf;
       }
       [Key(2)]
       public long SrcTarget { get; set; }
    }
    
}
