// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class ForwardClientMessageToServerNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ForwardClientMessageToServerNtf;
       }
       [Key(2)]
       public long PlayerId { get; set; } = 0;
       [Key(3)]
       public uint FromGate { get; set; } = 0;
       [Key(4)]
       public int MsgProtoCode { get; set; }
       [Key(5)]
       public byte[] Data { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class ForwardMessageToClientNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ForwardMessageToClientNtf;
       }
       [Key(2)]
       public long PlayerId { get; set; } = 0;
       [Key(3)]
       public uint SrcHost { get; set; } = 0;
       [Key(4)]
       public int MsgProtoCode { get; set; }
       [Key(5)]
       public byte[] Data { get; set; } = null;
       [Key(6)]
       public byte Flag { get; set; } = 0;
    }
    
    [MessagePackObject]
    public partial class ForwardClientMsgBroadcastNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ForwardClientMsgBroadcastNtf;
       }
       [Key(2)]
       public List<long> PlayerIds { get; set; }
       [Key(3)]
       public int SrcHost { get; set; } = 0;
       [Key(4)]
       public byte[] Data { get; set; } = null;
       [Key(5)]
       public bool IsBroadcastAll { get; set; }
    }
    
}
