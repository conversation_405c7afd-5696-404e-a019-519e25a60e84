// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class MsgPack_HeartBeat_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_HeartBeat_Req;
       }
       [IgnoreMember]
       public static readonly MsgPack_HeartBeat_Req Shared = new ();
    }
    
    [MessagePackObject]
    public partial class MsgPack_HeartBeat_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_HeartBeat_Ack;
       }
       [Key(2)]
       public long CurrentServerTime { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_ServerTime_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_ServerTime_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_ServerTime_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_ServerTime_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long CurrentServerTime { get; set; }
    }
    
}
