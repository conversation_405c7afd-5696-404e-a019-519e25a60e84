// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    public enum ETestEnum
    {
        None = 0,
        Test1 = 1,
        Test2 = 2,
        Test3 = 3,
    }

    [MessagePackObject]
    public partial class MsgPack_Test_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Test_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long UidTarget { get; set; } = 100;
       [Key(4)]
       public long UidTarget1 { get; set; } = 100;
       [Key(5)]
       public List<int> Test { get; set; } = new List<int>();
    }
    
    [MessagePackObject]
    public partial class MsgPack_Test_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Test_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_Echo_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Echo_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public string Content { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_Echo_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_Echo_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public string Content { get; set; }
    }
    
    [MessagePackObject]
    public partial class BandwidthTestReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.BandwidthTestReq;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ClientId { get; set; }
       [Key(4)]
       public long Timestamp { get; set; }
       [Key(5)]
       public byte[] Data { get; set; }
    }
    
    [MessagePackObject]
    public partial class BandwidthTestAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.BandwidthTestAck;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ClientId { get; set; }
       [Key(4)]
       public long Timestamp { get; set; }
       [Key(5)]
       public byte[] Data { get; set; }
    }
    
}
