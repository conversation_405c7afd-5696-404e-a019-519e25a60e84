// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class MsgPack_BagInfo_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_BagInfo_Req;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public long Uid { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_BagInfo_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_BagInfo_Ack;
       }
       public override bool HasToken() => true;
       public override uint? GetToken() => Token;
       public override void SetToken(uint token) => Token = token;
       [Key(2)]
       public uint Token { get; set; }
       [Key(3)]
       public int ErrCode { get; set; }
       [Key(4)]
       public MsgPack_DB_BagData BagData { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class MsgPack_DB_BagData
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("ListItemExample")]
       #endif
       public List<MsgPack_DB_Item> ListItemExample { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("DictItemExample")]
       [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
       #endif
       public Dictionary<string, MsgPack_DB_Item> DictItemExample { get; set; }
    }
    
}
