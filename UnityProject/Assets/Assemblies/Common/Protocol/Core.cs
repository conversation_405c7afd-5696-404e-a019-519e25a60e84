// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    [MessagePackObject]
    public partial class CheckConsistentDigestNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.CheckConsistentDigestNtf;
       }
       [Key(2)]
       public List<ConsistentDigestInfo> Digests { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class ConsistentDigestInfo
    {
       [Key(0)]
       public string Role { get; set; }
       [Key(1)]
       public uint Digest { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_EngineRpc_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_EngineRpc_Proto;
       }
    }
    
    [MessagePackObject]
    public partial class RpcShakeHandReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.RpcShakeHandReq;
       }
       [Key(2)]
       public int Magic { get; set; } = 0;
       [Key(3)]
       public byte[] AuthToken { get; set; } = null;
       [Key(4)]
       public ServerNodeInfo NodeInfo { get; set; } = null;
       [Key(5)]
       public long ProcId { get; set; } = 0;
       [Key(6)]
       public bool IsReady { get; set; }
    }
    
    [MessagePackObject]
    public partial class RpcShakeHandAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.RpcShakeHandAck;
       }
       [Key(2)]
       public uint NodeId { get; set; } = 0;
       [Key(3)]
       public int ErrorCode { get; set; } = 0;
    }
    
    [MessagePackObject]
    public partial class ConsistentDigestDiffNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ConsistentDigestDiffNtf;
       }
       [Key(2)]
       public string Role { get; set; }
    }
    
    [MessagePackObject]
    public partial class ServerNodesReadyNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodesReadyNtf;
       }
       [Key(2)]
       public List<uint> ReadyNodeList { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class ServerNodeHeartbeatReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodeHeartbeatReq;
       }
       [Key(2)]
       public long Timestamp { get; set; }
    }
    
    [MessagePackObject]
    public partial class ServerNodeHeartbeatAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodeHeartbeatAck;
       }
       [Key(2)]
       public long Timestamp { get; set; }
    }
    
    [MessagePackObject]
    public partial class ServerNodesJoinNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodesJoinNtf;
       }
       [Key(2)]
       public List<ServerNodeInfo> NodeInfoList { get; set; } = new();
    }
    
    [MessagePackObject]
    public partial class ServerNodeJoinNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodeJoinNtf;
       }
       [Key(2)]
       public ServerNodeInfo NodeInfo { get; set; }
    }
    
    [MessagePackObject]
    public partial class ServerNodeLeaveNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodeLeaveNtf;
       }
       [Key(2)]
       public uint NodeId { get; set; }
    }
    
    [MessagePackObject]
    public partial class ServerNodeReadyNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerNodeReadyNtf;
       }
       [Key(2)]
       public uint NodeId { get; set; }
    }
    
    [MessagePackObject]
    public partial class RegisterMailBoxReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.RegisterMailBoxReq;
       }
       [Key(2)]
       public string MailboxName { get; set; }
       [Key(3)]
       public uint NodeId { get; set; }
       [Key(4)]
       public bool IsOverride { get; set; }
       [Key(5)]
       public bool IsBroadcastRegister { get; set; }
    }
    
    [MessagePackObject]
    public partial class RegisterMailBoxAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.RegisterMailBoxAck;
       }
       [Key(2)]
       public string MailboxName { get; set; }
       [Key(3)]
       public int ErrorCode { get; set; }
       [Key(4)]
       public uint RouterNodeId { get; set; }
    }
    
    [MessagePackObject]
    public partial class RpcByMailBoxReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.RpcByMailBoxReq;
       }
       [Key(2)]
       public string MailboxName { get; set; }
       [Key(3)]
       public MsgPack_S_Rpc_Req RpcReq { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_Rpc_Req : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_Rpc_Req;
       }
       [Key(2)]
       public long ReqId { get; set; }
       [Key(3)]
       public int Flag { get; set; }
       [Key(4)]
       public uint SrcNode { get; set; }
       [Key(5)]
       public uint HiddenSrcNode { get; set; }
       [Key(6)]
       public string Uri { get; set; }
       [Key(7)]
       public string Func { get; set; }
       [Key(8)]
       public int MsgProtoCode { get; set; }
       [Key(9)]
       public byte[] Data { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_Rpc_Ack : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_Rpc_Ack;
       }
       [Key(2)]
       public long ReqId { get; set; } = 0;
       [Key(3)]
       public int Flag { get; set; } = 0;
       [Key(4)]
       public int ErrorCode { get; set; } = 0;
       [Key(5)]
       public uint ReqNode { get; set; } = 0;
       [Key(6)]
       public uint HiddenReqNode { get; set; } = 0;
       [Key(7)]
       public uint RespNodeId { get; set; } = 0;
       [Key(8)]
       public int MsgProtoCode { get; set; } = 0;
       [Key(9)]
       public byte[] RespData { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_RpcToNode_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_RpcToNode_Proto;
       }
       [Key(2)]
       public uint DestNodeId { get; set; } = 0;
       [Key(3)]
       public MsgPack_S_Rpc_Req RpcReq { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_RpcToPlayer_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_RpcToPlayer_Proto;
       }
       [Key(2)]
       public bool IsSpacePlayer { get; set; }
       [Key(3)]
       public long Uid { get; set; } = 0;
       [Key(4)]
       public MsgPack_S_Rpc_Req RpcReq { get; set; }
    }
    
    [MessagePackObject]
    public partial class MsgPack_S_RpcToPlayerRouter_Proto : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.MsgPack_S_RpcToPlayerRouter_Proto;
       }
       [Key(2)]
       public bool IsSpacePlayer { get; set; }
       [Key(3)]
       public long Uid { get; set; } = 0;
       [Key(4)]
       public MsgPack_S_Rpc_Req RpcReq { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class ServerNodeInfo
    {
       [Key(0)]
       public uint NodeId { get; set; } = 0;
       [Key(1)]
       public string Role { get; set; } = "";
       [Key(2)]
       public string Ip { get; set; } = "";
       [Key(3)]
       public uint Port { get; set; } = 0;
       [Key(4)]
       public uint RealmId { get; set; } = 0;
       [Key(5)]
       public bool IsReady { get; set; }
       [Key(6)]
       public byte[] ExtraData { get; set; } = null;
       [Key(7)]
       public string Tag { get; set; } = "";
    }
    
    [MessagePackObject]
    public partial class TransUpdateConsistentHashReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.TransUpdateConsistentHashReq;
       }
       [Key(2)]
       public long TransId { get; set; } = 0;
       [Key(3)]
       public byte TransState { get; set; } = 0;
       [Key(4)]
       public string Role { get; set; } = "";
       [Key(5)]
       public byte[] ConsistentHashData { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class TransUpdateConsistentHashAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.TransUpdateConsistentHashAck;
       }
       [Key(2)]
       public long TransId { get; set; } = 0;
       [Key(3)]
       public byte TransState { get; set; } = 0;
       [Key(4)]
       public string Role { get; set; } = "";
    }
    
    [MessagePackObject]
    public partial class UnregisterMailBoxReq : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.UnregisterMailBoxReq;
       }
       [Key(2)]
       public string MailboxName { get; set; } = "";
    }
    
    [MessagePackObject]
    public partial class UnregisterMailBoxAck : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.UnregisterMailBoxAck;
       }
       [Key(2)]
       public string MailboxName { get; set; } = "";
       [Key(3)]
       public int ErrorCode { get; set; } = 0;
    }
    
    [MessagePackObject]
    public partial class UpdateConsistentHashNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.UpdateConsistentHashNtf;
       }
       [Key(2)]
       public string Role { get; set; } = "";
       [Key(3)]
       public byte[] ConsistentHashData { get; set; } = null;
    }
    
    [MessagePackObject]
    public partial class ServerBroadcastDataNtf : MsgPackStructBase
    {
       public override void Init()
       {
            ProtoCode = EProtoCode.ServerBroadcastDataNtf;
       }
       [Key(2)]
       public string Key { get; set; }
       [Key(3)]
       public byte[] Data { get; set; }
    }
    
}
