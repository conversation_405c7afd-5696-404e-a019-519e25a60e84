// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class MsgPack_DB_Item
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("Id")]
       #endif
       public long Id { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("Type")]
       #endif
       public int Type { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("Count")]
       #endif
       public long Count { get; set; }
       [Key(3)]
       #if PHOENIX_SERVER
       [BsonElement("Name")]
       #endif
       public string Name { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    [BsonKnownTypes(typeof(HakoniwaBattleProcessInfo))]
    #endif
    [MessagePackObject]
    [Union(0, typeof(HakoniwaBattleProcessInfo))]
    public abstract partial class BattleProcessInfo
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("BattleType")]
       #endif
       public int BattleType { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("RandomSeed")]
       #endif
       public int RandomSeed { get; set; }
       [Key(2)]
       #if PHOENIX_SERVER
       [BsonElement("BattleLevelId")]
       #endif
       public int BattleLevelId { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class HakoniwaBattleMonsterInfo
    {
       [Key(0)]
       #if PHOENIX_SERVER
       [BsonElement("MonsterId")]
       #endif
       public int MonsterId { get; set; }
       [Key(1)]
       #if PHOENIX_SERVER
       [BsonElement("HateGroupId")]
       #endif
       public int HateGroupId { get; set; }
    }
    
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public partial class HakoniwaBattleProcessInfo : BattleProcessInfo
    {
       [Key(3)]
       #if PHOENIX_SERVER
       [BsonElement("HakoniwaId")]
       #endif
       public int HakoniwaId { get; set; }
       [Key(4)]
       #if PHOENIX_SERVER
       [BsonElement("LevelId")]
       #endif
       public int LevelId { get; set; }
       [Key(5)]
       #if PHOENIX_SERVER
       [BsonElement("MonsterInfo")]
       #endif
       public HakoniwaBattleMonsterInfo MonsterInfo { get; set; }
    }
    
}
