// Copyright (c) Phoenix All Rights Reserved.

using System.Linq;
using Phoenix.ConfigData.QuestGraph;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.CommonDefine
{
    public static class HakoniwaQuestHelper
    {
        public static bool IsAllConfCompleted(this HakoniwaQuestInfo questInfo)
        {
            return questInfo.QuestCondInfos.All(x => x.IsCompleted);
        }
    }

    public enum EHakoniwaEventType
    {
        None,
        BattleFinish,
        DialogComplete,
        ReachPoint,
        KillMonster,
    }

    /// <summary>
    /// 箱庭事件
    /// </summary>
    public abstract class HakoniwaEvent
    {
        public int HakoniwaId;
        public abstract EHakoniwaEventType EventType
        {
            get;
        }
    }

    public class HakoniwaBattleFinishEvent : HakoniwaEvent
    {
        public int LevelId;
        public override EHakoniwaEventType EventType
        {
            get => EHakoniwaEventType.BattleFinish;
        }
    }

    public class HakoniwaDialogCompleteEvent : HakoniwaEvent
    {
        public int DialogId;
        public override EHakoniwaEventType EventType
        {
            get => EHakoniwaEventType.DialogComplete;
        }
    }

    public class HakoniwaReachPointEvent : HakoniwaEvent
    {
        /// <summary>
        /// 直接用任务ID和索引去标识
        /// </summary>
        public int QuestId;
        public int QuestCondIndex;
        public override EHakoniwaEventType EventType
        {
            get => EHakoniwaEventType.ReachPoint;
        }
    }

    public class HakoniwaKillMonsterEvent : HakoniwaEvent
    {
        public int MonsterId;
        public override EHakoniwaEventType EventType
        {
            get => EHakoniwaEventType.KillMonster;
        }
    }
}

