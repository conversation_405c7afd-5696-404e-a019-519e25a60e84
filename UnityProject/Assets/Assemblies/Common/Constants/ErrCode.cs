// Copyright (c) Phoenix All Rights Reserved.

namespace Phoenix.CommonDefine
{
    public enum ErrCode
    {
        ErrCodeOk = 0,

        /// <summary>
        /// player name duplicate
        /// </summary>
        ErrCodePlayerNameDuplicate = 1,

        /// <summary>
        /// character not exist
        /// </summary>
        ErrCodeCharacterNotExist = 2,

        /// <summary>
        /// sign verify fail
        /// </summary>
        ErrCodeSignVerifyFail = 3,

        /// <summary>
        /// session token is expired
        /// </summary>
        ErrCodeSessionTokenExpired = 4,

        /// <summary>
        /// session token is expired
        /// </summary>
        ErrCodeLoginErrSessionTokenFormatInvalid = 5,

        /// <summary>
        /// Err that do not want user to known the reason
        /// </summary>
        ErrCodeInternalServerErr = 6,

        /// <summary>
        /// 非法配置
        /// </summary>
        ErrCodeInvalidConfigId = 7,

        /// <summary>
        /// 客户端非法参数
        /// </summary>
        ErrCodeInvalidClientParam = 8,

        #region Battle

        ErrCodeBattleProcessNotMatch = 900,

        #endregion

        #region Hakoniwa

        ErrCodeHakoniwaLocationIdUnlock = 1000,
        ErrCodeHakoniwaPlayerNotInHakoniwa = 1001,
        ErrCodeHakoniwaTreasureBoxOpened = 1002,
        ErrCodeHakoniwaLevelCompleted = 1003,
        ErrCodeHakoniwaMonsterDead = 1004,
        ErrCodeHakoniwaMonsterNotMatchLevel = 1005,
        ErrCodeHakoniwaMonsterNotOneHit = 1006,


        #endregion
    }
}
