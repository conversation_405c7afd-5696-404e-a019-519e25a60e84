// Copyright (c) Phoenix.  All Rights Reserved.

using System;

namespace Phoenix.CommonDefine
{
    /// <summary>
    ///     Represents categories of events.
    /// </summary>
    [Flags]
    public enum EventCategory
    {
        /// <summary>
        ///     Represents an invalid category.
        /// </summary>
        Invalid = 0,

        /// <summary>
        ///     Represents a basic category.
        /// </summary>
        Basic = 0x01,


        /// <summary>
        ///     Represents all categories.
        /// </summary>
        All = Basic
    }

    /// <summary>
    ///     Represents identifiers for game events.
    /// </summary>
    public enum GameEventIdDefine
    {
        None = 0,
    }

}
