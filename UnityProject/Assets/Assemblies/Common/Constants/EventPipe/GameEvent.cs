// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.CommonDefine
{

    /// <summary>
    ///     Represents a game event.
    /// </summary>
    public interface IGameEvent
    {
        /// <summary>
        ///     Represents the category of the event.
        /// </summary>
        EventCategory EventCategory { get; }

        /// <summary>
        ///     Represents the identifier of the event.
        /// </summary>
        GameEventIdDefine EventId { get; }
    }
}

