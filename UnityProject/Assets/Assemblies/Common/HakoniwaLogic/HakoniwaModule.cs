using System;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Hakoniwa.Logic
{
    public interface IHakoniwaModule
    {
        void Init();
        void Tick(float dt);
        void UnInit();
        void SerializeToDB(ExploringHakoniwaInfo toInfo);
    }

    public abstract class HakoniwaModule : IHakoniwaModule
    {
        public HakoniwaModule()
        {
        }

        protected Boolean m_inited;

        public HakoniwaBase Owner { get; set; }

        public void SetOwner(HakoniwaBase owner)
        {
            Owner = owner;
        }

        public virtual void Init()
        {
            OnInit();
            m_inited = true;
        }

        public virtual void Tick(float dt)
        {
            if (m_inited)
            {
                OnTick(dt);
            }
        }
        public virtual void UnInit()
        {
            m_inited = false;
            OnUnInit();
        }

        public virtual void SerializeToDB(ExploringHakoniwaInfo toInfo)
        {
        }

        protected void SetDirty()
        {
            Owner.SetDirty();
        }

        protected virtual void OnInit() { }
        protected virtual void OnTick(float dt) { }
        protected virtual void OnUnInit() { }
    }
}

