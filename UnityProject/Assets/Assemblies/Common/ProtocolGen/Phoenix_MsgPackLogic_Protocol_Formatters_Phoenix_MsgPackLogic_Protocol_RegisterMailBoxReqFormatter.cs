// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class RegisterMailBoxReqFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.RegisterMailBoxReq>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.RegisterMailBoxReq value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(6);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Serialize(ref writer, value.ProtoCode, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Serialize(ref writer, value.OpType, options);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.MailboxName, options);
            writer.Write(value.NodeId);
            writer.Write(value.IsOverride);
            writer.Write(value.IsBroadcastRegister);
        }

        public global::Phoenix.MsgPackLogic.Protocol.RegisterMailBoxReq Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __MailboxName__ = default(string);
            var __NodeId__ = default(uint);
            var __IsOverride__ = default(bool);
            var __IsBroadcastRegister__ = default(bool);
            var __ProtoCode__ = default(global::Phoenix.MsgPackLogic.Protocol.EProtoCode);
            var __OpType__ = default(global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 2:
                        __MailboxName__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __NodeId__ = reader.ReadUInt32();
                        break;
                    case 4:
                        __IsOverride__ = reader.ReadBoolean();
                        break;
                    case 5:
                        __IsBroadcastRegister__ = reader.ReadBoolean();
                        break;
                    case 0:
                        __ProtoCode__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Deserialize(ref reader, options);
                        break;
                    case 1:
                        __OpType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.RegisterMailBoxReq();
            ____result.MailboxName = __MailboxName__;
            ____result.NodeId = __NodeId__;
            ____result.IsOverride = __IsOverride__;
            ____result.IsBroadcastRegister = __IsBroadcastRegister__;
            ____result.ProtoCode = __ProtoCode__;
            ____result.OpType = __OpType__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
