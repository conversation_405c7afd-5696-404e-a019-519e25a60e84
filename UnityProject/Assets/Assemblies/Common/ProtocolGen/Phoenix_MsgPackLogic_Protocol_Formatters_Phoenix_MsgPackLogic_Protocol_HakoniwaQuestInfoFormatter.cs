// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class HakoniwaQuestInfoFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(1);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>>().Serialize(ref writer, value.QuestCondInfos, options);
        }

        public global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __QuestCondInfos__ = default(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __QuestCondInfos__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo();
            ____result.QuestCondInfos = __QuestCondInfos__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
