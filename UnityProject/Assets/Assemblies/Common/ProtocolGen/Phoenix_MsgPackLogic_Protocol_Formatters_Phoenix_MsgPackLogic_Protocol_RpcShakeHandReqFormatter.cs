// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class RpcShakeHandReqFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.RpcShakeHandReq>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.RpcShakeHandReq value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(7);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Serialize(ref writer, value.ProtoCode, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Serialize(ref writer, value.OpType, options);
            writer.Write(value.Magic);
            writer.Write(value.AuthToken);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.ServerNodeInfo>().Serialize(ref writer, value.NodeInfo, options);
            writer.Write(value.ProcId);
            writer.Write(value.IsReady);
        }

        public global::Phoenix.MsgPackLogic.Protocol.RpcShakeHandReq Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __Magic__ = default(int);
            var __AuthToken__ = default(byte[]);
            var __NodeInfo__ = default(global::Phoenix.MsgPackLogic.Protocol.ServerNodeInfo);
            var __ProcId__ = default(long);
            var __IsReady__ = default(bool);
            var __ProtoCode__ = default(global::Phoenix.MsgPackLogic.Protocol.EProtoCode);
            var __OpType__ = default(global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 2:
                        __Magic__ = reader.ReadInt32();
                        break;
                    case 3:
                        __AuthToken__ = reader.ReadBytes()?.ToArray();
                        break;
                    case 4:
                        __NodeInfo__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.ServerNodeInfo>().Deserialize(ref reader, options);
                        break;
                    case 5:
                        __ProcId__ = reader.ReadInt64();
                        break;
                    case 6:
                        __IsReady__ = reader.ReadBoolean();
                        break;
                    case 0:
                        __ProtoCode__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Deserialize(ref reader, options);
                        break;
                    case 1:
                        __OpType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.RpcShakeHandReq();
            ____result.Magic = __Magic__;
            ____result.AuthToken = __AuthToken__;
            ____result.NodeInfo = __NodeInfo__;
            ____result.ProcId = __ProcId__;
            ____result.IsReady = __IsReady__;
            ____result.ProtoCode = __ProtoCode__;
            ____result.OpType = __OpType__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
