// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class MsgPack_DB_ExampleDataFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleData>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleData value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(17);
            writer.Write(value.SByteValue);
            writer.Write(value.ByteValue);
            writer.Write(value.Int16Value);
            writer.Write(value.UInt16Value);
            writer.Write(value.Int32Value);
            writer.Write(value.UInt32Value);
            writer.Write(value.Int64Value);
            writer.Write(value.UInt64Value);
            writer.Write(value.FloatValue);
            writer.Write(value.SingleValue);
            writer.Write(value.DoubleValue);
            formatterResolver.GetFormatterWithVerify<string>().Serialize(ref writer, value.StringValue, options);
            writer.Write(value.BooleanValue);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<ulong>>().Serialize(ref writer, value.ListExample, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.Dictionary<ulong, double>>().Serialize(ref writer, value.DictExample, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>>().Serialize(ref writer, value.ListItemExample, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.Dictionary<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>>().Serialize(ref writer, value.DictItemExample, options);
        }

        public global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleData Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __SByteValue__ = default(sbyte);
            var __ByteValue__ = default(byte);
            var __Int16Value__ = default(short);
            var __UInt16Value__ = default(ushort);
            var __Int32Value__ = default(int);
            var __UInt32Value__ = default(uint);
            var __Int64Value__ = default(long);
            var __UInt64Value__ = default(ulong);
            var __FloatValue__ = default(float);
            var __SingleValue__ = default(float);
            var __DoubleValue__ = default(double);
            var __StringValue__ = default(string);
            var __BooleanValue__ = default(bool);
            var __ListExample__ = default(global::System.Collections.Generic.List<ulong>);
            var __DictExample__ = default(global::System.Collections.Generic.Dictionary<ulong, double>);
            var __ListItemExample__ = default(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>);
            var __DictItemExample__ = default(global::System.Collections.Generic.Dictionary<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __SByteValue__ = reader.ReadSByte();
                        break;
                    case 1:
                        __ByteValue__ = reader.ReadByte();
                        break;
                    case 2:
                        __Int16Value__ = reader.ReadInt16();
                        break;
                    case 3:
                        __UInt16Value__ = reader.ReadUInt16();
                        break;
                    case 4:
                        __Int32Value__ = reader.ReadInt32();
                        break;
                    case 5:
                        __UInt32Value__ = reader.ReadUInt32();
                        break;
                    case 6:
                        __Int64Value__ = reader.ReadInt64();
                        break;
                    case 7:
                        __UInt64Value__ = reader.ReadUInt64();
                        break;
                    case 8:
                        __FloatValue__ = reader.ReadSingle();
                        break;
                    case 9:
                        __SingleValue__ = reader.ReadSingle();
                        break;
                    case 10:
                        __DoubleValue__ = reader.ReadDouble();
                        break;
                    case 11:
                        __StringValue__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(ref reader, options);
                        break;
                    case 12:
                        __BooleanValue__ = reader.ReadBoolean();
                        break;
                    case 13:
                        __ListExample__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<ulong>>().Deserialize(ref reader, options);
                        break;
                    case 14:
                        __DictExample__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.Dictionary<ulong, double>>().Deserialize(ref reader, options);
                        break;
                    case 15:
                        __ListItemExample__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>>().Deserialize(ref reader, options);
                        break;
                    case 16:
                        __DictItemExample__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.Dictionary<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleData();
            ____result.SByteValue = __SByteValue__;
            ____result.ByteValue = __ByteValue__;
            ____result.Int16Value = __Int16Value__;
            ____result.UInt16Value = __UInt16Value__;
            ____result.Int32Value = __Int32Value__;
            ____result.UInt32Value = __UInt32Value__;
            ____result.Int64Value = __Int64Value__;
            ____result.UInt64Value = __UInt64Value__;
            ____result.FloatValue = __FloatValue__;
            ____result.SingleValue = __SingleValue__;
            ____result.DoubleValue = __DoubleValue__;
            ____result.StringValue = __StringValue__;
            ____result.BooleanValue = __BooleanValue__;
            ____result.ListExample = __ListExample__;
            ____result.DictExample = __DictExample__;
            ____result.ListItemExample = __ListItemExample__;
            ____result.DictItemExample = __DictItemExample__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
