// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class MsgPack_S_Rpc_AckFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Ack>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Ack value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(10);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Serialize(ref writer, value.ProtoCode, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Serialize(ref writer, value.OpType, options);
            writer.Write(value.ReqId);
            writer.Write(value.Flag);
            writer.Write(value.ErrorCode);
            writer.Write(value.ReqNode);
            writer.Write(value.HiddenReqNode);
            writer.Write(value.RespNodeId);
            writer.Write(value.MsgProtoCode);
            writer.Write(value.RespData);
        }

        public global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Ack Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __ReqId__ = default(long);
            var __Flag__ = default(int);
            var __ErrorCode__ = default(int);
            var __ReqNode__ = default(uint);
            var __HiddenReqNode__ = default(uint);
            var __RespNodeId__ = default(uint);
            var __MsgProtoCode__ = default(int);
            var __RespData__ = default(byte[]);
            var __ProtoCode__ = default(global::Phoenix.MsgPackLogic.Protocol.EProtoCode);
            var __OpType__ = default(global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 2:
                        __ReqId__ = reader.ReadInt64();
                        break;
                    case 3:
                        __Flag__ = reader.ReadInt32();
                        break;
                    case 4:
                        __ErrorCode__ = reader.ReadInt32();
                        break;
                    case 5:
                        __ReqNode__ = reader.ReadUInt32();
                        break;
                    case 6:
                        __HiddenReqNode__ = reader.ReadUInt32();
                        break;
                    case 7:
                        __RespNodeId__ = reader.ReadUInt32();
                        break;
                    case 8:
                        __MsgProtoCode__ = reader.ReadInt32();
                        break;
                    case 9:
                        __RespData__ = reader.ReadBytes()?.ToArray();
                        break;
                    case 0:
                        __ProtoCode__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Deserialize(ref reader, options);
                        break;
                    case 1:
                        __OpType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Ack();
            ____result.ReqId = __ReqId__;
            ____result.Flag = __Flag__;
            ____result.ErrorCode = __ErrorCode__;
            ____result.ReqNode = __ReqNode__;
            ____result.HiddenReqNode = __HiddenReqNode__;
            ____result.RespNodeId = __RespNodeId__;
            ____result.MsgProtoCode = __MsgProtoCode__;
            ____result.RespData = __RespData__;
            ____result.ProtoCode = __ProtoCode__;
            ____result.OpType = __OpType__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
