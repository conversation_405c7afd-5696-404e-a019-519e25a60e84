// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class ExploringHakoniwaInfoFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(9);
            writer.Write(value.HakoniwaId);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>>().Serialize(ref writer, value.QuestInfos, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.CompletedQuestIds, options);
            formatterResolver.GetFormatterWithVerify<global::System.DateTime>().Serialize(ref writer, value.CreateTime, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.UnlockWayPointIdIds, options);
            writer.Write(value.SceneId);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.PosInfo>().Serialize(ref writer, value.PosInfo, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntity>>().Serialize(ref writer, value.Entities, options);
            formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Serialize(ref writer, value.CompletedLevelIds, options);
        }

        public global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __HakoniwaId__ = default(int);
            var __QuestInfos__ = default(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>);
            var __CompletedQuestIds__ = default(global::System.Collections.Generic.List<int>);
            var __CreateTime__ = default(global::System.DateTime);
            var __UnlockWayPointIdIds__ = default(global::System.Collections.Generic.List<int>);
            var __SceneId__ = default(int);
            var __PosInfo__ = default(global::Phoenix.MsgPackLogic.Protocol.PosInfo);
            var __Entities__ = default(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntity>);
            var __CompletedLevelIds__ = default(global::System.Collections.Generic.List<int>);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 0:
                        __HakoniwaId__ = reader.ReadInt32();
                        break;
                    case 1:
                        __QuestInfos__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>>().Deserialize(ref reader, options);
                        break;
                    case 2:
                        __CompletedQuestIds__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 3:
                        __CreateTime__ = formatterResolver.GetFormatterWithVerify<global::System.DateTime>().Deserialize(ref reader, options);
                        break;
                    case 4:
                        __UnlockWayPointIdIds__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    case 5:
                        __SceneId__ = reader.ReadInt32();
                        break;
                    case 6:
                        __PosInfo__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.PosInfo>().Deserialize(ref reader, options);
                        break;
                    case 7:
                        __Entities__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntity>>().Deserialize(ref reader, options);
                        break;
                    case 8:
                        __CompletedLevelIds__ = formatterResolver.GetFormatterWithVerify<global::System.Collections.Generic.List<int>>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo();
            ____result.HakoniwaId = __HakoniwaId__;
            ____result.QuestInfos = __QuestInfos__;
            ____result.CompletedQuestIds = __CompletedQuestIds__;
            ____result.CreateTime = __CreateTime__;
            ____result.UnlockWayPointIdIds = __UnlockWayPointIdIds__;
            ____result.SceneId = __SceneId__;
            ____result.PosInfo = __PosInfo__;
            ____result.Entities = __Entities__;
            ____result.CompletedLevelIds = __CompletedLevelIds__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
