// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Resolvers
{
    using System;

    public class PhoenixResolver : global::MessagePack.IFormatterResolver
    {
        public static readonly global::MessagePack.IFormatterResolver Instance = new PhoenixResolver();

        private PhoenixResolver()
        {
        }

        public global::MessagePack.Formatters.IMessagePackFormatter<T> GetFormatter<T>()
        {
            return FormatterCache<T>.Formatter;
        }

        private static class FormatterCache<T>
        {
            internal static readonly global::MessagePack.Formatters.IMessagePackFormatter<T> Formatter;

            static FormatterCache()
            {
                var f = PhoenixResolverGetFormatterHelper.GetFormatter(typeof(T));
                if (f != null)
                {
                    Formatter = (global::MessagePack.Formatters.IMessagePackFormatter<T>)f;
                }
            }
        }
    }

    internal static class PhoenixResolverGetFormatterHelper
    {
        private static readonly global::System.Collections.Generic.Dictionary<Type, int> lookup;

        static PhoenixResolverGetFormatterHelper()
        {
            lookup = new global::System.Collections.Generic.Dictionary<Type, int>(143)
            {
                { typeof(global::System.Collections.Generic.Dictionary<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>), 0 },
                { typeof(global::System.Collections.Generic.Dictionary<ulong, double>), 1 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.ConsistentDigestInfo>), 2 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>), 3 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntity>), 4 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntityMonster>), 5 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>), 6 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>), 7 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>), 8 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.ServerNodeInfo>), 9 },
                { typeof(global::System.Collections.Generic.List<int>), 10 },
                { typeof(global::System.Collections.Generic.List<long>), 11 },
                { typeof(global::System.Collections.Generic.List<uint>), 12 },
                { typeof(global::System.Collections.Generic.List<ulong>), 13 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType), 14 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.EProtoCode), 15 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.BattleProcessInfo), 16 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntity), 17 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.BandwidthTestAck), 18 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.BandwidthTestReq), 19 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.BattleCancelAck), 20 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.BattleCancelReq), 21 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.CharacterCreateAck), 22 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.CharacterCreateReq), 23 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.CheckConsistentDigestNtf), 24 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ConsistentDigestDiffNtf), 25 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ConsistentDigestInfo), 26 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.DBCacheObject), 27 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo), 28 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ForwardClientMessageToServerNtf), 29 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ForwardClientMsgBroadcastNtf), 30 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ForwardMessageToClientNtf), 31 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaBattleFinishAck), 32 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaBattleFinishReq), 33 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaBattleMonsterInfo), 34 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaBattleProcessInfo), 35 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaBattleStartAck), 36 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaBattleStartReq), 37 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaDialogCompleteAck), 38 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaDialogCompleteReq), 39 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAck), 40 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterReq), 41 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntityGear), 42 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntityMonster), 43 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaFinishNtf), 44 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpAck), 45 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpReq), 46 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaPosSyncAck), 47 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaPosSyncReq), 48 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondChangeNtf), 49 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo), 50 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo), 51 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestStateChangeNtf), 52 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaReachPointAck), 53 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaReachPointReq), 54 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaTreasureBoxOpenAck), 55 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaTreasureBoxOpenReq), 56 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenAck), 57 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenReq), 58 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenAck), 59 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenReq), 60 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_Ack), 61 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_Req), 62 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_Ack), 63 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_Ntf), 64 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_Req), 65 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_Ack), 66 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_Req), 67 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallBasePlayerMethod_Proto), 68 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallEntityMethod_Proto), 69 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_BagData), 70 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleData), 71 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item), 72 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_Ack), 73 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_Ntf), 74 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_Req), 75 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionEnd_Ntf), 76 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionInit_Ntf), 77 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_Ack), 78 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_Ntf), 79 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_Req), 80 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_Ack), 81 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_Req), 82 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Example_Ack), 83 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Example_Req), 84 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleData_Ntf), 85 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_Ack), 86 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_Req), 87 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_Ack), 88 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_Req), 89 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Logout_Req), 90 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_BasicEntityCreate_Proto), 91 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_CallClientMethod_Proto), 92 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Duel_Ntf), 93 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_DuelReply_Ntf), 94 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EngineRpc_Proto), 95 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityCreawte_Proto), 96 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityDestroy_Proto), 97 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrate_Proto), 98 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrateReply_Proto), 99 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_ForwardEntityRpc_Proto), 100 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Ack), 101 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Req), 102 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToNode_Proto), 103 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayer_Proto), 104 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayerRouter_Proto), 105 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_Ack), 106 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_Req), 107 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Test_Ack), 108 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Test_Req), 109 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPackStructBase), 110 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerBasicCompDataNtf), 111 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerBattleCompDataNtf), 112 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataBasicInfo), 113 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataBattleInfo), 114 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataHakoniwaInfo), 115 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataWorldInfo), 116 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerDataSectionSyncEndNtf), 117 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerHakoniwaCompDataNtf), 118 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerInfoInitAck), 119 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerInfoInitReq), 120 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerWorldCompDataNtf), 121 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PosInfo), 122 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.RegisterMailBoxAck), 123 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.RegisterMailBoxReq), 124 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.RpcByMailBoxReq), 125 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.RpcShakeHandAck), 126 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.RpcShakeHandReq), 127 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerBroadcastDataNtf), 128 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerDisconnectNtf), 129 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodeHeartbeatAck), 130 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodeHeartbeatReq), 131 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodeInfo), 132 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodeJoinNtf), 133 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodeLeaveNtf), 134 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodeReadyNtf), 135 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodesJoinNtf), 136 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerNodesReadyNtf), 137 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.TransUpdateConsistentHashAck), 138 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.TransUpdateConsistentHashReq), 139 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.UnregisterMailBoxAck), 140 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.UnregisterMailBoxReq), 141 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.UpdateConsistentHashNtf), 142 },
            };
        }

        internal static object GetFormatter(Type t)
        {
            int key;
            if (!lookup.TryGetValue(t, out key))
            {
                return null;
            }

            switch (key)
            {
                case 0: return new global::MessagePack.Formatters.DictionaryFormatter<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>();
                case 1: return new global::MessagePack.Formatters.DictionaryFormatter<ulong, double>();
                case 2: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.ConsistentDigestInfo>();
                case 3: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>();
                case 4: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntity>();
                case 5: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEntityMonster>();
                case 6: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>();
                case 7: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>();
                case 8: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>();
                case 9: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.ServerNodeInfo>();
                case 10: return new global::MessagePack.Formatters.ListFormatter<int>();
                case 11: return new global::MessagePack.Formatters.ListFormatter<long>();
                case 12: return new global::MessagePack.Formatters.ListFormatter<uint>();
                case 13: return new global::MessagePack.Formatters.ListFormatter<ulong>();
                case 14: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.EMsgPackOpTypeFormatter();
                case 15: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.EProtoCodeFormatter();
                case 16: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.BattleProcessInfoFormatter();
                case 17: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEntityFormatter();
                case 18: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.BandwidthTestAckFormatter();
                case 19: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.BandwidthTestReqFormatter();
                case 20: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.BattleCancelAckFormatter();
                case 21: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.BattleCancelReqFormatter();
                case 22: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.CharacterCreateAckFormatter();
                case 23: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.CharacterCreateReqFormatter();
                case 24: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.CheckConsistentDigestNtfFormatter();
                case 25: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ConsistentDigestDiffNtfFormatter();
                case 26: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ConsistentDigestInfoFormatter();
                case 27: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.DBCacheObjectFormatter();
                case 28: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfoFormatter();
                case 29: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ForwardClientMessageToServerNtfFormatter();
                case 30: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ForwardClientMsgBroadcastNtfFormatter();
                case 31: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ForwardMessageToClientNtfFormatter();
                case 32: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaBattleFinishAckFormatter();
                case 33: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaBattleFinishReqFormatter();
                case 34: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaBattleMonsterInfoFormatter();
                case 35: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaBattleProcessInfoFormatter();
                case 36: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaBattleStartAckFormatter();
                case 37: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaBattleStartReqFormatter();
                case 38: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaDialogCompleteAckFormatter();
                case 39: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaDialogCompleteReqFormatter();
                case 40: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAckFormatter();
                case 41: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEnterReqFormatter();
                case 42: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEntityGearFormatter();
                case 43: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEntityMonsterFormatter();
                case 44: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaFinishNtfFormatter();
                case 45: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpAckFormatter();
                case 46: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpReqFormatter();
                case 47: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaPosSyncAckFormatter();
                case 48: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaPosSyncReqFormatter();
                case 49: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondChangeNtfFormatter();
                case 50: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfoFormatter();
                case 51: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfoFormatter();
                case 52: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaQuestStateChangeNtfFormatter();
                case 53: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaReachPointAckFormatter();
                case 54: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaReachPointReqFormatter();
                case 55: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaTreasureBoxOpenAckFormatter();
                case 56: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaTreasureBoxOpenReqFormatter();
                case 57: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenAckFormatter();
                case 58: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenReqFormatter();
                case 59: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenAckFormatter();
                case 60: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenReqFormatter();
                case 61: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_AckFormatter();
                case 62: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_ReqFormatter();
                case 63: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_AckFormatter();
                case 64: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_NtfFormatter();
                case 65: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_ReqFormatter();
                case 66: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_AckFormatter();
                case 67: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_ReqFormatter();
                case 68: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallBasePlayerMethod_ProtoFormatter();
                case 69: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallEntityMethod_ProtoFormatter();
                case 70: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DB_BagDataFormatter();
                case 71: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleDataFormatter();
                case 72: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ItemFormatter();
                case 73: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_AckFormatter();
                case 74: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_NtfFormatter();
                case 75: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_ReqFormatter();
                case 76: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionEnd_NtfFormatter();
                case 77: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionInit_NtfFormatter();
                case 78: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_AckFormatter();
                case 79: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_NtfFormatter();
                case 80: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_ReqFormatter();
                case 81: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_AckFormatter();
                case 82: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_ReqFormatter();
                case 83: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Example_AckFormatter();
                case 84: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Example_ReqFormatter();
                case 85: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleData_NtfFormatter();
                case 86: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_AckFormatter();
                case 87: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_ReqFormatter();
                case 88: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_AckFormatter();
                case 89: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_ReqFormatter();
                case 90: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Logout_ReqFormatter();
                case 91: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_BasicEntityCreate_ProtoFormatter();
                case 92: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_CallClientMethod_ProtoFormatter();
                case 93: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_Duel_NtfFormatter();
                case 94: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_DuelReply_NtfFormatter();
                case 95: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EngineRpc_ProtoFormatter();
                case 96: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityCreawte_ProtoFormatter();
                case 97: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityDestroy_ProtoFormatter();
                case 98: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrate_ProtoFormatter();
                case 99: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrateReply_ProtoFormatter();
                case 100: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_ForwardEntityRpc_ProtoFormatter();
                case 101: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_AckFormatter();
                case 102: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_ReqFormatter();
                case 103: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToNode_ProtoFormatter();
                case 104: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayer_ProtoFormatter();
                case 105: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayerRouter_ProtoFormatter();
                case 106: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_AckFormatter();
                case 107: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_ReqFormatter();
                case 108: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Test_AckFormatter();
                case 109: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Test_ReqFormatter();
                case 110: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPackStructBaseFormatter();
                case 111: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerBasicCompDataNtfFormatter();
                case 112: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerBattleCompDataNtfFormatter();
                case 113: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataBasicInfoFormatter();
                case 114: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataBattleInfoFormatter();
                case 115: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataHakoniwaInfoFormatter();
                case 116: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataWorldInfoFormatter();
                case 117: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerDataSectionSyncEndNtfFormatter();
                case 118: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerHakoniwaCompDataNtfFormatter();
                case 119: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerInfoInitAckFormatter();
                case 120: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerInfoInitReqFormatter();
                case 121: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerWorldCompDataNtfFormatter();
                case 122: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PosInfoFormatter();
                case 123: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.RegisterMailBoxAckFormatter();
                case 124: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.RegisterMailBoxReqFormatter();
                case 125: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.RpcByMailBoxReqFormatter();
                case 126: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.RpcShakeHandAckFormatter();
                case 127: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.RpcShakeHandReqFormatter();
                case 128: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerBroadcastDataNtfFormatter();
                case 129: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerDisconnectNtfFormatter();
                case 130: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodeHeartbeatAckFormatter();
                case 131: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodeHeartbeatReqFormatter();
                case 132: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodeInfoFormatter();
                case 133: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodeJoinNtfFormatter();
                case 134: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodeLeaveNtfFormatter();
                case 135: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodeReadyNtfFormatter();
                case 136: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodesJoinNtfFormatter();
                case 137: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerNodesReadyNtfFormatter();
                case 138: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.TransUpdateConsistentHashAckFormatter();
                case 139: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.TransUpdateConsistentHashReqFormatter();
                case 140: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.UnregisterMailBoxAckFormatter();
                case 141: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.UnregisterMailBoxReqFormatter();
                case 142: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.UpdateConsistentHashNtfFormatter();
                default: return null;
            }
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1649 // File name should match first type name
