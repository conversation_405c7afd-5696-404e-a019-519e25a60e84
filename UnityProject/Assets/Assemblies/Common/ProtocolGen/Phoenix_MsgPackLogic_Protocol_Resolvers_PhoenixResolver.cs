// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Resolvers
{
    using System;

    public class PhoenixResolver : global::MessagePack.IFormatterResolver
    {
        public static readonly global::MessagePack.IFormatterResolver Instance = new PhoenixResolver();

        private PhoenixResolver()
        {
        }

        public global::MessagePack.Formatters.IMessagePackFormatter<T> GetFormatter<T>()
        {
            return FormatterCache<T>.Formatter;
        }

        private static class FormatterCache<T>
        {
            internal static readonly global::MessagePack.Formatters.IMessagePackFormatter<T> Formatter;

            static FormatterCache()
            {
                var f = PhoenixResolverGetFormatterHelper.GetFormatter(typeof(T));
                if (f != null)
                {
                    Formatter = (global::MessagePack.Formatters.IMessagePackFormatter<T>)f;
                }
            }
        }
    }

    internal static class PhoenixResolverGetFormatterHelper
    {
        private static readonly global::System.Collections.Generic.Dictionary<Type, int> lookup;

        static PhoenixResolverGetFormatterHelper()
        {
            lookup = new global::System.Collections.Generic.Dictionary<Type, int>(118)
            {
                { typeof(global::System.Collections.Generic.Dictionary<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>), 0 },
                { typeof(global::System.Collections.Generic.Dictionary<ulong, double>), 1 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.CompletedHakoniwaInfo>), 2 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>), 3 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>), 4 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>), 5 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>), 6 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_CheckConsistentDigestInfo>), 7 },
                { typeof(global::System.Collections.Generic.List<global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_ServerNodeInfo_Proto>), 8 },
                { typeof(global::System.Collections.Generic.List<int>), 9 },
                { typeof(global::System.Collections.Generic.List<long>), 10 },
                { typeof(global::System.Collections.Generic.List<uint>), 11 },
                { typeof(global::System.Collections.Generic.List<ulong>), 12 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType), 13 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.EProtoCode), 14 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.CharacterCreateAck), 15 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.CharacterCreateReq), 16 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.CompletedHakoniwaInfo), 17 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.DBCacheObject), 18 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo), 19 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ForwardClientMessageToServerNtf), 20 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ForwardMessageToClientNtf), 21 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAck), 22 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterReq), 23 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpAck), 24 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpReq), 25 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo), 26 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo), 27 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.HakoniwaTreasureInfo), 28 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenAck), 29 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenReq), 30 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenAck), 31 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenReq), 32 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_Ack), 33 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_Req), 34 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_Ack), 35 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_Ntf), 36 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_Req), 37 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_Ack), 38 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_Req), 39 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallBasePlayerMethod_Proto), 40 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallEntityMethod_Proto), 41 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_BagData), 42 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleData), 43 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item), 44 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_Ack), 45 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_Ntf), 46 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_Req), 47 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionEnd_Ntf), 48 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionInit_Ntf), 49 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_Ack), 50 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_Ntf), 51 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_Req), 52 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_Ack), 53 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_Req), 54 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Example_Ack), 55 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Example_Req), 56 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleData_Ntf), 57 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_Ack), 58 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_Req), 59 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_Ack), 60 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_Req), 61 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Logout_Req), 62 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_BasicEntityCreate_Proto), 63 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_BroadcastData_Proto), 64 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_CallClientMethod_Proto), 65 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_CheckConsistentDigest_Proto), 66 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_CheckConsistentDigestInfo), 67 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Duel_Ntf), 68 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_DuelReply_Ntf), 69 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EngineRpc_Proto), 70 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityCreawte_Proto), 71 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityDestroy_Proto), 72 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrate_Proto), 73 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrateReply_Proto), 74 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_ForwardClientMsgBroadcast_Proto), 75 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_ForwardEntityRpc_Proto), 76 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_GetConsistentDigest_Proto), 77 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeHeartbeat_Ack), 78 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeHeartbeat_Req), 79 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeInfoList_Proto), 80 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeJoin_Ntf), 81 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeLeave_Ntf), 82 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeReady_Proto), 83 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodesReady_Proto), 84 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RegisterMailBox_Ack), 85 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RegisterMailBox_Req), 86 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Ack), 87 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_Req), 88 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcByMailBox_Proto), 89 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcConnect_Proto), 90 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcEstablish_Proto), 91 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToNode_Proto), 92 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayer_Proto), 93 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayerRouter_Proto), 94 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_ServerNodeInfo_Proto), 95 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_TransUpdateConsistentHash_Ack), 96 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_TransUpdateConsistentHash_Req), 97 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_UnregisterMailBox_Ack), 98 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_UnregisterMailBox_Req), 99 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_UpdateConsistentHash_Proto), 100 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_Ack), 101 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_Req), 102 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Test_Ack), 103 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPack_Test_Req), 104 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.MsgPackStructBase), 105 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerBasicCompDataNtf), 106 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataBasicInfo), 107 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataHakoniwaInfo), 108 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerCompDataWorldInfo), 109 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerDataSectionSyncEndNtf), 110 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerHakoniwaCompDataNtf), 111 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerInfoInitAck), 112 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerInfoInitReq), 113 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PlayerWorldCompDataNtf), 114 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.PosInfo), 115 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.ServerDisconnectNtf), 116 },
                { typeof(global::Phoenix.MsgPackLogic.Protocol.TestInfo), 117 },
            };
        }

        internal static object GetFormatter(Type t)
        {
            int key;
            if (!lookup.TryGetValue(t, out key))
            {
                return null;
            }

            switch (key)
            {
                case 0: return new global::MessagePack.Formatters.DictionaryFormatter<string, global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>();
                case 1: return new global::MessagePack.Formatters.DictionaryFormatter<ulong, double>();
                case 2: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.CompletedHakoniwaInfo>();
                case 3: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>();
                case 4: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfo>();
                case 5: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfo>();
                case 6: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.MsgPack_DB_Item>();
                case 7: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_CheckConsistentDigestInfo>();
                case 8: return new global::MessagePack.Formatters.ListFormatter<global::Phoenix.MsgPackLogic.Protocol.MsgPack_S_ServerNodeInfo_Proto>();
                case 9: return new global::MessagePack.Formatters.ListFormatter<int>();
                case 10: return new global::MessagePack.Formatters.ListFormatter<long>();
                case 11: return new global::MessagePack.Formatters.ListFormatter<uint>();
                case 12: return new global::MessagePack.Formatters.ListFormatter<ulong>();
                case 13: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.EMsgPackOpTypeFormatter();
                case 14: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.EProtoCodeFormatter();
                case 15: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.CharacterCreateAckFormatter();
                case 16: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.CharacterCreateReqFormatter();
                case 17: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.CompletedHakoniwaInfoFormatter();
                case 18: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.DBCacheObjectFormatter();
                case 19: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfoFormatter();
                case 20: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ForwardClientMessageToServerNtfFormatter();
                case 21: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ForwardMessageToClientNtfFormatter();
                case 22: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAckFormatter();
                case 23: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaEnterReqFormatter();
                case 24: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpAckFormatter();
                case 25: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaGiveUpReqFormatter();
                case 26: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaQuestCondInfoFormatter();
                case 27: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaQuestInfoFormatter();
                case 28: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.HakoniwaTreasureInfoFormatter();
                case 29: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenAckFormatter();
                case 30: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginByAuthTokenReqFormatter();
                case 31: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenAckFormatter();
                case 32: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.LoginBySessionTokenReqFormatter();
                case 33: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_AckFormatter();
                case 34: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BagInfo_ReqFormatter();
                case 35: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_AckFormatter();
                case 36: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_NtfFormatter();
                case 37: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleCommand_ReqFormatter();
                case 38: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_AckFormatter();
                case 39: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_BattleVerify_ReqFormatter();
                case 40: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallBasePlayerMethod_ProtoFormatter();
                case 41: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_C_CallEntityMethod_ProtoFormatter();
                case 42: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DB_BagDataFormatter();
                case 43: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ExampleDataFormatter();
                case 44: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DB_ItemFormatter();
                case 45: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_AckFormatter();
                case 46: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_NtfFormatter();
                case 47: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Duel_ReqFormatter();
                case 48: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionEnd_NtfFormatter();
                case 49: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelBattleSessionInit_NtfFormatter();
                case 50: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_AckFormatter();
                case 51: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_NtfFormatter();
                case 52: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_DuelReply_ReqFormatter();
                case 53: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_AckFormatter();
                case 54: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Echo_ReqFormatter();
                case 55: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Example_AckFormatter();
                case 56: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Example_ReqFormatter();
                case 57: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleData_NtfFormatter();
                case 58: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_AckFormatter();
                case 59: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ExampleExternal_ReqFormatter();
                case 60: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_AckFormatter();
                case 61: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_HeartBeat_ReqFormatter();
                case 62: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Logout_ReqFormatter();
                case 63: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_BasicEntityCreate_ProtoFormatter();
                case 64: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_BroadcastData_ProtoFormatter();
                case 65: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_CallClientMethod_ProtoFormatter();
                case 66: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_CheckConsistentDigest_ProtoFormatter();
                case 67: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_CheckConsistentDigestInfoFormatter();
                case 68: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_Duel_NtfFormatter();
                case 69: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_DuelReply_NtfFormatter();
                case 70: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EngineRpc_ProtoFormatter();
                case 71: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityCreawte_ProtoFormatter();
                case 72: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityDestroy_ProtoFormatter();
                case 73: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrate_ProtoFormatter();
                case 74: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_EntityMigrateReply_ProtoFormatter();
                case 75: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_ForwardClientMsgBroadcast_ProtoFormatter();
                case 76: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_ForwardEntityRpc_ProtoFormatter();
                case 77: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_GetConsistentDigest_ProtoFormatter();
                case 78: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeHeartbeat_AckFormatter();
                case 79: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeHeartbeat_ReqFormatter();
                case 80: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeInfoList_ProtoFormatter();
                case 81: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeJoin_NtfFormatter();
                case 82: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeLeave_NtfFormatter();
                case 83: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodeReady_ProtoFormatter();
                case 84: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_NodesReady_ProtoFormatter();
                case 85: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RegisterMailBox_AckFormatter();
                case 86: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RegisterMailBox_ReqFormatter();
                case 87: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_AckFormatter();
                case 88: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_Rpc_ReqFormatter();
                case 89: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcByMailBox_ProtoFormatter();
                case 90: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcConnect_ProtoFormatter();
                case 91: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcEstablish_ProtoFormatter();
                case 92: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToNode_ProtoFormatter();
                case 93: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayer_ProtoFormatter();
                case 94: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_RpcToPlayerRouter_ProtoFormatter();
                case 95: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_ServerNodeInfo_ProtoFormatter();
                case 96: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_TransUpdateConsistentHash_AckFormatter();
                case 97: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_TransUpdateConsistentHash_ReqFormatter();
                case 98: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_UnregisterMailBox_AckFormatter();
                case 99: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_UnregisterMailBox_ReqFormatter();
                case 100: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_S_UpdateConsistentHash_ProtoFormatter();
                case 101: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_AckFormatter();
                case 102: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_ServerTime_ReqFormatter();
                case 103: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Test_AckFormatter();
                case 104: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPack_Test_ReqFormatter();
                case 105: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.MsgPackStructBaseFormatter();
                case 106: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerBasicCompDataNtfFormatter();
                case 107: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataBasicInfoFormatter();
                case 108: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataHakoniwaInfoFormatter();
                case 109: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerCompDataWorldInfoFormatter();
                case 110: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerDataSectionSyncEndNtfFormatter();
                case 111: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerHakoniwaCompDataNtfFormatter();
                case 112: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerInfoInitAckFormatter();
                case 113: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerInfoInitReqFormatter();
                case 114: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PlayerWorldCompDataNtfFormatter();
                case 115: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.PosInfoFormatter();
                case 116: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.ServerDisconnectNtfFormatter();
                case 117: return new Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol.TestInfoFormatter();
                default: return null;
            }
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1649 // File name should match first type name
