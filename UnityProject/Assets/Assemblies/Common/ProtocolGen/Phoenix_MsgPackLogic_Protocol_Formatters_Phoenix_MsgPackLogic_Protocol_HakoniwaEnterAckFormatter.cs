// <auto-generated>
// THIS (.cs) FILE IS GENERATED BY MPC(MessagePack-CSharp). DO NOT CHANGE IT.
// </auto-generated>

#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

#pragma warning disable SA1129 // Do not use default value type constructor
#pragma warning disable SA1200 // Using directives should be placed correctly
#pragma warning disable SA1309 // Field names should not begin with underscore
#pragma warning disable SA1312 // Variable names should begin with lower-case letter
#pragma warning disable SA1403 // File may only contain a single namespace
#pragma warning disable SA1649 // File name should match first type name

namespace Phoenix.MsgPackLogic.Protocol.Formatters.Phoenix.MsgPackLogic.Protocol
{
    using System;
    using System.Buffers;
    using MessagePack;

    public sealed class HakoniwaEnterAckFormatter : global::MessagePack.Formatters.IMessagePackFormatter<global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAck>
    {

        public void Serialize(ref MessagePackWriter writer, global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAck value, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            IFormatterResolver formatterResolver = options.Resolver;
            writer.WriteArrayHeader(7);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Serialize(ref writer, value.ProtoCode, options);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Serialize(ref writer, value.OpType, options);
            writer.Write(value.Token);
            writer.Write(value.ErrCode);
            writer.Write(value.HakoniwaId);
            writer.Write(value.EnterWayPointId);
            formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>().Serialize(ref writer, value.FirstEnterHakoniwaInfo, options);
        }

        public global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAck Deserialize(ref MessagePackReader reader, global::MessagePack.MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return null;
            }

            options.Security.DepthStep(ref reader);
            IFormatterResolver formatterResolver = options.Resolver;
            var length = reader.ReadArrayHeader();
            var __Token__ = default(uint);
            var __ErrCode__ = default(int);
            var __HakoniwaId__ = default(int);
            var __EnterWayPointId__ = default(int);
            var __FirstEnterHakoniwaInfo__ = default(global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo);
            var __ProtoCode__ = default(global::Phoenix.MsgPackLogic.Protocol.EProtoCode);
            var __OpType__ = default(global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType);

            for (int i = 0; i < length; i++)
            {
                switch (i)
                {
                    case 2:
                        __Token__ = reader.ReadUInt32();
                        break;
                    case 3:
                        __ErrCode__ = reader.ReadInt32();
                        break;
                    case 4:
                        __HakoniwaId__ = reader.ReadInt32();
                        break;
                    case 5:
                        __EnterWayPointId__ = reader.ReadInt32();
                        break;
                    case 6:
                        __FirstEnterHakoniwaInfo__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.ExploringHakoniwaInfo>().Deserialize(ref reader, options);
                        break;
                    case 0:
                        __ProtoCode__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EProtoCode>().Deserialize(ref reader, options);
                        break;
                    case 1:
                        __OpType__ = formatterResolver.GetFormatterWithVerify<global::Phoenix.MsgPackLogic.Protocol.EMsgPackOpType>().Deserialize(ref reader, options);
                        break;
                    default:
                        reader.Skip();
                        break;
                }
            }

            var ____result = new global::Phoenix.MsgPackLogic.Protocol.HakoniwaEnterAck();
            ____result.Token = __Token__;
            ____result.ErrCode = __ErrCode__;
            ____result.HakoniwaId = __HakoniwaId__;
            ____result.EnterWayPointId = __EnterWayPointId__;
            ____result.FirstEnterHakoniwaInfo = __FirstEnterHakoniwaInfo__;
            ____result.ProtoCode = __ProtoCode__;
            ____result.OpType = __OpType__;
            reader.Depth--;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612

#pragma warning restore SA1129 // Do not use default value type constructor
#pragma warning restore SA1200 // Using directives should be placed correctly
#pragma warning restore SA1309 // Field names should not begin with underscore
#pragma warning restore SA1312 // Variable names should begin with lower-case letter
#pragma warning restore SA1403 // File may only contain a single namespace
#pragma warning restore SA1649 // File name should match first type name
