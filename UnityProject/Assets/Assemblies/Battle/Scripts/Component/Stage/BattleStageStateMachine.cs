using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleStageStateMachine
    {
        private BattleStageStateId m_curStateId;
        private Dictionary<int, BattleStageState> m_stateMap = new Dictionary<int, BattleStageState>();

        public BattleStageStateId curStateId
        {
            get { return m_curStateId; }
            set { m_curStateId = value; }
        }

        public void AddState(BattleStageStateId id, Func<BattlePerformance> actionOnEnter, Func<BattleStageStateId> actionOnReadyNextState)
        {
            if (m_stateMap.ContainsKey((int)id))
            {
                return;
            }
            m_stateMap.Add((int)id, new BattleStageState()
            {
                actionOnEnter = actionOnEnter,
                actionOnReadyNextState = actionOnReadyNextState,
            });
        }

        public void Reset()
        {
            m_curStateId = BattleStageStateId.None;
        }

        public BattlePerformance ChangeState(BattleStageStateId stateId)
        {
            if(stateId == BattleStageStateId.None)
            {
                return null; ;
            }
            BattleStageState state = GetState(stateId);
            if (state == null)
            {
                throw new Exception(string.Format("Error: State with Index {0} not exist!!", stateId));
            }
            m_curStateId = stateId;
            if (state.actionOnEnter == null)
            {
                return null;
            }
            return state.actionOnEnter();
        }

        public BattleStageState GetState(BattleStageStateId id)
        {
            BattleStageState state;
            m_stateMap.TryGetValue((int)id, out state);
            return state;
        }
    }
}
