using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleStageActionGroup : BattleObj
    {
        private List<BattleStageActionInfo> m_actionInfoList;

        protected int m_index;

        public override void OnRelease()
        {
            m_index = default;
            m_actionInfoList = default;
            base.OnRelease();
        }

        public BattlePerformance GetPerformance(List<BattleStageActionInfo> actionInfoList, BattleVagueParamValueSet valueSet)
        {
            m_index = 0;
            m_actionInfoList = actionInfoList;
            BattlePerformanceStageActionGroup performance = m_battle.FetchObj<BattlePerformanceStageActionGroup>();
            performance.actionInfoList = actionInfoList;
            for (int i = 0; i < m_actionInfoList.Count; ++i)
            {
                HandleAction(m_actionInfoList[i], valueSet, performance.resultList);
            }
            return performance;
        }

        private void HandleAction(BattleStageActionInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            if (info == null)
            {
                return;
            }
            switch (info.actionType)
            {
                case BattleStageActionType.Parallel:
                    HandelParallel(info as BattleStageActionInfo_Parallel, valueSet, resultList);
                    break;
                case BattleStageActionType.Sequence:
                    HandelSequence(info as BattleStageActionInfo_Sequence, valueSet, resultList);
                    break;
                case BattleStageActionType.Move_Actor_Uid:
                    HandleMove_Actor_Uid(info as BattleStageActionInfo_Move_Actor_Uid, valueSet, resultList);
                    break;
                case BattleStageActionType.ChangeBehavior_Actor_Uid:
                    HandleChangeBehavior_Actor_Uid(info as BattleStageActionInfo_ChangeBehavior_Actor_Uid, valueSet, resultList);
                    break;
                case BattleStageActionType.ChangeTeam_Actor_Uid:
                    HandleChangeTeam_Actor_Uid(info as BattleStageActionInfo_ChangeTeam_Actor_Uid, valueSet, resultList);
                    break;
                case BattleStageActionType.Retreat_Actor_Uid:
                    HandleRetreat_Actor_Uid(info as BattleStageActionInfo_Retreat_Actor_Uid, valueSet, resultList);
                    break;
                case BattleStageActionType.Summon_Actor:
                    HandleSummon(info as BattleStageActionInfo_Summon_Actor, valueSet, resultList);
                    break;
                //case BattleStageActionType.EntitySummon:
                //    HandleSummon(info as BattleStageActionEntitySummonInfo, valueSet, resultList);
                //    break;
                //case BattleStageActionType.TeamDecisionMark:
                //    HandleTeamDecisionMark(info as BattleStageActionTeamDecisionMarkInfo, valueSet, resultList);
                //    break;
                case BattleStageActionType.AttachBuff_Actor_Uid:
                    HandleAttachBuff_Actor_Uid(info as BattleStageActionInfo_AttachBuff_Actor_Uid, valueSet, resultList);
                    break;
                case BattleStageActionType.AttachBuff_Actor_TeamUid:
                    HandleAttachBuff_Actor_TeamUid(info as BattleStageActionInfo_AttachBuff_Actor_TeamUid, valueSet, resultList);
                    break;
                case BattleStageActionType.AttachBuff_Actor_CampUid:
                    HandleAttachBuff_Actor_CampUid(info as BattleStageActionInfo_AttachBuff_Actor_CampUid, valueSet, resultList);
                    break;
                case BattleStageActionType.DialogSelection:
                    HandleDialogSelection(info as BattleStageActionInfo_DialogSelection, valueSet, resultList);
                    break;
                //case BattleStageActionType.EntityDamaged:
                //    HandleEntityDamaged(info as BattleStageActionEntityDamagedInfo, valueSet, resultList);
                //    break;
                //case BattleStageActionType.EntityHeal:
                //    HandleEntityHeal(info as BattleStageActionEntityHealInfo, valueSet, resultList);
                //    break;
                //case BattleStageActionType.TerrainEffectShow:
                //    HandleTerrainEffectShow(info as BattleStageActionTerrainEffectShowInfo, valueSet, resultList);
                //    break;
                //case BattleStageActionType.TerrainEffectHide:
                //    HandleTerrainEffectHide(info as BattleStageActionTerrainEffectHideInfo, valueSet, resultList);
                //    break;
                //case BattleStageActionType.Music:
                //    HandleMusic(info as BattleStageActionMusicInfo, valueSet, resultList);
                //    break;
            }
        }

        private void HandelParallel(BattleStageActionInfo_Parallel info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            for (int i = 0; i < info.actionList.Count; ++i)
            {
                HandleAction(info.actionList[i], valueSet, resultList);
            }
        }

        private void HandelSequence(BattleStageActionInfo_Sequence info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            for (int i = 0; i < info.actionList.Count; ++i)
            {
                HandleAction(info.actionList[i], valueSet, resultList);
            }
        }

        private void HandleMove_Actor_Uid(BattleStageActionInfo_Move_Actor_Uid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            EntityMoveResult result = m_battle.FetchObj<EntityMoveResult>();
            IEntity entity = m_battle.GetEntityByUid(info.actorUid);
            if (entity != null)
            {
                entity.SetLocation(info.movePos);
                result.entityUid = info.actorUid;
                result.movePos = info.movePos;
            }
            resultList.Add(result);
        }

        private void HandleChangeBehavior_Actor_Uid(BattleStageActionInfo_ChangeBehavior_Actor_Uid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            foreach(var entityUid in info.actorUidList)
            {
                IEntity entity = m_battle.GetEntityByUid(entityUid);
                entity.SetAIType(info.aiType);
            }
        }

        private void HandleChangeTeam_Actor_Uid(BattleStageActionInfo_ChangeTeam_Actor_Uid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            EntityTeamChangeResult result = m_battle.FetchObj<EntityTeamChangeResult>();
            result.teamUid = info.teamUid;
            foreach (var actorUid in info.actorUidList)
            {
                IEntity entity = m_battle.GetEntityByUid(actorUid);
                if (entity != null)
                {
                    m_battle.GetTeamByUid(info.teamUid).AddEntityFrom(entity, entity.GetTeam(), false);
                    result.entityUidList.Add(actorUid);
                }
            }
            resultList.Add(result);
        }

        private void HandleRetreat_Actor_Uid(BattleStageActionInfo_Retreat_Actor_Uid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            EntityRetreatResult result = m_battle.FetchObj<EntityRetreatResult>();
            foreach (var actorUid in info.actorUidList)
            {
                IEntity entity = m_battle.GetEntityByUid(actorUid);
                if (entity != null)
                {
                    result.entityUidList.AddNotContains(entity.uid);
                    m_battle.DestroyEntityById(entity.uid);
                }
            }
            resultList.Add(result);
        }

        private void HandleSummon(BattleStageActionInfo_Summon_Actor info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            var result = m_battle.FetchObj<BattleStageActionEntitySummonResult>();
            resultList.Add(result);
            foreach (var itemInfo in info.itemList)
            {
                var team = battle.GetTeamByUid(itemInfo.teamId);
                if (team == null)
                {
                    m_battle.logger?.LogShout("召唤角色的队伍找不到：" + itemInfo.teamId);
                    continue;
                }
                var dataGetter = battle.infoGetter.GetEntityDataGetter(team.controlledPlayer.playerId, EntityType.Actor, itemInfo.entityRid);

                var conflictEntity = m_battle.GetFirstEntityByPos(itemInfo.pos, EntityType.Actor);
                GridPosition pos = itemInfo.pos;
                if (conflictEntity != null)
                {
                    pos = BattleUtility.GetNearLocatablePos(battle, dataGetter, pos, 5);
                    if (pos == GridPosition.invalid)
                    {
                        continue;
                    }
                }
                var entity = m_battle.CreateStaticEntity(dataGetter, itemInfo.entityUid, pos, itemInfo.dirType, true);
                entity.SetCurLevel(itemInfo.level);
                if (itemInfo.aiType != EntityAIType.None)
                {
                    entity.SetAIType(itemInfo.aiType);
                }
                entity.SetCurHpRate(itemInfo.hpRate);
                entity.SetActionChance(itemInfo.hasActionChance);
                team.AddEntity(entity, false);
                var resultPart = m_battle.FetchObj<BattleStageActionEntitySummonPartResult>();
                resultPart.info = itemInfo;
                resultPart.entity = entity.Copy(m_battle);
                entity.InitBuffAttach(resultPart.buffAttachResultList);
                BattleUtility.TryCheckAndEnterTerrain(m_battle, entity, entity.GetLocation(), resultPart.buffAttachResultList, valueSet);
                BattleUtility.TryCheckAndEnterTerrainBuff(m_battle, entity, entity.GetLocation(), resultPart.buffAttachResultList, valueSet);
                result.partResultList.Add(resultPart);
            }
            m_battle.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.EntityCountInRangeChange, valueSet, result.buffChangeActiveResultList);
        }

        //private void HandleTeamDecisionMark(BattleStageActionTeamDecisionMarkInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        //{
        //    var team = m_battle.GetTeamByUid(info.teamUid);
        //    if (team != null)
        //    {
        //        team.AddDecisionMark(info.decisionMarkId, info.pos, info.entityUid);
        //        m_battle.eventListener?.SetTeamDecisionMark(info.teamUid, info.decisionMarkId, info.pos, info.entityUid);
        //    }
        //}

        private void HandleAttachBuff_Actor_Uid(BattleStageActionInfo_AttachBuff_Actor_Uid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            var result = m_battle.FetchObj<BuffAttachGroupResult>();
            resultList.Add(result);

            var actorUidList = info.actorUidList;
            int actorUidCount = actorUidList.Count;
            var buffItemList = info.buffList;
            var buffItemCount = buffItemList.Count;
            for (int i = 0; i < actorUidCount; ++i)
            {
                var actorUid = actorUidList[i];
                var entity = m_battle.GetEntityByUid(actorUid);
                if (entity == null)
                {
                    Debug.LogShout(m_battle, "场景事件【添加Buff】角色不存在:{0}", actorUid);
                    continue;
                }
                for (int j = 0; j < buffItemCount; ++j)
                {
                    var buffItem = buffItemList[j];
                    var attachResult = entity.AttachBuff(buffItem.buffRid, buffItem.lifeTime, false, null, valueSet);
                    if (attachResult != null)
                    {
                        result.attachResultList.Add(attachResult);
                    }
                }
            }
        }

        private void HandleAttachBuff_Actor_TeamUid(BattleStageActionInfo_AttachBuff_Actor_TeamUid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            var result = m_battle.FetchObj<BuffAttachGroupResult>();
            resultList.Add(result);

            var teamUidList = info.teamUidList;
            int teamUidCount = teamUidList.Count;
            var buffItemList = info.buffList;
            var buffItemCount = buffItemList.Count;
            for (int i = 0; i < teamUidCount; ++i)
            {
                var teamUid = teamUidList[i];
                var team = m_battle.GetTeamByUid(teamUid);
                if (team == null)
                {
                    Debug.LogShout(m_battle, "场景事件【添加Buff】队伍不存在:{0}", teamUid);
                    continue;
                }
                var entityUidList = team.entityUidList;
                var entityUidCount = entityUidList.Count;
                for (int j = 0; j < entityUidCount; ++j)
                {
                    var entityUid = entityUidList[j];
                    var entity = m_battle.GetEntityByUid(entityUid);
                    for (int k = 0; k < buffItemCount; ++k)
                    {
                        var buffItem = buffItemList[k];
                        var attachResult = entity.AttachBuff(buffItem.buffRid, buffItem.lifeTime, false, null, valueSet);
                        if (attachResult != null)
                        {
                            result.attachResultList.Add(attachResult);
                        }
                    }
                }
            }
        }

        private void HandleAttachBuff_Actor_CampUid(BattleStageActionInfo_AttachBuff_Actor_CampUid info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            var result = m_battle.FetchObj<BuffAttachGroupResult>();
            resultList.Add(result);

            var campUid = info.campUid;
            var buffItemList = info.buffList;
            var buffItemCount = buffItemList.Count;
            var teamList = m_battle.GetTeamList();
            var teamCount = teamList.Count;

            for (int i = 0; i < teamCount; ++i)
            {
                var team = teamList[i];
                if (team.campId != campUid)
                {
                    continue;
                }
                var entityUidList = team.entityUidList;
                var entityUidCount = entityUidList.Count;
                for (int j = 0; j < entityUidCount; ++j)
                {
                    var entityUid = entityUidList[j];
                    var entity = m_battle.GetEntityByUid(entityUid);
                    for (int k = 0; k < buffItemCount; ++k)
                    {
                        var buffItem = buffItemList[k];
                        var attachResult = entity.AttachBuff(buffItem.buffRid, buffItem.lifeTime, false, null, valueSet);
                        if (attachResult != null)
                        {
                            result.attachResultList.Add(attachResult);
                        }
                    }
                }
            }
        }

        private void HandleDialogSelection(BattleStageActionInfo_DialogSelection info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        {
            m_battle.SetWaitInputCommandType(FrameCommandType.DialogSelect);
        }

        //private void HandleEntityDamaged(BattleStageActionEntityDamagedInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        //{
        //    var procedureContext = m_battle.FetchObj<BattleActionProcedureContext>();
        //    procedureContext.Init();
        //    var procedureResult = m_battle.FetchObj<StageActionGroupInnerProcedureResult>();
        //    procedureContext.procedureResult = procedureResult;
        //    procedureContext.valueSet.procedureResult = procedureResult;
        //    resultList.Add(procedureResult);
        //    var actionResult = m_battle.FetchObj<BattleActionEffectResult>();
        //    procedureResult.AddResult(actionResult);
        //    var team = m_battle.GetTeamByUid(info.teamUid);
        //    var entityUidList = m_battle.FetchObj<List<int>>();
        //    if (info.useTeam)
        //    {
        //        foreach (var entityUid in team.entityUidList)
        //        {
        //            entityUidList.AddNotContains(entityUid);
        //        }
        //    }
        //    else
        //    {
        //        foreach (var entityUid in info.actorIdList)
        //        {
        //            entityUidList.AddNotContains(entityUid);
        //        }
        //    }
        //    foreach (var entityUid in entityUidList)
        //    {
        //        var targetEntity = m_battle.GetEntityByUid(entityUid);
        //        if (targetEntity == null || !targetEntity.IsAlive() || targetEntity.entityType != EntityType.Actor)
        //        {
        //            continue;
        //        }
        //        FixedValue basedValue = info.isRateOfCurHpRate ? targetEntity.GetCurHp() : targetEntity.GetAttributeValue(AttributeId.HpMax);
        //        FixedValue damage = basedValue * info.damageHpRate / 100 + info.damageValue;
        //        var curHp = targetEntity.GetCurHp();

        //        var resultClip = m_battle.FetchObj<BattleActionEffectDamageEntityResultClip>();
        //        resultClip.resultType = BattleActionEffectResultType.Success;
        //        resultClip.entityUid = targetEntity.uid;
        //        resultClip.entityRid = targetEntity.rid;
        //        resultClip.expectDamage = damage;
        //        resultClip.preHp = curHp;
        //        actionResult.clipList.Add(resultClip);
        //        if (damage >= curHp)
        //        {
        //            resultClip.curHp = 0;
        //            resultClip.isDead = true;
        //            targetEntity.SetCurHp(0);
        //            if (targetEntity.IsAlive())
        //            {
        //                targetEntity.SetDying();

        //                BattleLogicEventEntityDead deadEvent = m_battle.FetchObj<BattleLogicEventEntityDead>();
        //                deadEvent.deadEntityUid = targetEntity.uid;
        //                procedureContext.PushEvent(deadEvent);
        //            }
        //        }
        //        else
        //        {
        //            targetEntity.AddCurHp(-damage);
        //            resultClip.curHp = targetEntity.GetCurHp();
        //        }
        //    }
        //    procedureContext.ExecuteEvent();
        //    procedureContext.Release();
        //    m_battle.Release(entityUidList);
        //}

        //private void HandleEntityHeal(BattleStageActionEntityHealInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        //{
        //    var procedureContext = m_battle.FetchObj<BattleActionProcedureContext>();
        //    procedureContext.Init();
        //    var procedureResult = m_battle.FetchObj<StageActionGroupInnerProcedureResult>();
        //    procedureContext.procedureResult = procedureResult;
        //    procedureContext.valueSet.procedureResult = procedureResult;
        //    resultList.Add(procedureResult);
        //    var actionResult = m_battle.FetchObj<BattleActionEffectResult>();
        //    procedureResult.AddResult(actionResult);
        //    var targetEntity = m_battle.GetEntityByUid(info.entityUid);
        //    if(targetEntity != null && targetEntity.IsAlive())
        //    {
        //        FixedValue heal = targetEntity.GetAttributeValue(AttributeId.HpMax) * info.healHpRate / 100 + info.healValue;
        //        var curHp = targetEntity.GetCurHp();
        //        var maxHp = targetEntity.GetAttributeValue(AttributeId.HpMax);

        //        var resultClip = m_battle.FetchObj<BattleActionEffectHealEntityResultClip>();
        //        resultClip.resultType = BattleActionEffectResultType.Success;
        //        resultClip.entityUid = targetEntity.uid;
        //        resultClip.entityRid = targetEntity.rid;
        //        resultClip.expectHeal = heal;
        //        actionResult.clipList.Add(resultClip);
        //        if (curHp + heal > maxHp)
        //        {
        //            resultClip.finalHeal = maxHp - curHp;
        //            targetEntity.SetCurHp(maxHp);
        //        }
        //        else
        //        {
        //            targetEntity.AddCurHp(heal);
        //            resultClip.finalHeal = heal;
        //        }
        //    }
        //    procedureContext.ExecuteEvent();
        //    procedureContext.Release();
        //}

        //private void HandleTerrainEffectShow(BattleStageActionTerrainEffectShowInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        //{
        //    if (info.isLoop)
        //    {
        //        m_battle.AddTerrainEffect(info.uid, info.rid, info.pos);
        //    }
        //}

        //private void HandleTerrainEffectHide(BattleStageActionTerrainEffectHideInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        //{
        //    m_battle.RemoveTerrainEffect(info.uid);
        //}

        //private void HandleMusic(BattleStageActionMusicInfo info, BattleVagueParamValueSet valueSet, List<BattleActionResult> resultList)
        //{
        //    m_battle.stageManageComponent.bgmPath = info.path;
        //}
    }
}
