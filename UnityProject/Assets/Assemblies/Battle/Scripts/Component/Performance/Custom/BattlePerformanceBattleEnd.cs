using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattlePerformanceBattleEnd : BattlePerformance
    {
        public bool isWin;

        public override BattlePerformanceType performanceType
        {
            get { return BattlePerformanceType.BattleEnd; }
        }

        public override void OnRelease()
        {
            isWin = default;
            base.OnRelease();
        }

        protected override void OnEnd()
        {
            base.OnEnd();
        }
    }
}
