using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattlePerformanceStepStart : BattlePerformance
    {
        public int stepIndexUpdate;

        public override BattlePerformanceType performanceType
        {
            get { return BattlePerformanceType.StepStart; }
        }

        public override void OnRelease()
        {
            stepIndexUpdate = default;
            base.OnRelease();
        }
    }
}
