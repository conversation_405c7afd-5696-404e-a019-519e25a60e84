using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleRefereeComponent : BattleComponent
    {
        public bool needTriggerStageWin;
        public bool needTriggerStageLose;

        protected override void OnUnInit()
        {
            needTriggerStageWin = false;
            needTriggerStageLose = false;
            base.OnUnInit();
        }

        public void ClearTriggerStageWinLose()
        {
            needTriggerStageWin = false;
            needTriggerStageLose = false;
        }

        public void SetStageWin_Gm()
        {
            needTriggerStageWin = true;
        }

        public void SetStageLose_Gm()
        {
            needTriggerStageLose = true;
        }

        //存在一个Camp胜利，或者所有Camp失败，就结束战斗
        public bool CheckAllCampRefereeFinished()
        {
            var curStageInfo = m_battle.GetCurStageInfo();
            var stageReferee = curStageInfo.stageRefreree;
            bool anyCampNotReferee = false;
            int campRefereeListCount = stageReferee.campRefereeList.Count;
            for (int i = 0; i < campRefereeListCount; ++i)
            {
                var campReferee = stageReferee.campRefereeList[i];
                int campId = campReferee.campId;
                if (m_battle.TryGetCampRefereeResult(campId, out bool result))
                {
                    if (result)
                    {
                        //存在一个Camp胜利
                        return true;
                    }
                }
                else
                {
                    anyCampNotReferee = true;
                }
            }
            return !anyCampNotReferee;
        }

        public bool TickReferee(BattleRefereeContext context)
        {
            var curStageInfo = m_battle.GetCurStageInfo();
            var stageReferee = curStageInfo.stageRefreree;
            if (needTriggerStageWin)
            {
                SetAllCampRefereeResult(stageReferee, true);
                ClearTriggerStageWinLose();
                return true;
            }
            if (needTriggerStageLose)
            {
                SetAllCampRefereeResult(stageReferee, false);
                ClearTriggerStageWinLose();
                return true;
            }
            if (context.stageStateId == BattleStageStateId.TurnEnd)
            {
                if (m_battle.GetCurTurnIndex() > stageReferee.maxTurnCount)
                {
                    SetAllCampRefereeResult(stageReferee, false);
                    return true;
                }
            }
            bool sthChanged = false;
            int campRefereeListCount = stageReferee.campRefereeList.Count;
            for (int i = 0; i < campRefereeListCount; ++i)
            {
                var campReferee = stageReferee.campRefereeList[i];
                context.campReferee = campReferee;
                sthChanged |= TickCampReferee(campReferee, context);
            }
            return sthChanged;
        }

        private bool TickCampReferee(BattleCampRefereeInfo referee, BattleRefereeContext context)
        {
            var battle = m_battle;
            if (battle.HasCampRefereeResult(referee.campId))
            {
                return false;
            }
            int winSituationCount = referee.winSituationList.Count;
            for (int i = 0; i < winSituationCount; ++i)
            {
                var situation = referee.winSituationList[i];
                if (CheckCampRefereeSituation(situation, context))
                {
                    battle.SetCampRefereeResult(referee.campId, true);
                    return true;
                }
            }
            int loseSituationCount = referee.loseSituationList.Count;
            for (int i = 0; i < loseSituationCount; ++i)
            {
                var situation = referee.loseSituationList[i];
                if (CheckCampRefereeSituation(situation, context))
                {
                    battle.SetCampRefereeResult(referee.campId, false);
                    return true;
                }
            }
            return false;
        }

        private bool CheckCampRefereeSituation(BattleCampRefereeSituationInfo situation, BattleRefereeContext context)
        {
            if (!situation.enabled)
            {
                return false;
            }
            switch (situation.refereeType)
            {
                case BattleCampRefereeSituationType.TurnEnd:
                    return CheckCampRefereeSituation_TurnEnd(situation, context);
                case BattleCampRefereeSituationType.EnemyAllDead:
                    return CheckCampRefereeSituation_EnemyAllDead(situation, context);
                case BattleCampRefereeSituationType.SelfAllDead:
                    return CheckCampRefereeSituation_SelfAllDead(situation, context);
                case BattleCampRefereeSituationType.EntityListAllDead:
                    return CheckCampRefereeSituation_EntityListAllDead(situation, context);
                case BattleCampRefereeSituationType.AnyActorOccupyAnyGrid:
                    return CheckCampRefereeSituation_AnyActorOccupyAnyGrid(situation, context);
            }
            throw new ToDoException(situation.refereeType.ToString());
        }

        private bool CheckCampRefereeSituation_TurnEnd(BattleCampRefereeSituationInfo baseOne, BattleRefereeContext context)
        {
            if (context.stageStateId != BattleStageStateId.TurnEnd)
            {
                return false;
            }
            var situation = baseOne as BattleCampRefereeSituationInfo_TurnEnd;
            return m_battle.GetCurTurnIndex() == situation.turnIndex - 1;
        }

        private bool CheckCampRefereeSituation_EnemyAllDead(BattleCampRefereeSituationInfo baseOne, BattleRefereeContext context)
        {
            var situation = baseOne as BattleCampRefereeSituationInfo_EnemyAllDead;
            var entityList = m_battle.GetEntityList();
            int entityCount = entityList.Count;
            var campId = context.campReferee.campId;
            for (int i = 0; i < entityCount; ++i)
            {
                var entity = entityList[i];
                if (entity.GetBattleCampId() == campId)
                {
                    continue;
                }
                if (entity.IsControllable() && entity.IsAlive())
                {
                    return false;
                }
            }
            return true;
        }

        private bool CheckCampRefereeSituation_SelfAllDead(BattleCampRefereeSituationInfo baseOne, BattleRefereeContext context)
        {
            var situation = baseOne as BattleCampRefereeSituationInfo_SelfAllDead;
            var entityList = m_battle.GetEntityList();
            int entityCount = entityList.Count;
            var campId = context.campReferee.campId;
            var defaultPlayerId = m_battle.GetDefaultPlayerId();
            for (int i = 0; i < entityCount; ++i)
            {
                var entity = entityList[i];
                if (entity.GetBattleCampId() != campId)
                {
                    continue;
                }
                var controlledPlayer = entity.GetTeam().controlledPlayer;
                if (controlledPlayer == null || controlledPlayer.playerId == defaultPlayerId)
                {
                    continue;
                }
                if (entity.IsControllable() && entity.IsAlive())
                {
                    return false;
                }
            }
            return true;
        }

        private bool CheckCampRefereeSituation_EntityListAllDead(BattleCampRefereeSituationInfo baseOne, BattleRefereeContext context)
        {
            var situation = baseOne as BattleCampRefereeSituationInfo_EntityListAllDead;
            var entityList = m_battle.GetEntityList();
            int entityCount = entityList.Count;
            var campId = context.campReferee.campId;
            for (int i = 0; i < entityCount; ++i)
            {
                var entity = entityList[i];
                if (!situation.entityUidList.Contains(entity.uid))
                {
                    continue;
                }
                if (entity.IsControllable() && entity.IsAlive())
                {
                    return false;
                }
            }
            return true;
        }

        private bool CheckCampRefereeSituation_AnyActorOccupyAnyGrid(BattleCampRefereeSituationInfo baseOne, BattleRefereeContext context)
        {
            var situation = baseOne as BattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid;
            int count = situation.actorUidList.Count;
            for (int i = 0; i < count; ++i)
            {
                var entity = m_battle.GetEntityByUid(situation.actorUidList[i]);
                if (entity != null && entity.IsLocatedOn(situation.pos))
                {
                    return true;
                }
            }
            return false;
        }

        private void SetAllCampRefereeResult(BattleStageRefereeInfo stageReferee, bool result)
        {
            int campRefereeListCount = stageReferee.campRefereeList.Count;
            for (int i = 0; i < campRefereeListCount; ++i)
            {
                var campReferee = stageReferee.campRefereeList[i];
                int campId = campReferee.campId;
                m_battle.SetCampRefereeResult(campId, result);
            }
        }
    }
}
