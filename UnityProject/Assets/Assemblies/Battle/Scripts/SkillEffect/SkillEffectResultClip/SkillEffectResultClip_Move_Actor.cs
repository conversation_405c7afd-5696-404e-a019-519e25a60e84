using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_Move_Actor : SkillEffectResultClip
    {
        public int entityUid;
        public GridPosition movePosition;
        public List<BattleActionEffectResult> effectResultList = new List<BattleActionEffectResult>();

        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Move_Actor; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            movePosition = default;
            effectResultList.ReleaseAll();
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
