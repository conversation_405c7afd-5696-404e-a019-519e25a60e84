using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public abstract class SkillEffectResultClip_Summon_Actor : SkillEffectResultClip
    {
        public int summonerUid;
        public Entity entity;
        public int destroyEntityUid;
        public List<BuffAttachResult> buffAttachResultList = new List<BuffAttachResult>();

        public override void OnRelease()
        {
            summonerUid = default;
            entity.Release();
            entity = null;
            destroyEntityUid = default;
            buffAttachResultList.ReleaseAll();
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entity.uid);
        }
    }
}
