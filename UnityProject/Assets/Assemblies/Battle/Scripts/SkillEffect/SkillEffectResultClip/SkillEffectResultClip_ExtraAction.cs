using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_ExtraAction : SkillEffectResultClip
    {
        public int entityUid;
        public CastSkillSource source;

        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.ExtraAction; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            source = default;
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
