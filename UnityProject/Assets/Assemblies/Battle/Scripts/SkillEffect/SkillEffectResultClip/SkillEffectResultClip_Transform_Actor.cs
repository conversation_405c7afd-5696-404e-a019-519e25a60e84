using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_Transform_Actor : SkillEffectResultClip
    {
        public int entityUid;
        public Entity entity;

        public override void OnRelease()
        {
            entity.Release();
            base.OnRelease();
        }

        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Transform_Actor; }
        }
    }
}
