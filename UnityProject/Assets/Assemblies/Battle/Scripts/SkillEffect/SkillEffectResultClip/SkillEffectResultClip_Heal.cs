using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_Heal : SkillEffectResultClip
    {
        public int entityUid;
        public int entityRid;
        public SkillEffectInfo_Heal healInfo;
        public FixedValue expectHeal;
        public FixedValue finalHeal;
        public Entity srcEntityCopy;
        public Entity targetEntityCopy;
        public List<BuffChangeActiveResult> buffChangeActiveResultList = new List<BuffChangeActiveResult>();

        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Heal; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            entityRid = default;
            healInfo = default;
            expectHeal = default;
            finalHeal = default;
            srcEntityCopy.Release();
            srcEntityCopy = null;
            targetEntityCopy.Release();
            targetEntityCopy = null;
            buffChangeActiveResultList.ReleaseAll();
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
