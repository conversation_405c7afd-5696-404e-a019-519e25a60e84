using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_Teleport : SkillEffectResultClip
    {
        public int entityUid;
        public GridPosition teleportPos;

        public override SkillEffectType effectType
        {
            get { return SkillEffectType.Teleport; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            teleportPos = default;
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
