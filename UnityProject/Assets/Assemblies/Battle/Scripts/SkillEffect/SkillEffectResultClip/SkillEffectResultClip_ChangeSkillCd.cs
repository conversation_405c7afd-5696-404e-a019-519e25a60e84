using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_ChangeSkillCd : SkillEffectResultClip
    {
        public int entityUid;
        public int skillUid;
        public int updateCd;

        public override SkillEffectType effectType
        {
            get { return SkillEffectType.ChangeSkillCd; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            skillUid = default;
            updateCd = default;
            base.OnRelease();
        }
    }
}
