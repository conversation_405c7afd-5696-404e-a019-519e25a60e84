using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_Teleport_Actor : SkillEffectResultClip
    {
        public int entityUid;
        public GridPosition teleportPos;

        public override void OnRelease()
        {
            entityUid = default;
            teleportPos = default;
            base.OnRelease();
        }

        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Teleport_Actor; }
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
