using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public abstract class SkillEffectResultClip_ExtraMove : SkillEffectResultClip
    {
        public int entityUid;
        public int extraMoveDist;
        public CastSkillSource source;

        public override void OnRelease()
        {
            entityUid = default;
            extraMoveDist = default;
            source = default;
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
