using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_Damage : SkillEffectResultClip
    {
        public int entityUid;
        public int entityRid;
        public int entityUidAttacker;
        public int entityRidAttacker;
        public BattleDamageInfo damageInfo;
        public Entity srcEntityCopy;
        public Entity targetEntityCopy;
        public FixedValue preHp;
        public FixedValue preHpAttacker;
        public FixedValue curHp;
        public FixedValue curHpAttacker;
        public FixedValue maxHpAttacker;
        public FixedValue expectDamage;
        public FixedValue expectDamageReflect;
        public FixedValue expectHealthSteal;
        public FixedValue finalHealthSteal;
        public bool isDead;
        public bool isDeadAttacker;
        public bool isCritical;
        public bool isRegist;
        public List<BuffChangeActiveResult> srcBuffChangeActiveResultList = new List<BuffChangeActiveResult>();
        public List<BuffChangeActiveResult> targetBuffChangeActiveResultList = new List<BuffChangeActiveResult>();
        public List<BuffChangeResult> buffChangeResultList = new List<BuffChangeResult>();

        public override SkillEffectType effectType
        {
            get { return SkillEffectType.Damage; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            entityRid = default;
            entityUidAttacker = default;
            entityRidAttacker = default;
            preHp = default;
            preHpAttacker = default;
            curHp = default;
            curHpAttacker = default;
            damageInfo = null;
            expectDamage = default;
            expectDamageReflect = default;
            expectHealthSteal = default;
            finalHealthSteal = default;
            srcEntityCopy.Release();
            srcEntityCopy = null;
            targetEntityCopy.Release();
            targetEntityCopy = null;
            isDead = false;
            isRegist = false;
            isDeadAttacker = false;
            isCritical = false;
            srcBuffChangeActiveResultList.ReleaseAll();
            targetBuffChangeActiveResultList.ReleaseAll();
            buffChangeResultList.ReleaseAll();
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
