using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectResultClip_ChangeTeamEnergy : SkillEffectResultClip
    {
        public int entityUid;
        public int teamUid;
        public int updateTeamEnergy;

        public override void OnRelease()
        {
            entityUid = default;
            teamUid = default;
            updateTeamEnergy = default;
            base.OnRelease();
        }

        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.ChangeTeamEnergy; }
        }
    }
}
