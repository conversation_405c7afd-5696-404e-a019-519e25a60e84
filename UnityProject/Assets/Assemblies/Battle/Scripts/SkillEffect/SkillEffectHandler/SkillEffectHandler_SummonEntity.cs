//using System;
//using System.Collections;
//using System.Collections.Generic;
//using Phoenix.Core;
//using Phoenix.ConfigData;

//namespace Phoenix.Battle
//{
//    public class BattleActionEffectSummonEntityHandler : BattleActionEffectHandler
//    {
//        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
//        {
//            var srcEntity = handleParams.castSkillContext.srcEntity;
//            BattleActionEffectSummonEntityInfo effectInfo = handleParams.effectInfo as BattleActionEffectSummonEntityInfo;
//            if (effectInfo.funcType == BattleActionEffectSummonFuncType.RidAndPos)
//            {
//                var info = effectInfo as BattleActionEffectSummonEntityRidAndPosInfo;
//                var posCollection = m_battle.CreateFieldSummaryForPosCollection();
//                BattleUtility.CollectRangeByBattleActionEffectFunc(info.posInfo, handleParams.castSkillContext.procedureContext.valueSet, posCollection);
//                var posList = posCollection.GetPosList();
//                if (posList.Count > 0)
//                {
//                    foreach (var item in info.itemList)
//                    {
//                        var dataGetter = m_battle.infoGetter.GetEntityDataGetter(srcEntity.GetPlayerId(), item.entityInfo.entityType, item.entityInfo.rid);
//                        if (dataGetter == null)
//                        {
//                            continue;
//                        }
//                        var pos = BattleUtility.GetNearLocatablePos(battle, dataGetter, posList[0], 3);
//                        if (pos == GridPosition.invalid)
//                        {
//                            break;
//                        }
//                        HandleSummon(srcEntity, item.entityInfo, srcEntity.GetTeamUid(), pos, srcEntity.GetDir(), result, handleParams.castSkillContext.procedureContext.valueSet);
//                    }
//                }
//                posCollection.Release();
//            }
//            else if (effectInfo.funcType == BattleActionEffectSummonFuncType.RidAndFullRange)
//            {
//                var info = effectInfo as BattleActionEffectSummonEntityRidAndFullRangeInfo;
//                var posCollection = m_battle.CreateFieldSummaryForPosCollection();
//                BattleUtility.CollectRangeByBattleActionEffectFunc(info.posInfo, handleParams.castSkillContext.procedureContext.valueSet, posCollection);
//                foreach (var pos in posCollection.GetPosList())
//                {
//                    HandleSummon(srcEntity, info.entityInfo, srcEntity.GetTeamUid(), pos, srcEntity.GetDir(), result, handleParams.castSkillContext.procedureContext.valueSet);
//                }
//                posCollection.Release();
//            }
//            else if (effectInfo.funcType == BattleActionEffectSummonFuncType.RandomRidAndPos)
//            {
//                if (battle.CheckRandomEnable())
//                {
//                    var info = effectInfo as BattleActionEffectSummonEntityRandomRidAndPosInfo;
//                    var posCollection = m_battle.CreateFieldSummaryForPosCollection();
//                    BattleUtility.CollectRangeByBattleActionEffectFunc(info.posInfo, handleParams.castSkillContext.procedureContext.valueSet, posCollection);
//                    var posList = posCollection.GetPosList();
//                    if (posList.Count > 0)
//                    {
//                        var indexList = m_battle.FetchObj<List<int>>();
//                        while (indexList.Count < info.count)
//                        {
//                            int index = m_battle.RandomInt(0, info.count - 1);
//                            if (indexList.Contains(index))
//                            {
//                                continue;
//                            }
//                            var itemInfo = info.itemList[index];
//                            var dataGetter = m_battle.infoGetter.GetEntityDataGetter(srcEntity.GetPlayerId(), itemInfo.entityType, itemInfo.rid);
//                            var pos = BattleUtility.GetNearLocatablePos(battle, dataGetter, posList[0], 3);
//                            if (pos == GridPosition.invalid)
//                            {
//                                break;
//                            }
//                            HandleSummon(srcEntity, itemInfo, srcEntity.GetTeamUid(), pos, srcEntity.GetDir(), result, handleParams.castSkillContext.procedureContext.valueSet);
//                            indexList.Add(index);
//                            if (!info.canRepeat && indexList.Count == info.count)
//                            {
//                                break;
//                            }
//                        }
//                        battle.Release(indexList);
//                    }
//                    posCollection.Release();
//                }
//            }
//            m_battle.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.EntityCountInRangeChange, handleParams.castSkillContext.procedureContext.valueSet, result.buffChangeActiveResultList);
//        }

//        private void HandleSummon(IEntity srcEntity, BattleActionEffectSummonEntityInitInfo enittyInitInfo, int teamId, GridPosition gridPosition, GridDirType gridDir, BattleActionEffectResult result, BattleVagueParamValueSet valueSet)
//        {
//            IEntityDataGetter getter = m_battle.infoGetter.GetEntityDataGetter(srcEntity.GetPlayerId(), enittyInitInfo.entityType, enittyInitInfo.rid); ;
//            var resultClip = m_battle.FetchObj<BattleActionEffectSummonEntityResultClip>();
//            if (getter.GetEntityType() == EntityType.TerrainBuff)
//            {
//                var terrainBuffDataGetter = getter as ITerrainBuffDataGetter;
//                if (terrainBuffDataGetter.GetGroupId() != TerrainBuffGroupId.None)
//                {
//                    var conflictEntityList = m_battle.GetEntityListByFieldSummary(gridPosition);
//                    foreach (var conflictEntity in conflictEntityList)
//                    {
//                        if (conflictEntity.entityType != EntityType.TerrainBuff)
//                        {
//                            continue;
//                        }
//                        var conflictTerrainBuffDataGetter = conflictEntity.dataGetter as ITerrainBuffDataGetter;
//                        if (terrainBuffDataGetter.GetGroupId() == conflictTerrainBuffDataGetter.GetGroupId())
//                        {
//                            resultClip.destroyEntityUid = conflictEntity.uid;
//                            m_battle.DestroyEntityById(conflictEntity.uid);
//                            break;
//                        }
//                    }
//                }
//            }
//            else
//            {
//                var creatureInfo = srcEntity.GetSummonCreatureInfo(enittyInitInfo.rid, false);
//                if (creatureInfo != null && enittyInitInfo.maxCount > 0)
//                {
//                    int removeCount = creatureInfo.entityUidList.Count - enittyInitInfo.maxCount + 1;
//                    for (int i = 0; i < removeCount; ++i)
//                    {
//                        if (creatureInfo.entityUidList.Count == 0)
//                        {
//                            break;
//                        }
//                        resultClip.destroyEntityUid = creatureInfo.entityUidList.GetValueSafely(0);
//                        creatureInfo.entityUidList.RemoveAt(0);
//                    }
//                }
//            }
//            Entity summonEntity = m_battle.CreateDynamicEntity(getter, gridPosition, gridDir, true);
//            if (summonEntity != null)
//            {
//                summonEntity.SetActionChance(true);
//                if (summonEntity.HasActionChance())
//                {
//                    m_battle.SetEntityMustAct(summonEntity.uid);
//                }
//                BattleTeam team = m_battle.GetTeamByUid(teamId);
//                if (team != null)
//                {
//                    team.AddEntity(summonEntity, false);
//                }
//                if (enittyInitInfo.needLifeTick)
//                {
//                    summonEntity.StartLifeTime(enittyInitInfo.lifeTime);
//                }

//                resultClip.entity = summonEntity.Copy(m_battle);
//                resultClip.summonerUid = srcEntity.uid;
//                summonEntity.InitBuffAttach(resultClip.buffAttachResultList);
//                BattleUtility.TryCheckAndEnterTerrain(m_battle, summonEntity, summonEntity.GetLocation(), resultClip.buffAttachResultList, valueSet);
//                BattleUtility.TryCheckAndEnterTerrainBuff(m_battle, summonEntity, summonEntity.GetLocation(), resultClip.buffAttachResultList, valueSet);
//                if (enittyInitInfo.maxCount > 0)
//                {
//                    var creatureInfo = srcEntity.GetSummonCreatureInfo(enittyInitInfo.rid, true);
//                    creatureInfo.entityUidList.Add(summonEntity.uid);
//                }
//                summonEntity.SetSummonerUid(srcEntity.uid);
//            }
//            result.clipList.Add(resultClip);
//        }
//    }
//}
