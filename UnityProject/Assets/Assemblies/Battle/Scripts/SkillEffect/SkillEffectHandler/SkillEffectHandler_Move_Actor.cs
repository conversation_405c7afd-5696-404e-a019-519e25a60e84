using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_Move_Actor : SkillEffectHandler
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            //var srcEntity = handleParams.castSkillContext.srcEntity;
            //BattleActionEffectMoveEntityInfo effectInfo = handleParams.effectInfo as BattleActionEffectMoveEntityInfo;

            //var targetEntityList = m_battle.FetchObj<List<IEntity>>();
            //BattleUtility.CollectEntityListByBattleActionEffectFunc(effectInfo.targetInfo, handleParams.castSkillContext.procedureContext.valueSet, targetEntityList);

            //GridDirType dirType = GridDirType.None;
            //var stepResult = handleParams.castSkillContext.procedureContext.valueSet.stepResult;
            //var lastStepWrap = stepResult.stepWrapList.GetLastValueSafely();
            //GridPosition centerPos = GridPosition.invalid;
            //if (lastStepWrap != null && lastStepWrap.stepInfo.funcType == TargetSelectFuncType.Dir)
            //{
            //    dirType = lastStepWrap.dir;
            //    centerPos = lastStepWrap.gridPosition;
            //}
            //if (dirType == GridDirType.None && lastStepWrap != null)
            //{
            //    dirType = lastStepWrap.GetGridDir();
            //}
            //if (dirType != GridDirType.None)
            //{
            //    int sign = 1;
            //    int distance = effectInfo.distance;
            //    if (effectInfo.distance >= 0)
            //    {
            //        BattleSortUtility.SortEntity_Distance(m_battle, targetEntityList, centerPos, true);
            //    }
            //    else
            //    {
            //        BattleSortUtility.SortEntity_Distance(m_battle, targetEntityList, centerPos, false);
            //        sign = -1;
            //        distance = -distance;
            //    }
            //    foreach (var targetEntity in targetEntityList)
            //    {
            //        var resultClip = CreateResultClipAndCheckImmune<BattleActionEffectMoveEntityResultClip>(targetEntity, handleParams.castSkillContext.procedureContext.valueSet);
            //        resultClip.entityUid = targetEntity.uid;
            //        result.clipList.Add(resultClip);
            //        if (resultClip.resultType == BattleActionEffectResultType.Success)
            //        {
            //            GridPosition prePos = targetEntity.GetLocation();
            //            GridPosition movePos = prePos;
            //            bool interrupt = false;
            //            for (int i = 0; i <= distance; ++i)
            //            {
            //                GridPosition position = BattleUtility.GetPositionByDirAndDistance(targetEntity.GetLocation(), dirType, i * sign);
            //                if (targetEntity.CheckLocatable(position))
            //                {
            //                    movePos = position;
            //                }
            //                else
            //                {
            //                    interrupt = true;
            //                    break;
            //                }
            //            }
            //            resultClip.movePosition = movePos;
            //            targetEntity.SetLocation(movePos);

            //            if (interrupt && effectInfo.effectInfoList.Count > 0)
            //            {
            //                CastSkillContext castSkillContext = m_battle.FetchObj<CastSkillContext>();
            //                castSkillContext.srcEntity = srcEntity;
            //                castSkillContext.source = handleParams.castSkillContext.source;
            //                castSkillContext.procedureContext = handleParams.castSkillContext.procedureContext;
            //                castSkillContext.stepResult = m_battle.FetchObj<TargetSelectStepResult>();
            //                castSkillContext.stepResult.rootWrap = TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(srcEntity), GridDirType.None, null);
            //                castSkillContext.stepResult.stepWrapList.Add(TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(targetEntity), GridDirType.None, null));
            //                var tempStepResult = castSkillContext.procedureContext.valueSet.stepResult;
            //                castSkillContext.procedureContext.valueSet.stepResult = castSkillContext.stepResult;
            //                SkillLogicUtility.CastEffectGroup(m_battle, effectInfo.effectInfoList, castSkillContext, resultClip.effectResultList);
            //                castSkillContext.procedureContext.valueSet.stepResult = tempStepResult;
            //            }
            //            BattleLogicEventEntityMove entityMoveEvent = m_battle.FetchObj<BattleLogicEventEntityMove>();
            //            entityMoveEvent.entityUid = targetEntity.uid;
            //            entityMoveEvent.moveDist = prePos.DistanceTo(movePos);
            //            entityMoveEvent.prePos = prePos;
            //            entityMoveEvent.movePos = movePos;
            //            handleParams.castSkillContext.procedureContext.PushEvent(entityMoveEvent);
            //        }
            //    }
            //}
            //m_battle.Release(targetEntityList);
        }
    }
}
