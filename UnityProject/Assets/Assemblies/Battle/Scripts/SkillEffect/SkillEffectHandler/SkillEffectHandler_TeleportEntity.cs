using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_Teleport_Actor : SkillEffectHandler_SingleTarget
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            var srcEntity = handleParams.castSkillContext.srcEntity;
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_Teleport_Actor;
            var targetEntityList = m_battle.FetchObj<List<IEntity>>();
            CollectTarget(handleParams, targetEntityList);
            //BattleUtility.GetPreAnnounceGridList
            //if (effectInfo.targetGridInfo.funcType == BattleActionEffectFuncOfGridType.Origin || effectInfo.targetGridInfo.funcType == BattleActionEffectFuncOfGridType.TargetSelect)
            //{
            //    GridPosition tarPos = GridPosition.invalid;
            //    if (effectInfo.targetGridInfo.funcType == BattleActionEffectFuncOfGridType.Origin)
            //    {
            //        tarPos = srcEntity.GetLocation();
            //    }
            //    else if (effectInfo.targetGridInfo.funcType == BattleActionEffectFuncOfGridType.TargetSelect)
            //    {
            //        var tarSelectInfo = effectInfo.targetGridInfo as BattleActionEffectFuncOfGridTargetSelectInfo;
            //        var stepWrap = handleParams.castSkillContext.stepResult.stepWrapList.GetValueSafely(tarSelectInfo.resultIndex);
            //        if (stepWrap != null)
            //        {
            //            tarPos = stepWrap.gridPosition;
            //        }
            //    }
            //    if (tarPos != GridPosition.invalid)
            //    {
            //        foreach (var targetEntity in targetEntityList)
            //        {
            //            var pos = BattleUtility.GetNearLocatablePos(battle, targetEntity.dataGetter, tarPos, 3, targetEntity);
            //            if (pos == GridPosition.invalid)
            //            {
            //                continue;
            //            }
            //            var resultClip = CreateResultClipAndCheckImmune<BattleActionEffectTeleportEntityResultClip>(targetEntity, handleParams.castSkillContext.procedureContext.valueSet);
            //            resultClip.entityUid = targetEntity.uid;
            //            result.clipList.Add(resultClip);
            //            if (resultClip.resultType == BattleActionEffectResultType.Success)
            //            {
            //                var prePos = targetEntity.GetLocation();
            //                targetEntity.SetLocation(pos);
            //                resultClip.entityUid = targetEntity.uid;
            //                resultClip.teleportPos = pos;

            //                BattleLogicEventEntityMove entityMoveEvent = m_battle.FetchObj<BattleLogicEventEntityMove>();
            //                entityMoveEvent.entityUid = targetEntity.uid;
            //                entityMoveEvent.prePos = prePos;
            //                entityMoveEvent.movePos = pos;
            //                entityMoveEvent.isTeleport = true;
            //                handleParams.castSkillContext.procedureContext.PushEvent(entityMoveEvent);
            //            }
            //        }
            //    }
            //}
            //else if (effectInfo.targetGridInfo.funcType == BattleActionEffectFuncOfGridType.RangeRandom)
            //{
            //    if (battle.CheckRandomEnable())
            //    {
            //        GridPosition tarPos = GridPosition.invalid;
            //        var randomRangeInfo = effectInfo.targetGridInfo as BattleActionEffectFuncOfGridRangeRandomInfo;
            //        if (randomRangeInfo.centerFuncType == BattleActionEffectFuncOfGridType.Origin)
            //        {
            //            tarPos = srcEntity.GetLocation();
            //        }
            //        else if (randomRangeInfo.centerFuncType == BattleActionEffectFuncOfGridType.TargetSelect)
            //        {
            //            var tarSelectInfo = effectInfo.targetGridInfo as BattleActionEffectFuncOfGridTargetSelectInfo;
            //            var stepWrap = handleParams.castSkillContext.stepResult.stepWrapList.GetValueSafely(tarSelectInfo.resultIndex);
            //            if (stepWrap != null)
            //            {
            //                tarPos = stepWrap.gridPosition;
            //            }
            //        }
            //        if (tarPos != GridPosition.invalid)
            //        {
            //            var posList = m_battle.FetchObj<List<GridPosition>>();
            //            var posCollection = m_battle.CreateFieldSummaryForPosCollection();
            //            foreach (var targetEntity in targetEntityList)
            //            {
            //                TargetSelectUtility.AppendRangePosList(m_battle, randomRangeInfo.rangeId, new TargetSelectInfo(tarPos), GridDirType.None, posCollection);
            //                foreach (var pos in posCollection.GetPosList())
            //                {
            //                    if (targetEntity.CheckLocatable(pos))
            //                    {
            //                        posList.Add(pos);
            //                    }
            //                }
            //                if (posList.Count == 0)
            //                {
            //                    continue;
            //                }
            //                var index = m_battle.RandomInt(0, posList.Count - 1);
            //                var teleportPos = posList[index];
            //                var resultClip = CreateResultClipAndCheckImmune<BattleActionEffectTeleportEntityResultClip>(targetEntity, handleParams.castSkillContext.procedureContext.valueSet);
            //                resultClip.entityUid = targetEntity.uid;
            //                result.clipList.Add(resultClip);
            //                if (resultClip.resultType == BattleActionEffectResultType.Success)
            //                {
            //                    var prePos = targetEntity.GetLocation();
            //                    targetEntity.SetLocation(teleportPos);
            //                    resultClip.entityUid = targetEntity.uid;
            //                    resultClip.teleportPos = teleportPos;

            //                    BattleLogicEventEntityMove entityMoveEvent = m_battle.FetchObj<BattleLogicEventEntityMove>();
            //                    entityMoveEvent.entityUid = targetEntity.uid;
            //                    entityMoveEvent.prePos = prePos;
            //                    entityMoveEvent.movePos = teleportPos;
            //                    entityMoveEvent.isTeleport = true;
            //                    handleParams.castSkillContext.procedureContext.PushEvent(entityMoveEvent);
            //                }
            //                posCollection.Reset();
            //            }
            //            posCollection.Release();
            //            posList.Clear();
            //        }
            //    }
            //}
            m_battle.Release(targetEntityList);
        }
    }
}
