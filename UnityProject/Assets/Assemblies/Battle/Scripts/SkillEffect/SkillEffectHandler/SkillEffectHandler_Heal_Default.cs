using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_Heal_Default : SkillEffectHandler_Heal
    {
        public override void CollectEntity(SkillEffectInfo baseInfo, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> entityList)
        {
            var effectInfo = baseInfo as SkillEffectInfo_Heal_Default;
            CollectEntity(effectInfo.target, ariseType, valueSet, entityList);
        }

        public override void CollectGridPos(SkillEffectInfo baseInfo, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var effectInfo = baseInfo as SkillEffectInfo_Heal_Default;
            CollectGridPos(effectInfo.target, ariseType, valueSet, posCollection);
        }

        protected override void OnHandle(HandleParams handleParams, SkillEffectResult result)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_Heal_Default;
            var castSkillContext = handleParams.castSkillContext;
            var srcEntity = castSkillContext.srcEntity;
            var valueSet = castSkillContext.procedureContext.valueSet;
            var targetEntityList = m_battle.FetchList<IEntity>();
            CollectEntity(handleParams, targetEntityList);
            RemoveHpTypeNotValue(targetEntityList);
            HealTargetList(srcEntity, targetEntityList, effectInfo.heal, valueSet, result);
        }
    }
}
