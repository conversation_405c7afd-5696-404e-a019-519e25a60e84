//using System;
//using System.Collections;
//using System.Collections.Generic;
//using Phoenix.Core;
//using Phoenix.ConfigData;

//namespace Phoenix.Battle
//{
//    public class BattleActionEffectDetachBuffHandler : BattleActionEffectHandler
//    {
//        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
//        {
//            BattleActionEffectDetachBuffInfo effectInfo = handleParams.effectInfo as BattleActionEffectDetachBuffInfo;
//            var targetEntityList = m_battle.FetchObj<List<IEntity>>();
//            BattleUtility.CollectEntityListByBattleActionEffectFunc(effectInfo.targetInfo, handleParams.castSkillContext.procedureContext.valueSet, targetEntityList);
//            if (effectInfo.funcType == BattleActionEffectDetachBuffFuncType.Rid)
//            {
//                var info = effectInfo as BattleActionEffectDetachBuffRidInfo;
//                foreach (var targetEntity in targetEntityList)
//                {
//                    foreach (var buffRid in info.buffRidList)
//                    {
//                        Buff buff = targetEntity.GetBuffByRid(buffRid);
//                        if (buff != null)
//                        {
//                            var resultClip = m_battle.FetchObj<BattleActionEffectDetachBuffResultClip>();
//                            resultClip.detachResultList.Add(targetEntity.DetachBuff(buff, true));
//                            result.clipList.Add(resultClip);
//                        }
//                    }
//                }
//            }
//            else if (effectInfo.funcType == BattleActionEffectDetachBuffFuncType.GroupId)
//            {
//                var info = effectInfo as BattleActionEffectDetachBuffGroupIdInfo;
//                List<Buff> buffListForColelct = m_battle.FetchObj<List<Buff>>();
//                List<Buff> buffListForRandom = m_battle.FetchObj<List<Buff>>();
//                if(info.count != 0)
//                {
//                    foreach (var targetEntity in targetEntityList)
//                    {
//                        foreach (var groupId in info.groupIdList)
//                        {
//                            targetEntity.GetBuffListByGroup(groupId, buffListForColelct);
//                        }
//                        foreach (var buff in buffListForColelct)
//                        {
//                            if (!buff.GetBuffInfo().detachable)
//                            {
//                                continue;
//                            }
//                            if (buff.level == 1)
//                            {
//                                buffListForRandom.Add(buff);
//                            }
//                            else if (buff.level > 1)
//                            {
//                                for (int i = 0; i < buff.level; ++i)
//                                {
//                                    buffListForRandom.Add(buff);
//                                }
//                            }
//                        }
//                        if (info.count > 0 && buffListForRandom.Count > info.count)
//                        {
//                            if (m_battle.CheckRandomEnable())
//                            {
//                                for (int i = 0; i < info.count; ++i)
//                                {
//                                    if (buffListForRandom.Count <= 0)
//                                    {
//                                        break;
//                                    }
//                                    int index = m_battle.RandomInt(0, buffListForRandom.Count - 1);
//                                    var buff = buffListForRandom[index];
//                                    buffListForRandom.RemoveAt(index);
//                                    var resultClip = m_battle.FetchObj<BattleActionEffectDetachBuffResultClip>();
//                                    resultClip.detachResultList.Add(targetEntity.DetachBuff(buff, true));
//                                    result.clipList.Add(resultClip);
//                                }
//                            }
//                        }
//                        else
//                        {
//                            for (int i = 0; i < buffListForRandom.Count; ++i)
//                            {
//                                var buff = buffListForRandom[i];
//                                var resultClip = m_battle.FetchObj<BattleActionEffectDetachBuffResultClip>();
//                                resultClip.detachResultList.Add(targetEntity.DetachBuff(buff, true));
//                                result.clipList.Add(resultClip);
//                            }
//                        }
//                        buffListForColelct.Clear();
//                        buffListForRandom.Clear();
//                    }
//                }
//                m_battle.Release(buffListForColelct);
//                m_battle.Release(buffListForRandom);
//            }
//            m_battle.Release(targetEntityList);
//        }
//    }
//}
