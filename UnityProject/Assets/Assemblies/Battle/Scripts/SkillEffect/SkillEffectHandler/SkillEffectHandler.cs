using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public abstract class SkillEffectHandler : BattleObj
    {
        public SkillEffectFuncType effectType;

        public override void OnRelease()
        {
            effectType = default;
            base.OnRelease();
        }

        public void Handle(HandleParams param, int id, List<BattleActionEffectResult> effectResultList)
        {
            BattleActionEffectResult result = m_battle.FetchObj<BattleActionEffectResult>();
            result.id = id;
            effectResultList.Add(result);
            if (BattleArgumentUtility.CheckConditionListByAnd(param.effectInfo.conditionList, BattleActionEffectConditionAriseType.BattleActionEffectCheck, param.castSkillContext.procedureContext.valueSet))
            {
                OnHandle(param, result);
                result.success = true;
            }
            else
            {
                result.success = false;
            }
        }

        protected T CreateResultClipAndCheckImmune<T>(IEntity entity, BattleVagueParamValueSet valueSet) where T : BattleActionEffectResultClip, new()
        {
            var resultClip = m_battle.FetchObj<T>();
            if (CheckEntityImmuneEffect(entity, valueSet))
            {
                resultClip.resultType = BattleActionEffectResultType.Immune;
            }
            else
            {
                resultClip.resultType = BattleActionEffectResultType.Success;
            }
            return resultClip;
        }

        protected bool CheckEntityImmuneEffect(IEntity entity, BattleVagueParamValueSet valueSet)
        {
            var immuneStateEffectList = m_battle.FetchObj<List<BuffEffectOfStateApply>>();
            entity.CollectStateEffect(BuffEffectStateType.Immune_SkillEffect, immuneStateEffectList);
            bool result = false;
            foreach (var stateEffect in immuneStateEffectList)
            {
                var immuneEffectInfo = stateEffect.effectInfo as BuffEffectInfo_Immune_SkillEffect;
                if (immuneEffectInfo == null)
                {
                    continue;
                }
                var runningBuff = valueSet.runningBuff;
                valueSet.runningBuff = stateEffect.buff;
                bool isActive = valueSet == null || stateEffect.CheckActive(valueSet);
                valueSet.runningBuff = runningBuff;
                if (!isActive)
                {
                    continue;
                }
                if (immuneEffectInfo.effectType == effectType)
                {
                    result = true;
                    break;
                }
            }
            m_battle.Release(immuneStateEffectList);
            return result;
        }

        protected abstract void OnHandle(HandleParams handleParams, BattleActionEffectResult result);

        public struct HandleParams
        {
            public SkillEffectInfo effectInfo;
            public TargetSelectStepResult stepResult;
            public CastSkillContext castSkillContext;
        }
    }
}
