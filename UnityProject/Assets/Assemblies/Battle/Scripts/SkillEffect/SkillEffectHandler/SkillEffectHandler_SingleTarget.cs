using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public abstract class SkillEffectHandler_SingleTarget : SkillEffectHandler
    {
        protected void CollectTarget(HandleParams handleParams, IList<IEntity> entityList)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_SingleTarget;
            BattleArgumentUtility.CollectEntity(effectInfo.target, BattleActionEffectConditionAriseType.Skill, handleParams.castSkillContext.procedureContext.valueSet, entityList);
        }

        protected void CollectRange(HandleParams handleParams, BattleFieldSummaryForPosCollection posCollection)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_SingleTarget;
            BattleArgumentUtility.CollectEntityCollectRange(effectInfo.target, BattleActionEffectConditionAriseType.Skill, handleParams.castSkillContext.procedureContext.valueSet, posCollection);
        }
    }
}
