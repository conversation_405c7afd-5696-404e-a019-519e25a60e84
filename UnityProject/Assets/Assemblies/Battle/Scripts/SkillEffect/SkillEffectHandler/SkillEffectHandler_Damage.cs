using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public abstract class SkillEffectHandler_Damage : SkillEffectHandler
    {
        protected void RemoveNotAlive(IList<IEntity> entityList)
        {
            for (int i = entityList.Count - 1; i >= 0; i--)
            {
                if (!entityList[i].IsAlive())
                {
                    entityList.RemoveAt(i);
                }
            }
        }

        protected void HandleDamage(CastSkillContext castSkillContext, BattleDamageInfo damageInfo, SkillEffectResult result, IList<IEntity> targetEntityList)
        {
            var srcEntity = castSkillContext.srcEntity;
            for (int i = 0; i < targetEntityList.Count; ++i)
            {
                IEntity targetEntity = targetEntityList[i];
                HandleDamage(targetEntity, castSkillContext, damageInfo, result);
            }
        }

        protected void HandleDamage(IEntity targetEntity, CastSkillContext castSkillContext, BattleDamageInfo damageInfo, SkillEffectResult result)
        {
            var srcEntity = castSkillContext.srcEntity;
            var resultClip = battle.FetchObj<SkillEffectResultClip_Damage>();
            resultClip.resultType = SkillEffectResultType.Success;
            if (targetEntity != null && targetEntity.entityType == EntityType.Actor)
            {
                targetEntity.GetTeam().isAggressive = true;
                resultClip.entityUid = targetEntity.uid;
                resultClip.entityRid = targetEntity.rid;
                resultClip.entityUidAttacker = srcEntity.uid;
                resultClip.entityRidAttacker = srcEntity.rid;
                resultClip.damageInfo = damageInfo;
                castSkillContext.procedureContext.valueSet.targetDist = targetEntity.GetLocation().DistanceTo(srcEntity.GetLocation());
                castSkillContext.procedureContext.valueSet.duringDamageEntity = true;
                srcEntity.HandleAccumulateParam(BuffEffectAttributeAccumulateType.TargetDist, castSkillContext.procedureContext.valueSet.targetDist);
                srcEntity.RefreshBuffActiveStateSilence(BattleVagueParamConditionChangeEventId.DuringDamageEntity, castSkillContext.procedureContext.valueSet);

                var resultClipList = GetResultClipList(castSkillContext, targetEntity.uid);
                if (CheckEngageCombat(srcEntity, castSkillContext.source))
                {
                    int dodgeCount = 0;
                    if (targetEntity.TryGetDodge(out dodgeCount, out BuffEffectOfStateApply buffEffect, castSkillContext.procedureContext.valueSet))
                    {
                        if (dodgeCount > resultClipList.Count)
                        {
                            resultClip.resultType = SkillEffectResultType.Dodge;
                            var buffChangeResult = targetEntity.TickLifeTime(buffEffect.buff, BuffLifeTimeType.Trigger);
                            if (buffChangeResult != null)
                            {
                                resultClip.buffChangeResultList.Add(buffChangeResult);
                            }
                        }
                    }
                }
                if (resultClip.resultType == SkillEffectResultType.Success)
                {
                    var entityBoard = castSkillContext.procedureContext.valueSet.GetActionBoard_Entity(srcEntity.uid, true);
                    entityBoard.damage = true;
                    FixedValue damage = FixedValue.zero;
                    var hpType = targetEntity.GetHpType();
                    if (hpType == EntityHpType.HitCount)
                    {
                        damage = FixedValue.one;
                    }
                    else if (hpType == EntityHpType.Value)
                    {
                        FixedValue criticalRate = FixedValue.zero;
                        if (srcEntity != null)
                        {
                            criticalRate = srcEntity.GetAttributeValue(AttributeId.CriticalRate) - targetEntity.GetAttributeValue(AttributeId.CriticalRateAnti);
                        }
                        bool isCritical = m_battle.RandomRate(criticalRate);
                        if (isCritical)
                        {
                            entityBoard.criticalHit = true;
                        }
                        resultClip.srcEntityCopy = (srcEntity as Entity).Copy(m_battle);
                        resultClip.targetEntityCopy = (targetEntity as Entity).Copy(m_battle);
                        EntityElementId elementId = damageInfo.elementType;
                        if (elementId == EntityElementId.None)
                        {
                            elementId = srcEntity.GetElementId();
                        }
                        FixedValue elementDamageMult = FixedValue.one + battle.GetElementMultValue(elementId, targetEntity.GetElementId());
                        resultClip.isRegist = elementDamageMult <= 1;
                        damage = BattleValueCalculateUtility.CalcDamage(battle, srcEntity, targetEntity, damageInfo.argument, isCritical, damageInfo.damageType, castSkillContext.damageRate, elementDamageMult, castSkillContext.procedureContext.valueSet);
                        resultClip.isCritical = isCritical;
                    }
                    castSkillContext.procedureContext.valueSet.duringDamageEntity = false;
                    srcEntity.RefreshBuffActiveStateSilence(BattleVagueParamConditionChangeEventId.DuringDamageEntity, castSkillContext.procedureContext.valueSet);
                    FixedValue damageReflect = 0;// damage / 2;
                    FixedValue healthSteal = 0;// damage / 2;
                    FixedValue curHp = targetEntity.GetCurHp();
                    FixedValue curHpAttacker = srcEntity.GetCurHp();
                    resultClip.expectDamage = damage;
                    resultClip.expectDamageReflect = damageReflect;
                    resultClip.expectHealthSteal = healthSteal;
                    resultClip.preHp = curHp;
                    resultClip.preHpAttacker = curHpAttacker;
                    resultClip.maxHpAttacker = srcEntity.GetAttributeValue(AttributeId.HpMax);
                    resultClipList.Add(resultClip);
                    if (damage >= curHp)
                    {
                        resultClip.curHp = 0;
                        resultClip.isDead = true;
                        entityBoard.kill = true;
                        targetEntity.SetCurHp(0);
                        if (targetEntity.IsAlive())
                        {
                            targetEntity.SetDying();

                            BattleLogicEventEntityDead deadEvent = m_battle.FetchObj<BattleLogicEventEntityDead>();
                            deadEvent.deadEntityUid = targetEntity.uid;
                            deadEvent.killerEntityUid = srcEntity.uid;
                            castSkillContext.procedureContext.PushEvent(deadEvent);
                        }
                    }
                    else
                    {
                        targetEntity.AddCurHp(-damage);
                        resultClip.curHp = targetEntity.GetCurHp();
                    }
                    targetEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.HpRateChange, castSkillContext.procedureContext.valueSet, resultClip.targetBuffChangeActiveResultList);
                    resultClip.curHpAttacker = castSkillContext.srcEntity.GetCurHp();
                    if (damageReflect > 0 || healthSteal > 0)
                    {
                        if (damageReflect >= curHpAttacker + healthSteal)
                        {
                            resultClip.curHpAttacker = 0;
                            resultClip.isDeadAttacker = true;
                            srcEntity.SetCurHp(0);
                            if (srcEntity.IsAlive())
                            {
                                srcEntity.SetDying();

                                BattleLogicEventEntityDead deadEvent = m_battle.FetchObj<BattleLogicEventEntityDead>();
                                deadEvent.deadEntityUid = srcEntity.uid;
                                deadEvent.killerEntityUid = targetEntity.uid;
                                castSkillContext.procedureContext.PushEvent(deadEvent);
                            }
                        }
                        else
                        {
                            srcEntity.AddCurHp(-damageReflect + healthSteal);
                            resultClip.curHpAttacker = srcEntity.GetCurHp();
                        }
                        srcEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.HpRateChange, castSkillContext.procedureContext.valueSet, resultClip.srcBuffChangeActiveResultList);
                    }
                }
                else
                {
                    resultClip.curHp = targetEntity.GetCurHp();
                    resultClip.curHpAttacker = castSkillContext.srcEntity.GetCurHp();
                }
            }
            result.clipList.Add(resultClip);
        }

        protected List<SkillEffectResultClip_Damage> GetResultClipList(CastSkillContext castSkillContext, int entityUid)
        {
            List<SkillEffectResultClip_Damage> list = null;
            int count = castSkillContext.resultClipList.Count;
            for (int i = 0; i < count; ++i)
            {
                list = castSkillContext.resultClipList[i];
                if (list.Count > 0)
                {
                    var damageResultClip = list[0] as SkillEffectResultClip_Damage;
                    if (damageResultClip != null)
                    {
                        if (damageResultClip.entityUid == entityUid)
                        {
                            return list;
                        }
                    }
                }
            }
            list = new List<SkillEffectResultClip_Damage>();
            castSkillContext.resultClipList.Add(list);
            return list;
        }

        protected bool CheckEngageCombat(IEntity srcEntity, CastSkillSource source)
        {
            if (source.type != CastSkillSourceType.Skill)
            {
                return false;
            }
            var skill = srcEntity.GetSkillBySkillUid(source.sourceUid);
            return skill != null && skill.skillInfo.engageType == SkillEngageType.Combat;
        }

        protected void HandleDamageToBlockToRange(BattleFieldSummaryForPosCollection effectRangeCollection, BattleActionProcedureContext procedureContext, SkillEffectResult result)
        {
            foreach (var targetPos in effectRangeCollection.GetPosList())
            {
                HandleDamageToBlockToPos(targetPos, procedureContext, result);
            }
        }

        protected void HandleDamageToBlockToPos(GridPosition targetPos, BattleActionProcedureContext procedureContext, SkillEffectResult result)
        {
            var entityList = m_battle.GetEntityListByFieldSummary(targetPos);
            foreach (var entity in entityList)
            {
                if (entity.blockComponent == null)
                {
                    continue;
                }
                if (entity.blockComponent.CheckBreak())
                {
                    continue;
                }
                if (!TryGetBlockChangeResult(result, entity.uid, out EntityBlockHitPointChangeResult blockChangeResult))
                {
                    blockChangeResult = m_battle.FetchObj<EntityBlockHitPointChangeResult>();
                    blockChangeResult.entityUid = entity.uid;
                    result.blockHitPointChangeResultList.Add(blockChangeResult);
                }
                var blockChangeResultClip = m_battle.FetchObj<EntityBlockHitPointChangeResultClip>();
                var blockHitPoint = entity.blockComponent.GetBlockHitPoint(targetPos);
                entity.blockComponent.SetBlockHitPoint(targetPos, blockHitPoint - 1);
                blockChangeResultClip.pos = targetPos;
                blockChangeResultClip.updateHitPoint = entity.blockComponent.GetBlockHitPoint(targetPos);
                blockChangeResult.clipList.Add(blockChangeResultClip);
                if (entity.blockComponent.CheckBreak())
                {
                    var blockBreakEvent = m_battle.FetchObj<BattleLogicEventEntityBlockBreak>();
                    blockBreakEvent.entityUid = entity.uid;
                    procedureContext.PushEvent(blockBreakEvent);
                }
            }
        }

        protected bool TryGetBlockChangeResult(SkillEffectResult effectResult, int entityUid, out EntityBlockHitPointChangeResult blockChangeResult)
        {
            foreach (var bcResult in effectResult.blockHitPointChangeResultList)
            {
                if (bcResult.entityUid == entityUid)
                {
                    blockChangeResult = bcResult;
                    return true;
                }
            }
            blockChangeResult = null;
            return false;
        }
    }
}
