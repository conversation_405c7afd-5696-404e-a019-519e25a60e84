using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_Damage : SkillEffectHandler_SingleTarget
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            using var targetEntityList = m_battle.FetchList<IEntity>();
            using var effectRangeCollection = m_battle.CreateFieldSummaryForPosCollection();
            var castSkillContext = handleParams.castSkillContext;
            var procedureContext = castSkillContext.procedureContext;
            CollectTarget(handleParams, targetEntityList);
            CollectRange(handleParams, effectRangeCollection);
            HandleDamageToBlockToRange(effectRangeCollection, procedureContext, result);
            HandleDamage(handleParams, result, targetEntityList);
        }

        private void HandleDamage(HandleParams handleParams, BattleActionEffectResult result, IList<IEntity> targetEntityList)
        {
            var castSkillContext = handleParams.castSkillContext;
            var srcEntity = handleParams.castSkillContext.srcEntity;
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_Damage;
            for (int i = 0; i < targetEntityList.Count; ++i)
            {
                IEntity targetEntity = targetEntityList[i];
                BattleActionEffectDamageEntityResultClip resultClip = battle.FetchObj<BattleActionEffectDamageEntityResultClip>();
                resultClip.resultType = BattleActionEffectResultType.Success;
                if (targetEntity != null && targetEntity.entityType == EntityType.Actor)
                {
                    targetEntity.GetTeam().isAggressive = true;
                    resultClip.entityUid = targetEntity.uid;
                    resultClip.entityRid = targetEntity.rid;
                    resultClip.entityUidAttacker = srcEntity.uid;
                    resultClip.entityRidAttacker = srcEntity.rid;
                    resultClip.damageInfo = effectInfo;
                    handleParams.castSkillContext.procedureContext.valueSet.targetDist = targetEntity.GetLocation().DistanceTo(srcEntity.GetLocation());
                    handleParams.castSkillContext.procedureContext.valueSet.duringDamageEntity = true;
                    srcEntity.HandleAccumulateParam(BuffEffectAttributeAccumulateType.TargetDist, handleParams.castSkillContext.procedureContext.valueSet.targetDist);
                    srcEntity.RefreshBuffActiveStateSilence(BattleVagueParamConditionChangeEventId.DuringDamageEntity, handleParams.castSkillContext.procedureContext.valueSet);

                    var resultClipList = GetResultClipList(castSkillContext, targetEntity.uid);
                    if (CheckEngageCombat(srcEntity, castSkillContext.source))
                    {
                        int dodgeCount = 0;
                        if (targetEntity.TryGetDodge(out dodgeCount, handleParams.castSkillContext.procedureContext.valueSet))
                        {
                            if (dodgeCount > resultClipList.Count)
                            {
                                resultClip.resultType = BattleActionEffectResultType.Dodge;
                            }
                        }
                    }
                    if (resultClip.resultType == BattleActionEffectResultType.Success)
                    {
                        FixedValue damage = FixedValue.zero;
                        var hpType = targetEntity.GetHpType();
                        if (hpType == EntityHpType.HitCount)
                        {
                            damage = FixedValue.one;
                        }
                        else if (hpType == EntityHpType.Value)
                        {
                            FixedValue criticalRate = FixedValue.zero;
                            if (srcEntity != null)
                            {
                                criticalRate = srcEntity.GetAttributeValue(AttributeId.CriticalRate) - targetEntity.GetAttributeValue(AttributeId.CriticalRateAnti);
                            }
                            bool isCritical = m_battle.RandomRate(criticalRate);
                            resultClip.srcEntityCopy = (srcEntity as Entity).Copy(m_battle);
                            resultClip.targetEntityCopy = (targetEntity as Entity).Copy(m_battle);
                            damage = BattleValueCalculateUtility.CalcDamage(battle, srcEntity, targetEntity, effectInfo, isCritical, castSkillContext.damageRate, castSkillContext.procedureContext.valueSet);
                            resultClip.isCritical = isCritical;
                        }
                        handleParams.castSkillContext.procedureContext.valueSet.duringDamageEntity = false;
                        srcEntity.RefreshBuffActiveStateSilence(BattleVagueParamConditionChangeEventId.DuringDamageEntity, handleParams.castSkillContext.procedureContext.valueSet);
                        FixedValue damageReflect = 0;// damage / 2;
                        FixedValue healthSteal = 0;// damage / 2;
                        FixedValue curHp = targetEntity.GetCurHp();
                        FixedValue curHpAttacker = srcEntity.GetCurHp();
                        resultClip.expectDamage = damage;
                        resultClip.expectDamageReflect = damageReflect;
                        resultClip.expectHealthSteal = healthSteal;
                        resultClip.preHp = curHp;
                        resultClip.preHpAttacker = curHpAttacker;
                        resultClip.maxHpAttacker = srcEntity.GetAttributeValue(AttributeId.HpMax);
                        resultClipList.Add(resultClip);
                        if (damage >= curHp)
                        {
                            resultClip.curHp = 0;
                            resultClip.isDead = true;
                            targetEntity.SetCurHp(0);
                            if (targetEntity.IsAlive())
                            {
                                targetEntity.SetDying();

                                BattleLogicEventEntityDead deadEvent = m_battle.FetchObj<BattleLogicEventEntityDead>();
                                deadEvent.deadEntityUid = targetEntity.uid;
                                deadEvent.killerEntityUid = srcEntity.uid;
                                handleParams.castSkillContext.procedureContext.PushEvent(deadEvent);
                            }
                        }
                        else
                        {
                            targetEntity.AddCurHp(-damage);
                            resultClip.curHp = targetEntity.GetCurHp();
                        }
                        targetEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.HpRateChange, handleParams.castSkillContext.procedureContext.valueSet, resultClip.targetBuffChangeActiveResultList);
                        resultClip.curHpAttacker = handleParams.castSkillContext.srcEntity.GetCurHp();
                        if (damageReflect > 0 || healthSteal > 0)
                        {
                            if (damageReflect >= curHpAttacker + healthSteal)
                            {
                                resultClip.curHpAttacker = 0;
                                resultClip.isDeadAttacker = true;
                                srcEntity.SetCurHp(0);
                                if (srcEntity.IsAlive())
                                {
                                    srcEntity.SetDying();

                                    BattleLogicEventEntityDead deadEvent = m_battle.FetchObj<BattleLogicEventEntityDead>();
                                    deadEvent.deadEntityUid = srcEntity.uid;
                                    deadEvent.killerEntityUid = targetEntity.uid;
                                    handleParams.castSkillContext.procedureContext.PushEvent(deadEvent);
                                }
                            }
                            else
                            {
                                srcEntity.AddCurHp(-damageReflect + healthSteal);
                                resultClip.curHpAttacker = srcEntity.GetCurHp();
                            }
                            srcEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.HpRateChange, handleParams.castSkillContext.procedureContext.valueSet, resultClip.srcBuffChangeActiveResultList);
                        }
                    }
                }
                result.clipList.Add(resultClip);
            }
        }

        private List<BattleActionEffectDamageEntityResultClip> GetResultClipList(CastSkillContext castSkillContext, int entityUid)
        {
            List<BattleActionEffectDamageEntityResultClip> list = null;
            int count = castSkillContext.resultClipList.Count;
            for (int i = 0; i < count; ++i)
            {
                list = castSkillContext.resultClipList[i];
                if (list.Count > 0)
                {
                    var damageResultClip = list[0] as BattleActionEffectDamageEntityResultClip;
                    if (damageResultClip != null)
                    {
                        if (damageResultClip.entityUid == entityUid)
                        {
                            return list;
                        }
                    }
                }
            }
            list = new List<BattleActionEffectDamageEntityResultClip>();
            castSkillContext.resultClipList.Add(list);
            return list;
        }

        private bool CheckEngageCombat(IEntity srcEntity, CastSkillSource source)
        {
            if (source.type != CastSkillSourceType.Skill)
            {
                return false;
            }
            var skill = srcEntity.GetSkillBySkillUid(source.sourceUid);
            return skill != null && skill.skillInfo.engageType == SkillEngageType.Combat;
        }

        private void HandleDamageToBlockToRange(BattleFieldSummaryForPosCollection effectRangeCollection, BattleActionProcedureContext procedureContext, BattleActionEffectResult result)
        {
            foreach (var targetPos in effectRangeCollection.GetPosList())
            {
                HandleDamageToBlockToPos(targetPos, procedureContext, result);
            }
        }

        private void HandleDamageToBlockToPos(GridPosition targetPos, BattleActionProcedureContext procedureContext, BattleActionEffectResult result)
        {
            var entityList = m_battle.GetEntityListByFieldSummary(targetPos);
            foreach (var entity in entityList)
            {
                if (entity.blockComponent == null)
                {
                    continue;
                }
                if (entity.blockComponent.CheckBreak())
                {
                    continue;
                }
                if (!TryGetBlockChangeResult(result, entity.uid, out EntityBlockHitPointChangeResult blockChangeResult))
                {
                    blockChangeResult = m_battle.FetchObj<EntityBlockHitPointChangeResult>();
                    blockChangeResult.entityUid = entity.uid;
                    result.blockHitPointChangeResultList.Add(blockChangeResult);
                }
                var blockChangeResultClip = m_battle.FetchObj<EntityBlockHitPointChangeResultClip>();
                var blockHitPoint = entity.blockComponent.GetBlockHitPoint(targetPos);
                entity.blockComponent.SetBlockHitPoint(targetPos, blockHitPoint - 1);
                blockChangeResultClip.pos = targetPos;
                blockChangeResultClip.updateHitPoint = entity.blockComponent.GetBlockHitPoint(targetPos);
                blockChangeResult.clipList.Add(blockChangeResultClip);
                if (entity.blockComponent.CheckBreak())
                {
                    var blockBreakEvent = m_battle.FetchObj<BattleLogicEventEntityBlockBreak>();
                    blockBreakEvent.entityUid = entity.uid;
                    procedureContext.PushEvent(blockBreakEvent);
                }
            }
        }

        private bool TryGetBlockChangeResult(BattleActionEffectResult effectResult, int entityUid, out EntityBlockHitPointChangeResult blockChangeResult)
        {
            foreach (var bcResult in effectResult.blockHitPointChangeResultList)
            {
                if (bcResult.entityUid == entityUid)
                {
                    blockChangeResult = bcResult;
                    return true;
                }
            }
            blockChangeResult = null;
            return false;
        }
    }
}
