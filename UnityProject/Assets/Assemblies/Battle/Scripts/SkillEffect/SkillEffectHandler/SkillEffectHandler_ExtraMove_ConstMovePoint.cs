using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_ExtraMove_ConstMovePoint : SkillEffectHandler_ExtraMove
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_ExtraMove_ConstMovePoint;

            using var targetEntityList = m_battle.FetchList<IEntity>();
            CollectTarget(handleParams, targetEntityList);

            int targetEntityCount = targetEntityList.Count;
            for (int i = 0; i < targetEntityCount; ++i)
            {
                HandleExtraMove(targetEntityList[i], handleParams.castSkillContext.source, effectInfo.movePoint, result);
            }
        }
    }
}
