using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_ExtraAction : SkillEffectHandler_SingleTarget
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_ExtraAction;

            using var targetEntityList = m_battle.FetchList<IEntity>();
            CollectTarget(handleParams, targetEntityList);

            int targetEntityCount = targetEntityList.Count;
            for (int i = 0; i < targetEntityCount; ++i)
            {
                var targetEntity = targetEntityList[i];
                HandlerExtraAction(targetEntity, handleParams.castSkillContext.source, result);
            }
        }

        private void HandlerExtraAction()
        {
        }
        protected void HandlerExtraAction(IEntity targetEntity, CastSkillSource castSkillSource, BattleActionEffectResult result)
        {
            var resultClip = m_battle.FetchObj<BattleActionEffectEntityExtraActionResultClip>();
            resultClip.entityUid = targetEntity.uid;
            targetEntity.SetExtraActionChance(castSkillSource);
            resultClip.source = castSkillSource;
            result.clipList.Add(resultClip);
            m_battle.SetEntityMustAct(targetEntity.uid);
        }
    }
}
