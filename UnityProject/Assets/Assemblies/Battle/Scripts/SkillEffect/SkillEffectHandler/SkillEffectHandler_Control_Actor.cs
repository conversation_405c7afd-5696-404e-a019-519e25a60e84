using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_Control_Actor : SkillEffectHandler
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            //BattleActionEffectChangeTeamInfo effectInfo = handleParams.effectInfo as BattleActionEffectChangeTeamInfo;

            //CastSkillContext castSkillContext = handleParams.castSkillContext;
            //var srcEntity = handleParams.castSkillContext.srcEntity;
            //var targetEntityList = m_battle.FetchObj<List<IEntity>>();
            //BattleUtility.CollectEntityListByBattleActionEffectFunc(effectInfo.targetInfo, castSkillContext.procedureContext.valueSet, targetEntityList);
            //foreach (var targetEntity in targetEntityList)
            //{
            //    if (targetEntity.GetTeamUid() == srcEntity.GetTeamUid())
            //    {
            //        continue;
            //    }
            //    var resultClip = CreateResultClipAndCheckImmune<BattleActionEffectChangeTeamResultClip>(targetEntity, handleParams.castSkillContext.procedureContext.valueSet);
            //    resultClip.entityUid = targetEntity.uid;
            //    result.clipList.Add(resultClip);
            //    if (resultClip.resultType == BattleActionEffectResultType.Success)
            //    {
            //        var preTeam = targetEntity.GetTeam();
            //        var curTeam = srcEntity.GetTeam();
            //        curTeam.AddEntityFrom(targetEntity, preTeam, false);
            //        //targetEntity.SetRecoverTeamUid(preTeam.uid);
            //        targetEntity.SetActionChance(true);
            //        resultClip.preTeamUid = preTeam.uid;
            //        resultClip.curTeamUid = curTeam.uid;

            //        var targetPos = targetEntity.GetLocation();
            //        int occupySize = targetEntity.GetOccupySize();
            //        int occupyOffset = (occupySize - 1) / 2;
            //        for (int i = -occupyOffset; i <= occupyOffset; ++i)
            //        {
            //            for (int j = -occupyOffset; j <= occupyOffset; ++j)
            //            {
            //                m_battle.GetFieldSummaryOfOccupyEntity().SetDependSummaryDirty(targetPos + new GridPosition(i, j));
            //            }
            //        }
            //    }
            //}
            //m_battle.Release(targetEntityList);
        }
    }
}
