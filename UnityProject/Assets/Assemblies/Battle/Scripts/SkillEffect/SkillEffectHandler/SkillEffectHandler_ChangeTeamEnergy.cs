using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_ChangeTeamEnergy : SkillEffectHandler_TeamByCampRef
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_ChangeTeamEnergy;

            using var targetTeamList = m_battle.FetchList<BattleTeam>();
            CollectTeam(handleParams, targetTeamList);

            int targetTeamCount = targetTeamList.Count;
            for (int i = 0; i < targetTeamCount; ++i)
            {
                var targetTeam = targetTeamList[i];
                var resultClip = m_battle.FetchObj<BattleActionEffectChangeTeamEnergyResultClip>();
                targetTeam.AddEnergy(effectInfo.deltaEnergy);
                resultClip.teamUid = targetTeam.uid;
                resultClip.updateTeamEnergy = targetTeam.sharedEnergy;
                result.clipList.Add(resultClip);
            }
        }
    }
}
