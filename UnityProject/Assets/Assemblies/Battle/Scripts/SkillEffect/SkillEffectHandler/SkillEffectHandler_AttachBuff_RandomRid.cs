using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_AttachBuff_RandomRid : SkillEffectHandler_AttachBuff
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_AttachBuff_RandomRid;
            var castSkillContext = handleParams.castSkillContext;
            var srcEntity = castSkillContext.srcEntity;
            var valueSet = castSkillContext.procedureContext.valueSet;
            using var targetEntityList = m_battle.FetchList<IEntity>();
            CollectTarget(handleParams, targetEntityList);
            //AttachBuffListToTargetList(srcEntity, targetEntityList, effectInfo.itemList, valueSet, result);
            throw new ToDoException(nameof(SkillEffectHandler_AttachBuff_RandomRid), nameof(OnHandle), "AttachBuffListToTargetList");
        }
    }
}