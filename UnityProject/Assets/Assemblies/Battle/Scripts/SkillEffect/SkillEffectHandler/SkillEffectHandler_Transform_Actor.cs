using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class SkillEffectHandler_Transform_Actor : SkillEffectHandler_SingleTarget
    {
        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
        {
            var effectInfo = handleParams.effectInfo as SkillEffectInfo_Transform_Actor;

            var targetEntityList = m_battle.FetchObj<List<IEntity>>();
            CollectTarget(handleParams, targetEntityList);
            foreach (var targetEntity in targetEntityList)
            {
                var targetActor = targetEntity as Actor;
                var data = m_battle.infoGetter.GetEntityDataGetter(targetEntity.GetPlayerId(), EntityType.Actor, effectInfo.actorRid);
                if (data == null)
                {
                    continue;
                }
                var transformActor = BattleFactory.CreateEntity(m_battle, EntityType.Actor) as Actor;
                transformActor.Init(targetActor.uid, data);
                transformActor.SetLocation(targetActor.GetLocation(), targetActor.GetLocation());
                transformActor.SetDir(targetActor.GetDir());
                transformActor.SetTeam(targetActor.GetTeamUid(), false);
                transformActor.SetActionChance(targetActor.HasActionChance());
                targetActor.transformActor = transformActor;
                targetActor.transformLifetime = effectInfo.lifeTime;
                targetActor.lockTransformLifeTime = true;
                var resultClip = m_battle.FetchObj<BattleActionEffectTransformEntityResultClip>();
                resultClip.entityUid = targetEntity.uid;
                resultClip.entity = transformActor.Copy(m_battle);
                result.clipList.Add(resultClip);
            }
            m_battle.Release(targetEntityList);
        }
    }
}
