using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public abstract class BuffEffect : EntityObj, IBattleSnapshot, IDebugTagNodeContainer, ICheckSame<BuffEffect>
    {
        protected Buff m_buff;
        protected BuffEffectInfo m_effectInfo;
        protected int m_effectIndex;
        protected bool m_isActive;

        private List<BattleVagueParamConditionChangeEventId> m_conditionChangeEventIdList = new List<BattleVagueParamConditionChangeEventId>();
        private DebugTagNode m_debugTagNode;

        public bool isActive
        {
            get { return m_isActive; }
        }

        public Buff buff
        {
            get { return m_buff; }
        }

        public BuffEffectInfo effectInfo
        {
            get { return m_effectInfo; }
        }

        public int effectIndex
        {
            get { return m_effectIndex; }
        }

        protected virtual bool initActive
        {
            get { return true; }
        }

        public override void OnRelease()
        {
            m_effectInfo = null;
            m_buff = null;
            m_effectIndex = default;
            m_conditionChangeEventIdList.Clear();
            m_debugTagNode.Release();
            m_debugTagNode = null;
            base.OnRelease();
        }

        public void Init(Buff buff, BuffEffectInfo effectInfo, int effectIndex)
        {
            m_buff = buff;
            m_effectInfo = effectInfo;
            m_effectIndex = effectIndex;
            InitEffectCondition();
        }

        public void Attach()
        {
            OnAttach();
            SetActive(initActive);
        }

        public void Detach()
        {
            SetActive(false);
            OnDetach();
        }

        internal void LevelChange()
        {
            OnLevelChanged();
        }

        public BuffChangeActiveResult TryUpdateActiveState(BattleVagueParamConditionChangeEventId conditionChangeEventId, BattleVagueParamValueSet valueSet)
        {
            if (!m_conditionChangeEventIdList.Contains(conditionChangeEventId))
            {
                return null;
            }
            return TryUpdateActiveState(valueSet);
        }

        public BuffChangeActiveResult TryUpdateActiveState(BattleVagueParamValueSet valueSet)
        {
            bool active = CheckActive(valueSet);
            if (active != m_isActive)
            {
                SetActive(active);
                BuffChangeActiveResult result = m_battle.FetchObj<BuffChangeActiveResult>();
                result.entityUid = m_entity.uid;
                result.buffUid = m_buff.buffUid;
                result.effectIndex = m_effectIndex;
                result.isActive = active;
                return result;
            }
            return null;
        }

        public void TryUpdateActiveStateSilence(BattleVagueParamConditionChangeEventId conditionChangeEventId, BattleVagueParamValueSet valueSet)
        {
            if (!m_conditionChangeEventIdList.Contains(conditionChangeEventId))
            {
                return;
            }
            TryUpdateActiveStateSilence(valueSet);
        }

        public void TryUpdateActiveStateSilence(BattleVagueParamValueSet valueSet)
        {
            bool active = CheckActive(valueSet);
            if (active != m_isActive)
            {
                SetActive(active);
            }
        }

        public void SetActive(bool isActive)
        {
            if (m_isActive != isActive)
            {
                m_isActive = isActive;
                OnActiveStateChanged();
            }
        }

        public bool CheckActive(BattleVagueParamValueSet valueSet)
        {
            if (valueSet == null)
            {
                return false;
            }
            return BattleArgumentUtility.CheckConditionListByAnd(m_effectInfo.conditionList, BattleActionEffectConditionAriseType.Buff, valueSet);
        }

        protected void InitEffectCondition()
        {
            for (int i = 0; i < m_effectInfo.conditionList.Count; ++i)
            {
                var conditionInfo = m_effectInfo.conditionList[i];
                //if(conditionInfo.funcType == BattleActionEffectFuncOfConditionType.DuringCombat)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.SkillEngageStateChange);
                //}
                //else if (conditionInfo.funcType == BattleActionEffectFuncOfConditionType.DuringDamageEntity)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.DuringDamageEntity);
                //}
                //else if (conditionInfo.funcType == BattleActionEffectFuncOfConditionType.ActiveAttack)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.SkillEngageStateChange);
                //}
                //else if (conditionInfo.funcType == BattleActionEffectFuncOfConditionType.TurnIndex)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.TurnIndexChange);
                //}
                //else if (conditionInfo.funcType == BattleActionEffectFuncOfConditionType.HpRate)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.HpRateChange);
                //}
                //else if (conditionInfo.funcType == BattleActionEffectFuncOfConditionType.EntityCountInRange)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.EntityCountInRangeChange);
                //}
                //else if(conditionInfo.funcType == BattleActionEffectFuncOfConditionType.StepIndex || conditionInfo.funcType == BattleActionEffectFuncOfConditionType.LastStep)
                //{
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.StepIndexStart);
                //    m_conditionChangeEventIdList.Add(BattleVagueParamConditionChangeEventId.StepIndexEnd);
                //}
            }
        }

        protected abstract void OnAttach();
        protected abstract void OnDetach();
        protected virtual void OnLevelChanged() { }
        protected virtual void OnActiveStateChanged() { }
        protected virtual void OnHandleActiveStateChange(BattleVagueParamValueSet valueSet) { }


        public DebugTagNode GetDebugTagNode()
        {
            if (m_debugTagNode == null)
            {
                m_debugTagNode = DebugTagNode.Create(m_battle, m_buff.GetDebugTagNode());
                m_debugTagNode.tag.Set("[{0}:{1}]", GetType().Name, m_effectIndex);
            }
            return m_debugTagNode;
        }

        public virtual bool CheckSame(BuffEffect baseOne)
        {
            bool result = true;
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_effectIndex, baseOne.m_effectIndex, "m_effectIndex");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_isActive, baseOne.m_isActive, "m_isActive");
            return result;
        }

        public virtual void CopyFrom(BuffEffect baseOne)
        {
            m_isActive = baseOne.m_isActive;
            m_conditionChangeEventIdList.Clear();
            m_conditionChangeEventIdList.AddRange(baseOne.m_conditionChangeEventIdList);
        }

        public void BuildSnapshot(ByteBufferBuilder builder)
        {
            //builder.WriteInt(m_effectUid);
        }

        public void LoadSnapshot(ByteBufferLoader loader)
        {
            //m_effectUid = loader.ReadInt();
        }
    }
}
