using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BuffEffectOfSkillTrigger : BuffEffect
    {
        private int m_coolTime;
        private int m_triggerCount;

        public BuffEffectInfo_SkillTrigger specificInfo
        {
            get { return m_effectInfo as BuffEffectInfo_SkillTrigger; }
        }

        public BuffTriggerInfo trigger
        {
            get { return specificInfo.trigger; }
        }

        public BuffTriggerType triggerType
        {
            get { return trigger.triggerType; }
        }

        public override void OnRelease()
        {
            m_coolTime = default;
            m_triggerCount = default;
            base.OnRelease();
        }

        public void TryTrigger(BattleActionProcedureContext context, IEntity triggerEntity)
        {
            var runningBuff = context.valueSet.runningBuff;
            context.valueSet.runningBuff = m_buff;
            if (CheckTrigger(context, triggerEntity))
            {
                Trigger(context);
            }
            context.valueSet.runningBuff = runningBuff;
        }

        public bool CheckTrigger(BattleActionProcedureContext context, IEntity triggerEntity)
        {
            if (!BuffTriggerUtility.CheckTrigger(trigger, context.valueSet, triggerEntity))
            {
                return false;
            }
            return CheckTrigger(context);
        }

        private void TriggerLimit(BattleTriggerLimitInfo info)
        {
            if (info == null)
            {
                return;
            }
            switch (info.limitType)
            {
                case BattleTriggerLimitType.Cd_Step:
                    m_coolTime = (info as BattleTriggerLimitInfo_Cd_Step).cd;
                    break;
                case BattleTriggerLimitType.Cd_Turn:
                    m_coolTime = (info as BattleTriggerLimitInfo_Cd_Turn).cd;
                    break;
                case BattleTriggerLimitType.Count:
                    m_triggerCount++;
                    break;
                case BattleTriggerLimitType.Count_PerTurn:
                    m_triggerCount++;
                    break;
                default:
                    throw new ToDoException(nameof(BuffEffectOfSkillTrigger), nameof(TriggerLimit), info.limitType.ToString());
            }
        }

        private bool CheckLimit(BattleTriggerLimitInfo info)
        {
            if (info == null)
            {
                return true;
            }
            switch (info.limitType)
            {
                case BattleTriggerLimitType.Cd_Step:
                    return m_coolTime == 0;
                case BattleTriggerLimitType.Cd_Turn:
                    return m_coolTime == 0;
                case BattleTriggerLimitType.Count:
                    return m_triggerCount < (info as BattleTriggerLimitInfo_Count).count;
                case BattleTriggerLimitType.Count_PerTurn:
                    return m_triggerCount < (info as BattleTriggerLimitInfo_Count_PerTurn).count;
                default:
                    throw new ToDoException(nameof(BuffEffectOfSkillTrigger), nameof(CheckLimit), info.limitType.ToString());
            }
        }

        public bool CheckTrigger(BattleActionProcedureContext context)
        {
            if (!CheckLimit(specificInfo.limit))
            {
                return false;
            }
            return CheckCondition(context.valueSet);
        }

        public void TryTrigger(BattleActionProcedureContext context)
        {
            if (CheckTrigger(context))
            {
                Trigger(context);
            }
        }

        public void ResetTriggerCountByTurn()
        {
            if (specificInfo.limit is BattleTriggerLimitInfo_Count_PerTurn)
            {
                m_triggerCount = 0;
            }
        }

        public int GetCoolTime()
        {
            return m_coolTime;
        }

        public void UnlockCd()
        {
        }

        public void SetCoolTime(int coolTime)
        {
            m_coolTime = System.Math.Max(0, coolTime);
        }

        public void TickCoolTimeByTurn(List<BuffCoolTimeUpdateResult> resultList)
        {
            if (specificInfo.limit is BattleTriggerLimitInfo_Cd_Turn limitInfo_Cd_Turn)
            {
                TickCoolTime(resultList);
            }
        }

        public void TickCoolTimeByStep(List<BuffCoolTimeUpdateResult> resultList)
        {
            if (specificInfo.limit is BattleTriggerLimitInfo_Cd_Step limitInfo_Cd_Step)
            {
                TickCoolTime(resultList);
            }
        }

        public void TickCoolTime(List<BuffCoolTimeUpdateResult> resultList)
        {
            if (m_coolTime > 0)
            {
                m_coolTime--;
                var result = m_battle.FetchObj<BuffCoolTimeUpdateResult>();
                result.entityUid = m_entity.uid;
                result.buffUid = m_buff.buffUid;
                result.effectIndex = effectIndex;
                result.coolTime = m_coolTime;
                resultList.Add(result);
            }
        }

        private bool CheckCondition(BattleVagueParamValueSet valueSet)
        {
            var runningBuff = valueSet.runningBuff;
            valueSet.runningBuff = m_buff;
            bool result = BattleArgumentUtility.CheckConditionListByAnd(trigger.conditionList, BattleActionEffectConditionAriseType.BuffTrigger, valueSet);
            valueSet.runningBuff = runningBuff;
            return result;
        }

        private void Trigger(BattleActionProcedureContext context)
        {
            if (m_entity.IsDead())
            {
                return;
            }
            var stepResult = context.valueSet.stepResult;
            CastSkillContext castSkillContext = m_battle.FetchObj<CastSkillContext>();
            castSkillContext.srcEntity = m_entity;
            castSkillContext.source = new CastSkillSource(m_buff);
            castSkillContext.damageRate = m_buff.level * 100;
            castSkillContext.procedureContext = context;
            castSkillContext.stepResult = m_battle.FetchObj<TargetSelectStepResult>();
            castSkillContext.stepResult.rootWrap = TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(m_entity), GridDirType.None, null);
            castSkillContext.stepResult.stepWrapList.Add(TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(m_entity), GridDirType.None, castSkillContext.stepResult.rootWrap));
            if (stepResult != null)
            {
                foreach (var stepWrap in stepResult.stepWrapList)
                {
                    castSkillContext.stepResult.stepWrapList.Add(TargetSelectInfoWrap.Create(m_battle, stepWrap.selectInfo, stepWrap.dir, castSkillContext.stepResult.rootWrap));
                }
            }

            var runningBuff = context.valueSet.runningBuff;
            context.valueSet.runningBuff = m_buff;
            context.valueSet.stepResult = castSkillContext.stepResult;
            BuffTriggerResult castResult = SkillLogicUtility.CastByBuff(m_entity, m_buff, m_effectIndex, castSkillContext);
            context.valueSet.stepResult = stepResult;
            context.valueSet.runningBuff = runningBuff;
            castSkillContext.Release();
            TriggerLimit(specificInfo.limit);
            //m_coolTime = specificInfo.coolTime;
            castResult.coolTime = m_coolTime;
            BuffChangeResult buffChangeResult = m_entity.TickLifeTime(m_buff, BuffLifeTimeType.Trigger);
            if (buffChangeResult != null)
            {
                context.procedureResult.AddResult(buffChangeResult);
            }
            context.procedureResult.AddResult(castResult);
        }

        private bool NeedCheckSkill()
        {
            switch (trigger.triggerType)
            {
                case BuffTriggerType.BeforeEngageBegin:
                case BuffTriggerType.AfterEngageBegin:
                case BuffTriggerType.BeforeEngageEnd:
                case BuffTriggerType.AfterEngageEnd:
                    return true;
            }
            return false;
        }

        private bool NeedCheckTriggerEntity()
        {
            switch (trigger.triggerType)
            {
                case BuffTriggerType.AfterLocateEnd:
                case BuffTriggerType.BeforeEngageBegin:
                case BuffTriggerType.AfterEngageBegin:
                case BuffTriggerType.BeforeCastSkill:
                case BuffTriggerType.AfterCastSkill:
                case BuffTriggerType.BeforeEngageEnd:
                case BuffTriggerType.AfterEngageEnd:
                case BuffTriggerType.BeforeActionEnd:
                case BuffTriggerType.AfterActionEnd:
                case BuffTriggerType.AfterEntityDead:
                case BuffTriggerType.AfterEntityMove:
                    return true;
            }
            return false;
        }

        private bool CheckTriggerEntity(IEntity triggerEntity)
        {
            //if(trigger.triggerType == BuffTriggerType.AfterEntityDead
            //    && trigger.deadFilterFuncType != BattleActionEffectCheckEntityFuncType.Origin
            //    && !m_entity.IsAlive())
            //{
            //    return false;
            //}
            //switch (trigger.deadFilterFuncType)
            //{
            //    case BattleActionEffectCheckEntityFuncType.Origin:
            //        return entity.uid == triggerEntity.uid;
            //    case BattleActionEffectCheckEntityFuncType.Friend:
            //        return entity.GetBattleCampId() == triggerEntity.GetBattleCampId();
            //    case BattleActionEffectCheckEntityFuncType.Enemy:
            //        return entity.GetBattleCampId() != triggerEntity.GetBattleCampId();

            //}
            return false;
        }

        public override void CopyFrom(BuffEffect baseOne)
        {
            base.CopyFrom(baseOne);
            var curOne = baseOne as BuffEffectOfSkillTrigger;
            m_coolTime = curOne.m_coolTime;
            m_triggerCount = curOne.m_triggerCount;
        }

        protected override void OnAttach()
        {
        }

        protected override void OnDetach()
        {
        }
    }
}
