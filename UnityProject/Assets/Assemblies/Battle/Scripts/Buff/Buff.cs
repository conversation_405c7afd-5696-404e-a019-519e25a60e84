using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class Buff : EntityObj, IBattleSnapshot, IDebugTagNodeContainer, ICheckSame<Buff>
    {
        public static readonly List<Buff> emptyList = new List<Buff>();

        public int srcEntityUid;
        public int buffUid;
        public int buffRid;
        public int leftLiftTime;
        public int count;
        public int level;
        public bool lockLifeTime;

        private BuffInfo m_buffInfo;
        private List<BuffEffect> m_effectList = new List<BuffEffect>();
        private Dictionary<int, FixedValue> m_accumulateParamMap = new Dictionary<int, FixedValue>();

        private DebugTagNode m_debugTagNode;

        public override void OnRelease()
        {
            srcEntityUid = default;
            buffUid = default;
            buffRid = default;
            leftLiftTime = default;
            count = default;
            level = default;
            lockLifeTime = default;
            m_buffInfo = default;
            m_accumulateParamMap.Clear();
            m_effectList.ReleaseAll();
            m_debugTagNode.Release();
            m_debugTagNode = null;
            base.OnRelease();
        }

        public BuffInfo GetBuffInfo()
        {
            if(m_buffInfo == null)
            {
                m_buffInfo = m_battle.infoGetter.GetBuffInfo(buffRid);
            }
            return m_buffInfo;
        }

        public BuffEffect GetBuffEffect(int index)
        {
            return m_effectList[index];
        }

        public void HandleAttachBuff()
        {
            BuffInfo buffInfo = GetBuffInfo();
            for (int i = 0; i < buffInfo.effectList.Count; ++i)
            {
                BuffEffectInfo effectInfo = buffInfo.effectList[i];
                BuffEffect effect = BattleFactory.CreateBuffEffect(m_entity, effectInfo.effectType);
                effect.Init(this, effectInfo, i);
                effect.Attach();
                m_effectList.Add(effect);
            }
        }

        public void HandleDetachBuff()
        {
            for (int i = 0; i < m_effectList.Count; ++i)
            {
                BuffEffect effect = m_effectList[i];
                effect.Detach();
            }
            m_effectList.ReleaseAll();
        }

        public void HandleLevelChange()
        {
            for (int i = 0; i < m_effectList.Count; ++i)
            {
                BuffEffect effect = m_effectList[i];
                effect.LevelChange();
            }
        }

        public void HandleAccumulateParam(BuffEffectAttributeAccumulateType accumulateType, FixedValue param)
        {
            if (!m_accumulateParamMap.ContainsKey((int)accumulateType))
            {
                m_accumulateParamMap[(int)accumulateType] = param;
            }
            else
            {
                m_accumulateParamMap[(int)accumulateType] += param;
            }
        }

        public void ResetAccumulateParam(BuffEffectAttributeAccumulateType accumulateType)
        {
            if (m_accumulateParamMap.ContainsKey((int)accumulateType))
            {
                m_accumulateParamMap[(int)accumulateType] = 0;
            }
        }

        public void RegisterAccumulateParam(BuffEffectAttributeAccumulateType accumulateType)
        {
            m_accumulateParamMap[(int)accumulateType] = 0;
        }

        public FixedValue GetAccumulateParam(ConfigData.BuffEffectAttributeAccumulateType accumulateType)
        {
            FixedValue value;
            m_accumulateParamMap.TryGetValue((int)accumulateType, out value);
            return value;
        }

        public void TryUpdateActiveState(BattleVagueParamConditionChangeEventId conditionChangeEventId, BattleVagueParamValueSet valueSet, List<BuffChangeActiveResult> resultList)
        {
            var runningBuff = valueSet.runningBuff;
            valueSet.runningBuff = this;
            for (int i = 0; i < m_effectList.Count; ++i)
            {
                BuffEffect effect = m_effectList[i];
                var activeChangeResult = effect.TryUpdateActiveState(conditionChangeEventId, valueSet);
                if(activeChangeResult != null)
                {
                    resultList.Add(activeChangeResult);
                }
            }
            valueSet.runningBuff = runningBuff;
        }

        public void TryUpdateActiveState(BattleVagueParamValueSet valueSet, List<BuffChangeActiveResult> resultList)
        {
            var runningBuff = valueSet.runningBuff;
            valueSet.runningBuff = this;
            for (int i = 0; i < m_effectList.Count; ++i)
            {
                BuffEffect effect = m_effectList[i];
                var activeChangeResult = effect.TryUpdateActiveState(valueSet);
                if (activeChangeResult != null)
                {
                    resultList.Add(activeChangeResult);
                }
            }
            valueSet.runningBuff = runningBuff;
        }

        public void TryUpdateActiveStateSilence(BattleVagueParamConditionChangeEventId conditionChangeEventId, BattleVagueParamValueSet valueSet)
        {
            var runningBuff = valueSet.runningBuff;
            valueSet.runningBuff = this;
            for (int i = 0; i < m_effectList.Count; ++i)
            {
                BuffEffect effect = m_effectList[i];
                effect.TryUpdateActiveStateSilence(conditionChangeEventId, valueSet);
            }
            valueSet.runningBuff = runningBuff;
        }

        public void CollectSkillTriggerEffect(BuffTriggerType triggerType, IList<BuffEffectOfSkillTrigger> list)
        {
            int length = m_effectList.Count;
            for (int i = 0; i < length; ++i)
            {
                var effect = m_effectList[i] as BuffEffectOfSkillTrigger;
                if (effect == null)
                {
                    continue;
                }
                if (effect.triggerType == triggerType)
                {
                    list.Add(effect);
                }
            }
        }

        public void CollectSkillTriggerEffect(List<BuffEffectOfSkillTrigger> list)
        {
            int length = m_effectList.Count;
            for (int i = 0; i < length; ++i)
            {
                var effect = m_effectList[i] as BuffEffectOfSkillTrigger;
                if (effect == null)
                {
                    continue;
                }
                list.Add(effect);
            }
        }

        public void CollectAuraEffect(List<BuffEffectOfAuraApply> list)
        {
            int length = m_effectList.Count;
            for (int i = 0; i < length; ++i)
            {
                var effect = m_effectList[i] as BuffEffectOfAuraApply;
                if (effect == null)
                {
                    continue;
                }
                list.Add(effect);
            }
        }

        public void CollectStateEffect(BuffEffectStateType stateType, List<BuffEffectOfStateApply> list)
        {
            int length = m_effectList.Count;
            for (int i = 0; i < length; ++i)
            {
                var effect = m_effectList[i] as BuffEffectOfStateApply;
                if (effect == null)
                {
                    continue;
                }
                if (effect.stateType == stateType)
                {
                    list.Add(effect);
                }
            }
        }

        public void UnlockAllTriggerCd()
        {
            int length = m_effectList.Count;
            for (int i = 0; i < length; ++i)
            {
                var effect = m_effectList[i] as BuffEffectOfSkillTrigger;
                if (effect == null)
                {
                    continue;
                }
                effect.UnlockCd();
            }
        }

        public DebugTagNode GetDebugTagNode()
        {
            if (m_debugTagNode == null)
            {
                m_debugTagNode = DebugTagNode.Create(m_battle, m_entity.buffComponent.GetDebugTagNode());
                m_debugTagNode.tag.Set("[{0}:{1},{2}]", GetType().Name, buffUid, buffRid);
            }
            return m_debugTagNode;
        }

        public virtual bool CheckSame(Buff baseOne)
        {
            bool result = true;
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, srcEntityUid, baseOne.srcEntityUid, "srcEntityUid");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, buffUid, baseOne.buffUid, "buffUid");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, buffRid, baseOne.buffRid, "buffRid");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, leftLiftTime, baseOne.leftLiftTime, "leftLiftTime");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, count, baseOne.count, "count");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, level, baseOne.level, "level");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, lockLifeTime, baseOne.lockLifeTime, "lockLifeTime");
            result &= BattleUtility.CheckSameClassListOrLogout(m_battle, this, m_effectList, baseOne.m_effectList, "m_effectList");
            return result;
        }

        public Buff Copy(IFetchable fetchable)
        {
            Buff buff = fetchable.FetchObj<Buff>();
            buff.CopyFrom(this);
            return buff;
        }

        public virtual void CopyFrom(Buff baseOne)
        {
            buffUid = baseOne.buffUid;
            buffRid = baseOne.buffRid;
            srcEntityUid = baseOne.srcEntityUid;
            leftLiftTime = baseOne.leftLiftTime;
            level = baseOne.level;
            count = baseOne.count;
            lockLifeTime = baseOne.lockLifeTime;
            m_buffInfo = baseOne.m_buffInfo;
            m_effectList.ReleaseAll();
            foreach (var effect in baseOne.m_effectList)
            {
                BuffEffect newEffect = BattleFactory.CreateBuffEffect(m_entity, effect.effectInfo.effectType);
                newEffect.Init(this, effect.effectInfo, effect.effectIndex);
                newEffect.Attach();
                newEffect.CopyFrom(effect);
                m_effectList.Add(newEffect);
            }
        }

        public void BuildSnapshot(ByteBufferBuilder builder)
        {
            builder.WriteInt(srcEntityUid);
            builder.WriteInt(buffUid);
            builder.WriteInt(buffRid);
            builder.WriteByte(leftLiftTime);
            builder.WriteByte(count);
            builder.WriteBool(lockLifeTime);
        }

        public void LoadSnapshot(ByteBufferLoader loader)
        {
            srcEntityUid = loader.ReadInt();
            buffUid = loader.ReadInt();
            buffRid = loader.ReadInt();
            leftLiftTime = loader.ReadByte();
            count = loader.ReadByte();
            lockLifeTime = loader.ReadBool();
        }
    }
}
