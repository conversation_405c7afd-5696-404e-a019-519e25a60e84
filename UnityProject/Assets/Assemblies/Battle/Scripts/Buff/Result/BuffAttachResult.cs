using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BuffAttachResult : BuffChangeResult
    {
        public int buffUid;
        public int buffRid;
        public int level;
        public int lifeTime;
        public int passiveUid;

        public override BuffChangeType changeType
        {
            get { return BuffChangeType.Attach; }
        }

        public override void OnRelease()
        {
            buffUid = default;
            buffRid = default;
            level = default;
            lifeTime = default;
            passiveUid = default;
            base.OnRelease();
        }
    }
}
