using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BuffDetachResult : BuffChangeResult
    {
        public int buffRid;
        public int buffUid;
        public int level;
        public bool remove;

        public override BuffChangeType changeType
        {
            get { return BuffChangeType.Detach; }
        }

        public override void OnRelease()
        {
            buffUid = default;
            buffRid = default;
            level = default;
            remove = false;
            base.OnRelease();
        }
    }
}
