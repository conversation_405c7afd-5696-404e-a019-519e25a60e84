using System.Collections.Generic;

namespace Phoenix.Battle
{
    public partial class ChangeBattleFormationFrameCommandHandler : FrameCommandHandler
    {
        public override BattleErrorCode Check(FrameCommand baseCmd, int playerSlotId, bool checkPlayerControlable)
        {
            var cmd = baseCmd as ChangeBattleFormationFrameCommand;
            var teamUid = cmd.teamUid;
            var formationId = cmd.formationId;
            var team = m_battle.GetTeamByUid(teamUid);

            if (checkPlayerControlable)
            {
                var errorCode = CheckTeamControllable(playerSlotId, teamUid);
                if (errorCode != BattleErrorCode.Ok)
                {
                    return errorCode;
                }
            }

            var player = m_battle.GetPlayerBySlotId(playerSlotId);
            if (player == null)
            {
                return BattleErrorCode.InvalidPlayerSlotId;
            }
            if (team == null)
            {
                return BattleErrorCode.InvalidTeamUid;
            }
            if (team.controlledPlayer != player)
            {
                return BattleErrorCode.CannotControlOtherPlayer;
            }
            foreach (var entityUid in team.entityUidList)
            {
                var entity = m_battle.GetEntityByUid(entityUid);
                var formationGetter = entity.dataGetter as IBattleFormationGetter;
                if (formationGetter.GetBattleFormationIdList().Contains(formationId))
                {
                    return BattleErrorCode.Ok;
                }
            }
            return BattleErrorCode.InvalidFormationId;
        }

        public override void Execute(FrameCommand baseCmd)
        {
            var cmd = baseCmd as ChangeBattleFormationFrameCommand;
            var teamUid = cmd.teamUid;
            var formationId = cmd.formationId;
            var team = m_battle.GetTeamByUid(teamUid);
            team.formationId = formationId;
            foreach (var entityUid in team.entityUidList)
            {
                var entity = m_battle.GetEntityByUid(entityUid);
                var formationGetter = entity.dataGetter as IBattleFormationGetter;
                if (formationGetter.GetBattleFormationIdList().Contains(formationId))
                {
                    team.formationEntityUid = entityUid;
                }
            }
            m_battle.eventListener?.BattleFormationChanged(team.uid, team.formationId, team.formationEntityUid);
        }
    }
}
