using System;
using System.Collections.Generic;
using System.Xml;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public partial class EntityCastSkillFrameCommandHandler : EntityActionFrameCommandHandler
    {
        public override bool needWaitPerformance
        {
            get { return true; }
        }

        public override BattleErrorCode Check(FrameCommand baseCmd, int playerSlotId, bool checkPlayerControlable)
        {
            var cmd = baseCmd as EntityCastSkillFrameCommand;
            var entityUid = cmd.entityUid;
            var skillUid = cmd.skillUid;
            var movePos = new GridPosition(cmd.movePosX, cmd.movePosY);
            var paramList = cmd.paramList;

            BattleErrorCode errorCode = BattleErrorCode.Ok;
            if (checkPlayerControlable)
            {
                errorCode = CheckEntityControllable(playerSlotId, entityUid);
                if (errorCode != BattleErrorCode.Ok)
                {
                    return errorCode;
                }
            }

            IEntity entity = m_battle.GetEntityByUid(entityUid);
            if (entity == null)
            {
                return BattleErrorCode.InvalidEntityUid;
            }
            if (entity.GetTeam() != m_battle.GetTeamByIndex(m_battle.GetCurTeamIndex()))
            {
                return BattleErrorCode.TeamNotInTurn;
            }
            if (!entity.HasActionChance())
            {
                return BattleErrorCode.EntityHasNoActionChance;
            }
            if (!CheckCanMove(entity, movePos))
            {
                return BattleErrorCode.OutOfMoveRange;
            }
            TargetSelectParamContainer paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
            paramContainer.Init(paramList);
            errorCode = SkillLogicUtility.CanCastSkillForcastMove(entity, movePos, skillUid, paramContainer);
            paramContainer.Release();
            return errorCode;
        }

        public override void Execute(FrameCommand baseCmd)
        {
            var cmd = baseCmd as EntityCastSkillFrameCommand;
            var entityUid = cmd.entityUid;
            var skillUid = cmd.skillUid;
            var movePos = new GridPosition(cmd.movePosX, cmd.movePosY);
            var paramList = cmd.paramList;

            IEntity originEntity = m_battle.GetEntityByUid(entityUid);
            Skill skill = originEntity.GetSkillBySkillUid(skillUid);
            GridPosition prePos = originEntity.GetLocation();
            int moveDist = BattleUtility.AnalyzeMoveDist(prePos, movePos, originEntity.GetMovePathRule());
            originEntity.GetTeam().AddEnergy(skill.skillInfo.energyGain - skill.skillInfo.energyCost);
            //originEntity.SetCurHp(originEntity.GetCurHp() * (1 - skill.skillInfo.hpCost));

            EntityCastSkillProcedureResult procedureResult = m_battle.FetchObj<EntityCastSkillProcedureResult>();
            procedureResult.entityUid = entityUid;
            procedureResult.entityRid = originEntity.rid;
            procedureResult.skillUid = skill.uid;
            procedureResult.skillRid = skill.rid;
            procedureResult.teamUid = originEntity.GetTeamUid();
            procedureResult.updateEnergy = originEntity.GetTeam().sharedEnergy;
            procedureResult.updateCurHp = originEntity.GetCurHp();
            procedureResult.movePos = movePos;

            EntityCastSkillProcedureContext procedureContext = m_battle.FetchObj<EntityCastSkillProcedureContext>();
            procedureContext.Init();
            procedureContext.procedureResult = procedureResult;
            procedureContext.srcEntity = originEntity;
            procedureContext.prePos = originEntity.GetLocation();
            procedureContext.movePos = movePos;
            procedureContext.moveDist = moveDist;

            procedureContext.valueSet.procedureCauserEntity = originEntity;
            procedureContext.valueSet.moveDist = moveDist;
            procedureContext.valueSet.procedureResult = procedureResult;
            procedureContext.valueSet.engageSkill = skill;
            procedureContext.valueSet.engageSkillInfo = skill.skillInfo;
            originEntity.HandleAccumulateParam(BuffEffectAttributeAccumulateType.MoveDistTemporary, moveDist);
            procedureContext.valueSet.triggerStepStart = true;
            originEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.StepIndexStart, procedureContext);

            m_battle.ResetEntityMustAct(originEntity.uid);
            BattleUtility.SetOtherSummonRefMustAct(m_battle, originEntity.uid, originEntity.uid);
            if (originEntity.HasExtraMoveChance())
            {
                originEntity.ResetExtraMoveChance();
                procedureResult.resetExtraMove = true;
            }
            else if (originEntity.HasExtraActionChance())
            {
                originEntity.ResetExtraActionChance();
                procedureResult.resetExtraAction = true;
            }

            BattleUtility.TryCheckAndExitTerrain(m_battle, originEntity, prePos, procedureContext);
            BattleUtility.TryCheckAndExitTerrainBuff(m_battle, originEntity, prePos, procedureContext);
            originEntity.ChangeLocation(movePos, procedureContext);
            BattleUtility.TryCheckAndEnterTerrain(m_battle, originEntity, movePos, procedureContext);
            BattleUtility.TryCheckAndEnterTerrainBuff(m_battle, originEntity, movePos, procedureContext);
            BattleUtility.TryCheckAndTriggerTerrain(m_battle, originEntity, movePos, TerrainTriggerMomentType.BeforeEngage, procedureContext);
            BattleUtility.TryCheckAndTriggerTerrainBuff(m_battle, originEntity, movePos, TerrainTriggerMomentType.BeforeEngage, procedureContext);
            BattleLogicEventEntityMove entityMoveEvent = m_battle.FetchObj<BattleLogicEventEntityMove>();
            entityMoveEvent.entityUid = originEntity.uid;
            entityMoveEvent.moveDist = moveDist;
            entityMoveEvent.prePos = prePos;
            entityMoveEvent.movePos = movePos;
            procedureContext.PushEvent(entityMoveEvent);
            BattleUtility.UpdateAllAura(m_battle, procedureContext);
            procedureContext.ExecuteEvent();

            originEntity = m_battle.GetEntityByUid(entityUid);
            if (originEntity != null)
            {
                //战前触发需要获取目标，所以要放在前面
                TargetSelectStepResult stepResult = GetSelectStepResult(originEntity, skill, paramList);
                procedureContext.stepResult = stepResult;
                procedureContext.valueSet.stepResult = stepResult;
                List<int> buffUidList = new List<int>();
                foreach (var buffRid in skill.skillInfo.buffAttachBeforeList)
                {
                    var buffAttachResult = originEntity.AttachBuff(buffRid, 1, true, originEntity, procedureContext.valueSet);
                    buffUidList.Add(buffAttachResult.buffUid);
                    procedureResult.AddResult(buffAttachResult);
                }
                m_battle.TryBuffTrigger(BuffTriggerType.BeforeEngageBegin, procedureContext, originEntity);
                procedureContext.ExecuteEvent();
                originEntity = m_battle.GetEntityByUid(entityUid);
                //护卫位移放到这里
                bool triggerGuard = false;
                IEntity assistGuardEntity = null;
                GridPosition assistGuardOriginalPos = GridPosition.invalid;
                IEntity originalTargetEntity = null;
                int assistGuardBuffRid = 0;
                if (skill.engageType == SkillEngageType.Combat)
                {
                    GridPosition targetPos = TargetSelectUtility.GetSelectPos(m_battle, stepResult, TargetSelectStepPosObtainType.Pos_1);
                    IEntity targetEntity = m_battle.GetFirstEntityByPos(targetPos, EntityType.Actor);
                    if (targetEntity != null)
                    {
                        IEntity guardEntity = GetAssistGuarder(targetEntity, procedureContext, out assistGuardBuffRid);
                        if (guardEntity != null)
                        {
                            triggerGuard = true;
                            originalTargetEntity = targetEntity;
                            assistGuardEntity = guardEntity;
                            assistGuardOriginalPos = assistGuardEntity.GetLocation();
                            assistGuardEntity.SetLocation(targetPos);
                            targetEntity.AddUnselectableFlag(EntityIsolatedType.AssistGuard);
                        }
                    }
                }
                originEntity = m_battle.GetEntityByUid(entityUid);
                if (originEntity != null && !originEntity.BanAction())
                {
                    m_battle.TryBuffTrigger(BuffTriggerType.AfterEngageBegin, procedureContext, originEntity);
                    procedureContext.ExecuteEvent();
                    CastSkillContext castSkillContext = m_battle.FetchObj<CastSkillContext>();
                    castSkillContext.srcEntity = originEntity;
                    castSkillContext.source = new CastSkillSource(skill);
                    castSkillContext.procedureContext = procedureContext;
                    castSkillContext.stepResult = procedureContext.stepResult;

                    GridPosition targetPos = TargetSelectUtility.GetSelectPos(m_battle, stepResult, TargetSelectStepPosObtainType.Pos_1);
                    GridDirType targetDir = GridDirType.None;

                    procedureResult.targetPos = targetPos;

                    if (skill.engageType == SkillEngageType.Combat)
                    {
                        var entityBoard = procedureContext.valueSet.GetActionBoard_Entity(originEntity.uid, true);
                        entityBoard.combat = true;
                        IEntity targetEntity = m_battle.GetFirstEntityByPos(targetPos, EntityType.Actor);
                        if (targetEntity != null)
                        {
                            entityBoard = procedureContext.valueSet.GetActionBoard_Entity(targetEntity.uid, true);
                            entityBoard.combat = true;
                            SkillEngageResult skillEngageResult = m_battle.FetchObj<SkillEngageResult>();
                            skillEngageResult.skillUid = skill.uid;
                            skillEngageResult.skillRid = skill.rid;
                            skillEngageResult.originEntityUid = entityUid;
                            procedureContext.procedureResult.AddResult(skillEngageResult);

                            if (triggerGuard)
                            {
                                skillEngageResult.originalEntityUid = originalTargetEntity.uid;
                                skillEngageResult.assistGuardBuffRid = assistGuardBuffRid;
                                skillEngageResult.assistGuardOriginalPos = assistGuardOriginalPos;
                            }
                            skillEngageResult.targetEntityUid = targetEntity.uid;
                            procedureResult.targetEntityUid = targetEntity.uid;

                            procedureContext.valueSet.duringCombat = true;
                            procedureContext.valueSet.duringEngage = true;
                            procedureContext.valueSet.combatEntityList.Add(originEntity);
                            procedureContext.valueSet.combatEntityList.Add(targetEntity);
                            originEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.SkillEngageStateChange, procedureContext);
                            targetEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.SkillEngageStateChange, procedureContext);

                            castSkillContext.combatTargetEntityUid = targetEntity.uid;
                            procedureContext.valueSet.combatTargetEnttiy = targetEntity;
                            originEntity.CastSkill(skill.uid, castSkillContext, skillEngageResult);
                            procedureContext.valueSet.combatTargetEnttiy = null;
                            castSkillContext.Release();
                            procedureContext.stepResult = null;
                            procedureContext.valueSet.stepResult = null;
                            bool isNormalAttack = originEntity.GetNormalAttack() == skill;
                            if (isNormalAttack)
                            {
                                entityBoard = procedureContext.valueSet.GetActionBoard_Entity(originEntity.uid, true);
                                entityBoard.normalAttack = true;
                            }
                            //连击
                            FixedValue rate;
                            if (originEntity.IsAlive() && !originEntity.BanAction() && originEntity.TryGetTriggerContinuousAttack(out rate, procedureContext.valueSet))
                            {
                                if (targetEntity.IsAlive())
                                {
                                    Skill continuousSkill = originEntity.skillComponent.GetContinuousAttack();
                                    if (continuousSkill != null && continuousSkill.CanCastSkill() == BattleErrorCode.Ok)
                                    {
                                        castSkillContext = m_battle.FetchObj<CastSkillContext>();
                                        castSkillContext.srcEntity = originEntity;
                                        castSkillContext.source = new CastSkillSource(continuousSkill);
                                        castSkillContext.damageRate = rate + originEntity.GetAttributeValue(AttributeId.DoubleHitAttackDamageRate);
                                        castSkillContext.combatTargetEntityUid = targetEntity.uid;
                                        castSkillContext.procedureContext = procedureContext;
                                        using var paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
                                        paramContainer.PushGridPosition(targetPos);
                                        castSkillContext.stepResult = TargetSelectUtility.HandleSelect(m_battle, continuousSkill.skillInfo.selectStep, originEntity, originEntity.GetLocation(), continuousSkill.GetExtraSelectRange(), paramContainer);
                                        castSkillContext.stepResult.rootWrap = TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(originEntity), GridDirType.None, null);
                                        castSkillContext.stepResult.stepWrapList.Add(TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(targetEntity), GridDirType.None, castSkillContext.stepResult.rootWrap));
                                        procedureContext.valueSet.stepResult = castSkillContext.stepResult;
                                        procedureContext.valueSet.combatTargetEnttiy = targetEntity;
                                        originEntity.CastSkill(continuousSkill.uid, castSkillContext, skillEngageResult);
                                        procedureContext.valueSet.combatTargetEnttiy = null;
                                        castSkillContext.Release();
                                        procedureContext.stepResult = null;
                                        procedureContext.valueSet.stepResult = null;
                                    }
                                }
                            }
                            //反击
                            if (originEntity.IsAlive() && targetEntity.IsAlive() && !targetEntity.BanAction())
                            {
                                Skill fightBackSkill = targetEntity.GetCounterAttack();
                                if (fightBackSkill != null && fightBackSkill.CanCastSkill() == BattleErrorCode.Ok)
                                {
                                    var selectStepInfo = fightBackSkill.skillInfo.selectStep;
                                    using var fieldSummary = m_battle.CreateFieldSummaryForPosCollection();
                                    var originPos = originEntity.GetLocation();
                                    var extraRange = fightBackSkill.GetExtraSelectRange();
                                    using var paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
                                    paramContainer.PushGridPosition(originPos);
                                    var selectResult = TargetSelectUtility.HandleSelect(m_battle, selectStepInfo, targetEntity, targetEntity.GetLocation(), extraRange, paramContainer);
                                    if (selectResult != null)
                                    {
                                        if (selectResult.errorCode == BattleErrorCode.Ok)
                                        {
                                            castSkillContext = m_battle.FetchObj<CastSkillContext>();
                                            castSkillContext.srcEntity = targetEntity;
                                            castSkillContext.source = new CastSkillSource(fightBackSkill);
                                            castSkillContext.damageRate = 100 + targetEntity.GetAttributeValue(AttributeId.FightbackAttackDamageRate);
                                            castSkillContext.combatTargetEntityUid = originEntity.uid;
                                            castSkillContext.procedureContext = procedureContext;
                                            castSkillContext.stepResult = selectResult;
                                            castSkillContext.stepResult.stepInfo = selectStepInfo;
                                            procedureContext.valueSet.stepResult = castSkillContext.stepResult;
                                            procedureContext.valueSet.combatTargetEnttiy = originEntity;
                                            targetEntity.CastSkill(fightBackSkill.uid, castSkillContext, skillEngageResult);
                                            procedureContext.valueSet.combatTargetEnttiy = null;
                                            castSkillContext.Release();
                                            procedureContext.stepResult = null;
                                            procedureContext.valueSet.stepResult = null;
                                        }
                                        else
                                        {
                                            selectResult.Release();
                                        }
                                    }
                                }
                            }
                            //if (originEntity.IsAlive() && !originEntity.BanAction() && originEntity.TryGetTriggerAdditionalAttack(out rate, procedureContext.valueSet))
                            //{
                            //    if (targetEntity.IsAlive())
                            //    {
                            //        Skill addtionalSkill = originEntity.skillComponent.GetAdditionalAttack();
                            //        if (addtionalSkill != null && addtionalSkill.CanCastSkill() == BattleErrorCode.Ok)
                            //        {
                            //            castSkillContext = m_battle.FetchObj<CastSkillContext>();
                            //            castSkillContext.srcEntity = originEntity;
                            //castSkillContext.ariseType = BattleActionEffectConditionAriseType.Skill;
                            //            castSkillContext.source = new CastSkillSource(addtionalSkill);
                            //            castSkillContext.damageRate = rate + originEntity.GetAttributeValue(AttributeId.ChaseHitAttackDamageRate);
                            //            castSkillContext.combatTargetEntityUid = targetEntity.uid;
                            //            castSkillContext.procedureContext = procedureContext;
                            //            castSkillContext.stepResult = m_battle.FetchObj<TargetSelectStepResult>();
                            //            castSkillContext.stepResult.rootWrap = TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(originEntity), GridDirType.None, null);
                            //            castSkillContext.stepResult.stepWrapList.Add(TargetSelectInfoWrap.Create(m_battle, new TargetSelectInfo(targetEntity), GridDirType.None, castSkillContext.stepResult.rootWrap));
                            //            procedureContext.valueSet.stepResult = castSkillContext.stepResult;
                            //            procedureContext.valueSet.combatTargetEnttiy = targetEntity;
                            //            originEntity.CastSkill(addtionalSkill.uid, castSkillContext, skillEngageResult);
                            //            procedureContext.valueSet.combatTargetEnttiy = null;
                            //            castSkillContext.Release();
                            //            procedureContext.stepResult = null;
                            //            procedureContext.valueSet.stepResult = null;
                            //        }
                            //    }
                            //}
                            procedureContext.valueSet.duringCombat = false;
                            procedureContext.valueSet.duringEngage = false;
                            originEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.SkillEngageStateChange, procedureContext);
                            targetEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.SkillEngageStateChange, procedureContext);
                        }
                        m_battle.TryBuffTrigger(BuffTriggerType.AfterCombat, procedureContext, originEntity);
                        m_battle.TryBuffTrigger(BuffTriggerType.AfterCombat, procedureContext, targetEntity);
                    }
                    else
                    {
                        SkillEngageResult skillEngageResult = m_battle.FetchObj<SkillEngageResult>();
                        skillEngageResult.skillUid = skill.uid;
                        skillEngageResult.skillRid = skill.rid;
                        skillEngageResult.originEntityUid = entityUid;
                        procedureContext.procedureResult.AddResult(skillEngageResult);

                        procedureResult.targetPos = targetPos;
                        procedureResult.targetDir = targetDir;
                        procedureContext.valueSet.duringEngage = true;
                        originEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.SkillEngageStateChange, procedureContext);
                        originEntity.CastSkill(skill.uid, castSkillContext, skillEngageResult);
                        castSkillContext.Release();
                        procedureContext.stepResult = null;
                        procedureContext.valueSet.stepResult = null;
                        procedureContext.valueSet.duringEngage = false;
                        originEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.SkillEngageStateChange, procedureContext);
                    }
                    procedureContext.ExecuteEvent();
                    BattleUtility.UpdateAllAura(m_battle, procedureContext);
                    procedureContext.ExecuteEvent();
                }

                m_battle.TryBuffTrigger(BuffTriggerType.BeforeEngageEnd, procedureContext, originEntity);
                procedureContext.ExecuteEvent();

                if (triggerGuard)
                {
                    assistGuardEntity.SetLocation(assistGuardOriginalPos);
                    originalTargetEntity.RemoveUnselectableFlag(EntityIsolatedType.AssistGuard);
                }

                m_battle.TryBuffTrigger(BuffTriggerType.AfterEngageEnd, procedureContext, originEntity);
                m_battle.TryBuffTrigger(BuffTriggerType.FriendAttackAssist, procedureContext, originEntity);
                procedureContext.ExecuteEvent();

                originEntity = m_battle.GetEntityByUid(entityUid);
                if(originEntity != null)
                {
                    BattleUtility.TryCheckAndTriggerTerrain(m_battle, originEntity, movePos, TerrainTriggerMomentType.AfterEngage, procedureContext);
                    BattleUtility.TryCheckAndTriggerTerrainBuff(m_battle, originEntity, movePos, TerrainTriggerMomentType.AfterEngage, procedureContext);
                }
                bool handleExtarMove = false;
                if (originEntity != null && originEntity.IsAlive())
                {
                    if (!originEntity.HasExtraMoveChance())
                    {
                        ActionEnd(procedureContext, entityUid);
                    }
                    else
                    {
                        handleExtarMove = true;
                    }
                }
                if (originEntity != null)
                {
                    for (int i = 0; i < buffUidList.Count; ++i)
                    {
                        var buffAttachResult = originEntity.DetachBuff(buffUidList[i], false);
                        procedureResult.AddResult(buffAttachResult);
                    }
                    buffUidList.Clear();
                    originEntity.ResetAccumulateParam(BuffEffectAttributeAccumulateType.MoveDistTemporary);

                    if (!handleExtarMove)
                    {
                        m_battle.TryBuffTrigger(BuffTriggerType.BeforeActionEnd, procedureContext, originEntity);
                        procedureContext.ExecuteEvent();

                        m_battle.TryBuffTrigger(BuffTriggerType.AfterActionEnd, procedureContext, originEntity);
                        m_battle.TryBuffTrigger(BuffTriggerType.FirstAct, procedureContext, originEntity);
                        procedureContext.ExecuteEvent();

                        procedureContext.valueSet.triggerStepStart = false;
                        originEntity.RefreshBuffActiveState(BattleVagueParamConditionChangeEventId.StepIndexEnd, procedureContext);
                    }

                    TryTriggerTreasureBox(entityUid, procedureContext);
                }
            }

            BattlePerformanceEntityCastSkill performance = m_battle.FetchObj<BattlePerformanceEntityCastSkill>();
            performance.entityUid = entityUid;
            performance.procedureResult = procedureContext.procedureResult as EntityCastSkillProcedureResult;
            m_battle.SetPerformanceStart(performance);
            procedureContext.Release();
            m_battle.ClearWaitInputCommandTypeList();
        }

        private TargetSelectStepResult GetSelectStepResult(IEntity entity, Skill skill, List<short> paramList)
        {
            int extraRange = SkillLogicUtility.GetSkillExtraStepRange(entity, skill.uid);
            TargetSelectParamContainer paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
            paramContainer.Init(paramList);
            TargetSelectStepResult stepResult = TargetSelectUtility.HandleSelect(m_battle, skill.skillInfo.selectStep, entity, entity.GetLocation(), extraRange, paramContainer);
            paramContainer.Release();
            return stepResult;
        }

        private IEntity GetAssistGuarder(IEntity originEntity, BattleActionProcedureContext context, out int buffRid)
        {
            Entity assistGuarder = null;
            buffRid = 0;
            foreach (Entity e in m_battle.GetEntityList())
            {
                if (originEntity == e)
                {
                    continue;
                }
                TargetSelectRangeId rangeId;
                TargetSelectTargetFilterType filterFuncType;
                if (e.TryGetTopBuffAssistGuardState(out buffRid, out rangeId, out filterFuncType, context.valueSet))
                {
                    var posCollection = m_battle.CreateFieldSummaryForPosCollection();
                    TargetSelectUtility.AppendRangePosList(m_battle, rangeId, new TargetSelectInfo(e), GridDirType.None, posCollection);
                    foreach (var guardGridPos in posCollection.GetPosList())
                    {
                        if (!originEntity.IsLocatedOn(guardGridPos))
                        {
                            continue;
                        }
                        if (TargetSelectUtility.CheckFilter(m_battle, filterFuncType, new TargetSelectFilterContext(e), guardGridPos))
                        {
                            assistGuarder = e;
                            break;
                        }
                    }
                    m_battle.Release(posCollection);
                    if (assistGuarder != null)
                    {
                        break;
                    }
                }
            }
            return assistGuarder;
        } 
    }
}
