using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class BattleInitData
    {
        public int battleRid;
        public int randomSeed;
        public bool needExpand;
        public List<PlayerInitData> playerInitDataList = new List<PlayerInitData>();
        public List<BattleDisposedActorInitData> disposedActorInitDataList = new List<BattleDisposedActorInitData>();
        public List<BattleDisposedActorPosInitData> disposedActorPosInitDataList = new List<BattleDisposedActorPosInitData>();
        public List<int> gainedTreasureBoxIdList = new List<int>();
        public List<int> completedAchievementIdList = new List<int>();
        public int groundStartX;
        public int groundStartY;
        public int groundEndX;
        public int groundEndY;
        public string startTime;
    }
}