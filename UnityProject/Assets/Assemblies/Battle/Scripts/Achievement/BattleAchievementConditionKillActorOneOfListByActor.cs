using System;
using System.Collections.Generic;
using System.Linq;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleAchievementConditionKillActorOneOfListByActor : BattleAchievementCondition, IBattleReportEventListener<BattleReportEventEntityDead>
    {
        public int actorUid;
        public List<int> killActorUidList = new List<int>();

        private bool m_killed;

        public override void OnRelease()
        {
            actorUid = default;
            killActorUidList.Clear();
            m_killed = false;
            base.OnRelease();
        }

        protected override void OnInit()
        {
            m_battle.RegieterReportEventListener(BattleReportEventType.EntityDead, this);
        }

        protected override void OnUnInit()
        {
            m_battle.UnRegieterReportEventListener(BattleReportEventType.EntityDead, this);
        }

        public void ExecuteEvent(BattleReportEventEntityDead evt)
        {
            if (m_killed)
            {
                return;
            }
            if (actorUid == evt.killerEntityUid && killActorUidList.Contains(evt.deadEntityUid))
            {
                m_killed = true;
                SetFlag(true);
            }
        }
    }
}
