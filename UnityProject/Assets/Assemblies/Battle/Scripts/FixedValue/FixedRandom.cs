using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;

public class FixedRandom
{
    private const int MBIG = int.MaxValue;

    private const int MSEED = 161803398;

    private const int MZ = 0;

    public int inext;

    public int inextp;

    public int[] SeedArray = new int[56];

    public void InitBySeed(int Seed)
    {
        Clear();
        int num = MSEED - Math.Abs(Seed);
        SeedArray[55] = num;
        int num2 = 1;
        for (int i = 1; i < 55; i++)
        {
            int num3 = 21 * i % 55;
            SeedArray[num3] = num2;
            num2 = num - num2;
            if (num2 < 0)
            {
                num2 += MBIG;
            }

            num = SeedArray[num3];
        }

        for (int j = 1; j < 5; j++)
        {
            for (int k = 1; k < 56; k++)
            {
                SeedArray[k] -= SeedArray[1 + (k + 30) % 55];
                if (SeedArray[k] < 0)
                {
                    SeedArray[k] += MBIG;
                }
            }
        }

        inext = 0;
        inextp = 21;
    }

    public void Clear()
    {
        inext = default;
        inextp = default;
        for (int i = 0; i < SeedArray.Length; ++i)
        {
            SeedArray[i] = default;
        }
    }

    public FixedValue Sample()
    {
        return (FixedValue)InternalSample() / MBIG;
    }

    private int InternalSample()
    {
        int num = inext;
        int num2 = inextp;
        if (++num >= 56)
        {
            num = 1;
        }

        if (++num2 >= 56)
        {
            num2 = 1;
        }

        int num3 = SeedArray[num] - SeedArray[num2];
        if (num3 < 0)
        {
            num3 += MBIG;
        }

        SeedArray[num] = num3;
        inext = num;
        inextp = num2;
        return num3;
    }

    public virtual int Next()
    {
        return InternalSample();
    }

    private FixedValue GetSampleForLargeRange()
    {
        int num = InternalSample();
        if ((InternalSample() % 2 == 0) ? true : false)
        {
            num = -num;
        }

        FixedValue num2 = num;
        num2 += MBIG - 1;
        return num2 / (FixedValue)((long)MBIG * 2 - 1);
    }

    public int Next(int minValue, int maxValue)
    {
        if (minValue > maxValue)
        {
            throw new ArgumentOutOfRangeException("minValue", "minValue is larger");
        }

        long num = (long)maxValue - minValue;
        if (num <= MBIG)
        {
            return (int)(Sample() * (FixedValue)num) + minValue;
        }

        return (int)((long)(GetSampleForLargeRange() * (FixedValue)num) + minValue);
    }

    public virtual int Next(int maxValue)
    {
        if (maxValue < 0)
        {
            throw new ArgumentOutOfRangeException("maxValue", "maxValue is smaller than 0");
        }

        return (int)(Sample() * (FixedValue)maxValue);
    }

    public virtual FixedValue NextFixedValue()
    {
        return Sample();
    }

    public virtual void NextBytes(byte[] buffer)
    {
        if (buffer == null)
        {
            throw new ArgumentNullException("buffer");
        }

        for (int i = 0; i < buffer.Length; i++)
        {
            buffer[i] = (byte)(InternalSample() % 256);
        }
    }

    public void CopyFrom(FixedRandom random)
    {
        inext = random.inext;
        inextp = random.inextp;
        Array.Copy(random.SeedArray, SeedArray, SeedArray.Length);
    }
}