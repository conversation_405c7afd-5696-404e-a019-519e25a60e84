using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static class BattleSortUtility
    {
        public static void SortEntity_Distance(IBattle battle, List<IEntity> entityList, GridPosition pos, bool isIncrease)
        {
            var sortHelper = battle.FetchObj<BattleSortHelper>();
            sortHelper.AddComparer_EntityDistanceToPos(pos, !isIncrease);
            sortHelper.AddComparer_EntityUid();
            sortHelper.Sort(entityList);
            sortHelper.Release();
        }
    }
}
