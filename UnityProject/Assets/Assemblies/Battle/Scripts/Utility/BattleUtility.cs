using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.SqlClient;
//using Codice.Client.Commands.Merge;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleUtility
    {
        public static void BuildRandomBuffer(this FixedRandom random, ByteBufferBuilder builder)
        {
            builder.WriteInt(random.inext);
            builder.WriteInt(random.inextp);
            for (int i = 0; i < random.SeedArray.Length; ++i)
            {
                builder.WriteInt(random.SeedArray[i]);
            }
        }

        public static void LoadRandomBuffer(this FixedRandom random, ByteBufferLoader loader)
        {
            random.inext = loader.ReadInt();
            random.inextp = loader.ReadInt();
            for (int i = 0; i < random.SeedArray.Length; ++i)
            {
                random.SeedArray[i] = loader.ReadInt();
            }
        }

        public static bool CheckStrongElement(this IBattle battle, EntityElementId srcId, EntityElementId destId)
        {
            return GetElementMultValue(battle, srcId, destId) > 0;
        }

        public static bool CheckWeakElement(this IBattle battle, EntityElementId srcId, EntityElementId destId)
        {
            return GetElementMultValue(battle, srcId, destId) < 0;
        }

        public static FixedValue GetElementMultValue(this IBattle battle, EntityElementId srcId, EntityElementId destId)
        {
            FixedValue multValue = FixedValue.zero;
            var elementInfo = battle.infoGetter.GetEntityElementInfo((int)srcId);
            if (elementInfo != null)
            {
                elementInfo.damageMultMap.TryGetValue((int)destId, out multValue);
            }
            return multValue;
        }

        public static GridPosition GetOffsetByDir(GridDirType dir)
        {
            switch (dir)
            {
                case GridDirType.Up:
                    return new GridPosition(0, 1);
                case GridDirType.Down:
                    return new GridPosition(0, -1);
                case GridDirType.Left:
                    return new GridPosition(-1, 0);
                case GridDirType.Right:
                    return new GridPosition(1, 0);
            }
            return GridPosition.zero;
        }

        public static GridDirType GetAntiClockwiseByDir(GridDirType dir)
        {
            switch (dir)
            {
                case GridDirType.Up:
                    return GridDirType.Left;
                case GridDirType.Down:
                    return GridDirType.Right;
                case GridDirType.Left:
                    return GridDirType.Down;
                case GridDirType.Right:
                    return GridDirType.Up;
            }
            return GridDirType.None;
        }

        public static GridDirType GetClockwiseByDir(GridDirType dir)
        {
            switch (dir)
            {
                case GridDirType.Up:
                    return GridDirType.Right;
                case GridDirType.Down:
                    return GridDirType.Left;
                case GridDirType.Left:
                    return GridDirType.Up;
                case GridDirType.Right:
                    return GridDirType.Down;
            }
            return GridDirType.None;
        }

        public static GridDirType GetDirByPostion(GridPosition originPosition, GridPosition destPosition)
        {
            return GetDirByOffset(destPosition - originPosition);
        }

        public static GridDirType GetDirByOffset(GridPosition offset)
        {
            if ((offset.x == 0) == (offset.y == 0))
            {
                return GridDirType.None;
            }
            if (offset.x == 0)
            {
                return offset.y > 0 ? GridDirType.Up : GridDirType.Down;
            }
            return offset.x > 0 ? GridDirType.Right : GridDirType.Left;
        }

        public static GridPosition GetPositionByDirAndDistance(GridPosition originPosition, GridDirType dir, int distance)
        {
            return originPosition + GetOffsetByDir(dir) * distance;
        }

        public static void UpdateAllAura(IBattle battle, BattleActionProcedureContext context)
        {
            List<int> entityUidList = battle.FetchObj<List<int>>();
            var auraEffectList = battle.FetchObj<List<BuffEffectOfAuraApply>>();
            foreach (var entity in battle.GetEntityList())
            {
                entity.CollectAuraEffect(auraEffectList);
                foreach (var auraEffect in auraEffectList)
                {
                    var auraInfo = auraEffect.effectInfo as BuffEffectInfo_AuraApply;
                    var buffEffectInfo = auraEffect.buff.GetBuffInfo().effectList.GetValueSafely(auraEffect.effectIndex);
                    if(buffEffectInfo == null)
                    {
                        continue;
                    }
                    var auraApplyInfo = buffEffectInfo as BuffEffectInfo_AuraApply;
                    if (auraApplyInfo == null)
                    {
                        continue;
                    }
                    TargetSelectUtility.AppendEntityUidList(battle, auraApplyInfo.rangeId, new TargetSelectInfo(entity), GridDirType.None, auraApplyInfo.filterFuncType, new TargetSelectFilterContext(entity), entityUidList);
                    foreach (var affectedEntityUid in auraEffect.affectedEntityUidList)
                    {
                        if (!entityUidList.Contains(affectedEntityUid))
                        {
                            IEntity affectedEntity = battle.GetEntityByUid(affectedEntityUid);
                            if (affectedEntity != null)
                            {
                                var auraAttachBuff = affectedEntity.GetBuffByRid(auraApplyInfo.buffRid);
                                if(auraAttachBuff != null)
                                {
                                    context.procedureResult.AddResult(affectedEntity.DetachBuff(auraAttachBuff.buffUid, false));
                                }
                            }
                        }
                    }
                    foreach(var affectedEntityUid in entityUidList)
                    {
                        if (!auraEffect.affectedEntityUidList.Contains(affectedEntityUid))
                        {
                            IEntity affectedEntity = battle.GetEntityByUid(affectedEntityUid);
                            context.procedureResult.AddResult(affectedEntity.AttachBuff(auraApplyInfo.buffRid, -1, false, null, context.valueSet));
                        }
                    }
                    auraEffect.affectedEntityUidList.Clear();
                    auraEffect.affectedEntityUidList.AddRange(entityUidList);
                    entityUidList.Clear();
                }
                auraEffectList.Clear();
            }
            battle.Release(auraEffectList);
            battle.Release(entityUidList);
        }

        public static bool CanCoexistWhenLocate(IEntity originEntity, IEntity targetEntity, IEntity exceptEntity = null)
        {
            if (originEntity.uid == targetEntity.uid || exceptEntity != null && exceptEntity.uid == targetEntity.uid)
            {
                return true;
            }
            if (originEntity.entityType == EntityType.Actor)
            {
                if (targetEntity.entityType == EntityType.Actor)
                {
                    return false;
                }
                else if (targetEntity.entityType == EntityType.TerrainBuff)
                {
                    return true;
                }
            }
            else if (originEntity.entityType == EntityType.TerrainBuff)
            {
                if (targetEntity.entityType != EntityType.Actor)
                {
                    return true;
                }
                else if (targetEntity.entityType != EntityType.TerrainBuff)
                {
                    return true;
                }
            }
            return false;
        }

        public static bool CanCoexistWhenLocate(IEntityDataGetter originEntity, IEntity targetEntity, IEntity exceptEntity = null)
        {
            if (exceptEntity != null && exceptEntity.uid == targetEntity.uid)
            {
                return true;
            }
            if (originEntity.GetEntityType() == EntityType.Actor)
            {
                if (targetEntity.entityType == EntityType.Actor)
                {
                    return false;
                }
                else if (targetEntity.entityType == EntityType.TerrainBuff)
                {
                    return true;
                }
            }
            else if (originEntity.GetEntityType() == EntityType.TerrainBuff)
            {
                if (targetEntity.entityType != EntityType.Actor)
                {
                    return true;
                }
                else if (targetEntity.entityType != EntityType.TerrainBuff)
                {
                    return true;
                }
            }
            return false;
        }

        public static bool CanCoexistWhenPass(IEntity originEntity, IEntity targetEntity)
        {
            if (originEntity.uid == targetEntity.uid)
            {
                return true;
            }
            if (originEntity.entityType == EntityType.Actor)
            {
                if (targetEntity.entityType == EntityType.Actor)
                {
                    return originEntity.GetBattleCampId() == targetEntity.GetBattleCampId();
                }
                else if (targetEntity.entityType == EntityType.TerrainBuff)
                {
                    return true;
                }
            }
            else if (originEntity.entityType == EntityType.TerrainBuff)
            {
                if (targetEntity.entityType != EntityType.Actor)
                {
                    return true;
                }
                else if (targetEntity.entityType != EntityType.TerrainBuff)
                {
                    return true;
                }
            }
            return false;
        }

        public static bool CheckEntityCanLocate(IBattle battle, IEntityDataGetter entityData, GridPosition pos, IEntity exceptEntity = null)
        {
            int occupySize = entityData.GetSize();
            int occupyOffset = (occupySize - 1) / 2;
            for (int i = -occupyOffset; i <= occupyOffset; ++i)
            {
                for (int j = -occupyOffset; j <= occupyOffset; ++j)
                {
                    var checkPos = pos + new GridPosition(i, j);
                    if (!battle.CheckRuleLocatableByFieldSummary(entityData.GetMoveRuleRid(), checkPos))
                    {
                        return false;
                    }
                    if (!battle.CheckPosValid(checkPos))
                    {
                        return false;
                    }
                    List<IEntity> entityList = battle.GetEntityListByFieldSummary(checkPos);
                    foreach (var occupyEntity in entityList)
                    {
                        if (!CanCoexistWhenLocate(entityData, occupyEntity, exceptEntity))
                        {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        public static bool CheckEntityCanLocate(IBattle battle, IEntity entity, BattleFieldSummaryOfRuleLocatable fieldSummaryOfRuleLocatable, GridPosition pos)
        {
            if (!fieldSummaryOfRuleLocatable.GetValue(pos))
            {
                return false;
            }
            List<IEntity> entityList = battle.GetEntityListByFieldSummary(pos);
            foreach (var occupyEntity in entityList)
            {
                if (!CanCoexistWhenLocate(entity, occupyEntity))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool CheckSameClass<T>(T v1, T v2) where T : class
        {
            if (v1 == v2)
            {
                return true;
            }
            if (v1 == null && v2 == null)
            {
                return true;
            }
            ICheckSame<T> checkSameV1 = v1 as ICheckSame<T>;
            ICheckSame<T> checkSameV2 = v2 as ICheckSame<T>;
            if (checkSameV1 == null && checkSameV2 == null)
            {
                if (v1 != null)
                {
                    return v1.Equals(v2);
                }
                if (v2 != null)
                {
                    return v2.Equals(v1);
                }
            }
            if (checkSameV1 != null)
            {
                return checkSameV1.CheckSame(v2);
            }
            if (checkSameV2 != null)
            {
                return checkSameV2.CheckSame(v1);
            }
            return true;
        }

        public static bool CheckSameStruct<T>(T v1, T v2) where T : struct
        {
            return TypeMethodUtility.CheckEqual(v1, v2);
        }

        public static bool CheckSameStructOrLogout<T>(IBattle battle, IDebugTagNodeContainer debugTagNodeContainer, T v1, T v2, string fieldName) where T : struct
        {
            if (!CheckSameStruct(v1, v2))
            {
                Debug.LogError(battle, string.Format("{0} CheckSameFailed {1} {2} {3}", debugTagNodeContainer.GetDebugTagNode().GetTagPath(), fieldName, v1, v2));
                return false;
            }
            return true;
        }

        public static bool CheckSameClassOrLogout<T>(IBattle battle, IDebugTagNodeContainer debugTagNodeContainer, T v1, T v2, string fieldName) where T : class
        {
            if (!CheckSameClass(v1, v2))
            {
                Debug.LogError(battle, string.Format("{0} CheckSameFailed {1} {2} {3}", debugTagNodeContainer.GetDebugTagNode().GetTagPath(), fieldName, v1, v2));
                return false;
            }
            return true;
        }

        public static bool CheckSameStructListOrLogout<T>(IBattle battle, IDebugTagNodeContainer debugTagNodeContainer, List<T> v1List, List<T> v2List, string fieldName) where T : struct
        {
            if (v1List.Count != v2List.Count)
            {
                Debug.LogError(battle, string.Format("{0} CheckSameFailed {1} {2} {3}", debugTagNodeContainer.GetDebugTagNode().GetTagPath(), fieldName + ".Count", v1List.Count, v2List.Count));
                return false;
            }
            bool isSame = true;
            for (int i = 0; i < v1List.Count; ++i)
            {
                T v1 = v1List[i];
                T v2 = v2List[i];
                if (!CheckSameStruct(v1, v2))
                {
                    Debug.LogError(battle, string.Format("{0} CheckSameFailed {1} {2} {3}", debugTagNodeContainer.GetDebugTagNode().GetTagPath(), string.Format("{0}[{1}]", fieldName, i), v1, v2));
                    isSame = false;
                }
            }
            return isSame;
        }

        public static bool CheckSameClassListOrLogout<T>(IBattle battle, IDebugTagNodeContainer debugTagNodeContainer, IList<T> v1List, IList<T> v2List, string fieldName) where T : class
        {
            if (v1List.Count != v2List.Count)
            {
                Debug.LogError(battle, string.Format("{0} CheckSameFailed {1} {2} {3}", debugTagNodeContainer.GetDebugTagNode().GetTagPath(), fieldName + ".Count", v1List.Count, v2List.Count));
                return false;
            }
            bool isSame = true;
            for (int i = 0; i < v1List.Count; ++i)
            {
                T v1 = v1List[i];
                T v2 = v2List[i];
                if (!CheckSameClass(v1, v2))
                {
                    Debug.LogError(battle, string.Format("{0} CheckSameFailed {1}", debugTagNodeContainer.GetDebugTagNode().GetTagPath(), string.Format("{0}[{1}]", fieldName, i)));
                    isSame = false;
                }
            }
            return isSame;
        }

        public static int AnalyzeMoveDist(GridPosition prePos, GridPosition movePos, MovePathRule movePathRule)
        {
            var pathFindResult = MovePathFindUtility.FindPathByRule(prePos, movePos, movePathRule);
            int moveDist = Math.Max(0, pathFindResult.gridResultList.Count - 1);
            pathFindResult.Release();
            return moveDist;
        }

        public static AttributeId GetAttributeIdByPartId(AttributePartId partId)
        {
            return m_convertArrayOfAttributePartIdToAttributeId[(int)partId];
        }

        public static int GetAttributePartIdIndex(AttributePartId partId)
        {
            if (partId == AttributePartId.None)
            {
                return -1;
            }
            return m_convertArrayOfAttributePartIdToIndexInAttributeId[(int)partId];
        }

        public static AttributePartId[] GetPartArrayOfAttribute(AttributeId attributeId)
        {
            return m_attributePartIdArrays[(int)attributeId];
        }

        public static void TryCheckAndEnterTerrain(IBattle battle, IEntity originEntity, GridPosition pos, BattleActionProcedureContext procedureContext)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            int terrainRid = battle.GetTerrainRid(pos);
            var logicInfo = battle.infoGetter.GetTerrainLogicInfo(terrainRid);
            if (logicInfo != null)
            {
                var resultList = battle.FetchObj<List<BuffAttachResult>>();
                HandleTerrainEnterLogic(originEntity, null, logicInfo, resultList, procedureContext.valueSet);
                if (resultList.Count > 0)
                {
                    var enterResult = battle.FetchObj<TerrainEnterResult>();
                    enterResult.buffAttachResultList.AddRange(resultList);
                    procedureContext.procedureResult.AddResult(enterResult);
                }
                battle.Release(resultList);
            }
        }

        public static void TryCheckAndEnterTerrain(IBattle battle, IEntity originEntity, GridPosition pos, List<BuffAttachResult> attachResultList, BattleVagueParamValueSet valueSet)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            int terrainRid = battle.GetTerrainRid(pos);
            var logicInfo = battle.infoGetter.GetTerrainLogicInfo(terrainRid);
            if (logicInfo != null)
            {
                HandleTerrainEnterLogic(originEntity, null, logicInfo, attachResultList, valueSet);
            }
        }

        public static void TryCheckAndTriggerTerrain(IBattle battle, IEntity originEntity, GridPosition pos, TerrainTriggerMomentType momentType, BattleActionProcedureContext procedureContext)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            int terrainRid = battle.GetTerrainRid(pos);
            var logicInfo = battle.infoGetter.GetTerrainLogicInfo(terrainRid);
            if (logicInfo != null)
            {
                if (logicInfo.trigger != null && logicInfo.trigger.momentType == momentType)
                {
                    var enterResult = HandleTerrainTriggerLogic(battle, originEntity, false, null, terrainRid, logicInfo, procedureContext);
                    if (enterResult != null)
                    {
                        procedureContext.procedureResult.AddResult(enterResult);
                    }
                }
            }
        }

        public static void TryCheckAndEnterTerrainBuff(IBattle battle, IEntity originEntity, GridPosition pos, BattleActionProcedureContext procedureContext)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            var resultList = battle.FetchObj<List<BuffAttachResult>>();
            List <IEntity> entityList = battle.GetEntityListByFieldSummary(pos);
            for (int i = 0; i < entityList.Count; ++i)
            {
                IEntity entity = entityList[i];
                if (entity.entityType != EntityType.TerrainBuff)
                {
                    continue;
                }
                var terrainBuff = entity as TerrainBuff;
                var logicInfo = terrainBuff.logicInfo;
                if (logicInfo == null)
                {
                    continue;
                }
                HandleTerrainEnterLogic(originEntity, terrainBuff, logicInfo, resultList, procedureContext.valueSet);
                if (resultList.Count > 0)
                {
                    var enterResult = battle.FetchObj<TerrainEnterResult>();
                    enterResult.buffAttachResultList.AddRange(resultList);
                    procedureContext.procedureResult.AddResult(enterResult);
                }
                resultList.Clear();
            }
            battle.Release(resultList);
        }

        public static void TryCheckAndEnterTerrainBuff(IBattle battle, IEntity originEntity, GridPosition pos, List<BuffAttachResult> attachResultList, BattleVagueParamValueSet valueSet)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            List<IEntity> entityList = battle.GetEntityListByFieldSummary(pos);
            for (int i = 0; i < entityList.Count; ++i)
            {
                IEntity entity = entityList[i];
                if (entity.entityType != EntityType.TerrainBuff)
                {
                    continue;
                }
                var terrainBuff = entity as TerrainBuff;
                var logicInfo = terrainBuff.logicInfo;
                if (logicInfo == null)
                {
                    continue;
                }
                HandleTerrainEnterLogic(originEntity, terrainBuff, logicInfo, attachResultList, valueSet);
            }
        }

        public static void TryCheckAndTriggerTerrainBuff(IBattle battle, IEntity originEntity, GridPosition pos, TerrainTriggerMomentType momentType, BattleActionProcedureContext procedureContext)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            List<TerrainBuff> removeTerrainBuffList = new List<TerrainBuff>();
            List<IEntity> entityList = battle.GetEntityListByFieldSummary(pos);
            for (int i = 0; i < entityList.Count; ++i)
            {
                IEntity entity = entityList[i];
                if (entity.entityType != EntityType.TerrainBuff)
                {
                    continue;
                }
                var terrainBuff = entity as TerrainBuff;
                var logicInfo = terrainBuff.logicInfo;
                if (logicInfo == null)
                {
                    continue;
                }
                if (logicInfo.trigger == null || logicInfo.trigger.momentType != momentType)
                {
                    continue;
                }
                var triggerResult = HandleTerrainTriggerLogic(battle, originEntity, true, terrainBuff, default, logicInfo, procedureContext);
                if (triggerResult != null)
                {
                    procedureContext.procedureResult.AddResult(triggerResult);
                }
                if (triggerResult != null && triggerResult.triggerResult != null)
                {
                    if (terrainBuff.triggerCount > 0)
                    {
                        terrainBuff.triggerCount--;
                        if (terrainBuff.triggerCount == 0)
                        {
                            var exitResult = HandleTerrainExitLogic(battle, originEntity, true, terrainBuff, default, logicInfo, procedureContext);
                            if (exitResult != null)
                            {
                                procedureContext.procedureResult.AddResult(triggerResult);
                            }
                            triggerResult.destroy = true;
                            removeTerrainBuffList.Add(terrainBuff);
                        }
                    }
                }
            }
            foreach (var terrainBuff in removeTerrainBuffList)
            {
                battle.DestroyEntityById(terrainBuff.uid);
            }
        }

        public static void TryCheckAndExitTerrain(IBattle battle, IEntity originEntity, GridPosition pos, BattleActionProcedureContext procedureContext)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            int terrainRid = battle.GetTerrainRid(pos);
            var logicInfo = battle.infoGetter.GetTerrainLogicInfo(terrainRid);
            if (logicInfo != null)
            {
                var exitResult = HandleTerrainExitLogic(battle, originEntity, false, null, terrainRid, logicInfo, procedureContext);
                if (exitResult != null)
                {
                    procedureContext.procedureResult.AddResult(exitResult);
                }
            }
        }

        public static void TryCheckAndExitTerrainBuff(IBattle battle, IEntity originEntity, GridPosition pos, BattleActionProcedureContext procedureContext)
        {
            if (!originEntity.IsAlive())
            {
                return;
            }
            List<IEntity> entityList = battle.GetEntityListByFieldSummary(pos);
            for (int i = 0; i < entityList.Count; ++i)
            {
                IEntity entity = entityList[i];
                if (entity.entityType != EntityType.TerrainBuff)
                {
                    continue;
                }
                var terrainBuff = entity as TerrainBuff;
                var logicInfo = terrainBuff.logicInfo;
                if (logicInfo == null)
                {
                    continue;
                }
                var exitResult = HandleTerrainExitLogic(battle, originEntity, true, terrainBuff, default, logicInfo, procedureContext);
                if (exitResult != null)
                {
                    procedureContext.procedureResult.AddResult(exitResult);
                }
            }
        }

        private static void HandleTerrainEnterLogic(IEntity originEntity, IEntity terrainBuff, TerrainLogicInfo logicInfo, List<BuffAttachResult> attachResultList, BattleVagueParamValueSet valueSet)
        {
            foreach (var buffInfo in logicInfo.buffList)
            {
                foreach (var buffRid in buffInfo.buffIdList)
                {
                    attachResultList.Add(originEntity.AttachBuff(buffRid, -1, false, terrainBuff, valueSet));
                }
            }
        }

        private static TerrainTriggerResult HandleTerrainTriggerLogic(IBattle battle, IEntity originEntity, bool isTerrainBuff, IEntity terrainBuff, int terrainRid, TerrainLogicInfo logicInfo, BattleActionProcedureContext procedureContext)
        {
            TerrainTriggerResult triggerResult = null;
            if (logicInfo.trigger != null)
            {
                if (triggerResult == null)
                {
                    triggerResult = battle.FetchObj<TerrainTriggerResult>();
                }
                CastSkillContext castSkillContext = battle.FetchObj<CastSkillContext>();
                castSkillContext.srcEntity = terrainBuff;
                if (isTerrainBuff)
                {
                    castSkillContext.source = new CastSkillSource(terrainBuff as TerrainBuff);
                }
                else
                {
                    castSkillContext.source = new CastSkillSource(terrainRid);
                }
                castSkillContext.procedureContext = procedureContext;
                castSkillContext.stepResult = battle.FetchObj<TargetSelectStepResult>();
                castSkillContext.stepResult.rootWrap = TargetSelectInfoWrap.Create(battle, new TargetSelectInfo(terrainBuff), GridDirType.None, null);
                castSkillContext.stepResult.stepWrapList.Add(TargetSelectInfoWrap.Create(battle, new TargetSelectInfo(originEntity), GridDirType.None, castSkillContext.stepResult.rootWrap));
                var stepResult = procedureContext.valueSet.stepResult;
                procedureContext.valueSet.stepResult = castSkillContext.stepResult;
                CastEffectGroup(battle, terrainBuff, logicInfo.trigger.effectList, castSkillContext, triggerResult);
                procedureContext.valueSet.stepResult = stepResult;
                castSkillContext.Release();
            }
            if (triggerResult != null)
            {
                triggerResult.logicInfo = logicInfo;
                triggerResult.entityUid = originEntity.uid;
                triggerResult.entityRid = originEntity.rid;
                triggerResult.isTerrainBuff = isTerrainBuff;
                if (isTerrainBuff)
                {
                    triggerResult.terrainBuffUid = terrainBuff.uid;
                    triggerResult.terrainRid = terrainBuff.rid;
                }
                else
                {
                    triggerResult.terrainRid = terrainRid;
                }
            }
            return triggerResult;
        }

        private static TerrainExitResult HandleTerrainExitLogic(IBattle battle, IEntity originEntity, bool isTerrainBuff, IEntity terrainBuff, int terrainRid, TerrainLogicInfo logicInfo, BattleActionProcedureContext procedureContext)
        {
            TerrainExitResult exitResult = null;
            foreach (var buffInfo in logicInfo.buffList)
            {
                foreach (var buffRid in buffInfo.buffIdList)
                {
                    if (exitResult == null)
                    {
                        exitResult = battle.FetchObj<TerrainExitResult>();
                    }
                    var buff = originEntity.GetBuffByRid(buffRid);
                    if (buff != null)
                    {
                        exitResult.buffDetachResultList.Add(originEntity.DetachBuff(buff, false));
                    }
                }
            }
            if (exitResult != null)
            {
                exitResult.entityUid = originEntity.uid;
                exitResult.isTerrainBuff = isTerrainBuff;
                if (isTerrainBuff)
                {
                    exitResult.terrainBuffUid = terrainBuff.uid;
                }
                else
                {
                    exitResult.terrainRid = terrainRid;
                }
            }
            return exitResult;
        }


        private static void CastEffectGroup(IBattle battle, IEntity entity, List<SkillEffectInfo> effectInfoList, CastSkillContext context, TerrainTriggerResult result)
        {
            for (int i = 0; i < effectInfoList.Count; ++i)
            {
                CastEffect(battle, entity, i, effectInfoList[i], context, result);
            }
        }

        private static void CastEffect(IBattle battle, IEntity entity, int id, SkillEffectInfo effectInfo, CastSkillContext context, TerrainTriggerResult result)
        {
            var handler = BattleFactory.CreateSkillEffectHandler(battle, effectInfo.effectType);
            if (handler != null)
            {
                handler.effectType = effectInfo.effectType;
                var handleParams = new SkillEffectHandler.HandleParams()
                {
                    effectInfo = effectInfo,
                    castSkillContext = context,
                };
                if (result.triggerResult == null)
                {
                    result.triggerResult = battle.FetchObj<TerrainEffectTriggerResult>();
                }
                handler.Handle(handleParams, id, result.triggerResult.effectResultList);
                handler.Release();
            }
        }

        public static bool TryUpdateFormationId(IBattle battle, int teamUid)
        {
            var team = battle.GetTeamByUid(teamUid);
            List<IEntity> entityList = battle.FetchObj<List<IEntity>>();
            foreach (var entityUid in team.entityUidList)
            {
                entityList.Add(battle.GetEntityByUid(entityUid));
            }
            int formationId = 0;
            int formationEntityUid = 0;
            bool anyEntityHasFormationId = false;
            if (team.formationId != 0)
            {
                foreach (var entity in entityList)
                {
                    var battleFormationGetter = entity.dataGetter as IBattleFormationGetter;
                    if (battleFormationGetter != null)
                    {
                        if (battleFormationGetter.GetBattleFormationIdList().Contains(team.formationId))
                        {
                            formationId = team.formationId;
                            formationEntityUid = entity.uid;
                            anyEntityHasFormationId = true;
                            break;
                        }
                    }
                }
            }
            bool isUpdate = false;
            if (!anyEntityHasFormationId)
            {
                foreach (var entity in entityList)
                {
                    var battleFormationGetter = entity.dataGetter as IBattleFormationGetter;
                    if (battleFormationGetter != null)
                    {
                        if (battleFormationGetter.GetBattleFormationIdList().Count > 0)
                        {
                            formationId = battleFormationGetter.GetBattleFormationIdList()[0];
                            formationEntityUid = entity.uid;
                        }
                    }
                }
            }

            if (formationId != team.formationId || formationEntityUid != team.formationEntityUid)
            {
                team.formationId = formationId;
                team.formationEntityUid = formationEntityUid;
                isUpdate = true;
            }
            battle.Release(entityList);
            return isUpdate;
        }

        public static bool CheckBattleFormationCondition(IBattle battle, IEntity formationEntity, List<IEntity> checkingEntityList, BattleFormationEffectInfo effectInfo)
        {
            var usedEntityList = battle.FetchObj<List<IEntity>>();
            int conditionIndex = 0;
            bool result = CheckBattleFormationCondition(formationEntity, checkingEntityList, effectInfo, conditionIndex, usedEntityList);
            battle.Release(usedEntityList);
            return result;
        }

        public static bool CheckBattleFormationCondition(IEntity formationEntity, List<IEntity> checkingEntityList, BattleFormationEffectInfo effectInfo, int conditionIndex, List<IEntity> usedEntityList)
        {
            if (conditionIndex >= effectInfo.conditionInfoList.Count)
            {
                return true;
            }
            var conditionInfo = effectInfo.conditionInfoList[conditionIndex];
            foreach (var entity in checkingEntityList)
            {
                //阵眼也算
                //if (entity == formationEntity)
                //{
                //    continue;
                //}
                if (usedEntityList.Contains(entity))
                {
                    continue;
                }
                if (CheckBattleFormationCondition(conditionInfo, entity))
                {
                    usedEntityList.Add(entity);
                    if (conditionIndex + 1 >= effectInfo.conditionInfoList.Count || CheckBattleFormationCondition(formationEntity, checkingEntityList, effectInfo, conditionIndex + 1, usedEntityList))
                    {
                        return true;
                    }
                    usedEntityList.Remove(entity);
                }
            }
            return false;
        }

        private class BattleFormationCheckNode
        {
            public BattleFormationCheckNode preNode;
            public IEntity entity;

            public  bool ContainsEntityInParents(IEntity entity)
            {
                if (entity == this.entity)
                {
                    return true;
                }
                if (preNode != null)
                {
                    return preNode.ContainsEntityInParents(entity);
                }
                return false;
            }

            public int GetFitCount()
            {
                return (entity != null ? 1 : 0) + (preNode != null ? preNode.GetFitCount() : 0);
            }

            public void SetFlags(bool[] result, int depth)
            {
                result[depth] = entity != null;
                if (preNode != null)
                {
                    preNode.SetFlags(result, depth - 1);
                }
            }
        }
        public static bool[] GetConditionErrorFlags(IBattle battle, IEntity formationEntity, List<IEntity> checkingEntityList, int formationId, int effectIndex)
        {
            var formationInfo = battle.infoGetter.GetBattleFormationInfo(formationId);
            if (formationInfo != null)
            {
                var effectInfo = formationInfo.effectInfoList.GetValueSafely(effectIndex);
                if (effectInfo != null)
                {
                    return GetConditionErrorFlags(formationEntity, checkingEntityList, effectInfo);
                }
            }
            return null;
        }

        public static bool[] GetConditionErrorFlags(IEntity formationEntity, List<IEntity> checkingEntityList, BattleFormationEffectInfo effectInfo)
        {
            List<BattleFormationCheckNode>[] depthNodeList = new List<BattleFormationCheckNode>[effectInfo.conditionInfoList.Count];
            bool[] result = new bool[effectInfo.conditionInfoList.Count];
            List<IEntity> tempEntityList = new List<IEntity>();
            for (int i = 0; i < effectInfo.conditionInfoList.Count; ++i)
            {
                depthNodeList[i] = new List<BattleFormationCheckNode>();
            }
            for (int i = 0; i < effectInfo.conditionInfoList.Count; ++i)
            {
                var conditionInfo = effectInfo.conditionInfoList[i];
                foreach (var entity in checkingEntityList)
                {
                    //阵眼也算
                    //if (entity == formationEntity)
                    //{
                    //    continue;
                    //}
                    if (CheckBattleFormationCondition(conditionInfo, entity))
                    {
                        tempEntityList.Add(entity);
                    }
                }
                List<BattleFormationCheckNode> preDepthList = depthNodeList.GetValueSafely(i - 1);
                List<BattleFormationCheckNode> curDepthList = depthNodeList[i];

                if (preDepthList == null)
                {
                    foreach (var entity in tempEntityList)
                    {
                        BattleFormationCheckNode node = new BattleFormationCheckNode();
                        node.entity = entity;
                        curDepthList.Add(node);
                    }
                    curDepthList.Add(new BattleFormationCheckNode());
                }
                else
                {
                    foreach (var preNode in preDepthList)
                    {
                        foreach (var entity in tempEntityList)
                        {
                            if (preNode.ContainsEntityInParents(entity))
                            {
                                continue;
                            }
                            BattleFormationCheckNode node = new BattleFormationCheckNode();
                            node.entity = entity;
                            node.preNode = preNode;
                            curDepthList.Add(node);
                        }
                        BattleFormationCheckNode emptyNnode = new BattleFormationCheckNode();
                        emptyNnode.preNode = preNode;
                        curDepthList.Add(emptyNnode);
                    }
                }
                tempEntityList.Clear();
            }
            BattleFormationCheckNode maxFitNode = null;
            int maxFitCount = -1;
            var lastDpethList = depthNodeList.GetLastValueSafely();
            foreach (var lastDepthNode in lastDpethList)
            {
                int fitCount = lastDepthNode.GetFitCount();
                if (fitCount > maxFitCount)
                {
                    maxFitCount = fitCount;
                    maxFitNode = lastDepthNode;
                }
            }
            if (maxFitNode != null)
            {
                maxFitNode.SetFlags(result, result.Length - 1);
            }
            return result;
        }

        private static bool CheckBattleFormationCondition(BattleFormationConditionInfo conditionInfo, IEntity entity)
        {
            if (conditionInfo.actorRid != 0)
            {
                if (entity.entityType != EntityType.Actor || conditionInfo.actorRid != entity.rid)
                {
                    return false;
                }
            }
            else
            {
                var actorDataGetter = entity.dataGetter as IActorDataGetter;
                if (actorDataGetter == null)
                {
                    return false;
                }
                if (conditionInfo.careerId != EntityCareerId.None)
                {
                    if (conditionInfo.careerId != actorDataGetter.GetCareerId())
                    {
                        return false;
                    }
                }
                if (conditionInfo.raceId != EntityRaceId.None)
                {
                    if (conditionInfo.raceId != actorDataGetter.GetRaceId())
                    {
                        return false;
                    }
                }
                if (conditionInfo.elementId != EntityElementId.None)
                {
                    if (conditionInfo.elementId != actorDataGetter.GetElementId())
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        public static IEntity GetFirstEntityNeedAct(IBattle battle)
        {
            return GetNextEntityNeedAct(battle, null);
        }

        public static IEntity GetNextEntityNeedAct(IBattle battle, IEntity curEntity)
        {
            var entityUidList = battle.GetMustActEntityUidList();
            if (entityUidList.Count == 0)
            {
                var team = battle.GetTeamByIndex(battle.GetCurTeamIndex());
                entityUidList = team.entityUidList;
            }
            int index = 0;
            if (curEntity != null)
            {
                for (int i = 0; i < entityUidList.Count; ++i)
                {
                    var entity = battle.GetEntityByUid(entityUidList[i]);
                    if (entity == curEntity)
                    {
                        index = i + 1;
                        break;
                    }
                }
            }
            for (int i = 0; i < entityUidList.Count; ++i)
            {
                int repeatIndex = (i + index) % entityUidList.Count;
                var entityUid = entityUidList[repeatIndex];
                var entity = battle.GetEntityByUid(entityUid);
                if (!entity.BanAction() && entity.HasActionChance())
                {
                    return entity;
                }
            }
            for (int i = 0; i < entityUidList.Count; ++i)
            {
                int repeatIndex = (i + index) % entityUidList.Count;
                var entityUid = entityUidList[repeatIndex];
                var entity = battle.GetEntityByUid(entityUid);
                if (entity.BanAction() && entity.HasActionChance())
                {
                    return entity;
                }
            }
            return null;
        }

        public static GridPosition GetNearLocatablePos(IBattle battle, IEntityDataGetter dataGetter, GridPosition pos, int maxRange, IEntity exceptEntity = null)
        {
            if (CheckEntityCanLocate(battle, dataGetter, pos))
            {
                return pos;
            }
            GridPosition nearPosCanLocate = GridPosition.invalid;
            var summary = battle.CreateFieldSummaryForPosCollection();
            for (int i = 1; i <= maxRange; ++i)
            {
                TargetSelectUtility.AppendRhombRangePosList(pos, 1, i, summary);
                foreach (var nearPos in summary.GetPosList())
                {
                    if (CheckEntityCanLocate(battle, dataGetter, nearPos, exceptEntity))
                    {
                        nearPosCanLocate = nearPos;
                        break;
                    }
                }
                if (nearPosCanLocate != GridPosition.invalid)
                {
                    break;
                }
                summary.Reset();
            }
            summary.Release();
            return nearPosCanLocate;
        }

        public static bool CheckElementRef(IBattle battle, ElementRefType refType, EntityElementId srcId, EntityElementId destId)
        {
            switch (refType)
            {
                case ElementRefType.None:
                    return GetElementMultValue(battle, srcId, destId) == 0;
                case ElementRefType.Same:
                    return srcId == destId;
                case ElementRefType.Different:
                    return srcId != destId;
                case ElementRefType.Weak:
                    return battle.CheckWeakElement(srcId, destId);
                case ElementRefType.Strong:
                    return battle.CheckStrongElement(srcId, destId);
            }
            throw new ToDoException("CheckElementRef:" + refType);
        }

        public static bool CheckCompare(CompareType compareType, int value, int compareValue)
        {
            switch (compareType)
            {
                case CompareType.Equal:
                    return value == compareValue;
                case CompareType.Greater:
                    return value > compareValue;
                case CompareType.GreaterOrEqual:
                    return value >= compareValue;
                case CompareType.Smaller:
                    return value < compareValue;
                case CompareType.SmallerOrEqual:
                    return value <= compareValue;
            }
            throw new ImposibleException();
        }

        public static bool CheckCompare(CompareType compareType, FixedValue value, FixedValue compareValue)
        {
            switch (compareType)
            {
                case CompareType.Equal:
                    return value == compareValue;
                case CompareType.Greater:
                    return value > compareValue;
                case CompareType.GreaterOrEqual:
                    return value >= compareValue;
                case CompareType.Smaller:
                    return value < compareValue;
                case CompareType.SmallerOrEqual:
                    return value <= compareValue;
            }
            throw new ImposibleException();
        }

        public static void TryBuffTrigger(this IBattle battle, BuffTriggerType triggerType, int entityUid, BattleActionProcedureContext context)
        {
            var entityList = battle.FetchObj<List<IEntity>>();
            entityList.AddRange(battle.GetEntityList());
            foreach (var entity in entityList)
            {
                if (entity.uid == entityUid && entity.buffComponent != null)
                {
                    entity.buffComponent.TryBuffTrigger(triggerType, context);
                }
            }
            battle.Release(entityList);
        }

        public static void TryBuffTrigger(this IBattle battle, BuffTriggerType triggerType, BattleActionProcedureContext context, IEntity triggerEntity)
        {
            var entityList = battle.FetchObj<List<IEntity>>();
            entityList.AddRange(battle.GetEntityList());
            foreach (var entity in entityList)
            {
                if (entity.buffComponent != null)
                {
                    entity.buffComponent.TryBuffTrigger(triggerType, context, triggerEntity);
                }
            }
            battle.Release(entityList);
        }

        public static void TryBuffTrigger(this IBattle battle, BuffTriggerType triggerType, BattleActionProcedureContext context)
        {
            var entityList = battle.FetchObj<List<IEntity>>();
            entityList.AddRange(battle.GetEntityList());
            foreach (var entity in entityList)
            {
                if (entity.buffComponent != null)
                {
                    entity.buffComponent.TryBuffTrigger(triggerType, context);
                }
            }
            battle.Release(entityList);
        }

        public static void CollectSkillTriggerEffect(this IBattle battle, BuffTriggerType triggerType, List<BuffEffectOfSkillTrigger> list)
        {
            foreach (var entity in battle.GetEntityList())
            {
                if (entity.buffComponent != null)
                {
                    entity.buffComponent.CollectSkillTriggerEffect(triggerType, list);
                }
            }
        }

        public static void CollectSkillTriggerEffect(this IBattle battle, List<BuffEffectOfSkillTrigger> list)
        {
            foreach (var entity in battle.GetEntityList())
            {
                if (entity.buffComponent != null)
                {
                    entity.buffComponent.CollectSkillTriggerEffect(list);
                }
            }
        }

        public static void UnlockAllBuffLifeTime(IBattle battle)
        {
            foreach (var e in battle.GetEntityList())
            {
                foreach (var buff in e.GetBuffList())
                {
                    buff.lockLifeTime = false;
                }
            }
        }

        public static bool CheckCampRef(int campId1, int campId2, BattleCampRefType campRefType)
        {
            switch (campRefType)
            {
                case BattleCampRefType.Friendly:
                    return campId1 == campId2;
                case BattleCampRefType.Rival:
                    return campId1 != campId2;
            }
            return false;
        }

        public static List<GridPosition> GetPreAnnounceGridList(IEntity entity, out Skill skill)
        {
            IBattle battle = entity.GetBattle();
            int preAnnoucneSkillUid = entity.GetPreAnnounceSkillUid();
            if (preAnnoucneSkillUid <= 0)
            {
                skill = null;
                return null;
            }
            var preAnnounceSkillStepPosList = entity.GetPreAnnounceStepPosList();
            skill = entity.GetSkillBySkillUid(preAnnoucneSkillUid);
            var effectInfo = skill.skillInfo.effectList.GetValueSafely(skill.skillInfo.mainEffectIndex);
            var posCollection = battle.CreateFieldSummaryForPosCollection();
            var paramContainer = battle.FetchObj<TargetSelectParamContainer>();
            paramContainer.Init(preAnnounceSkillStepPosList);
            BattleVagueParamValueSet valueSet = SkillForcastUtiltiy.GetForcastValueSet(battle, skill, paramContainer);

            using var effectHandler = BattleFactory.CreateSkillEffectHandler(battle, effectInfo.effectType);
            effectHandler.CollectGridPos(effectInfo, BattleActionEffectConditionAriseType.SkillEffect, valueSet, posCollection);

            List<GridPosition> posList = new List<GridPosition>(posCollection.GetPosList());
            posCollection.Release();
            valueSet.Release();
            paramContainer.Release();
            return posList;
        }

        public static void RefreshBuffActiveState(this IBattle battle, BattleVagueParamConditionChangeEventId conditionChangeEventId, BattleActionProcedureContext context)
        {
            var buffChangeActiveGroupResult = battle.FetchObj<BuffChangeActiveGroupResult>();
            context.procedureResult.AddResult(buffChangeActiveGroupResult);
            RefreshBuffActiveState(battle, conditionChangeEventId, context.valueSet, buffChangeActiveGroupResult.resultList);
        }

        public static void RefreshBuffActiveState(this IBattle battle, BattleVagueParamConditionChangeEventId conditionChangeEventId, BattleVagueParamValueSet valueSet, List<BuffChangeActiveResult> resultList)
        {
            foreach (var entity in battle.GetEntityList())
            {
                entity.RefreshBuffActiveState(conditionChangeEventId, valueSet, resultList);
            }
        }

        public static void SetOtherSummonRefMustAct(IBattle battle, int entityUid, int exceptEntityUid)
        {
            var entity = battle.GetEntityByUid(entityUid);
            if (entity == null)
            {
                return;
            }
            var rootSummonerEntity = entity;
            while (true)
            {
                var summonerEntityUid = rootSummonerEntity.GetSummonerUid();
                IEntity summonerEntity = battle.GetEntityByUid(summonerEntityUid);
                if (summonerEntity == null)
                {
                    break;
                }
                rootSummonerEntity = summonerEntity;
            }

            if (rootSummonerEntity == null)
            {
                return;
            }
            SetOtherSummonCreatureRefMustAct(battle, rootSummonerEntity, exceptEntityUid);
        }

        public static void SetOtherSummonCreatureRefMustAct(IBattle battle, IEntity entity, int exceptEntityUid)
        {
            if (entity.uid != exceptEntityUid && entity.HasActionChance())
            {
                battle.SetEntityMustAct(entity.uid);
            }
            var creatureInfoList = entity.GetSummonCreatureInfoList();
            if (creatureInfoList == null || creatureInfoList.Count == 0)
            {
                return;
            }
            foreach(var creatureInfo in creatureInfoList)
            {
                foreach (var entityUid in creatureInfo.entityUidList)
                {
                    var summonCreatureEntity = battle.GetEntityByUid(entityUid);
                    if (summonCreatureEntity == null)
                    {
                        continue;
                    }
                    SetOtherSummonCreatureRefMustAct(battle, summonCreatureEntity, exceptEntityUid);
                }
            }
        }
    }
}
