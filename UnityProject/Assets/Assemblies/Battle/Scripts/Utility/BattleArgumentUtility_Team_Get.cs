using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static BattleTeam GetTeam(BattleArgumentInfo_Team info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Team) + "是空的");
                return default;
            }
            switch (info.funcType)
            {
                case BattleArgumentTeamFuncType.Uid:
                    return GetTeam(info as BattleArgumentInfo_Team_Uid, ariseType, valueSet);
                case BattleArgumentTeamFuncType.Self:
                    return GetTeam(info as BattleArgumentInfo_Team_Self, ariseType, valueSet);
                case BattleArgumentTeamFuncType.Entity:
                    return GetTeam(info as BattleArgumentInfo_Team_Entity, ariseType, valueSet);
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(GetTeam), info.funcType.ToString());
            }
        }

        private static BattleTeam GetTeam(BattleArgumentInfo_Team_Uid info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.battle.GetTeamByUid(info.uid);
        }

        private static BattleTeam GetTeam(BattleArgumentInfo_Team_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return null;
            }
            return selfEntity.GetTeam();
        }

        private static BattleTeam GetTeam(BattleArgumentInfo_Team_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var entity = GetEntity(info.entity, ariseType, valueSet);
            if (entity == null)
            {
                return null;
            }
            return entity.GetTeam();
        }
    }
}
