using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static void CollectEntity(BattleArgumentInfo_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            switch (info.funcType)
            {
                case BattleArgumentEntityFuncType.Uid:
                case BattleArgumentEntityFuncType.Self:
                case BattleArgumentEntityFuncType.Summoner:
                case BattleArgumentEntityFuncType.CombatTarget:
                case BattleArgumentEntityFuncType.SkillCaster:
                case BattleArgumentEntityFuncType.Destruct_Pos1:
                case BattleArgumentEntityFuncType.Destruct_Pos_Custom:
                    {
                        var entity = GetEntity(info, ariseType, valueSet);
                        if (entity != null)
                        {
                            collectList.Add(entity);
                        }
                    }
                    break;
                case BattleArgumentEntityFuncType.UidList:
                    CollectEntity(info as BattleArgumentInfo_Entity_UidList, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentEntityFuncType.Union:
                    CollectEntity(info as BattleArgumentInfo_Entity_Union, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Range_Self:
                    CollectEntity(info as BattleArgumentInfo_Destruct_Range_Self, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Range_Select1:
                    CollectEntity(info as BattleArgumentInfo_Destruct_Range_Select1, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Range_Custom:
                    CollectEntity(info as BattleArgumentInfo_Destruct_Range_Custom, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentEntityFuncType.LastSkillEffectTarget:
                    CollectEntity(info as BattleArgumentInfo_LastSkillEffectTarget, ariseType, valueSet, collectList);
                    break;
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectEntity), info.funcType.ToString());
            }
        }

        private static void CollectEntity(BattleArgumentInfo_Entity_UidList info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            int count = info.uidList.Count;
            for (int i = 0; i < count; ++i)
            {
                collectList.AddNotContainsNotNull(valueSet.battle.GetEntityByUid(info.uidList[i]));
            }
        }

        private static void CollectEntity(BattleArgumentInfo_Entity_Union info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            int count = info.entityList.Count;
            for (int i = 0; i < count; ++i)
            {
                CollectEntity(info.entityList[i], ariseType, valueSet, collectList);
            }
        }

        private static void CollectEntity(BattleArgumentInfo_Destruct_Range_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            using var posCollection = valueSet.battle.CreateFieldSummaryForPosCollection();
            CollectEntityCollectRange(info, ariseType, valueSet, posCollection);
            CollectDestructEntity(posCollection, collectList, info.campRefType, ariseType, valueSet);
        }

        private static void CollectEntity(BattleArgumentInfo_Destruct_Range_Select1 info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            using var posCollection = valueSet.battle.CreateFieldSummaryForPosCollection();
            CollectEntityCollectRange(info, ariseType, valueSet, posCollection);
            CollectDestructEntity(posCollection, collectList, info.campRefType, ariseType, valueSet);
        }

        private static void CollectEntity(BattleArgumentInfo_Destruct_Range_Custom info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            using var posCollection = valueSet.battle.CreateFieldSummaryForPosCollection();
            CollectEntityCollectRange(info, ariseType, valueSet, posCollection);
            CollectDestructEntity(posCollection, collectList, info.campRefType, ariseType, valueSet);
        }

        private static void CollectEntity(BattleArgumentInfo_LastSkillEffectTarget info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<IEntity> collectList)
        {
            throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectEntity), info.funcType.ToString());
        }
    }
}
