using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static FixedValue GetValue(BattleArgumentInfo_Value info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Value) + "是空的");
                return default;
            }
            switch (info.funcType)
            {
                case BattleArgumentValueFuncType.MoveDist:
                    return GetValue(info as BattleArgumentInfo_Value_MoveDist, ariseType, valueSet);
                case BattleArgumentValueFuncType.TargetDist:
                    return GetValue(info as BattleArgumentInfo_Value_TargetDist, ariseType, valueSet);
                case BattleArgumentValueFuncType.TurnIndex:
                    return GetValue(info as BattleArgumentInfo_Value_TurnIndex, ariseType, valueSet);
                case BattleArgumentValueFuncType.StepIndex:
                    return GetValue(info as BattleArgumentInfo_Value_StepIndex, ariseType, valueSet);
                case BattleArgumentValueFuncType.TeamEnergy:
                    return GetValue(info as BattleArgumentInfo_Value_TeamEnergy, ariseType, valueSet);
                case BattleArgumentValueFuncType.HpRate:
                    return GetValue(info as BattleArgumentInfo_Value_HpRate, ariseType, valueSet);
                case BattleArgumentValueFuncType.EntityCountInRange_Self:
                    return GetValue(info as BattleArgumentInfo_Value_EntityCountInRange_Self, ariseType, valueSet);
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(GetValue), info.funcType.ToString());


            }
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_MoveDist info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.moveDist;
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_TargetDist info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.targetDist;
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_TurnIndex info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.battle.GetCurTurnIndex();
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_StepIndex info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var battle = valueSet.battle;
            var curTeamIndex = battle.GetCurTeamIndex();
            var curTeam = battle.GetTeamByIndex(curTeamIndex);
            return valueSet.battle.GetCurStepIndex(curTeam.uid);
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_TeamEnergy info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return FixedValue.zero;
            }
            return selfEntity.GetTeam().sharedEnergy;
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_HpRate info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var entity = GetEntity(info.entity, ariseType, valueSet);
            if (entity == null)
            {
                return FixedValue.invalid;
            }
            return entity.GetCurHpRate();
        }

        private static FixedValue GetValue(BattleArgumentInfo_Value_EntityCountInRange_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return 0;
            }
            var battle = valueSet.battle;
            var rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(battle, info.rangeId);
            using var posCollection = battle.CreateFieldSummaryForPosCollection();
            TargetSelectUtility.AppendRangePosList(battle, rangeInfo, selfEntity.GetLocation(), GridDirType.None, posCollection);
            var posList = posCollection.GetPosList();
            var posCount = posList.Count;
            int result = 0;
            for (int i = 0; i < posCount; ++i)
            {
                var pos = posList[i];
                var entityList = battle.GetEntityListByFieldSummary(pos);
                int entityCount = entityList.Count;
                for (int j = 0; j < entityCount; ++j)
                {
                    var entity = entityList[j];
                    if (BattleObjCheckUtility.CheckEntity(info.entityCheck, ariseType, valueSet, entity))
                    {
                        result++;
                    }
                }
            }
            return result;
        }

    }
}
