using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;
// using PlasticGui.Gluon.WorkspaceWindow.Views.WorkspaceExplorer;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        private static IEntity GetSelfEntity(BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            switch (ariseType)
            {
                case BattleActionEffectConditionAriseType.BuffTrigger:
                case BattleActionEffectConditionAriseType.Buff:
                    if (valueSet.runningBuff != null)
                    {
                        return valueSet.runningBuff.entity;
                    }
                    return null;
                case BattleActionEffectConditionAriseType.Skill:
                case BattleActionEffectConditionAriseType.SkillSelect:
                case BattleActionEffectConditionAriseType.SkillEffect:
                    if (valueSet.runningSkill != null)
                    {
                        return valueSet.runningSkill.entity;
                    }
                    return null;
            }
            return null;
        }

        private static IEntity GetDestructEntity(GridPosition pos, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var entityList = valueSet.battle.GetEntityListByFieldSummary(pos);
            int count = entityList.Count;
            for (int i = 0; i < count; ++i)
            {
                var entity = entityList[i];
                if (ariseType == BattleActionEffectConditionAriseType.SkillSelect)
                {
                    if (!entity.IsSelecatableWhenSelect())
                    {
                        continue;
                    }
                }
                else if(ariseType == BattleActionEffectConditionAriseType.SkillEffect
                    || ariseType == BattleActionEffectConditionAriseType.BuffTrigger)
                {
                    if (!entity.IsSelecatableWhenEffect())
                    {
                        continue;
                    }
                }
                if (CheckEntityDestructable(entity))
                {
                    return entity;
                }
            }
            return null;
        }

        private static bool CheckEntityDestructable(IEntity entity)
        {
            return entity.IsDestruct();
        }

        private static void CollectDestructEntity(BattleFieldSummaryForPosCollection posCollection, IList<IEntity> entityList, BattleCampRefType campRefType, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            var selfCamp = selfEntity.GetBattleCampId();
            foreach (var pos in posCollection.GetPosList())
            {
                var entity = GetDestructEntity(pos, ariseType, valueSet);
                if (campRefType == BattleCampRefType.Any)
                {
                    entityList.AddNotContainsNotNull(entity);
                }
                else
                {
                    bool isFriendType = campRefType == BattleCampRefType.Friendly;
                    bool isSameCamp = selfCamp == entity.GetBattleCampId();
                    if (isFriendType == isSameCamp)
                    {
                        entityList.AddNotContainsNotNull(entity);
                    }
                }
            }
        }

        private static void CollectSkill(IList<Skill> skillList, BattleObjCheckInfo_Skill skillCheck, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<Skill> collectList)
        {
            int skillCount = skillList.Count;
            for (int i = 0; i < skillCount; ++i)
            {
                var skill = skillList[i];
                if (BattleObjCheckUtility.CheckSkill(skillCheck, ariseType, valueSet, skill))
                {
                    collectList.AddNotContainsNotNull(skill);
                }
            }
        }

        private static TargetSelectRangeInfo GetRangeInfo(TargetSelectRangeId rangeId, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            int extraRange = 0;
            if (valueSet.runningSkill != null)
            {
                if (ariseType == BattleActionEffectConditionAriseType.SkillEffect)
                {
                    extraRange = valueSet.runningSkill.GetExtraEffectRange();
                }
                else if (ariseType == BattleActionEffectConditionAriseType.SkillSelect)
                {
                    extraRange = valueSet.runningSkill.GetExtraSelectRange();
                }
            }
            return TargetSelectUtility.GetTargetSelectRangeInfo(valueSet.battle, rangeId, extraRange);
        }

        private static bool CheckDuringCombat(BattleObjCheckInfo_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var combatEntityList = valueSet.combatEntityList;
            int combatEntityCount = combatEntityList.Count;
            for (int i = 0; i < combatEntityCount; ++i)
            {
                var combatEntity = combatEntityList[i];
                if (BattleObjCheckUtility.CheckEntity(info, ariseType, valueSet, combatEntity))
                {
                    return true;
                }
            }
            return false;
        }
    }
}
