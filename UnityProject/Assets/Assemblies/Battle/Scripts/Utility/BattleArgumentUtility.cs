using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        private static IEntity GetSelfEntity(BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            switch (ariseType)
            {
                case BattleActionEffectConditionAriseType.BuffTrigger:
                case BattleActionEffectConditionAriseType.BuffEffectCheck:
                case BattleActionEffectConditionAriseType.Buff:
                    if (valueSet.runningBuff != null)
                    {
                        return valueSet.runningBuff.entity;
                    }
                    return null;
                case BattleActionEffectConditionAriseType.Skill:
                    if (valueSet.runningSkill != null)
                    {
                        return valueSet.runningSkill.entity;
                    }
                    return null;
                case BattleActionEffectConditionAriseType.BattleActionEffectCheck:
                    if (valueSet.runningBuff != null)
                    {
                        return valueSet.runningBuff.entity;
                    }
                    if (valueSet.runningSkill != null)
                    {
                        return valueSet.runningSkill.entity;
                    }
                    return null;
            }
            return null;
        }

        private static IEntity GetDestructEntity(GridPosition pos, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var entityList = valueSet.battle.GetEntityListByFieldSummary(pos);
            int count = entityList.Count;
            for (int i = 0; i < count; ++i)
            {
                var entity = entityList[i];
                if (CheckEntityDestructable(entity))
                {
                    return entity;
                }
            }
            return null;
        }

        private static bool CheckEntityDestructable(IEntity entity)
        {
            return entity.IsDestruct();
        }

        private static void CollectDestructEntity(BattleFieldSummaryForPosCollection posCollection, IList<IEntity> entityList, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var pos in posCollection.GetPosList())
            {
                var entity = GetDestructEntity(pos, ariseType, valueSet);
                entityList.AddNotContainsNotNull(entity);
            }
        }

        private static TargetSelectRangeInfo GetRangeInfo(TargetSelectRangeId rangeId, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            int extraRange = 0;
            if (ariseType == BattleActionEffectConditionAriseType.Skill)
            {
                if (valueSet.runningSkill != null)
                {
                    extraRange = valueSet.runningSkill.GetExtraEffectRange();
                }
            }
            return TargetSelectUtility.GetTargetSelectRangeInfo(valueSet.battle, rangeId, extraRange);
        }
    }
}
