using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static GridDirType GetDir(BattleArgumentInfo_Dir info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Dir) + "是空的");
                return default;
            }
            switch (info.funcType)
            {
                case BattleArgumentDirFuncType.Const:
                    return GetDir(info as BattleArgumentInfo_Dir_Const, ariseType, valueSet);
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(GetDir), info.funcType.ToString());
            }
            return GridDirType.None;
        }

        public static GridDirType GetDir(BattleArgumentInfo_Dir_Const info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return info.dirType;
        }
    }
}
