using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static void CollectGridPos(BattleArgumentInfo_Grid info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Grid) + "是空的");
                return;
            }
            switch (info.funcType)
            {
                case BattleArgumentGridFuncType.Pos:
                case BattleArgumentGridFuncType.Pos1:
                case BattleArgumentGridFuncType.Pos_Teleport1:
                    {
                        var pos = GetGridPos(info, ariseType, valueSet);
                        if (pos.isValid)
                        {
                            posCollection.AddPos(pos);
                        }
                    }
                    break;
                case BattleArgumentGridFuncType.PosList:
                    CollectGridPos(info as BattleArgumentInfo_Grid_PosList, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentGridFuncType.Area:
                    CollectGridPos(info as BattleArgumentInfo_Grid_Area, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentGridFuncType.Range_Self:
                    CollectGridPos(info as BattleArgumentInfo_Grid_Range_Self, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentGridFuncType.Range_Custom:
                    CollectGridPos(info as BattleArgumentInfo_Grid_Range_Custom, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentGridFuncType.Range_Select1:
                    CollectGridPos(info as BattleArgumentInfo_Grid_Range_Select1, ariseType, valueSet, posCollection);
                    break;
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectGridPos), info.funcType.ToString());

            }
        }

        private static void CollectGridPos(BattleArgumentInfo_Grid_PosList info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            int count = info.pos.Count;
            for (int i = 0; i < count; ++i)
            {
                posCollection.AddPos(info.pos[i]);
            }
        }

        private static void CollectGridPos(BattleArgumentInfo_Grid_Area info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            int startX = info.startPos.x;
            int startY = info.startPos.y;
            int endX = info.endPos.x;
            int endY = info.endPos.x;
            for (int x = startX; x <= endX; ++x)
            {
                for (int y = startY; y <= endY; ++y)
                {
                    posCollection.AddPos(new GridPosition(x, y));
                }
            }
        }

        private static void CollectGridPos(BattleArgumentInfo_Grid_Range_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return;
            }
            var centerPos = selfEntity.GetLocation();
            var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
            TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, GridDirType.None, posCollection);
        }

        private static void CollectGridPos(BattleArgumentInfo_Grid_Range_Custom info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var centerPos = GetGridPos(info.basedGrid, ariseType, valueSet);
            var dir = GetDir(info.dir, ariseType, valueSet);
            var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
            TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, dir, posCollection);
        }

        private static void CollectGridPos(BattleArgumentInfo_Grid_Range_Select1 info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            bool isNeedDir = TargetSelectUtility.IsRangeNeedDir(valueSet.battle, info.rangeId);
            if (isNeedDir)
            {
                var dirType = TargetSelectUtility.GetSelectDir(valueSet.battle, valueSet.stepResult, TargetSelectStepDirObtainType.Dir_1);
                if (dirType == GridDirType.None)
                {
                    return;
                }
                var selfEntity = valueSet.GetSelfEntity(ariseType);
                if (selfEntity == null)
                {
                    return;
                }
                var centerPos = selfEntity.GetLocation();
                var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
                TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, dirType, posCollection);
            }
            else
            {
                var centerPos = TargetSelectUtility.GetSelectPos(valueSet.battle, valueSet.stepResult, TargetSelectStepPosObtainType.Pos_1);
                var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
                TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, GridDirType.None, posCollection);
            }
        }
    }
}
