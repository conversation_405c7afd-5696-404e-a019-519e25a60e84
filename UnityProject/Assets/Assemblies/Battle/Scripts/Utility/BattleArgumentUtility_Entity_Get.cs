using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static IEntity GetEntity(BattleArgumentInfo_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Entity) + "是空的");
                return default;
            }
            switch (info.funcType)
            {
                case BattleArgumentEntityFuncType.Uid:
                    return GetEntity(info as BattleArgumentInfo_Entity_Uid, ariseType, valueSet);
                case BattleArgumentEntityFuncType.Self:
                    return GetEntity(info as BattleArgumentInfo_Entity_Self, ariseType, valueSet);
                case BattleArgumentEntityFuncType.Summoner:
                    return GetEntity(info as BattleArgumentInfo_Entity_Summoner, ariseType, valueSet);
                case BattleArgumentEntityFuncType.CombatTarget:
                    return GetEntity(info as BattleArgumentInfo_Entity_CombatTarget, ariseType, valueSet);
                case BattleArgumentEntityFuncType.SkillCaster:
                    return GetEntity(info as BattleArgumentInfo_Entity_SkillCaster, ariseType, valueSet);
                case BattleArgumentEntityFuncType.Destruct_Pos1:
                    return GetEntity(info as BattleArgumentInfo_Destruct_Pos1, ariseType, valueSet);
                case BattleArgumentEntityFuncType.Destruct_Pos_Custom:
                    return GetEntity(info as BattleArgumentInfo_Destruct_Pos_Custom, ariseType, valueSet);
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(GetEntity), info.funcType.ToString());
            }
        }

        private static IEntity GetEntity(BattleArgumentInfo_Entity_Uid info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.battle.GetEntityByUid(info.uid);
        }

        private static IEntity GetEntity(BattleArgumentInfo_Entity_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.GetSelfEntity(ariseType);
        }

        private static IEntity GetEntity(BattleArgumentInfo_Entity_Summoner info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity != null)
            {
                var summonerUid = selfEntity.GetSummonerUid();
                if (summonerUid != 0)
                {
                    return valueSet.battle.GetEntityByUid(summonerUid);
                }
            }
            return null;
        }

        private static IEntity GetEntity(BattleArgumentInfo_Entity_CombatTarget info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return null;
            }
            bool hasSelf = false;
            var combatEntityList = valueSet.combatEntityList;
            int combatEntityCount = combatEntityList.Count;
            IEntity combatTarget = null;
            for (int i = 0; i < combatEntityCount; ++i)
            {
                var combatEntity = combatEntityList[i];
                if (combatEntity == selfEntity)
                {
                    hasSelf = true;
                }
                else
                {
                    combatTarget = combatEntity;
                }
            }
            if (!hasSelf)
            {
                return null;
            }
            return combatTarget;
        }

        private static IEntity GetEntity(BattleArgumentInfo_Entity_SkillCaster info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.procedureCauserEntity;
        }

        private static IEntity GetEntity(BattleArgumentInfo_Destruct_Pos1 info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var targetPos = TargetSelectUtility.GetSelectPos(valueSet.battle, valueSet.stepResult, TargetSelectStepPosObtainType.Pos_1);
            return GetDestructEntity(targetPos, ariseType, valueSet);
        }

        private static IEntity GetEntity(BattleArgumentInfo_Destruct_Pos_Custom info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var targetPos = GetGridPos(info.centerGrid, ariseType, valueSet);
            return GetDestructEntity(targetPos, ariseType, valueSet);
        }
    }
}
