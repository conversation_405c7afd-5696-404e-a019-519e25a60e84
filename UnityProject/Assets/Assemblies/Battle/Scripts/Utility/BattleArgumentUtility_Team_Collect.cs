using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static void CollectTeam(BattleArgumentInfo_Team info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<BattleTeam> collectList)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Team) + "是空的");
                return;
            }
            switch (info.funcType)
            {
                case BattleArgumentTeamFuncType.Uid:
                case BattleArgumentTeamFuncType.Self:
                    var team = GetTeam(info, ariseType, valueSet);
                    collectList.AddNotContainsNotNull(team);
                    break;
                case BattleArgumentTeamFuncType.Entity:
                    CollectTeam(info as BattleArgumentInfo_Team_Entity, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentTeamFuncType.CampRef_Self:
                    CollectTeam(info as BattleArgumentInfo_Team_CampRef_Self, ariseType, valueSet, collectList);
                    break;
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectTeam), info.funcType.ToString());
            }
        }

        private static void CollectTeam(BattleArgumentInfo_Team_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<BattleTeam> collectList)
        {
            using var entityList = valueSet.battle.FetchList<IEntity>();
            CollectEntity(info.entity, ariseType, valueSet, entityList);
            int entityCount = entityList.Count;
            for (int i = 0; i < entityCount; ++i)
            {
                var entity = entityList[i];
                collectList.AddNotContains(entity.GetTeam());
            }
        }

        private static void CollectTeam(BattleArgumentInfo_Team_CampRef_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<BattleTeam> collectList)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return;
            }
            var allTeamList = valueSet.battle.GetTeamList();
            int teamCount = allTeamList.Count;
            var selfCampId = selfEntity.GetBattleCampId();

            switch (info.campRefType)
            {
                case BattleCampRefType.Any:
                    for (int i = 0; i < teamCount; ++i)
                    {
                        collectList.Add(allTeamList[i]);
                    }
                    break;
                case BattleCampRefType.Friendly:
                    for (int i = 0; i < teamCount; ++i)
                    {
                        var team = allTeamList[i];
                        if (team.campId == selfCampId)
                        {
                            collectList.Add(team);
                        }
                    }
                    break;
                case BattleCampRefType.Rival:
                    for (int i = 0; i < teamCount; ++i)
                    {
                        var team = allTeamList[i];
                        if (team.campId != selfCampId)
                        {
                            collectList.Add(team);
                        }
                    }
                    break;

            }
        }
    }
}
