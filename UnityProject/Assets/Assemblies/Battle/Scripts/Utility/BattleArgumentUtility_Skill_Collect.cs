using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;
// using PlasticGui.Gluon.WorkspaceWindow.Views.WorkspaceExplorer;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static void CollectSkill(BattleArgumentInfo_Skill info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<Skill> collectList)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Skill) + "是空的");
                return;
            }
            switch (info.funcType)
            {
                case BattleArgumentSkillFuncType.Self:
                    CollectSkill(info as BattleArgumentInfo_Skill_Self, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentSkillFuncType.Entity:
                    CollectSkill(info as BattleArgumentInfo_Skill_Entity, ariseType, valueSet, collectList);
                    break;
                case BattleArgumentSkillFuncType.Running:
                    CollectSkill(info as BattleArgumentInfo_Skill_Running, ariseType, valueSet, collectList);
                    break;  
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectSkill), info.funcType.ToString());
            }
        }

        private static void CollectSkill(BattleArgumentInfo_Skill_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<Skill> collectList)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return;
            }
            CollectSkill(selfEntity.GetSkillList(), info.skillCheck, ariseType, valueSet, collectList);
        }

        private static void CollectSkill(BattleArgumentInfo_Skill_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<Skill> collectList)
        {
            using var entityList = valueSet.battle.FetchList<IEntity>();
            CollectEntity(info.entity, ariseType, valueSet, entityList);
            int entityCount = entityList.Count;
            for (int i = 0; i < entityCount; ++i)
            {
                var entity = entityList[i];
                CollectSkill(entity.GetSkillList(), info.skillCheck, ariseType, valueSet, collectList);
            }
        }

        private static void CollectSkill(BattleArgumentInfo_Skill_Running info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, IList<Skill> collectList)
        {
            collectList.AddNotContainsNotNull(valueSet.runningSkill);
        }
    }
}
