using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static void CollectEntityCollectRange(BattleArgumentInfo_Entity info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            if (info == null)
            {
                valueSet.battle.logger?.LogShout(nameof(BattleArgumentInfo_Entity) + "是空的");
                return;
            }
            switch (info.funcType)
            {
                case BattleArgumentEntityFuncType.Uid:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_Uid, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Self:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_Self, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Summoner:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_Summoner, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.CombatTarget:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_CombatTarget, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.SkillCaster:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_SkillCaster, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Pos1:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Destruct_Pos1, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Pos_Custom:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Destruct_Pos_Custom, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.UidList:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_UidList, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Union:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Entity_Union, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Range_Self:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Destruct_Range_Self, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Range_Select1:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Destruct_Range_Select1, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.Destruct_Range_Custom:
                    CollectEntityCollectRange(info as BattleArgumentInfo_Destruct_Range_Custom, ariseType, valueSet, posCollection);
                    break;
                case BattleArgumentEntityFuncType.LastSkillEffectTarget:
                    CollectEntityCollectRange(info as BattleArgumentInfo_LastSkillEffectTarget, ariseType, valueSet, posCollection);
                    break;
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectEntityCollectRange), info.funcType.ToString());
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_Uid info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var entity = GetEntity(info, ariseType, valueSet);
            if (entity != null)
            {
                entity.CollectOccupiedPos(posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var entity = GetEntity(info, ariseType, valueSet);
            if (entity != null)
            {
                entity.CollectOccupiedPos(posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_Summoner info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var entity = GetEntity(info, ariseType, valueSet);
            if (entity != null)
            {
                entity.CollectOccupiedPos(posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_CombatTarget info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var entity = GetEntity(info, ariseType, valueSet);
            if (entity != null)
            {
                entity.CollectOccupiedPos(posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_SkillCaster info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var entity = GetEntity(info, ariseType, valueSet);
            if (entity != null)
            {
                entity.CollectOccupiedPos(posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Destruct_Pos1 info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var targetPos = TargetSelectUtility.GetSelectPos(valueSet.battle, valueSet.stepResult, TargetSelectStepPosObtainType.Pos_1);
            if (targetPos.isValid)
            {
                posCollection.AddPos(targetPos);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Destruct_Pos_Custom info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var targetPos = GetGridPos(info.centerGrid, ariseType, valueSet);
            if (targetPos.isValid)
            {
                posCollection.AddPos(targetPos);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_UidList info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            int count = info.uidList.Count;
            for (int i = 0; i < count; ++i)
            {
                var entity = valueSet.battle.GetEntityByUid(info.uidList[i]);
                if (entity != null)
                {
                    entity.CollectOccupiedPos(posCollection);
                }
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Entity_Union info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            int count = info.entityList.Count;
            for (int i = 0; i < count; ++i)
            {
                CollectEntityCollectRange(info.entityList[i], ariseType, valueSet, posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Destruct_Range_Self info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity != null)
            {
                var centerPos = selfEntity.GetLocation();
                var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
                TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, GridDirType.None, posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Destruct_Range_Select1 info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            bool isNeedDir = TargetSelectUtility.IsRangeNeedDir(valueSet.battle, info.rangeId);
            if (isNeedDir)
            {
                var dirType = TargetSelectUtility.GetSelectDir(valueSet.battle, valueSet.stepResult, TargetSelectStepDirObtainType.Dir_1);
                if (dirType == GridDirType.None)
                {
                    return;
                }
                var selfEntity = valueSet.GetSelfEntity(ariseType);
                if (selfEntity == null)
                {
                    return;
                }
                var centerPos = selfEntity.GetLocation();
                var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
                TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, dirType, posCollection);
            }
            else
            {
                var centerPos = TargetSelectUtility.GetSelectPos(valueSet.battle, valueSet.stepResult, TargetSelectStepPosObtainType.Pos_1);
                var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
                TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, GridDirType.None, posCollection);
            }
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_Destruct_Range_Custom info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            var centerPos = GetGridPos(info.centerGrid, ariseType, valueSet);
            var dir = GetDir(info.dir, ariseType, valueSet);
            var rangeInfo = GetRangeInfo(info.rangeId, ariseType, valueSet);
            TargetSelectUtility.AppendRangePosList(valueSet.battle, rangeInfo, centerPos, dir, posCollection);
        }

        private static void CollectEntityCollectRange(BattleArgumentInfo_LastSkillEffectTarget info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet, BattleFieldSummaryForPosCollection posCollection)
        {
            throw new ToDoException(nameof(BattleArgumentUtility), nameof(CollectEntityCollectRange), info.funcType.ToString());
        }
    }
}
