using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static bool CheckConditionListByAnd(List<BattleArgumentInfo_Condition> conditionList, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var condition in conditionList)
            {
                if (!CheckCondition(condition, ariseType, valueSet))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            switch (condition.funcType)
            {
                case BattleArgumentConditionFuncType.Or:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Or, ariseType, valueSet);
                case BattleArgumentConditionFuncType.And:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_And, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Not:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Not, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Random:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Random, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Action_During_Combat:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Action_During_Combat, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Action_Caused_BySelf:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Action_Caused_BySelf, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Check_GlobalVar_Bool:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Check_GlobalVar_Bool, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Check_GlobalVar_Int:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Check_GlobalVar_Int, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Check_Buff_Rid:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Check_Buff_Rid, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Compare_Variable:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Compare_Variable, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Compare_VariableOpend:
                    return CheckCondition(condition as BattleArgumentInfo_Condition_Compare_VariableOpend, ariseType, valueSet);

            }
            throw new ToDoException("CheckConditionByBattleActionEffectFunc");
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_And condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var child in condition.conditionList)
            {
                if (!CheckCondition(child, ariseType, valueSet))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Or condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var child in condition.conditionList)
            {
                if (CheckCondition(child, ariseType, valueSet))
                {
                    return true;
                }
            }
            return false;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Not condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return !CheckCondition(condition.condition, ariseType, valueSet);
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Random condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            //TO CHECK:随机数获取是否多次调用
            return valueSet.battle.RandomRate(condition.random);
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Action_During_Combat condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            using var entityList = valueSet.battle.FetchList<IEntity>();
            var combatEntityList = valueSet.combatEntityList;
            int combatEntityCount = combatEntityList.Count;
            for (int i = 0; i < combatEntityCount; ++i)
            {
                var combatEntity = combatEntityList[i];
                if (BattleObjCheckUtility.CheckEntity(condition.subjectCheck, ariseType, valueSet, combatEntity))
                {
                    entityList.Add(combatEntity);
                }
            }
            return false;
            //if (condition.activeFilterType == SkillActiveFilterType.Any)
            //{
            //    if (entityList.Count == 0)
            //    {
            //        return false;
            //    }
            //}
            //else
            //{
            //    int entityCount = entityList.Count;
            //    for (int i = 0; i < entityCount; ++i)
            //    {
            //        var entity = entityList[i];
            //        var isActive = valueSet.procedureCauserEntity == entity;
            //        var isActive2 = condition.activeFilterType == SkillActiveFilterType.Active;
            //        if (isActive != isActive2)
            //        {
            //            return false;
            //        }
            //    }
            //}
            //if (condition.skillCheck == null)
            //{
            //    return true;
            //}
            //return BattleObjCheckUtility.CheckSkill(condition.skillCheck, ariseType, valueSet, valueSet.engageSkill);
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Action_Caused_BySelf condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var selfEntity = valueSet.GetSelfEntity(ariseType);
            if (selfEntity == null)
            {
                return false;
            }
            var entityBoard = valueSet.GetActionBoard_Entity(selfEntity.uid);
            if (condition.damage && entityBoard.damage)
            {
                return true;
            }
            if (condition.kill && entityBoard.kill)
            {
                return true;
            }
            if (condition.criticalHit && entityBoard.criticalHit)
            {
                return true;
            }
            if (condition.assistGuard && entityBoard.assistGuard)
            {
                return true;
            }
            if (condition.normalAttack && entityBoard.normalAttack)
            {
                return true;
            }
            if (condition.combat && entityBoard.combat)
            {
                return true;
            }
            return false;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Check_GlobalVar_Bool condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.battle.GetBoolVariable(condition.id) == condition.boolValue;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Check_GlobalVar_Int condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.battle.GetIntVariable(condition.id) == condition.intValue;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Check_Buff_Rid condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var entity = GetEntity(condition.entity, ariseType, valueSet);
            return entity.HasAnyBuff(condition.buffRidList);
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Compare_Variable condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var srcValue = GetValue(condition.srcValue, ariseType, valueSet);
            return BattleUtility.CheckCompare(condition.compareType, srcValue, condition.destValue);
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition_Compare_VariableOpend condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var srcValue = GetValue(condition.srcValue, ariseType, valueSet);
            var destValue = GetValue(condition.destValue, ariseType, valueSet);
            return BattleUtility.CheckCompare(condition.compareType, srcValue, destValue);
        }
    }
}
