using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static bool CheckConditionListByAnd(List<BattleArgumentInfo_Condition> conditionList, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var condition in conditionList)
            {
                if (!CheckCondition(condition, ariseType, valueSet))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool CheckCondition(BattleArgumentInfo_Condition condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            switch (condition.funcType)
            {
                case BattleArgumentConditionFuncType.Or:
                    return CheckCondition_Or(condition as BattleArgumentInfo_Condition_Or, ariseType, valueSet);
                case BattleArgumentConditionFuncType.And:
                    return CheckCondition_And(condition as BattleArgumentInfo_Condition_And, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Not:
                    return CheckCondition_Not(condition as BattleArgumentInfo_Condition_Not, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Random:
                    return CheckCondition_Random(condition as BattleArgumentInfo_Condition_Random, ariseType, valueSet);
                case BattleArgumentConditionFuncType.Action_During:
                    return CheckCondition_Action_During(condition as BattleArgumentInfo_Condition_Action_During, ariseType, valueSet);

            }
            throw new ToDoException("CheckConditionByBattleActionEffectFunc");
        }

        public static bool CheckCondition_And(BattleArgumentInfo_Condition_And condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var child in condition.conditionList)
            {
                if (!CheckCondition(child, ariseType, valueSet))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool CheckCondition_Or(BattleArgumentInfo_Condition_Or condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            foreach (var child in condition.conditionList)
            {
                if (CheckCondition(child, ariseType, valueSet))
                {
                    return true;
                }
            }
            return false;
        }

        public static bool CheckCondition_Not(BattleArgumentInfo_Condition_Not condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return !CheckCondition(condition.condition, ariseType, valueSet);
        }

        public static bool CheckCondition_Random(BattleArgumentInfo_Condition_Random condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return valueSet.battle.RandomRate(condition.random);
        }

        public static bool CheckCondition_Action_During(BattleArgumentInfo_Condition_Action_During condition, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            var subjectEntity = BattleArgumentUtility.GetEntity(condition.subject, ariseType, valueSet);
            if (subjectEntity == null)
            {
                return false;
            }
            return valueSet.duringCombat && valueSet.combatEntityList.Contains(subjectEntity);
        }
    }
}
