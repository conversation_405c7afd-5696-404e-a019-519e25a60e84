using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleArgumentUtility
    {
        public static GridPosition GetGridPos(BattleArgumentInfo_Grid info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            switch (info.funcType)
            {
                case BattleArgumentGridFuncType.Pos:
                    return GetGridPos(info as BattleArgumentInfo_Grid_Pos, ariseType, valueSet);
                case BattleArgumentGridFuncType.Pos1:
                    return GetGridPos(info as BattleArgumentInfo_Grid_Pos1, ariseType, valueSet);
                case BattleArgumentGridFuncType.Pos_Teleport1:
                    return GetGridPos(info as BattleArgumentInfo_Grid_Pos_Teleport1, ariseType, valueSet);
                default:
                    throw new ToDoException(nameof(BattleArgumentUtility), nameof(GetGridPos), info.funcType.ToString());
            }
        }

        private static GridPosition GetGridPos(BattleArgumentInfo_Grid_Pos info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return info.pos;
        }

        private static GridPosition GetGridPos(BattleArgumentInfo_Grid_Pos1 info, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return TargetSelectUtility.GetSelectPos(valueSet.battle, valueSet.stepResult, TargetSelectStepPosObtainType.Pos_1);
        }

        private static GridPosition GetGridPos(BattleArgumentInfo_Grid_Pos_Teleport1 battleArgumentInfo_Grid_Pos_SelectTeleport, BattleActionEffectConditionAriseType ariseType, BattleVagueParamValueSet valueSet)
        {
            return TargetSelectUtility.GetSelectPos(valueSet.battle, valueSet.stepResult, TargetSelectStepPosObtainType.Teleport_1);
        }
    }
}
