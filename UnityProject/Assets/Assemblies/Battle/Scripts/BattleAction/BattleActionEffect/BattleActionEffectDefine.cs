using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using Phoenix.Core;

namespace Phoenix.Battle
{
    [EnumForDesc]
    public enum BuffTriggerType
    {
        [EnumValueDesc("移动结束时")]
        //落位结束刷新位置
        AfterLocateEnd,
        [EnumValueDesc("技能开始前，护卫前")]
        //初始化目标选择信息，技能信息,
        //攻击者临时添加Buff
        BeforeEngageBegin,//护卫之前战前触发，目标还是护卫前的目标
        [EnumValueDesc("技能开始前，护卫后")]
        //护卫开始逻辑，触发护卫时目标替换成护卫者
        AfterEngageBegin,//护卫之后战前触发，目标是护卫后的目标
        [EnumValueDesc("单次攻击前")]
        //初始化使用的技能信息，攻击者受击者信息
        BeforeCastSkill,//攻击前触发
        [EnumValueDesc("单次攻击后")]
        //技能效果逻辑
        AfterCastSkill,//攻击后触发
        [EnumValueDesc("技能后，护卫复位前")]
        //跳出循环
        BeforeEngageEnd,//护卫结束前，目标还是护卫者//触发再移动
        [EnumValueDesc("技能后，护卫复位后")]
        //护卫结束逻辑，护卫者恢复原位，目标变成此前的目标
        AfterEngageEnd,//技能流程结束后触发，目标是此前的目标
        [EnumValueDesc("行动结束前")]
        //技能临时添加的Buff删除
        BeforeActionEnd,//触发再移动时不执行
        [EnumValueDesc("行动结束后")]
        //刷新buff持续时间，技能CD，触发再移动时不执行
        AfterActionEnd,//触发再移动时不执行

        [EnumValueDesc("战斗开始时")]
        MainBattleStart,
        [EnumValueDesc("回合开始时")]
        TurnStart,
        [EnumValueDesc("回合结束时")]
        TurnEnd,
        [EnumValueDesc("死亡时")]
        AfterEntityDead,
        [EnumValueDesc("位移后")]
        AfterEntityMove,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfEntityListType
    {
        [EnumValueDesc("目标")]
        TargetSelect,
        [EnumValueDesc("技能范围")]
        SkillRangeSelect,
        [EnumValueDesc("自己")]
        Origin,
        [EnumValueDesc("技能效果目标")]
        EffectTarget,
        [EnumValueDesc("召唤者")]
        Summoner,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfGridListType
    {
        [EnumValueDesc("目标")]
        TargetSelect,
        [EnumValueDesc("技能范围")]
        SkillRangeSelect,
        [EnumValueDesc("自己")]
        Origin,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfGridType
    {
        [EnumValueDesc("选择结果序号")]
        TargetSelect,
        [EnumValueDesc("范围内随机")]
        RangeRandom,
        [EnumValueDesc("自己")]
        Origin,
    }

    [EnumForDesc]
    public enum BattleActionEffectGridSelectType
    {
        [EnumValueDesc("选择结果序号")]
        TargetSelect,
        [EnumValueDesc("自己")]
        Origin,
    }


    [EnumForDesc]
    public enum BattleActionEffectFuncOfValueType
    {
        [EnumValueDesc("位移距离")]
        MoveDist,
        [EnumValueDesc("目标距离")]
        TargetDist,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfConditionType
    {
        [EnumValueDesc("对战中")]
        DuringCombat,
        [EnumValueDesc("主动攻击")]
        ActiveAttack,
        [EnumValueDesc("造成伤害时")]
        DuringDamageEntity,
        [EnumValueDesc("击杀角色")]
        KillTarget,
        [EnumValueDesc("暴击")]
        CriticalHit,
        [EnumValueDesc("血量百分比")]
        HpRate,
        [EnumValueDesc("范围内存在角色")]
        EntityCountInRange,
        [EnumValueDesc("效果影响的角色数量")]
        EntityCountAffected,
        [EnumValueDesc("回合序号")]
        TurnIndex,
        [EnumValueDesc("行动序号")]
        StepIndex,
        [EnumValueDesc("最后行动")]
        LastStep,
        [EnumValueDesc("触发护卫")]
        TriggerAssistGuard,
        [EnumValueDesc("拥有Buff")]
        CheckBuff,
        [EnumValueDesc("团队能量")]
        TeamEnergy,
        [EnumValueDesc("概率")]
        Random,
        [EnumValueDesc("全局变量")]
        Variable,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfConditionKillTargetFuncType
    {
        [EnumValueDesc("任意目标角色")]
        AnyTarget,
    }

    public enum BattleActionEffectFuncOfConditionBattleVariableFuncType
    {
        Bool,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfConditionCheckBuffFuncType
    {
        [EnumValueDesc("BuffId")]
        BuffRid,
        [EnumValueDesc("组Id")]
        BuffGroupId,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfConditionLogicType
    {
        [EnumValueDesc("与")]
        And,
        [EnumValueDesc("或")]
        Or,
    }

    [EnumForDesc]
    public enum BattleActionEffectSummonLevelType
    {
        [EnumValueDesc("继承召唤者等级")]
        Summoner,
        [EnumValueDesc("继承战役等级")]
        Level,
        [EnumValueDesc("自定义")]
        Custom,
    }

    public enum BattleActionEffectResultType
    {
        None,
        Success,
        Skip,
        Miss,
        Immune,
        HealInverse,
        Reflect,
        Guard,
        Dodge,
    }

    [EnumForDesc]
    public enum BattleActionEffectType
    {
        [EnumValueDesc("无")]
        None,
        [EnumValueDesc("添加Buff")]
        AttachBuff,
        [EnumValueDesc("删除Buff")]
        DetachBuff,
        [EnumValueDesc("偷取Buff")]
        StealBuff,
        [EnumValueDesc("增加Buff持续时间")]
        ExpandBuff,
        [EnumValueDesc("减少Buff持续时间")]
        ReduceBuff,
        [EnumValueDesc("伤害")]
        DamageEntity,
        [EnumValueDesc("治疗")]
        HealEntity,
        [EnumValueDesc("召唤")]
        SummonEntity,
        [EnumValueDesc("位移")]
        MoveEntity,
        [EnumValueDesc("传送")]
        TeleportEntity,
        [EnumValueDesc("换位")]
        ReplaceEntity,
        [EnumValueDesc("克隆")]
        CopyEntity,
        [EnumValueDesc("变身")]
        TransformEntity,
        [EnumValueDesc("复活")]
        ResurrectEntity,
        [EnumValueDesc("激活")]
        ActivateEntity,
        [EnumValueDesc("再移动")]
        EntityExtraMove,
        [EnumValueDesc("再行动")]
        EntityExtraAction,
        [EnumValueDesc("团队能量")]
        ChangeTeamEnergy,
        [EnumValueDesc("施放技能")]
        CastSkill,
        [EnumValueDesc("消灭角色")]
        DestroyEntity,
        [EnumValueDesc("挖墙脚")]
        ChangeTeam,
        [EnumValueDesc("减CD")]
        ReduceCoolTime,
    }

    [EnumForDesc]
    public enum BuffEffectImmuneFuncType
    {
        [EnumValueDesc("技能效果")]
        BattleActionEffect,
    }

    [EnumForDesc]
    public enum BattleActionEffectAttachBuffFuncType
    {
        [EnumValueDesc("指定BuffId")]
        Rid,
        [EnumValueDesc("随即组Id")]
        RandomRid,
        [EnumValueDesc("随机组（自定义）")]
        RandomCustom,
    }

    [EnumForDesc]
    public enum BattleActionEffectDetachBuffFuncType
    {
        [EnumValueDesc("指定Id")]
        Rid,
        [EnumValueDesc("指定类型")]
        GroupId,
        [EnumValueDesc("筛选器自定义")]
        CustomFilter,
    }

    [EnumForDesc]
    public enum BuffEffectSkillTriggerCdType
    {
        [EnumValueDesc("行动")]
        Step,
        [EnumValueDesc("回合")]
        Turn,
    }

    [EnumForDesc]
    public enum BattleActionEffectFactorType
    {
        [EnumValueDesc("乘")]
        Mult,
        [EnumValueDesc("除")]
        Devide,
    }

    public enum BattleActionEffectSummonFuncType
    {
        [EnumValueDesc("指定Id,指定位置")]
        RidAndPos,
        [EnumValueDesc("指定Id,范围填充")]
        RidAndFullRange,
        [EnumValueDesc("随机角色")]
        RandomRidAndPos,
    }

    [EnumForDesc]
    public enum BattleActionEffectCheckEntityFuncType
    {
        [EnumValueDesc("自己")]
        Origin,
        [EnumValueDesc("友方")]
        Friend,
        [EnumValueDesc("敌方")]
        Enemy,
    }

    public enum BattleActionEffectDecreaseBuffFuncType
    {
        Rid,
        Group,
        RidLevel,
        GroupLevel,
        CustomFilter,
        CustomFilterLevel,
    }

    public enum TerrainEffectFuncType
    {
        [EnumValueDesc("Buff")]
        Buff,
        [EnumValueDesc("触发")]
        Trigger,
    }

    [EnumForDesc]
    public enum BattleActionEffectFuncOfConditionSubjectType
    {
        [EnumValueDesc("自己（持有者）")]
        Self,
        [EnumValueDesc("对战目标")]
        CombatTarget,
        [EnumValueDesc("阵营关系")]
        CampRef,
    }

    public enum BattleActionEffectConditionAriseType
    {
        Skill,
        Buff,
        BuffTrigger,
        BuffEffectCheck,
        BattleTrigger,
        BattleActionEffectCheck,
        StageAction,
    }
}
