//using System;
//using System.Collections;
//using System.Collections.Generic;
//using Phoenix.Core;

//namespace Phoenix.Battle
//{
//    public abstract class BattleActionEffectHandlerConsecutively : BattleActionEffectHandler
//    {
//        protected sealed override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
//        {
//            //for (int i = 0; i < handleParams.stepResult.targetSelectInfoWrapList.Count; ++i)
//            //{
//            //    TargetSelectInfoWrap selectInfoWrap = handleParams.stepResult.targetSelectInfoWrapList[i];
//            //    HandleClipParams handleClipParams = new HandleClipParams
//            //    {
//            //        srcEntity = handleParams.srcEntity,
//            //        effectInfo = handleParams.effectInfo,
//            //        castSkillContext = handleParams.castSkillContext,
//            //        selectInfoWrap = selectInfoWrap,
//            //    };
//            //    BattleActionEffectResultClip resultClip = HandleClip(handleClipParams);
//            //    result.clipList.Add(resultClip);
//            //}
//        }

//        protected abstract BattleActionEffectResultClip HandleClip(HandleClipParams handleClipParams);

//        public struct HandleClipParams
//        {
//            public IEntity srcEntity;
//            public BattleActionEffectInfo effectInfo;
//            public TargetSelectInfoWrap selectInfoWrap;
//            public CastSkillContext castSkillContext;
//        }
//    }
//}
