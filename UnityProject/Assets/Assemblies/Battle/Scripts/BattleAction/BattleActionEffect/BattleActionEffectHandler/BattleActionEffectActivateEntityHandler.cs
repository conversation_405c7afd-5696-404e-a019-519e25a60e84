//using System;
//using System.Collections;
//using System.Collections.Generic;
//using Phoenix.Core;
//using Phoenix.ConfigData;

//namespace Phoenix.Battle
//{
//    public class BattleActionEffectActivateEntityHandler : BattleActionEffectHandler
//    {
//        protected override void OnHandle(HandleParams handleParams, BattleActionEffectResult result)
//        {
//            BattleActionEffectActivateEntityInfo info = handleParams.effectInfo as BattleActionEffectActivateEntityInfo;
//            BattleActionEffectActivateEntityResultClip resultClip = m_battle.FetchObj<BattleActionEffectActivateEntityResultClip>();
//            //IEntity targetEntity = entityReturn.GetReturn(handleParams.castSkillContext.procedureContext.valueSet);
//            //if (targetEntity != null)
//            //{
//            //    targetEntity.SetExtraActionChance();
//            //    resultClip.entityUid = targetEntity.uid;
//            //}
//        }
//    }
//}
