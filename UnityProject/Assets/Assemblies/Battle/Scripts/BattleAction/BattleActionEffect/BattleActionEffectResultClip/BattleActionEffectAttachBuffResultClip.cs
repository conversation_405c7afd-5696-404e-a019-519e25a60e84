using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class BattleActionEffectAttachBuffResultClip : BattleActionEffectResultClip
    {
        public int entityUid;
        public List<BuffAttachResult> attachResultList = new List<BuffAttachResult>();

        public override BattleActionEffectType effectType
        {
            get { return BattleActionEffectType.AttachBuff; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            attachResultList.ReleaseAll();
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            foreach (var attachResult in attachResultList)
            {
                entityUidList.Add(attachResult.entityUid);
            }
        }
    }
}
