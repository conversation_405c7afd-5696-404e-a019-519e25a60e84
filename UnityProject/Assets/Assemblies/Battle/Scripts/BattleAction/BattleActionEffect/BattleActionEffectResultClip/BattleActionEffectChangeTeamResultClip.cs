using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class BattleActionEffectChangeTeamResultClip : BattleActionEffectResultClip
    {
        public int entityUid;
        public int preTeamUid;
        public int curTeamUid;
        
        public override BattleActionEffectType effectType
        {
            get { return BattleActionEffectType.ChangeTeam; }
        }

        public override void OnRelease()
        {
            entityUid = default;
            preTeamUid = default;
            curTeamUid = default;
            base.OnRelease();
        }

        public override void CollectEntityUid(List<int> entityUidList)
        {
            entityUidList.Add(entityUid);
        }
    }
}
