using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleActionEffectResult : BattleActionResult
    {
        public int id;
        public bool success;
        public TargetSelectResult selectResult;
        public List<EntityBlockHitPointChangeResult> blockHitPointChangeResultList = new List<EntityBlockHitPointChangeResult>();
        public List<BattleActionEffectResultClip> clipList = new List<BattleActionEffectResultClip>();
        public List<BuffChangeActiveResult> buffChangeActiveResultList = new List<BuffChangeActiveResult>();


        public override void OnRelease()
        {
            id = default;
            success = false;
            blockHitPointChangeResultList.ReleaseAll();
            buffChangeActiveResultList.ReleaseAll();
            clipList.ReleaseAll();
            base.OnRelease();
        }

        public void CollectEntityUid(List<int> entityUidList)
        {
            foreach (var clip in clipList)
            {
                clip.CollectEntityUid(entityUidList);
            }
        }
    }
}
