using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TargetSelectStepResult : BattleObj
    {
        public BattleErrorCode errorCode;
        public int interruptIndex;
        public IEntity originEntity;
        public GridPosition originPos;
        public TargetSelectStepInfo stepInfo;
        public int extraRange;


        public TargetSelectInfoWrap rootWrap;
        public List<TargetSelectInfoWrap> stepWrapList = new List<TargetSelectInfoWrap>();

        public override void OnRelease()
        {
            interruptIndex = 0;
            stepInfo = default;
            errorCode = default;
            originEntity = default;
            originPos = default;
            extraRange = default;
            rootWrap.Release();
            rootWrap = default;
            stepWrapList.Clear();
            base.OnRelease();
        }
    }
}
