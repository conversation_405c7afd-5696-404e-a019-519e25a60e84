using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class TargetSelectUtility
    {
        private static readonly KeyValueCache<int, TargetSelectRangeInfo> m_rangeInfoCache = new KeyValueCache<int, TargetSelectRangeInfo>(5, (a, b) => a == b);

        public static void AppendEntityUidList(IBattle battle, TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, TargetSelectTargetFilterType filterType, TargetSelectFilterContext filterContext, List<int> entityUidList)
        {
            TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId);
            if (rangeInfo == null)
            {
                return;
            }
            var summaryForPosCollection = battle.CreateFieldSummaryForPosCollection();
            AppendRangePosList(battle, rangeInfo, originSelectInfo, dirType, summaryForPosCollection);
            List<IEntity> entityList = battle.FetchObj<List<IEntity>>();
            foreach (var gridPos in summaryForPosCollection.GetPosList())
            {
                CheckFilterAndCollectEntity(battle, filterType, filterContext, gridPos, entityList);
            }
            foreach (var entity in entityList)
            {
                entityUidList.Add(entity.uid);
            }
            battle.Release(entityList);
            battle.Release(summaryForPosCollection);
        }

        public static void AppendSkillSelectRangePosList(this IEntity entity, int skillUid, TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, BattleFieldSummaryForPosCollection summary)
        {
            int extraRange = SkillLogicUtility.GetSkillExtraStepRange(entity, skillUid);
            IBattle battle = entity.GetBattle();
            TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId, extraRange);
            AppendRangePosList(battle, rangeInfo, originSelectInfo, dirType, summary);
        }

        public static void AppendSkillEffectRangePosList(this IEntity entity, int skillUid, TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, BattleFieldSummaryForPosCollection summary)
        {
            int extraRange = SkillLogicUtility.GetSkillExtraEffectRange(entity, skillUid);
            IBattle battle = entity.GetBattle();
            TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId, extraRange);
            AppendRangePosList(battle, rangeInfo, originSelectInfo, dirType, summary);
        }

        public static void AppendRangePosList(IBattle battle, TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, BattleFieldSummaryForPosCollection summary)
        {
            TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId);
            if (rangeInfo == null)
            {
                return;
            }
            AppendRangePosList(battle, rangeInfo, originSelectInfo, dirType, summary);
        }

        public static void AppendDirPosList(IBattle battle, IEntity originEntity, GridPosition originPos, BattleFieldSummaryForPosCollection summary)
        {
            int occupySize = 1;
            if (originEntity != null)
            {
                occupySize = originEntity.GetOccupySize();
            }
            int occupyOffset = (occupySize - 1) / 2;
            GridPosition edgeOffsetVec = new GridPosition(0, -1);
            for (int i = 0; i < 4; ++i)
            {
                GridPosition edgePos = originPos + edgeOffsetVec * (occupyOffset + 1);
                CollectGridToList(edgePos, summary);
                AntiClockwise(ref edgeOffsetVec);
            }
        }

        public static void AppendRangePosList(IBattle battle, TargetSelectRangeInfo rangeInfo, TargetSelectInfo originSelectInfo, GridDirType dirType, BattleFieldSummaryForPosCollection summary)
        {
            AppendRangePosList(battle, rangeInfo, originSelectInfo.gridPos, dirType, summary);
        }

        public static void AppendRangePosList(IBattle battle, TargetSelectRangeInfo rangeInfo, GridPosition originPos, GridDirType dirType, BattleFieldSummaryForPosCollection summary)
        {
            if (rangeInfo == null)
            {
                return;
            }
            int occupySize = 1;
            int occupyOffset = (occupySize - 1) / 2;
            var groundStartPos = summary.startPos;
            var groundEndPos = summary.endPos;
            int groudWidth = summary.width;
            int groudHeight = summary.height;
            switch (rangeInfo.rangeType)
            {
                case TargetSelectRangeType.All:
                    {
                        for (int x = groundStartPos.x; x <= groundEndPos.x; ++x)
                        {
                            for (int y = groundStartPos.y; y <= groundEndPos.y; ++y)
                            {
                                CollectGridToList(new GridPosition(x, y), summary);
                            }
                        }
                    }
                    break;
                case TargetSelectRangeType.Rhomb:
                    {
                        for (int r = rangeInfo.min; r <= rangeInfo.max; r++)
                        {
                            AppendRhombRangePosList(originPos, occupySize, r, summary);
                        }
                        break;
                    }
                case TargetSelectRangeType.Square:
                    {
                        for (int r = rangeInfo.min; r <= rangeInfo.max; r++)
                        {
                            if (r == 0)
                            {
                                for (int i = 0; i < occupySize; ++i)
                                {
                                    for (int j = 0; j < occupySize; ++j)
                                    {
                                        CollectGridToList(new GridPosition(originPos.x + i, originPos.y + j), summary);
                                    }
                                }
                            }
                            else
                            {
                                GridPosition edgeOffsetVec = new GridPosition(0, -1);
                                GridPosition edgeStepVec = new GridPosition(1, 0);
                                GridPosition refPos = originPos;
                                for (int i = 0; i < 4; ++i)
                                {
                                    for (int j = 0; j < occupySize; ++j)
                                    {
                                        GridPosition edgePos = refPos + edgeOffsetVec * r;
                                        CollectGridToList(edgePos, summary);
                                        if (j == occupySize - 1)
                                        {
                                            for (int k = 1; k <= r; ++k)
                                            {
                                                CollectGridToList(edgePos + edgeStepVec * k, summary);
                                            }
                                        }
                                        else
                                        {
                                            refPos += edgeStepVec;
                                        }
                                    }
                                    AntiClockwise(ref edgeOffsetVec);
                                    AntiClockwise(ref edgeStepVec);
                                }
                            }
                        }
                        break;
                    }
                case TargetSelectRangeType.LineForward:
                    {
                        GridPosition forwardVec = BattleUtility.GetOffsetByDir(dirType);
                        for (int r = rangeInfo.min; r <= rangeInfo.max; r++)
                        {
                            GridPosition refPos = originPos;
                            GridPosition forwardOffsetVec = refPos + forwardVec * (r + occupyOffset);
                            CollectGridToList(forwardOffsetVec, summary);
                        }
                        break;
                    }
                case TargetSelectRangeType.LineForward3L:
                    {
                        GridPosition forwardVec = BattleUtility.GetOffsetByDir(dirType);
                        GridPosition leftVec = forwardVec;
                        GridPosition rightVec = forwardVec;
                        AntiClockwise(ref leftVec);
                        Clockwise(ref rightVec);
                        for (int r = rangeInfo.min; r <= rangeInfo.max; r++)
                        {
                            GridPosition refPos = originPos;
                            GridPosition forwardOffsetVec = refPos + forwardVec * (r + occupyOffset);
                            CollectGridToList(forwardOffsetVec, summary);
                            CollectGridToList(forwardOffsetVec + leftVec, summary);
                            CollectGridToList(forwardOffsetVec + rightVec, summary);
                        }
                        break;
                    }
                case TargetSelectRangeType.Cross:
                    {
                        for (int r = rangeInfo.min; r <= rangeInfo.max; r++)
                        {
                            if (r == 0)
                            {
                                for (int i = 0; i < occupySize; ++i)
                                {
                                    for (int j = 0; j < occupySize; ++j)
                                    {
                                        CollectGridToList(new GridPosition(originPos.x + i, originPos.y + j), summary);
                                    }
                                }
                            }
                            else
                            {
                                GridPosition edgeOffsetVec = new GridPosition(0, -1);
                                GridPosition edgeStepVec = new GridPosition(1, 0);
                                GridPosition refPos = originPos;
                                for (int i = 0; i < 4; ++i)
                                {
                                    for (int j = 0; j < occupySize; ++j)
                                    {
                                        CollectGridToList(refPos + edgeStepVec * j + edgeOffsetVec * r, summary);
                                    }
                                    refPos += edgeStepVec * (occupySize - 1);
                                    AntiClockwise(ref edgeStepVec);
                                    AntiClockwise(ref edgeOffsetVec);
                                }
                            }
                        }
                        break;
                    }
            }
        }

        public static void AppendRhombRangePosList(GridPosition originPos, int occupySize, int r, BattleFieldSummaryForPosCollection summary)
        {
            int occupyOffset = (occupySize - 1) / 2;
            if (r == 0)
            {
                for (int i = -occupyOffset; i <= occupyOffset; ++i)
                {
                    for (int j = -occupyOffset; j <= occupyOffset; ++j)
                    {
                        CollectGridToList(new GridPosition(originPos.x + i, originPos.y + j), summary);
                    }
                }
            }
            else
            {
                GridPosition edgeOffsetVec = new GridPosition(0, -1);
                GridPosition slashStepVec = new GridPosition(1, 1);
                GridPosition edgeStepVec = new GridPosition(1, 0);
                for (int i = 0; i < 4; ++i)
                {
                    for (int j = -occupyOffset; j <= occupyOffset; ++j)
                    {
                        GridPosition edgePos = originPos + edgeOffsetVec * (r + occupyOffset) + edgeStepVec * j;
                        CollectGridToList(edgePos, summary);
                        if (j == occupyOffset)
                        {
                            for (int k = 1; k < r; ++k)
                            {
                                CollectGridToList(edgePos + slashStepVec * k, summary);
                            }
                        }
                    }
                    AntiClockwise(ref edgeOffsetVec);
                    AntiClockwise(ref slashStepVec);
                    AntiClockwise(ref edgeStepVec);
                }
            }
        }

        public static bool CheckInSkillSelectRange(this IEntity entity, int skillUid, TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, GridPosition pos)
        {
            int extraRange = SkillLogicUtility.GetSkillExtraStepRange(entity, skillUid);
            IBattle battle = entity.GetBattle();
            TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId, extraRange);
            return CheckInRange(battle, rangeInfo, originSelectInfo.gridPos, dirType, pos);
        }

        public static bool CheckInSkillEffectRange(this IEntity entity, int skillUid, TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, GridPosition pos)
        {
            int extraRange = SkillLogicUtility.GetSkillExtraEffectRange(entity, skillUid);
            IBattle battle = entity.GetBattle();
            TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId, extraRange);
            return CheckInRange(battle, rangeInfo, originSelectInfo.gridPos, dirType, pos);
        }

        public static GridDirType GetDir(IBattle battle, TargetSelectInfo originSelectInfo, GridPosition pos)
        {
            GridPosition originPos = originSelectInfo.gridPos;
            int occupySize = 1;
            int occupyOffset = (occupySize - 1) / 2;
            GridPosition refPos = originPos;
            if (occupySize > 1)
            {
                int refX = Math.Clamp(pos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
                int refY = Math.Clamp(pos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
                refPos = new GridPosition(refX, refY);
            }
            return BattleUtility.GetDirByPostion(refPos, pos);
        }

        public static bool CheckInRange(IBattle battle, TargetSelectRangeInfo rangeInfo, GridPosition originPos, GridDirType dirType, GridPosition checkPos)
        {
            if (rangeInfo == null)
            {
                return false;
            }
            int occupySize = 1;
            int occupyOffset = (occupySize - 1) / 2;
            switch (rangeInfo.rangeType)
            {
                case TargetSelectRangeType.All:
                    return true;
                case TargetSelectRangeType.Rhomb:
                    {
                        GridPosition refPos = originPos;
                        if (occupySize > 1)
                        {
                            int refX = Math.Clamp(checkPos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
                            int refY = Math.Clamp(checkPos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
                            refPos = new GridPosition(refX, refY);
                        }
                        int distance = refPos.DistanceTo(checkPos);
                        return distance >= rangeInfo.min && distance <= rangeInfo.max;
                    }
                case TargetSelectRangeType.Square:
                    {
                        GridPosition refPos = originPos;
                        if (occupySize > 1)
                        {
                            int refX = Math.Clamp(checkPos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
                            int refY = Math.Clamp(checkPos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
                            refPos = new GridPosition(refX, refY);
                        }
                        int distanceX = Math.Abs(refPos.x - checkPos.x);
                        int distanceY = Math.Abs(refPos.y - checkPos.y);
                        return distanceX >= rangeInfo.min && distanceX <= rangeInfo.max && distanceY <= rangeInfo.max
                            || distanceY >= rangeInfo.min && distanceY <= rangeInfo.max && distanceX <= rangeInfo.max;
                    }
                case TargetSelectRangeType.LineForward:
                    {
                        GridPosition refPos = originPos;
                        if (occupySize > 1)
                        {
                            int refX = Math.Clamp(checkPos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
                            int refY = Math.Clamp(checkPos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
                            refPos = new GridPosition(refX, refY);
                        }
                        if (BattleUtility.GetDirByPostion(refPos, checkPos) == dirType || checkPos == refPos)
                        {
                            int distance = refPos.DistanceTo(checkPos);
                            return distance >= rangeInfo.min + rangeInfo.offsetForward && distance <= rangeInfo.max + rangeInfo.offsetForward;
                        }
                        return false;
                    }
                case TargetSelectRangeType.LineForward3L:
                    {
                        GridPosition refPos = originPos;
                        if (occupyOffset > 1)
                        {
                            int refX = Math.Clamp(checkPos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
                            int refY = Math.Clamp(checkPos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
                            refPos = new GridPosition(refX, refY);
                        }
                        if (BattleUtility.GetDirByPostion(refPos, checkPos) == dirType || checkPos == refPos)
                        {
                            int distance = refPos.DistanceTo(checkPos);
                            return distance >= rangeInfo.min + rangeInfo.offsetForward && distance <= rangeInfo.max + rangeInfo.offsetForward;
                        }
                        return false;
                    }
                case TargetSelectRangeType.Cross:
                    {
                        GridPosition refPos = originPos;
                        if (occupySize > 1)
                        {
                            int refX = Math.Clamp(checkPos.x, originPos.x, originPos.x + occupySize);
                            int refY = Math.Clamp(checkPos.y, originPos.y, originPos.y + occupySize);
                            refPos = new GridPosition(refX, refY);
                        }
                        int distanceX = Math.Abs(refPos.x - checkPos.x);
                        int distanceY = Math.Abs(refPos.y - checkPos.y);
                        return distanceX == 0 && distanceY >= rangeInfo.min && distanceY <= rangeInfo.max
                            || distanceY == 0 && distanceX >= rangeInfo.min && distanceX <= rangeInfo.max;
                    }
            }
            return false;
        }

        public static bool IsDirGrid(IBattle battle, IEntity originEntity, GridPosition originPos, GridPosition pos)
        {
            int occupySize = 1;
            if (originEntity != null)
            {
                occupySize = originEntity.GetOccupySize();
            }
            int occupyOffset = (occupySize - 1) / 2;
            GridPosition refPos = originPos;
            if (occupySize > 1)
            {
                int refX = Math.Clamp(pos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
                int refY = Math.Clamp(pos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
                refPos = new GridPosition(refX, refY);
            }
            int distance = refPos.DistanceTo(pos);
            return distance == 1;
        }

        private static void CollectGridToList(GridPosition pos, BattleFieldSummaryForPosCollection collection)
        {
            collection.AddPos(pos);
        }

        private static void AntiClockwise(ref GridPosition vec)
        {
            vec = new GridPosition(-vec.y, vec.x);
        }

        private static void Clockwise(ref GridPosition vec)
        {
            vec = new GridPosition(vec.y, -vec.x);
        }

        public static TargetSelectRangeInfo GetTargetSelectRangeInfo(IBattle battle, TargetSelectRangeId rangeId, int extraRange)
        {
            if (extraRange > 0)
            {
                for (int i = 0; i < extraRange; ++i)
                {
                    TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId);
                    rangeId = rangeInfo.nextId;
                }
            }
            else if (extraRange < 0)
            {
                extraRange = -extraRange;
                for (int i = 0; i < extraRange; ++i)
                {
                    TargetSelectRangeInfo rangeInfo = GetTargetSelectRangeInfo(battle, rangeId);
                    rangeId = rangeInfo.preId;
                }
            }
            return GetTargetSelectRangeInfo(battle, rangeId);
        }

        public static TargetSelectRangeInfo GetTargetSelectRangeInfo(IBattle battle, TargetSelectRangeId rangeId)
        {
            TargetSelectRangeInfo rangeInfo;
            bool hasValue;
            if(!m_rangeInfoCache.TryGet((int)rangeId, out rangeInfo, out hasValue))
            {
                rangeInfo = battle.infoGetter.GetTargetSelectRangeInfo((int)rangeId);
                hasValue = rangeInfo != null;
                m_rangeInfoCache.AddTop((int)rangeId, rangeInfo, hasValue);
            }
            if (hasValue)
            {
                return rangeInfo;
            }
            return null;
        }

        public static bool IsRangeNeedDir(IBattle battle, TargetSelectRangeId rangeId)
        {
            var rangeInfo = GetTargetSelectRangeInfo(battle, rangeId);
            if (rangeInfo == null)
            {
                return false;
            }
            return IsRangeNeedDir(rangeInfo.rangeType);
        }

        public static bool IsRangeNeedDir(TargetSelectRangeType rangeType)
        {
            switch (rangeType)
            {
                case TargetSelectRangeType.LineForward:
                case TargetSelectRangeType.LineForward3L:
                case TargetSelectRangeType.LineForwardBackward:
                case TargetSelectRangeType.LineSide:
                    return true;
                case TargetSelectRangeType.Cross:
                case TargetSelectRangeType.Rhomb:
                case TargetSelectRangeType.Square:
                case TargetSelectRangeType.All:
                case TargetSelectRangeType.None:
                    return false;
            }
            throw new ToDoException(nameof(TargetSelectUtility), nameof(IsRangeNeedDir), rangeType.ToString());
        }
    }
}
