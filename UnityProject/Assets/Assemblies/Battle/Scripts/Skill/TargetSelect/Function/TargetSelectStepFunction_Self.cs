using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class TargetSelectStepFunction_Self : TargetSelectStepFunction
    {
        public override void AppendFirstRange(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, BattleFieldSummaryForPosCollection posCollection)
        {
        }

        public override bool CheckSelectable(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, GridPosition checkPos, bool checkInRange)
        {
            if (paramContainer.CanPopGridPosition())
            {
                return false;
            }
            return checkPos == originPos;
        }

        public override GridPosition GetSelectPos(TargetSelectStepResult baseResult, TargetSelectStepPosObtainType obtainType)
        {
            return GridPosition.invalid;
        }

        public override GridDirType GetSelectDir(TargetSelectStepResult baseResult, TargetSelectStepDirObtainType obtainType)
        {
            return GridDirType.None;
        }

        public override GridPosition GetLastSelectPos(TargetSelectStepResult baseResult)
        {
            return baseResult.originPos;
        }

        protected override void OnHandleSelect(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, TargetSelectStepResult baseResult)
        {
        }
    }
}
