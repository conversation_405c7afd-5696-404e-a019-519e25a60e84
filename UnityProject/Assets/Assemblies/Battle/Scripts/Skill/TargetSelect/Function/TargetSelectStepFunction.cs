using System.Collections;
using System.Collections.Generic;
using Phoenix.Battle;

namespace Phoenix.Battle
{
    public abstract class TargetSelectStepFunction : BattleObj
    {
        public TargetSelectStepResult HandleSelect(TargetSelectStepInfo stepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer)
        {
            var result = TargetSelectUtility.CreateResult(m_battle, stepInfo.funcType);
            result.errorCode = BattleErrorCode.Ok;
            result.originEntity = originEntity;
            result.originPos = originPos;
            result.extraRange = extraRange;
            result.stepInfo = stepInfo;
            OnHandleSelect(stepInfo, originEntity, originPos, extraRange, paramContainer, result);
            return result;
        }

        public virtual void AppendInterruptRange(TargetSelectStepInfo baseStepInfo, TargetSelectStepResult baseResult, BattleFieldSummaryForPosCollection posCollection)
        {
            if (baseResult.errorCode != BattleErrorCode.Ok)
            {
                switch (baseResult.interruptIndex)
                {
                    case 0:
                        AppendFirstRange(baseStepInfo, baseResult.originEntity, baseResult.originPos, baseResult.extraRange, posCollection);
                        break;
                }
            }
        }

        public virtual bool TryGetLastSelectPosAndDir(TargetSelectStepResult baseResult, out GridPosition pos, out GridDirType dir)
        {
            pos = GetLastSelectPos(baseResult);
            dir = GridDirType.None;
            return false;
        }

        public abstract void AppendFirstRange(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, BattleFieldSummaryForPosCollection posCollection);
        public abstract bool CheckSelectable(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, GridPosition checkPos, bool checkInRange);
        public abstract GridPosition GetSelectPos(TargetSelectStepResult baseResult, TargetSelectStepPosObtainType obtainType);
        public abstract GridDirType GetSelectDir(TargetSelectStepResult baseResult, TargetSelectStepDirObtainType obtainType);
        public abstract GridPosition GetLastSelectPos(TargetSelectStepResult baseResult);
        protected abstract void OnHandleSelect(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, TargetSelectStepResult baseResult);
    }
}
