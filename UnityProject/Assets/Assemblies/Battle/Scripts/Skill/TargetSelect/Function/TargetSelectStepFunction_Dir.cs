using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class TargetSelectStepFunction_Dir : TargetSelectStepFunction
    {
        public override bool CheckSelectable(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, GridPosition checkPos, bool checkInRange)
        {
            if (paramContainer.CanPopGridPosition())
            {
                return false;
            }
            return TargetSelectUtility.IsDirGrid(m_battle, originEntity, originPos, checkPos);
        }

        public override void AppendFirstRange(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, BattleFieldSummaryForPosCollection posCollection)
        {
            TargetSelectUtility.AppendDirPosList(m_battle, originEntity, originPos, posCollection);
        }

        public override GridPosition GetSelectPos(TargetSelectStepResult baseResult, TargetSelectStepPosObtainType obtainType)
        {
            return GridPosition.invalid;
        }

        public override GridDirType GetSelectDir(TargetSelectStepResult baseResult, TargetSelectStepDirObtainType obtainType)
        {
            var result = baseResult as TargetSelectStepResult_Dir;
            switch (obtainType)
            {
                case TargetSelectStepDirObtainType.Dir_1:
                    return result.selectDir;
            }
            return GridDirType.None;
        }

        public override GridPosition GetLastSelectPos(TargetSelectStepResult baseResult)
        {
            return baseResult.originPos;
        }

        public override bool TryGetLastSelectPosAndDir(TargetSelectStepResult baseResult, out GridPosition pos, out GridDirType dir)
        {
            var result = baseResult as TargetSelectStepResult_Dir;
            pos = result.originPos;
            dir = result.selectDir;
            return true;
        }

        protected override void OnHandleSelect(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, TargetSelectStepResult baseResult)
        {
            var result = baseResult as TargetSelectStepResult_Dir;
            if (!paramContainer.CanPopGridDir())
            {
                result.errorCode = BattleErrorCode.TargetSelectStepParamCanNotPop;
                return;
            }
            var pos = paramContainer.PopGridPosition();
            var dir = BattleUtility.GetDirByPostion(originPos, pos);
            //var dir = paramContainer.PopGridDir();
            if (dir == GridDirType.None)
            {
                result.errorCode = BattleErrorCode.TargetSelectOutOfRange;
                return;
            }
            result.selectDir = dir;
        }
    }
}
