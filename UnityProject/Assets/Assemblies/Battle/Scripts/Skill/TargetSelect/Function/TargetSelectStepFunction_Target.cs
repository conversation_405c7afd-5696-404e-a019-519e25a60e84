using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class TargetSelectStepFunction_Target : TargetSelectStepFunction
    {
        public override void AppendFirstRange(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, BattleFieldSummaryForPosCollection posCollection)
        {
            var stepInfo = baseStepInfo as TargetSelectStepInfo_Target;
            var rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(m_battle, stepInfo.rangeId, extraRange);
            TargetSelectUtility.AppendRangePosList(m_battle, rangeInfo, originPos, GridDirType.None, posCollection);
        }

        public override bool CheckSelectable(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, GridPosition checkPos, bool checkInRange)
        {
            if (paramContainer.CanPopGridPosition())
            {
                return false;
            }
            var stepInfo = baseStepInfo as TargetSelectStepInfo_Target;
            if (checkInRange)
            {
                var rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(m_battle, stepInfo.rangeId, extraRange);
                if (!TargetSelectUtility.CheckInRange(m_battle, rangeInfo, originPos, GridDirType.None, checkPos))
                {
                    return false;
                }
            }
            return TargetSelectUtility.CheckFilter(m_battle, stepInfo.filterType, new TargetSelectFilterContext(originEntity), checkPos);
        }

        public override GridPosition GetSelectPos(TargetSelectStepResult baseResult, TargetSelectStepPosObtainType obtainType)
        {
            var result = baseResult as TargetSelectStepResult_Target;
            switch (obtainType)
            {
                case TargetSelectStepPosObtainType.Pos_1:
                    return result.targetPos;
            }
            return GridPosition.invalid;
        }

        public override GridDirType GetSelectDir(TargetSelectStepResult baseResult, TargetSelectStepDirObtainType obtainType)
        {
            return GridDirType.None;
        }

        public override GridPosition GetLastSelectPos(TargetSelectStepResult baseResult)
        {
            var result = baseResult as TargetSelectStepResult_Target;
            return result.targetPos;
        }

        protected override void OnHandleSelect(TargetSelectStepInfo baseStepInfo, IEntity originEntity, GridPosition originPos, int extraRange, TargetSelectParamContainer paramContainer, TargetSelectStepResult baseResult)
        {
            var stepInfo = baseStepInfo as TargetSelectStepInfo_Target;
            var result = baseResult as TargetSelectStepResult_Target;
            if (!paramContainer.CanPopGridPosition())
            {
                result.errorCode = BattleErrorCode.TargetSelectStepParamCanNotPop;
                return;
            }
            var selectPos = paramContainer.PopGridPosition();
            var rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(m_battle, stepInfo.rangeId, extraRange);
            bool isInRange = TargetSelectUtility.CheckInRange(m_battle, rangeInfo, originPos, GridDirType.None, selectPos);
            if (!isInRange)
            {
                result.errorCode = BattleErrorCode.TargetSelectOutOfRange;
                return;
            }
            bool filterResult = TargetSelectUtility.CheckFilter(m_battle, stepInfo.filterType, new TargetSelectFilterContext(originEntity), selectPos);
            if (!filterResult)
            {
                result.errorCode = BattleErrorCode.TargetSelectFilterNotMatch;
                return;
            }
            result.targetPos = selectPos;
        }
    }
}