using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TargetSelectInfoWrap : BattleObj
    {
        public TargetSelectInfo selectInfo;
        public TargetSelectStepInfo stepInfo;
        public GridDirType dir;
        public TargetSelectInfoWrap parent;
        public List<TargetSelectInfoWrap> childList = new List<TargetSelectInfoWrap>();

        public int entityUid
        {
            get { return selectInfo.entityUid; }
        }

        public GridPosition gridPosition
        {
            get { return selectInfo.gridPos; }
        }

        public TargetSelectInfoWrap root
        {
            get
            {
                TargetSelectInfoWrap wrap = this;
                while (wrap.parent != null)
                {
                    wrap = wrap.parent;
                }
                return wrap;
            }
        }

        public static TargetSelectInfoWrap Create(IBattle battle, TargetSelectInfo selectInfo, TargetSelectInfoWrap parent)
        {
            TargetSelectInfoWrap wrap = battle.FetchObj<TargetSelectInfoWrap>();
            wrap.selectInfo = selectInfo;
            wrap.parent = parent;
            if (parent != null)
            {
                parent.childList.Add(wrap);
            }
            return wrap;
        }

        public static TargetSelectInfoWrap Create(IBattle battle, TargetSelectInfo selectInfo, GridDirType dir, TargetSelectInfoWrap parent)
        {
            TargetSelectInfoWrap wrap = battle.FetchObj<TargetSelectInfoWrap>();
            wrap.selectInfo = selectInfo;
            wrap.parent = parent;
            wrap.dir = dir;
            if (parent != null)
            {
                parent.childList.Add(wrap);
            }
            return wrap;
        }

        public TargetSelectInfoWrap CopySingle()
        {
            TargetSelectInfoWrap wrap = m_battle.FetchObj<TargetSelectInfoWrap>();
            wrap.selectInfo = selectInfo;
            wrap.dir = dir;
            return wrap;
        }

        public override void OnRelease()
        {
            selectInfo = default;
            stepInfo = null;
            dir = default;
            parent = null;
            childList.ReleaseAll();
            base.OnRelease();
        }

        public int GetCenterDistance()
        {
            if(parent != null)
            {
                return gridPosition.DistanceTo(parent.gridPosition);
            }
            return default;
        }

        public GridDirType GetGridDir()
        {
            if(parent != null)
            {
                return BattleUtility.GetDirByPostion(parent.gridPosition, gridPosition);
            }
            return GridDirType.None;
        }
    }
}
