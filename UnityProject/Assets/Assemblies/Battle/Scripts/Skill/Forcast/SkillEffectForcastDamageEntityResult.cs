using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class SkillEffectForcastDamageEntityResult : SkillEffectForcastResult
    {
        public FixedValue damage;

        //public override SkillEffectType effectType
        //{
        //    get { return SkillEffectType.DamageEntity; }
        //}

        public override void OnRelease()
        {
            damage = default;
            base.OnRelease();
        }
    }
}
