using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class Skill : EntityObj, IBattleSnapshot, IDebugTagNodeContainer, ICheckSame<Skill>
    {
        public static readonly List<Skill> emptyList = new List<Skill>();

        public int uid;
        public int rid;
        public SkillSlotId slotId;
        public int coolTimeLeft;
        public bool lockCoolTimeLeft;
        public int exSkillUid;
        public int originSkillUid;

        private SkillInfo m_skillInfo;
        private List<EntityAttributePartItemFromSkill> m_partItemList = new List<EntityAttributePartItemFromSkill>();
        private DebugTagNode m_debugTagNode;

        public SkillInfo skillInfo
        {
            get
            {
                if (m_skillInfo == null)
                {
                    m_skillInfo = m_battle.infoGetter.GetSkillInfo(rid);
                }
                return m_skillInfo;
            }
        }

        public SkillEngageType engageType
        {
            get { return skillInfo.engageType; }
        }

        public bool isCoolDown
        {
            get { return coolTimeLeft <= 0; }
        }

        public override void OnFetch()
        {
            base.OnFetch();
        }

        public override void OnRelease()
        {
            uid = default;
            rid = default;
            slotId = default;
            coolTimeLeft = default;
            lockCoolTimeLeft = default;
            originSkillUid = default;
            exSkillUid = default;
            m_debugTagNode.Release();
            m_debugTagNode = null;
            m_skillInfo = null;
            m_partItemList.ReleaseAll();
            base.OnRelease();
        }

        public void LockCoolTime()
        {
            lockCoolTimeLeft = true;
            Skill exSkill = GetExSkill();
            if (exSkill != null)
            {
                exSkill.LockCoolTime();
            }
            Skill originSkill = GetOriginSkill();
            if (originSkill != null)
            {
                originSkill.LockCoolTime();
            }
        }

        public void UnlockCoolTime()
        {
            lockCoolTimeLeft = false;
        }

        public void Init(SkillInfo skillInfo, SkillSlotId slotId)
        {
            uid =  (skillInfo.id % 10000000) * 100 + (int)slotId;
            rid = skillInfo.id;
            this.slotId = slotId;
            m_skillInfo = skillInfo;
            foreach (var changeInfo in skillInfo.attributeChangeList)
            {
                var partItem = m_entity.FetchObj<EntityAttributePartItemFromSkill>();
                partItem.value = changeInfo.rate;
                partItem.skill = this;
                m_partItemList.Add(partItem);
            }
        }

        public void StartCoolTime()
        {
            SetCoolTime(skillInfo.coolTime);
        }

        public void SetCoolTime(int coolTime)
        {
            coolTimeLeft = System.Math.Max(0, coolTime);
            Skill exSkill = GetExSkill();
            if (exSkill != null)
            {
                exSkill.coolTimeLeft = coolTimeLeft;
            }
            Skill originSkill = GetOriginSkill();
            if (originSkill != null)
            {
                originSkill.coolTimeLeft = coolTimeLeft;
            }
        }

        public void TickCoolTime(List<EntitySkillCoolTimeUpdateResult> resultList)
        {
            if (lockCoolTimeLeft)
            {
                return;
            }
            if (coolTimeLeft > 0)
            {
                SetCoolTime(coolTimeLeft - 1);
                EntitySkillCoolTimeUpdateResult result = m_battle.FetchObj<EntitySkillCoolTimeUpdateResult>();
                result.skillUid = uid;
                result.cooltime = coolTimeLeft;
                resultList.Add(result);
            }
        }

        public BattleErrorCode CanCastSkill()
        {
            if (coolTimeLeft > 0)
            {
                return BattleErrorCode.SkillNotCoolDown;
            }
            BattleErrorCode errorCode = BattleErrorCode.Ok;
            if (entity.BanActiveSkill())
            {
                return BattleErrorCode.SkillBan;
            }
            if (m_entity.GetTeam().sharedEnergy < skillInfo.energyCost)
            {
                return BattleErrorCode.SkillAngerNotEnough;
            }
            return errorCode;
        }

        public bool ContainsTag(SkillTagType tag)
        {
            int count = skillInfo.tagList.Count;
            for(int i = 0; i < count; ++i)
            {
                if (skillInfo.tagList[i] == tag)
                {
                    return true;
                }
            }
            return false;
        }

        public EntityElementId GetElementId()
        {
            if (skillInfo.elementId == EntityElementId.None)
            {
                return m_entity.GetElementId();
            }
            return skillInfo.elementId;
        }

        public bool IsOriginSkill()
        {
            return originSkillUid == 0;
        }

        public bool IsExSkill()
        {
            return exSkillUid == 0;
        }

        public Skill GetExSkill()
        {
            return m_entity.GetSkillBySkillUid(exSkillUid);
        }

        public Skill GetOriginSkill()
        {
            return m_entity.GetSkillBySkillUid(originSkillUid);
        }

        public int GetExtraEffectRange()
        {
            return (int)m_entity.GetAttributeValue(AttributeId.ExtraSkillEffectRange);
        }

        public int GetExtraSelectRange()
        {
            return (int)m_entity.GetAttributeValue(AttributeId.ExtraSkillSelectRange);
        }

        public void RegisterPartItems()
        {
            int length = m_partItemList.Count;
            var changeList = skillInfo.attributeChangeList;
            for (int i = 0; i < length; ++i)
            {
                m_entity.AddAttributePartItem(changeList[i].partId, m_partItemList[i]);
            }
        }

        public void UnRegisterPartItems()
        {
            int length = m_partItemList.Count;
            var changeList = skillInfo.attributeChangeList;
            for (int i = 0; i < length; ++i)
            {
                m_entity.RemoveAttributePartItem(changeList[i].partId, m_partItemList[i]);
            }
        }

        public DebugTagNode GetDebugTagNode()
        {
            if (m_debugTagNode == null)
            {
                m_debugTagNode = DebugTagNode.Create(m_battle, m_entity.skillComponent.GetDebugTagNode());
                m_debugTagNode.tag.Set("[{0}:{1}]", GetType().Name, uid);
            }
            return m_debugTagNode;
        }

        public bool CheckSame(Skill baseInfo)
        {
            bool result = true;
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, uid, baseInfo.uid, "uid");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, rid, baseInfo.rid, "rid");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, (int)slotId, (int)baseInfo.slotId, "slotId");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, coolTimeLeft, baseInfo.coolTimeLeft, "coolTimeLeft");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, lockCoolTimeLeft, baseInfo.lockCoolTimeLeft, "lockCoolTimeLeft");
            return result;
        }

        public Skill Copy(IFetchable fetchable)
        {
            Skill skill = fetchable.FetchObj<Skill>();
            skill.uid = uid;
            skill.rid = rid;
            skill.slotId = slotId;
            skill.coolTimeLeft = coolTimeLeft;
            skill.lockCoolTimeLeft = lockCoolTimeLeft;
            skill.m_skillInfo = m_skillInfo;
            skill.exSkillUid = exSkillUid;
            skill.originSkillUid = originSkillUid;
            return skill;
        }

        public void BuildSnapshot(ByteBufferBuilder builder)
        {
            builder.WriteInt(uid);
            builder.WriteInt(rid);
            builder.WriteByte((byte)slotId);
            builder.WriteByte(coolTimeLeft);
            builder.WriteBool(lockCoolTimeLeft);
        }

        public void LoadSnapshot(ByteBufferLoader loader)
        {
            uid = loader.ReadInt();
            rid = loader.ReadInt();
            slotId = (SkillSlotId)loader.ReadByte();
            coolTimeLeft = loader.ReadByte();
            lockCoolTimeLeft = loader.ReadBool();
        }

        public static int SortByPriority(Skill x, Skill y)
        {
            var result = y.skillInfo.decisionPriority.CompareTo(x.skillInfo.decisionPriority);
            if (result == 0)
            {
                result = x.uid.CompareTo(y.uid);
            }
            return result;
        }
    }
}
