using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public class CastSkillContext : BattleObj
    {
        public IEntity srcEntity;
        public CastSkillSource source;
        public int combatTargetEntityUid;
        public TargetSelectStepResult stepResult;
        public List<List<BattleActionEffectDamageEntityResultClip>> resultClipList_Old = new List<List<BattleActionEffectDamageEntityResultClip>>();
        public List<List<SkillEffectResultClip_Damage>> resultClipList = new List<List<SkillEffectResultClip_Damage>>();
        public FixedValue damageRate;
        public BattleActionProcedureContext procedureContext;
        public SkillBaseResult skillBaseResult;

        public override void OnFetch()
        {
            damageRate = 100;
            base.OnFetch();
        }

        public override void OnRelease()
        {
            srcEntity = default;
            source = default;
            combatTargetEntityUid = default;
            procedureContext = default;
            stepResult.Release();
            stepResult = default;
            damageRate = default;
            skillBaseResult = null;
            resultClipList_Old.Clear();
            resultClipList.Clear();
            base.OnRelease();
        }
    }
}
