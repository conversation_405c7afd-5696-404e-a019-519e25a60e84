using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static class SkillLogicUtility
    {
        public static int GetSkillExtraStepRange(IEntity entity, int skillUid)
        {
            Skill skill = entity.GetSkillBySkillUid(skillUid);
            if (skill == null)
            {
                return 0;
            }
            bool attackOrSkill = skill.ContainsTag(SkillTagType.NormalAttack);
            int extraRange;
            if (attackOrSkill)
            {
                extraRange = (int)entity.GetAttributeValue(AttributeId.ExtraAttackSelectRange);
            }
            else
            {
                extraRange = (int)entity.GetAttributeValue(AttributeId.ExtraSkillSelectRange);
            }
            return extraRange;
        }

        public static int GetSkillExtraEffectRange(IEntity entity, int skillUid)
        {
            return (int)entity.GetAttributeValue(AttributeId.ExtraSkillEffectRange);
        }

        public static BattleErrorCode CanCastSkillForcastMove(IEntity entity, GridPosition movePos, int skillUid, TargetSelectParamContainer paramContainer)
        {
            GridPosition prePos = entity.GetLocation();
            entity.SetLocation(movePos, false);
            BattleErrorCode errorCode = CanCastSkill(entity, movePos, skillUid, paramContainer);
            entity.SetLocation(prePos, false);
            return errorCode;
        }

        public static BattleErrorCode CanCastSkill(IEntity entity, GridPosition movePos, int skillUid, TargetSelectParamContainer paramContainer)
        {
            Skill skill = entity.GetSkillBySkillUid(skillUid);
            BattleErrorCode errorCode = skill.CanCastSkill();
            if (errorCode != BattleErrorCode.Ok)
            {
                return errorCode;
            }
            int extraRange = GetSkillExtraStepRange(entity, skillUid);
            TargetSelectStepResult result = TargetSelectUtility.HandleSelect(entity.GetBattle(), skill.skillInfo.selectStep, entity, movePos, extraRange, paramContainer);
            errorCode = result.errorCode;
            result.Release();
            return errorCode;
        }

        public static void Cast(IEntity entity, int skillUid, CastSkillContext context, List<SkillCastResult> skillCastResultList)
        {
            IBattle battle = entity.GetBattle();
            SkillCastResult result = battle.FetchObj<SkillCastResult>();
            skillCastResultList.Add(result);
            Skill skill = entity.GetSkillBySkillUid(skillUid);
            skill.StartCoolTime();
            if (!skill.isCoolDown)
            {
                skill.LockCoolTime();
            }
            result.skillUid = skill.uid;
            result.skillRid = skill.rid;
            result.coolTime = skill.coolTimeLeft;
            result.originEntityUid = context.srcEntity.uid;
            result.originEntityRid = context.srcEntity.rid;
            result.combatTargetEntityUid = context.combatTargetEntityUid;
            context.skillBaseResult = result;
            context.procedureContext.valueSet.runningSkill = skill;
            context.procedureContext.valueSet.skillBaseResult = result;
            foreach (var wrap in context.stepResult.stepWrapList)
            {
                result.targetSelectInfoList.Add(wrap.selectInfo);
            }
            skill.RegisterPartItems();
            CastEffectGroup(battle, skill.skillInfo.effectList, context, result.effectResultList);
            skill.UnRegisterPartItems();
            context.procedureContext.valueSet.skillBaseResult = null;
            context.procedureContext.valueSet.runningSkill = null;
        }

        public static BuffTriggerResult CastByBuff(IEntity entity, Buff buff, int effectIndex, CastSkillContext context)
        {
            IBattle battle = entity.GetBattle();
            BuffInfo buffInfo = buff.GetBuffInfo();
            BuffEffectInfo effectInfo = buffInfo.effectList.GetValueSafely(effectIndex);
            var skillTriggerInfo = effectInfo as BuffEffectInfo_SkillTrigger;
            BuffTriggerResult result = battle.FetchObj<BuffTriggerResult>();
            result.buffUid = buff.buffUid;
            result.buffRid = buff.buffRid;
            result.originEntityUid = entity.uid;
            result.originEntityRid = entity.rid;
            result.effectIndex = effectIndex;
            context.skillBaseResult = result;
            context.procedureContext.valueSet.runningBuff = buff;
            context.procedureContext.valueSet.skillBaseResult = result;
            CastEffectGroup(battle, skillTriggerInfo.effectList, context, result.effectResultList);
            context.procedureContext.valueSet.runningBuff = null;
            context.procedureContext.valueSet.skillBaseResult = null;
            return result;
        }

        public static void CastEffectGroup(IBattle battle, List<SkillEffectInfo> effectInfoList, CastSkillContext context, List<BattleActionEffectResult> effectResultList)
        {
            for (int i = 0; i < effectInfoList.Count; ++i)
            {
                CastEffect(battle, i, effectInfoList[i], context, effectResultList);
            }
        }

        private static void CastEffect(IBattle battle, int id, SkillEffectInfo effectInfo, CastSkillContext context, List<BattleActionEffectResult> effectResultList)
        {
            var handler = BattleFactory.CreateSkillEffectHandler(battle, effectInfo.effectType);
            if (handler != null)
            {
                handler.effectType = effectInfo.effectType;
                var handleParams = new SkillEffectHandler.HandleParams()
                {
                    effectInfo = effectInfo,
                    castSkillContext = context,
                };
                handler.Handle(handleParams, id, effectResultList);
                handler.Release();
            }
        }
    }
}
