using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static class BattleValueCalculateUtility
    {
        public static FixedValue CalcDamage(IBattle battle, IEntity originEntity, IEntity targetEntity, BattleDamageInfo damageInfo, bool isCritical, FixedValue skillRate, BattleVagueParamValueSet valueSet)
        {
            EntityElementId elementId = damageInfo.elementType;
            if (elementId == EntityElementId.None)
            {
                elementId = originEntity.GetElementId();
            }
            return CalcDamage(battle, originEntity, targetEntity, elementId, damageInfo.argument, isCritical, damageInfo.damageType, skillRate, valueSet);
        }

        public static FixedValue CalcDamage(IBattle battle, IEntity originEntity, IEntity targetEntity, EntityElementId elementId, SkillEffectArgumentInfo argument, bool isCritical, SkillDamageType damageType, FixedValue skillRate, BattleVagueParamValueSet valueSet)
        {
            FixedValue skillDamage = CalcSkillDamageOrHealByAttribute(originEntity, targetEntity, argument, valueSet);
            FixedValue totalDamage = 0;
            if (skillDamage > 0)
            {
                FixedValue defence = 0;
                FixedValue attack = 0;
                FixedValue defendRateByDefend = 100;
                FixedValue damageRateByType1 = 100;
                FixedValue damageRateByType2 = 100;

                FixedValue elementDamageMult = FixedValue.one + battle.GetElementMultValue(elementId, targetEntity.GetElementId());
                if (damageType == SkillDamageType.Real)
                {
                    totalDamage = skillDamage;
                    damageRateByType1 += -targetEntity.GetAttributeValue(AttributeId.RealDamageRateAnti);
                }
                else
                {
                    FixedValue defendSubSrc = FixedValue.zero;
                    if (damageType == SkillDamageType.Physical)
                    {
                        defendSubSrc = originEntity.GetAttributeValue(AttributeId.PhysicalAttack);
                        defence = targetEntity.GetAttributeValue(AttributeId.PhysicalDefence) * (100 - originEntity.GetAttributeValue(AttributeId.PhysicalDefenceBreakRate)) / 100;
                        damageRateByType1 += originEntity.GetAttributeValue(AttributeId.PhysicalDamageRate) - targetEntity.GetAttributeValue(AttributeId.PhysicalDamageRateAnti);
                        damageRateByType2 += originEntity.GetAttributeValue(AttributeId.PhysicalDamageRate2) - targetEntity.GetAttributeValue(AttributeId.PhysicalDamageRate2Anti);
                    }
                    else if (damageType == SkillDamageType.Magical)
                    {
                        defendSubSrc = originEntity.GetAttributeValue(AttributeId.MagicalAttack);
                        defence = targetEntity.GetAttributeValue(AttributeId.MagicalDefence) * (100 - originEntity.GetAttributeValue(AttributeId.MagicalDefenceBreakRate)) / 100;
                        damageRateByType1 += originEntity.GetAttributeValue(AttributeId.MagicalDamageRate) - targetEntity.GetAttributeValue(AttributeId.MagicalDamageRateAnti);
                        damageRateByType2 += originEntity.GetAttributeValue(AttributeId.MagicalDamageRate2) - targetEntity.GetAttributeValue(AttributeId.MagicalDamageRate2Anti);
                    }
                    defendSubSrc *= argument.defendSubSrcRate / 100;
                    if (defendSubSrc > 0)
                    {
                        defendSubSrc = defendSubSrc * elementDamageMult;
                        defendRateByDefend = CutZero((defendSubSrc - defence) * 100 / defendSubSrc);
                    }
                }
                damageRateByType1 += originEntity.GetAttributeValue(AttributeId.AllDamageRate) - targetEntity.GetAttributeValue(AttributeId.AllDamageRateAnti);
                damageRateByType2 += originEntity.GetAttributeValue(AttributeId.AllDamageRate2) - targetEntity.GetAttributeValue(AttributeId.AllDamageRate2Anti);
                damageRateByType2 = CutZero(damageRateByType2);
                if (isCritical)
                {
                    var criticalDamageRate = (100 + originEntity.GetAttributeValue(AttributeId.CriticalDamageRate) - targetEntity.GetAttributeValue(AttributeId.CriticalDamageRateAnti)) / 100;
                    damageRateByType2 *= CutZero(criticalDamageRate);
                }
                damageRateByType1 = CutZero(damageRateByType1);
                damageRateByType2 = CutZero(damageRateByType2);
                totalDamage = (argument.baseValue + skillDamage * defendRateByDefend / 100) * damageRateByType1 * damageRateByType2 / 10000;
                totalDamage *= elementDamageMult;
                totalDamage *= skillRate / 100;
                if (totalDamage < 1)
                {
                    totalDamage = 1;
                }
            }
            return totalDamage;
        }

        public static FixedValue CalcHeal(IBattle battle, IEntity originEntity, IEntity targetEntity, BattleHealInfo healInfo, BattleVagueParamValueSet valueSet)
        {
            return CalcHeal(battle, originEntity, targetEntity, healInfo.argument, valueSet);
        }

        public static FixedValue CalcHeal(IBattle battle, IEntity originEntity, IEntity targetEntity, SkillEffectArgumentInfo argument, BattleVagueParamValueSet valueSet)
        {
            FixedValue skillHeal = CalcSkillDamageOrHealByAttribute(originEntity, targetEntity, argument, valueSet);
            FixedValue totalHeal = 0;
            if (skillHeal > 0)
            {
                totalHeal = skillHeal;
                totalHeal *= (100 + originEntity.GetAttributeValue(AttributeId.HealRate)) / 100;
                if (totalHeal < 1)
                {
                    totalHeal = 1;
                }
            }
            return totalHeal;
        }

        public static FixedValue CalcSkillDamageOrHealByAttribute(IEntity originEntity, IEntity targetEntity, SkillEffectArgumentInfo argument, BattleVagueParamValueSet valueSet)
        {
            FixedValue damage = 0;
            damage += originEntity.GetAttributeValue(AttributeId.PhysicalAttack) * argument.physicalAttackRate / 100;
            damage += originEntity.GetAttributeValue(AttributeId.MagicalAttack) * argument.magicalAttackRate / 100;
            damage += targetEntity.GetCurHp() * argument.curTargetHpRate / 100;
            damage += targetEntity.GetAttributeValue(AttributeId.HpMax) * argument.maxTargetHpRate / 100;
            return damage;
        }

        private static FixedValue CutZero(FixedValue value)
        {
            return FixedValueMath.Max(FixedValue.zero, value);
        }
    }
}
