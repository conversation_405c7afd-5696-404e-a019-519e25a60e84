using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleStageActionInfo_TurnDir_Actor_Uid_Dir : BattleStageActionInfo_TurnDir
    {
        public List<int> actorUidList = new List<int>();
        public GridDirType dir;
        
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.TurnDir_Actor_Uid_Dir; }
        }
    }
}
