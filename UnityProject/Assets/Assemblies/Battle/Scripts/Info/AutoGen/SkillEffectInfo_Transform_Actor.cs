using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_Transform_Actor : SkillEffectInfo
    {
        public BattleArgumentInfo_Entity target;
        public int actorRid;
        public int lifeTime;
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Transform_Actor; }
        }
    }
}
