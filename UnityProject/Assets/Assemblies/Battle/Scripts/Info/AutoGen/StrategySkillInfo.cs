using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class StrategySkillInfo
    {
        public int id;
        public string name;
        public string iconName;
        public SkillIndicatorType indicatorType;
        public int coolTime;
        public List<BattleArgumentInfo_Condition> conditionList = new List<BattleArgumentInfo_Condition>();
        public BattleTriggerInfo trigger;
        public TargetSelectStepInfo selectStep;
        public List<SkillEffectInfo> effectList = new List<SkillEffectInfo>();
        public int mainEffectIndex;
    }
}
