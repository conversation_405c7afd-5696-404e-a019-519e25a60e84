using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleCampRefereeSituationInfo_TurnEnd : BattleCampRefereeSituationInfo
    {
        public int turnIndex;
        
        public override BattleCampRefereeSituationType refereeType
        {
            get { return BattleCampRefereeSituationType.TurnEnd; }
        }
    }
}
