using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_AttachBuff_Rid : SkillEffectInfo
    {
        public BattleArgumentInfo_Entity target;
        public List<BattleAttachBuffItemInfo> itemList = new List<BattleAttachBuffItemInfo>();
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.AttachBuff_Rid; }
        }
    }
}
