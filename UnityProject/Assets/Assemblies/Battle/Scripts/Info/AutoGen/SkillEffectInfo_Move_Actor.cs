using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_Move_Actor : SkillEffectInfo_SingleTarget
    {
        public int distance;
        public List<SkillEffectInfo> bumpEffectList = new List<SkillEffectInfo>();
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Move_Actor; }
        }
    }
}
