using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleStageActionInfo_Move_Actor_Uid : BattleStageActionInfo
    {
        public int actorUid;
        public GridPosition movePos;
        public bool moveBack;
        
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.Move_Actor_Uid; }
        }
    }
}
