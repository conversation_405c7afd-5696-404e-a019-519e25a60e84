using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Value_EntityCountInRange_Self : BattleArgumentInfo_Value
    {
        public TargetSelectRangeId rangeId;
        public BattleObjCheckInfo_Entity entityCheck;
        
        public override BattleArgumentValueFuncType funcType
        {
            get { return BattleArgumentValueFuncType.EntityCountInRange_Self; }
        }
    }
}
