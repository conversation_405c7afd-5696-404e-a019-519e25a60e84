using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_Heal_Default : SkillEffectInfo
    {
        public BattleArgumentInfo_Entity target;
        public BattleHealInfo heal;
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Heal_Default; }
        }
    }
}
