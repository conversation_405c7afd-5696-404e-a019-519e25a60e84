using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleObjCheckInfo_Entity_Rid : BattleObjCheckInfo_Entity
    {
        public List<int> ridList = new List<int>();
        
        public override BattleObjCheckEntityType checkType
        {
            get { return BattleObjCheckEntityType.Rid; }
        }
    }
}
