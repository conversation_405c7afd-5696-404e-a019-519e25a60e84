using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleAchievementInfo_WinBeforeTurn : BattleAchievementInfo
    {
        public int turnIndex;
        
        public override BattleAchievementType achievementType
        {
            get { return BattleAchievementType.WinBeforeTurn; }
        }
    }
}
