using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_DetachBuff_Rid : SkillEffectInfo
    {
        public BattleArgumentInfo_Entity target;
        public List<BattleDetachBuffItemInfo> itemList = new List<BattleDetachBuffItemInfo>();
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.DetachBuff_Rid; }
        }
    }
}
