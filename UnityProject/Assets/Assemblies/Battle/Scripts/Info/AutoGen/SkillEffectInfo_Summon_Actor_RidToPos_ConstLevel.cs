using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_Summon_Actor_RidToPos_ConstLevel : SkillEffectInfo
    {
        public BattleArgumentInfo_Grid grid;
        public int rid;
        public int level;
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.Summon_Actor_RidToPos_ConstLevel; }
        }
    }
}
