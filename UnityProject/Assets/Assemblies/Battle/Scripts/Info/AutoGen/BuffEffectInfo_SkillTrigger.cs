using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BuffEffectInfo_SkillTrigger : BuffEffectInfo
    {
        public BuffTriggerInfo trigger;
        public BattleTriggerLimitInfo limit;
        public bool costLevel;
        public List<SkillEffectInfo> effectList = new List<SkillEffectInfo>();
        public string dramaName;
        
        public override BuffEffectType effectType
        {
            get { return BuffEffectType.SkillTrigger; }
        }
    }
}
