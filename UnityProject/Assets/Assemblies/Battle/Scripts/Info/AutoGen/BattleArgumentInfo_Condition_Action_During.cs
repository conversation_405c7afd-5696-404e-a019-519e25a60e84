using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Condition_Action_During : BattleArgumentInfo_Condition
    {
        public BattleArgumentInfo_Entity subject;
        public bool isActive;
        public bool isSkill;
        
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Action_During; }
        }
    }
}
