using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Condition_Check_GlobalVar : BattleArgumentInfo_Condition
    {
        public int id;
        public bool boolValue;
        
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Check_GlobalVar; }
        }
    }
}
