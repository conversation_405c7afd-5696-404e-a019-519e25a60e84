using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleCampRefereeSituationInfo_AnyActorOccupyAnyGrid2 : BattleCampRefereeSituationInfo
    {
        public List<int> actorUidList = new List<int>();
        public GridPosition startPos;
        public GridPosition endPos;
        
        public override BattleCampRefereeSituationType refereeType
        {
            get { return BattleCampRefereeSituationType.AnyActorOccupyAnyGrid2; }
        }
    }
}
