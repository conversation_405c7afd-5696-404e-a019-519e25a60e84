using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_AttachBuff_RandomCustom : SkillEffectInfo
    {
        public BattleArgumentInfo_Entity target;
        public List<BattleAttachBuffItemInfo_Random> itemList = new List<BattleAttachBuffItemInfo_Random>();
        public int count;
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.AttachBuff_RandomCustom; }
        }
    }
}
