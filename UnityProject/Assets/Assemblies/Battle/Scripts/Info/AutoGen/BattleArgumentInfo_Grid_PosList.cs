using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Grid_PosList : BattleArgumentInfo_Grid
    {
        public List<GridPosition> pos = new List<GridPosition>();
        
        public override BattleArgumentGridFuncType funcType
        {
            get { return BattleArgumentGridFuncType.PosList; }
        }
    }
}
