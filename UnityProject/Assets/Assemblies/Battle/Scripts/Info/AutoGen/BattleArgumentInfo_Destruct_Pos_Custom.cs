using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Destruct_Pos_Custom : BattleArgumentInfo_Entity
    {
        public BattleArgumentInfo_Grid centerGrid;
        
        public override BattleArgumentEntityFuncType funcType
        {
            get { return BattleArgumentEntityFuncType.Destruct_Pos_Custom; }
        }
    }
}
