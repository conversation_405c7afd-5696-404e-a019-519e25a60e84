using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleStageActionInfo_AttachBuff_Actor_Uid : BattleStageActionInfo_AttachBuff_Default
    {
        public List<int> actorUidList = new List<int>();
        
        public override BattleStageActionType actionType
        {
            get { return BattleStageActionType.AttachBuff_Actor_Uid; }
        }
    }
}
