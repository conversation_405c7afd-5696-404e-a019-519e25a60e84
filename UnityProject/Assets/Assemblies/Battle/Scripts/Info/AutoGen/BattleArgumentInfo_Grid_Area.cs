using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Grid_Area : BattleArgumentInfo_Grid
    {
        public GridPosition startPos;
        public GridPosition endPos;
        
        public override BattleArgumentGridFuncType funcType
        {
            get { return BattleArgumentGridFuncType.Area; }
        }
    }
}
