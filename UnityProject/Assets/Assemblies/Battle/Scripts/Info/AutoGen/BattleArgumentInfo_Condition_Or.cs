using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Condition_Or : BattleArgumentInfo_Condition
    {
        public List<BattleArgumentInfo_Condition> conditionList = new List<BattleArgumentInfo_Condition>();
        
        public override BattleArgumentConditionFuncType funcType
        {
            get { return BattleArgumentConditionFuncType.Or; }
        }
    }
}
