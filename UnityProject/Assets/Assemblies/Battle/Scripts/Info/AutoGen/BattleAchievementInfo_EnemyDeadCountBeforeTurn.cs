using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleAchievementInfo_EnemyDeadCountBeforeTurn : BattleAchievementInfo
    {
        public int teamUid;
        public int turnIndex;
        public int killCount;
        
        public override BattleAchievementType achievementType
        {
            get { return BattleAchievementType.EnemyDeadCountBeforeTurn; }
        }
    }
}
