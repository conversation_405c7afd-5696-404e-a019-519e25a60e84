using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class SkillEffectInfo_AttachBuff_RandomRid : SkillEffectInfo
    {
        public BattleArgumentInfo_Entity target;
        public int buffBatchRid;
        public int count;
        
        public override SkillEffectFuncType effectType
        {
            get { return SkillEffectFuncType.AttachBuff_RandomRid; }
        }
    }
}
