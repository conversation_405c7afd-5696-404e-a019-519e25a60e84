using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public partial class BattleArgumentInfo_Value_TeamEnergy : BattleArgumentInfo_Value
    {
        public BattleArgumentInfo_Team team;
        
        public override BattleArgumentValueFuncType funcType
        {
            get { return BattleArgumentValueFuncType.TeamEnergy; }
        }
    }
}
