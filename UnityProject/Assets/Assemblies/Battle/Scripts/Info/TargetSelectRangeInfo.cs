using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class  TargetSelectRangeInfo
    {
        public TargetSelectRangeId id;
        public TargetSelectRangeId preId;
        public TargetSelectRangeId nextId;
        public TargetSelectRangeType rangeType;
        public int min;
        public int max;
        public int offsetForward;
        public string range;
        public string area;
    }
}
