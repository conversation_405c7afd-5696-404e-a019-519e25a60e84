using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleFieldSummaryOfTerrainRid : BattleFieldSummaryStruct<int>, IBattleSnapshot
    {
        public void Init(BattleStageInfo stageInfo)
        {
            BattleGroundInfo groundInfo = stageInfo.groundInfo;
            for (int i = 0; i < groundInfo.terreinRidList.Count; ++i)
            {
                int posX = i / groundInfo.height;
                int posY = i % groundInfo.height;
                var pos = new GridPosition(posX, posY);
                if (!pos.InArea(m_startPos, m_endPos))
                {
                    continue;
                }
                SetValue(pos, groundInfo.terreinRidList[i], false);
            }
            SetAllDirtyFlag(false);
        }

        public void BuildSnapshot(ByteBufferBuilder builder)
        {
            for (int i = 0; i < m_size; ++i)
            {
                builder.WriteByte(m_valueTable[i]);
            }
        }

        public void LoadSnapshot(ByteBufferLoader loader)
        {
            for (int i = 0; i < m_size; ++i)
            {
                m_valueTable[i] = loader.ReadByte();
            }
            SetAllDirtyFlag(false);
        }
    }
}
