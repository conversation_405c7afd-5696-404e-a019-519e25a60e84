using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleFieldSummaryOfEntityLocatable : BattleFieldSummaryStruct<bool>
    {
        private IEntity m_entity;
        private BattleFieldSummaryOfOccupyEntity m_fieldSummaryOfOccupyEntity;
        private BattleFieldSummaryOfRuleLocatable m_fieldSummaryOfRuleLocatable;

        public override void OnRelease()
        {
            m_entity = default;
            m_fieldSummaryOfOccupyEntity = default;
            m_fieldSummaryOfRuleLocatable = default;
            base.OnRelease();
        }

        public void SetEntity(IEntity entity)
        {
            m_entity = entity;
        }

        public void SetFieldSummaryOfOccupyEntity(BattleFieldSummaryOfOccupyEntity fieldSummaryOfOccupyEntity)
        {
            if (m_fieldSummaryOfOccupyEntity != null)
            {
                m_fieldSummaryOfOccupyEntity.UnRegisterDependSummary(this);
            }
            m_fieldSummaryOfOccupyEntity = fieldSummaryOfOccupyEntity;
            m_fieldSummaryOfOccupyEntity.RegisterDependSummary(this);
        }

        public void SetFieldSummaryOfRuleLocatable(BattleFieldSummaryOfRuleLocatable fieldSummaryOfRuleLocatable)
        {
            if (m_fieldSummaryOfRuleLocatable != null)
            {
                m_fieldSummaryOfRuleLocatable.UnRegisterDependSummary(this);
            }
            m_fieldSummaryOfRuleLocatable = fieldSummaryOfRuleLocatable;
            m_fieldSummaryOfRuleLocatable.RegisterDependSummary(this);
        }

        protected override void OnUnInit()
        {
            m_fieldSummaryOfRuleLocatable.UnRegisterDependSummary(this);
            m_fieldSummaryOfOccupyEntity.UnRegisterDependSummary(this);
            base.OnUnInit();
        }

        protected override bool OnGetUpdateValue(GridPosition pos)
        {
            return BattleUtility.CheckEntityCanLocate(m_battle, m_entity, m_fieldSummaryOfRuleLocatable, pos);
        }
    }
}
