using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleFieldSummaryList<T> : BattleFieldSummary
    {
        protected List<T>[] m_listTable;
        protected static readonly List<T> m_emptyList = new List<T>();

        public override void OnRelease()
        {
            //m_listTableֻ��Ҫ��վ���
            if (m_listTable != null)
            {
                for (int i = 0; i < m_listTable.Length; ++i)
                {
                    if(m_listTable[i] != null)
                    {
                        m_listTable[i].Clear();
                    }
                }
            }
            base.OnRelease();
        }

        public override void Reset()
        {
            if (m_listTable != null)
            {
                for (int i = 0; i < m_listTable.Length; ++i)
                {
                    if (m_listTable[i] != null)
                    {
                        m_listTable[i].Clear();
                    }
                }
            }
            base.Reset();
        }

        public void AddValue(GridPosition pos, T value, bool setDependDirty)
        {
            int index = GetIndex(pos);
            AddValue(index, value, setDependDirty);
        }

        public void AddValue(int index, T value, bool setDependDirty)
        {
            if (m_listTable[index] == null)
            {
                m_listTable[index] = new List<T>();
            }
            m_listTable[index].Add(value);
            if (setDependDirty)
            {
                SetDependSummaryDirty(index);
            }
        }

        public void RemoveValue(GridPosition pos, T value, bool setDependDirty)
        {
            int index = GetIndex(pos);
            RemoveValue(index, value, setDependDirty);
        }

        public void RemoveValue(int index, T value, bool setDependDirty)
        {
            if (m_listTable[index] == null)
            {
                m_listTable[index] = new List<T>();
            }
            if (m_listTable[index].Remove(value))
            {
                if (setDependDirty)
                {
                    SetDependSummaryDirty(index);
                }
            }
        }

        public List<T> GetList(GridPosition pos)
        {
            int index = GetIndex(pos);
            List<T> list = m_listTable[index];
            if (list == null)
            {
                list = new List<T>();
                m_listTable[index] = list;
            }
            if (m_dirtyTable[index])
            {
                m_dirtyTable[index] = false;
                OnGetUpdateValue(pos, list);
            }
            return list;
        }

        protected override void IncreaseCapacity(int size)
        {
            m_listTable = new List<T>[size];
            base.IncreaseCapacity(size);
        }

        public override void CopyFrom(BattleFieldSummary baseSummary)
        {
            base.CopyFrom(baseSummary);
            var summary = baseSummary as BattleFieldSummaryList<T>;
            if (m_listTable == null || m_listTable.Length < m_size)
            {
                m_listTable = new List<T>[m_size];
            }
            for (int i = 0; i < m_size; ++i)
            {
                if (m_listTable[i] == null)
                {
                    if (summary.m_listTable[i] != null)
                    {
                        m_listTable[i] = new List<T>();
                    }
                }
                else
                {
                    m_listTable[i].Clear();
                }
                if (summary.m_listTable[i] != null)
                {
                    foreach (var item in summary.m_listTable[i])
                    {
                        m_listTable[i].Add(GetValueWhenCopy(item));
                    }
                }
            }
        }

        protected virtual void OnGetUpdateValue(GridPosition pos, List<T> list) { }
        protected virtual T GetValueWhenCopy(T value) { return value; }
    }
}
