using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleFieldSummary : BattleObj
    {
        private List<BattleFieldSummary> m_dependSummaryList = new List<BattleFieldSummary>();
        protected bool[] m_dirtyTable;
        protected GridPosition m_startPos;
        protected GridPosition m_endPos;
        protected int m_width;
        protected int m_height;
        protected int m_size;

        public int width
        {
            get { return m_width; }
        }

        public int height
        {
            get { return m_height; }
        }

        public GridPosition startPos
        {
            get { return m_startPos; }
        }

        public GridPosition endPos
        {
            get { return m_endPos; }
        }

        public override void OnRelease()
        {
            //m_size 不用改
            //m_width 不用改
            //m_height 不用改
            //m_dirtyTable下面函数处理
            SetAllDirtyFlag(true);
            m_dependSummaryList.Clear();
            base.OnRelease();
        }

        public void UnInit()
        {
            OnUnInit();
        }

        public virtual void Reset()
        {
            SetAllDirtyFlag(true);
        }

        public void SetAllDirtyFlag(bool flag)
        {
            if (m_dirtyTable != null)
            {
                for (int i = 0; i < m_size; ++i)
                {
                    m_dirtyTable[i] = flag;
                }
            }
        }

        public void InitSize(BattleGroundInfo groundInfo, GridPosition startPos, GridPosition endPos)
        {
            m_startPos = GridPosition.zero;
            m_endPos = new GridPosition(groundInfo.width - 1, groundInfo.height - 1);
            if (startPos != GridPosition.invalid && endPos != GridPosition.invalid && startPos != endPos)
            {
                m_startPos = new GridPosition(Math.Min(startPos.x, endPos.x), Math.Min(startPos.y, endPos.y));
                m_endPos = new GridPosition(Math.Max(startPos.x, endPos.x), Math.Max(startPos.y, endPos.y));

                m_startPos = new GridPosition(Math.Max(0, m_startPos.x), Math.Max(0, m_startPos.y));
                m_endPos = new GridPosition(Math.Min(groundInfo.width - 1, m_endPos.x), Math.Min(groundInfo.height - 1, m_endPos.y));
            }
            m_width = m_endPos.x - m_startPos.x + 1;
            m_height = m_endPos.y - m_startPos.y + 1;
            int size = m_width * m_height;
            if (size > m_size)
            {
                IncreaseCapacity(size);
            }
            m_size = size;
            Reset();
        }

        protected virtual void IncreaseCapacity(int size)
        {
            m_dirtyTable = new bool[size];
        }

        protected virtual void OnUnInit() { }

        public bool CheckPos(GridPosition pos)
        {
            return pos.InArea(m_startPos, m_endPos);
        }

        public bool CheckDirty(GridPosition pos)
        {
            return m_dirtyTable[GetIndex(pos)];
        }

        public bool CheckDirty(int index)
        {
            return m_dirtyTable[index];
        }

        public void SetDirty(GridPosition pos)
        {
            m_dirtyTable[GetIndex(pos)] = true;
            SetDependSummaryDirty(pos);
        }

        public void SetDirty(int index)
        {
            m_dirtyTable[index] = true;
            SetDependSummaryDirty(index);
        }

        public void SetDependSummaryDirty(GridPosition pos)
        {
            foreach (var summary in m_dependSummaryList)
            {
                summary.SetDirty(pos);
            }
        }

        protected int GetIndex(GridPosition pos)
        {
            if (CheckPos(pos))
            {
                var p = pos - m_startPos;
                return p.x + p.y * m_width;
            }
            throw new Exception($"GetIndex Out of Range {pos}");
        }

        protected void SetDependSummaryDirty(int index)
        {
            foreach (var summary in m_dependSummaryList)
            {
                summary.SetDirty(index);
            }
        }

        protected void ResetDirty(GridPosition pos)
        {
            m_dirtyTable[GetIndex(pos)] = false;
        }

        public void RegisterDependSummary(BattleFieldSummary summary)
        {
            m_dependSummaryList.Add(summary);
        }


        public void UnRegisterDependSummary(BattleFieldSummary summary)
        {
            m_dependSummaryList.Remove(summary);
        }

        public virtual void CopyFrom(BattleFieldSummary baseSummary)
        {
            m_size = baseSummary.m_size;
            m_width = baseSummary.m_width;
            m_height = baseSummary.m_height;
            m_startPos = baseSummary.m_startPos;
            m_endPos = baseSummary.m_endPos;
            m_dirtyTable = new bool[m_size];
            for (int i = 0; i < m_size; ++i)
            {
                m_dirtyTable[i] = baseSummary.m_dirtyTable[i];
            }
        }
    }
}
