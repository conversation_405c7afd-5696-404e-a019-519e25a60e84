using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public abstract class EntityObj : BattleObj
    {
        protected IEntity m_entity;

        public IEntity entity
        {
            get { return m_entity; }
        }

        public override void OnRelease()
        {
            m_entity = null;
            base.OnRelease();
        }

        public void InitEntityObj(IEntity entity)
        {
            m_entity = entity;
        }

        public override object FetchObj(Type type)
        {
            return m_entity.FetchObj(type);
        }
    }
}