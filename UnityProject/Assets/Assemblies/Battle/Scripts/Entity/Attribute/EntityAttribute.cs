using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityAttribute : EntityObj, IBattleSnapshot, IDebugTagNodeContainer, ICheckSame<EntityAttribute>
    {
        public AttributeId attributeId;

        private bool m_isDirty;
        private FixedValue m_result;
        private EntityAttributeInfo m_attributeInfo;
        private EntityAttributePart[] m_partArray = new EntityAttributePart[BattleUtility.MaxAttributePartCountInAttribute];

        private DebugTagNode m_debugTagNode;

        public EntityAttributePart[] partArray
        {
            get { return m_partArray; }
        }

        public override void OnRelease()
        {
            m_isDirty = default;
            attributeId = default;
            m_attributeInfo = null;
            m_result = default;
            m_partArray.ReleaseAll();
            m_debugTagNode.Release();
            m_debugTagNode = null;
            base.OnRelease();
        }

        public EntityAttributePart AddInitPartItem(FixedValue value, AttributePartId partId)
        {
            var part = GetPart(partId, true);
            var item = m_battle.FetchObj<EntityAttributePartItem>();
            item.Init(part);
            item.value = value;
            part.AddItem(item);
            m_isDirty = true;
            return part;
        }

        public EntityAttributePart AddPartItem(AttributePartId partId, IEntityAttributePartItem item)
        {
            var part = GetPart(partId, true);
            part.AddItem(item);
            m_isDirty = true;
            return part;
        }

        public bool RemovePartItem(AttributePartId partId, IEntityAttributePartItem item)
        {
            var part = GetPart(partId, false);
            if (part != null)
            {
                if (part.RemoveItem(item))
                {
                    m_isDirty = true;
                    return true;
                }
            }
            return false;
        }

        public EntityAttributePart GetPart(AttributePartId partId, bool autoCreate)
        {
            if (partId == AttributePartId.None)
            {
                return null;
            }
            int index = BattleUtility.GetAttributePartIdIndex(partId);
            var part = m_partArray[index];
            if (part == null && autoCreate)
            {
                part = m_battle.FetchObj<EntityAttributePart>();
                part.Init(this, partId);
                m_partArray[index] = part;
            }
            return part;
        }

        public void SetDirty()
        {
            m_isDirty = true;
        }

        public FixedValue GetValue(bool ignoreDirty)
        {
            if (ignoreDirty)
            {
                return CalcValueInternal(ignoreDirty);
            }
            if (m_isDirty)
            {
                m_isDirty = false;
                m_result = CalcValueInternal(ignoreDirty);
            }
            return m_result;
        }

        public FixedValue GetPartValue(AttributePartId partId, bool ignoreDirty)
        {
            if (partId == AttributePartId.None)
            {
                return FixedValue.zero;
            }
            var part = m_partArray[BattleUtility.GetAttributePartIdIndex(partId)];
            if (part != null)
            {
                return part.GetValue(ignoreDirty);
            }
            return FixedValue.zero;
        }

        private FixedValue CalcValueInternal(bool ignoreDirty)
        {
            var result = EntityAttributeCalcUtiltiy.Calc(this, ignoreDirty);
            if (m_attributeInfo == null)
            {
                m_attributeInfo = m_battle.infoGetter.GetAttributeInfo((int)attributeId);
            }
            if (m_attributeInfo.hasMax)
            {
                result = FixedValueMath.Min(m_attributeInfo.max, result);
            }
            if (m_attributeInfo.hasMin)
            {
                result = FixedValueMath.Max(m_attributeInfo.min, result);
            }
            return result;
        }

        public EntityAttribute Copy(IFetchable fetchable)
        {
            EntityAttribute info = fetchable.FetchObj<EntityAttribute>();
            info.CopyForm(this);
            return info;
        }

        public void CopyForm(EntityAttribute attribute)
        {
            attributeId = attribute.attributeId;
            m_isDirty = attribute.m_isDirty;
            m_result = attribute.m_result;
            m_attributeInfo = attribute.m_attributeInfo;
            m_battle.ReleaseAll(m_partArray);
            int length = attribute.m_partArray.Length;
            for (int i = 0; i < length; ++i)
            {
                var part = attribute.m_partArray[i];
                if (part == null)
                {
                    continue;
                }
                var newPart = m_entity.FetchObj<EntityAttributePart>();
                newPart.Init(this, part.partId);
                newPart.CopyFrom(part);
                m_partArray[i] = newPart;
            }
        }

        public DebugTagNode GetDebugTagNode()
        {
            if (m_debugTagNode == null)
            {
                m_debugTagNode = DebugTagNode.Create(m_battle, m_entity.attributeComponent.GetDebugTagNode());
                m_debugTagNode.tag.Set("[{0}:{1}]", GetType().Name, attributeId);
            }
            return m_debugTagNode;
        }

        public bool CheckSame(EntityAttribute baseOne)
        {
            bool result = true;
            result &= BattleUtility.CheckSameClassListOrLogout(m_battle, this, m_partArray, baseOne.m_partArray, "m_partArray");
            return result;
        }

        public void BuildSnapshot(ByteBufferBuilder builder)
        {
            //m_attributeId����Ҫ�ӽ�ȥ����̬��ֵ
            //m_dependInfoList����Ҫ�ӽ�ȥ����̬��ֵ
            //builder.WriteInt(FixedValue.Export(m_result));
            //builder.WriteInt(FixedValue.Export(m_resultExceptDepend));
            //builder.WriteByte(m_partList.Count);
            //for(int i = 0; i < m_partList.Count; ++i)
            //{
            //    var part = m_partList[i];
            //    builder.WriteByte((byte)part.partType);
            //    m_partList[i].BuildSnapshot(builder);
            //}
        }

        public void LoadSnapshot(ByteBufferLoader loader)
        {
            //m_attributeId����Ҫ�ӽ�ȥ����̬��ֵ
            //m_dependInfoList����Ҫ�ӽ�ȥ����̬��ֵ
            //m_result = FixedValue.Import(loader.ReadInt());
            //m_resultExceptDepend = FixedValue.Import(loader.ReadInt());
            //byte partListCount = loader.ReadByte();
            //for (int i = 0; i < partListCount; ++i)
            //{
            //    EntityAttributePartType partType = (EntityAttributePartType)loader.ReadByte();
            //    var part = CreatePart(partType);
            //    part.LoadSnapshot(loader);
            //}
        }
    }
}
