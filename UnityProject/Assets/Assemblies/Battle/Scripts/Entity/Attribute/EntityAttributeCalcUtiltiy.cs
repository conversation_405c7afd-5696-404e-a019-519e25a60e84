using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static class EntityAttributeCalcUtiltiy
    {
        private static AttributePartId[][] m_calculatePartIdArrays = new AttributePartId[BattleDefine.AttributeIdCount][];

        static EntityAttributeCalcUtiltiy()
        {
            m_calculatePartIdArrays[(int)AttributeId.HpMax] = new AttributePartId[] {
                AttributePartId.HpMaxBase,
                AttributePartId.HpMaxBaseGrowth,
                AttributePartId.HpMaxBaseMult,
                AttributePartId.HpMaxAdd,
                AttributePartId.HpMaxMult,
                AttributePartId.HpMaxMultLevel,
                AttributePartId.None,
                AttributePartId.HpMaxMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.ShieldHpMax] = new AttributePartId[] {
                AttributePartId.ShieldHpMaxBase,
                AttributePartId.ShieldHpMaxBaseGrowth,
                AttributePartId.ShieldHpMaxBaseMult,
                AttributePartId.ShieldHpMaxAdd,
                AttributePartId.ShieldHpMaxMult,
                AttributePartId.ShieldHpMaxMultLevel,
                AttributePartId.None,
                AttributePartId.ShieldHpMaxMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.PhysicalAttack] = new AttributePartId[] {
                AttributePartId.PhysicalAttackBase,
                AttributePartId.PhysicalAttackBaseGrowth,
                AttributePartId.PhysicalAttackBaseMult,
                AttributePartId.PhysicalAttackAdd,
                AttributePartId.PhysicalAttackMult,
                AttributePartId.PhysicalAttackMultLevel,
                AttributePartId.PhysicalAttackAddExtra,
                AttributePartId.PhysicalAttackMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.MagicalAttack] = new AttributePartId[] {
                AttributePartId.MagicalAttackBase,
                AttributePartId.MagicalAttackBaseGrowth,
                AttributePartId.MagicalAttackBaseMult,
                AttributePartId.MagicalAttackAdd,
                AttributePartId.MagicalAttackMult,
                AttributePartId.MagicalAttackMultLevel,
                AttributePartId.MagicalAttackAddExtra,
                AttributePartId.MagicalAttackMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.PhysicalDefence] = new AttributePartId[] {
                AttributePartId.PhysicalDefenceBase,
                AttributePartId.PhysicalDefenceBaseGrowth,
                AttributePartId.PhysicalDefenceBaseMult,
                AttributePartId.PhysicalDefenceAdd,
                AttributePartId.PhysicalDefenceMult,
                AttributePartId.PhysicalDefenceMultLevel,
                AttributePartId.PhysicalDefenceAddExtra,
                AttributePartId.PhysicalDefenceMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.MagicalDefence] = new AttributePartId[] {
                AttributePartId.MagicalDefenceBase,
                AttributePartId.MagicalDefenceBaseGrowth,
                AttributePartId.MagicalDefenceBaseMult,
                AttributePartId.MagicalDefenceAdd,
                AttributePartId.MagicalDefenceMult,
                AttributePartId.MagicalDefenceMultLevel,
                AttributePartId.MagicalDefenceAddExtra,
                AttributePartId.MagicalDefenceMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.Tech] = new AttributePartId[] {
                AttributePartId.TechBase,
                AttributePartId.TechBaseGrowth,
                AttributePartId.TechBaseMult,
                AttributePartId.TechAdd,
                AttributePartId.TechMult,
                AttributePartId.TechMultLevel,
                AttributePartId.TechAddExtra,
                AttributePartId.TechMultExtra };
            m_calculatePartIdArrays[(int)AttributeId.Speed] = new AttributePartId[] {
                AttributePartId.SpeedBase,
                AttributePartId.SpeedBaseGrowth,
                AttributePartId.SpeedBaseMult,
                AttributePartId.SpeedAdd,
                AttributePartId.SpeedMult,
                AttributePartId.SpeedMultLevel,
                AttributePartId.SpeedAddExtra,
                AttributePartId.SpeedMultExtra };
        }

        public static FixedValue Calc(EntityAttribute attribute, bool ignoreDirty)
        {
            var attributeId = attribute.attributeId;
            var calculatePartIdArray = m_calculatePartIdArrays[(int)attributeId];
            if (calculatePartIdArray != null)
            {
                return CalcBasicValue(attribute, calculatePartIdArray, ignoreDirty);
            }
            var attributePartArray = BattleUtility.GetPartArrayOfAttribute(attributeId);
            return CalcValueAdd(attribute, attributePartArray, ignoreDirty);
        }

        private static FixedValue CalcBasicValue(EntityAttribute attribute, AttributePartId[] partIds, bool ignoreDirty)
        {
            FixedValue baseValue = attribute.GetPartValue(partIds[0], ignoreDirty);
            FixedValue baseGrowthValue = attribute.GetPartValue(partIds[1], ignoreDirty);
            FixedValue baseMultValue = attribute.GetPartValue(partIds[2], ignoreDirty);
            FixedValue addValue = attribute.GetPartValue(partIds[3], ignoreDirty);
            FixedValue multValue = attribute.GetPartValue(partIds[4], ignoreDirty);
            FixedValue multLevelValue = 100;// attribute.GetPartValue(partIds[5], ignoreDirty);
            FixedValue addExtraValue = attribute.GetPartValue(partIds[6], ignoreDirty);
            FixedValue multExtraValue = attribute.GetPartValue(partIds[7], ignoreDirty);
            int level = attribute.entity.GetCurLevel();
            //(((baseValue + A1) * M1 + A2) * M2 + A3) * M3 + A4) * M4 + A5) * M5
            //A1:baseGrowthValue * level
            //M1:baseMultValue
            //M2:multValue
            //A3:addValue
            //M3:multLevelValue
            //M4:multExtraValue
            //A5:addExtraValue
            return ((baseValue + baseGrowthValue * level) * (100 + baseMultValue) / 100 * (100 + multValue) / 100 + addValue) * multLevelValue / 100 * (100 + multExtraValue) / 100 + addExtraValue;
        }

        private static FixedValue CalcValueAdd(EntityAttribute attribute, AttributePartId[] partIdArray, bool ignoreDirty)
        {
            FixedValue value = FixedValue.zero;
            for (int i = 0; i < partIdArray.Length; ++i)
            {
                value += attribute.GetPartValue(partIdArray[i], ignoreDirty);
            }
            return value;
        }
    }
}
