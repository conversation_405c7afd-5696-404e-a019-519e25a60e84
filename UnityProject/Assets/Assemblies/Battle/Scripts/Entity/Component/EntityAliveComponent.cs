using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityAliveComponent : EntityComponent
    {
        private bool m_isDying;
        private bool m_isDead;
        private bool m_canResurrect;

        public override void OnRelease()
        {
            m_isDying = false;
            m_isDead = false;
            m_canResurrect = false;
            base.OnRelease();
        }

        protected override void OnInitBasic()
        {
            base.OnInitBasic();
        }

        public bool IsAlive()
        {
            return !m_isDying && !m_isDead;
        }

        public bool IsDying()
        {
            return m_isDying;
        }

        public void SetDying()
        {
            m_isDying = true;
            m_isDead = false;
        }

        public bool IsDead()
        {
            return m_isDead;
        }

        public void SetDead()
        {
            m_isDying = false;
            m_isDead = true;
        }

        public bool CanResurrect()
        {
            //if (m_entity.buffComponent != null)
            //{
            //    return true;
            //}
            return m_canResurrect;
        }

        public void Resurrect()
        {
            m_isDying = false;
            m_isDead = false;
        }

        public override bool CheckSame(EntityComponent baseComponent)
        {
            bool result = true;
            var component = baseComponent as EntityAliveComponent;
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_isDying, component.m_isDying, "m_isDying");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_isDead, component.m_isDead, "m_isDead");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_canResurrect, component.m_canResurrect, "m_canResurrect");
            return result;
        }

        public override void CopyFrom(EntityComponent baseComponent)
        {
            var component = baseComponent as EntityAliveComponent;
            m_isDying = component.m_isDying;
            m_isDead = component.m_isDead;
            m_canResurrect = component.m_canResurrect;
        }

        public override void BuildSnapshot(ByteBufferBuilder builder)
        {
            builder.WriteBool(m_isDying);
            builder.WriteBool(m_isDead);
            builder.WriteBool(m_canResurrect);
        }

        public override void LoadSnapshot(ByteBufferLoader loader)
        {
            m_isDying = loader.ReadBool();
            m_isDead = loader.ReadBool();
            m_canResurrect = loader.ReadBool();
        }
    }
}
