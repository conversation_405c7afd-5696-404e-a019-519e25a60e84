using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityLocationComponent : EntityComponent
    {
        protected GridPosition m_locatedPos;
        protected GridDirType m_dir;
        protected int m_overrideMoveRuleRid;

        protected BattleFieldSummaryOfEntityMoveCost m_fieldSummaryOfMoveCost;
        protected BattleFieldSummaryOfEntityLocatable m_fieldSummaryOfLocatable;
        protected BattleFieldSummaryOfEntityPassable m_fieldSummaryOfPassable;
        protected EntityMovePathRule m_movePathRule;

        public override void OnRelease()
        {
            m_locatedPos = default;
            m_dir = default;
            m_overrideMoveRuleRid = default;
            m_movePathRule.Release();
            m_movePathRule = default;
            m_fieldSummaryOfMoveCost.Release();
            m_fieldSummaryOfMoveCost = default;
            m_fieldSummaryOfLocatable.Release();
            m_fieldSummaryOfLocatable = default;
            m_fieldSummaryOfPassable.Release();
            m_fieldSummaryOfPassable = default;
            base.OnRelease();
        }

        protected override void OnInitBasic()
        {
            m_locatedPos = GridPosition.invalid;
            base.OnInitBasic();
        }

        protected override void OnUnInit()
        {
            if (m_fieldSummaryOfMoveCost != null)
            {
                m_fieldSummaryOfMoveCost.UnInit();
            }
            if (m_fieldSummaryOfLocatable != null)
            {
                m_fieldSummaryOfLocatable.UnInit();
            }
            if (m_fieldSummaryOfPassable != null)
            {
                m_fieldSummaryOfPassable.UnInit();
            }
            base.OnUnInit();
        }

        public GridPosition GetLocation()
        {
            return m_locatedPos;
        }

        public GridPosition GetNearestOccupiedPos(GridPosition targetPos)
        {
            return GetNearestOccupiedPos(m_locatedPos, targetPos);
        }

        public GridPosition GetNearestOccupiedPos(GridPosition originPos, GridPosition targetPos)
        {
            int occupySize = entity.GetOccupySize();
            if (occupySize <= 1)
            {
                return originPos;
            }
            int occupyOffset = (occupySize - 1) / 2;
            int posX = Math.Clamp(targetPos.x, originPos.x - occupyOffset, originPos.x + occupyOffset);
            int posY = Math.Clamp(targetPos.y, originPos.y - occupyOffset, originPos.y + occupyOffset);
            return new GridPosition(posX, posY);
        }

        public void InitLocation(GridPosition pos)
        {
            m_battle.AddEntityToFieldSummary(m_entity, pos);
            m_locatedPos = pos;
        }

        public void SetLocation(GridPosition pos, bool setDependDirty = true)
        {
            m_battle.MoveEntityToFieldSummary(m_entity, m_locatedPos, pos, setDependDirty);
            m_locatedPos = pos;
        }

        public void SetLocation(GridPosition prePos, GridPosition curPos, bool setDependDirty = true)
        {
            m_battle.MoveEntityToFieldSummary(m_entity, prePos, curPos, setDependDirty);
            m_locatedPos = curPos;
        }

        public void ChangeLocation(GridPosition newPos, BattleActionProcedureContext procedureContext)
        {
            SetLocation(newPos);
        }

        public GridDirType GetDir()
        {
            return m_dir;
        }

        public void SetDir(GridDirType dir)
        {
            m_dir = dir;
        }

        public int GetOccupySize()
        {
            return m_entity.dataGetter.GetSize();
        }

        public bool IsLocatedOn(GridPosition pos)
        {
            GridPosition vec = pos - m_locatedPos;
            int occupySize = GetOccupySize();
            int occupyOffset = (occupySize - 1) / 2;
            return Math.Abs(vec.x) <= occupyOffset && Math.Abs(vec.y) <= occupyOffset;
        }

        public MovePathRule GetMovePathRule()
        {
            if (m_movePathRule == null)
            {
                m_movePathRule = m_battle.FetchObj<EntityMovePathRule>();
                m_movePathRule.Init(m_entity);
            }
            return m_movePathRule;
        }

        public int GetMoveRuleRid()
        {
            if (m_overrideMoveRuleRid > 0)
            {
                return m_overrideMoveRuleRid;
            }
            return m_entity.dataGetter.GetMoveRuleRid();
        }

        public void ChangeMoveRule(int ruleRid)
        {
            m_overrideMoveRuleRid = ruleRid;
            UpdateMoveRuleInternal(GetMoveRuleRid());
        }

        public void ResetMoveRule()
        {
            m_overrideMoveRuleRid = default;
            UpdateMoveRuleInternal(GetMoveRuleRid());
        }

        public int GetMoveCost(GridPosition pos)
        {
            return GetFieldSummaryOfEntityMoveCost().GetValue(pos);
        }

        public bool CheckLocatable(GridPosition pos)
        {
            var locatablePosSummary = GetFieldSummaryOfEntityLocatable();
            using (var posCollectionSummary = m_battle.CreateFieldSummaryForPosCollection())
            {
                CollectOccupiedPos(pos, posCollectionSummary);
                if (posCollectionSummary.GetPosList().Count != GetOccupySize() * GetOccupySize())
                {
                    return false;
                }
                bool result = true;
                foreach (var checkPos in posCollectionSummary.GetPosList())
                {
                    if (!locatablePosSummary.CheckPos(checkPos) || !locatablePosSummary.GetValue(checkPos))
                    {
                        result = false;
                        break;
                    }
                }
                return result;
            }
        }

        public void CollectOccupiedPos(List<GridPosition> posList)
        {
            CollectOccupiedPos(GetLocation(), posList);
        }

        public void CollectOccupiedPos(GridPosition locationPos, List<GridPosition> posList)
        {
            if (posList.Count > 0)
            {
                throw new ImposibleException();
            }
            int occupySize = entity.GetOccupySize();
            if (occupySize < 1)
            {
                return;
            }
            if (occupySize == 1)
            {
                posList.Add(locationPos);
                return;
            }
            int occupyOffset = (occupySize - 1) / 2;
            for (int i = -occupyOffset; i <= occupyOffset; ++i)
            {
                for (int j = -occupyOffset; j <= occupyOffset; ++j)
                {
                    posList.Add(locationPos + new GridPosition(i, j));
                }
            }
        }

        public bool CheckPassable(GridPosition pos)
        {
            var summary = GetFieldSummaryOfEntityPassable();
            int occupySize = entity.GetOccupySize();
            int occupyOffset = (occupySize - 1) / 2;
            for (int i = -occupyOffset; i <= occupyOffset; ++i)
            {
                for (int j = -occupyOffset; j <= occupyOffset; ++j)
                {
                    var checkPos = pos + new GridPosition(i, j);
                    if (!summary.CheckPos(checkPos) || !summary.GetValue(checkPos))
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        public void CollectOccupiedPos(BattleFieldSummaryForPosCollection posCollection)
        {
            CollectOccupiedPos(GetLocation(), posCollection);
        }

        public void CollectOccupiedPos(GridPosition locationPos, BattleFieldSummaryForPosCollection posCollection)
        {
            int occupySize = entity.GetOccupySize();
            if (occupySize < 1)
            {
                return;
            }
            if (occupySize == 1)
            {
                posCollection.AddPos(locationPos);
                return;
            }
            int occupyOffset = (occupySize - 1) / 2;
            for (int i = -occupyOffset; i <= occupyOffset; ++i)
            {
                for (int j = -occupyOffset; j <= occupyOffset; ++j)
                {
                    posCollection.AddPos(locationPos + new GridPosition(i, j));
                }
            }
        }

        private BattleFieldSummaryOfEntityMoveCost GetFieldSummaryOfEntityMoveCost()
        {
            if (m_fieldSummaryOfMoveCost == null)
            {
                m_fieldSummaryOfMoveCost = m_battle.FetchObj<BattleFieldSummaryOfEntityMoveCost>();
                m_fieldSummaryOfMoveCost.SetEntity(m_entity);

                var fieldSummaryOfRuleMoveCost = m_battle.GetFieldSummaryOfRuleMoveCost(GetMoveRuleRid());

                m_fieldSummaryOfMoveCost.SetFieldSummaryOfRuleMoveCost(fieldSummaryOfRuleMoveCost);

                m_battle.stageManageComponent.InitFieldSummarySize(m_fieldSummaryOfMoveCost);
            }
            return m_fieldSummaryOfMoveCost;
        }

        private BattleFieldSummaryOfEntityLocatable GetFieldSummaryOfEntityLocatable()
        {
            if (m_fieldSummaryOfLocatable == null)
            {
                m_fieldSummaryOfLocatable = m_battle.FetchObj<BattleFieldSummaryOfEntityLocatable>();
                m_fieldSummaryOfLocatable.SetEntity(m_entity);

                var fieldSummaryOfOccupyEntity = m_battle.GetFieldSummaryOfOccupyEntity();
                var fieldSummaryOfRuleLocatable = m_battle.GetFieldSummaryOfRuleLocatable(GetMoveRuleRid());

                m_fieldSummaryOfLocatable.SetFieldSummaryOfOccupyEntity(fieldSummaryOfOccupyEntity);
                m_fieldSummaryOfLocatable.SetFieldSummaryOfRuleLocatable(fieldSummaryOfRuleLocatable);
                m_battle.stageManageComponent.InitFieldSummarySize(m_fieldSummaryOfLocatable);
            }
            return m_fieldSummaryOfLocatable;
        }

        private BattleFieldSummaryOfEntityPassable GetFieldSummaryOfEntityPassable()
        {
            if (m_fieldSummaryOfPassable == null)
            {
                m_fieldSummaryOfPassable = m_battle.FetchObj<BattleFieldSummaryOfEntityPassable>();
                m_fieldSummaryOfPassable.SetEntity(m_entity);

                var fieldSummaryOfOccupyEntity = m_battle.GetFieldSummaryOfOccupyEntity();
                var fieldSummaryOfRulePassable = m_battle.GetFieldSummaryOfRulePassable(GetMoveRuleRid());

                m_fieldSummaryOfPassable.SetFieldSummaryOfOccupyEntity(fieldSummaryOfOccupyEntity);
                m_fieldSummaryOfPassable.SetFieldSummaryOfRulePassable(fieldSummaryOfRulePassable);
                m_battle.stageManageComponent.InitFieldSummarySize(m_fieldSummaryOfPassable);
            }
            return m_fieldSummaryOfPassable;
        }

        private void UpdateMoveRuleInternal(int ruleRid)
        {
            var fieldSummaryOfOccupyEntity = m_battle.GetFieldSummaryOfOccupyEntity();
            var fieldSummaryOfRuleMoveCost = m_battle.GetFieldSummaryOfRuleMoveCost(ruleRid);
            var fieldSummaryOfRuleLocatable = m_battle.GetFieldSummaryOfRuleLocatable(ruleRid);
            var fieldSummaryOfRulePassable = m_battle.GetFieldSummaryOfRulePassable(ruleRid);

            if (m_fieldSummaryOfMoveCost != null)
            {
                m_fieldSummaryOfMoveCost.SetFieldSummaryOfRuleMoveCost(fieldSummaryOfRuleMoveCost);
                m_fieldSummaryOfMoveCost.SetAllDirtyFlag(true);
            }
            if (m_fieldSummaryOfLocatable != null)
            {
                m_fieldSummaryOfLocatable.SetFieldSummaryOfOccupyEntity(fieldSummaryOfOccupyEntity);
                m_fieldSummaryOfLocatable.SetFieldSummaryOfRuleLocatable(fieldSummaryOfRuleLocatable);
                m_fieldSummaryOfLocatable.SetAllDirtyFlag(true);
            }
            if (m_fieldSummaryOfPassable != null)
            {
                m_fieldSummaryOfPassable.SetFieldSummaryOfOccupyEntity(fieldSummaryOfOccupyEntity);
                m_fieldSummaryOfPassable.SetFieldSummaryOfRulePassable(fieldSummaryOfRulePassable);
                m_fieldSummaryOfPassable.SetAllDirtyFlag(true);
            }
        }

        public override bool CheckSame(EntityComponent baseComponent)
        {
            bool result = true;
            EntityLocationComponent component = baseComponent as EntityLocationComponent;
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_locatedPos, component.m_locatedPos, "m_locatedPos");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, (byte)m_dir, (byte)component.m_dir, "m_dir");
            result &= BattleUtility.CheckSameStructOrLogout(m_battle, this, m_overrideMoveRuleRid, component.m_overrideMoveRuleRid, "m_overrideMoveRuleRid");
            return result;
        }

        public override void CopyFrom(EntityComponent baseComponent)
        {
            var component = baseComponent as EntityLocationComponent;
            m_locatedPos = component.m_locatedPos;
            m_dir = component.m_dir;
            m_overrideMoveRuleRid = component.m_overrideMoveRuleRid;
        }

        public override void BuildSnapshot(ByteBufferBuilder builder)
        {
            builder.WriteByte(m_locatedPos.x);
            builder.WriteByte(m_locatedPos.y);
            builder.WriteByte((byte)m_dir);
            builder.WriteByte(m_overrideMoveRuleRid);
        }

        public override void LoadSnapshot(ByteBufferLoader loader)
        {
            m_locatedPos = new GridPosition(loader.ReadByte(), loader.ReadByte());
            m_dir = (GridDirType)loader.ReadByte();
            m_overrideMoveRuleRid = loader.ReadByte();
        }
    }
}
