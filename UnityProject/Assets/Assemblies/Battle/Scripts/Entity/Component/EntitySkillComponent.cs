using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntitySkillComponent : EntityComponent
    {
        private List<Skill> m_skillList = new List<Skill>();
        private List<PassiveSkill> m_passiveSkillList = new List<PassiveSkill>();
        private int m_preAnnounceSkillUid;
        private List<GridPosition> m_preAnnounceStepPosList = new List<GridPosition>();

        public List<Skill> skillList
        {
            get { return m_skillList; }
        }

        public override void OnRelease()
        {
            m_skillList.ReleaseAll();
            m_passiveSkillList.ReleaseAll();
            m_preAnnounceSkillUid = 0;
            m_preAnnounceStepPosList.Clear();
            base.OnRelease();
        }

        protected override void OnInitAdvanced()
        {
            IEntitySkillGetter skillGetter = m_entity.dataGetter as IEntitySkillGetter;
            if (skillGetter != null)
            {
                InitAndAddPassiveSkill(skillGetter.GetTallentPassiveSkillRid(), SkillSlotId.Tallent);
                InitAndAddSkill(skillGetter.GetArtifactSkillRid(), SkillSlotId.Artifact);
                InitAndAddSkill(skillGetter.GetNormalAttackRid(), SkillSlotId.NormalAttack);
                InitAndAddSkill(skillGetter.GetCounterAttackRid(), SkillSlotId.CounterAttack);
                InitAndAddSkill(skillGetter.GetContinuousAttackRid(), SkillSlotId.ContinuousAttack);
                InitAndAddSkill(skillGetter.GetAdditionalAttackRid(), SkillSlotId.AdditionalAttack);
                SkillSlotId slotIdCursor = SkillSlotId.SkillSlot1;
                var skillRidList = skillGetter.GetSkillRidList();
                for (int i = 0; i < skillRidList.Count; ++i)
                {
                    InitAndAddSkill(skillRidList[i], slotIdCursor);
                    slotIdCursor++;
                }
                var passiveSkillRidList = skillGetter.GetPassiveSkillRidList();
                for (int i = 0; i < passiveSkillRidList.Count; ++i)
                {
                    InitAndAddPassiveSkill(passiveSkillRidList[i], slotIdCursor);
                    slotIdCursor++;
                }
            }
        }

        public List<Skill> GetSkillList()
        {
            return m_skillList;
        }

        public Skill GetNormalAttack()
        {
            return GetSkillBySkillSlotId(SkillSlotId.NormalAttack);
        }

        public Skill GetCounterAttack()
        {
            return GetSkillBySkillSlotId(SkillSlotId.CounterAttack);
        }

        public Skill GetContinuousAttack()
        {
            return GetSkillBySkillSlotId(SkillSlotId.ContinuousAttack);
        }

        public Skill GetAdditionalAttack()
        {
            return GetSkillBySkillSlotId(SkillSlotId.AdditionalAttack);
        }

        public Skill GetSkillBySkillSlotId(SkillSlotId slotId)
        {
            int count = m_skillList.Count;
            for (int i = 0; i < count; ++i)
            {
                if (m_skillList[i].slotId == slotId)
                {
                    return m_skillList[i];
                }
            }
            return null;
        }

        public Skill GetSkillBySkillUid(int skillUid)
        {
            for (int i = 0; i < m_skillList.Count; ++i)
            {
                Skill skill = m_skillList[i];
                if (skill != null && skill.uid == skillUid)
                {
                    return skill;
                }
            }
            return null;
        }

        public void CastSkill(int skillIUid, CastSkillContext context, SkillEngageResult engageResult)
        {
            Skill skill = GetSkillBySkillUid(skillIUid);
            SkillLogicUtility.Cast(m_entity, skillIUid, context, engageResult.skillCastResultList);
        }

        public void TickAllSkillCoolTime(List<EntitySkillCoolTimeUpdateResult> resultList)
        {
            foreach (var skill in m_skillList)
            {
                if (skill == null)
                {
                    continue;
                }
                skill.TickCoolTime(resultList);
                skill.UnlockCoolTime();
            }
        }

        public List<PassiveSkill> GetPassiveSkillList()
        {
            return m_passiveSkillList;
        }

        public PassiveSkill GetPassiveSkillByUid(int uid)
        {
            foreach (var passiveSkill in m_passiveSkillList)
            {
                if (passiveSkill.uid == uid)
                {
                    return passiveSkill;
                }
            }
            return null;
        }

        public PassiveSkill GetPassiveSkillBySlotId(SkillSlotId slotId)
        {
            foreach (var passiveSkill in m_passiveSkillList)
            {
                if (passiveSkill.slotId == slotId)
                {
                    return passiveSkill;
                }
            }
            return null;
        }

        public void SetPreAnnounce(int skillUid, List<GridPosition> stepPosList)
        {
            m_preAnnounceSkillUid = skillUid;
            m_preAnnounceStepPosList.Clear();
            m_preAnnounceStepPosList.AddRange(stepPosList);
        }

        public void ResetPreAnnounce()
        {
            m_preAnnounceSkillUid = 0;
            m_preAnnounceStepPosList.Clear();
        }

        public int GetPreAnnounceSkillUid()
        {
            return m_preAnnounceSkillUid;
        }

        public List<GridPosition> GetPreAnnounceStepPosList()
        {
            return m_preAnnounceStepPosList;
        }

        private void InitAndAddSkill(int skillRid, SkillSlotId slotId)
        {
            if (skillRid == 0)
            {
                return;
            }
            SkillInfo skillInfo = m_battle.infoGetter.GetSkillInfo(skillRid);
            if (skillInfo != null)
            {
                Skill skill = m_entity.FetchObj<Skill>();

                skill.Init(skillInfo, slotId);
                m_skillList.Add(skill);
                //if (skillInfo.exSkillRid > 0)
                //{
                //    SkillInfo exSkillInfo = m_battle.infoGetter.GetSkillInfo(skillInfo.exSkillRid);
                //    if (exSkillInfo != null)
                //    {
                //        Skill exSkill = m_entity.FetchObj<Skill>();
                //        exSkill.Init(exSkillInfo, slotId);
                //        m_skillList.Add(exSkill);
                //        skill.exSkillUid = exSkill.uid;
                //        exSkill.originSkillUid = skill.uid;
                //    }
                //}
                for (int i = 0; i < skillInfo.passiveSkillRidList.Count; ++i)
                {
                    InitAndAddPassiveSkill(skillInfo.passiveSkillRidList[i], slotId);
                }
            }
        }

        private void InitAndAddPassiveSkill(int passiveSkillRid, SkillSlotId slotId)
        {
            if (passiveSkillRid == 0)
            {
                return;
            }
            var passiveSkillInfo = m_battle.infoGetter.GetPassiveSkillInfo(passiveSkillRid);
            if (passiveSkillInfo != null)
            {
                var passiveSkill = m_entity.FetchObj<PassiveSkill>();

                passiveSkill.Init(passiveSkillRid, slotId);
                m_passiveSkillList.Add(passiveSkill);
            }
        }

        public override bool CheckSame(EntityComponent baseComponent)
        {
            var component = baseComponent as EntitySkillComponent;
            var result = BattleUtility.CheckSameClassListOrLogout(m_battle, this, m_skillList, component.m_skillList, "skillList");
            result &= BattleUtility.CheckSameClassListOrLogout(m_battle, this, m_passiveSkillList, component.m_passiveSkillList, "m_passiveSkillList");
            return result;
        }

        public override void CopyFrom(EntityComponent baseComponent)
        {
            var component = baseComponent as EntitySkillComponent;
            m_preAnnounceSkillUid = component.m_preAnnounceSkillUid;
            m_preAnnounceStepPosList.AddRange(component.m_preAnnounceStepPosList);
            m_skillList.ReleaseAll();
            for (int i = 0; i < component.m_skillList.Count; ++i)
            {
                m_skillList.Add(component.m_skillList[i].Copy(entity));
            }
            m_passiveSkillList.ReleaseAll();
            for (int i = 0; i < component.m_passiveSkillList.Count; ++i)
            {
                m_passiveSkillList.Add(component.m_passiveSkillList[i].Copy(entity));
            }
        }

        public override void BuildSnapshot(ByteBufferBuilder builder)
        {
            builder.WriteByte(m_skillList.Count);
            for (int i = 0; i < m_skillList.Count; ++i)
            {
                m_skillList[i].BuildSnapshot(builder);
            }
        }

        public override void LoadSnapshot(ByteBufferLoader loader)
        {
            int skillCount = loader.ReadByte();
            for (int i = 0; i < skillCount; ++i)
            {
                Skill skill = m_battle.FetchObj<Skill>();
                skill.LoadSnapshot(loader);
                m_skillList.Add(skill);
            }
        }
    }
}
