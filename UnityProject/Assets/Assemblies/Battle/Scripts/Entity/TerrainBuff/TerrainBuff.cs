using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TerrainBuff : Entity
    {
        public int summonerEntityUid;
        public int triggerCount;
        private TerrainLogicInfo m_logicInfo;

        public override EntityType entityType
        {
            get { return EntityType.TerrainBuff; }
        }

        public TerrainLogicInfo logicInfo
        {
            get { return m_logicInfo; }
        }

        public override void OnRelease()
        {
            base.OnRelease();
            summonerEntityUid = default;
            triggerCount = default;
            m_logicInfo = default;
        }

        protected override void OnInitBasic()
        {
            base.OnInitBasic();
            ITerrainBuffDataGetter terrainBuffData = dataGetter as ITerrainBuffDataGetter;
            triggerCount = terrainBuffData.GetTriggerCount();
            m_logicInfo = terrainBuffData.GetLogicInfo();
        }

        protected override void OnInitComponentListWhenInitBasic()
        {
            AddComponentWhenInitBasic<EntityLocationComponent>();
            AddComponentWhenInitBasic<EntityTeamComponent>();
            AddComponentWhenInitBasic<EntityStateComponent>();
            AddComponentWhenInitBasic<EntityAliveComponent>();
            AddComponentWhenInitBasic<EntityLifeTimeComponent>();
            AddComponentWhenInitBasic<EntitySummonComponent>();
        }

        public override bool CanTriggerTerrainBuff()
        {
            return false;
        }
    }
}
