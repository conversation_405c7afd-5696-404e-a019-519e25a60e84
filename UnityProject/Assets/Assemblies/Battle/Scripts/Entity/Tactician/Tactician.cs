using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class Tactician : Entity
    {
        public override EntityType entityType
        {
            get { return EntityType.Tactician; }
        }

        public override void OnRelease()
        {
            base.OnRelease();
        }

        protected override void OnInitBasic()
        {
            base.OnInitBasic();
        }

        protected override void OnInitComponentListWhenInitBasic()
        {
            AddComponentWhenInitBasic<EntitySkillComponent>();
        }
    }
}
