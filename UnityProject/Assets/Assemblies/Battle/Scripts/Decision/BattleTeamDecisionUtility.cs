using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public static class BattleTeamDecisionUtility
    {
        public static BattleTeamDecisionResult GetDecision(IBattle battle, int teamUid)
        {
            var team = battle.GetTeamByUid(teamUid);
            var handler = battle.FetchObj<BattleTeamDecisionHandler>();
            handler.Init(team);
            var result = handler.GetDecision();
            return result;
        }

        public static BattleTeamDecisionResult GetDecisionOfSkill(IBattle battle, int entityUid, int skillUid, TeamDecisionId decisionId)
        {
            var entity = battle.GetEntityByUid(entityUid);
            if (entity == null)
            {
                return null;
            }
            var handler = battle.FetchObj<BattleTeamDecisionHandler>();
            handler.Init(entity.GetTeam());
            var result = handler.GetDecisionOfSkill(entityUid, skillUid, null, decisionId);
            return result;
        }

        public static BattleTeamDecisionResult GetDecisionOfSkill(IBattle battle, int entityUid, int skillUid, GridPosition exactMovePos, TeamDecisionId decisionId)
        {
            var entity = battle.GetEntityByUid(entityUid);
            if (entity == null)
            {
                return null;
            }
            var handler = battle.FetchObj<BattleTeamDecisionHandler>();
            handler.Init(entity.GetTeam());
            var result = handler.GetDecisionOfSkill(entityUid, skillUid, exactMovePos, decisionId);
            return result;
        }

        public static BattleTeamDecisionResult GetPreAnnoucneDecision(IBattle battle, IEntity originEntity, TeamDecisionId decisionId)
        {
            if (originEntity == null)
            {
                return null;
            }
            var handler = battle.FetchObj<BattleTeamDecisionHandler>();
            handler.Init(originEntity.GetTeam());
            var result = handler.GetPreAnnoucneDecision(originEntity, decisionId);
            return result;
        }
    }
}
