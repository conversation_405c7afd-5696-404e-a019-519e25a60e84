using System;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public partial class BattleTeamDecisionItemData
    {
        public int m_entityElementId;
        public int m_entityCareerId;
        public int m_entityRespType;
        public int m_actorRid;
        public int m_skillSelectType;
        public int m_skillRid;
        public List<int> m_skillTags = new List<int>();
        public int m_condition1;
        public int m_condition2;
        public int m_locateSelect;
        public BattleTeamDecisionItemInfo m_info = new BattleTeamDecisionItemInfo();

    }
}