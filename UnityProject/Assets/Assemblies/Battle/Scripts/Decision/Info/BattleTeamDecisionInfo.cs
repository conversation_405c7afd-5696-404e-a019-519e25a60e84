using MessagePack;
using System;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    [MessagePackObject]
    public class BattleTeamDecisionInfo
    {
        [Key(0)]
        public Int32 id { get; set; } = 0;

        [Key(1)]
        public Phoenix.ConfigData.TeamDecisionOriginSelectPatternType originSelectPatternType { get; set; } = 0;

        [Key(2)]
        public List<Phoenix.Battle.BattleTeamDecisionItemInfo> itemList = new List<Phoenix.Battle.BattleTeamDecisionItemInfo>();

    }
}