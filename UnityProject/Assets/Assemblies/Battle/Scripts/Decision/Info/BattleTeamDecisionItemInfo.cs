using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class BattleTeamDecisionItemInfo
    {
        public int id;
        public BattleTeamDecisionOriginSelectInfo originSelectInfo = new BattleTeamDecisionOriginSelectInfo();
        public BattleTeamDecisionSkillSelectInfo skillSelectInfo = new BattleTeamDecisionSkillSelectInfo();
        public List<BattleTeamDecisionConditionInfo> conditionInfoList = new List<BattleTeamDecisionConditionInfo>();
        public List<BattleTeamDecisionConditionInfo> conditionInfoList2 = new List<BattleTeamDecisionConditionInfo>();

    }
}