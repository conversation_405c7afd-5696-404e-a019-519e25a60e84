using Phoenix.ConfigData;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class BattleTeamDecisionConditionInfo
    {
        public TeamDecisionConditionFuncId funcType;
        public EntityElementId elementId;
        public EntityCareerId careerId;
        public BattleCampRefType campRefType;
        public EntityRespType respType;
        public TeamDecisionMarkId markId;
        public TeamDecisionCompareType compareType;
        public ElementRefType elementRefType;
        public int param1;
        public int skillRid;
        public bool checkSelf;
        public TeamDecisionConditionRuleType ruleType;

    }
}