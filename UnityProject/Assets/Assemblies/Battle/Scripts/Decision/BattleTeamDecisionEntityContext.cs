using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class BattleTeamDecisionEntityContext : BattlePoolObj
    {
        public BattleTeamDecisionContext owner;

        public IEntity entity;
        public Dictionary<int, BattleTeamDecisionSkillContext> skillMap = new Dictionary<int, BattleTeamDecisionSkillContext>();
        public BattleFieldSummaryForPosCollection assistGuardPosCollection;
        public BattleFieldSummaryForPosCollection moveRangePosCollection;

        public override void OnRelease()
        {
            owner = null;
            entity = null;
            skillMap.ReleaseAll();
            assistGuardPosCollection.Release();
            assistGuardPosCollection = null;
            moveRangePosCollection.Release();
            moveRangePosCollection = null;
            base.OnRelease();
        }
    }
}
