using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleTeamDecisionHandler : BattleObj
    {
        private BattleTeam m_team;

        public void Init(BattleTeam team)
        {
            m_team = team;
        }

        public BattleTeamDecisionResult GetDecision()
        {
            BattleTeamDecisionResult result = null;
            if (!UpdateTeamAggressive())
            {
                StayOrigin(BattleUtility.GetFirstEntityNeedAct(m_battle), out result);
                return result;
            }
            var decisionInfo = m_battle.infoGetter.GetTeamDecisionInfo(m_team.controlledPlayer.playerId, m_team.decisionIndex);
            if (decisionInfo == null)
            {
                decisionInfo = m_battle.infoGetter.GetTeamDecisionInfo(m_team.controlledPlayer.playerId, (int)TeamDecisionId.Default);
            }
            switch (decisionInfo.originSelectPatternType)
            {
                case TeamDecisionOriginSelectPatternType.Fit:
                    result = GetDecisionOriginFit(decisionInfo);
                    break;
                case TeamDecisionOriginSelectPatternType.Index:
                    result = GetDecisionOriginIndex(decisionInfo);
                    break;
            }
            if (result != null)
            {
                Debug.Log(m_battle, string.Format("[{0}]Id：{1}", "AI结果", result.resultId));
            }
            if (result == null && !MoveToNearestEnemy(BattleUtility.GetFirstEntityNeedAct(m_battle), out result))
            {
                StayOrigin(BattleUtility.GetFirstEntityNeedAct(m_battle), out result);
            }
            return result;
        }

        private BattleTeamDecisionResult GetDecisionOriginIndex(BattleTeamDecisionInfo decisionInfo)
        {
            IEntity entity = BattleUtility.GetNextEntityNeedAct(m_battle, null);
            if (entity == null)
            {
                return null;
            }
            BattleTeamDecisionResult result = null;
            using (var decisionContext = CreateDecisionContext(decisionInfo))
            {
                foreach (var itemInfo in decisionInfo.itemList)
                {
                    var originSelectInfo = itemInfo.originSelectInfo;
                    if (!CheckEntity(originSelectInfo, entity, entity))
                    {
                        continue;
                    }
                    bool checkSelfCondResult = true;
                    foreach (var condition in itemInfo.conditionInfoList)
                    {
                        if (!CheckCondition(condition, entity))
                        {
                            checkSelfCondResult = false;
                            break;
                        }
                    }
                    if (!checkSelfCondResult)
                    {
                        continue;
                    }
                    var entityContext = CreateEntityContext(decisionContext, entity, null);
                    using (var itemContext = CreateItemContext(decisionContext, itemInfo))
                    {
                        result = GetDecisionOfSkill(itemContext, entityContext);
                        if (result != null)
                        {
                            result.resultId = itemInfo.id;
                            break;
                        }
                    }
                }
            }
            return result;
        }

        private BattleTeamDecisionResult GetDecisionOriginFit(BattleTeamDecisionInfo decisionInfo)
        {
            List<IEntity> checkedEntityList = m_battle.FetchObj<List<IEntity>>();
            BattleTeamDecisionResult result = null;
            using (var decisionContext = CreateDecisionContext(decisionInfo))
            {
                foreach (var itemInfo in decisionInfo.itemList)
                {
                    IEntity entity = null;
                    while (true)
                    {
                        entity = BattleUtility.GetNextEntityNeedAct(m_battle, entity);
                        if (entity == null)
                        {
                            break;
                        }
                        if (checkedEntityList.Contains(entity))
                        {
                            break;
                        }
                        checkedEntityList.Add(entity);

                        var originSelectInfo = itemInfo.originSelectInfo;
                        if (!CheckEntity(originSelectInfo, entity, entity))
                        {
                            continue;
                        }
                        bool checkSelfCondResult = true;
                        foreach (var condition in itemInfo.conditionInfoList)
                        {
                            if (!CheckCondition(condition, entity))
                            {
                                checkSelfCondResult = false;
                                break;
                            }
                        }
                        if (!checkSelfCondResult)
                        {
                            continue;
                        }
                        var entityContext = CreateEntityContext(decisionContext, entity, null);
                        using (var itemContext = CreateItemContext(decisionContext, itemInfo))
                        {
                            result = GetDecisionOfSkill(itemContext, entityContext);
                            if (result != null)
                            {
                                break;
                            }
                        }
                    }
                    checkedEntityList.Clear();
                    if (result != null)
                    {
                        result.resultId = itemInfo.id;
                        break;
                    }
                }
            }
            m_battle.Release(checkedEntityList);
            return result;
        }

        private BattleTeamDecisionResult GetDecisionOfSkill(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionEntityContext entityContext)
        {
            BattleTeamDecisionResult result = null;
            if (itemContext.itemInfo.skillSelectInfo.skillSelectType == TeamDecisionSkillSelectType.None)
            {
                result = GetDecisionOfJustMove(itemContext, entityContext);
            }
            else
            {
                result = GetDecisionOfCastSkill(itemContext, entityContext);
            }
            return result;
        }

        private BattleTeamDecisionResult GetDecisionOfJustMove(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionEntityContext entityContext)
        {
            var skillContext = CreateSkillContext(entityContext, null);
            return GetDecision(itemContext, skillContext);
        }

        private BattleTeamDecisionResult GetDecisionOfCastSkill(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionEntityContext entityContext)
        {
            List<Skill> sortedSkillList = m_battle.FetchObj<List<Skill>>();
            CollectSortedSkillList(entityContext.entity, itemContext.itemInfo.skillSelectInfo, false, sortedSkillList);
            BattleTeamDecisionResult result = null;
            foreach (var skill in sortedSkillList)
            {
                var skillContext = CreateSkillContext(entityContext, skill);
                result = GetDecision(itemContext, skillContext);
                if (result != null)
                {
                    break;
                }
            }
            m_battle.Release(sortedSkillList);
            if (result != null)
            {
                return result;
            }
            return null;
        }

        private void CollectSortedSkillList(IEntity entity, BattleTeamDecisionSkillSelectInfo skillSelectInfo, bool preAnnounce, List<Skill> list)
        {
            foreach (var skill in entity.GetSkillList())
            {
                if (skill.skillInfo.needPreAnnounce != preAnnounce)
                {
                    continue;
                }
                if (skill.CanCastSkill() != BattleErrorCode.Ok)
                {
                    continue;
                }
                switch (skillSelectInfo.skillSelectType)
                {
                    case TeamDecisionSkillSelectType.Priority:
                        list.Add(skill);
                        break;
                    case TeamDecisionSkillSelectType.Tag:
                        if (skill.ContainsTag(skillSelectInfo.skillTageType))
                        {
                            list.Add(skill);
                        }
                        break;
                    case TeamDecisionSkillSelectType.Rid:
                        if (skill.rid == skillSelectInfo.skillRid)
                        {
                            list.Add(skill);
                        }
                        break;
                }
            }
            list.Sort(Skill.SortByPriority);
        }


        private IEntity GetFitEntityInTeam(BattleTeam team)
        {
            foreach (var entityUid in team.entityUidList)
            {
                var entity = m_battle.GetEntityByUid(entityUid);
                if (!entity.HasActionChance())
                {
                    continue;
                }
                if (entity.BanAction())
                {
                    continue;
                }
                return entity;
            }
            return null;
        }

        public BattleTeamDecisionResult GetDecisionOfSkill(int entityUid, int skillUid, GridPosition? exactMovePos,  TeamDecisionId decisionId)
        {
            var originEntity = m_battle.GetEntityByUid(entityUid);
            if (originEntity == null || originEntity.BanAction() || !originEntity.HasActionChance())
            {
                return null;
            }
            var selectedSkill = originEntity.GetSkillBySkillUid(skillUid);
            if (selectedSkill == null || selectedSkill.CanCastSkill() != BattleErrorCode.Ok)
            {
                return null;
            }
            var decisionInfo = m_battle.infoGetter.GetTeamDecisionInfo(m_team.controlledPlayer.playerId, (int)decisionId);
            using (var decisionContext = CreateDecisionContext(decisionInfo))
            {
                foreach (var itemInfo in decisionInfo.itemList)
                {
                    var originSelectInfo = itemInfo.originSelectInfo;
                    if (!CheckEntity(originSelectInfo, originEntity, originEntity))
                    {
                        continue;
                    }
                    bool checkSelfCondResult = true;
                    foreach (var condition in itemInfo.conditionInfoList)
                    {
                        if (!CheckCondition(condition, originEntity))
                        {
                            checkSelfCondResult = false;
                            break;
                        }
                    }
                    if (!checkSelfCondResult)
                    {
                        continue;
                    }
                    if (itemInfo.skillSelectInfo.skillSelectType == TeamDecisionSkillSelectType.Tag)
                    {
                        if (!selectedSkill.ContainsTag(itemInfo.skillSelectInfo.skillTageType))
                        {
                            continue;
                        }
                    }
                    var entityContext = CreateEntityContext(decisionContext, originEntity, exactMovePos);
                    var skillContext = CreateSkillContext(entityContext, selectedSkill);
                    using (var itemContext = CreateItemContext(decisionContext, itemInfo))
                    {
                        var result = GetDecision(itemContext, skillContext);
                        if (result != null)
                        {
                            return result;
                        }
                    }
                }
            }
            return null;
        }

        private BattleTeamDecisionResult GetDecision(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionSkillContext skillContext)
        {
            var entityContext = skillContext.owner;
            var originEntity = entityContext.entity;
            var itemInfo = itemContext.itemInfo;
            var selectedSkill = skillContext.skill;

            var selectionList = m_battle.FetchObj<List<BattleTeamDecisionSelection>>();
            selectionList.AddRange(skillContext.selectionList);
            BattleTeamDecisionResult result = null;
            if (selectionList.Count > 0)
            {
                if (selectedSkill != null)
                {
                    var resultSelectionListCastSkill = m_battle.FetchObj<List<BattleTeamDecisionSelection>>();
                    var resultSelectionListLocate = m_battle.FetchObj<List<BattleTeamDecisionSelection>>();
                    FilterDecisionList(itemContext, skillContext, itemInfo.conditionInfoList, selectionList, resultSelectionListCastSkill);
                    FilterDecisionList(itemContext, skillContext, itemInfo.conditionInfoList2, resultSelectionListCastSkill, resultSelectionListLocate);
                    BattleTeamDecisionSelection fitSelection = resultSelectionListLocate.GetValueSafely(0);
                    if (fitSelection != null)
                    {
                        result = m_battle.FetchObj<BattleTeamDecisionResult>();
                        result.entityUid = originEntity.uid;
                        result.movePos = fitSelection.movePos;
                        result.isCastSkill = true;
                        result.skillUid = selectedSkill.uid;
                        result.stepPosList.AddRange(fitSelection.stepPosList);
                    }
                    m_battle.Release(resultSelectionListCastSkill);
                    m_battle.Release(resultSelectionListLocate);
                }
                else
                {
                    var resultSelectionListLocate = m_battle.FetchObj<List<BattleTeamDecisionSelection>>();
                    FilterDecisionList(itemContext, skillContext, itemInfo.conditionInfoList2, selectionList, resultSelectionListLocate);
                    BattleTeamDecisionSelection fitSelection = resultSelectionListLocate.GetValueSafely(0);
                    if (fitSelection != null)
                    {
                        result = m_battle.FetchObj<BattleTeamDecisionResult>();
                        result.entityUid = originEntity.uid;
                        result.movePos = fitSelection.movePos;
                        result.isCastSkill = false;
                    }
                    m_battle.Release(resultSelectionListLocate);
                }
            }
            m_battle.Release(selectionList);
            return result;
        }

        public BattleTeamDecisionResult GetPreAnnoucneDecision(IEntity originEntity, TeamDecisionId decisionId)
        {
            if (originEntity.BanAction())
            {
                return null;
            }
            if (!UpdateTeamAggressive())
            {
                return null;
            }
            var decisionInfo = battle.infoGetter.GetTeamDecisionInfo(m_team.controlledPlayer.playerId, (int)decisionId);
            using (var decisionContext = CreateDecisionContext(decisionInfo))
            {
                foreach (var itemInfo in decisionInfo.itemList)
                {
                    var originSelectInfo = itemInfo.originSelectInfo;

                    if (!CheckEntity(originSelectInfo, originEntity, originEntity))
                    {
                        continue;
                    }
                    bool checkSelfCondResult = true;
                    foreach (var condition in itemInfo.conditionInfoList)
                    {
                        if (!CheckCondition(condition, originEntity))
                        {
                            checkSelfCondResult = false;
                            break;
                        }
                    }
                    if (!checkSelfCondResult)
                    {
                        continue;
                    }
                    List<Skill> sortedSkillList = m_battle.FetchObj<List<Skill>>();
                    Skill selectedSkill = null;
                    CollectSortedSkillList(originEntity, itemInfo.skillSelectInfo, true, sortedSkillList);
                    selectedSkill = sortedSkillList.GetValueSafely(0);
                    m_battle.Release(sortedSkillList);
                    if (selectedSkill == null)
                    {
                        continue;
                    }
                    var entityContext = CreateEntityContext(decisionContext, originEntity, originEntity.GetLocation());
                    var skillContext = CreateSkillContext(entityContext, selectedSkill);
                    using (var itemContext = CreateItemContext(decisionContext, itemInfo))
                    {
                        return GetDecision(itemContext, skillContext);
                    }
                }
            }
            return null;
        }

        private bool CheckEntity(BattleTeamDecisionOriginSelectInfo originSelectInfo, IEntity originEntity, IEntity targetEntity)
        {
            if (originSelectInfo.isAny)
            {
                return true;
            }
            else if (originSelectInfo.useActorRid)
            {
                if (targetEntity.rid == originSelectInfo.actorRid)
                {
                    return true;
                }
            }
            else
            {
                return CheckEntity(originSelectInfo.careerId, originSelectInfo.elementId, originSelectInfo.respType, BattleCampRefType.Any, originEntity, targetEntity);
            }
            return false;
        }

        private bool CheckEntity(EntityCareerId careerId, EntityElementId elementId, EntityRespType respType, BattleCampRefType campRefType, IEntity originEntity, IEntity targetEntity)
        {
            var actorDataGetter = targetEntity.dataGetter as IActorDataGetter;
            if (actorDataGetter == null)
            {
                return false;
            }
            if (careerId != EntityCareerId.None && careerId != actorDataGetter.GetCareerId())
            {
                return false;
            }
            if (elementId != EntityElementId.None && elementId != actorDataGetter.GetElementId())
            {
                return false;
            }
            if (respType != EntityRespType.None && respType != actorDataGetter.GetRespType())
            {
                return false;
            }
            if (campRefType != BattleCampRefType.Any && !BattleUtility.CheckCampRef(originEntity.GetBattleCampId(), targetEntity.GetBattleCampId(), campRefType))
            {
                return false;
            }
            return true;
        }

        private void FilterDecisionList(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionSkillContext skillContext, List<BattleTeamDecisionConditionInfo> conditionInfoList, List<BattleTeamDecisionSelection> selectionList, List<BattleTeamDecisionSelection> resultSelectionList)
        {
            FilterDecisionListIter(itemContext, skillContext, conditionInfoList, 0, selectionList, resultSelectionList);

        }
        private void FilterDecisionListIter(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionSkillContext skillContext, List<BattleTeamDecisionConditionInfo> conditionInfoList, int conditionIndex, List<BattleTeamDecisionSelection> selectionList, List<BattleTeamDecisionSelection> resultSelectionList)
        {
            if (conditionInfoList.Count == 0)
            {
                resultSelectionList.AddRange(selectionList);
            }
            if (selectionList.Count == 0)
            {
                return;
            }
            if (conditionIndex >= conditionInfoList.Count)
            {
                return;
            }
            var selectionListTrue = m_battle.FetchObj<List<BattleTeamDecisionSelection>>();
            var selectionListFalse = m_battle.FetchObj<List<BattleTeamDecisionSelection>>();
            var conditionInfo = conditionInfoList[conditionIndex];
            var condtionContext = CreateConditionContext(itemContext, conditionInfo);
            FilterDecisionList(condtionContext, skillContext, selectionList, selectionListTrue, selectionListFalse);
            int nextConditionIndex = conditionIndex + 1;
            bool hasNextCondition = nextConditionIndex < conditionInfoList.Count;
            if (selectionListTrue.Count > 0)
            {
                if (hasNextCondition)
                {
                    FilterDecisionListIter(itemContext, skillContext, conditionInfoList, conditionIndex + 1, selectionListTrue, resultSelectionList);
                }
                else
                {
                    resultSelectionList.AddRange(selectionListTrue);
                }
            }
            if (resultSelectionList.Count == 0 && selectionListFalse.Count > 0)
            {
                if (hasNextCondition)
                {
                    FilterDecisionListIter(itemContext, skillContext, conditionInfoList, conditionIndex + 1, selectionListFalse, resultSelectionList);
                }
                else
                {
                    resultSelectionList.AddRange(selectionListFalse);
                }
            }
            m_battle.Release(selectionListTrue);
            m_battle.Release(selectionListFalse);
        }

        private void FilterDecisionList(BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, List<BattleTeamDecisionSelection> selectionList, List<BattleTeamDecisionSelection> selectionListTrue, List<BattleTeamDecisionSelection> selectionListFalse)
        {
            var conditionInfo = conditionContext.conditionInfo;
            bool isPosCondition = CheckPosCondition(conditionInfo.funcType);
            if (conditionInfo.ruleType == TeamDecisionConditionRuleType.Condition)
            {
                selectionListTrue.AddRange(selectionList);
                //Do Nothing
            }
            else if (conditionInfo.ruleType == TeamDecisionConditionRuleType.Priority)
            {
                if (isPosCondition)
                {
                    foreach (var selection in selectionList)
                    {

                        if (CheckSelectionPos(selection, conditionContext, skillContext))
                        {
                            selectionListTrue.Add(selection);
                        }
                        else
                        {
                            selectionListFalse.Add(selection);
                        }
                    }
                }
                else
                {
                    List<IEntity> targetEntityList = m_battle.FetchObj<List<IEntity>>();
                    foreach (var selection in selectionList)
                    {
                        CollectEntityListOfSkill(conditionContext, skillContext, selection, targetEntityList);
                        if (targetEntityList.Count > 0)
                        {
                            if (CheckTargetList(targetEntityList, conditionContext, skillContext, selection))
                            {
                                selectionListTrue.Add(selection);
                            }
                            else
                            {
                                selectionListFalse.Add(selection);
                            }
                        }
                        targetEntityList.Clear();
                    }
                    m_battle.Release(targetEntityList);
                }
            }
            else if (conditionInfo.ruleType == TeamDecisionConditionRuleType.Only)
            {
                if (isPosCondition)
                {
                    foreach (var selection in selectionList)
                    {
                        if (CheckSelectionPos(selection, conditionContext, skillContext))
                        {
                            selectionListTrue.Add(selection);
                        }
                    }
                }
                else
                {
                    List<IEntity> targetEntityList = m_battle.FetchObj<List<IEntity>>();
                    foreach (var selection in selectionList)
                    {
                        CollectEntityListOfSkill(conditionContext, skillContext, selection, targetEntityList);
                        if (targetEntityList.Count > 0)
                        {
                            if (CheckTargetList(targetEntityList, conditionContext, skillContext, selection))
                            {
                                selectionListTrue.Add(selection);
                            }
                        }
                        targetEntityList.Clear();
                    }
                    m_battle.Release(targetEntityList);
                }
            }
            else if (conditionInfo.ruleType == TeamDecisionConditionRuleType.Most)
            {
                bool findFitValue = false;
                FixedValue fitValue = FixedValue.zero;
                if (isPosCondition)
                {
                    foreach (var selection in selectionList)
                    {
                        FixedValue valueToCompare;
                        if (TryGetValueToCompare(selection, conditionContext, skillContext, out valueToCompare))
                        {
                            if (!findFitValue)
                            {
                                fitValue = valueToCompare;
                                findFitValue = true;
                                selectionListTrue.Add(selection);
                                continue;
                            }
                            if (CheckUpdateCompareFit(conditionInfo, valueToCompare, fitValue))
                            {
                                fitValue = valueToCompare;
                                selectionListTrue.Clear();
                                selectionListTrue.Add(selection);
                            }
                        }
                    }
                }
                else
                {
                    List<IEntity> targetEntityList = m_battle.FetchObj<List<IEntity>>();
                    foreach (var selection in selectionList)
                    {
                        CollectEntityListOfSkill(conditionContext, skillContext, selection, targetEntityList);
                        if (targetEntityList.Count > 0)
                        {
                            FixedValue valueToCompare;
                            if (TryGetValueToCompare(targetEntityList, conditionContext, skillContext, out valueToCompare))
                            {
                                if (!findFitValue)
                                {
                                    fitValue = valueToCompare;
                                    findFitValue = true;
                                    selectionListTrue.Add(selection);
                                }
                                else if (CheckUpdateCompareFit(conditionInfo, valueToCompare, fitValue))
                                {
                                    fitValue = valueToCompare;
                                    selectionListTrue.Clear();
                                    selectionListTrue.Add(selection);
                                }
                            }
                        }
                        targetEntityList.Clear();
                    }
                    m_battle.Release(targetEntityList);
                }
            }
            else
            {
                throw new ToDoException("FilterDecisionList:" + conditionInfo.ruleType);
            }
        }

        private bool CheckSelectionPos(BattleTeamDecisionSelection selection, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext)
        {
            var entityContext = skillContext.owner;
            var decisionContext = entityContext.owner;
            var conditionInfo = conditionContext.conditionInfo;
            var originEntity = entityContext.entity;
            FixedValue valueToCompare;
            if (TryGetValueToCompare(selection, conditionContext, skillContext, out valueToCompare))
            {
                return CompareValue(conditionInfo.compareType, valueToCompare, conditionInfo.param1);
            }
            var preLocation = originEntity.GetLocation();
            switch (conditionInfo.funcType)
            {
                case TeamDecisionConditionFuncId.PosOrigin:
                    return preLocation == selection.movePos;
                case TeamDecisionConditionFuncId.PosInAssistGuard:
                    return entityContext.assistGuardPosCollection.ContainsPos(selection.movePos);
                case TeamDecisionConditionFuncId.PosOutDanger:
                    return !decisionContext.dangerPosCollection.ContainsPos(selection.movePos);

            }
            return false;
        }

        private bool CheckTargetList(List<IEntity> targetEntityList, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var conditionInfo = conditionContext.conditionInfo;
            if (CheckNeedCompare(conditionInfo.funcType))
            {
                FixedValue valueToCompare;
                if (TryGetValueToCompare(targetEntityList, conditionContext, skillContext, out valueToCompare))
                {
                    return CompareValue(conditionInfo.compareType, valueToCompare, conditionInfo.param1);
                }
            }
            switch (conditionInfo.funcType)
            {
                case TeamDecisionConditionFuncId.TargetCampRef:
                    return CheckTargetList(targetEntityList, CheckCampRef, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetCareer:
                    return CheckTargetList(targetEntityList, CheckCareer, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetElementRef:
                    return CheckTargetList(targetEntityList, CheckElementRef, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetActorRid:
                    return CheckTargetList(targetEntityList, CheckActorRid, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetMark:
                    return CheckTargetList(targetEntityList, CheckMark, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetInDanger:
                    return CheckTargetList(targetEntityList, CheckInDanger, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetOutAssistGuard:
                    return CheckTargetList(targetEntityList, CheckOutAssistGuard, conditionContext, skillContext, selection);
                case TeamDecisionConditionFuncId.TargetCounterDistLessThanSkillRange:
                    return CheckTargetList(targetEntityList, CheckCounterDistLessThanSkillRange, conditionContext, skillContext, selection);


            }
            return false;
        }

        private bool CheckTargetList(List<IEntity> targetEntityList, Func<IEntity, BattleTeamDecisionConditionContext, BattleTeamDecisionSkillContext, BattleTeamDecisionSelection, bool> funcCheck, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            foreach (var targetEntity in targetEntityList)
            {
                if (funcCheck(targetEntity, conditionContext, skillContext, selection))
                {
                    return true;
                }
            }
            return false;
        }

        private bool TryGetValueToCompare(List<IEntity> targetEntityList, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, out FixedValue valueToCompare)
        {
            var conditionInfo = conditionContext.conditionInfo;
            switch (conditionInfo.funcType)
            {
                case TeamDecisionConditionFuncId.TargetCountInSkillRange:
                    valueToCompare = targetEntityList.Count;
                    return true;
                case TeamDecisionConditionFuncId.TargetCountInjuredInSkillRange:
                    valueToCompare = GetInjuredTargetCount(targetEntityList);
                    return true;
            }
            if (!CheckNeedCompare(conditionInfo.funcType))
            {
                throw new ToDoException("TryGetValueToCompare:" + conditionInfo.funcType);
            }
            FixedValue fitValue = FixedValue.zero;
            bool findFitValue = false;
            foreach (var target in targetEntityList)
            {
                FixedValue value;
                if (TryGetValueToCompare(target, conditionContext, skillContext, out value))
                {
                    if (!findFitValue)
                    {
                        fitValue = value;
                        findFitValue = true;
                        continue;
                    }
                    if (CheckUpdateCompareFit(conditionInfo, value, fitValue))
                    {
                        fitValue = value;
                    }
                }
            }
            if (findFitValue)
            {
                valueToCompare = fitValue;
                return true;
            }
            valueToCompare = FixedValue.zero;
            return false;
        }

        private bool TryGetValueToCompare(BattleTeamDecisionSelection selection, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, out FixedValue valueToCompare)
        {
            var entityContext = skillContext.owner;
            var conditionInfo = conditionContext.conditionInfo;
            var originEntity = entityContext.entity;
            var preLocation = originEntity.GetLocation();
            switch (conditionInfo.funcType)
            {
                case TeamDecisionConditionFuncId.PosDistanceToMark:
                    var markPos = GridPosition.invalid;
                    var mark = originEntity.GetTeam().GetDecisionMark(conditionInfo.markId);
                    if (mark != null)
                    {
                        if (conditionInfo.markId == TeamDecisionMarkId.Move)
                        {
                            markPos = mark.pos;
                        }
                        else
                        {
                            var entity = m_battle.GetEntityByUid(mark.entityUid);
                            if (entity != null && entity != originEntity)
                            {
                                markPos = entity.GetLocation();
                            }
                        }
                    }
                    if (markPos != GridPosition.invalid)
                    {
                        if (selection.movePos == markPos)
                        {
                            valueToCompare = 0;
                            return true;
                        }
                        var pathFindResult = MovePathFindUtility.FindPathAnywayIgnoreTarget(selection.movePos, markPos, originEntity.GetMovePathRule());
                        valueToCompare = Math.Max(0, pathFindResult.gridResultList.Count - 1);
                        pathFindResult.Release();
                        if (valueToCompare > 0)
                        {
                            return true;
                        }
                        return false;
                    }
                    valueToCompare = default;
                    return false;
                case TeamDecisionConditionFuncId.PosDistanceToOrigin:
                    valueToCompare = preLocation.DistanceTo(selection.movePos);
                    return true;
                case TeamDecisionConditionFuncId.PosDistanceToTarget:
                    {
                        bool success = false;
                        valueToCompare = default;
                        if (selection.stepPosList.Count > 0)
                        {
                            valueToCompare = selection.movePos.DistanceTo(selection.stepPosList.GetValueSafely(0));
                            return true;
                        }
                        else
                        {
                            var moveRule = m_battle.FetchObj<EntityMovePathRuleForFindNearestTarget>();
                            moveRule.Init(originEntity, TargetSelectFilterFuncType.RivalActor);
                            var result = MovePathFindUtility.FindPathToNearestTarget(selection.movePos, moveRule);
                            if (result.success)
                            {
                                success = true;
                                valueToCompare = Math.Max(0, result.gridResultList.Count - 1);
                            }
                            result.Release();
                            moveRule.Release();
                        }
                        return success;
                    }
                case TeamDecisionConditionFuncId.PosDistanceToActor:
                    {
                        int fitDistance = 0;
                        IEntity fitEntity = null;
                        foreach (var entity in m_battle.GetEntityList())
                        {
                            if (entity == originEntity)
                            {
                                continue;
                            }
                            if (CheckEntity(conditionInfo.careerId, conditionInfo.elementId, conditionInfo.respType, conditionInfo.campRefType, originEntity, entity))
                            {
                                var distance = entity.GetLocation().DistanceTo(selection.movePos);
                                if (fitEntity == null)
                                {
                                    fitDistance = distance;
                                    fitEntity = entity;
                                }
                                else if (CheckUpdateCompareFit(conditionInfo, distance, fitDistance))
                                {
                                    fitDistance = distance;
                                    fitEntity = entity;
                                }
                            }
                        }
                        if (fitEntity != null)
                        {
                            valueToCompare = fitDistance;
                            return true;
                        }
                        valueToCompare = default;
                        return false;
                    }
                case TeamDecisionConditionFuncId.PosAssistGuardCount:
                    valueToCompare = GetAssistGuardEntityCountInDanger(selection, skillContext);
                    return true;
                case TeamDecisionConditionFuncId.PosActorCountInRhombRange:
                    throw new ToDoException("TryGetValueToCompare:" + conditionInfo.funcType);

            }
            valueToCompare = FixedValue.zero;
            return false;
        }

        private bool CheckUpdateCompareFit(BattleTeamDecisionConditionInfo condition, FixedValue valueToCompare, FixedValue fitValue)
        {
            switch (condition.compareType)
            {
                case TeamDecisionCompareType.Greater:
                case TeamDecisionCompareType.GreaterEqual:
                case TeamDecisionCompareType.Greatest:
                    return valueToCompare > fitValue;
                case TeamDecisionCompareType.Less:
                case TeamDecisionCompareType.LessEqual:
                case TeamDecisionCompareType.Smallest:
                    return valueToCompare < fitValue;
            }
            throw new ToDoException("CheckCompareFit:" + condition.compareType);
        }

        private bool CheckPosCondition(TeamDecisionConditionFuncId funcType)
        {
            switch (funcType)
            {
                case TeamDecisionConditionFuncId.PosOrigin:
                case TeamDecisionConditionFuncId.PosDistanceToMark:
                case TeamDecisionConditionFuncId.PosDistanceToOrigin:
                case TeamDecisionConditionFuncId.PosDistanceToTarget:
                case TeamDecisionConditionFuncId.PosDistanceToActor:
                case TeamDecisionConditionFuncId.PosOutDanger:
                case TeamDecisionConditionFuncId.PosInAssistGuard:
                case TeamDecisionConditionFuncId.PosAssistGuardCount:
                case TeamDecisionConditionFuncId.PosActorCountInRhombRange:
                    return true;
            }
            return false;
        }

        private bool CheckNeedCompare(TeamDecisionConditionFuncId funcType)
        {
            switch (funcType)
            {
                case TeamDecisionConditionFuncId.TargetHpRate:
                case TeamDecisionConditionFuncId.TargetHpValue:
                case TeamDecisionConditionFuncId.TargetPhysicalAttack:
                case TeamDecisionConditionFuncId.TargetPhysicalDeffence:
                case TeamDecisionConditionFuncId.TargetMagicalAttack:
                case TeamDecisionConditionFuncId.TargetMagicalDeffence:
                case TeamDecisionConditionFuncId.TargetCountInSkillRange:
                case TeamDecisionConditionFuncId.TargetCountInjuredInSkillRange:
                    return true;
            }
            return false;
        }

        private bool TryGetValueToCompare(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, out FixedValue valueToCompare)
        {
            var conditionInfo = conditionContext.conditionInfo;
            var entityContext = skillContext.owner;
            var originEntity = entityContext.entity;
            switch (conditionInfo.funcType)
            {
                case TeamDecisionConditionFuncId.TargetHpRate:
                    valueToCompare = GetHpRateForCompare(targetEntity);
                    return true;
                case TeamDecisionConditionFuncId.TargetHpValue:
                    valueToCompare = GetHpValueForCompare(targetEntity);
                    return true;
                case TeamDecisionConditionFuncId.TargetPhysicalAttack:
                    valueToCompare = GetPhycicalAttackForCompare(targetEntity);
                    return true;
                case TeamDecisionConditionFuncId.TargetPhysicalDeffence:
                    valueToCompare = GetPhycicalDeffenceForCompare(targetEntity);
                    return true;
                case TeamDecisionConditionFuncId.TargetMagicalAttack:
                    valueToCompare = GetMagicalAttackForCompare(targetEntity);
                    return true;
                case TeamDecisionConditionFuncId.TargetMagicalDeffence:
                    valueToCompare = GetMagicalDeffenceForCompare(targetEntity);
                    return true;
                case TeamDecisionConditionFuncId.TargetDistance:
                    valueToCompare = GetTargetDistanceForCompare(originEntity, targetEntity);
                    return true;
            }
            valueToCompare = FixedValue.zero;
            return false;

        }

        private int GetInjuredTargetCount(List<IEntity> targetEntityList)
        {
            int count = 0;
            foreach (var targetEntity in targetEntityList)
            {
                if (targetEntity.GetCurHpRate() < FixedValue.one)
                {
                    count++;
                }
            }
            return count;
        }

        private FixedValue GetHpRateForCompare(IEntity entity)
        {
            return entity.GetCurHpRate() * 100;
        }

        private FixedValue GetHpValueForCompare(IEntity entity)
        {
            return entity.GetCurHp();
        }

        private FixedValue GetPhycicalAttackForCompare(IEntity entity)
        {
            return entity.GetAttributeValue(AttributeId.PhysicalAttack);
        }

        private FixedValue GetPhycicalDeffenceForCompare(IEntity entity)
        {
            return entity.GetAttributeValue(AttributeId.PhysicalDefence);
        }

        private FixedValue GetMagicalAttackForCompare(IEntity entity)
        {
            return entity.GetAttributeValue(AttributeId.MagicalAttack);
        }

        private FixedValue GetMagicalDeffenceForCompare(IEntity entity)
        {
            return entity.GetAttributeValue(AttributeId.MagicalDefence);
        }

        private bool CheckCampRef(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            return BattleUtility.CheckCampRef(targetEntity.GetTeam().campId, skillContext.owner.entity.GetTeam().campId, conditionContext.conditionInfo.campRefType);
        }

        private bool CheckCareer(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var actorDataGetter = targetEntity.dataGetter as IActorDataGetter;
            if (actorDataGetter != null)
            {
                return actorDataGetter.GetCareerId() == conditionContext.conditionInfo.careerId;
            }
            return false;
        }

        private bool CheckElementRef(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var entityContext = skillContext.owner;
            var originEntity = entityContext.entity;
            var conditionInfo = conditionContext.conditionInfo;
            var selectedSkill = skillContext.skill;
            var actorDataGetter = originEntity.dataGetter as IActorDataGetter;
            if (actorDataGetter != null && selectedSkill != null)
            {
                return BattleUtility.CheckElementRef(m_battle, conditionInfo.elementRefType, selectedSkill.GetElementId(), targetEntity.GetElementId());
            }
            return false;
        }

        private bool CheckActorRid(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            return targetEntity.rid == conditionContext.conditionInfo.param1;
        }

        private bool CheckMark(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var markList = skillContext.owner.entity.GetTeam().GetDecisionMarkList();
            foreach (var mark in markList)
            {
                if (mark.markId == TeamDecisionMarkId.Focus
                    || mark.markId == TeamDecisionMarkId.Control
                    || mark.markId == TeamDecisionMarkId.Protect)
                {
                    if (mark.entityUid == targetEntity.uid)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool CheckInDanger(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var entityContext = skillContext.owner;
            var decisionContext = entityContext.owner;
            var dangerPosCollection = decisionContext.dangerPosCollection;

            var locationPos = targetEntity.GetLocation();
            var occupySize = targetEntity.GetOccupySize();
            var occupyOffset = (occupySize - 1) / 2;
            for (int i = -occupyOffset; i <= occupyOffset; ++i)
            {
                for (int j = -occupyOffset; j <= occupyOffset; ++j)
                {
                    GridPosition pos = locationPos + new GridPosition(i, j);
                    if (dangerPosCollection.ContainsPos(pos))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool CheckOutAssistGuard(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var locationPos = targetEntity.GetLocation();
            var occupySize = targetEntity.GetOccupySize();
            var occupyOffset = (occupySize - 1) / 2;
            for (int i = -occupyOffset; i <= occupyOffset; ++i)
            {
                for (int j = -occupyOffset; j <= occupyOffset; ++j)
                {
                    GridPosition pos = locationPos + new GridPosition(i, j);
                    if (skillContext.owner.assistGuardPosCollection.ContainsPos(pos))
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private bool CheckCounterDistLessThanSkillRange(IEntity targetEntity, BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection)
        {
            var targetCounterSkill = targetEntity.GetCounterAttack();
            if (targetCounterSkill == null || targetCounterSkill.CanCastSkill() != BattleErrorCode.Ok)
            {
                return true;
            }
            var extraRange = targetCounterSkill.GetExtraSelectRange();
            using var paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
            var isInCounterRange = TargetSelectUtility.CheckSelectable(m_battle, targetCounterSkill.skillInfo.selectStep, targetEntity, targetEntity.GetLocation(), extraRange, paramContainer, true, selection.movePos);
            return !isInCounterRange;
        }

        private int GetTargetDistanceForCompare(IEntity originEntity, IEntity targetEntity)
        {
            return originEntity.GetLocation().DistanceTo(targetEntity.GetLocation());
        }

        private bool CheckCondition(BattleTeamDecisionConditionInfo condition, IEntity originEntity)
        {
            switch (condition.funcType)
            {
                case TeamDecisionConditionFuncId.SelfHpRate:
                    return CompareValue(condition.compareType, originEntity.GetCurHpRate(), condition.param1);
                case TeamDecisionConditionFuncId.SelfTeamEnergy:
                    return CheckValue(condition.compareType, originEntity.GetTeam().sharedEnergy, condition.param1);
                case TeamDecisionConditionFuncId.SelfLeftEntityCount:
                    return CompareValue(condition.compareType, originEntity.GetTeam().entityUidList.Count, condition.param1);
                case TeamDecisionConditionFuncId.SelfCareerNotExist:
                    {
                        foreach (var entityUid in originEntity.GetTeam().entityUidList)
                        {
                            var entity = m_battle.GetEntityByUid(entityUid);
                            if (entity.entityType != EntityType.Actor)
                            {
                                continue;
                            }
                            var actorGetter = entity.dataGetter as IActorDataGetter;
                            if (actorGetter.GetCareerId() == condition.careerId)
                            {
                                return false;
                            }
                        }
                        return true;
                    }
                case TeamDecisionConditionFuncId.SelfEntityRepc:
                    var actorDataGetter = originEntity.dataGetter as IActorDataGetter;
                    if (actorDataGetter != null)
                    {
                        return actorDataGetter.GetRespType() == condition.respType;
                    }
                    return false;
                case TeamDecisionConditionFuncId.SelfFriendInDanger:
                    return CheckFriendInDanager(originEntity);
            }
            return true;
        }

        private bool CompareValue(TeamDecisionCompareType compareType, FixedValue srcValue, FixedValue compareValue)
        {
            switch (compareType)
            {
                case TeamDecisionCompareType.Greater:
                    return srcValue > compareValue;
                case TeamDecisionCompareType.GreaterEqual:
                    return srcValue >= compareValue;
                case TeamDecisionCompareType.Less:
                    return srcValue < compareValue;
                case TeamDecisionCompareType.LessEqual:
                    return srcValue < compareValue;
            }
            return false;
        }

        private bool CheckValue(TeamDecisionCompareType compareType, int srcValue, int compareValue)
        {
            switch (compareType)
            {
                case TeamDecisionCompareType.Greater:
                    return srcValue > compareValue;
                case TeamDecisionCompareType.GreaterEqual:
                    return srcValue >= compareValue;
                case TeamDecisionCompareType.Less:
                    return srcValue < compareValue;
                case TeamDecisionCompareType.LessEqual:
                    return srcValue < compareValue;
            }
            return false;
        }

        private bool CheckFriendInDanager(IEntity originEntity)
        {
            var posCollection = m_battle.CreateFieldSummaryForPosCollection();
            BattleUtility.CollectDangerGridListOfOtherCamp(m_battle, originEntity.GetBattleCampId(), posCollection);
            bool result = false;
            foreach (var entityUid in originEntity.GetTeam().entityUidList)
            {
                var entity = m_battle.GetEntityByUid(entityUid);
                if (entityUid == originEntity.uid)
                {
                    continue;
                }
                var pos = entity.GetLocation();
                int occupySize = entity.GetOccupySize();
                int occupyOffset = (occupySize - 1) / 2;
                for (int i = -occupyOffset; i <= occupyOffset; ++i)
                {
                    for (int j = -occupyOffset; j <= occupyOffset; ++j)
                    {
                        var checkPos = pos + new GridPosition(i, j);
                        if (posCollection.ContainsPos(checkPos))
                        {
                            result = true;
                            break;
                        }
                    }
                    if (result)
                    {
                        break;
                    }
                }
                if (result)
                {
                    break;
                }
            }
            posCollection.Release();
            return result;
        }

        private void CollectMoveRange(IEntity originEntity, BattleFieldSummaryForPosCollection moveRangePosCollection, GridPosition? exactMovePos)
        {
            if (!originEntity.BanMove())
            {
                var rangeFindResult = MovePathFindUtility.FindRange(originEntity.GetLocation(), originEntity.GetMovePathRule());
                foreach (var gridResult in rangeFindResult.gridResultList)
                {
                    if (!gridResult.canLocate)
                    {
                        continue;
                    }
                    if (exactMovePos.HasValue && exactMovePos.Value != gridResult.pos)
                    {
                        continue;
                    }
                    moveRangePosCollection.AddPos(gridResult.pos);
                }
                rangeFindResult.Release();
            }
            else
            {
                if (!exactMovePos.HasValue || exactMovePos.Value == originEntity.GetLocation())
                {
                    moveRangePosCollection.AddPos(originEntity.GetLocation());
                }
            }
        }

        private void CollectSelectionList(BattleTeamDecisionSkillContext skillContext)
        {
            var entityContext = skillContext.owner;
            var originEntity = entityContext.entity;
            var selectedSkill = skillContext.skill;
            var moveRangePosCollection = entityContext.moveRangePosCollection;
            if (selectedSkill != null)
            {
                var extraRange = selectedSkill.GetExtraSelectRange();
                using var paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
                using var skillRangePosCollection = m_battle.CreateFieldSummaryForPosCollection();
                var skillInfo = selectedSkill.skillInfo;
                var selectStep = skillInfo.selectStep;
                //TODO : RRR skillInfo.targetSelectStepInfoList.Count <= 1
                foreach (var moveRangePos in moveRangePosCollection.GetPosList())
                {
                    TargetSelectUtility.AppendFirstRange(m_battle, selectStep, originEntity, moveRangePos, extraRange, skillRangePosCollection);
                    var prePos = originEntity.GetLocation();
                    originEntity.SetLocation(moveRangePos);
                    foreach (var skillRangePos in skillRangePosCollection.GetPosList())
                    {
                        if (TargetSelectUtility.CheckSelectable(m_battle, selectStep, originEntity, moveRangePos, extraRange, paramContainer, false, skillRangePos))
                        {
                            var selection = m_battle.FetchObj<BattleTeamDecisionSelection>();
                            selection.movePos = moveRangePos;
                            selection.stepPosList.Add(skillRangePos);
                            skillContext.selectionList.Add(selection);
                        }
                    }
                    originEntity.SetLocation(prePos);
                    skillRangePosCollection.Reset();
                }
            }
            else
            {
                foreach (var moveRangePos in moveRangePosCollection.GetPosList())
                {
                    var selection = m_battle.FetchObj<BattleTeamDecisionSelection>();
                    selection.movePos = moveRangePos;
                    skillContext.selectionList.Add(selection);
                }
            }
        }

        private void CollectEntityListOfSkill(BattleTeamDecisionConditionContext conditionContext, BattleTeamDecisionSkillContext skillContext, BattleTeamDecisionSelection selection, List<IEntity> targetEntityList)
        {
            var entityContext = skillContext.owner;
            var originEntity = entityContext.entity;
            var selectedSkill = skillContext.skill;
            var itemContext = conditionContext.owner;

            var paramContainer = m_battle.FetchObj<TargetSelectParamContainer>();
            foreach (var pos in selection.stepPosList)
            {
                paramContainer.PushGridPosition(pos);
            }
            GridPosition prePos = originEntity.GetLocation();
            originEntity.SetLocation(selection.movePos, false);
            //if selectedSkill is null, Add the TeamDecisionConditionFuncId to CheckPosCondition
            BattleVagueParamValueSet valueSet = SkillForcastUtiltiy.GetForcastValueSet(m_battle, selectedSkill, paramContainer);
            var mainEffectInfo = selectedSkill.skillInfo.effectList.GetValueSafely(selectedSkill.skillInfo.mainEffectIndex);
            using var effectHandler = BattleFactory.CreateSkillEffectHandler(battle, mainEffectInfo.effectType);
            effectHandler.CollectEntity(mainEffectInfo, BattleActionEffectConditionAriseType.SkillEffect, valueSet, targetEntityList);
            if (itemContext.itemInfo.id != (int)TeamDecisionId.ArrayAdvance
                && itemContext.itemInfo.id != (int)TeamDecisionId.OffenseFirst
                //&& context.decisionId != (int)TeamDecisionId.SurvivalFirst
                )
            {
                for (int i = targetEntityList.Count - 1; i >= 0; i--)
                {
                    if (!targetEntityList[i].IsControllable())
                    {
                        targetEntityList.RemoveAt(i);
                    }
                }
            }
            paramContainer.Release();
            valueSet.Release();
            originEntity.SetLocation(prePos);
        }

        private bool UpdateTeamAggressive()
        {
            if (m_team.isAggressive)
            {
                return true;
            }
            var dangerPosCollection = m_battle.CreateFieldSummaryForPosCollection();
            BattleUtility.CollectDangerGridListOfTeam(m_battle, m_team.uid, dangerPosCollection);
            foreach (var gridPos in dangerPosCollection.GetPosList())
            {
                List<IEntity> entityList = m_battle.stageManageComponent.GetEntityListByFieldSummary(gridPos);
                foreach (var entity in entityList)
                {
                    if (entity.entityType != EntityType.Actor)
                    {
                        continue;
                    }
                    if (entity.GetBattleCampId() != m_team.campId)
                    {
                        m_team.isAggressive = true;
                        break;
                    }
                }
                if (m_team.isAggressive)
                {
                    break;
                }
            }
            m_battle.Release(dangerPosCollection);
            return m_team.isAggressive;
        }

        private void CollectGridPosListInAssistGuard(IEntity originEntity, BattleFieldSummaryForPosCollection fieldSummary)
        {
            int originCampId = originEntity.GetBattleCampId();
            foreach (var entity in m_battle.GetEntityList())
            {
                if (entity.GetBattleCampId() == originCampId)
                {
                    int buffRid;
                    TargetSelectRangeId rangeId;
                    TargetSelectTargetFilterType filterFuncType;
                    if (entity.TryGetTopBuffAssistGuardState(out buffRid, out rangeId, out filterFuncType, null))
                    {
                        if (!TargetSelectUtility.CheckFilter(filterFuncType, new TargetSelectFilterContext(entity), originEntity))
                        {
                            continue;
                        }
                        TargetSelectUtility.AppendRangePosList(m_battle, rangeId, new TargetSelectInfo(entity), GridDirType.None, fieldSummary);
                    }
                }
            }
        }

        private int GetAssistGuardEntityCountInDanger(BattleTeamDecisionSelection selection, BattleTeamDecisionSkillContext skillContext)
        {
            var selectedSkill = skillContext.skill;
            var entityContext = skillContext.owner;
            var originEntity = entityContext.entity;
            if (selectedSkill != null)
            {
                foreach (var effectInfo in selectedSkill.skillInfo.effectList)
                {
                    if (effectInfo.effectType != SkillEffectFuncType.AttachBuff_Rid)
                    {
                        continue;
                    }
                    var attachBuffRidInfo = effectInfo as SkillEffectInfo_AttachBuff_Rid;
                    foreach (var buffItem in attachBuffRidInfo.itemList)
                    {
                        var buffInfo = m_battle.infoGetter.GetBuffInfo(buffItem.buffRid);
                        if (buffInfo == null)
                        {
                            continue;
                        }
                        foreach (var buffEffectInfo in buffInfo.effectList)
                        {
                            if (buffEffectInfo.effectType != BuffEffectType.StateApply)
                            {
                                continue;
                            }
                            var stateApplyInfo = buffEffectInfo as BuffEffectInfo_StateApply;
                            if (stateApplyInfo.stateType != BuffEffectStateType.AssistGuard)
                            {
                                continue;
                            }
                            var guardInfo = stateApplyInfo as BuffEffectInfo_AssistGuard;
                            return GetAssistGuardEntityCountInDanger(guardInfo.rangeId, new TargetSelectInfo(selection.movePos), GridDirType.None, guardInfo.filterFuncType, new TargetSelectFilterContext(originEntity), skillContext);
                        }
                    }
                }
            }
            int buffRid;
            TargetSelectRangeId rangeId;
            TargetSelectTargetFilterType filterFuncType;
            if (originEntity.TryGetTopBuffAssistGuardState(out buffRid, out rangeId, out filterFuncType, null))
            {
                return GetAssistGuardEntityCountInDanger(rangeId, new TargetSelectInfo(selection.movePos), GridDirType.None, filterFuncType, new TargetSelectFilterContext(originEntity), skillContext);
            }
            return 0;
        }

        private int GetAssistGuardEntityCountInDanger(TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, TargetSelectTargetFilterType filterFuncType, TargetSelectFilterContext filterContext, BattleTeamDecisionSkillContext skillContext)
        {
            var entityContext = skillContext.owner;
            var decisionContext = entityContext.owner;
            var dangerPosCollection = decisionContext.dangerPosCollection;
            var originEntity = entityContext.entity;
            TargetSelectRangeInfo rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(m_battle, rangeId);
            if (rangeInfo == null)
            {
                return 0;
            }
            int count = 0;
            var summaryForPosCollection = m_battle.CreateFieldSummaryForPosCollection();
            TargetSelectUtility.AppendRangePosList(m_battle, rangeInfo, originSelectInfo, dirType, summaryForPosCollection);
            foreach (var gridPos in summaryForPosCollection.GetPosList())
            {
                if (!dangerPosCollection.ContainsPos(gridPos))
                {
                    continue;
                }
                if (TargetSelectUtility.CheckFilter(m_battle, filterFuncType, filterContext, gridPos))
                {
                    var e = m_battle.GetFirstEntityByPos(gridPos, EntityType.Actor);
                    if (e != originEntity)
                    {
                        count++;
                    }
                }
            }
            m_battle.Release(summaryForPosCollection);
            return count;
        }

        private int GetAssistGuardPos(TargetSelectRangeId rangeId, TargetSelectInfo originSelectInfo, GridDirType dirType, TargetSelectFilterFuncType filterFuncType, TargetSelectFilterContext filterContext, BattleTeamDecisionConditionContext context, BattleTeamDecisionSkillContext skillContext)
        {
            TargetSelectRangeInfo rangeInfo = TargetSelectUtility.GetTargetSelectRangeInfo(m_battle, rangeId);
            if (rangeInfo == null)
            {
                return 0;
            }
            int count = 0;
            var summaryForPosCollection = m_battle.CreateFieldSummaryForPosCollection();
            TargetSelectUtility.AppendRangePosList(m_battle, rangeInfo, originSelectInfo, dirType, summaryForPosCollection);
            foreach (var gridPos in summaryForPosCollection.GetPosList())
            {
                if (!skillContext.owner.owner.dangerPosCollection.ContainsPos(gridPos))
                {
                    continue;
                }
                if (TargetSelectUtility.CheckFilter(m_battle, filterFuncType, filterContext, gridPos))
                {
                    count++;
                }
            }
            m_battle.Release(summaryForPosCollection);
            return count;
        }

        private bool MoveToNearestEnemy(IEntity originEntity, out BattleTeamDecisionResult decisionResult)
        {
            if (originEntity.BanMove())
            {
                decisionResult = null;
                return false;
            }
            var entity = BattleUtility.GetFirstEntityNeedAct(m_battle);
            var moveRule = m_battle.FetchObj<EntityMovePathRuleForFindNearestTarget>();
            moveRule.Init(entity, TargetSelectFilterFuncType.RivalActor);
            var result = MovePathFindUtility.FindPathToNearestTarget(entity.GetLocation(), moveRule);
            GridPosition movePos = default;
            bool findPos = false;
            var lastGridResultInRange = GetLastGridResultInRange(result, moveRule);
            if (lastGridResultInRange != null)
            {
                if (entity.CheckLocatable(lastGridResultInRange.pos))
                {
                    findPos = true;
                    movePos = lastGridResultInRange.pos;
                }
                else if (result.gridResultList.Count > 0)
                {
                    var lastPos = result.gridResultList.GetLastValueSafely().pos;
                    var result2 = MovePathFindUtility.FindNearestPathInRange(entity.GetLocation(), lastPos, entity.GetMovePathRule());
                    lastGridResultInRange = GetLastLocatableGridResultInRange(result2, entity.GetMovePathRule());
                    if (lastGridResultInRange != null)
                    {
                        findPos = true;
                        movePos = lastGridResultInRange.pos;
                    }
                    result2.Release();
                    //var blockPosCollection = m_battle.CreateFieldSummaryForPosCollection();
                    //foreach (var e in m_battle.GetEntityList())
                    //{
                    //    if (TargetSelectUtility.CheckFilter(TargetSelectFilterFuncType.RivalActor, new TargetSelectFilterContext(originEntity), e))
                    //    {
                    //        e.CollectOccupiedPos(blockPosCollection);
                    //    }
                    //}
                    //var moveRule2 = m_battle.FetchObj<EntityMovePathRuleForFindNearestTargetFriendBlock>();
                    //moveRule2.Init(entity, blockPosCollection);
                    //blockPosCollection.AddPos(lastGridResultInRange.pos);
                    //while (true)
                    //{
                    //    var result2 = MovePathFindUtility.FindPathAnyway(entity.GetLocation(), lastPos, moveRule2);
                    //    lastGridResultInRange = GetLastGridResultInRange(result2, moveRule2);
                    //    if(lastGridResultInRange == null)
                    //    {
                    //        break;
                    //    }
                    //    if (lastGridResultInRange.canLocate)
                    //    {
                    //        findPos = true;
                    //        movePos = lastGridResultInRange.pos;
                    //        break;
                    //    }
                    //    blockPosCollection.AddPos(lastGridResultInRange.pos);
                    //    result2.Release();
                    //}
                    //blockPosCollection.Release();
                    //moveRule2.Release();
                }
            }
            result.Release();
            moveRule.Release();

            if (findPos)
            {
                decisionResult = m_battle.FetchObj<BattleTeamDecisionResult>();
                decisionResult.entityUid = originEntity.uid;
                decisionResult.movePos = movePos;
                decisionResult.isCastSkill = false;
                return true;
            }
            decisionResult = null;
            return false;
        }

        private MovePathGridResult GetLastGridResultInRange(MovePathFindResult result, MovePathRule rule)
        {
            if (!result.success)
            {
                return null;
            }
            MovePathGridResult lastGridResultInRange = null;
            foreach (var gridResult in result.gridResultList)
            {
                if (lastGridResultInRange == null)
                {
                    lastGridResultInRange = gridResult;
                }
                else if (rule.CheckG(gridResult.g))
                {
                    lastGridResultInRange = gridResult;
                }
                else
                {
                    break;
                }
            }
            return lastGridResultInRange;
        }

        private MovePathGridResult GetLastLocatableGridResultInRange(MovePathFindResult result, MovePathRule rule)
        {
            if (!result.success)
            {
                return null;
            }
            int index = 0;
            int g = 0;
            for (int i = 0; i < result.gridResultList.Count; ++i)
            {
                var gridResult = result.gridResultList[i];
                var nextGridResult = result.gridResultList.GetValueSafely(i + 1);
                if (nextGridResult == null)
                {
                    index = i;
                    break;
                }
                var newG = g + rule.CalcG(gridResult.pos, nextGridResult.pos, i == 0);
                if (!rule.CheckG(newG))
                {
                    index = i;
                    break;
                }
                g = newG;
            }
            for (int i = index; i >= 0; i--)
            {
                var gridResult = result.gridResultList[i];
                if (gridResult.canLocate)
                {
                    return gridResult;
                }
            }
            return null;
        }

        private bool TryGetNearestPos(IEntity originEntity, GridPosition targetPos, out GridPosition movePos)
        {
            GridPosition moveTargetPos;
            GridPosition originEntityPos = originEntity.GetLocation();
            if (TryGetNearestLocatableGridPos(originEntity, targetPos, out moveTargetPos))
            {
                var pathFindResult = MovePathFindUtility.FindPathAnyway(originEntityPos, moveTargetPos, originEntity.GetMovePathRule());
                var rangeFindResult = MovePathFindUtility.FindRange(originEntityPos, originEntity.GetMovePathRule());
                MovePathGridResult lastLocatableGridResult = null;
                for (int i = 0; i < pathFindResult.gridResultList.Count; ++i)
                {
                    var gridResult = pathFindResult.gridResultList[i];
                    if (rangeFindResult.gridResultList.Find(x => x.pos == gridResult.pos) == null)
                    {
                        break;
                    }
                    lastLocatableGridResult = gridResult;
                }
                GridPosition findPos = default;
                bool findResult = false;
                if (lastLocatableGridResult != null)
                {
                    findResult = true;
                    findPos = originEntityPos;
                    for (int i = 0; i < rangeFindResult.gridResultList.Count; ++i)
                    {
                        var gridResult = rangeFindResult.gridResultList[i];
                        if (!gridResult.canLocate)
                        {
                            continue;
                        }
                        if (gridResult.pos.DistanceTo(lastLocatableGridResult.pos) <= findPos.DistanceTo(lastLocatableGridResult.pos) && gridResult.pos.DistanceTo(originEntityPos) >= findPos.DistanceTo(originEntityPos))
                        {
                            findPos = gridResult.pos;
                        }
                    }
                }
                pathFindResult.Release();
                rangeFindResult.Release();
                if (findResult)
                {
                    movePos = findPos;
                    return true;
                }
            }
            movePos = default;
            return false;
        }

        private bool TryGetNearestLocatableGridPos(IEntity entity, GridPosition pos, out GridPosition targetPos)
        {
            GridPosition entityPos = entity.GetLocation();
            var fieldSummary = m_battle.CreateFieldSummaryForPosCollection();
            TargetSelectUtility.AppendRangePosList(m_battle, TargetSelectRangeId.Rhomb11, new TargetSelectInfo(pos), GridDirType.None, fieldSummary);
            GridPosition? nearestPos = null;
            foreach (var gridPos in fieldSummary.GetPosList())
            {
                if (entity.CheckLocatable(gridPos))
                {
                    if (nearestPos == null)
                    {
                        nearestPos = gridPos;
                    }
                    else if (gridPos.DistanceTo(entityPos) < nearestPos.Value.DistanceTo(entityPos))
                    {
                        var result = MovePathFindUtility.FindPathAnyway(entityPos, gridPos, entity.GetMovePathRule());
                        if (result.success)
                        {
                            nearestPos = gridPos;
                        }
                        result.Release();
                    }
                }
            }
            fieldSummary.Release();
            if (nearestPos.HasValue)
            {
                targetPos = nearestPos.Value;
                return true;
            }
            targetPos = default;
            return false;
        }

        private bool StayOrigin(IEntity originEntity, out BattleTeamDecisionResult decisionResult)
        {
            decisionResult = m_battle.FetchObj<BattleTeamDecisionResult>();
            decisionResult.entityUid = originEntity.uid;
            decisionResult.isCastSkill = false;
            decisionResult.movePos = originEntity.GetLocation();
            return true;
        }

        private BattleTeamDecisionContext CreateDecisionContext(BattleTeamDecisionInfo decisionInfo)
        {
            var context = m_battle.FetchObj<BattleTeamDecisionContext>();
            context.decisionInfo = decisionInfo;
            context.dangerPosCollection = m_battle.CreateFieldSummaryForPosCollection();
            BattleUtility.CollectDangerGridListOfOtherCamp(m_battle, m_team.campId, context.dangerPosCollection);
            return context;
        }

        private BattleTeamDecisionEntityContext CreateEntityContext(BattleTeamDecisionContext context, IEntity entity, GridPosition? exactMovePos)
        {
            if (!context.entityMap.TryGetValue(entity.uid, out var entityContext))
            {
                entityContext = m_battle.FetchObj<BattleTeamDecisionEntityContext>();
                entityContext.entity = entity;
                entityContext.owner = context;
                entityContext.assistGuardPosCollection = m_battle.CreateFieldSummaryForPosCollection();
                entityContext.moveRangePosCollection = m_battle.CreateFieldSummaryForPosCollection();
                CollectGridPosListInAssistGuard(entity, entityContext.assistGuardPosCollection);
                CollectMoveRange(entity, entityContext.moveRangePosCollection, exactMovePos);
                context.entityMap.Add(entity.uid, entityContext);
            }
            return entityContext;
        }

        private BattleTeamDecisionSkillContext CreateSkillContext(BattleTeamDecisionEntityContext entityContext, Skill skill)
        {
            int skillUid = 0;
            if (skill != null)
            {
                skillUid = skill.uid;
            }
            if (!entityContext.skillMap.TryGetValue(skillUid, out var skillContext))
            {
                skillContext = m_battle.FetchObj<BattleTeamDecisionSkillContext>();
                skillContext.skill = skill;
                skillContext.owner = entityContext;
                CollectSelectionList(skillContext);
                entityContext.skillMap.Add(skillUid, skillContext);
            }
            return skillContext;
        }

        private BattleTeamDecisionItemContext CreateItemContext(BattleTeamDecisionContext decisionContext, BattleTeamDecisionItemInfo itemInfo)
        {
            var battle = decisionContext.battle;
            var itemContext = battle.FetchObj<BattleTeamDecisionItemContext>();
            itemContext.itemInfo = itemInfo;
            itemContext.owner = decisionContext;
            return itemContext;
        }

        private BattleTeamDecisionConditionContext CreateConditionContext(BattleTeamDecisionItemContext itemContext, BattleTeamDecisionConditionInfo conditionInfo)
        {
            var battle = itemContext.owner.battle;
            var conditionContext = battle.FetchObj<BattleTeamDecisionConditionContext>();
            conditionContext.conditionInfo = conditionInfo;
            conditionContext.owner = itemContext;
            return conditionContext;
        }
    }
}
