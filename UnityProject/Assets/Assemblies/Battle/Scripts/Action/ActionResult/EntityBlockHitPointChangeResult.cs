using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityBlockHitPointChangeResult : BattleActionResult
    {
        public int entityUid;
        public List<EntityBlockHitPointChangeResultClip> clipList = new List<EntityBlockHitPointChangeResultClip>();

        public override void OnRelease()
        {
            entityUid = default;
            clipList.ReleaseAll();
            base.OnRelease();
        }
    }
}
