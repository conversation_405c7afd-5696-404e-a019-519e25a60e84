using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TerrainEffectTriggerResult : BattleActionResult
    {
        public List<BattleActionEffectResult> effectResultList = new List<BattleActionEffectResult>();

        public override void OnRelease()
        {
            effectResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
