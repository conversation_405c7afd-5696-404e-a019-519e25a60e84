using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TerrainTriggerResult : BattleActionResult
    {
        public bool isTerrainBuff;
        public int terrainBuffUid;
        public int terrainRid;
        public int entityUid;
        public int entityRid;
        public TerrainLogicInfo logicInfo;
        public TerrainEffectTriggerResult triggerResult;
        public bool destroy;

        public override void OnRelease()
        {
            entityUid = default;
            entityRid = default;
            isTerrainBuff = default;
            terrainBuffUid = default;
            logicInfo = default;
            if (triggerResult != null)
            {
                triggerResult.Release();
                triggerResult = null;
            }
            destroy = false;
            base.OnRelease();
        }
    }
}
