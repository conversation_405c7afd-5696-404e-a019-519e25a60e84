using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TerrainEnterResult : BattleActionResult
    {
        public List<BuffAttachResult> buffAttachResultList = new List<BuffAttachResult>();

        public override void OnRelease()
        {
            buffAttachResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
