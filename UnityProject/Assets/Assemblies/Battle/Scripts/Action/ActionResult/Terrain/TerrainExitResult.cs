using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TerrainExitResult : BattleActionResult
    {
        public bool isTerrainBuff;
        public int terrainBuffUid;
        public int terrainRid;
        public int entityUid;
        public List<BuffDetachResult> buffDetachResultList = new List<BuffDetachResult>();

        public override void OnRelease()
        {
            isTerrainBuff = default;
            terrainBuffUid = default;
            terrainRid = default;
            buffDetachResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
