using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleStageActionEntitySummonResult : BattleActionResult
    {
        public List<BattleStageActionEntitySummonPartResult> partResultList = new List<BattleStageActionEntitySummonPartResult>();
        public List<BuffChangeActiveResult> buffChangeActiveResultList = new List<BuffChangeActiveResult>();

        public override void OnRelease()
        {
            partResultList.ReleaseAll();
            buffChangeActiveResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
