using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class BattleStageActionEntitySummonPartResult : BattleActionResult
    {
        public Entity entity;
        public List<BuffAttachResult> buffAttachResultList = new List<BuffAttachResult>();
        public BattleStageActionSummonActorItemInfo info;

        public override void OnRelease()
        {
            info = null;
            entity.Release();
            entity = null;
            buffAttachResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
