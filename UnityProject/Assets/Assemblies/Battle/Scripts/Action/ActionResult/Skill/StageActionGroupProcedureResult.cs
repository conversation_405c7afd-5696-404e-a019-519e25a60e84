using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class StageActionGroupProcedureResult : BattleActionProcedureResult
    {
        public int triggerIndex;

        public override BattleActionProcedureType type
        {
            get { return BattleActionProcedureType.StageActionGroup; }
        }

        public override void OnRelease()
        {
            triggerIndex = default;
            base.OnRelease();
        }
    }
}
