using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityCastSkillProcedureResult : BattleActionProcedureResult
    {
        public int entityUid;
        public int entityRid;
        public int skillUid;
        public int skillRid;
        public int teamUid;
        public int updateEnergy;
        public FixedValue updateCurHp;
        public GridPosition targetPos;
        public GridDirType targetDir;
        public int targetEntityUid;
        public bool resetExtraMove;
        public bool resetExtraAction;
        public GridPosition movePos;

        public override BattleActionProcedureType type
        {
            get { return BattleActionProcedureType.CastSkill; }
        }

        public override void OnFetch()
        {
            targetPos = GridPosition.invalid;
            movePos = GridPosition.invalid;
            base.OnFetch();
        }

        public override void OnRelease()
        {
            entityUid = default;
            entityRid = default;
            skillUid = default;
            skillRid = default;
            teamUid = default;
            updateEnergy = default;
            updateCurHp = default;
            resetExtraMove = default;
            resetExtraAction = default;
            targetDir = GridDirType.None;
            movePos = default;
            targetPos = GridPosition.invalid;
            targetEntityUid = default;
            base.OnRelease();
        }
    }
}
