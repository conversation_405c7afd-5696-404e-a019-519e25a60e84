using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class MainBattleStartProcedureResult : BattleActionProcedureResult
    {
        public List<BuffAttachResult> buffAttachResultList = new List<BuffAttachResult>();
        public List<TeamEnergyChangeResult> teamEnergyChangeResultList = new List<TeamEnergyChangeResult>();
        public List<TeamMaxEnergyChangeResult> teamMaxEnergyChangeResultList = new List<TeamMaxEnergyChangeResult>();
        public List<BuffChangeActiveResult> buffChangeActiveResultList = new List<BuffChangeActiveResult>();

        public override BattleActionProcedureType type
        {
            get { return BattleActionProcedureType.MainBattleStart; }
        }

        public override void OnRelease()
        {
            buffAttachResultList.ReleaseAll();
            teamEnergyChangeResultList.ReleaseAll();
            teamMaxEnergyChangeResultList.ReleaseAll();
            buffChangeActiveResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
