using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class StageEnterProcedureResult : BattleActionProcedureResult
    {
        public int stageIndex;

        public override BattleActionProcedureType type
        {
            get { return BattleActionProcedureType.StageStart; }
        }

        public override void OnRelease()
        {
            stageIndex = default;
            base.OnRelease();
        }
    }
}
