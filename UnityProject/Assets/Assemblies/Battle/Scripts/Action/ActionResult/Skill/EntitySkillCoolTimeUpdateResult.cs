using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntitySkillCoolTimeUpdateResult : BattleActionResult
    {
        public int skillUid;
        public int cooltime;

        public override void OnRelease()
        {
            skillUid = default;
            cooltime = default;
            base.OnRelease();
        }
    }
}
