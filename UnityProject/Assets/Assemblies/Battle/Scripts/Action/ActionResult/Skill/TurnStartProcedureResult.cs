using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TurnStartProcedureResult : BattleActionProcedureResult
    {
        public int turnIndex;
        public List<TeamEnergyChangeResult> teamEnergyChangeResultList = new List<TeamEnergyChangeResult>();
        public List<EntityBlockHitPointChangeResult> blockHpChangeResultList = new List<EntityBlockHitPointChangeResult>();

        public override BattleActionProcedureType type
        {
            get { return BattleActionProcedureType.TurnStart; }
        }

        public override void OnRelease()
        {
            blockHpChangeResultList.ReleaseAll();
            teamEnergyChangeResultList.ReleaseAll();
            turnIndex = default;
            base.OnRelease();
        }
    }
}
