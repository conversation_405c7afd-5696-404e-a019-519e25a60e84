using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityActionEndResult : BattleActionResult
    {
        public int entityUid;
        public int transformLifeTime;
        public bool triggerExtraAction;
        public bool triggerExtraMove;
        public bool hasActionChance;
        public bool needChangeTeam;
        public int changeTeamUid;
        public List<BuffChangeResult> buffChangeResultList = new List<BuffChangeResult>();
        public List<EntitySkillCoolTimeUpdateResult> skillCoolTimeUpdateResultList = new List<EntitySkillCoolTimeUpdateResult>();
        public List<BuffCoolTimeUpdateResult> buffCoolTimeUpdateResultList = new List<BuffCoolTimeUpdateResult>();

        public bool triggerPreAnnounce;
        public int preAnnounceSkillUid;
        public List<GridPosition> preAnnounceStepPosList = new List<GridPosition>();

        public override void OnRelease()
        {
            entityUid = default;
            triggerExtraAction = false;
            triggerExtraMove = false;
            transformLifeTime = default;
            hasActionChance = default;
            needChangeTeam = default;
            changeTeamUid = default;
            buffChangeResultList.ReleaseAll();
            skillCoolTimeUpdateResultList.ReleaseAll();
            triggerPreAnnounce = false;
            preAnnounceSkillUid = default;
            preAnnounceStepPosList.Clear();
            base.OnRelease();
        }
    }
}
