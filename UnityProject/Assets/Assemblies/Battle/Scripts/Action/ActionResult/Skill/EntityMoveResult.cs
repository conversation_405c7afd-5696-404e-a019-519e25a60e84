using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityMoveResult : BattleActionResult
    {
        public int entityUid;
        public GridPosition movePos;

        public override void OnRelease()
        {
            entityUid = default;
            movePos = default;
            base.OnRelease();
        }
    }
}
