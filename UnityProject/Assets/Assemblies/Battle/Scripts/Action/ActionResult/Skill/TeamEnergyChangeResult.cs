using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TeamEnergyChangeResult : BattlePoolObj
    {
        public int teamUid;
        public int updateEnergy;

        public override void OnRelease()
        {
            teamUid = default;
            updateEnergy = default;
            base.OnRelease();
        }
    }
}
