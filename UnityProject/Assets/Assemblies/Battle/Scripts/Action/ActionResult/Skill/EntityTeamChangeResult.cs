using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityTeamChangeResult : BattleActionResult
    {
        public List<int> entityUidList = new List<int>();
        public int teamUid;

        public override void OnRelease()
        {
            entityUidList.Clear();
            teamUid = default;
            base.OnRelease();
        }
    }
}
