using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public abstract class BattleActionProcedureResult : BattleActionResult
    {
        public List<BattleActionResult> subResultList = new List<BattleActionResult>();

        public abstract BattleActionProcedureType type { get; }

        public override void OnRelease()
        {
            subResultList.ReleaseAll();
            base.OnRelease();
        }

        public virtual void AddResult(BattleActionResult result)
        {
            if(result != null)
            {
                subResultList.Add(result);
            }
        }
    }
}
