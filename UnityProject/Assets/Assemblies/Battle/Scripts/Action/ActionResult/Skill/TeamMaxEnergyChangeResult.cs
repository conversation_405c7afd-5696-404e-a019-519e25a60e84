using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TeamMaxEnergyChangeResult : BattlePoolObj
    {
        public int teamUid;
        public int updateMaxEnergy;

        public override void OnRelease()
        {
            //teamUid = default;
            //updateMaxEnergy = default;
            base.OnRelease();
        }
    }
}
