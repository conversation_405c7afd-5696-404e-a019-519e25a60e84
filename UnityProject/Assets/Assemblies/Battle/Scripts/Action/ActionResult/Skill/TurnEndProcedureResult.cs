using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class TurnEndProcedureResult : BattleActionProcedureResult
    {
        public int turnIndex;
        public List<BuffCoolTimeUpdateResult> buffCoolTimeUpdateResultList = new List<BuffCoolTimeUpdateResult>();

        public override BattleActionProcedureType type
        {
            get { return BattleActionProcedureType.TurnEnd; }
        }

        public override void OnRelease()
        {
            turnIndex = default;
            buffCoolTimeUpdateResultList.ReleaseAll();
            base.OnRelease();
        }
    }
}
