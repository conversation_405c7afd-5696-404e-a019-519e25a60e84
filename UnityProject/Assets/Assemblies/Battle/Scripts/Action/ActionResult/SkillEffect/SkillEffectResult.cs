using System.Collections;
using System.Collections.Generic;


namespace Phoenix.Battle
{
    public class SkillEffectResult : BattleActionResult
    {
        public int id;
        public bool success;
        public TargetSelectResult selectResult;
        public List<EntityBlockHitPointChangeResult> blockHitPointChangeResultList = new List<EntityBlockHitPointChangeResult>();
        public List<SkillEffectResultClip> clipList = new List<SkillEffectResultClip>();
        public List<BuffChangeActiveResult> buffChangeActiveResultList = new List<BuffChangeActiveResult>();


        public override void OnRelease()
        {
            id = default;
            success = false;
            blockHitPointChangeResultList.ReleaseAll();
            buffChangeActiveResultList.ReleaseAll();
            clipList.ReleaseAll();
            base.OnRelease();
        }

        public void CollectEntityUid(List<int> entityUidList)
        {
            foreach (var clip in clipList)
            {
                clip.CollectEntityUid(entityUidList);
            }
        }
    }
}
