using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;

namespace Phoenix.Battle
{
    public abstract class SkillEffectResultClip : BattleActionResult
    {
        public abstract SkillEffectFuncType effectType { get; }
        public SkillEffectResultType resultType;

        public virtual void CollectEntityUid(List<int> entityUidList) { }

        public override void OnRelease()
        {
            resultType = default;
            base.OnRelease();
        }
    }
}
