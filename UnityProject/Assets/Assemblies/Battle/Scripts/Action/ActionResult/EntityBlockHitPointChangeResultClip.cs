using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class EntityBlockHitPointChangeResultClip : BattleActionResult
    {
        public GridPosition pos;
        public int updateHitPoint;

        public override void OnRelease()
        {
            pos = default;
            updateHitPoint = default;
            base.OnRelease();
        }
    }
}
