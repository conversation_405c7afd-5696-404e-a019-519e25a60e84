using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleExtension
    {
        public static int GetCurStageIndex(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStageIndex();
            }
            return default;
        }
        
        public static void SetCurStageIndex(this IBattle battle, int index)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetCurStageIndex(index);
            }
        }
        
        public static BattleStageInfo GetCurStageInfo(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStageInfo();
            }
            return default;
        }

        public static void SetEntityMustAct(this IBattle battle, int entityUid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetEntityMustAct(entityUid);
            }
        }

        public static void ResetEntityMustAct(this IBattle battle, int entityUid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.ResetEntityMustAct(entityUid);
            }
        }

        public static bool CheckEntityMustAct(this IBattle battle, int entityUid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckEntityMustAct(entityUid);
            }
            return default;
        }
        
        public static List<int> GetMustActEntityUidList(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetMustActEntityUidList();
            }
            return default;
        }
        
        public static int GetCurTurnIndex(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurTurnIndex();
            }
            return default;
        }
        
        public static int GetCurStepIndex(this IBattle battle, int teamUid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStepIndex(teamUid);
            }
            return default;
        }
        
        public static void SetCurStepIndex(this IBattle battle, int teamUid, int stepIndex)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetCurStepIndex(teamUid, stepIndex);
            }
        }
        
        public static void ClearCurStepIndex(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.ClearCurStepIndex();
            }
        }
        
        public static void SetCurTurnIndex(this IBattle battle, int turnIndex)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetCurTurnIndex(turnIndex);
            }
        }
        
        public static int GetCurTeamIndex(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurTeamIndex();
            }
            return default;
        }
        
        public static void SetCurTeamIndex(this IBattle battle, int teamIndex)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetCurTeamIndex(teamIndex);
            }
        }
        
        public static List<BattleStageDispositionInfo> GetCurStageDispositionInfoList(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStageDispositionInfoList();
            }
            return default;
        }

        public static BattleStageDispositionInfo GetCurStageDispositionInfo(this IBattle battle, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStageDispositionInfo(pos);
            }
            return default;
        }

        public static int GetCurStageDispositionId(this IBattle battle, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStageDispositionId(pos);
            }
            return default;
        }
        
        public static int GetCurStageDispositionMaxCount(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetCurStageDispositionMaxCount();
            }
            return default;
        }
        
        public static bool CheckCurStageCanResurrect(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckCurStageCanResurrect();
            }
            return default;
        }
        
        public static void EnterStage_Sync(this IBattle battle, int index)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.EnterStage_Sync(index);
            }
        }
        
        public static void ExitStage_Sync(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.ExitStage_Sync();
            }
        }
        
        public static bool CanOccupyEntity(this IBattle battle, IEntityDataGetter entityData, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CanOccupyEntity(entityData, pos);
            }
            return default;
        }
        
        public static bool CheckStaticEntityUidConflict(this IBattle battle, int uid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckStaticEntityUidConflict(uid);
            }
            return default;
        }
        
        public static int GetMappingUid(this IBattle battle, int rid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetMappingUid(rid);
            }
            return default;
        }
        
        public static int GetTerrainRid(this IBattle battle, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetTerrainRid(pos);
            }
            return default;
        }
        
        public static bool CheckPosValid(this IBattle battle, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckPosValid(pos);
            }
            return default;
        }
        
        public static bool CanSetupFormation(this IBattle battle, GridPosition gridPosition)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CanSetupFormation(gridPosition);
            }
            return default;
        }
        
        public static void SetStageState_Sync(this IBattle battle, BattleStageStateId stateId)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetStageState_Sync(stateId);
            }
        }
        
        public static bool CheckPrepareState(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckPrepareState();
            }
            return default;
        }
        
        public static bool CheckMainBattleState(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckMainBattleState();
            }
            return default;
        }
        
        public static BattlePerformance TickLogic(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.TickLogic();
            }
            return default;
        }
        
        public static BattlePerformance EnterFirstStage(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.EnterFirstStage();
            }
            return default;
        }
        
        public static BattlePerformance StartStageActionGroup(this IBattle battle, int groupId, BattleVagueParamValueSet valueSet)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.StartStageActionGroup(groupId, valueSet);
            }
            return default;
        }
        
        public static BattlePerformance ChangeState(this IBattle battle, BattleStageStateId stateId)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.ChangeState(stateId);
            }
            return default;
        }

        public static void SetCampRefereeResult(this IBattle battle, int campId, bool result)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetCampRefereeResult(campId, result);
            }
        }

        public static bool HasCampRefereeResult(this IBattle battle, int campId)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.HasCampRefereeResult(campId);
            }
            return default;
        }

        public static bool TryGetCampRefereeResult(this IBattle battle, int campId, out bool result)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.TryGetCampRefereeResult(campId, out result);
            }
            result = default;
            return default;
        }

        public static bool HasAnyCampRefereeWin(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.HasAnyCampRefereeWin();
            }
            return default;
        }

        public static bool CheckReferee(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckReferee();
            }
            return default;
        }
        
        public static bool CheckAllPlayerReferee(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckAllPlayerReferee();
            }
            return default;
        }
        
        public static void SetAllPlayerReferee(this IBattle battle, bool flag)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.SetAllPlayerReferee(flag);
            }
        }
        
        public static bool CheckAnyPlayerWin(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckAnyPlayerWin();
            }
            return default;
        }
        
        public static bool CheckAnyPlayerLose(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckAnyPlayerLose();
            }
            return default;
        }
        
        public static bool CheckRuleLocatableByFieldSummary(this IBattle battle, int moveRuleRid, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CheckRuleLocatableByFieldSummary(moveRuleRid, pos);
            }
            return default;
        }
        
        public static BattleFieldSummaryOfOccupyEntity GetFieldSummaryOfOccupyEntity(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetFieldSummaryOfOccupyEntity();
            }
            return default;
        }
        
        public static void AddEntityToFieldSummary(this IBattle battle, IEntity entity, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.AddEntityToFieldSummary(entity, pos);
            }
        }
        
        public static void RemoveEntityToFieldSummary(this IBattle battle, IEntity entity, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.RemoveEntityToFieldSummary(entity, pos);
            }
        }
        
        public static void MoveEntityToFieldSummary(this IBattle battle, IEntity entity, GridPosition pre, GridPosition cur, bool setDependDirty)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.MoveEntityToFieldSummary(entity, pre, cur, setDependDirty);
            }
        }
        
        public static List<IEntity> GetEntityListByFieldSummary(this IBattle battle, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetEntityListByFieldSummary(pos);
            }
            return default;
        }
        
        public static BattleFieldSummaryForPosCollection CreateFieldSummaryForPosCollection(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.CreateFieldSummaryForPosCollection();
            }
            return default;
        }
        
        public static BattleFieldSummaryOfRuleMoveCost GetFieldSummaryOfRuleMoveCost(this IBattle battle, int moveRuleRid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetFieldSummaryOfRuleMoveCost(moveRuleRid);
            }
            return default;
        }
        
        public static BattleFieldSummaryOfRuleLocatable GetFieldSummaryOfRuleLocatable(this IBattle battle, int moveRuleRid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetFieldSummaryOfRuleLocatable(moveRuleRid);
            }
            return default;
        }
        
        public static BattleFieldSummaryOfRulePassable GetFieldSummaryOfRulePassable(this IBattle battle, int moveRuleRid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetFieldSummaryOfRulePassable(moveRuleRid);
            }
            return default;
        }
        
        public static BattleStageTerrainEffect AddTerrainEffect(this IBattle battle, int uid, int rid, GridPosition pos)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.AddTerrainEffect(uid, rid, pos);
            }
            return default;
        }
        
        public static void RemoveTerrainEffect(this IBattle battle, int uid)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                battle.stageManageComponent.RemoveTerrainEffect(uid);
            }
        }
        
        public static BattleStageTerrainEffect GetTerrainEffect(this IBattle battle, int id)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetTerrainEffect(id);
            }
            return default;
        }
        
        public static Dictionary<int, BattleStageTerrainEffect> GetTerrainEffectMap(this IBattle battle)
        {
            if (battle != null && battle.stageManageComponent != null)
            {
                return battle.stageManageComponent.GetTerrainEffectMap();
            }
            return default;
        }
    }
}
