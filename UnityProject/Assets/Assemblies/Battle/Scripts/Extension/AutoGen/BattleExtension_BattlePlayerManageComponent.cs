using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static partial class BattleExtension
    {
        public static List<BattlePlayer> GetPlayerList(this IBattle battle)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.GetPlayerList();
            }
            return default;
        }
        
        public static ulong GetDefaultPlayerId(this IBattle battle)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.GetDefaultPlayerId();
            }
            return default;
        }
        
        public static int GetDefaultPlayerSlotId(this IBattle battle)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.GetDefaultPlayerSlotId();
            }
            return default;
        }
        
        public static BattlePlayer GetPlayerBySlotId(this IBattle battle, int slotId)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.GetPlayerBySlotId(slotId);
            }
            return default;
        }
        
        public static bool HasPlayerBySlotId(this IBattle battle, int slotId)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.HasPlayerBySlotId(slotId);
            }
            return default;
        }
        
        public static BattlePlayer GetPlayerByPlayerId(this IBattle battle, ulong playerId)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.GetPlayerByPlayerId(playerId);
            }
            return default;
        }
        
        public static void AddPlayerSlotBySlotId(this IBattle battle, int slotId)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                battle.playerManageComponent.AddPlayerSlotBySlotId(slotId);
            }
        }
        
        public static void AddPlayerSlotByConfigData(this IBattle battle, BattlePlayerSlotInfo info)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                battle.playerManageComponent.AddPlayerSlotByConfigData(info);
            }
        }
        
        public static int GetPlayerSlotIdByPlayerId(this IBattle battle, ulong playerId)
        {
            if (battle != null && battle.playerManageComponent != null)
            {
                return battle.playerManageComponent.GetPlayerSlotIdByPlayerId(playerId);
            }
            return default;
        }
    }
}
