using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.ConfigData;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public abstract class BattleBase : IBattle
    {
        private BattleInfo m_battleInfo;
        private BattleInstanceType m_instanceType;
        private BattleInitData m_initData;
        private IBattleEventListener m_eventListener;
        private FixedRandom m_random = new FixedRandom();
        private List<BattleComponent> m_componentList = new List<BattleComponent>();
        private DebugTagNode m_debugTagNode;
        private ILogger m_logger;
        private IBattleInfoGetter m_infoGetter;
        private ClassPoolMap m_poolMap;
        private bool m_isEnd;

        private Dictionary<int, FrameCommandHandler> m_frameCommandHandler = new Dictionary<int, FrameCommandHandler>();
        private List<FrameCut> m_frameCutList = new List<FrameCut>();
        private List<byte> m_waitingPlayerSlotIdList = new List<byte>();
       
        private bool m_needTickLogic;
        private List<FrameCommandType> m_waitInputCommandTypeList = new List<FrameCommandType>();
        private bool m_waitPrepareEnd;

        private BattleObjIdComponent m_objIdComponent;
        private BattleVariableComponent m_variableComponent;
        private EntityManageComponent m_entityManageComponent;
        private BattlePlayerManageComponent m_playerManageComponent;
        private BattleTeamManageComponent m_teamManageComponent;
        private BattleTriggerManageComponent m_triggerManageComponent;
        private BattleDecisionComponent m_decisionComponent;
        private BattlePerformanceComponent m_performanceComponent;
        private BattleStageManageComponent m_stageManageComponent;
        private BattleRefereeComponent m_refereeComponent;
        private BattleStatisticComponent m_statisticComponent;

        public bool disableRandom;

        public BattleInfo battleInfo
        {
            get { return m_battleInfo; }
        }

        public BattleInitData initData
        {
            get { return m_initData; }
        }

        public IBattleEventListener eventListener
        {
            get { return m_eventListener; }
        }

        public ILogger logger
        {
            get { return m_logger; }
        }

        public IBattleInfoGetter infoGetter
        {
            get { return m_infoGetter; }
        }

        public BattleInstanceType instanceType
        {
            get { return m_instanceType; }
        }

        public BattleObjIdComponent objIdComponent { get { return m_objIdComponent; } }
        public BattleVariableComponent variableComponent { get { return m_variableComponent; } }
        public EntityManageComponent entityManageComponent { get { return m_entityManageComponent; } }
        public BattlePlayerManageComponent playerManageComponent { get { return m_playerManageComponent; } }
        public BattleTeamManageComponent teamManageComponent { get { return m_teamManageComponent; } }
        public BattleTriggerManageComponent triggerManageComponent { get { return m_triggerManageComponent; } }
        public BattleDecisionComponent decisionComponent { get { return m_decisionComponent; } }
        public BattlePerformanceComponent performanceComponent { get { return m_performanceComponent; } }
        public BattleStageManageComponent stageManageComponent { get { return m_stageManageComponent; } }
        public BattleRefereeComponent refereeComponent { get { return m_refereeComponent; } }
        public BattleStatisticComponent statisticComponent { get { return m_statisticComponent; } }

        static BattleBase()
        {
            TypeMethodUtility.AddMethodHolder(new TypeMethodUtility.MethodHolder<FixedValue>()
            {
                Add = (a, b) => (a + b),
                Minus = (a, b) => (a - b),
                Multiply = (a, b) => (a * b),
                Divide = (a, b) => (a / b),
                CheckEqual = (a, b) => a == b,
            });
        }

        public BattleBase(BattleInstanceType instanceType)
        {
            m_instanceType = instanceType;
        }

        public void Init(BattleInitData battleInitData, IBattleEventListener eventListener, IBattleInfoGetter infoGetter, ILogger logger, ClassPoolMap classPoolMap)
        {
            m_initData = battleInitData;
            m_poolMap = classPoolMap;
            m_eventListener = eventListener;
            m_infoGetter = infoGetter;
            m_logger = logger;
            m_battleInfo = infoGetter.GetBattleInfo(battleInitData.battleRid);
            m_random.InitBySeed(battleInitData.randomSeed);
            CreateComponents();
        }

        public void InitAllProcess()
        {
            for (int i = 0; i < BattleDefine.BattleInitProcessTypeCount; ++i)
            {
                InitProcess((BattleInitProcessType)i);
            }
        }

        public void UnInit()
        {
            m_random.Clear();
            for (int i = 0; i < m_componentList.Count; ++i)
            {
                m_componentList[i].UnInit();
            }
            m_waitInputCommandTypeList.Clear();
            m_componentList.Clear();
            ReleaseComponentList();
            m_debugTagNode.Release();
            m_debugTagNode = null;
            if (m_poolMap != null)
            {
                m_poolMap.ReleaseToCreater();
            }
        }

        protected void CreateComponents()
        {
            m_objIdComponent = AddComponent(CreateObjIdComponent());
            m_variableComponent = AddComponent(CreateVariableComponent());
            m_playerManageComponent = AddComponent(CreatePlayerManageComponent());

            m_entityManageComponent = AddComponent(CreateEntityManageComponent());
            m_teamManageComponent = AddComponent(CreateTeamManageComponent());
            m_stageManageComponent = AddComponent(CreateStageManageComponent());
            m_triggerManageComponent = AddComponent(CreateTriggerManageComponent());

            m_performanceComponent = AddComponent(CreatePerformanceComponent());
            m_decisionComponent = AddComponent(CreateDecisionComponent());

            m_refereeComponent = AddComponent(CreateRefereeComponent());
            m_statisticComponent = AddComponent(CreateStatisticComponent());
        }

        private void ReleaseComponentList()
        {
            ReleaseComponent(ref m_objIdComponent);
            ReleaseComponent(ref m_variableComponent);
            ReleaseComponent(ref m_playerManageComponent);
            ReleaseComponent(ref m_entityManageComponent);
            ReleaseComponent(ref m_teamManageComponent);
            ReleaseComponent(ref m_stageManageComponent);
            ReleaseComponent(ref m_triggerManageComponent);
            ReleaseComponent(ref m_performanceComponent);
            ReleaseComponent(ref m_decisionComponent);
            ReleaseComponent(ref m_refereeComponent);
            ReleaseComponent(ref m_statisticComponent);
        }

        private void InitProcess(BattleInitProcessType processType)
        {
            OnInitProcess(processType);
            for (int i = 0; i < m_componentList.Count; ++i)
            {
                m_componentList[i].InitProcess(processType);
            }
        }

        protected abstract BattleObjIdComponent CreateObjIdComponent();
        protected abstract BattleVariableComponent CreateVariableComponent();
        protected abstract EntityManageComponent CreateEntityManageComponent();
        protected abstract BattlePlayerManageComponent CreatePlayerManageComponent();
        protected abstract BattleTeamManageComponent CreateTeamManageComponent();
        protected abstract BattleTriggerManageComponent CreateTriggerManageComponent();
        protected abstract BattleDecisionComponent CreateDecisionComponent();
        protected abstract BattlePerformanceComponent CreatePerformanceComponent();
        protected abstract BattleStageManageComponent CreateStageManageComponent();
        protected abstract BattleRefereeComponent CreateRefereeComponent();
        protected abstract BattleStatisticComponent CreateStatisticComponent();

        protected virtual void OnInitProcess(BattleInitProcessType processType) { }

        private T AddComponent<T>(T component) where T : BattleComponent
        {
            m_componentList.Add(component);
            return component;
        }

        private void ReleaseComponent<T>(ref T component) where T : BattleComponent
        {
            if(component != null)
            {
                component.Release();
                component = null;
            }
        }

        public void End()
        {
            m_isEnd = true;
            m_eventListener?.End();
        }

        public DebugTagNode GetDebugTagNode()
        {
            if (m_debugTagNode == null)
            {
                m_debugTagNode = DebugTagNode.Create(this, null);
                m_debugTagNode.tag.Set("[Battle:{0}]", m_instanceType);
            }
            return m_debugTagNode;
        }

        public bool CheckRandomEnable()
        {
            return !disableRandom;
        }

        public bool RandomRate(FixedValue rate)
        {
            if (disableRandom)
            {
                return rate > 100;
            }
            FixedValue randomResult = m_random.NextFixedValue();
            return rate > randomResult * 100;
        }

        public int RandomInt(int min, int max)
        {
            if (max == min)
            {
                return min;
            }
            if (max < min)
            {
                RandomInt(max, min);
            }
            var result = min + (int)(m_random.NextFixedValue() * (max + 1 - min));
            if (result > max)
            {
                return max;
            }
            return result;
        }

        public bool NeedTickLogic()
        {
            return m_needTickLogic;
        }

        public void SetWaitInputCommandType(FrameCommandType cmdType)
        {
            m_waitInputCommandTypeList.Add(cmdType);
        }

        public List<FrameCommandType> GetWaitInputCommandTypeList()
        {
            return m_waitInputCommandTypeList;
        }

        public bool IsWaitInputCommand()
        {
            return m_waitInputCommandTypeList.Count > 0;
        }

        public void ClearWaitInputCommandTypeList()
        {
            m_waitInputCommandTypeList.Clear();
        }

        public void SetWaitPrepareEnd()
        {
            m_waitPrepareEnd = true;
            foreach (var player in m_playerManageComponent.GetPlayerList())
            {
                if (player.playerId == this.GetDefaultPlayerId())
                {
                    continue;
                }
                m_waitingPlayerSlotIdList.Add((byte)player.slotId);
            }
        }

        public void SetWaitPrepareEnd(int playerSlotId)
        {
            if (m_waitingPlayerSlotIdList.Count > 0)
            {
                m_waitingPlayerSlotIdList.Remove((byte)playerSlotId);
                if (m_waitingPlayerSlotIdList.Count == 0)
                {
                    m_waitPrepareEnd = false;
                }
            }
        }

        public bool IsWaitPrepareEnd()
        {
            return m_waitPrepareEnd;
        }

        public bool IsBattleEnd()
        {
            return m_isEnd;
        }

        public BattlePerformance TickLogic()
        {
            return stageManageComponent.TickLogic();
        }

        public IEntity GetEntityByUid(int uid)
        {
            return m_entityManageComponent.GetEntityByUid(uid);
        }

        public IEntity GetFirstEntityByPos(GridPosition pos, EntityType entityType)
        {
            return m_entityManageComponent.GetFirstEntityByPos(pos, entityType);
        }

        public BattleTeam GetTeamByUid(int uid)
        {
            return m_teamManageComponent.GetTeamByUid(uid);
        }

        public BattleTeam GetTeamByIndex(int index)
        {
            return m_teamManageComponent.GetTeamByIndex(index);
        }

        public FrameCommandHandler GetFrameCommandHander(FrameCommandType commandType)
        {
            if (!m_frameCommandHandler.TryGetValue((int)commandType, out FrameCommandHandler handler))
            {
                handler = FrameCommandHandlerFactory.CreateHandler(commandType);
                handler.Init(this);
                m_frameCommandHandler.Add((int)commandType, handler);
            }
            return handler;
        }

        public bool CheckFrameCommandNeedReverse(FrameCommandType commandType)
        {
            var handler = GetFrameCommandHander(commandType);
            return handler.needReserve;
        }

        public bool CheckFrameCommandNeedWaitPerformance(FrameCommandType commandType)
        {
            var handler = GetFrameCommandHander(commandType);
            return handler.needWaitPerformance;
        }

        public int GetFrameCutCount()
        {
            return m_frameCutList.Count;
        }

        public List<FrameCut> GetFrameCutList()
        {
            return m_frameCutList;
        }

        public BattleErrorCode CheckFrameCut(FrameCut cut)
        {
            return CheckFrameCutInternal(cut, 0, false);
        }

        public BattleErrorCode CheckFrameCut(FrameCut cut, int playerSlot)
        {
            return CheckFrameCutInternal(cut, playerSlot, true);
        }

        private BattleErrorCode CheckFrameCutInternal(FrameCut cut, int playerSlot, bool checkPlayerControlable)
        {
            if (m_isEnd)
            {
                return BattleErrorCode.BattleAlreadyEnd;
            }
            if (cut == null)
            {
                return BattleErrorCode.FrameCutIsNull;
            }
            if (cut.cmd == null)
            {
                return BattleErrorCode.FrameCommandIsNull;
            }
            var handler = GetFrameCommandHander(cut.cmd.commandType);
            if (handler == null)
            {
                return BattleErrorCode.NotRealizedCommandType;
            }
            return handler.Check(cut.cmd, playerSlot, checkPlayerControlable);
        }

        public void ExecuteFrameCut(FrameCut cut)
        {
            var handler = GetFrameCommandHander(cut.cmd.commandType);
            if (handler != null)
            {
                handler.Execute(cut.cmd);
                if (handler.needReserve)
                {
                    m_frameCutList.Add(cut);
                }
            }
        }

        public T FetchObj<T>() where T : class, new()
        {
            return FetchObj(typeof(T)) as T;
        }

        public object FetchObj(Type type)
        {
            object obj = m_poolMap.Fetch(type);
            BattleObj battleObj = obj as BattleObj;
            if (battleObj != null)
            {
                battleObj.InitBattleObj(this);
            }
            return obj;
        }

        public ClassPoolList<T> FetchList<T>()
        {
            return m_poolMap.FetchList<T>();
        }

        public void Release(object obj)
        {
            m_poolMap.Release(obj);
        }

        public void ReleaseAll(IList objList)
        {
            m_poolMap.ReleaseAll(objList);
        }

        public void ReleaseAll<T>(T[] objs) where T : BattleObj
        {
            m_poolMap.ReleaseAll(objs);
        }

        public void CopyFrom(BattleBase battle, ClassPoolMap classPoolMap)
        {
            Init(battle.initData, null, battle.infoGetter, battle.logger, classPoolMap);
            InitAllProcess();
            for (int i = 0; i < BattleDefine.BattleCopyProcessTypeCount; ++i)
            {
                CopyProcess(battle, (BattleCopyProcessType)i);
            }
        }

        private void CopyProcess(BattleBase battle, BattleCopyProcessType processType)
        {
            for (int i = 0; i < m_componentList.Count; ++i)
            {
                m_componentList[i].CopyFromProcess(battle.m_componentList[i], processType);
            }
        }

        public byte[] BuildSnapshot()
        {
            ByteBufferBuilder builder = new ByteBufferBuilder();
            //m_random.BuildRandomBuffer(builder);
            //for (int i = 0; i < m_componentList.Count; ++i)
            //{
            //    m_componentList[i].BuildSnapshot(builder);
            //}
            return builder.ToArray();
        }

        public void LoadSnapshot(byte[] bytes)
        {
            //InitBasic(m_initData);
            //ByteBufferLoader loader = new ByteBufferLoader(bytes);
            //m_random.LoadRandomBuffer(loader);
            //for (int i = 0; i < m_componentList.Count; ++i)
            //{
            //    m_componentList[i].LoadSnapshot(loader);
            //}
        }

        public override string ToString()
        {
            return m_instanceType.ToString() + GetHashCode();
        }
    }
}
