using System;
using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public class MovePathFindContext : BattlePoolObj
    {
        public IFetchable fetchable;
        public MovePathRule rule;
        public GridPosition startGridPos;
        public GridPosition targetGridPos;
        public MovePathCheckMode checkMode;
        public LinkedList<MovePath> openList = new LinkedList<MovePath>();
        public List<MovePath> closeList = new List<MovePath>();
        public List<MovePath> createdPathList = new List<MovePath>();

        public override void OnRelease()
        {
            fetchable = default;
            rule = default;
            startGridPos = default;
            targetGridPos = default;
            checkMode = default;
            for(int i = 0; i < createdPathList.Count; ++i)
            {
                createdPathList[i].Release();
            }
            createdPathList.Clear();
            openList.Clear();
            closeList.Clear();
            base.OnRelease();
        }
    }
}
