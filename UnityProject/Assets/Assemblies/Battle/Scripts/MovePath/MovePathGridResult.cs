using System.Collections;
using System.Collections.Generic;

namespace Phoenix.Battle
{
    public class MovePathGridResult : BattleObj
    {
        public GridPosition pos;
        public int g;
        public int costG;
        public bool canLocate;

        public override void OnRelease()
        {
            pos = default;
            g = default;
            costG = default;
            canLocate = default;
            base.OnRelease();
        }

        public MovePathGridResult Copy()
        {
            MovePathGridResult result = m_battle.FetchObj<MovePathGridResult>();
            result.pos = pos;
            result.canLocate = canLocate;
            result.g = g;
            result.costG = costG;
            return result;
        }
    }
}
