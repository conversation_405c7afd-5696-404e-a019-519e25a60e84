using System.Collections;
using System.Collections.Generic;
using Phoenix.Core;

namespace Phoenix.Battle
{
    public static class MovePathFindUtility
    {
        public static MovePathFindResult FindPathAnyway(GridPosition startGridPos, GridPosition targetGridPos, MovePathRule rule)
        {
            MovePathFindContext context = rule.FetchObj<MovePathFindContext>();
            context.fetchable = rule;
            context.rule = rule;
            context.checkMode = MovePathCheckMode.Anyway;
            context.startGridPos = startGridPos;
            context.targetGridPos = targetGridPos;
            return FindPath(context);
        }

        public static MovePathFindResult FindPathAnywayIgnoreTarget(GridPosition startGridPos, GridPosition targetGridPos, MovePathRule rule)
        {
            MovePathFindContext context = rule.FetchObj<MovePathFindContext>();
            context.fetchable = rule;
            context.rule = rule;
            context.checkMode = MovePathCheckMode.AnywayIgnoreTarget;
            context.startGridPos = startGridPos;
            context.targetGridPos = targetGridPos;
            return FindPath(context);
        }


        public static MovePathFindResult FindPathByRule(GridPosition startGridPos, GridPosition targetGridPos, MovePathRule rule)
        {
            MovePathFindContext context = rule.FetchObj<MovePathFindContext>();
            context.fetchable = rule;
            context.rule = rule;
            context.checkMode = MovePathCheckMode.Rule;
            context.startGridPos = startGridPos;
            context.targetGridPos = targetGridPos;
            return FindPath(context);
        }

        public static MovePathFindResult FindPathToNearestTarget(GridPosition startGridPos, MovePathRule rule)
        {
            MovePathFindContext context = rule.FetchObj<MovePathFindContext>();
            context.fetchable = rule;
            context.rule = rule;
            context.checkMode = MovePathCheckMode.NearestTarget;
            context.startGridPos = startGridPos;
            context.targetGridPos = new GridPosition(int.MaxValue, int.MaxValue);
            return FindPath(context);
        }

        public static MoveRangeFindResult FindRange(GridPosition startGridPos, MovePathRule rule)
        {
            MoveRangeFindResult moveRangeResult = rule.FetchObj<MoveRangeFindResult>();
            MovePathFindContext context = rule.FetchObj<MovePathFindContext>();
            context.fetchable = rule;
            context.rule = rule;
            context.checkMode = MovePathCheckMode.Rule;
            context.startGridPos = startGridPos;
            context.targetGridPos = new GridPosition(int.MaxValue, int.MaxValue);
            AddToOpenList(context, null, context.startGridPos, 0, 0, true);
            EState state;
            do
            {
                state = FindPathOnce(context);
            } while (state == EState.Finding);
            int closeListCount = context.closeList.Count;
            for (int i = 0; i < closeListCount; ++i)
            {
                var path = context.closeList[i];
                if (!path.canLocate)
                {
                    continue;
                }
                CollectToGridResultList(context, path, moveRangeResult.gridResultList);
            }
            context.Release();
            return moveRangeResult;
        }

        public static MovePathFindResult FindNearestPathInRange(GridPosition startGridPos, GridPosition targetGridPos, MovePathRule rule)
        {
            MovePathFindContext context = rule.FetchObj<MovePathFindContext>();
            context.fetchable = rule;
            context.rule = rule;
            context.checkMode = MovePathCheckMode.Rule;
            context.startGridPos = startGridPos;
            context.targetGridPos = targetGridPos;
            AddToOpenList(context, null, context.startGridPos, 0, 0, true);
            EState state;
            do
            {
                state = FindPathOnce(context);
            } while (state == EState.Finding);

            if (state != EState.Succeeded)
            {
                for (int i = context.closeList.Count - 1; i >= 0; i--)
                {
                    var closeNode = context.closeList[i];
                    if (closeNode.canLocate)
                    {
                        closeNode.f = closeNode.h;
                        closeNode.g = 0;
                        context.closeList.RemoveAt(i);
                        AddToOpenList(context, closeNode);
                    }
                }
            }
            context.checkMode = MovePathCheckMode.AnywayIgnoreTarget;
            do
            {
                state = FindPathOnce(context);
            } while (state == EState.Finding);
            MovePathFindResult movePathResult = context.fetchable.FetchObj<MovePathFindResult>();
            movePathResult.success = state == EState.Succeeded;
            if (movePathResult.success)
            {
                MovePath path = context.openList.First.Value;
                CollectToGridResultList(context, path, movePathResult.gridResultList);
                movePathResult.gridResultList.Reverse();
            }
            context.Release();
            return movePathResult;
        }

        private static MovePathFindResult FindPath(MovePathFindContext context)
        {
            MovePathFindResult movePathResult = context.fetchable.FetchObj<MovePathFindResult>();
            if (context.startGridPos == context.targetGridPos)
            {
                movePathResult.success = false;
                context.Release();
                return movePathResult;
            }
            AddToOpenList(context, null, context.startGridPos, 0, 0, true);
            EState state;
            do
            {
                state = FindPathOnce(context);
            } while (state == EState.Finding);

            movePathResult.success = state == EState.Succeeded;
            if (movePathResult.success)
            {
                MovePath path = context.openList.First.Value;
                CollectToGridResultList(context, path, movePathResult.gridResultList);
                movePathResult.gridResultList.Reverse();
            }
            context.Release();
            return movePathResult;
        }

        private static EState FindPathOnce(MovePathFindContext context)
        {
            if (context.openList.Count == 0)
            {
                return EState.Failed;
            }
            MovePath path = context.openList.First.Value;
            bool isArrive = false;
            if (context.checkMode == MovePathCheckMode.AnywayIgnoreTarget)
            {
                isArrive = path.gridPos == context.targetGridPos;
            }
            else
            {
                isArrive = context.rule.CheckArrive(path, context.targetGridPos);
            }
            if (isArrive)
            {
                return EState.Succeeded;
            }
            context.openList.RemoveFirst();
            context.closeList.Add(path);
            if (path.offsetX != 0 || path.offsetY != 0)
            {
                AddNewPath(context, path, path.offsetX, path.offsetY, false);
            }
            AddNewPath(context, path, -1, 0, true);
            AddNewPath(context, path, 1, 0, true);
            AddNewPath(context, path, 0, -1, true);
            AddNewPath(context, path, 0, 1, true);
            return EState.Finding;
        }

        private static void AddNewPath(MovePathFindContext context, MovePath fromPath, int offsetX, int offsetY, bool skipSameOffset)
        {
            if (skipSameOffset && fromPath.offsetX == offsetX && fromPath.offsetY == offsetY)
            {
                return;
            }
            GridPosition gridPos = fromPath.gridPos + new GridPosition(offsetX, offsetY);
            if (!context.rule.battle.CheckPosValid(gridPos))
            {
                return;
            }
            if (context.checkMode != MovePathCheckMode.AnywayIgnoreTarget || gridPos != context.targetGridPos)
            {
                if (!context.rule.CanPass(gridPos))
                {
                    return;
                }
            }
            AddToOpenList(context, fromPath, gridPos, offsetX, offsetY, false);
        }

        private static void AddToOpenList(MovePathFindContext context, MovePath fromPath, GridPosition toGridPos, int offsetX, int offsetY, bool isFirst)
        {
            int h = context.rule.CalcH(toGridPos, context.targetGridPos);
            int g = 0;
            if (fromPath != null)
            {
                g = fromPath.g + context.rule.CalcG(fromPath.gridPos, toGridPos, isFirst);
            }
            int f = g + h;
            if (!CheckCanOpen(context.rule, context.checkMode, toGridPos, g))
            {
                return;
            }
            int closeListCount = context.closeList.Count;
            for (int i = 0; i < closeListCount; ++i)
            {
                MovePath closePath = context.closeList[i];
                if (closePath.gridPos == toGridPos)
                {
                    if (f >= closePath.f)
                    {
                        return;
                    }
                    context.closeList.RemoveAt(i);
                    break;
                }
            }
            MovePath openPath = null;
            foreach(MovePath path in context.openList)
            {
                if (path.gridPos == toGridPos)
                {
                    openPath = path;
                    break;
                }
            }
            if (openPath != null)
            {
                if (f >= openPath.f)
                {
                    return;
                }
                context.openList.Remove(openPath);
            }
            MovePath toPath = CreatePath(context);
            toPath.gridPos = toGridPos;
            toPath.fromPath = fromPath;
            toPath.g = g;
            toPath.h = h;
            toPath.f = f;
            toPath.canLocate = context.rule.CanLocate(toGridPos);
            toPath.offsetX = offsetX;
            toPath.offsetY = offsetY;
            AddToOpenList(context, toPath);
        }

        private static void AddToOpenList(MovePathFindContext context, MovePath path)
        {
            LinkedListNode <MovePath> current = context.openList.First;
            while (current != null)
            {
                if (path.f < current.Value.f)
                {
                    break;
                }
                current = current.Next;
            }

            if (current != null)
            {
                context.openList.AddBefore(current, path);
            }
            else
            {
                context.openList.AddLast(path);
            }
        }

        private static void CollectToGridResultList(MovePathFindContext context, MovePath path, List<MovePathGridResult> gridResultList)
        {
            while (path != null)
            {
                MovePath curPath = path;
                path = path.fromPath;
                bool hasSameGrid = false;
                for (int i = 0; i < gridResultList.Count; ++i)
                {
                    if (gridResultList[i].pos == curPath.gridPos)
                    {
                        hasSameGrid = true;
                        break;
                    }
                }
                if (hasSameGrid)
                {
                    continue;
                }
                MovePathGridResult gridResult = context.fetchable.FetchObj<MovePathGridResult>();
                gridResult.pos = curPath.gridPos;
                gridResult.canLocate = curPath.canLocate;
                gridResult.g = curPath.g;
                gridResult.costG = curPath.g - (curPath.fromPath != null ? curPath.fromPath.g : 0);
                gridResultList.Add(gridResult);
            }
        }

        private static MovePath CreatePath(MovePathFindContext context)
        {
            MovePath path = context.fetchable.FetchObj<MovePath>();
            context.createdPathList.Add(path);
            return path;
        }

        private static bool CheckCanOpen(MovePathRule rule, MovePathCheckMode checkMode, GridPosition toGridPos, int g)
        {
            switch (checkMode)
            {
                case MovePathCheckMode.Rule:
                    if (!rule.CheckG(g))
                    {
                        return false;
                    }
                    break;
                case MovePathCheckMode.Anyway:
                case MovePathCheckMode.AnywayIgnoreTarget:
                case MovePathCheckMode.NearestTarget:
                case MovePathCheckMode.RangeToTarget:
                    return true;
            }
            return true;
        }

        private static void GetS(MovePath path, MovePathFindContext context)
        {
            var curPath = path;
            while (curPath != null)
            {
                if (context.rule.CheckG(curPath.g))
                {

                }
            }
        }

        private static bool CheckFinalOfNearestTargetInRange(MovePath path, MovePathFindContext context)
        {
            bool isBlockMove = false;
            var curPath = path;
            while (curPath != null)
            {
                if (context.rule.CheckG(curPath.g))
                {
                    isBlockMove = !curPath.canLocate;
                    break;
                }
                curPath = curPath.fromPath;
            }
            if (isBlockMove)
            {
                curPath = path;
                while (curPath != null)
                {
                    if (context.rule.CheckG(curPath.g) && curPath.canLocate)
                    {
                        break;
                    }
                    curPath.f = int.MaxValue;
                    curPath = curPath.fromPath;
                }
                return false;
            }
            return true;
        }

        private enum EState
        {
            Failed,
            Succeeded,
            Finding,
        }
    }
}
