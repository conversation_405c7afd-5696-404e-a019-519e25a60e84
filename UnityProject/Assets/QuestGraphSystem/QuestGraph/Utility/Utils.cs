
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using ParadoxNotion.Design;
using UnityEngine;

namespace Phoenix.QuestGraphEditor
{
    public static class Utils
    {

        public static String[] GetEnumNames(Enum customEnum)
        {
            List<String> result = new List<string>();
            Type type = customEnum.GetType();
            FieldInfo[] fields = type.GetFields(BindingFlags.Public);
            foreach (FieldInfo field in fields)
            {
                NameAttribute nameAttribute = field.GetCustomAttribute<NameAttribute>();
                if (nameAttribute != null)
                {
                    result.Add(nameAttribute.name);
                }
                else
                {
                    result.Add(field.Name);
                }
            }
            return result.ToArray();
        }

        /// <summary>
        /// Fetch NameAttribute.name
        /// </summary>
        /// <param name="customEnum"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static String FetchEnumValueName(Enum customEnum, String defaultValue = "NotDefine")
        {
            String result = defaultValue;
            if (customEnum.FetchEnumAttribute(out NameAttribute attr))
            {
                result = attr.name;
            }
            return result;
        }


        public static String FetchNameAttribute(this Type type, String defaultVal = "NULL")
        {
            var attrs = type.GetCustomAttributes(typeof(NameAttribute), true);
            NameAttribute attr = attrs.Length > 0 ? (NameAttribute)attrs[0] : null;
            return attr != null ? attr.name : defaultVal;
        }

        public static bool FetchEnumAttribute<T>(this Enum value, out T attr) where T : System.Attribute
        {
            attr = null;
            var field = value.GetType().GetField(value.ToString());
            var attrs = field?.GetCustomAttributes(typeof(T), true);
            if (attrs != null)
            {
                attr = attrs.Length > 0 ? (T)attrs[0] : null;
            }
            return attr != null;
        }
        /// <summary>get default name of obj</summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static bool FetchAttribute<T>(this object obj, out T attr) where T : System.Attribute
        {
            var type = obj.GetType();
            var attrs = type.GetCustomAttributes(typeof(T), true);
            attr = attrs.Length > 0 ? (T)attrs[0] : null;
            return attr != null;
        }

        /// <summary>reset all [SeralizedField] field to defalut value</summary>
        public static void ResetObject<T>(T obj) where T : class
        {
            if (obj is null) return;

            /* get all fields */
            var type = obj.GetType();
            var fields = type.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);

            /* reset all [SerializeField] field to default value */
            foreach (var field in fields.Where(x => x.IsDefined(typeof(SerializeField), true)))
                field.SetValue(obj, field.FieldType.IsValueType ? Activator.CreateInstance(field.FieldType) : null);
        }

        /// <summary>copy object simple implementation</summary>
        public static T CopyObject<T>(T obj) where T : class
        {
            if (obj is null) return null;
            if (obj is string) return obj;
            if (obj.GetType().IsAbstract) return null;

            /* get all fields */
            var type = obj.GetType();
            var fields = type.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);

            /* create new instance */
            var newObj = Activator.CreateInstance(type);

            /* copy all [SerializeField] field to new object */
            foreach (var field in fields.Where(x => x.IsDefined(typeof(SerializeField), true)))
                field.SetValue(newObj, field.GetValue(obj));

            return newObj as T;
        }

        /// <summary>copy object datas from other object</summary>
        public static void CopyObjectFrom<T>(T self, T other) where T : class
        {
            if (self is null || other is null) return;
            if (self.GetType() != other.GetType()) return;

            /* get all fields */
            var type = self.GetType();
            var fields = type.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);

            /* copy all [SerializeField] field to new object */
            foreach (var field in fields.Where(x => x.IsDefined(typeof(SerializeField), true)))
            {
                field.SetValue(self, field.GetValue(other));
            }
        }
    }
}
