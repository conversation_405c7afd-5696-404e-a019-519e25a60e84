using System;
using System.Collections.Generic;
using ParadoxNotion.Design;
using UnityEngine;


namespace Phoenix.QuestGraphEditor
{
    [Name("任务模板")]
    [Description("通用基础任务模板")]
    /// <summary> 任务模板 - 通用基础任务模板 </summary>
    public partial class QuestNodeTemplate : QuestNode
    {
        [Name("任务ID")]
        [SerializeField]
        public Int32 QuestId;
        [Name("任务名字")]
        [SerializeField]
        public String QuestName;
        [Name("任务描述")]
        [SerializeField]
        public String QuestDesc;
        [Name("完成条件关系")]
        [SerializeField]
        public LogicalMode QuestConditionMode;
        [Name("完成条件列表")]
        [SerializeField]
        public List<QuestCompleteCondition> CompleteConditions = new List<QuestCompleteCondition>();
    }
}
