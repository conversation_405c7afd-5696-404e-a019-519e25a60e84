using System;
using System.Collections.Generic;
using ParadoxNotion.Design;
using UnityEngine;


namespace Phoenix.QuestGraphEditor
{
    [Name("等级")]
    [Description("与玩家等级关联的条件")]
    /// <summary> 等级 - 与玩家等级关联的条件 </summary>
    public partial class QuestConditionPlayerLevel
    {
        [Name("等级")]
        [SerializeField]
        public Int32 Level;
        [Name("比较方式")]
        [SerializeField]
        public CompareMode CompareMode;
    }
}
