using System;
using System.Collections.Generic;
using ParadoxNotion.Design;
using UnityEngine;


namespace Phoenix.QuestGraphEditor
{
    [Name("添加角色")]
    [Description("添加角色到场景中")]
    /// <summary> 添加角色 - 添加角色到场景中 </summary>
    public partial class QuestActionNpcAdd
    {
        [Name("角色ID")]
        [SerializeField]
        public Int32 NpcId;
        [Name("模型ID")]
        [SerializeField]
        public Int32 CharacterId;
        [Name("角色名字")]
        [SerializeField]
        public String Name;
        [Name("箱庭场景ID")]
        [SerializeField]
        public Int32 HakoniwaSceneId;
        [Name("位置X")]
        [SerializeField]
        public Single PositionX;
        [Name("位置Y")]
        [SerializeField]
        public Single PositionY;
        [Name("位置Z")]
        [SerializeField]
        public Single PositionZ;
    }
}
