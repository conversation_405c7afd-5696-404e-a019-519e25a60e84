using System;
using System.Collections.Generic;
using ParadoxNotion.Design;
using UnityEngine;


namespace Phoenix.QuestGraphEditor
{
    [Name("抵达")]
    [Description("抵达目标位置")]
    /// <summary> 抵达 - 抵达目标位置 </summary>
    public partial class QuestCompleteConditionReach
    {
        [Name("箱庭地图ID")]
        [SerializeField]
        public Int32 SceneId;
        [Name("位置X")]
        [SerializeField]
        public Single PositionX;
        [Name("位置Y")]
        [SerializeField]
        public Single PositionY;
        [Name("位置Z")]
        [SerializeField]
        public Single PositionZ;
        [Name("范围半径")]
        [SerializeField]
        public Single ReachRadius;
        [Name("目标模型ID")]
        [SerializeField]
        public Int32 EntitySkinId;
        [Name("目标模型朝向")]
        [SerializeField]
        public Int32 EntityDirection;
    }
}
