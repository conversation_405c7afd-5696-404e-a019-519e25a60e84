using System;
using System.Collections.Generic;
using ParadoxNotion.Design;
using UnityEngine;


namespace Phoenix.QuestGraphEditor
{
    [Name("战斗")]
    [Description("完成战斗")]
    /// <summary> 战斗 - 完成战斗 </summary>
    public partial class QuestCompleteConditionBattle
    {
        [Name("箱庭关卡ID")]
        [Description("箱庭关卡ID")]
        [SerializeField]
        public Int32 LevelId;
        [Name("必须胜利")]
        [Description("是否要求必须胜利")]
        [SerializeField]
        public Boolean MustWin;
    }
}
