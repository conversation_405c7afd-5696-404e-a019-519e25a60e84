
using System;
using ParadoxNotion.Design;
using UnityEditor;
using UnityEngine;

namespace Phoenix.QuestGraphEditor
{
    [Name("需求")]
    [NodeElement]
    public abstract class QuestCompleteCondition : NodeElement
    {
        public QuestNodeTemplate Owner => (QuestNodeTemplate)m_owner;

        protected override GenericMenu GetContextMenu()
        {
            GenericMenu menu = new GenericMenu();
            // SavePreset
            menu.AddItem(new GUIContent("保存预设"), false, () => { });
            menu.AddItem(new GUIContent("Reset"), false, Reset);
            menu.AddItem(new GUIContent("Copy"), false, () => CopyBuffer.SetCache(this));
            if (CopyBuffer.TryGetCache(out QuestCompleteCondition cache) && cache != this && cache.GetType() == GetType())
            {
                menu.AddItem(new GUIContent("Paste"), false, () => Utils.CopyObjectFrom(this, cache));
            }
            else
            {
                menu.AddDisabledItem(new GUIContent("Paste"));
            }

            if (Owner == null)
            {
                menu.AddDisabledItem(new GUIContent("Delete(Owner=null)"));
            }
            else
            {
                menu.AddItem(new GUIContent("Delete"), false, () => Owner.DeleteRequire(this));
            }
            OnCreateContextMenu(menu);
            return menu;
        }

        protected virtual GenericMenu OnCreateContextMenu(GenericMenu menu) => menu;

    }

}
