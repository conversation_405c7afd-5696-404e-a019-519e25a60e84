

using ParadoxNotion.Design;
using UnityEditor;
using UnityEngine;

namespace Phoenix.QuestGraphEditor
{
    [Name("行为")]
    [NodeElement]
    public abstract class QuestAction : NodeElement
    {
        public QuestNodeAction Owner => (QuestNodeAction)m_owner;


#if UNITY_EDITOR

        protected override GenericMenu GetContextMenu()
        {
            GenericMenu menu = new GenericMenu();
            menu.AddItem(new GUIContent("Reset"), false, Reset);
            menu.AddItem(new GUIContent("Copy"), false, () => CopyBuffer.SetCache(this));
            if (CopyBuffer.TryGetCache(out QuestAction cache) && cache != this && cache.GetType() == GetType())
            {
                menu.AddItem(new GUIContent("Paste"), false, () => Utils.CopyObjectFrom(this, cache));
            }
            else
            {
                menu.AddDisabledItem(new GUIContent("Paste"));
            }
            if (Owner == null)
            {
                menu.AddDisabledItem(new GUIContent("Delete(Owner=null)"));
            }
            else
            {
                menu.AddItem(new GUIContent("Delete"), false, () => Owner.DeleteAction(this));
            }
            OnCreateContextMenu(menu);
            return menu;
        }

        protected virtual GenericMenu OnCreateContextMenu(GenericMenu menu) => menu;

#endif

    }
}
