#if UNITY_EDITOR
using System;
using ParadoxNotion.Design;
using UnityEngine;


namespace Phoenix.QuestGraphEditor
{
    public partial class QuestActionNpcAdd : QuestAction
    {


        public override string Summary
        {
            get
            {
                return $"\"添加{Name}({NpcId})到{HakoniwaSceneId}-({PositionX},{PositionY},{PositionZ})\"";
            }
        }
    }
}
#endif