

using NodeCanvas.Editor;
using NodeCanvas.Framework;
using ParadoxNotion;
using ParadoxNotion.Design;
using System;
using System.Collections;
using UnityEditor;
using UnityEngine;

namespace Phoenix.QuestGraphEditor
{
    public abstract class QuestNode : Node , IUpdatable
    {
        public override Type outConnectionType => typeof(CustomConnection);

        public override Int32 maxInConnections => 1;

        public override Int32 maxOutConnections => 10;

        public override bool allowAsPrime => true;

        public override bool canSelfConnect => false;

        public override Alignment2x2 commentsAlignment => Alignment2x2.Left;

        public override Alignment2x2 iconAlignment => Alignment2x2.Left;


        #region Simulate Runtime 

        protected QuestGraph StoryGraph => (QuestGraph)graph;

        protected override Status OnExecute(Component agent, IBlackboard blackboard)
        {
            StartCoroutine(Delay());
            return Status.Success;
        }

        IEnumerator Delay()
        {
            yield return new WaitForSeconds(2);
            Debug.LogError(name);
            StoryGraph.Continue(this);
        }

        public virtual void Update()
        {
        }

        #endregion


        protected override void OnNodeInspectorGUI()
        {
            if (InvalidFlag)
            {
                Rect tempRect = EditorGUILayout.BeginVertical().ExpandBy(-6);
                {
                    GUILayout.Space(5);
                    GUI.color = StyleSheet.GetStatusColor(Status.Failure);
                    Styles.Draw(tempRect, StyleSheet.windowHighlight);
                    GUI.color = Color.white;
                    EditorGUILayout.SelectableLabel(InvalidMessage, "WhiteLargeLabel");
                }
                EditorGUILayout.EndVertical();
            }
        }


        private void DrawInspectorToolBar()
        {
            GUILayout.BeginHorizontal("PreToolbar");
            {
                GUILayout.Space(-4);
                GUIContent content = EditorUtils.GetTempContent("高级", null, "启用高级配置");
                //Advanced = GUILayout.Toggle(Advanced, content, EditorStyles.toolbarButton);
                GUILayout.FlexibleSpace();
            }
            GUILayout.EndHorizontal();
            GUILayout.Space(5);
        }

        #region Valid Check

        protected override string GetWarningOrError()
        {
            String result = base.GetWarningOrError() ?? InvalidMessage;
            return result;
        }
        protected override void OnUpdateValidState()
        {
            Valid valid = CheckValid();
            InvalidMessage = valid.Message;
        }
        protected override void OnNodeExternalGUI()
        {
            base.OnNodeExternalGUI();
            if (InvalidFlag)
            {
                GUI.color = StyleSheet.GetStatusColor(Status.Error);
                Styles.Draw(rect, StyleSheet.windowHighlight);
                GUI.color = Color.white;
            }
        }

        public Valid CheckValid() { return OnCheckValid(); }

        protected virtual Valid OnCheckValid() {  return Valid.OK; }

        #endregion


        private FieldReader m_fieldReader;
        public String GetNameAttribute(String fieldName)
        {
            m_fieldReader ??= new FieldReader(GetType());
            return m_fieldReader.GetNameAttribute(fieldName);
        }
    }

}
