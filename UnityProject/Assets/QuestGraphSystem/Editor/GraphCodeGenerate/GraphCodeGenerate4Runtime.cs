#if UNITY_EDITOR


using System;
using System.IO;
using System.Security;
using System.Collections.Generic;
using Mono.Xml;
using Phoenix.Core;

public static class GraphCodeGenerate4Runtime
{
    public static String Prefix = "ConfigData";
    public static String Suffix = "";
    public static String RuntimeNodeCodePath = DataDefine.RuntimeCodePath;
    public static Dictionary<String, String> BuiltInTypes = new Dictionary<String, String>(); //系统内置类型
    public static List<String> EnumTypes = new List<String>();
    public static List<String> ClassTypes = new List<String>();


    public static void GeneraterCode()
    {
        String xmlContent = File.ReadAllText(DataDefine.NodeConfigFilePath); // 加载 XML 文件
        SecurityParser securityParser = new SecurityParser();
        securityParser.LoadXml(xmlContent);
        SecurityElement rootElement = securityParser.ToXml();
        if (rootElement == null)
        {
            throw new Exception($"XML file is empty, filePath: {DataDefine.NodeConfigFilePath}.");
        }
        if (Directory.Exists(RuntimeNodeCodePath))
        {
            Directory.Delete(RuntimeNodeCodePath, true);
        }

        AnylizerBuiltInValueTypes(rootElement);
        ExportEnumCode(rootElement, "Structure");
        ExportStructureCode(rootElement, "Structure");

        ExportNodeCode(rootElement, "QuestNode");
        ExportNodeElementCode(rootElement, "QuestAction");
        ExportNodeElementCode(rootElement, "QuestActiveCondition");
        ExportNodeElementCode(rootElement, "QuestCompleteCondition");

        ExportJsonDeserializeCode(rootElement, "Common");
    }

    #region BuiltInTypes

    private static void AnylizerBuiltInValueTypes(SecurityElement rootElement)
    {
        BuiltInTypes.Clear();
        SecurityElement element = rootElement.GetElement("BuiltInValueType");
        if (element != null && element.Children.Count > 0)
        {
            foreach (SecurityElement elem in element.Children)
            {
                String name = elem.Attribute("name");
                String fullName = elem.Attribute("fullName");
                BuiltInTypes[name] = fullName;
            }
        }
    }

    #endregion


    #region Enum

    private static void ExportEnumCode(SecurityElement rootElement, String subFolder)
    {
        EnumTypes.Clear();
        SecurityElement enumElement = rootElement.GetElement("EnumDefine");
        if (enumElement != null && enumElement.Children.Count > 0)
        {
            String filePath = GraphCodeGenerater.GenDirectory(RuntimeNodeCodePath, subFolder);
            FileWriter writer = new FileWriter();
            GraphCodeGenerateUtility.AppendUsingNamespaceHeader(writer);
            GraphCodeGenerateUtility.AppendAutoGenTips(writer);

            writer.AppendLine();
            writer.AppendLine("namespace {0}", DataDefine.QuestGraphRuntimeNameSpace);
            writer.StartBlock();
            {
                ExportQuestNodeEnumCodeInternal(writer, rootElement, subFolder);

                foreach (SecurityElement element in enumElement.Children)
                {
                    writer.AppendLine();
                    ExportEnumCodeInternal(writer, element);
                }
            }
            writer.EndBlock();
            writer.WriteFile(Path.Combine(filePath, $"QuestGraphEnums.cs"));
        }
    }

    private static void ExportQuestNodeEnumCodeInternal(FileWriter writer, SecurityElement rootElement, String folderPath)
    {
        SecurityElement element = rootElement.GetElement("QuestNode");

        writer.AppendLine($"/// <summary> 节点类型枚举 </summary>");
        writer.AppendLine($"public enum QuestNodeType");
        writer.StartBlock();
        {
            if (element.Children != null && element.Children.Count > 0)
            {
                foreach (SecurityElement elem in element.Children)
                {
                    if (elem.Attribute("attrName", out String attrName)) writer.AppendLine($"/// <summary> {attrName} </summary>");
                    writer.AppendLine($"{elem.Attribute("name")},");
                }
            }
        }
        writer.EndBlock();
    }

    private static void ExportEnumCodeInternal(FileWriter writer, SecurityElement element)
    {
        if (element.Attribute("name", out String enumName))
        {
            EnumTypes.Add(enumName);
            if (element.Attribute("desc", out String desc)) writer.AppendLine($"/// <summary> {desc} </summary>");
            if (element.Attribute("base", out String baseName))
            {
                writer.AppendLine($"public enum {enumName} : {baseName}");
            }
            else
            {
                writer.AppendLine($"public enum {enumName}");
            }
            writer.StartBlock();
            {
                foreach (SecurityElement fieldElem in element.Children)
                {
                    if (fieldElem.Attribute("attrName", out String attrName)) writer.AppendLine($"/// <summary> {attrName} </summary>");
                    if (fieldElem.Attribute("value", out String enumValue)) enumValue = $" = {enumValue}";
                    writer.AppendLine($"{fieldElem.Attribute("name")}{enumValue},");
                }
            }
            writer.EndBlock();
        }
    }

    #endregion


    #region Structure

    private static void ExportStructureCode(SecurityElement rootElement, String subFolder)
    {
        ClassTypes.Clear();
        SecurityElement structureElement = rootElement.GetElement("StructureDefine");
        if (structureElement != null && structureElement.Children.Count > 0)
        {
            String filePath = GraphCodeGenerater.GenDirectory(RuntimeNodeCodePath, subFolder);
            FileWriter writer = new FileWriter();

            GraphCodeGenerateUtility.AppendUsingNamespaceHeader(writer);
            GraphCodeGenerateUtility.AppendAutoGenTips(writer);

            writer.AppendLine();
            writer.AppendLine("namespace {0}", DataDefine.QuestGraphRuntimeNameSpace);
            writer.StartBlock();
            {
                foreach (SecurityElement element in structureElement.Children)
                {
                    writer.AppendLine();
                    ExportStructureCodeInternal(writer, element);
                }
            }
            writer.EndBlock();
            writer.WriteFile(Path.Combine(filePath, $"QuestGraphStructures.cs"));
        }
    }

    private static void ExportStructureCodeInternal(FileWriter writer, SecurityElement element)
    {
        if (element.Attribute("name", out String className))
        {
            ClassTypes.Add(className);
            if (element.Attribute("desc", out String desc)) writer.AppendLine($"/// <summary> {desc} </summary>");
            if (element.Attribute("base", out String baseNameStr)) baseNameStr = $": {baseNameStr}";
            else baseNameStr = String.Empty;
            writer.AppendLine($"public {element.Tag.ToLower()} {className}{baseNameStr}");
            writer.StartBlock();
            {
                if (element.Children != null && element.Children.Count > 0)
                {
                    foreach (SecurityElement elem in element.Children)
                    {
                        AppendFieldDeclare(writer, elem);
                    }
                }
            }
            writer.EndBlock();
        }
    }

    #endregion


    #region Node

    private static void ExportNodeCode(SecurityElement rootElement, String elemName)
    {
        SecurityElement nodeElement = rootElement.GetElement(elemName);
        if (nodeElement != null && nodeElement.Children.Count > 0)
        {
            String autoGenFolderPath = GraphCodeGenerater.GenDirectory(RuntimeNodeCodePath, elemName);
            foreach (SecurityElement element in nodeElement.Children)
            {
                ExportNodeCode_AutoGen(element, autoGenFolderPath);
            }
        }
    }

    private static void ExportNodeCode_AutoGen(SecurityElement element, String folderPath)
    {
        FileWriter writer = new FileWriter();
        GraphCodeGenerateUtility.AppendUsingNamespaceHeader(writer);
        GraphCodeGenerateUtility.AppendAutoGenTips(writer);
        String className = element.Attribute("name");
        String baseName = element.Attribute("base");
        writer.AppendLine();
        writer.AppendLine();
        writer.AppendLine("namespace {0}", DataDefine.QuestGraphRuntimeNameSpace);
        writer.StartBlock();
        {
            String val = String.Empty;
            writer.AppendLine();
            AppendComment(writer, element);
            writer.AppendLine($"public partial class {Prefix}{className} : {baseName}");
            writer.StartBlock();
            {
                if (element.Children != null && element.Children.Count > 0)
                {
                    foreach (SecurityElement elem in element.Children)
                    {
                        AppendFieldDeclare(writer, elem);
                    }
                }
            }
            writer.EndBlock();
        }
        writer.EndBlock();
        String autoGenFilePath = Path.Combine(folderPath, $"{Prefix}{className}.cs");
        writer.WriteFile(autoGenFilePath);
    }

    #endregion


    #region NodeElement

    private static void ExportNodeElementCode(SecurityElement rootElement, String elemName)
    {
        SecurityElement elements = rootElement.GetElement(elemName);
        if (elements != null && elements.Children.Count > 0)
        {
            String autoGenFolder = GraphCodeGenerater.GenDirectory(RuntimeNodeCodePath, elemName);
            foreach (SecurityElement element in elements.Children)
            {
                ExportNodeElementCode_AutoGen(element, autoGenFolder);
            }
        }
    }

    private static void ExportNodeElementCode_AutoGen(SecurityElement element, String filePath)
    {
        FileWriter writer = new FileWriter();
        GraphCodeGenerateUtility.AppendUsingNamespaceHeader(writer);
        GraphCodeGenerateUtility.AppendAutoGenTips(writer);
        writer.AppendLine("namespace {0}", DataDefine.QuestGraphRuntimeNameSpace);
        String className = element.Attribute("name");
        String baseName = element.Attribute("base");
        writer.StartBlock();
        {
            AppendComment(writer, element);
            String val = String.Empty;
            writer.AppendLine($"public partial class {Prefix}{className} : {baseName}");
            writer.StartBlock();
            {
                if (element.Children != null && element.Children.Count > 0)
                {
                    foreach (SecurityElement elem in element.Children)
                    {
                        AppendFieldDeclare(writer, elem);
                    }
                }
            }
            writer.EndBlock();
        }
        writer.EndBlock();
        String autoGenFilePath = Path.Combine(filePath, $"{Prefix}{className}.cs");
        writer.WriteFile(autoGenFilePath);
    }

    #endregion



    #region LoadFromJsonUtility

    private static void ExportJsonDeserializeCode(SecurityElement rootElement, String subFolder)
    {
        String filePath = GraphCodeGenerater.GenDirectory(RuntimeNodeCodePath, subFolder);
        FileWriter writer = new FileWriter();
        writer.AppendLine("using Newtonsoft.Json;");
        writer.AppendLine("using Newtonsoft.Json.Linq;");
        GraphCodeGenerateUtility.AppendUsingNamespaceHeader(writer);
        GraphCodeGenerateUtility.AppendAutoGenTips(writer);
        writer.AppendLine();
        writer.AppendLine("namespace {0}", DataDefine.QuestGraphRuntimeNameSpace);
        writer.StartBlock();
        {
            writer.AppendLine($"/// <summary> Json反序列化辅助类 </summary>");
            writer.AppendLine($"public partial class DeserializeUtility");
            writer.StartBlock();
            {
                AppendBaseDeserializeFunc(writer, rootElement, "QuestNode");
                AppendBaseDeserializeFunc(writer, rootElement, "QuestCompleteCondition");
                AppendBaseDeserializeFunc(writer, rootElement, "QuestActiveCondition");
                AppendBaseDeserializeFunc(writer, rootElement, "QuestAction");
                writer.AppendLine();
                writer.AppendLine();
                AppendStructureDeserializeFunc(writer, rootElement, "StructureDefine");
                AppendQuestNodeDeserializeFunc(writer, rootElement, "QuestNode");
                AppendDeserializeFunc(writer, rootElement, "QuestCompleteCondition");
                AppendDeserializeFunc(writer, rootElement, "QuestActiveCondition");
                AppendDeserializeFunc(writer, rootElement, "QuestAction");
            }
            writer.EndBlock();
        }
        writer.EndBlock();
        writer.WriteFile(Path.Combine(filePath, $"DeserializeUtility.cs"));

    }

    private static void AppendBaseDeserializeFunc(FileWriter writer, SecurityElement rootElement, String elemName)
    {
        writer.AppendLine();
        SecurityElement element = rootElement.GetElement(elemName);
        if (element != null && element.Children.Count > 0)
        {
            String className = $"{elemName}";
            writer.AppendLine($"public static {className} Deserialize{className}(JObject jo)");
            writer.StartBlock();
            {
                writer.AppendLine($"{className} result = null;");

                // switch begin
                writer.AppendLine($"String typeFullName = jo[\"$type\"].Value<String>();");
                writer.AppendLine($"switch (typeFullName)");
                writer.StartBlock();
                {
                    if (element.Children != null && element.Children.Count > 0)
                    {
                        foreach (SecurityElement elem in element.Children)
                        {
                            String nodeName = elem.Attribute("name");
                            writer.AppendLine($"case \"{DataDefine.QuestGraphEditorNameSpace}.{nodeName}\":");
                            writer.StartTab();
                            {
                                writer.AppendLine($"result = DeserializeUtility.Deserialize{nodeName}(jo);");
                                writer.AppendLine($"break;");
                            }
                            writer.EndTab();
                        }
                    }
                    writer.AppendLine("default: break;");
                }
                writer.EndBlock();
                // switch end

                writer.AppendLine($"return result;");
            }
            writer.EndBlock();
        }
    }

    private static void AppendDeserializeFunc(FileWriter writer, SecurityElement rootElement, String elemName)
    {
        writer.AppendLine($"#region {elemName}");
        writer.AppendLine();

        SecurityElement elements = rootElement.GetElement(elemName);
        if (elements != null && elements.Children.Count > 0)
        {
            foreach (SecurityElement element in elements.Children)
            {
                String className = element.Attribute("name");
                String className1 = $"{Prefix}{className}";
                writer.AppendLine($"public static {className1} Deserialize{className}(JObject jo)");
                writer.StartBlock();
                {
                    writer.AppendLine($"{className1} data = new {className1}();");
                    writer.AppendLine($"foreach (KeyValuePair<String, JToken> item1 in jo)");
                    writer.StartBlock();
                    {
                        if (element.Children != null && element.Children.Count > 0)
                        {
                            foreach (SecurityElement elem in element.Children)
                            {
                                AppendField(writer, elem);
                            }
                        }
                    }
                    writer.EndBlock();
                    writer.AppendLine($"return data;");
                }
                writer.EndBlock();
            }
        }

        writer.AppendLine();
        writer.AppendLine($"#endregion");
        writer.AppendLine();
        writer.AppendLine();
    }

    private static void AppendStructureDeserializeFunc(FileWriter writer, SecurityElement rootElement, String elemName)
    {
        writer.AppendLine($"#region {elemName}");
        writer.AppendLine();

        SecurityElement elements = rootElement.GetElement(elemName);
        if (elements != null && elements.Children.Count > 0)
        {
            foreach (SecurityElement element in elements.Children)
            {
                String className = element.Attribute("name");
                writer.AppendLine($"public static {className} Deserialize{className}(JObject jo)");
                writer.StartBlock();
                {
                    writer.AppendLine($"{className} data = new {className}();");
                    writer.AppendLine($"foreach (KeyValuePair<String, JToken> item1 in jo)");
                    writer.StartBlock();
                    {
                        if (element.Children != null && element.Children.Count > 0)
                        {
                            foreach (SecurityElement elem in element.Children)
                            {
                                AppendField(writer, elem);
                            }
                        }
                    }
                    writer.EndBlock();
                    writer.AppendLine($"return data;");
                }
                writer.EndBlock();
            }
        }

        writer.AppendLine();
        writer.AppendLine($"#endregion");
        writer.AppendLine();
        writer.AppendLine();
    }

    private static void AppendQuestNodeDeserializeFunc(FileWriter writer, SecurityElement rootElement, String elemName)
    {
        writer.AppendLine($"#region {elemName}");
        writer.AppendLine();
        SecurityElement elements = rootElement.GetElement(elemName);
        if (elements != null && elements.Children.Count > 0)
        {
            foreach (SecurityElement element in elements.Children)
            {
                String className = element.Attribute("name");
                String className1 = $"{Prefix}{className}";
                writer.AppendLine($"public static {className1} Deserialize{className}(JObject jo)");
                writer.StartBlock();
                {
                    writer.AppendLine($"{className1} data = new {className1}();");
                    writer.AppendLine("data.NodeUid = jo[\"$id\"].Value<Int32>();");
                    writer.AppendLine();
                    writer.AppendLine("foreach (KeyValuePair<String, JToken> item1 in jo)");
                    writer.StartBlock();
                    {
                        if (element.Children != null && element.Children.Count > 0)
                        {
                            foreach (SecurityElement elem in element.Children)
                            {
                                AppendField(writer, elem);
                            }
                        }
                    }
                    writer.EndBlock();
                    writer.AppendLine($"return data;");
                }
                writer.EndBlock();
            }
        }
        writer.AppendLine();
        writer.AppendLine($"#endregion");
        writer.AppendLine();
        writer.AppendLine();
    }

    private static void AppendField(FileWriter writer, SecurityElement element)
    {
        String fieldName = element.Attribute("name");
        String typeName = element.Attribute("type");
        String typeFullName = GetTypeFullName(typeName);


        Type type = Type.GetType(typeName);
        Boolean isList = element.BooleanAttribute("isList");
        Boolean isBuildInType = BuiltInTypes.ContainsKey(typeName);
        Boolean isEnum = type != null ? type.IsEnum : EnumTypes.Contains(typeName);
        Boolean isCustomType = ClassTypes.Contains(typeName);

        if (isList)
        {
            // 列表类型
            writer.AppendLine($"if (item1.Key.ToString() == \"{fieldName}\")");
            writer.StartBlock();
            {
                writer.AppendLine($"data.{fieldName} = new List<{typeName}>();");
                writer.AppendLine($"foreach (JObject item2 in item1.Value.Values<JObject>())");
                writer.StartBlock();
                {
                    if (isBuildInType)
                    {
                        writer.AppendLine($"{typeName} elem = item2.Value<{typeName}>();");
                    }
                    else if (isCustomType)
                    {
                        writer.AppendLine($"{typeName} elem = DeserializeUtility.Deserialize{typeName}(item2);");
                    }
                    else
                    {
                        writer.AppendLine($"{typeName} elem = DeserializeUtility.Deserialize{typeName}(item2);");
                    }
                    writer.AppendLine($"data.{fieldName}.Add(elem);");
                }
                writer.EndBlock();
                writer.AppendLine("continue;");
            }
            writer.EndBlock();
        }
        else
        {
            String assign = String.Empty;
            if (isBuildInType)
            {
                assign = $"data.{fieldName} = item1.Value.Value<{typeName}>();";
                writer.AppendLine($"if (item1.Key.ToString() == \"{fieldName}\") {{ {assign} continue; }}");
            }
            else if (isEnum)
            {
                assign = $"data.{fieldName} = ({typeName})item1.Value.Value<Int32>();";
                writer.AppendLine($"if (item1.Key.ToString() == \"{fieldName}\") {{ {assign} continue; }}");
            }
            else if (isCustomType)
            {
                writer.AppendLine($"if (item1.Key.ToString() == \"{fieldName}\")");
                writer.StartBlock();
                {
                    writer.AppendLine($"data.{fieldName} = DeserializeUtility.Deserialize{typeName}(item1.Value as JObject);");
                    writer.AppendLine("continue;");
                }
                writer.EndBlock();
            }
            else
            {
                assign = $"data.{fieldName} = Activator.CreateInstance(typeof({typeFullName})) as {typeFullName};";
                writer.AppendLine($"if (item1.Key.ToString() == \"{fieldName}\") {{ {assign} continue; }}");
            }
        }
    }

    #endregion



    public static void AppendFieldDeclare(FileWriter writer, SecurityElement element)
    {
        String val = String.Empty;
        String description = String.Empty;
        if (element.Attribute("attrName", out val)) description = $"{val}";
        if (element.Attribute("attrDesc", out val)) description = $"{description} - {val}";
        writer.AppendLine($"/// <summary> {description} </summary>");
        String fieldName = element.Attribute("name");
        String typeName = element.Attribute("type");
        if (element.BooleanAttribute("isList"))
        {
            writer.AppendLine($"public List<{typeName}> {fieldName} {{ get; set; }}");
        }
        else
        {
            writer.AppendLine($"public {typeName} {fieldName} {{ get; set; }}");
        }
    }

    public static void AppendComment(FileWriter writer, SecurityElement element)
    {
        String description = String.Empty;
        if (element.Attribute("attrName", out String attrName)) description = $"{attrName}";
        if (element.Attribute("attrDesc", out String attrDesc)) description = $"{description} - {attrDesc}";
        writer.AppendLine($"/// <summary> {description} </summary>");
    }

    public static String GetTypeFullName(String typeName)
    {
        String typeFullName = typeName;
        if (BuiltInTypes.ContainsKey(typeName))
        {
            typeFullName = BuiltInTypes[typeName];
        }
        else if (EnumTypes.Contains(typeName) || ClassTypes.Contains(typeName))
        {
            typeFullName = $"{DataDefine.QuestGraphRuntimeNameSpace}.{typeName}";
        }
        return typeFullName;
    }


}


#endif