
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

public static class MaterialKeywordTools {
    private const string keywordReportFilename = "/KeywordToolsReport.txt";
    private static string[] keywordsToRemove = new string[] {
        // -----------------------------------------------------
        // Your keywords go HERE! \/
        // -----------------------------------------------------
        "_AO2MAPUV_UV0",
    };

    [MenuItem("Project/Keyword Tools/Generate Variants Collection", false, 100)]
    private static void GenerateVariantsAll()
    {
        GenerateVariants(false);
    }

    [MenuItem("Project/Keyword Tools/Generate Variants Collection(With Log)", false, 100)]
    private static void GenerateVariantsAllLog()
    {
        GenerateVariants(true);
    }

    [MenuItem("Project/Keyword Tools/Instantiate FX", false, 100)]
    private static void InstantiateFX()
    {
        var folders = new string[] { "Assets/GameProject/RuntimeAssets/FX" };
        var objAssets = AssetDatabase.FindAssets("t:GameObject", folders);
        foreach (string objAsset in objAssets)
        {
            var path = AssetDatabase.GUIDToAssetPath(objAsset);
            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            GameObject gObj = PrefabUtility.InstantiatePrefab(obj) as GameObject;
            //if (gObj != null)
            //{
            //    GameObject.DestroyImmediate(gObj);
            //}
        }

    }


    private static void GenerateVariants(bool bLog)
    {
        var collection = new ShaderVariantCollection();
        var folders = new string[]{ "Assets/GameProject/RuntimeAssets/FX" };
        var mats = AssetDatabase.FindAssets("t:Material", folders);
        Dictionary<Shader, List<HashSet<string>>> shaderVariant = new Dictionary<Shader, List<HashSet<string>>>();
        foreach (var guid in mats)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var mat = AssetDatabase.LoadAssetAtPath<Material>(path);
            if (!shaderVariant.ContainsKey(mat.shader))
            {
                shaderVariant.Add(mat.shader, new List<HashSet<string>>());
            }
            if (mat.shaderKeywords.Length > 0)
            {
                HashSet<string> shaderKeywords = new HashSet<string>(mat.shaderKeywords);
                bool bFind = false;
                foreach (HashSet<string> hhSet in shaderVariant[mat.shader])
                {
                    if (hhSet.Count == shaderKeywords.Count && hhSet.SetEquals(shaderKeywords))
                    {
                        bFind = true;
                        break;
                    }
                }
                if (!bFind)
                {
                    shaderVariant[mat.shader].Add(shaderKeywords);
                }
            }
        }
        if (bLog)
        {
            FileInfo fileInfo = new FileInfo(Application.dataPath + "/ShaderVariants.log");
            StreamWriter fileWr = fileInfo.CreateText();
            foreach (Shader shader in shaderVariant.Keys)
            {
                fileWr.WriteLine("===ShaderName: " + shader.name);
                foreach (HashSet<string> hashSet in shaderVariant[shader])
                {
                    StringBuilder sb = new StringBuilder();
                    foreach (string s in hashSet)
                    {
                        sb.Append(s);
                        sb.Append(";");
                    }
                    fileWr.WriteLine(sb.ToString());
                }
                fileWr.WriteLine("==============");
            }
            fileWr.Close();
        }
        foreach (Shader shader in shaderVariant.Keys)
        {
            var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.Normal, "");
            collection.Add(variant);
            foreach (HashSet<string> hashSet in shaderVariant[shader])
            {

                try
                {
                    var variantC = new ShaderVariantCollection.ShaderVariant(shader, PassType.Normal, hashSet.ToArray());
                    collection.Add(variantC);
                }
                catch (ArgumentException e)
                {
                    Debug.LogError(e.Message);
                    throw;
                }
                
            }
        }
        //var variant = new ShaderVariantCollection.ShaderVariant(mat.shader, PassType.ForwardBase, mat.shaderKeywords);
        //collection.Add(variant);
        AssetDatabase.CreateAsset(collection, "Assets/GameProject/RuntimeAssets/Shader_ABS/MaterialShader.shadervariants");
    }

    [MenuItem("Project/Keyword Tools/Reporter", false, 100)]
    private static void Reporter() {
        var fileName = Application.dataPath + keywordReportFilename;
        var sr = File.CreateText(fileName);
        var materials = GetSceneMaterialsWithKeywords().OrderBy(m => m.name);
        var keywordList = materials
                        .SelectMany(m => m.shaderKeywords)
                        .Where(k => !string.IsNullOrEmpty(k))
                        .Distinct()
                        .OrderBy(k => k);
        var keywordCount = keywordList.Count();
        var materialShaderNames = new List<string>();

        sr.WriteLine(" ");
        sr.WriteLine("-----------------------------------------------------------------------");
        sr.WriteLine(" Keywords: " + keywordCount);
        sr.WriteLine("-----------------------------------------------------------------------");

        foreach (var keyword in keywordList) {
            sr.WriteLine("\"" + keyword + "\",");
        }

        sr.WriteLine(" ");
        sr.WriteLine("-----------------------------------------------------------------------");
        sr.WriteLine(" Keywords -> Materials: " + keywordCount);
        sr.WriteLine("-----------------------------------------------------------------------");

        foreach (var keyword in keywordList) {
            sr.WriteLine("\"" + keyword + "\",");

            foreach (var material in materials) {
                var shaderKeywords = material.shaderKeywords;

                if (shaderKeywords.Contains(keyword)) {
                    sr.WriteLine("    " + material.name);
                }
            }

            sr.WriteLine(" ");
        }

        sr.WriteLine("-----------------------------------------------------------------------");
        sr.WriteLine(" Materials -> Keywords: " + materials.Count());
        sr.WriteLine("-----------------------------------------------------------------------");

        foreach (var material in materials) {
            var shaderKeywords = material.shaderKeywords.OrderBy(k => k);

            sr.WriteLine(material.name);

            foreach (var keyword in shaderKeywords) {
                if (!string.IsNullOrEmpty(keyword)) {
                    sr.WriteLine("    \"" + keyword + "\",");
                }
            }

            sr.WriteLine(" ");
            materialShaderNames.Add(material.shader.name);
        }

        var shaderNames = materialShaderNames.Distinct().OrderBy(s => s);

        sr.WriteLine("-----------------------------------------------------------------------");
        sr.WriteLine(" Shaders -> Materials: " + shaderNames.Count());
        sr.WriteLine("-----------------------------------------------------------------------");

        foreach (var shaderName in shaderNames) {
            var shaderMaterials = materials.Where(m => m.shader.name == shaderName).Select(m => m.name);

            sr.WriteLine("\"" + shaderName + "\"");

            foreach (var materialName in shaderMaterials) {
                if (!string.IsNullOrEmpty(materialName)) {
                    sr.WriteLine("    " + materialName);
                }
            }

            sr.WriteLine(" ");
        }

        sr.Close();
        System.Diagnostics.Process.Start(fileName);
    }

    [MenuItem("Project/Keyword Tools/Obliterater", false, 100)]
    private static void Obliterater() {
        var materials = GetSceneMaterialsWithKeywords().ToList();

        try {
            for (int i = 0, length = materials.Count(); i < length; i++) {
                var material = materials[i];
                var keywordsToRemove = material.shaderKeywords.Intersect(MaterialKeywordTools.keywordsToRemove);

                EditorUtility.DisplayProgressBar(
                    "Removing Keywords...",
                    string.Format("{0} / {1} materials cleaned.", i, length),
                    i / (float)(length - 1));

                if (keywordsToRemove.Count() > 0) {
                    foreach (var keyword in keywordsToRemove) {
                        if (!string.IsNullOrEmpty(keyword)) {
                            material.DisableKeyword(keyword);
                        }
                    }

                    EditorUtility.SetDirty(material);
                }
            }
        }
        finally {
            EditorUtility.ClearProgressBar();
        }
    }

    private static IEnumerable<Material> GetSceneMaterialsWithKeywords() {
        return Resources.FindObjectsOfTypeAll<Material>().Where(m => m.shaderKeywords.Length > 0);
    }
}
