using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace Framework.Editor
{
    /// <summary>
    /// 编码转换工具，用于检测和转换CS文件的编码
    /// </summary>
    public class EncodingConverter : EditorWindow
    {
        // 搜索路径
        private string _searchPath = "Assets";
        // 是否包含子目录
        private bool _includeSubdirectories = true;
        // 检测到的GBK/GB编码文件列表
        private List<FileInfo> _gbkFiles = new List<FileInfo>();
        // 是否显示详细信息
        private bool _showDetails = false;
        // 滚动视图位置
        private Vector2 _scrollPosition;
        // 转换状态
        private string _conversionStatus = "";
        // 是否正在扫描
        private bool _isScanning = false;
        // 是否正在转换
        private bool _isConverting = false;

        // 添加菜单项
        [MenuItem("Tools/编码转换工具")]
        public static void ShowWindow()
        {
            GetWindow<EncodingConverter>("编码转换工具");
        }

        private void OnGUI()
        {
            GUILayout.Label("CS文件编码检测与转换工具", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // 路径选择
            EditorGUILayout.BeginHorizontal();
            _searchPath = EditorGUILayout.TextField("搜索路径:", _searchPath);
            if (GUILayout.Button("浏览", GUILayout.Width(60)))
            {
                string path = EditorUtility.OpenFolderPanel("选择要检测的文件夹", _searchPath, "");
                if (!string.IsNullOrEmpty(path))
                {
                    // 将绝对路径转换为相对于项目的路径
                    if (path.StartsWith(Application.dataPath))
                    {
                        _searchPath = "Assets" + path.Substring(Application.dataPath.Length);
                    }
                    else
                    {
                        _searchPath = path;
                    }
                }
            }
            EditorGUILayout.EndHorizontal();

            _includeSubdirectories = EditorGUILayout.Toggle("包含子目录", _includeSubdirectories);

            EditorGUILayout.Space();

            // 功能按钮
            EditorGUILayout.BeginHorizontal();
            GUI.enabled = !_isScanning && !_isConverting;
            if (GUILayout.Button("检测GBK/GB编码文件"))
            {
                _gbkFiles.Clear();
                _conversionStatus = "";
                _isScanning = true;
                EditorApplication.delayCall += () =>
                {
                    ScanGbkFiles();
                    _isScanning = false;
                    Repaint();
                };
            }

            GUI.enabled = !_isScanning && !_isConverting && _gbkFiles.Count > 0;
            if (GUILayout.Button("转换为UTF-8"))
            {
                if (EditorUtility.DisplayDialog("确认转换", 
                    $"确定要将 {_gbkFiles.Count} 个文件从GBK/GB编码转换为UTF-8编码吗？", 
                    "确定", "取消"))
                {
                    _isConverting = true;
                    EditorApplication.delayCall += () =>
                    {
                        ConvertToUtf8();
                        _isConverting = false;
                        Repaint();
                    };
                }
            }
            GUI.enabled = true;
            EditorGUILayout.EndHorizontal();

            // 状态信息
            if (_isScanning)
            {
                EditorGUILayout.HelpBox("正在扫描文件...", MessageType.Info);
            }
            else if (_isConverting)
            {
                EditorGUILayout.HelpBox("正在转换文件...", MessageType.Info);
            }
            else if (!string.IsNullOrEmpty(_conversionStatus))
            {
                EditorGUILayout.HelpBox(_conversionStatus, MessageType.Info);
            }

            // 显示检测结果
            if (_gbkFiles.Count > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField($"检测到 {_gbkFiles.Count} 个GBK/GB编码的CS文件:");
                
                _showDetails = EditorGUILayout.Foldout(_showDetails, "详细信息");
                
                if (_showDetails)
                {
                    _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
                    foreach (var file in _gbkFiles)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField(GetRelativePath(file.FullName));
                        if (GUILayout.Button("定位", GUILayout.Width(60)))
                        {
                            // 获取相对于Assets的路径
                            string relativePath = GetRelativePath(file.FullName);
                            // 在Project窗口中选中该文件
                            UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(relativePath);
                            if (obj != null)
                            {
                                Selection.activeObject = obj;
                                EditorGUIUtility.PingObject(obj);
                            }
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                    EditorGUILayout.EndScrollView();
                }
            }
            else if (!_isScanning && string.IsNullOrEmpty(_conversionStatus))
            {
                EditorGUILayout.HelpBox("请点击检测GBK/GB编码文件按钮开始检测。", MessageType.Info);
            }
        }

        /// <summary>
        /// 扫描GBK/GB编码的CS文件
        /// </summary>
        private void ScanGbkFiles()
        {
            _gbkFiles.Clear();
            
            try
            {
                string fullPath = GetFullPath(_searchPath);
                if (!Directory.Exists(fullPath))
                {
                    _conversionStatus = $"路径不存在: {fullPath}";
                    return;
                }

                SearchOption searchOption = _includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                string[] files = Directory.GetFiles(fullPath, "*.cs", searchOption);

                foreach (string file in files)
                {
                    if (IsGbkEncoded(file))
                    {
                        _gbkFiles.Add(new FileInfo(file));
                    }
                }

                if (_gbkFiles.Count == 0)
                {
                    _conversionStatus = "未检测到GBK/GB编码的CS文件。";
                }
                else
                {
                    _conversionStatus = $"检测完成，发现 {_gbkFiles.Count} 个GBK/GB编码的CS文件。";
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"扫描文件时出错: {ex.Message}");
                _conversionStatus = $"扫描文件时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 将GBK/GB编码的文件转换为UTF-8编码
        /// </summary>
        private void ConvertToUtf8()
        {
            int successCount = 0;
            int failCount = 0;
            List<string> failedFiles = new List<string>();

            try
            {
                // 开始转换前先刷新资源数据库
                AssetDatabase.StartAssetEditing();

                foreach (var file in _gbkFiles)
                {
                    try
                    {
                        // 直接读取文件的二进制数据
                        byte[] fileBytes = File.ReadAllBytes(file.FullName);
                        
                        // 使用GBK编码解码文件内容
                        string content = Encoding.GetEncoding("GBK").GetString(fileBytes);
                        
                        // 写入文件（使用UTF-8编码，不带BOM）
                        File.WriteAllText(file.FullName, content, new UTF8Encoding(false));
                        
                        Debug.Log($"成功转换文件: {GetRelativePath(file.FullName)}");
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"转换文件 {file.FullName} 时出错: {ex.Message}");
                        failedFiles.Add(file.FullName);
                        failCount++;
                    }
                }
            }
            finally
            {
                // 完成后刷新资源数据库
                AssetDatabase.StopAssetEditing();
                AssetDatabase.Refresh();
            }

            // 更新转换状态
            _conversionStatus = $"转换完成。成功: {successCount}, 失败: {failCount}";
            
            // 如果有失败的文件，记录到日志
            if (failCount > 0)
            {
                Debug.LogWarning($"有 {failCount} 个文件转换失败:");
                foreach (var file in failedFiles)
                {
                    Debug.LogWarning($"- {file}");
                }
            }

            // 转换完成后重新扫描
            _gbkFiles.Clear();
            ScanGbkFiles();
        }

        /// <summary>
        /// 检测文件是否为GBK/GB编码
        /// </summary>
        private bool IsGbkEncoded(string filePath)
        {
            try
            {
                // 读取文件的二进制数据
                byte[] bytes = File.ReadAllBytes(filePath);
                
                // 检查是否有UTF-8 BOM
                if (bytes.Length >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF)
                {
                    return false; // 有UTF-8 BOM，不是GBK
                }
                
                // 尝试以UTF-8解码
                string utf8Content;
                try
                {
                    utf8Content = Encoding.UTF8.GetString(bytes);
                }
                catch
                {
                    return true; // UTF-8解码失败，可能是GBK
                }
                
                // 尝试以GBK解码
                string gbkContent;
                try
                {
                    gbkContent = Encoding.GetEncoding("GBK").GetString(bytes);
                }
                catch
                {
                    return false; // GBK解码失败，不是GBK
                }
                
                // 检查是否包含中文字符
                bool hasChineseChars = false;
                for (int i = 0; i < gbkContent.Length; i++)
                {
                    if (gbkContent[i] >= 0x4E00 && gbkContent[i] <= 0x9FFF)
                    {
                        hasChineseChars = true;
                        break;
                    }
                }
                
                if (!hasChineseChars)
                {
                    return false; // 不包含中文字符，可能不需要转换
                }
                
                // 将UTF-8编码的内容再次编码为字节数组
                byte[] utf8Bytes = Encoding.UTF8.GetBytes(utf8Content);
                
                // 如果UTF-8重新编码后的字节数组与原始字节数组不同，可能是GBK编码
                if (!ByteArraysEqual(bytes, utf8Bytes))
                {
                    // 检查是否有GBK特有的编码范围
                    for (int i = 0; i < bytes.Length - 1; i++)
                    {
                        byte b1 = bytes[i];
                        byte b2 = bytes[i + 1];
                        
                        // GBK编码范围
                        if ((b1 >= 0x81 && b1 <= 0xFE) && 
                            ((b2 >= 0x40 && b2 <= 0x7E) || (b2 >= 0x80 && b2 <= 0xFE)))
                        {
                            return true; // 符合GBK编码特征
                        }
                    }
                }
                
                // 检查UTF-8解码后再重新编码是否与原始内容相同
                string reEncodedUtf8 = Encoding.UTF8.GetString(Encoding.UTF8.GetBytes(utf8Content));
                if (utf8Content != reEncodedUtf8)
                {
                    return true; // UTF-8解码再编码不一致，可能是GBK
                }
                
                return false; // 默认不是GBK
            }
            catch
            {
                // 如果解码过程中出错，可能是编码问题，保守起见认为是GBK
                return true;
            }
        }
        
        /// <summary>
        /// 比较两个字节数组是否相等
        /// </summary>
        private bool ByteArraysEqual(byte[] a, byte[] b)
        {
            if (a.Length != b.Length)
                return false;
                
            for (int i = 0; i < a.Length; i++)
            {
                if (a[i] != b[i])
                    return false;
            }
            
            return true;
        }

        /// <summary>
        /// 获取相对于项目的路径
        /// </summary>
        private string GetRelativePath(string fullPath)
        {
            if (fullPath.StartsWith(Application.dataPath))
            {
                return "Assets" + fullPath.Substring(Application.dataPath.Length);
            }
            return fullPath;
        }

        /// <summary>
        /// 获取完整路径
        /// </summary>
        private string GetFullPath(string relativePath)
        {
            if (relativePath.StartsWith("Assets"))
            {
                return Application.dataPath + relativePath.Substring(6);
            }
            return relativePath;
        }
    }
}
