using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using UnityEditor.SceneManagement;
using System.Collections.Generic;
using System.Linq;
using YooAsset.Editor;
using MyFramework;
using YooAsset;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

public class BuildApp
{

    // [MenuItem("BuildApp/BuildTest")]
    public static void BuildTest()
    {
        // BuildHash();

        // string _outputPath = Path.Combine(Application.streamingAssetsPath, Code.BDFramework.Core.Tools.BDApplication.GetPlatformPath(RuntimePlatform.Android));
        // FmodBuildConfig.GenerateConfig(_outputPath, BuildTarget.Android);

        
    }
    

    

    public enum ParamType
    {
        Int,
        Bool,
        String,
        Enum
    }


    public static void Build()
    {
        Debug.Log("[UnityBuildStart] 获取参数...");

        //生成csproj
        // Unity.CodeEditor.CodeEditor.CurrentEditor.SyncAll();

        //设置宏定义
        SetDefineSymbols();

        // 检查aot引用
        CheckAotReferences();

        //激活构建平台
        ActiveBuildTarget();

        BuildDlls();

        BuildAssetBundles();

        BuildAppFile();
        

        // string BuildPipelineStr = GetParameter("BuildPipeline".ToUpper());
        // Debug.Log("BuildPipeline : " + BuildPipelineStr);
        // EDefaultBuildPipeline eBuildPipeline = (EDefaultBuildPipeline)Enum.Parse(typeof(EDefaultBuildPipeline), BuildPipelineStr);


        // BuildVersion.BuildVersionCMD_Complex(buildTarget, YooAsset.EPlayMode.OfflinePlayMode, false, true, false, false, false);

        Debug.Log("[UnityBuildEnd] 打包结束！");
    }

    public static void ChangeConfigParam(string configPath, Dictionary<string, string> paramDics, Dictionary<string, ParamType> typeDics)
    {
        try
        {
            // string path = "Assets/StreamingAssets/Config/GameConfig.txt";
            TextAsset textContent = AssetDatabase.LoadAssetAtPath<TextAsset>(configPath);
            JObject jsonData = JObject.Parse(textContent.text);
            // JsonData jsonData = JsonMapper.ToObject(textContent.text);

            // Debug.Log("读取配置 " + jsonData[key].ToString());

            foreach (var itemDic in paramDics)
            {
                string key = itemDic.Key;
                string value = itemDic.Value;
                ParamType type = typeDics[key];
                if (type == ParamType.String)
                {
                    jsonData[key] = value;
                }
                else if (type == ParamType.Bool)
                {
                    if (value == "true")
                    {
                        jsonData[key] = true;
                    }
                    else
                    {
                        jsonData[key] = false;
                    }
                }
                else if (type == ParamType.Int)
                {
                    jsonData[key] = Convert.ToInt32(value);
                }
                else if (type == ParamType.Enum)
                {
                    // if (key == "Channel")
                    // {
                    //     ChannelEnum enumValue = (ChannelEnum)Enum.Parse(typeof(ChannelEnum), value);
                    //     jsonData[key] = (int)enumValue;
                    // }
                }
            }

            
            string json = JsonConvert.SerializeObject(jsonData);
            // string json = JsonMapper.ToJson(jsonData);
            Debug.Log(json);

            File.WriteAllText(configPath, json);

            Debug.Log("[UnityBuildProcess] ChangeConfigParam " + configPath + ":" + json);

            AssetDatabase.Refresh();
        }
        catch (Exception e)
        {
            Debug.LogError(e.Message);
        }
    }

    private static void CheckAotReferences()
    {
        Debug.Log("[UnityBuildProcess] CheckAotReferences...");

        //开启hybridclr
        HybridCLR.Editor.SettingsUtil.Enable = true;

        //判断是否存在脚本错误引用
        var referencesHot = BuildDLLCommand.CheckAotReferences();
        if (referencesHot)
        {
            Debug.LogError("[UnityBuildError] CheckAotReferences: " + referencesHot.ToString());
            return;
        }
        else
        {
            Debug.Log("[UnityBuildProcess] CheckAotReferences: " + referencesHot.ToString());
        }
    }

    private static void BuildDlls()
    {
        BuildTarget buildTarget = GetBuildTarget();
        string IsBuildDll = GetParameter("IsBuildDll".ToUpper());
        Debug.Log("IsBuildDll : " + IsBuildDll);
        bool isBuildDll = IsBuildDll == "true";
        if (isBuildDll)
        {
            Debug.Log("[UnityBuildProcess] GenerateAllAndCopyDlls...");
            BuildDLLCommand.GenerateAllAndCopyDlls(buildTarget);
        }
        else
        {
            // BuildDLLCommand.CopyAOTHotUpdateDlls(buildTarget);
        }
    }

    private static void BuildAssetBundles()
    {
        string IsBuildAssetBundles = GetParameter("IsBuildAssetBundles".ToUpper());
        Debug.Log("IsBuildAssetBundles : " + IsBuildAssetBundles);
        bool isBuildAssetBundles = IsBuildAssetBundles == "true";
        if (!isBuildAssetBundles)
        {
            return;
        }

        Debug.Log("[UnityBuildProcess] 开始打资源包...");

        string IsMiniPackage = GetParameter("IsMiniPackage".ToUpper());
        Debug.Log("IsMiniPackage : " + IsMiniPackage);
        EBuildinFileCopyOption copyOption = EBuildinFileCopyOption.ClearAndCopyAll;
        if (IsMiniPackage == "true")
        {
            copyOption = EBuildinFileCopyOption.None;
        }

        BuildTarget buildTarget = GetBuildTarget();
        BuildResult buildResult = YooAssetBuild_SBP(buildTarget, copyOption);
        if (buildResult.Success)
        {
            Debug.Log("[UnityBuildProcess] 打资源包成功！");

            string dirPath = GetParameter("app_dir");
            
            // Move the output package directory to dir_path
            if (!string.IsNullOrEmpty(dirPath))
            {
                try
                {
                    string bundlesPath = dirPath + "/Bundles";
                    if (!Directory.Exists(bundlesPath))
                    {
                        Directory.CreateDirectory(bundlesPath);
                    }
                    
                    string outputDir = buildResult.OutputPackageDirectory;
                    string lastDirName = Path.GetFileName(outputDir);
                    string destDir = Path.Combine(bundlesPath, lastDirName);
                    
                    if (Directory.Exists(destDir))
                    {
                        Directory.Delete(destDir, true);
                    }
                    
                    Directory.Move(outputDir, destDir);
                    Debug.Log($"[UnityBuildProcess] 已移动资源包到: {destDir}");

                    string configPath = "Assets/StreamingAssets/Config/ServerUrl.txt";

                    string AssetBundlesUrl = GetParameter("AssetBundlesUrl".ToUpper());
                    Debug.Log("AssetBundlesUrl : " + AssetBundlesUrl);

                    string BranchName = GetParameter("BranchName".ToUpper());
                    Debug.Log("BranchName : " + BranchName);

                    string AppVersion = GetParameter("AppVersion".ToUpper());
                    Debug.Log("AppVersion : " + AppVersion);

                    // http://10.27.239.38:8081/Bundles/{BranchName}/{AppVersion}/{BundleVersion}
                    AssetBundlesUrl = AssetBundlesUrl.Replace("{BranchName}", BranchName);
                    AssetBundlesUrl = AssetBundlesUrl.Replace("{AppVersion}", AppVersion);
                    AssetBundlesUrl = AssetBundlesUrl.Replace("{BundleVersion}", lastDirName);
                    string paramKey = "AssetBundleDownLoadServerList";
                    Dictionary<string, string> paramDics = new Dictionary<string, string>();
                    paramDics.Add(paramKey, AssetBundlesUrl);
                    Dictionary<string, ParamType> typeDics = new Dictionary<string, ParamType>();
                    typeDics.Add(paramKey, ParamType.String);
                    ChangeConfigParam(configPath, paramDics, typeDics);

                    string newFile = Path.Combine(bundlesPath, "ServerUrl.txt");
                    File.Copy(configPath, newFile, true);   
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[UnityBuildWarning] 移动资源包失败: {ex.Message}");
                }
            }
            // EditorUtility.RevealInFinder(buildResult.OutputPackageDirectory);
            // ExecuteRsync("rsync_package", buildParameters.GetPackageOutputDirectory(), BuildTargetUtility.BuildTarget2Platform(buildTarget));
        }
        else
        {
            Debug.Log("[UnityBuildError] FailedTask:" + buildResult.FailedTask);
            Debug.Log("[UnityBuildError] 打资源包失败：" + buildResult.ErrorInfo);
        }
    }


    public static BuildResult YooAssetBuild_SBP(BuildTarget buildTarget, EBuildinFileCopyOption copyOption)
    {
        string packageName = AssetBundleCollectorSettingData.Setting.Packages[0].PackageName;
        var buildPipeline = EBuildPipeline.ScriptableBuildPipeline;

        // string AssetBuildMode = GetParameter("AssetBuildMode".ToUpper());
        // Debug.Log("AssetBuildMode : " + AssetBuildMode);
        // EBuildMode eBuildMode = (EBuildMode)Enum.Parse(typeof(EBuildMode), AssetBuildMode);
        EBuildMode eBuildMode = EBuildMode.IncrementalBuild;
        AssetBundleBuilderSetting.SetPackageBuildMode(packageName, buildPipeline, eBuildMode);

        AssetBundleBuilderSetting.SetPackageBuildinFileCopyOption(packageName, buildPipeline, copyOption);

        AssetBundleBuilderSetting.SetPackageFileNameStyle(packageName, buildPipeline, EFileNameStyle.BundleName_HashName);

        AssetBundleBuilderSetting.SetPackageCompressOption(packageName, buildPipeline, ECompressOption.LZ4);
        
        var buildMode = AssetBundleBuilderSetting.GetPackageBuildMode(packageName, buildPipeline);
        var fileNameStyle = AssetBundleBuilderSetting.GetPackageFileNameStyle(packageName, buildPipeline);
        var buildinFileCopyOption = AssetBundleBuilderSetting.GetPackageBuildinFileCopyOption(packageName, buildPipeline);
        var buildinFileCopyParams = AssetBundleBuilderSetting.GetPackageBuildinFileCopyParams(packageName, buildPipeline);
        var compressOption = AssetBundleBuilderSetting.GetPackageCompressOption(packageName, buildPipeline);

        ScriptableBuildParameters buildParameters = new ScriptableBuildParameters();
        buildParameters.BuildOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
        buildParameters.BuildinFileRoot = AssetBundleBuilderHelper.GetStreamingAssetsRoot();
        buildParameters.BuildPipeline = buildPipeline.ToString();
        buildParameters.BuildTarget = buildTarget;
        buildParameters.BuildMode = buildMode;
        buildParameters.PackageName = packageName;
        buildParameters.PackageVersion = GetDefaultPackageVersion();
        buildParameters.EnableSharePackRule = true;
        buildParameters.VerifyBuildingResult = true;
        buildParameters.FileNameStyle = fileNameStyle;
        buildParameters.BuildinFileCopyOption = buildinFileCopyOption;
        buildParameters.BuildinFileCopyParams = buildinFileCopyParams;
        buildParameters.EncryptionServices = null;
        buildParameters.CompressOption = compressOption;

        ScriptableBuildPipeline pipeline = new ScriptableBuildPipeline();
        var buildResult = pipeline.Run(buildParameters, true);
        
        return buildResult; 
    }

    private static string GetDefaultPackageVersion()
    {
        int totalMinutes = DateTime.Now.Hour * 60 + DateTime.Now.Minute;
        return DateTime.Now.ToString("yyyy-MM-dd") + "-" + totalMinutes;
    }

    private List<string> GetBuildPackageNames()
    {
        List<string> result = new List<string>();
        foreach (var package in AssetBundleCollectorSettingData.Setting.Packages)
        {
            result.Add(package.PackageName);
        }
        return result;
    } 

    

    /// <summary>
    /// 设置宏定义
    /// </summary>
    private static void SetDefineSymbols()
    {
        Debug.Log("[UnityBuildProcess] SetReleaseDefineSymbols...");
        BuildTarget buildTarget = GetBuildTarget();
        string ReleaseDefineSymbols = GetParameter("ReleaseDefineSymbols".ToUpper());
        Debug.Log("ReleaseDefineSymbols : " + ReleaseDefineSymbols);
        bool isRelease = ReleaseDefineSymbols == "true";
        BuildVersion.SetReleaseDefineSymbols(buildTarget,isRelease);
        BuildTargetGroup buildTargetGroup = BuildTargetUtility.GetBuildTargetGroup(buildTarget);
        string currentSymbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup);
        Debug.Log("[UnityBuildProcess] New Scripting Define Symbols: " + currentSymbols);
    }

    private static void ActiveBuildTarget()
    {
        var buildTarget = GetBuildTarget();
        // Debug.Log("buildTarget:"+buildTarget.ToString());
        BuildTargetGroup buildTargetGroup = BuildPipeline.GetBuildTargetGroup(buildTarget);
        // Debug.Log("buildTargetGroup:"+buildTargetGroup.ToString());
        //切换构建平台
        if (EditorUserBuildSettings.activeBuildTarget != buildTarget)
        {
            Debug.Log("[UnityBuildProcess] 切换构建平台..." + buildTarget.ToString());
            
            EditorUserBuildSettings.SwitchActiveBuildTarget(buildTargetGroup, buildTarget);
        }

        PlayerSettings.SetScriptingBackend(buildTargetGroup, ScriptingImplementation.IL2CPP);
    }

    private static BuildTarget _buildTarget = BuildTarget.NoTarget;
    private static BuildTarget GetBuildTarget()
    {
        if (_buildTarget != BuildTarget.NoTarget)
        {
            return _buildTarget;
        }
        string BuildPlatform = GetParameter("BuildPlatform".ToUpper());
        Debug.Log("BuildPlatform : " + BuildPlatform);
        _buildTarget = (BuildTarget)Enum.Parse(typeof(BuildTarget), BuildPlatform);
        return _buildTarget;
    }

    private static BuildTargetGroup GetBuildTargetGroup()
    {
        string BuildPlatform = GetParameter("BuildPlatform".ToUpper());
        Debug.Log("BuildPlatform : " + BuildPlatform);
        BuildTargetGroup buildTarget = (BuildTargetGroup)Enum.Parse(typeof(BuildTargetGroup), BuildPlatform);
        return buildTarget;
    }



    private static void BuildAppFile()
    {
        string IsBuildApp = GetParameter("IsBuildApp".ToUpper());
        Debug.Log("IsBuildApp : " + IsBuildApp);
        bool isBuildApp = IsBuildApp == "true";

        if (!isBuildApp)
        {
            return;
        }

        SetAssetPlayMode();

        string IsMiniPackage = GetParameter("IsMiniPackage".ToUpper());
        Debug.Log("IsMiniPackage : " + IsMiniPackage);
        if(IsMiniPackage == "true")
        {
            EditorTools.ClearFolder(AssetBundleBuilderHelper.GetStreamingAssetsRoot());
            AssetDatabase.Refresh();
        }


        string AppVersion = GetParameter("AppVersion".ToUpper());
        Debug.Log("AppVersion : " + AppVersion);

        string CompanyName = GetParameter("CompanyName".ToUpper());
        Debug.Log("CompanyName : " + CompanyName);

        string ProductName = GetParameter("ProductName".ToUpper());
        Debug.Log("ProductName : " + ProductName);

        string PackageName = GetParameter("PackageName".ToUpper());
        Debug.Log("PackageName : " + PackageName);

        string Development = GetParameter("Development".ToUpper());
        Debug.Log("Development : " + Development);

        string StrictMode = GetParameter("StrictMode".ToUpper());
        Debug.Log("StrictMode : " + StrictMode);

        string appFile = GetParameter("app_file");
        Debug.Log("appFile : " + appFile);

        string configPath = "Assets/StreamingAssets/Config/GameConfig.txt";

        string ServerSettingUrl = GetParameter("ServerSettingUrl".ToUpper());
        Debug.Log("ServerSettingUrl : " + ServerSettingUrl);

        string BranchName = GetParameter("BranchName".ToUpper());
        Debug.Log("BranchName : " + BranchName);

        string dirPath = GetParameter("app_dir");
        string cachePath = dirPath + "/cache";

        // http://10.27.239.38:8081/Bundles/{BranchName}/{AppVersion}/{BundleVersion}
        ServerSettingUrl = ServerSettingUrl.Replace("{BranchName}", BranchName);
        ServerSettingUrl = ServerSettingUrl.Replace("{AppVersion}", AppVersion);
        string paramKey = "ServerSettingUrl";
        Dictionary<string, string> paramDics = new Dictionary<string, string>();
        paramDics.Add(paramKey, ServerSettingUrl);
        Dictionary<string, ParamType> typeDics = new Dictionary<string, ParamType>();
        typeDics.Add(paramKey, ParamType.String);
        ChangeConfigParam(configPath, paramDics, typeDics);

        string newFile = Path.Combine(cachePath, "GameConfig.txt");
        File.Copy(configPath, newFile, true);   

        //包体版本号
        PlayerSettings.bundleVersion = AppVersion;

        //公司名字
        PlayerSettings.companyName = CompanyName;

        //APP名字
        PlayerSettings.productName = ProductName;

        //包名：com.BiliBili.G42
        PlayerSettings.applicationIdentifier = PackageName;

        //不显示Unity logo
        PlayerSettings.SplashScreen.showUnityLogo = false;
        PlayerSettings.SplashScreen.show = false;

        //不导出Android Project
        EditorUserBuildSettings.exportAsGoogleAndroidProject = false;

        // string IsMiniPackage = GetParameter("IsMiniPackage".ToUpper());
        // Debug.Log("IsMiniPackage : " + IsMiniPackage);
        // bool isMiniPackage = IsMiniPackage == "true";
        // if (isMiniPackage)
        // {
        //     EditorTools.ClearFolder(AssetBundleBuilderHelper.GetStreamingAssetsRoot());
        //     AssetDatabase.Refresh();
        // }

        EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;

        // 构建场景列表
        string[] scenePaths = new string[scenes.Length];
        for (int i = 0; i < scenes.Length; i++)
        {
            scenePaths[i] = scenes[i].path;
        }

        BuildTarget buildTarget = GetBuildTarget();

        BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();
        buildPlayerOptions.scenes = scenePaths;
        buildPlayerOptions.target = buildTarget;
        buildPlayerOptions.locationPathName = appFile + ".apk";

        BuildOptions buildOptions = BuildOptions.None;
        if (Development == "true")
        {
            buildOptions = buildOptions | BuildOptions.Development;
        }
        if (StrictMode == "true")
        {
            buildOptions = buildOptions | BuildOptions.StrictMode;
        }
        buildPlayerOptions.options = buildOptions;

        Debug.Log("[UnityBuildProcess] BuildPipeline.BuildPlayer..." + buildTarget.ToString());

        var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
        var summary = report.summary;
        if (summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
        {
            Debug.Log("[UnityBuildProcess] BuildAppCMD Succeeded: " + summary.totalSize + " bytes");
            // if(autoUpload)
            // {
            //     //ExecuteBat("gdlc_upload_package", BuildTargetUtility.BuildTarget2Platform(buildTarget), $"{Application.dataPath}/../{buildPlayerOptions.locationPathName}");
            //     ExecuteRsync("rsync_package", $"{Application.dataPath}/../{buildPlayerOptions.locationPathName}", BuildTargetUtility.BuildTarget2Platform(buildTarget));
            // }
            BuildDLLCommand.CopyAotDllsPackageBackup(buildTarget);
        }
        else
        {
            Debug.LogErrorFormat("[UnityBuildError] BuildAppCMD Failed {0} Errors!", summary.totalErrors);
        }
    }


    /// <summary>
    /// 设置资源加载模式
    /// </summary>
    private static void SetAssetPlayMode()
    {
        string playMode = GetParameter("PlayMode".ToUpper());
        Debug.Log("PlayMode : " + playMode);
        EPlayMode ePlayMode = (EPlayMode)Enum.Parse(typeof(EPlayMode), playMode);
        BuildVersion.SetStartSceneParam(ePlayMode);
    }

    




    /// <summary>
    /// 控制UI显示
    /// </summary>
    /// <param name="channel"></param>
    /// <returns></returns>
    private static Dictionary<string, bool> GetChannelUI(string channel)
    {
        if (channel == "ios")
        {
            return new Dictionary<string, bool>()
            {
                {"permission", false},
                {"permissionex", true},
                {"loading", true},
                {"duihuan", true},
                {"kefu", true},
                {"libao", true},
                {"logout", true},
            };
        }
        else if (channel == "uc")
        {
            return new Dictionary<string, bool>()
            {
                {"permission", true},
                {"permissionex", false},
                {"loading", true},
                {"duihuan", true},
                {"kefu", true},
                {"libao", true},
                {"logout", false},
            };
        }
        else if (channel == "oppo" || channel == "vivo" || channel == "xiaomi")
        {
            return new Dictionary<string, bool>()
            {
                {"permission", true},
                {"permissionex", true},
                {"loading", true},
                {"duihuan", true},
                {"kefu", true},
                {"libao", true},
                {"logout", false},
            };
        }
        else if (channel == "huawei")
        {
            return new Dictionary<string, bool>()
            {
                {"permission", true},
                {"permissionex", true},
                {"loading", true},
                {"duihuan", true},
                {"kefu", true},
                {"libao", true},
                {"logout", true},
            };
        }
        else
        {
            return new Dictionary<string, bool>()
            {
                {"permission", false},
                {"permissionex", true},
                {"loading", true},
                {"duihuan", true},
                {"kefu", true},
                {"libao", true},
                {"logout", true},
            };
        }
        // if (channel == "ios")
        // {
        //     return new Dictionary<string, bool>()
        //     {
        //         {"permission", false},
        //         {"permissionex", true},
        //         {"loading", false},
        //         {"duihuan", false},
        //         {"kefu", false},
        //         {"libao", false},
        //         {"logout", true},
        //     };
        // }
        // else if (channel == "uc")
        // {
        //     return new Dictionary<string, bool>()
        //     {
        //         {"permission", true},
        //         {"permissionex", false},
        //         {"loading", true},
        //         {"duihuan", true},
        //         {"kefu", false},
        //         {"libao", false},
        //         {"logout", false},
        //     };
        // }
        // else if (channel == "oppo" || channel == "vivo" || channel == "xiaomi")
        // {
        //     return new Dictionary<string, bool>()
        //     {
        //         {"permission", true},
        //         {"permissionex", true},
        //         {"loading", true},
        //         {"duihuan", true},
        //         {"kefu", true},
        //         {"libao", false},
        //         {"logout", false},
        //     };
        // }
        // else if (channel == "huawei")
        // {
        //     return new Dictionary<string, bool>()
        //     {
        //         {"permission", true},
        //         {"permissionex", true},
        //         {"loading", true},
        //         {"duihuan", true},
        //         {"kefu", true},
        //         {"libao", false},
        //         {"logout", true},
        //     };
        // }
        // else
        // {
        //     return new Dictionary<string, bool>()
        //     {
        //         {"permission", false},
        //         {"permissionex", true},
        //         {"loading", true},
        //         {"duihuan", true},
        //         {"kefu", true},
        //         {"libao", true},
        //         {"logout", true},
        //     };
        // }
    }

    /// <summary>
    /// 检查shader资源包
    /// </summary>
    static void CheckShader()
    {
        string steamShaderFile = Application.streamingAssetsPath + @"\Android\Art\shaders.ab";
        DirectoryInfo streamPathInfo = new DirectoryInfo(steamShaderFile);
        if (!Directory.Exists(streamPathInfo.Parent.FullName))
        {
            Directory.CreateDirectory(streamPathInfo.Parent.FullName);
        }

        DirectoryInfo pathInfo = new DirectoryInfo(Application.dataPath);
        string cacheShaderFile = pathInfo.Parent.FullName + @"\Build\shaders.ab";

        if (File.Exists(steamShaderFile))
        {
            File.Copy(steamShaderFile, cacheShaderFile, true);
        }
        else if (File.Exists(cacheShaderFile))
        {
            File.Copy(cacheShaderFile, steamShaderFile, true);
        }

        AssetDatabase.Refresh();

        System.Threading.Thread.Sleep(1000);
    }



    private static Dictionary<string, string> EnvironmentsDic = new Dictionary<string, string>();
    /// <summary>
    ///解释jenkins 传输的参数
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    static string GetParameter(string name)
    {
        if (EnvironmentsDic.ContainsKey(name))
        {
            return EnvironmentsDic[name];
        }
        foreach (string arg in Environment.GetCommandLineArgs())
        {
            if (arg.StartsWith(name + "="))
            {
                string val = arg.Split("="[0])[1];
                if (!EnvironmentsDic.ContainsKey(name))
                {
                    EnvironmentsDic.Add(name, val);
                }
                return val;
            }
        }
        return null;
    }

    public static void ChangeConfigParam(string key, string value, ParamType type)
    {
        try
        {
            //string path = EditorBuildPackage.GetJsonPath(EditorBuildPackage.BuildMode.Release);
            //TextAsset textContent = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
            //JsonData jsonData = JsonMapper.ToObject(textContent.text);

            //Debug.Log("读取配置 " + jsonData[key].ToString());

            //if (type == ParamType.String)
            //{
            //    jsonData[key] = value;
            //}
            //else if (type == ParamType.Bool)
            //{
            //    if (value == "true")
            //    {
            //        jsonData[key] = true;
            //    }
            //    else
            //    {
            //        jsonData[key] = false;
            //    }
            //}
            //else if (type == ParamType.Int)
            //{
            //    jsonData[key] = Convert.ToInt32(value);
            //}
            //else if (type == ParamType.Enum)
            //{
            //    if (key == "Channel")
            //    {
            //        BDFramework.ChannelEnum enumValue = (BDFramework.ChannelEnum)Enum.Parse(typeof(BDFramework.ChannelEnum), value);
            //        jsonData[key] = (int)enumValue;
            //    }
            //}

            //string json = JsonMapper.ToJson(jsonData);
            //Debug.Log(json);

            //FileHelper.WriteAllText(path, json);


            //Debug.Log("写入配置 " + key + ":" + value + ", jsonData[key]:" + jsonData[key]);

            AssetDatabase.Refresh();
        }
        catch (Exception e)
        {
            Debug.LogError(e.Message);
        }
    }

    /// <summary>
    /// 添加日志面板
    /// </summary>
    /// <param name="reporter"></param>
    static public void AddLogReporter(string reporter)
    {
        //var scene = EditorSceneManager.OpenScene(EditorBuildPackage.SCENEPATH);
        //GameObject reporterObject = GameObject.Find("Reporter");
        //if (reporterObject != null)
        //{
        //    UnityEngine.Object.DestroyImmediate(reporterObject);
        //}
        //if (reporter == "true")
        //{
        //    ReporterEditor.CreateReporter();
        //}
        //EditorSceneManager.SaveScene(scene);

        AssetDatabase.Refresh();
    }

    /// <summary>
    /// 添加PocoSDK 自动化测试
    /// </summary>
    /// <param name="reporter"></param>
    static public void AddPocoSDK(string pocoSDK)
    {
        //var scene = EditorSceneManager.OpenScene(EditorBuildPackage.SCENEPATH);
        //GameObject pocoSDKObject = GameObject.Find("PocoSDK");
        //if (pocoSDKObject != null)
        //{
        //    UnityEngine.Object.DestroyImmediate(pocoSDKObject);
        //}
        //if (pocoSDK == "true")
        //{
        //    GameObject pocoObj = new GameObject();
        //    pocoObj.name = "PocoSDK";
        //    PocoManager pocoManager = pocoObj.AddComponent<PocoManager>();
        //}
        //EditorSceneManager.SaveScene(scene);

        AssetDatabase.Refresh();
    }

    /// <summary>
    /// 修改脚本编译类型
    /// </summary>
    /// <param name="type"></param>
    public static void ChangeScriptingBackend(string type, BuildTargetGroup buildTargetGroup)
    {
        if (type == "Mono2x")
        {
            PlayerSettings.SetScriptingBackend(buildTargetGroup, ScriptingImplementation.Mono2x);
        }
        else if (type == "IL2CPP")
        {
            PlayerSettings.SetScriptingBackend(buildTargetGroup, ScriptingImplementation.IL2CPP);
        }
        else
        {
            PlayerSettings.SetScriptingBackend(buildTargetGroup, ScriptingImplementation.WinRTDotNET);
        }
    }

    /// <summary>
    /// 设置sdk环境，沙盒还是release
    /// </summary>
    /// <param name="env"></param>
    static public void SetSDKEnv(string env)
    {
        //var scene = EditorSceneManager.OpenScene(EditorBuildPackage.SCENEPATH);
        //GameObject go = GameObject.Find("BoomSdkConfig");
        //if (go != null)
        //{
        //    Code.BoomSdk.BoomSdkConfig config = go.GetComponent<Code.BoomSdk.BoomSdkConfig>();
        //    if (config != null)
        //    {
        //        if (env == "RELEASE")
        //        {
        //            config._env = Code.BoomSdk.BoomSdkConfig.TestEnv.RELEASE;
        //        }
        //        else if (env == "TEST")
        //        {
        //            config._env = Code.BoomSdk.BoomSdkConfig.TestEnv.TEST;
        //        }
        //        else
        //        {
        //            config._env = Code.BoomSdk.BoomSdkConfig.TestEnv.SANDBOX;
        //        }
        //    }
        //}
        //EditorSceneManager.SaveScene(scene);
    }

    static public void SetLogEnable(string logEnable)
    {
        //var scene = EditorSceneManager.OpenScene(EditorBuildPackage.SCENEPATH);
        //GameObject bdFrameGo = GameObject.Find("BDFrame");
        //if (bdFrameGo != null)
        //{
        //    BDFramework.BDLauncher bDLauncher = bdFrameGo.GetComponent<BDFramework.BDLauncher>();
        //    if (bDLauncher != null)
        //    {
        //        bDLauncher.LogEnable = logEnable == "true";
        //    }

        //    BDebug bdbug = bdFrameGo.GetComponent<BDebug>();
        //    if (bdbug != null)
        //    {
        //        bdbug.IsLog = logEnable == "true";
        //    }
        //}

        //GameObject jGPushSDKGO = GameObject.Find("SDK/JGPushSDK");
        //if (jGPushSDKGO != null)
        //{
        //    JGPushSDK jGPushSDK = jGPushSDKGO.GetComponent<JGPushSDK>();
        //    if (jGPushSDK != null)
        //    {
        //        jGPushSDK.isDebug = logEnable == "true";
        //    }
        //}

        //GameObject thinkingAnalyticsGO = GameObject.Find("SDK/ThinkingAnalytics");
        //if (thinkingAnalyticsGO != null)
        //{
        //    ThinkingAnalyticsAPI thinking = thinkingAnalyticsGO.GetComponent<ThinkingAnalyticsAPI>();
        //    if (thinking != null)
        //    {
        //        thinking.enableLog = logEnable == "true";
        //    }
        //}

        //GameObject oceanEngineSDKGo = GameObject.Find("SDK/OceanEngineSDK");
        //if (oceanEngineSDKGo != null)
        //{
        //    OceanEngineSDK oceanEngineSDK = oceanEngineSDKGo.GetComponent<OceanEngineSDK>();
        //    if (oceanEngineSDK != null)
        //    {
        //        oceanEngineSDK.isDebug = logEnable == "true";
        //    }
        //}

        //EditorSceneManager.SaveScene(scene);

        AssetDatabase.Refresh();
    }


    /// <summary>
    /// 运行cmd命令行
    /// </summary>
    /// <param name="cmd"></param>
    /// <returns></returns>
    public static Dictionary<string, string> RunCmd(string cmd)
    {
        Dictionary<string, string> res = new Dictionary<string, string>();

        try
        {
            using (var process = new System.Diagnostics.Process())
            {
                process.StartInfo.FileName = "cmd.exe";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardInput = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                //process.StartInfo.WorkingDirectory = @"C:\"; //不设置就是当前目录
                //process.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Normal;
                process.StartInfo.CreateNoWindow = true; //不显示命令行的黑窗口
                process.StartInfo.StandardErrorEncoding = System.Text.Encoding.GetEncoding("gb2312");
                process.StartInfo.StandardOutputEncoding = System.Text.Encoding.GetEncoding("gb2312");
                process.Start(); //启动程序

                //process.StandardInput.AutoFlush = true; //下面手动Flush了, 所以这边可以注释掉
                process.StandardInput.WriteLine(cmd); //执行命令
                process.StandardInput.WriteLine("exit"); //执行完后自动退出cmd
                process.StandardInput.Flush(); //或 process.StandardInput.Close(); 

                process.WaitForExit(); //等待程序执行完退出进程
                if (process.HasExited)
                {
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    if (!string.IsNullOrEmpty(output))
                    {
                        res.Add("msg", output);
                    }
                    if (!string.IsNullOrEmpty(error))
                    {
                        res.Add("err", error);
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError(e.Message);
        }

        return res;
    }
}
