using System;
using UnityEditor;
using UnityEngine;
using UnityEditor.UIElements;
using UnityEngine.UIElements;
using YooAsset.Editor;
using Sirenix.Utilities;
using YooAsset;

namespace MyFramework
{
    //https://blog.csdn.net/qq_18192161/article/details/107867320 �ര������

    public class BuildWindow : EditorWindow
    {
        public const int FontSize = 20;

        [MenuItem("Project/Build Window", false, 103)]
        public static void OpenWindow()
        {
            Type menuType = typeof(BuildWindow);
            //�������������
            var containerInstance = EditorContainerWindow.CreateInstance();

            //������������
            var splitViewInstance = EditorSplitView.CreateInstance();
            EditorSplitView.SetVertical(splitViewInstance, true);

            //���ø�����
            EditorContainerWindow.SetRootView(containerInstance, splitViewInstance);
            //���menu�����͹�������
            object menuDockAreaInstance = EditorDockArea.CreateInstance();
            EditorDockArea.SetPosition(menuDockAreaInstance, new Rect(0, 0, 800, 400));
            EditorWindow menuWindow = (EditorWindow)ScriptableObject.CreateInstance(menuType);
            //menuWindow.position = new Rect(0,0,150,800);
            EditorDockArea.AddTab(menuDockAreaInstance, menuWindow);
            EditorSplitView.AddChild(splitViewInstance, menuDockAreaInstance);

            EditorEditorWindow.MakeParentsSettingsMatchMe(menuWindow);

            EditorContainerWindow.SetPosition(containerInstance, new Rect(100, 100, 800, 900));
            EditorSplitView.SetPosition(splitViewInstance, new Rect(0, 0, 800, 900));
            EditorContainerWindow.Show(containerInstance, 0, true, false, true);
            EditorContainerWindow.OnResize(containerInstance);

            var buildWindow = menuWindow as BuildWindow;
            buildWindow.containerInstance = containerInstance;
            buildWindow.splitViewInstance = splitViewInstance;
            buildWindow.ChangeShowYooassetWindow(!BuildSetting.GetSkipAssetBundle());
        }

        private Toolbar _toolbar;
        private ToolbarMenu _targetMenu;
        private BuildTarget _buildTarget;
        private ToolbarMenu _playModeMenu;
        private YooAsset.EPlayMode _playMode;
        private VisualElement _hybridRoot;
        private Toggle _hybridclrEnable;
        private Toggle _skipBuildHybridCLR;
        private bool isSkipBuildHybridCLR;
        private Button _openHybridCLRSet;
        private Button _patchBtn;
        private Toggle _hookEnable;
        private TextField _serverIPField; // ������Ա����
        private Toggle _relaseEnable;
        private bool isRelease;
        private Toggle _developmentEnable;
        private bool isDevelopment;
        private Toggle _skipAssetBundle;
        private bool isSkipAssetBundle;
        private Toggle _miniPackage;
        private bool isminiPackage;
        private VisualElement _buildRoot;
        private VisualElement _androidRoot;
        private EditorWindow yooassetWindow;
        

        
        protected object containerInstance;
        protected object splitViewInstance;

        private Toggle CreateToggleData(string name, VisualElement parent, bool visible = true)
        {
            var toggle = new Toggle(name);
            toggle.style.width = 150;
            toggle.style.unityTextAlign = TextAnchor.MiddleLeft;
            toggle.visible = visible;
            parent.Add(toggle);
            return toggle;
        }

        public void CreateGUI()
        {
            try
            {
                VisualElement root = this.rootVisualElement;

                _toolbar = new Toolbar();
                
                _targetMenu = new ToolbarMenu();
                _targetMenu.style.width = 200;
                _targetMenu.menu.AppendAction(BuildTarget.Android.ToString(), BuildTargetMenuAction, BuildTargetMenuFun, BuildTarget.Android);
                _targetMenu.menu.AppendAction(BuildTarget.StandaloneWindows64.ToString(), BuildTargetMenuAction, BuildTargetMenuFun, BuildTarget.StandaloneWindows64);
                _targetMenu.menu.AppendAction(BuildTarget.iOS.ToString(), BuildTargetMenuAction, BuildTargetMenuFun, BuildTarget.iOS);
                _buildTarget = BuildSetting.GetBuildTarget();
                _targetMenu.text = _buildTarget.ToString();
                _toolbar.Add(_targetMenu);

                _playModeMenu = new ToolbarMenu();
                _playModeMenu.style.width = 200;
                _playModeMenu.menu.AppendAction(YooAsset.EPlayMode.OfflinePlayMode.ToString(), PlayModeMenuAction, PlayModeMenuFun, YooAsset.EPlayMode.OfflinePlayMode);
                _playModeMenu.menu.AppendAction(YooAsset.EPlayMode.HostPlayMode.ToString(), PlayModeMenuAction, PlayModeMenuFun, YooAsset.EPlayMode.HostPlayMode);
                _playMode = BuildSetting.GetEPlayMode();
                _playModeMenu.text = _playMode.ToString();
                _toolbar.Add(_playModeMenu);

                root.Add(_toolbar);

                _hybridRoot = new VisualElement();
                _hybridclrEnable = CreateToggleData("����HybridCLR", _hybridRoot);
                _hybridclrEnable.style.width = 150;
                _hybridclrEnable.SetValueWithoutNotify(HybridCLR.Editor.SettingsUtil.Enable);

                _skipBuildHybridCLR = CreateToggleData("����BuildHotDll", _hybridRoot);
                _skipBuildHybridCLR.style.marginLeft = 20;
                isSkipBuildHybridCLR = _skipBuildHybridCLR.value;

                _openHybridCLRSet = new Button();
                _openHybridCLRSet.text = "HybridCLR Setting";
                _openHybridCLRSet.style.width = 300;
                _openHybridCLRSet.style.marginLeft = 20;
                _openHybridCLRSet.clicked += () =>
                {
                    EditorApplication.ExecuteMenuItem("HybridCLR/Settings...");
                };
                _hybridRoot.Add(_openHybridCLRSet);

                _hybridRoot.style.flexDirection = FlexDirection.Row;

                ChangeHybridCLR(HybridCLR.Editor.SettingsUtil.Enable);
                
                root.Add(_hybridRoot);

                _hookEnable = CreateToggleData("�������ע��", root);
                _hookEnable.SetValueWithoutNotify(Monitor.GOT.Editor.Settings.MonitorSettings.instance.enable);

                // �޸�Ϊʹ�ó�Ա����
                _serverIPField = new TextField("�ϴ�·��(serverIP)");
                _serverIPField.value = BuildVersion.serverIP;
                _serverIPField.style.width = 400;
                _serverIPField.RegisterValueChangedCallback(evt => {
                    BuildVersion.serverIP = evt.newValue;
                    AssetDatabase.SaveAssets();
                });
                root.Add(_serverIPField);

                _patchBtn = new Button();
                _patchBtn.text = "�ȸ��°�";
                _patchBtn.style.height = 50;
                _patchBtn.style.fontSize = 30;
                _patchBtn.style.marginTop = 50;
                _patchBtn.style.backgroundColor = new StyleColor(new Color(1f, 0.4808381f, 0, 1f));
                _patchBtn.clicked += () =>
                {
                    BuildVersion.BuildAssetBundleCMD_SBP(_buildTarget, true, EBuildinFileCopyOption.None);
                    // BuildVersion.BuildVersionCMD_PatchDll(_buildTarget, _skipBuildHybridCLR.value);
                };

                root.Add(_patchBtn);
                
                _buildRoot = new VisualElement();
                _buildRoot.style.flexDirection = FlexDirection.Row;
                _buildRoot.style.paddingTop = 50;

                _relaseEnable = CreateToggleData("relase�汾", _buildRoot);
                _relaseEnable.SetValueWithoutNotify(BuildSetting.GetIsRelease());
                isRelease = _relaseEnable.value;

                _developmentEnable = CreateToggleData("Development�汾", _buildRoot);
                _developmentEnable.style.marginLeft = 30;
                _developmentEnable.SetValueWithoutNotify(BuildSetting.GetIsDevelopment());
                isDevelopment = _developmentEnable.value;

                _skipAssetBundle = CreateToggleData("������Դ����", _buildRoot);
                _skipAssetBundle.style.marginLeft = 30;
                _skipAssetBundle.SetValueWithoutNotify(BuildSetting.GetSkipAssetBundle());
                isSkipAssetBundle = _skipAssetBundle.value;

                _miniPackage = CreateToggleData("��С��", _buildRoot);
                _miniPackage.style.marginLeft = 30;
                _miniPackage.SetValueWithoutNotify(BuildSetting.GetMiniPackage());
                isminiPackage = _miniPackage.value;

                root.Add(_buildRoot);

                _androidRoot = new VisualElement();
                root.Add(_androidRoot);
                
                var keystorePass = new TextField("keystorePass");
                keystorePass.value = BuildSetting.GetAndroidKeystorePass();
                keystorePass.style.width = 400;
                _androidRoot.Add(keystorePass);
                
                var keyaliasName = new TextField("keyaliasName");
                keyaliasName.value = BuildSetting.GetAndroidKeyaliasName();
                keyaliasName.style.width = 400;
                _androidRoot.Add(keyaliasName);
                
                var keyaliasPass = new TextField("keyaliasPass");
                keyaliasPass.value = BuildSetting.GetAndroidKeyaliasPass();
                keyaliasPass.style.width = 400;
                _androidRoot.Add(keyaliasPass);
                
                _androidRoot.visible = _buildTarget == BuildTarget.Android;
                _androidRoot.style.display = _androidRoot.visible ? DisplayStyle.Flex : DisplayStyle.None;


                var btnBuild = new Button();
                btnBuild.text = "����";
                btnBuild.style.height = 50;
                btnBuild.style.fontSize = 30;
                btnBuild.style.marginTop = 10;
                btnBuild.style.backgroundColor = new StyleColor(new Color(40f / 250f, 106f/250f, 42f/250f, 1f));
                btnBuild.clicked += ()=>{
                    PlayerSettings.Android.keystorePass = keystorePass.value;
                    PlayerSettings.Android.keyaliasName = keyaliasName.value;
                    PlayerSettings.Android.keyaliasPass = keyaliasPass.value;
                    BuildSetting.SetAndroidKeystorePass(keystorePass.value);
                    BuildSetting.SetAndroidKeyaliasName(keyaliasName.value);
                    BuildSetting.SetAndroidKeyaliasPass(keyaliasPass.value);
                    
                    if(SettingsUtils.HybridCLRCustomGlobalSettings.Enable != HybridCLR.Editor.SettingsUtil.Enable)
                    {
                        SettingsUtils.HybridCLRCustomGlobalSettings.Enable = HybridCLR.Editor.SettingsUtil.Enable;
                        ProjectGameSettingsProvider.SaveAssetData(SettingsUtils.FrameworkGlobalSettings, SettingsUtils.HybridCLRCustomGlobalSettings);
                    }

                    BuildVersion.BuildVersionCMD_Complex(_buildTarget, _playMode, isRelease, isDevelopment, isSkipAssetBundle, _skipBuildHybridCLR.value, isminiPackage);
                };

                root.Add(btnBuild);

            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }
        }

        private void BuildTargetMenuAction(DropdownMenuAction action)
        {
            var buildTarget = (BuildTarget)action.userData;
            if (buildTarget != _buildTarget)
            {
                _androidRoot.visible = buildTarget == BuildTarget.Android;
                _androidRoot.style.display = _androidRoot.visible ? DisplayStyle.Flex : DisplayStyle.None;
                _buildTarget = buildTarget;
                _targetMenu.text = _buildTarget.ToString();
                BuildSetting.SetBuildTarget(buildTarget);
            }
        }
        private DropdownMenuAction.Status BuildTargetMenuFun(DropdownMenuAction action)
        {
            var buildTarget = (BuildTarget)action.userData;
            if (buildTarget == _buildTarget)
                return DropdownMenuAction.Status.Checked;
            else
                return DropdownMenuAction.Status.Normal;
        }

        private void PlayModeMenuAction(DropdownMenuAction action)
        {
            var playMode = (YooAsset.EPlayMode)action.userData;
            if (playMode != _playMode)
            {
                _playMode = playMode;
                _playModeMenu.text = _playMode.ToString();
                BuildSetting.SetEPlayMode(_playMode);
            }
        }
        private DropdownMenuAction.Status PlayModeMenuFun(DropdownMenuAction action)
        {
            var playMode = (YooAsset.EPlayMode)action.userData;
            if (playMode == _playMode)
                return DropdownMenuAction.Status.Checked;
            else
                return DropdownMenuAction.Status.Normal;
        }

        void OnGUI()
        {
            if (HybridCLR.Editor.SettingsUtil.Enable != _hybridclrEnable.value)
            {
                HybridCLR.Editor.SettingsUtil.Enable = _hybridclrEnable.value;
                ChangeHybridCLR(HybridCLR.Editor.SettingsUtil.Enable);
            }
            if(Monitor.GOT.Editor.Settings.MonitorSettings.instance.enable != _hookEnable.value)
            {
                Monitor.GOT.Editor.Settings.MonitorSettings.instance.enable = _hookEnable.value;
                Monitor.GOT.Editor.Settings.MonitorSettings.instance.Save();
            }
            if (isRelease != _relaseEnable.value)
            {
                isRelease = _relaseEnable.value;
                BuildSetting.SetIsRelease(isRelease);
            }
            if (isDevelopment != _developmentEnable.value)
            {
                isDevelopment = _developmentEnable.value;
                BuildSetting.SetIsDevelopment(isDevelopment);
            }
            if (isSkipAssetBundle != _skipAssetBundle.value)
            {
                isSkipAssetBundle = _skipAssetBundle.value;
                BuildSetting.SetSkipAssetBundle(isSkipAssetBundle);
                ChangeShowYooassetWindow(!isSkipAssetBundle);
            }
            if (isminiPackage != _miniPackage.value)
            {
                isminiPackage = _miniPackage.value;
                BuildSetting.SetMiniPackage(isminiPackage);
            }
        }

        private void ChangeShowYooassetWindow(bool show)
        {
            if (show)
            {
                if (yooassetWindow == null && splitViewInstance != null && containerInstance != null)
                {
                    //���yooasset����
                    object yooassetDockAreaInstance = EditorDockArea.CreateInstance();
                    EditorDockArea.SetPosition(yooassetDockAreaInstance, new Rect(0, 0, 800, 500));
                    yooassetWindow = (EditorWindow)ScriptableObject.CreateInstance(typeof(AssetBundleBuilderWindow));
                    EditorDockArea.AddTab(yooassetDockAreaInstance, yooassetWindow);
                    EditorSplitView.AddChild(splitViewInstance, yooassetDockAreaInstance);

                    EditorEditorWindow.MakeParentsSettingsMatchMe(yooassetWindow);

                    EditorContainerWindow.Show(containerInstance, 0, true, false, true);
                    EditorContainerWindow.OnResize(containerInstance);
                }
            }
            else if (yooassetWindow != null)
            {
                yooassetWindow.Close();
                yooassetWindow = null;
                if (containerInstance != null)
                {
                    EditorContainerWindow.Show(containerInstance, 0, true, false, true);
                    EditorContainerWindow.OnResize(containerInstance);
                }
            }
        }

        private void ChangeHybridCLR(bool show)
        {
            _skipBuildHybridCLR.visible = show;
            _openHybridCLRSet.visible = show;
        }
    }
}
