using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using YooAsset.Editor;
using UnityEditor.SceneManagement;
using System.IO;
using System.Text;

namespace MyFramework
{
    public class BuildVersion
    {
        /// <summary>
        /// 1,build dll 2,build assetbundle 3,build apk or xcode
        /// </summary>
        /// <param name="buildTarget"></param>
        [MenuItem("Project/Build/Version/Release/APK")]
        static void BuildVersionCMDAPK_Release()
        {
            BuildVersionCMD(BuildTarget.Android);
        }

        [MenuItem("Project/Build/Version/Release/PC")]
        static void BuildVersionCMDPC_Release()
        {
            BuildVersionCMD(BuildTarget.StandaloneWindows64);
        }

        [MenuItem("Project/Build/Version/Release/iOS")]
        static void BuildVersionCMDiOS_Release()
        {
            BuildVersionCMD(BuildTarget.iOS);
        }

        [MenuItem("Project/Build/Version/Debug/APK")]
        static void BuildVersionCMDAPKDev()
        {
            BuildVersionCMD(BuildTarget.Android, true, false);
        }

        [MenuItem("Project/Build/Version/Debug/PC")]
        static void BuildVersionCMDPC()
        {
            BuildVersionCMD(BuildTarget.StandaloneWindows64, true, false);
        }

        [MenuItem("Project/Build/Version/Debug/iOS")]
        static void BuildVersionCMDiOS()
        {
            BuildVersionCMD(BuildTarget.iOS, true, false);
        }

        static void BuildVersionCMD(BuildTarget buildTarget, bool development = false, bool bRelease=true)
        {
            BuildReport rp = new BuildReport();
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            //Modify StartScene Params
            SetStartSceneParam();
            stopwatch.Start();
            //Build HydridCLR
            BuildDLLCommand.GenerateAllAndCopyDlls(buildTarget);
            rp.HybirdCLRTime = (int)stopwatch.ElapsedMilliseconds;

            stopwatch.Restart();
            //Build AssetBundle
            BuildAssetBundleCMD(buildTarget);
            rp.AssetBundleTime = (int)stopwatch.ElapsedMilliseconds;

            stopwatch.Restart();
            //Build App
            BuildAppCMD(buildTarget, development, bRelease);
            rp.AppBuildTime = (int)stopwatch.ElapsedMilliseconds;

            string rpJson = JsonUtility.ToJson(rp);
            File.WriteAllText(Application.dataPath + "/BuildReport.json", rpJson);
        }

        static bool BuildAssetBundleCMD(BuildTarget buildTarget, bool autoUpload = false)
        {
            string PackageName = GetDefaultBuildPackageName();
            var BuildPipeline = EBuildPipeline.BuiltinBuildPipeline;
            var buildMode = AssetBundleBuilderSetting.GetPackageBuildMode(PackageName, BuildPipeline);
            var fileNameStyle = AssetBundleBuilderSetting.GetPackageFileNameStyle(PackageName, BuildPipeline);
            var buildinFileCopyOption = AssetBundleBuilderSetting.GetPackageBuildinFileCopyOption(PackageName, BuildPipeline);
            var buildinFileCopyParams = AssetBundleBuilderSetting.GetPackageBuildinFileCopyParams(PackageName, BuildPipeline);
            //var compressOption = AssetBundleBuilderSetting.GetPackageCompressOption(PackageName, BuildPipeline);

            BuiltinBuildParameters buildParameters = new BuiltinBuildParameters();
            buildParameters.BuildOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
            buildParameters.BuildinFileRoot = AssetBundleBuilderHelper.GetStreamingAssetsRoot();
            buildParameters.BuildPipeline = BuildPipeline.ToString();
            buildParameters.BuildTarget = buildTarget;
            buildParameters.BuildMode = buildMode;
            buildParameters.PackageName = PackageName;
            buildParameters.PackageVersion = GetDefaultPackageVersion();
            buildParameters.EnableSharePackRule = true;
            buildParameters.VerifyBuildingResult = true;
            buildParameters.FileNameStyle = fileNameStyle;
            buildParameters.BuildinFileCopyOption = buildinFileCopyOption;
            buildParameters.BuildinFileCopyParams = buildinFileCopyParams;
            buildParameters.EncryptionServices = null;
            buildParameters.CompressOption = ECompressOption.LZ4;

            BuiltinBuildPipeline pipeline = new BuiltinBuildPipeline();
            var buildResult = pipeline.Run(buildParameters, true);
            if (buildResult.Success)
                Debug.Log("Build Version Success!");
                if(autoUpload)
                {
                //ExecuteBat("gdlc_upload_pacth", BuildTargetUtility.BuildTarget2Platform(buildTarget), buildParameters.GetPackageOutputDirectory());
                ExecuteRsync("rsync_package", buildParameters.GetPackageOutputDirectory(), BuildTargetUtility.BuildTarget2Platform(buildTarget));
            }
            else
            {
                Debug.LogError("Build Version Fail! "+buildResult.ErrorInfo);
            }
            return buildResult.Success;
        }

        public static bool BuildAssetBundleCMD_SBP(BuildTarget buildTarget, bool autoUpload = false, EBuildinFileCopyOption copyOption = EBuildinFileCopyOption.ClearAndCopyAll)
        {

            // Debug.Log("[UnityBuildProcess] ��ʼ����Դ��...");
            string packageName = GetDefaultBuildPackageName();
            AssetBundleBuilderSetting.SetPackageBuildinFileCopyOption(packageName, EBuildPipeline.ScriptableBuildPipeline, copyOption);

            var buildPipeline = EBuildPipeline.ScriptableBuildPipeline;
            var buildMode = AssetBundleBuilderSetting.GetPackageBuildMode(packageName, buildPipeline);
            var fileNameStyle = AssetBundleBuilderSetting.GetPackageFileNameStyle(packageName, buildPipeline);
            var buildinFileCopyOption = AssetBundleBuilderSetting.GetPackageBuildinFileCopyOption(packageName, buildPipeline);
            var buildinFileCopyParams = AssetBundleBuilderSetting.GetPackageBuildinFileCopyParams(packageName, buildPipeline);
            var compressOption = AssetBundleBuilderSetting.GetPackageCompressOption(packageName, buildPipeline);

            ScriptableBuildParameters buildParameters = new ScriptableBuildParameters();
            buildParameters.BuildOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
            buildParameters.BuildinFileRoot = AssetBundleBuilderHelper.GetStreamingAssetsRoot();
            buildParameters.BuildPipeline = buildPipeline.ToString();
            buildParameters.BuildTarget = buildTarget;
            buildParameters.BuildMode = buildMode;
            buildParameters.PackageName = packageName;
            buildParameters.PackageVersion = GetDefaultPackageVersion();
            buildParameters.EnableSharePackRule = true;
            buildParameters.VerifyBuildingResult = true;
            buildParameters.FileNameStyle = fileNameStyle;
            buildParameters.BuildinFileCopyOption = buildinFileCopyOption;
            buildParameters.BuildinFileCopyParams = buildinFileCopyParams;
            buildParameters.EncryptionServices = null;
            buildParameters.CompressOption = compressOption;

            ScriptableBuildPipeline pipeline = new ScriptableBuildPipeline();
            var buildResult = pipeline.Run(buildParameters, true);
            if (buildResult.Success)
            {
                if(autoUpload)
                {
                    //ExecuteBat("gdlc_upload_pacth", BuildTargetUtility.BuildTarget2Platform(buildTarget), buildParameters.GetPackageOutputDirectory());
                    ExecuteRsync("rsync_package", buildParameters.GetPackageOutputDirectory(), BuildTargetUtility.BuildTarget2Platform(buildTarget));
                }
            }
            return buildResult.Success; 
        }

        static void BuildAppCMD(BuildTarget buildTarget, bool development, bool bRelease, bool autoUpload = false)
        {
            // ��ȡ���г���
            EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;

            // ���������б�
            string[] scenePaths = new string[scenes.Length];
            for (int i = 0; i < scenes.Length; i++)
            {
                scenePaths[i] = scenes[i].path;
            }
            BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();
            buildPlayerOptions.scenes = scenePaths;
            if(buildTarget == BuildTarget.Android)
                buildPlayerOptions.locationPathName = string.Format("../Publish/{0}_{1}_{2}.apk",Application.productName, buildTarget.ToString(), GetBuildTime());
            else if(buildTarget == BuildTarget.StandaloneWindows64)
                buildPlayerOptions.locationPathName = string.Format("../Publish/{2}/{0}_{1}.exe", Application.productName, buildTarget.ToString(), GetBuildTime());
            else if(buildTarget == BuildTarget.iOS)
                buildPlayerOptions.locationPathName = string.Format("../Publish/{0}_{1}_{2}", Application.productName, buildTarget.ToString(), GetBuildTime());
            buildPlayerOptions.target = buildTarget;

            buildPlayerOptions.options = development ? (BuildOptions.Development | BuildOptions.ConnectWithProfiler) : BuildOptions.None;

            SetReleaseDefineSymbols(buildTarget, bRelease);

            var report = BuildPipeline.BuildPlayer(buildPlayerOptions);
            var summary = report.summary;

            if (summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log("Build succeeded: " + summary.totalSize + " bytes");
                if(autoUpload)
                {
                    //ExecuteBat("gdlc_upload_package", BuildTargetUtility.BuildTarget2Platform(buildTarget), $"{Application.dataPath}/../{buildPlayerOptions.locationPathName}");
                    ExecuteRsync("rsync_package", $"{Application.dataPath}/../{buildPlayerOptions.locationPathName}", BuildTargetUtility.BuildTarget2Platform(buildTarget));
                }
                BuildDLLCommand.CopyAotDllsPackageBackup(buildTarget);
            }

            if (summary.result == UnityEditor.Build.Reporting.BuildResult.Failed)
            {
                Debug.LogErrorFormat("Build failed {0} Errors!", summary.totalErrors);
            }
        }

        //[MenuItem("Project/Build/Version/TestSaveScene")]
        public static void SetStartSceneParam(YooAsset.EPlayMode playMode = YooAsset.EPlayMode.OfflinePlayMode)
        {
            EditorSceneManager.OpenScene("Assets/Scenes/StartScene.unity");
            GameObject obj = GameObject.Find("GameManager");
            var gameEntry = obj.GetComponent<GameEntry>();
            if(gameEntry != null)
            {
                gameEntry.PlayMode = playMode;
                if(playMode == YooAsset.EPlayMode.HostPlayMode)
                {
                    gameEntry.SkipLogo = true;
                    gameEntry.SkipUpdatePatch = false;
                    gameEntry.SingleMode = false;
                }
                else
                {
                    gameEntry.SkipLogo = true;
                    gameEntry.SkipUpdatePatch = true;
                    gameEntry.SingleMode = true;
                }
                EditorUtility.SetDirty(obj);
            }
            EditorSceneManager.SaveOpenScenes();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        static string GetDefaultBuildPackageName()
        {
            List<string> packages = GetBuildPackageNames();
            if (packages.Count == 0)
            {
                Debug.LogError("Not found any package");
                return "";
            }
            return packages[0];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        static private List<string> GetBuildPackageNames()
        {
            List<string> result = new List<string>();
            foreach (var package in AssetBundleCollectorSettingData.Setting.Packages)
            {
                result.Add(package.PackageName);
            }
            return result;
        }

        static private string GetDefaultPackageVersion()
        {
            int totalMinutes = DateTime.Now.Hour * 60 + DateTime.Now.Minute;
            return DateTime.Now.ToString("yyyy-MM-dd") + "-" + totalMinutes;
        }

        static private string GetBuildTime()
        {
            return DateTime.Now.ToString("MM_dd_HH_mm");
        }

        [MenuItem("Project/Build/Version/Debug/DefineSymbols")]
        static void TestDebugDefineSymbols()
        {
            SetReleaseDefineSymbols(BuildTarget.Android, false);
        }

        static string sRelease = "RELEASE";
        static string sDebug= "DEBUG";

        public static void SetReleaseDefineSymbols(BuildTarget buildTarget, bool bRelease)
        {
            // ��ȡ��ǰ�Ľű��������
            BuildTargetGroup buildTargetGroup = BuildTargetUtility.GetBuildTargetGroup(buildTarget);
            string currentSymbols = PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup);
            Debug.Log("Current Scripting Define Symbols: " + currentSymbols);
            string newSymbols = currentSymbols;
            // ����µĶ������
            if (bRelease)
            {
                if (newSymbols.Contains(sDebug))
                {
                    newSymbols = newSymbols.Replace(sDebug, sRelease);
                }
                else if (!newSymbols.Contains(sRelease))
                {
                    newSymbols = currentSymbols + ";" + sRelease;
                }
            }
                
            else
            {
                if (newSymbols.Contains(sRelease))
                {
                    newSymbols = newSymbols.Replace(sRelease, sDebug);
                }
                else if (!newSymbols.Contains(sDebug))
                {
                    newSymbols = currentSymbols + ";" + sDebug;
                }
            }
                

            // �����µĽű��������
            PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, newSymbols);
        }

        public static void BuildVersionCMD_Complex(BuildTarget buildTarget, YooAsset.EPlayMode playMode = YooAsset.EPlayMode.OfflinePlayMode, bool isRelease = true, bool isDevelopment = false, bool skipAssetBundle = false, bool _skipBuildHybridCLR = false, bool isMiniPackage = false)
        {
            BuildReport rp = new BuildReport();
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            //Modify StartScene Params
            SetStartSceneParam(playMode);

            SetReleaseDefineSymbols(buildTarget, isRelease);

            if(HybridCLR.Editor.SettingsUtil.Enable)
            {
                if(BuildDLLCommand.CheckAotReferences())
                {
                    return;
                }
                //todo onlyHotDll
                //Build HydridCLR
                stopwatch.Restart();
                if(_skipBuildHybridCLR)
                {
                    BuildDLLCommand.CopyAOTHotUpdateDlls(buildTarget);
                }
                else
                {
                    BuildDLLCommand.GenerateAllAndCopyDlls(buildTarget);
                }
                rp.HybirdCLRTime = (int)stopwatch.ElapsedMilliseconds;
                stopwatch.Stop();
            }

            if(isMiniPackage)
            {
                EditorTools.ClearFolder(AssetBundleBuilderHelper.GetStreamingAssetsRoot());
                AssetDatabase.Refresh();
            }
            else if(!skipAssetBundle)
            {
                stopwatch.Restart();
                //Build AssetBundle
                // var isSucceed = BuildAssetBundleCMD(buildTarget, true);
                EBuildinFileCopyOption copyOption = EBuildinFileCopyOption.ClearAndCopyAll;
                if (isMiniPackage)
                {
                    copyOption = EBuildinFileCopyOption.None;
                }
                var isSucceed = BuildAssetBundleCMD_SBP(buildTarget, true, copyOption);
                if(!isSucceed)
                {
                    return;
                }
                rp.AssetBundleTime = (int)stopwatch.ElapsedMilliseconds;
                stopwatch.Stop();
            }

            stopwatch.Restart();
            //Build App
            BuildAppCMD(buildTarget, isDevelopment, isRelease, true);
            rp.AppBuildTime = (int)stopwatch.ElapsedMilliseconds;
            stopwatch.Stop();

            string rpJson = JsonUtility.ToJson(rp);
            File.WriteAllText(Application.dataPath + "/BuildReport.json", rpJson);
        }

        public static void BuildVersionCMD_PatchDll(BuildTarget buildTarget, bool _skipBuildHybridCLR = false)
        {
            BuildReport rp = new BuildReport();
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            
            if(HybridCLR.Editor.SettingsUtil.Enable)
            {
                 if(BuildDLLCommand.CheckAotReferences())
                {
                    return;
                }
                //todo onlyHotDll
                //Build HydridCLR
                stopwatch.Restart();
                if(_skipBuildHybridCLR)
                {
                    BuildDLLCommand.CopyAOTHotUpdateDlls(buildTarget);
                }
                else
                {
                    BuildDLLCommand.GenerateAllAndCopyDlls(buildTarget);
                }
                rp.HybirdCLRTime = (int)stopwatch.ElapsedMilliseconds;
                stopwatch.Stop();
            }

            stopwatch.Restart();
            //Patch
            string PackageName = GetDefaultBuildPackageName();
            var BuildPipeline = EBuildPipeline.BuiltinBuildPipeline;
            var buildMode = EBuildMode.IncrementalBuild;
            var fileNameStyle = AssetBundleBuilderSetting.GetPackageFileNameStyle(PackageName, BuildPipeline);
            var buildinFileCopyOption = EBuildinFileCopyOption.None;
            var buildinFileCopyParams = "";
            //var compressOption = AssetBundleBuilderSetting.GetPackageCompressOption(PackageName, BuildPipeline);

            BuiltinBuildParameters buildParameters = new BuiltinBuildParameters();
            buildParameters.BuildOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
            buildParameters.BuildinFileRoot = AssetBundleBuilderHelper.GetStreamingAssetsRoot();
            buildParameters.BuildPipeline = BuildPipeline.ToString();
            buildParameters.BuildTarget = buildTarget;
            buildParameters.BuildMode = buildMode;
            buildParameters.PackageName = PackageName;
            buildParameters.PackageVersion = GetDefaultPackageVersion();
            buildParameters.EnableSharePackRule = true;
            buildParameters.VerifyBuildingResult = true;
            buildParameters.FileNameStyle = fileNameStyle;
            buildParameters.BuildinFileCopyOption = buildinFileCopyOption;
            buildParameters.BuildinFileCopyParams = buildinFileCopyParams;
            buildParameters.EncryptionServices = null;
            buildParameters.CompressOption = ECompressOption.LZ4;

            BuiltinBuildPipeline pipeline = new BuiltinBuildPipeline();
            var buildResult = pipeline.Run(buildParameters, true);
            if (buildResult.Success)
            {
                Debug.Log("Build Version Success!");
                //ExecuteBat("gdlc_upload_pacth", BuildTargetUtility.BuildTarget2Platform(buildTarget), buildParameters.GetPackageOutputDirectory());
                ExecuteRsync("rsync_package", buildParameters.GetPackageOutputDirectory(), BuildTargetUtility.BuildTarget2Platform(buildTarget));
            }
            else
            {
                Debug.LogError("Build Version Fail! "+buildResult.ErrorInfo);
            }
            stopwatch.Stop();

            string rpJson = JsonUtility.ToJson(rp);
            File.WriteAllText(Application.dataPath + "/BuildReport.json", rpJson);
        }

        public static void ExecuteBat(string batName, params string[] batParams)
        {
            System.Diagnostics.Process proc = null;
            try
            {
                StringBuilder output = new StringBuilder();
                proc = new System.Diagnostics.Process();
                proc.StartInfo.FileName = $"{Application.dataPath}/../../Tools/Package/{batName}.bat";
                proc.StartInfo.Arguments = string.Join(" ", batParams);
                proc.StartInfo.CreateNoWindow = false;
                proc.StartInfo.UseShellExecute = false;
                proc.StartInfo.RedirectStandardOutput = true;
                proc.OutputDataReceived += (object sender, System.Diagnostics.DataReceivedEventArgs e)=>{
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        output.Append(e.Data);
                    }
                };
                proc.Start();
                proc.BeginOutputReadLine();
                proc.WaitForExit();
                Debug.Log(output.ToString());
                Debug.LogError($"ExecuteBat Success name:{batName}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ExecuteBat fail name:{batName} err:{ex.Message}");
            }
        }

        public static string serverIP = "g42@************:/mnt/d/ClientFilesRoot/";

        public static void ExecuteRsync(string batName, params string[] batParams)
        {
            System.Diagnostics.Process proc = null;
            try
            {
                StringBuilder output = new StringBuilder();
                proc = new System.Diagnostics.Process();
                proc.StartInfo.FileName = $"{Application.dataPath}/../../Tools/Package/{batName}.bat";
                string wslSrc = WslPathConverter.ConvertWindowsPathToWslPath(batParams[0]);
                string wslDst = batParams[1];
                if(string.IsNullOrEmpty(serverIP))
                {
                    Debug.LogError($"ExecuteBat Error Dest Path! ");
                    return;
                }
                wslDst = serverIP + wslDst;
                proc.StartInfo.Arguments = string.Format("{0}/ {1}/", wslSrc, wslDst);
                proc.StartInfo.CreateNoWindow = false;
                proc.StartInfo.UseShellExecute = false;
                proc.StartInfo.RedirectStandardOutput = true;
                proc.OutputDataReceived += (object sender, System.Diagnostics.DataReceivedEventArgs e) => {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        output.Append(e.Data);
                    }
                };
                proc.Start();
                proc.BeginOutputReadLine();
                proc.WaitForExit();
                Debug.Log(output.ToString());
                Debug.LogError($"ExecuteBat Success name:{batName} {proc.StartInfo.Arguments}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"ExecuteBat fail name:{batName} err:{ex.Message}");
            }
        }

        //[MenuItem("Project/Build Window/RsyncUnitTest")]
        //public static void TestRsync()
        //{
        //    string src = @"C:\Test\";
        //    ExecuteRsync("rsync_package", src, "Android");
        //}
    }

    public class BuildReport
    {
        public int HybirdCLRTime;
        public int AssetBundleTime;
        public int AppBuildTime;
    }

    public static class BuildTargetUtility
    {
        public static BuildTargetGroup GetBuildTargetGroup(BuildTarget buildTarget)
        {
            switch (buildTarget)
            {
                case BuildTarget.StandaloneOSX:
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                case BuildTarget.StandaloneLinux64:
                    return BuildTargetGroup.Standalone;

                case BuildTarget.iOS:
                    return BuildTargetGroup.iOS;

                case BuildTarget.Android:
                    return BuildTargetGroup.Android;

                case BuildTarget.WebGL:
                    return BuildTargetGroup.WebGL;

                case BuildTarget.WSAPlayer:
                    return BuildTargetGroup.WSA;

                case BuildTarget.PS4:
                case BuildTarget.PS5:
                    return BuildTargetGroup.PS4; // �����������Ҫʹ�� BuildTargetGroup.PS5

                case BuildTarget.XboxOne:
                    return BuildTargetGroup.XboxOne;

                case BuildTarget.tvOS:
                    return BuildTargetGroup.tvOS;

                // �������ƽ̨�Ķ�Ӧ��ϵ
                default:
                    Debug.LogWarning("Unknown BuildTarget: " + buildTarget);
                    return BuildTargetGroup.Unknown;
            }
        }

        public static string BuildTarget2Platform(BuildTarget buildTarget)
        {
            switch (buildTarget)
            {
                case BuildTarget.StandaloneOSX:
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                case BuildTarget.StandaloneLinux64:
                    return "pc";

                case BuildTarget.iOS:
                    return "ios";

                case BuildTarget.Android:
                    return "android";

                case BuildTarget.WebGL:
                    return "webgl";

                case BuildTarget.WSAPlayer:
                    return "wsaplayer";

                case BuildTarget.PS4:
                case BuildTarget.PS5:
                    return "ps"; // �����������Ҫʹ�� BuildTargetGroup.PS5

                case BuildTarget.XboxOne:
                    return "xbox";

                case BuildTarget.tvOS:
                    return "tvos";

                // �������ƽ̨�Ķ�Ӧ��ϵ
                default:
                    Debug.LogWarning("Unknown BuildTarget: " + buildTarget);
                    return "unkonwn";
            }
        }
    }

    public static class WslPathConverter
    {
        public static string ConvertWindowsPathToWslPath(string windowsPath)
        {
            // �淶·����ʽ���������·����������ţ�
            string fullPath = Path.GetFullPath(windowsPath);

            // ����Ƿ�Ϊ����·��
            if (fullPath.StartsWith(@"\\"))
                throw new ArgumentException("Network paths are not supported");

            // ��֤�Ƿ�Ϊ��Ч��������·��
            if (!fullPath.Contains(":\\"))
                throw new ArgumentException("Invalid Windows path format");

            // ��ȡ�������ţ��� C:��
            string driveLetter = fullPath.Substring(0, 1).ToLower();
            string remainingPath = fullPath.Substring(3); // ���� "C:\"

            // ���� WSL ·��
            return $"/mnt/{driveLetter}/{remainingPath.Replace('\\', '/')}";
        }

        /* �߼��汾���������߽���� */
        public static string SafeConvert(string windowsPath)
        {
            try
            {
                string fullPath = Path.GetFullPath(windowsPath);

                if (fullPath.StartsWith(@"\\", StringComparison.Ordinal))
                    return $"/mnt/host/{fullPath.Substring(2).Replace('\\', '/')}";

                if (Path.GetPathRoot(fullPath) is string root && root.Contains(":"))
                {
                    string drive = root.Substring(0, 1).ToLower();
                    string unixPath = fullPath
                        .Substring(root.Length)
                        .Replace('\\', '/')
                        .TrimEnd('/');

                    return $"/mnt/{drive}/{unixPath}";
                }

                throw new ArgumentException("Unsupported path type");
            }
            catch (Exception ex)
            {
                return $"ת��ʧ��: {ex.Message}";
            }
        }
    }

}
