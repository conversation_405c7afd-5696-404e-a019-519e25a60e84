// Copyright (c) Microsoft. All rights reserved.
// Licensed under the MIT license. See LICENSE file in the project root for full license information.

using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("DotNetty.Buffers.Tests, PublicKey=0024000004800000940000000602000000240000525341310004000001000100d9782d5a0b850f230f71e06de2e101d8441d83e15eef715837eee38fdbf5cb369b41ec36e6e7668c18cbb09e5419c179360461e740c1cce6ffbdcf81f245e1e705482797fe42aff2d31ecd72ea87362ded3c14066746fbab4a8e1896f8b982323c84e2c1b08407c0de18b7feef1535fb972a3b26181f5a304ebd181795a46d8f")]
[assembly: InternalsVisibleTo("DotNetty.Microbench, PublicKey=0024000004800000940000000602000000240000525341310004000001000100d9782d5a0b850f230f71e06de2e101d8441d83e15eef715837eee38fdbf5cb369b41ec36e6e7668c18cbb09e5419c179360461e740c1cce6ffbdcf81f245e1e705482797fe42aff2d31ecd72ea87362ded3c14066746fbab4a8e1896f8b982323c84e2c1b08407c0de18b7feef1535fb972a3b26181f5a304ebd181795a46d8f")]

