using System;
using UnityEditor;
using UnityEngine;
using UnityEditor.UIElements;
using UnityEngine.UIElements;
using Monitor.GOT.Editor.Settings;

namespace Monitor.GOT.Editor
{
    public class MonitorWindow : EditorWindow
    {
        public const int FontSize = 20;

        [MenuItem("Monitor/Setting", false, 103)]
        public static void OpenWindow()
        {
            MonitorWindow window = GetWindow<MonitorWindow>("Monitor Setting", true);
            window.minSize = new Vector2(800, 600);
        }

        private SerializedObject _serializedObject;

        // Toggle _enbaleToggle;

        public void CreateGUI()
        {
            try
            {
                _serializedObject = new SerializedObject(MonitorSettings.instance);
                // VisualElement root = this.rootVisualElement;

                // _enbaleToggle = new Toggle("Monitor Enable");
                // _enbaleToggle.SetValueWithoutNotify(MonitorSettings.instance.enable);

                // var list = new ListView();

                // root.Add(_enbaleToggle);
                // root.Add(list);

            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }
        }

        void OnGUI()
        {
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(_serializedObject.FindProperty("enable"));
            EditorGUILayout.PropertyField(_serializedObject.FindProperty("enableEditor"));
            EditorGUILayout.PropertyField(_serializedObject.FindProperty("hookAssemblyDefinitions"));
            if (EditorGUI.EndChangeCheck())
            {
                _serializedObject.ApplyModifiedPropertiesWithoutUndo();
                MonitorSettings.instance.Save();
            }
            // if(_enbaleToggle.value != MonitorSettings.instance.enable)
            // {
            //     MonitorSettings.instance.enable = _enbaleToggle.value;
            //     MonitorSettings.instance.Save();
            // }
        }
    }
}