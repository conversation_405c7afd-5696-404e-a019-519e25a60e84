using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.Profiling;

namespace Monitor.GOT
{
    public static class MonitorDataUtil
    {
        public static bool IsMonitor { get; private set; }
        private static Dictionary<string, FunctionHookData> ms_dictFucntionHookData = new Dictionary<string, FunctionHookData>();

        public static void FuncBegin(string methodFullName)
        {
            if (!IsMonitor)
                return;

            FunctionHookData tmp;
            long tmpMemory = Profiler.GetTotalAllocatedMemoryLong();
            if (!ms_dictFucntionHookData.TryGetValue(methodFullName, out tmp))
            {
                tmp.stopwatch = new Stopwatch();
                tmp.FuncName = methodFullName;
                tmp.FuncCalls = 0;
                tmp.FuncTotalMemory = 0L;
                tmp.FuncTotalTime = 0L;
                ms_dictFucntionHookData.Add(methodFullName, tmp);
                tmp.stopwatch.Start();
                tmp.BeginMemory = tmpMemory;
            }
            else
            {
                tmp.stopwatch.Restart();
                tmp.BeginMemory = tmpMemory;
            }
        }

        public static void FuncEnd(string methodFullName)
        {
            if (!IsMonitor)
                return;

            FunctionHookData tmp;
            if (ms_dictFucntionHookData.TryGetValue(methodFullName, out tmp))
            {
                tmp.stopwatch.Stop();
                tmp.FuncTotalTime += tmp.stopwatch.ElapsedMilliseconds;
                tmp.FuncCalls++;
                tmp.FuncTotalMemory = Profiler.GetTotalAllocatedMemoryLong() - tmp.BeginMemory;
                ms_dictFucntionHookData[methodFullName] = tmp;
            }
        }

        public static void MonitorBegin()
        {
            if (IsMonitor)
                return;

            ms_dictFucntionHookData.Clear();
            IsMonitor = true;
        }

        public static void MonitorEnd()
        {
            if (!IsMonitor)
                return;

            IsMonitor = false;

            int totalMinutes = DateTime.Now.Hour * 60 + DateTime.Now.Minute;
            var path = Path.Combine(UnityEngine.Application.persistentDataPath, MonitorSetting.MonitorPath);

            if (Directory.Exists(path) == false)
            {
                Directory.CreateDirectory(path);
            }

            MonitorData data = new MonitorData();
            data.functionHookDataLst = ms_dictFucntionHookData.Values.ToList();
            data.deviceInfo = GetSystemInfo();

            String json = UnityEngine.JsonUtility.ToJson(data);
            using (StreamWriter sw = new StreamWriter($"{path}/monitor_{DateTime.Now.ToString("yyyy-MM-dd")}-{totalMinutes}{MonitorSetting.MonitorExtension}"))
            {
                sw.Write(json);
            }
        }

		public static void CopyFolder(string srcPath, string tarPath)
		{
			if (!Directory.Exists(srcPath))
			{
				UnityEngine.Debug.Log("CopyFolder is finish.");
				return;
			}

			if (!Directory.Exists(tarPath))
			{
				Directory.CreateDirectory(tarPath);
			}

			//���Դ�ļ��������ļ�
			List<string> files = new List<string>(Directory.GetFiles(srcPath));
			files.ForEach(f =>
			{
				string destFile = Path.Combine(tarPath, Path.GetFileName(f));
				File.Copy(f, destFile, true); //����ģʽ
			});

			//���Դ�ļ�������Ŀ¼�ļ�
			List<string> folders = new List<string>(Directory.GetDirectories(srcPath));
			folders.ForEach(f =>
			{
				string destDir = Path.Combine(tarPath, Path.GetFileName(f));
				CopyFolder(f, destDir); //�ݹ�ʵ�����ļ��п���
			});
		}

        private static DeviceInfo GetSystemInfo()
        {
            return new DeviceInfo()
            {
                UnityVersion = Application.unityVersion,
                DeviceModel = SystemInfo.deviceModel,
                BatteryLevel = SystemInfo.batteryLevel,
                DeviceName = SystemInfo.deviceName,
                DeviceUniqueIdentifier = SystemInfo.deviceUniqueIdentifier,
                GraphicsDeviceName = SystemInfo.graphicsDeviceName,
                GraphicsDeviceVendor = SystemInfo.graphicsDeviceVendor,
                GraphicsDeviceVersion = SystemInfo.graphicsDeviceVersion,
                GraphicsMemorySize = SystemInfo.graphicsMemorySize,
                OperatingSystem = SystemInfo.operatingSystem,
                ProcessorCount = SystemInfo.processorCount,
                ProcessorFrequency = SystemInfo.processorFrequency,
                ProcessorType = SystemInfo.processorType,
                SupportsShadows = SystemInfo.supportsShadows,
                SystemMemorySize = SystemInfo.systemMemorySize,
                ScreenHeight = Screen.height,
                ScreenWidth = Screen.width
            };
        }

        private static void DecodeMetchAndWrite(string path, string outPath, out string jsonStr)
        {
            string fileBaseName = Path.GetFileNameWithoutExtension(path);
            string fileCSVName = $"{outPath}/{fileBaseName}.csv";
            string fileDeviceInfo = $"{outPath}/{fileBaseName}_deciceInfo.txt";

            MonitorData data;

            using (StreamReader sr = new StreamReader(path))
            {
                jsonStr = sr.ReadToEnd();
                data = UnityEngine.JsonUtility.FromJson<MonitorData>(jsonStr);
            }

            if(data == null)
                return;
            
            var functionHookDataLst = data.functionHookDataLst.Where(x => x.FuncCalls > 0).ToList();
            functionHookDataLst.Sort((a, b) => {
                if( a.FuncTotalTime > b.FuncTotalTime)
                {
                    return -1;
                }
                else if( a.FuncTotalTime < b.FuncTotalTime)
                {
                    return 1;
                }
                else
                {
                    return 0;
                }
            });

            string header = "FuncName,FuncMemory/k,FuncAverageMemory/k,FuncUseTime/s,FuncAverageTime/ms,FuncCalls";
            using (StreamWriter sw = new StreamWriter(fileCSVName))
            {
                sw.WriteLine(header);
                foreach(var tmp in functionHookDataLst)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.AppendFormat("{0},", tmp.FuncName.Replace(",", " "));
                    sb.AppendFormat("{0:f4}\t,", tmp.FuncTotalMemory / 1024.0); //���ص���ռ���ڴ�
                    sb.AppendFormat("{0:f4}\t,", tmp.FuncTotalMemory / (tmp.FuncCalls * 1024.0));
                    sb.AppendFormat("{0}\t,", tmp.FuncTotalTime / 1000);
                    sb.AppendFormat("{0:}\t,", (long)((double)tmp.FuncTotalTime / (double)tmp.FuncCalls));
                    sb.AppendFormat("{0}\t", tmp.FuncCalls);
                    sw.WriteLine(sb);
                }
                sw.Close();
            }
            UnityEngine.Debug.Log($"�������ܱ���{fileCSVName}�ļ�������");

            using(StreamWriter sw = new StreamWriter(fileDeviceInfo))
            {
                sw.WriteLine(data.deviceInfo.ToString());
            }
        }

        private static void HtmlDataWrite(string jsonStr, string path)
        {
            if(File.Exists(path))
                File.Delete(path);

            using(StreamWriter sw = new StreamWriter(path))
            {
                sw.WriteLine($"train({jsonStr})");
            }
        }

        public static void MethodAnalysisReport(string monitorPath)
        {
            string fileBaseName = Path.GetFileNameWithoutExtension(monitorPath);
            string filePath = $"{Path.GetDirectoryName(monitorPath)}/{fileBaseName}";
            
            if (!File.Exists(monitorPath))
            {
                return;
            }

            if(!Directory.Exists(filePath))
            {
                Directory.CreateDirectory(filePath);
            }

            CopyFolder(Path.Combine(Application.dataPath, MonitorSetting.monitorHtmlTmpPath), $"{filePath}/monitorHtml");

            string jsonStr;
            DecodeMetchAndWrite(monitorPath, filePath, out jsonStr);

            var dataPath = Path.Combine(filePath, "monitorHtml/data/monitor.data");
            HtmlDataWrite(jsonStr, dataPath);

            System.Diagnostics.Process.Start("explorer.exe", filePath.Replace("/", "\\"));
        }

        public static void MethodCompareReport(string monitorPathA, string monitorPathB)
        {
            string fileBaseNameA = Path.GetFileNameWithoutExtension(monitorPathA);
            string fileBaseNameB = Path.GetFileNameWithoutExtension(monitorPathB);

            string filePath = $"{Path.GetDirectoryName(monitorPathA)}/{fileBaseNameA}_{fileBaseNameB}";
            
            if (!File.Exists(monitorPathA) || !File.Exists(monitorPathB))
            {
                return;
            }

            if(!Directory.Exists(filePath))
            {
                Directory.CreateDirectory(filePath);
            }

            CopyFolder(Path.Combine(Application.dataPath, MonitorSetting.monitorCompareHtmlTmpPath), $"{filePath}/monitorHtml");

            string jsonStrA;
            DecodeMetchAndWrite(monitorPathA, filePath, out jsonStrA);
            var dataPathA = Path.Combine(filePath, "monitorHtml/data/monitor_1.data");
            HtmlDataWrite(jsonStrA, dataPathA);

            string jsonStrB;
            DecodeMetchAndWrite(monitorPathB, filePath, out jsonStrB);
            var dataPathB = Path.Combine(filePath, "monitorHtml/data/monitor_2.data");
            HtmlDataWrite(jsonStrB, dataPathB);

            System.Diagnostics.Process.Start("explorer.exe", filePath.Replace("/", "\\"));
        }
    }
}