using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace YooAsset.Editor
{
    public class TaskCopyRawFile_SBP : IBuildTask
    {
        void IBuildTask.Run(BuildContext context)
        {
            var buildParametersContext = context.GetContextObject<BuildParametersContext>();
            var buildParameters = context.GetContextObject<BuildParametersContext>();
            var buildMapContext = context.GetContextObject<BuildMapContext>();

            var buildMode = buildParameters.Parameters.BuildMode;
            if (buildMode == EBuildMode.ForceRebuild || buildMode == EBuildMode.IncrementalBuild)
            {
                CopyRawBundle(buildMapContext, buildParametersContext);
            }
        }

        /// <summary>
        /// 拷贝原生文件
        /// </summary>
        private void CopyRawBundle(BuildMapContext buildMapContext, BuildParametersContext buildParametersContext)
        {
            Dictionary<string, string> rawBundleDict = new Dictionary<string, string>();
            string pipelineOutputDirectory = buildParametersContext.GetPipelineOutputDirectory();
            foreach (var bundleInfo in buildMapContext.Collection)
            {
                if (bundleInfo.IsRawFile)
                {
                    string dest = $"{pipelineOutputDirectory}/{bundleInfo.BundleName}";
                    foreach (var buildAsset in bundleInfo.MainAssets)
                    {
                        EditorTools.CopyFile(buildAsset.AssetInfo.AssetPath, dest, true);
                        string md5 = HashUtility.FileMD5(buildAsset.AssetInfo.AssetPath);
                        rawBundleDict.Add(buildAsset.AssetInfo.AssetPath, md5);
                    }
                }
            }

            string rawjson = Newtonsoft.Json.JsonConvert.SerializeObject(rawBundleDict, Newtonsoft.Json.Formatting.Indented);
            // string json = JsonUtility.ToJson(wwiseFiles, true);
            string savePath = $"{pipelineOutputDirectory}/RawFiles.json";
            FileUtility.WriteAllText(savePath, rawjson);
        }
    }
}