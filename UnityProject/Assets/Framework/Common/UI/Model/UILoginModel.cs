using QFramework;
using Cysharp.Threading.Tasks;
using UnityEngine;
using YooAsset;

namespace MyFramework
{
    public class UILoginModel : AbstractModel
    {
        public string UserName { get; set; }
        public string Password { get; set; }

        AssetHandle mHandleUI;

        SceneHandle mHandleScene;

        GameObject mLoginPrefab;

        protected override void OnInit()
        {
            UserName = string.Empty;    
            Password = string.Empty;
        }

        public void StartLogin()
        {
            var package = YooAssets.GetPackage("DefaultPackage");
            mHandleUI = package.LoadAssetAsync<GameObject>("Assets/Framework/Sample/UI/Login/LoginUI.prefab");
            mHandleUI.Completed += Handle_Completed;

            string location = "Assets/Framework/Sample/Scene/Login.unity";
            var sceneMode = UnityEngine.SceneManagement.LoadSceneMode.Additive;
            mHandleScene = package.LoadSceneAsync(location, sceneMode);
        }

        public void Destroy()
        {
            if(mHandleUI != null)
            {
                mHandleUI.Release();
            }
            if(mHandleScene != null)
            {
                mHandleScene.UnloadAsync();
            }
            GameObject.Destroy(mLoginPrefab);
        }

        void Handle_Completed(AssetHandle handle)
        {
            GameObject loginData = handle.AssetObject as GameObject;
            mLoginPrefab = GameObject.Instantiate(loginData);
        }

        public void Login(string name, string password)
        {         
           
        }

        private void LoginSuccess()
        {
            //CommonArchitecture.Interface.GetUtility<GameStateFSM>().ChangeState(GameStateFSM.States.GameWorld);
        }
    }
}
