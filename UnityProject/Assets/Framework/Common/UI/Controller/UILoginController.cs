using UnityEngine;
using UnityEngine.UI;
using QFramework;


namespace MyFramework
{
    public class UILoginController : Mono<PERSON><PERSON><PERSON><PERSON>, IController
    {
        UILoginModel mModel;
        public TMPro.TMP_InputField mUserInput;
        public TMPro.TMP_InputField mPasswordInput;
        public Button mEnterBtn;

        void Start()
        {
            // 5. 获取模型
            mModel = this.GetModel<UILoginModel>();

            // 监听输入
            mEnterBtn.onClick.AddListener(() =>
            {
                // 交互逻辑
                this.SendCommand<LoginCommand>(new LoginCommand(mUserInput.text, mPasswordInput.text));
                // 表现逻辑
                UpdateView();
            });

            UpdateView();
        }
        void UpdateView()
        {
            
        }

        private void OnDestroy()
        {
            mModel = null;
        }

        public IArchitecture GetArchitecture()
        {
            return UIArchitecture.Interface;
        }
    }

    public class LoginCommand : AbstractCommand
    {
        private string msName;
        private string msPassword;
        public LoginCommand(string name, string password)
        {
            msName = name;
            msPassword = password;
        }
        protected override void OnExecute()
        {
            this.GetModel<UILoginModel>().Login(msName, msPassword);
        }
    }
}
