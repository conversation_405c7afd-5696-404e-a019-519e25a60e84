// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: test/test.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
/// <summary>Holder for reflection information generated from test/test.proto</summary>
[System.Serializable]
public static partial class testReflection {

  #region Descriptor
  /// <summary>File descriptor for test/test.proto</summary>
  public static pbr::FileDescriptor Descriptor {
    get { return descriptor; }
  }
  private static pbr::FileDescriptor descriptor;

  static testReflection() {
    byte[] descriptorData = global::System.Convert.FromBase64String(
        @"
          Cg90ZXN0L3Rlc3QucHJvdG8iTAoNU2VhcmNoUmVxdWVzdBINCgVxdWVyeRgB
          IAEoCRITCgtwYWdlX251bWJlchgCIAEoBRIXCg9yZXN1bHRfcGVyX3BhZ2UY
          AyABKAViBnByb3RvMw==");
    descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
        new pbr::FileDescriptor[] { },
        new pbr::GeneratedClrTypeInfo(null, new pbr::GeneratedClrTypeInfo[] {
          new pbr::GeneratedClrTypeInfo(typeof(global::SearchRequest), global::SearchRequest.Parser, new[]{ "query", "page_number", "result_per_page" }, null, null, null)
        }));
  }
  #endregion

}
#region Messages
[System.Serializable]
public sealed partial class SearchRequest : pb::IMessage<SearchRequest> {
  private static readonly pb::MessageParser<SearchRequest> _parser = new pb::MessageParser<SearchRequest>(() => new SearchRequest());
  private pb::UnknownFieldSet _unknownFields;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pb::MessageParser<SearchRequest> Parser { get { return _parser; } }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public static pbr::MessageDescriptor Descriptor {
    get { return global::testReflection.Descriptor.MessageTypes[0]; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  pbr::MessageDescriptor pb::IMessage.Descriptor {
    get { return Descriptor; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public SearchRequest() {
    OnConstruction();
  }

  partial void OnConstruction();

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public SearchRequest(SearchRequest other) : this() {
    query_ = other.query_;
    page_number_ = other.page_number_;
    result_per_page_ = other.result_per_page_;
    _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public SearchRequest Clone() {
    return new SearchRequest(this);
  }

  /// <summary>Field number for the "query" field.</summary>
  public const int queryFieldNumber = 1;
  private string query_ = "";
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public string query {
    get { return query_; }
    set {
      query_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
    }
  }

  /// <summary>Field number for the "page_number" field.</summary>
  public const int page_numberFieldNumber = 2;
  private int page_number_;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public int page_number {
    get { return page_number_; }
    set {
      page_number_ = value;
    }
  }

  /// <summary>Field number for the "result_per_page" field.</summary>
  public const int result_per_pageFieldNumber = 3;
  private int result_per_page_;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public int result_per_page {
    get { return result_per_page_; }
    set {
      result_per_page_ = value;
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override bool Equals(object other) {
    return Equals(other as SearchRequest);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public bool Equals(SearchRequest other) {
    if (ReferenceEquals(other, null)) {
      return false;
    }
    if (ReferenceEquals(other, this)) {
      return true;
    }
    if (query != other.query) return false;
    if (page_number != other.page_number) return false;
    if (result_per_page != other.result_per_page) return false;
    return Equals(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override int GetHashCode() {
    int hash = 1;
    if (query.Length != 0) hash ^= query.GetHashCode();
    if (page_number != 0) hash ^= page_number.GetHashCode();
    if (result_per_page != 0) hash ^= result_per_page.GetHashCode();
    if (_unknownFields != null) {
      hash ^= _unknownFields.GetHashCode();
    }
    return hash;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public override string ToString() {
    return pb::JsonFormatter.ToDiagnosticString(this);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void WriteTo(pb::CodedOutputStream output) {
    if (query.Length != 0) {
      output.WriteRawTag(10);
      output.WriteString(query);
    }
    if (page_number != 0) {
      output.WriteRawTag(16);
      output.WriteInt32(page_number);
    }
    if (result_per_page != 0) {
      output.WriteRawTag(24);
      output.WriteInt32(result_per_page);
    }
    if (_unknownFields != null) {
      _unknownFields.WriteTo(output);
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public int CalculateSize() {
    int size = 0;
    if (query.Length != 0) {
      size += 1 + pb::CodedOutputStream.ComputeStringSize(query);
    }
    if (page_number != 0) {
      size += 1 + pb::CodedOutputStream.ComputeInt32Size(page_number);
    }
    if (result_per_page != 0) {
      size += 1 + pb::CodedOutputStream.ComputeInt32Size(result_per_page);
    }
    if (_unknownFields != null) {
      size += _unknownFields.CalculateSize();
    }
    return size;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(SearchRequest other) {
    if (other == null) {
      return;
    }
    if (other.query.Length != 0) {
      query = other.query;
    }
    if (other.page_number != 0) {
      page_number = other.page_number;
    }
    if (other.result_per_page != 0) {
      result_per_page = other.result_per_page;
    }
    _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  public void MergeFrom(pb::CodedInputStream input) {
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
      switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
          break;
        case 10: {
          query = input.ReadString();
          break;
        }
        case 16: {
          page_number = input.ReadInt32();
          break;
        }
        case 24: {
          result_per_page = input.ReadInt32();
          break;
        }
      }
    }
  }

}

#endregion


#endregion Designer generated code
