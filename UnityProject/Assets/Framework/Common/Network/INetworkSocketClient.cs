using System.Collections;
using System.Net;
using System;

namespace MyFramework
{
    /// <summary>
    /// Socket client
    /// </summary>
    public interface INetworkSocketClient
    {
        /// <summary>
        /// Server address
        /// </summary>
        string Address { get; }

        /// <summary>
        /// Server listening port
        /// </summary>
        int Port { get; }

        /// <summary>
        /// If client is connected
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// Close the client
        /// </summary>
        /// <remarks>
        /// <see cref="Closed"/> will be invoked on main thread if you call this
        /// <para/>
        /// Both sending and receiving will be shutdown
        /// </remarks>
        void Close();

        /// <summary>
        /// Connect to server
        /// </summary>
        /// <remarks>
        /// This won't block the main thread
        /// <para/>
        /// <see cref="Connected"/> will be invoked on the async thread if you call this
        /// <para/>
        /// If successful, a receiving loop will be started and <see cref="Received"/>/<see cref="ReceivedAsString"/> will be invoked when a message is received
        /// </remarks>
        void Connect();


        /// <summary>
        /// Send message to server
        /// </summary>
        /// <param name="message">Raw messaged</param>
        /// <remarks>
        /// This won't block the main thread
        /// <para/>
        /// <see cref="Sent"/> will be invoked on the async thread if you call this
        /// </remarks>
        void Send(byte[] message, int iType = 0);

        /// <summary>
        /// Send message to server
        /// </summary>
        /// <param name="message">String message</param>
        /// <remarks>
        /// This won't block the main thread
        /// <para/>
        /// <see cref="Sent"/> will be invoked on the async thread if you call this
        /// </remarks>
        void Send(string message);


        /// <summary>
        /// Disconnect from server
        /// </summary>
        /// <remarks>
        /// This won't block the main thread
        /// <para/>
        /// <see cref="Disconnected"/> will be invoked on the async thread if you call this
        /// </remarks>
        void Disconnect();

    }

}