using System.Collections;
using System.Collections.Generic;
using YooAsset;
using QFramework;

namespace MyFramework
{
    public class GameStateFSM : Tickable,IUtility
    {
        public bool IsEnd { set; get; } = false;
        public enum States
        {
            StartLogo,
            CheckGameConfig,
            PatchUpdate,
            //GameLogin,
            //GameWorld,
        }

        public FSM<States> FSM = new FSM<States>();

        public void Init()
        {
            Register();
            FSM.AddState(States.StartLogo, new GameStateStartLogo(FSM, this));
            FSM.AddState(States.CheckGameConfig, new GameStateCheckGameConfig(FSM, this));
            FSM.AddState(States.PatchUpdate, new GameStatePatchUpdate(FSM, this));
            //FSM.AddState(States.GameLogin, new GameStateLogin(FSM, this));
            //FSM.AddState(States.GameWorld, new GameStateWorld(FSM, this));
        }

        public void UnInit()
        {
            UnRegister();
        }

        public void StartFSM(EPlayMode playMode)
        {
            mPlayMode = playMode;
            FSM.StartState(States.StartLogo);
        }

        /// <summary>
        /// 外部调用切换状态
        /// </summary>
        /// <param name="st"></param>
        public void ChangeState(States st)
        {
            FSM.ChangeState(st);
        }

        public override void Tick()
        {
            FSM.Update(); 
        }

        /// <summary>
        /// 
        /// </summary>
        public void OnGUI()
        {
            FSM.OnGUI();
        }

        public EPlayMode mPlayMode;
    }
}

