using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;


namespace MyFramework
{
    public class GameStatePatchUpdate : AbstractState<GameStateFSM.States, GameStateFSM>
    {
        PatchManagerFSM managerFSM;
        public GameStatePatchUpdate(FSM<GameStateFSM.States> fsm, GameStateFSM target) : base(fsm, target)
        {           
        }

        /// <summary>
        /// 加载StartLogo的prefab
        /// </summary>
        protected override void OnEnter()
        {
            Debug.Log("GameStatePatchUpdate OnEnter!");
            
            managerFSM = CommonArchitecture.Interface.GetUtility<PatchManagerFSM>();
            managerFSM.StartLogic(mTarget.mPlayMode);
        }

        //protected override void OnUpdate()
        //{
        //    if(managerFSM.IsEnd)
        //    {
        //        mTarget.IsEnd = true;
        //    }
        //}

    }

}

