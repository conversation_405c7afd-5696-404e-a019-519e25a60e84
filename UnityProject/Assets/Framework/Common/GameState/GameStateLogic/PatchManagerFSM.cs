using System.Collections;
using QFramework;
using UnityEngine;
using YooAsset;

namespace MyFramework
{
    public class PatchManagerFSM : Tickable,IUtility
    {

		public interface IWndEventMessage
        {

        }

		/// <summary>
		/// 补丁包初始化失败
		/// </summary>
		public class InitializeFailed : IWndEventMessage
		{

		}

		/// <summary>
		/// 补丁流程步骤改变
		/// </summary>
		public class PatchStatesChange : IWndEventMessage
		{
			public string Tips;

			public static void SendEventMessage(string tips)
			{
				var msg = new PatchStatesChange();
				msg.Tips = tips;
				TypeEventSystem.Global.Send<IWndEventMessage>(msg);
			}
		}

		/// <summary>
		/// 发现更新文件
		/// </summary>
		public class FoundUpdateFiles : IWndEventMessage
		{
			public int TotalCount;
			public long TotalSizeBytes;

			public static void SendEventMessage(int totalCount, long totalSizeBytes)
			{
				var msg = new FoundUpdateFiles();
				msg.TotalCount = totalCount;
				msg.TotalSizeBytes = totalSizeBytes;
				TypeEventSystem.Global.Send<IWndEventMessage>(msg);
			}
		}

		/// <summary>
		/// 下载进度更新
		/// </summary>
		public class DownloadProgressUpdate : IWndEventMessage
		{
			public int TotalDownloadCount;
			public int CurrentDownloadCount;
			public long TotalDownloadSizeBytes;
			public long CurrentDownloadSizeBytes;

			public static void SendEventMessage(int totalDownloadCount, int currentDownloadCount, long totalDownloadSizeBytes, long currentDownloadSizeBytes)
			{
				var msg = new DownloadProgressUpdate();
				msg.TotalDownloadCount = totalDownloadCount;
				msg.CurrentDownloadCount = currentDownloadCount;
				msg.TotalDownloadSizeBytes = totalDownloadSizeBytes;
				msg.CurrentDownloadSizeBytes = currentDownloadSizeBytes;
				TypeEventSystem.Global.Send<IWndEventMessage>(msg);
			}
		}

		/// <summary>
		/// 资源版本号更新失败
		/// </summary>
		public class PackageVersionUpdateFailed : IWndEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new PackageVersionUpdateFailed();
				TypeEventSystem.Global.Send<IWndEventMessage>(msg);
			}
		}

		/// <summary>
		/// 补丁清单更新失败
		/// </summary>
		public class PatchManifestUpdateFailed : IWndEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new PatchManifestUpdateFailed();
				TypeEventSystem.Global.Send<IWndEventMessage>(msg);
			}
		}

		/// <summary>
		/// 网络文件下载失败
		/// </summary>
		public class WebFileDownloadFailed : IWndEventMessage
		{
			public string FileName;
			public string Error;

			public static void SendEventMessage(string fileName, string error)
			{
				var msg = new WebFileDownloadFailed();
				msg.FileName = fileName;
				msg.Error = error;
				TypeEventSystem.Global.Send<IWndEventMessage>(msg);
			}
		}

		public interface IEventMessage
        {

        }

		/// <summary>
		/// 用户尝试再次初始化资源包
		/// </summary>
		public struct UserTryInitialize : IEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new UserTryInitialize();
				TypeEventSystem.Global.Send<IEventMessage>(msg);
			}
		}

		/// <summary>
		/// 用户开始下载网络文件
		/// </summary>
		public struct UserBeginDownloadWebFiles : IEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new UserBeginDownloadWebFiles();
				TypeEventSystem.Global.Send<IEventMessage>(msg);
			}
		}

		/// <summary>
		/// 用户尝试再次更新静态版本
		/// </summary>
		public struct UserTryUpdatePackageVersion : IEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new UserTryUpdatePackageVersion();
				TypeEventSystem.Global.Send<IEventMessage>(msg);
			}
		}

		/// <summary>
		/// 用户尝试再次更新补丁清单
		/// </summary>
		public struct UserTryUpdatePatchManifest : IEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new UserTryUpdatePatchManifest();
				TypeEventSystem.Global.Send<IEventMessage>(msg);
			}
		}

		/// <summary>
		/// 用户尝试再次下载网络文件
		/// </summary>
		public struct UserTryDownloadWebFiles : IEventMessage
		{
			public static void SendEventMessage()
			{
				var msg = new UserTryDownloadWebFiles();
				TypeEventSystem.Global.Send<IEventMessage>(msg);
			}
		}



		public enum States
		{
			PatchUpdateStatePrepare,
			PatchUpdateStateInitialize,
			PatchUpdateStateUpdateVersion,
			PatchUpdateStateUpdateManifest,
			PatchUpdateStateCreateDownloader,
			PatchUpdateStateDownloadFiles,
			PatchUpdateStateCopyRawFiles,//拷贝原生文件
			PatchUpdateStateDownloadOver,
            PatchUpdateStateLoadDll,
            PatchUpdateStateClearCache,
			PatchUpdateStateDone,
		}

		public FSM<States> FSM = new FSM<States>();

		/// <summary>
		/// 运行模式
		/// </summary>
		public EPlayMode PlayMode { private set; get; }

		/// <summary>
		/// 包裹的版本信息
		/// </summary>
		public string PackageVersion { set; get; }

		/// <summary>
		/// 下载器
		/// </summary>
		public ResourceDownloaderOperation Downloader { set; get; }

		public GameObject mPatchWindowGo;

		public bool IsEnd { set; get; } = false;

		private bool _isRun = false;

        public void Init()
		{
			Register();	
		}


        public override void Tick()
        {
			if(_isRun)
				FSM.Update();
		}

		public void StartLogic(EPlayMode playMode)
		{
			if (_isRun == false)
			{
				_isRun = true;
				PlayMode = playMode;

                // 注册监听事件
                TypeEventSystem.Global.Register<IEventMessage>(OnHandleEventMessage);

                Debug.Log("开启补丁更新流程...");
				FSM.AddState(States.PatchUpdateStatePrepare, new PatchUpdateStatePrepare(FSM, this));
				FSM.AddState(States.PatchUpdateStateInitialize, new PatchUpdateStateInitialize(FSM, this));
				FSM.AddState(States.PatchUpdateStateUpdateVersion, new PatchUpdateStateUpdateVersion(FSM, this));
				FSM.AddState(States.PatchUpdateStateUpdateManifest, new PatchUpdateStateUpdateManifest(FSM, this));
				FSM.AddState(States.PatchUpdateStateCreateDownloader, new PatchUpdateStateCreateDownloader(FSM, this));
				FSM.AddState(States.PatchUpdateStateDownloadFiles, new PatchUpdateStateDownloadFiles(FSM, this));
				FSM.AddState(States.PatchUpdateStateDownloadOver, new PatchUpdateStateDownloadOver(FSM, this));
                FSM.AddState(States.PatchUpdateStateLoadDll, new PatchUpdateStateLoadDll(FSM, this));
                FSM.AddState(States.PatchUpdateStateClearCache, new PatchUpdateStateClearCache(FSM, this));
				FSM.AddState(States.PatchUpdateStateDone, new PatchUpdateStateDone(FSM, this));

                var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
                bool? bSkipUpdatePatch = gameIni.GetBool("SkipUpdatePatch");

                if (bSkipUpdatePatch.HasValue && bSkipUpdatePatch.Value)
				{
					FSM.StartState(States.PatchUpdateStateInitialize);
                }
                else
                {
                    FSM.StartState(States.PatchUpdateStatePrepare);
                }               
            }
			else
			{
				Debug.LogWarning("补丁更新已经正在进行中!");
			}
		}

		public void Destroy()
        {
            if (mPatchWindowGo != null)
            {
                GameObject.Destroy(mPatchWindowGo);
            }
            IsEnd = true;
            UnRegister();
			TypeEventSystem.Global.UnRegister<IEventMessage>(OnHandleEventMessage);
        }


		private void OnHandleEventMessage(IEventMessage message)
		{
			if (message is UserTryInitialize)
			{
				FSM.ChangeState(States.PatchUpdateStateInitialize);
			}
			else if (message is UserBeginDownloadWebFiles)
			{
				FSM.ChangeState(States.PatchUpdateStateDownloadFiles);
			}
			else if (message is UserTryUpdatePackageVersion)
			{
				FSM.StartState(States.PatchUpdateStateUpdateVersion);
			}
			else if (message is UserTryUpdatePatchManifest)
			{
				FSM.StartState(States.PatchUpdateStateUpdateManifest);
			}
			else if (message is UserTryDownloadWebFiles)
			{
				FSM.StartState(States.PatchUpdateStateCreateDownloader);
			}
			else
			{
				throw new System.NotImplementedException($"{message.GetType()}");
			}
		}
	}
}

