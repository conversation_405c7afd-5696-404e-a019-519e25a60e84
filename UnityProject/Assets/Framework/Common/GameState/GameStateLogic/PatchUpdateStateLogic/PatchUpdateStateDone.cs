using UnityEngine;
using Q<PERSON>ramework;


namespace MyFramework
{
    public class PatchUpdateStateDone : AbstractState<PatchManagerFSM.States, PatchManagerFSM>
    {
        public PatchUpdateStateDone(FSM<PatchManagerFSM.States> fsm, PatchManagerFSM target) : base(fsm, target)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        protected override void OnEnter()
        {
            Debug.Log("PatchUpdateStateDone OnEnter!");
            //load bt tree
            //BehaviacLoader loader = new BehaviacLoader();
            //loader.Init(mTarget.PlayMode, "Assets/Res/BTTree");
            PatchManagerFSM.PatchStatesChange.SendEventMessage("开始游戏！");
            //var gameStateFSM = CommonArchitecture.Interface.GetUtility<GameStateFSM>();
            //gameStateFSM.ChangeState(GameStateFSM.States.GameLogin);
            mTarget.Destroy();
            //CommonArchitecture.Interface.GetUtility<PatchManagerFSM>().Destroy();
            //GameManager.instance.StartGame();
        }

       
    }

}

