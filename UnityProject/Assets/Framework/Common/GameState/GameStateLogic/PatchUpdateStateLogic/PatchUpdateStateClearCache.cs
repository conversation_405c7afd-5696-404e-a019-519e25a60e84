using UnityEngine;
using Q<PERSON>ramework;


namespace MyFramework
{
    public class PatchUpdateStateClearCache : AbstractState<PatchManagerFSM.States, PatchManagerFSM>
    {
        public PatchUpdateStateClearCache(FSM<PatchManagerFSM.States> fsm, PatchManagerFSM target) : base(fsm, target)
        {
        }

        /// <summary>
        /// 加载StartLogo的prefab
        /// </summary>
        protected override void OnEnter()
        {
            Debug.Log("PatchUpdateStateClearCache OnEnter!");
            PatchManagerFSM.PatchStatesChange.SendEventMessage("清理未使用的缓存文件！");
            var package = YooAsset.YooAssets.GetPackage("DefaultPackage");
            var operation = package.ClearUnusedCacheFilesAsync();
            operation.Completed += Operation_Completed;
        }


        private void Operation_Completed(YooAsset.AsyncOperationBase obj)
        {
            mFSM.ChangeState(PatchManagerFSM.States.PatchUpdateStateDone);
        }
    }

}

