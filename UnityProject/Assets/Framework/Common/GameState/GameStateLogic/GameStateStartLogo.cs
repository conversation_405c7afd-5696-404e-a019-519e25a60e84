using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;


namespace MyFramework
{
    public class GameStateStartLogo : AbstractState<GameStateFSM.States, GameStateFSM>
    {
        GameObject mLogo;
        float mfTick = 0.0f;
        const float mcsTimer = 1.0f;
        public GameStateStartLogo(FSM<GameStateFSM.States> fsm, GameStateFSM target) : base(fsm, target)
        {
        }

        /// <summary>
        /// 加载StartLogo的prefab
        /// </summary>
        protected override void OnEnter()
        {
            Debug.Log("GameStateStartLogo OnEnter!");
            //if skip logo then goto next state
            var gameIni = CommonArchitecture.Interface.GetUtility<GameINI>();
            bool? skipLogo = gameIni.GetBool("SkipLogo");
            if (skipLogo.HasValue && skipLogo.Value)
            {
                mFSM.ChangeState(GameStateFSM.States.CheckGameConfig);
            }
            else
            {
                string path = "GameInit/Prefabs/StartLogo";
                GameObject prefab = Resources.Load(path) as GameObject;
                mLogo = GameObject.Instantiate(prefab);
            }
        }

        protected override void OnUpdate()
        {
            if(mfTick < mcsTimer)
            {
                mfTick += Time.deltaTime;
            }
            else
            {
                mFSM.ChangeState(GameStateFSM.States.CheckGameConfig);
                GameObject.Destroy(mLogo);
            }
        }
    }

}

