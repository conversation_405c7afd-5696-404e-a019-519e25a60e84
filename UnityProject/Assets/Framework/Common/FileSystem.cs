using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using Cysharp.Threading.Tasks;
using System;
using System.Linq;

namespace MyFramework
{
    public sealed class FileSystem
    {
		private static readonly Dictionary<string, bool> _cacheData = new Dictionary<string, bool>(128);
		//private static string m_streamingAssetsPath = "";

		public static async UniTask<UnityWebRequest> FileReadFromStreamingAssets(string filePath, bool bCheckExsit=false)
        {
			string path = GetFileStreamingAssetsPath(filePath);
			if(bCheckExsit&& !FileExists(path))
            {
				Debug.LogErrorFormat("File is not exsited: {0}",path);
				return null;
            }
			UnityWebRequest req = UnityWebRequest.Get(path);
			await req.SendWebRequest().ToUniTask();
			if(req.result == UnityWebRequest.Result.Success && req.isDone)
            {
				return req;
            }
			else
            {
				Debug.LogErrorFormat("Read File Fail {0} : {1}", req.result, req.error);
				return null;
            }
		}

#if UNITY_ANDROID && !UNITY_EDITOR
		private static AndroidJavaClass _unityPlayerClass;
		public static AndroidJavaClass UnityPlayerClass
		{
			get
			{
				if (_unityPlayerClass == null)
					_unityPlayerClass = new UnityEngine.AndroidJavaClass("com.unity3d.player.UnityPlayer");
				return _unityPlayerClass;
			}
		}

		private static AndroidJavaObject _currentActivity;
		public static AndroidJavaObject CurrentActivity
		{
			get
			{
				if (_currentActivity == null)
					_currentActivity = UnityPlayerClass.GetStatic<AndroidJavaObject>("currentActivity");
				return _currentActivity;
			}
		}

		private static AndroidJavaObject _assetsManager;
		public static AndroidJavaObject AssetsManager
		{
			get
			{
				if (_assetsManager == null)
					_assetsManager = CurrentActivity.Call<AndroidJavaObject>("getAssets");
				return _assetsManager;
			}
		}

		/// <summary>
		/// 利用安卓原生接口查询内置文件是否存在
		/// </summary>
		public static bool FileExists(string filePath)
		{
			if (_cacheData.TryGetValue(filePath, out bool result) == false)
			{
				result = false;
				try
				{
					var directory = System.IO.Path.GetDirectoryName(filePath);
					var fileName = System.IO.Path.GetFileName(filePath);
					var paths = AssetsManager.Call<string[]>("list", directory);
					foreach(var path in paths)
					{
						if(path == fileName)
						{
							result = true;
						}
					}
				}
				catch(Exception e)
				{
					Debug.Log(e.Message);
				}
				
				// result = CurrentActivity.Call<bool>("CheckAssetExist", filePath);
				_cacheData.Add(filePath, result);
			}
			return result;
		}
#else
		public static bool FileExists(string filePath)
		{
			if (_cacheData.TryGetValue(filePath, out bool result) == false)
			{
				result = System.IO.File.Exists(System.IO.Path.Combine(Application.streamingAssetsPath, filePath));
				_cacheData.Add(filePath, result);
			}
			return result;
		}
#endif

		public static string GetFileStreamingAssetsPath(string assetPath)
		{
			return string.Format("{0}/{1}", Application.streamingAssetsPath, assetPath);
		}

        public static string GetRegularPath(string path)
        {
            if (path == null)
            {
                return null;
            }

            return path.Replace('\\', '/');
        }
    }
}
