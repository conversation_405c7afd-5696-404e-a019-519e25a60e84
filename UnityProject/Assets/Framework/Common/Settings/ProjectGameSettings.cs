using UnityEngine;

namespace MyFramework
{
    [CreateAssetMenu(fileName = "ProjectGameSettings", menuName = "Project/ProjectGameSettings")]
    public class ProjectGameSettings : ScriptableObject
    {
        [Header("Framework")][SerializeField] private FrameworkSettings m_FrameworkGlobalSettings;

        public FrameworkSettings FrameworkGlobalSettings => m_FrameworkGlobalSettings;

        [Header("HybridCLR")][SerializeField] private HybridCLRCustomGlobalSettings m_BybridCLRCustomGlobalSettings;

        public HybridCLRCustomGlobalSettings BybridCLRCustomGlobalSettings => m_BybridCLRCustomGlobalSettings;

        public void Set(FrameworkSettings globalSettings, HybridCLRCustomGlobalSettings hybridClrCustomGlobalSettings)
        {
            m_FrameworkGlobalSettings = globalSettings;
            m_BybridCLRCustomGlobalSettings = hybridClrCustomGlobalSettings;
        }
    }
}
