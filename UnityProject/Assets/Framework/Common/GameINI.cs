using System;
using System.Collections.Generic;
using QFramework;

namespace MyFramework
{
    /// <summary>
    /// Game ini class, store game config such as server url, sdk open,gm open
    /// </summary>
    public class GameINI : IUtility
    {
        Dictionary<string, Variant> _gameINI = new Dictionary<string, Variant>(); 

        public Boolean? GetBool(string key)
        {
            if(_gameINI.ContainsKey(key))
            {
                return (Boolean)(_gameINI[key]);
            }
            else
            {
                return null;
            }
        }

        public void SetBool(string key, bool bBool)
        {
            if (!_gameINI.ContainsKey(key))
            {
                _gameINI.Add(key, bBool);
            }
            else
            {
                _gameINI[key] = bBool;
            }
        }

        public int? GetInt(string key)
        {
            if (_gameINI.ContainsKey(key))
            {
                return (int)(_gameINI[key]);
            }

            return null;
        }

        public void SetInt(string key, int iNum)
        {
            if (!_gameINI.ContainsKey(key))
            {
                _gameINI.Add(key, iNum);
            }
            else
            {
                _gameINI[key] = iNum;
            }
        }

        public float? GetFloat(string key)
        {
            if (_gameINI.ContainsKey(key))
            {
                return (float)(_gameINI[key]);
            }

            return null;
        }

        public void SetFloat(string key, float iNum)
        {
            if (!_gameINI.ContainsKey(key))
            {
                _gameINI.Add(key, iNum);
            }
            else
            {
                _gameINI[key] = iNum;
            }
        }

        public string GetString(string key)
        {
            if (_gameINI.ContainsKey(key))
            {
                return (string)(_gameINI[key]);
            }

            return null;
        }

        public void SetString(string key, string sStr)
        {
            if (!_gameINI.ContainsKey(key))
            {
                _gameINI.Add(key, sStr);
            }
            else
            {
                _gameINI[key] = sStr;
            }
        }

        public void SetValue(string key, Variant vv)
        {
            if (!_gameINI.ContainsKey(key))
            {
                _gameINI.Add(key, vv);
            }
            else
            {
                _gameINI[key] = vv;
            }
        }

        public const string ServerSettingUrl = "ServerSettingUrl";
        public const string GMMenu = "GMMenu";
        public const string Version = "Version";
        public const string BuildTime = "BuildTime";

        public const string LoginAgent = "LoginAgent";
        public const string AssetBundleDownLoadServerList = "AssetBundleDownLoadServerList";
        public const string LoginAnnouncementUrl = "LoginAnnouncementUrl";
        public const string ServerListUrl = "ServerListUrl";
        public const string ClientUploadLogUrl = "ClientUploadLogUrl";
        public const string TranslateServerUrl = "TranslateServerUrl";

        public class GameConfigJson
        {
            public string ServerSettingUrl;
            public bool GMMenu;
            public string Version;
            public string BuildTime;
        }

        public class ServerUrlJson
        {
            public string LoginAgent;
            public string AssetBundleDownLoadServerList;
            public string LoginAnnouncementUrl;
            public string ServerListUrl;
            public string ClientUploadLogUrl;
            public string TranslateServerUrl;
        }
    }
}
