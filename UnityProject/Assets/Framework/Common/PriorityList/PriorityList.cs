using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace MyFramework.Common
{
    public class PriorityList<T>
    {
        private List<PriorityItem<T>> _items = new List<PriorityItem<T>>();

        public void Add(T item, int priority)
        {
            var priorityItem = new PriorityItem<T>(item, priority);
            _items.Add(priorityItem);
            Sort();
        }

        public bool Remove(T item)
        {
            var index = _items.FindIndex(priorityItem => priorityItem.Item.Equals(item));
            if (index >= 0)
            {
                _items.RemoveAt(index);
                Sort();
                return true;
            }
            return false;
        }

        public T Peek()
        {
            if (_items.Count == 0)
            {
                throw new InvalidOperationException("The priority list is empty.");
            }
            return _items[0].Item;
        }

        public T Pop()
        {
            if (_items.Count == 0)
            {
                throw new InvalidOperationException("The priority list is empty.");
            }

            var nextItem = _items[0];
            _items.RemoveAt(0);
            Sort();
            return nextItem.Item;
        }

        public T this[int index]
        {
            get { return _items[index].Item; }
            set { _items[index].Item = value; }
        }

        void Sort()
        {
            _items.Sort((x, y) =>
            {
                int priorityComparison = y.Priority.CompareTo(x.Priority);
                return priorityComparison == 0 ? y.Timestamp.CompareTo(x.Timestamp) : priorityComparison;
            });
        }

        public int Count => _items.Count;
    }
}
